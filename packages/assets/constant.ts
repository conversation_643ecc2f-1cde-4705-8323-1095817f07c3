import { THEME_NAME_MAP } from '../base/grid/helpers/constants/themeConfig'
import Theme from './newTheme/utils'

// PROJECT是跟着分支走，合并都用源分支
export const PROJECT = 'SDP'
export const VERSION = 'V4.15.00.2'
export const BE_VERSION = '2.0' // 后端代码版本号
export const LANGUAGE_SWITCHING = 'languageSwitching'

export const FONT_SIZE_LIST = [6, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 48, 72] as const

export const TIME_RANGE = {
  min: 1,
  max: 60
}
// 主题样式常量
export const MAIN_STYLE = {
  /* 颜色变量定义 */
  '$colorMain': '#553CCE',
  '$colorTheme': '#34383c',
  '$colorMainHover': '#7B75EF',
  '$colorMainBac': '#e7f8f7',
  '$colorPreviewInput': '#dcdfe6',
  // $color-primary: #333;
  '$colorBgDefault': '#f5f7fa',
  '$colorWhite': '#ffffff',
  '$colorBgGrey': '#efeff0',

  /* border 样式 */
  '$boardGridBoard': '10px solid #f5f7fa',
  // 右边内容区域背景色
  '$rightContentBg': '#efefef',
  '$mainLightColor': '#553CCE',
  '$elementPrimary': '#553CCE',
  // 数据集被禁
  '$datasetBgGrid': '#f8f8f8',
  '$datasetBgBoard': '#ffffff',
  '$iconDatesetGrid': '#f3f3f3',

  '$colorMainPreview': '#34383c',
  '$colorPreview': '#455964',

  // 默认字体
  '$defaultFontFamily': 'SourceHanSansCN-Regular, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif',
}

export const INFORMATION_KEY = {
  DATA_UPDATE_TIME: 'dataUpdateTime',
  DATA_REFRESH_TIME: 'dataRefreshTime',
  PROPERTY_NUMBER: 'propertyNumber',
  DATA_DATE: 'dataDate',
  WEATHER_DATA: 'weatherData',
  EL_DATA_UPDATE_TIME: 'elDataUpdateTime',
  EL_DATA_UPDATE_TIME_STAMP: 'elDataUpdateTimeStamp',
  EL_DATA_REFRESH_TIME: 'elDataRefreshTime',
}
export const weatherIcon = {
  '晴': 'icon-sdp-qing',
  '少云': 'icon-sdp-shaoyun1',
  '晴间多云': 'icon-sdp-duoyun',
  '多云': 'icon-sdp-duoyun',
  '阴': 'icon-sdp-yin',
  '有风': 'icon-sdp-youfeng',
  '平静': 'icon-sdp-pingjing',
  '微风': 'icon-sdp-weifeng',
  '和风': 'icon-sdp-weifeng',
  '清风': 'icon-sdp-weifeng',
  '强风/劲风': 'icon-sdp-qiangfeng',
  '疾风': 'icon-sdp-qiangfeng',
  '大风': 'icon-sdp-qiangfeng',
  '烈风': 'icon-sdp-qiangfeng',
  '风暴': 'icon-sdp-kuangfeng',
  '狂爆风': 'icon-sdp-kuangfeng',
  '飓风': 'icon-sdp-kuangfeng',
  '热带风暴': 'icon-sdp-kuangfeng',
  '霾': 'icon-sdp-mai',
  '中度霾': 'icon-sdp-zhongdumai',
  '重度霾': 'icon-sdp-zhongdumai1',
  '严重霾': 'icon-sdp-zhongdumai1',
  '阵雨': 'icon-sdp-zhenyu',
  '雷阵雨': 'icon-sdp-leizhenyu',
  '雷阵雨并伴有冰雹': 'icon-sdp-leizhenyubanyoubingbao',
  '小雨': 'icon-sdp-xiaoyu',
  '中雨': 'icon-sdp-zhongyu',
  '大雨': 'icon-sdp-dayu',
  '暴雨': 'icon-sdp-baoyu',
  '大暴雨': 'icon-sdp-dabaoyu',
  '特大暴雨': 'icon-sdp-tedabaoyu',
  '强阵雨': 'icon-sdp-qiangzhenyu',
  '强雷阵雨': 'icon-sdp-qiangleizhenyu',
  '极端降雨': 'icon-sdp-tedabaoyu',
  '毛毛雨/细雨': 'icon-sdp-xiaoyu-zhongyu',
  '雨': 'icon-sdp-xiaoyu-zhongyu',
  '小雨-中雨': 'icon-sdp-xiaoyu-zhongyu',
  '中雨-大雨': 'icon-sdp-zhongyu-dayu',
  '大雨-暴雨': 'icon-sdp-dayu-baoyu',
  '暴雨-大暴雨': 'icon-sdp-baoyu-dabaoyu',
  '大暴雨-特大暴雨': 'icon-sdp-dabaoyu-tedabaoyu',
  '雨雪天气': 'icon-sdp-yujiaxue',
  '雨夹雪': 'icon-sdp-yujiaxue',
  '阵雨夹雪': 'icon-sdp-yujiaxue',
  '冻雨': 'icon-sdp-dongyu',
  '雪': 'icon-sdp-xiaoxue',
  '阵雪': 'icon-sdp-zhenxue',
  '小雪': 'icon-sdp-xiaoxue',
  '中雪': 'icon-sdp-zhongxue',
  '大雪': 'icon-sdp-daxue',
  '暴雪': 'icon-sdp-baoxue',
  '小雪-中雪': 'icon-sdp-xiaoxue-zhongxue',
  '中雪-大雪': 'icon-sdp-zhongxue-daxue',
  '大雪-暴雪': 'icon-sdp-daxue-baoxue',
  '浮尘': 'icon-sdp-fuchen',
  '扬沙': 'icon-sdp-yangsha',
  '沙尘暴': 'icon-sdp-shachenbao',
  '强沙尘暴': 'icon-sdp-qiangshachenbao',
  '龙卷风': 'icon-sdp-longjuanfeng',
  '雾': 'icon-sdp-wu',
  '浓雾': 'icon-sdp-nongwu',
  '强浓雾': 'icon-sdp-nongwu',
  '轻雾': 'icon-sdp-wu',
  '大雾': 'icon-sdp-nongwu',
  '特强浓雾 ': 'icon-sdp-teqiangnongwu',
  '热': 'icon-sdp-qing',
  '冷': 'icon-sdp-xiaoxue',
  '未知': 'icon-sdp-wushuju',
}

export const dataUpdateTime = Object.freeze({
  name: INFORMATION_KEY.DATA_UPDATE_TIME,
  show: false,
  position: 'right',
})
export const dataRefreshTime = Object.freeze({
  name: INFORMATION_KEY.DATA_REFRESH_TIME,
  show: false,
  position: 'right',
})
export const propertyNumber = Object.freeze({
  name: INFORMATION_KEY.PROPERTY_NUMBER,
  show: false,
  position: 'right',
})
export const dataDate = Object.freeze({
  name: INFORMATION_KEY.DATA_DATE,
  show: false,
  position: 'left',
  format: 0
})
export const weatherData = Object.freeze({
  name: INFORMATION_KEY.WEATHER_DATA,
  show: false,
  position: 'left',
  province: '',
  city: '',
  data: [
    {
      isOpen: true,
      name: 'weatherSituation'
    },
    {
      isOpen: true,
      name: 'tempRange'
    },
    {
      isOpen: false,
      name: 'currentTemp'
    },
    {
      isOpen: true,
      name: 'wind'
    },
    {
      isOpen: false,
      name: 'humidity'
    },
  ]
})
export const refreshDataInit = Object.freeze({
  interval: 1,
  carouselTime: 30,
  groupList: [ dataUpdateTime, dataRefreshTime, propertyNumber, dataDate, weatherData ]
})
// 生产环境打包，需要切换到
let host = ['build_prod', 'lib_prod'].includes(process.env.VUE_APP_BUILD_ENV)
  ? 'https://sdp-cdn.shijicloud.com'
  : 'https://sdptest.shijicloud.com'

// 静态资源存放路径, 不要随意添加新的路径
export const STATIC_BASE_PATH = {
  images: `${host}/static/images/sdp/`,
  svg: `${host}/static/svg/sdp/`,
  css: `${host}/static/sdp_app/web/assets/`,
  map: `${host}/static/map/`
}

export const SUPERLINK_CONST_TYPE = {
  variavle: 'variavle',
  constant: 'constant',
  title: 'title',
}
export const SUPER_LINK_CONST_SITE = {
  TITLE: 'Title',
}

/**
 * 对外暴露组件列表
 * 可以修改和新增每个对外暴露组件的名字
 * 不允许改变顺序和删除
 * @type {{board: string[]}}
 */
export const EXPORT_COMP_LIST = {
  board: ['displayPanel', 'settingsPanel', 'titleBar', 'featuresBar']
}

export const EVENT_DATA_PARAMS = {
  parent: 'parent',
  displayPanel: 'displayPanel',
  displayPanelMobile: 'displayPanelMobile',
  displayPanelPcToMobile: 'displayPanelPcToMobile'
}
// 空间类型常量
export const PRIVATE = '1'
// createToast 提示时间
export const CREATE_TOAST_TIME = 2500
export const DATA_THEME = 'data-theme'

export const RUN_TYPE = {
  // 手动调用
  handRun: 'handRun',
  // 点击日历快选组件
  paramsRun: 'paramsRun',
  // 点击location快选组件
  locationRun: 'locationRun',
  // 过滤所有组件调用
  elRefreshRun: 'elRefreshRun',
}

export const RUN_ELEMENT_TYPE = {
  // 刷新
  refresh: 'refresh',
  // 看板参数组件run
  paramEl: 'paramEl',
  // 看板元素组件run
  refreshEl: 'refreshEl',
}
export const TAGNEWCARD = {
  // 单指标卡片
  SINGLEINDEX: 'singleIndex',
  // 双指标卡片
  TWOINDICES: 'twoIndeces',
  // 比率卡片
  RATECARD: 'rate',
  COMPARECARD: 'compare',
  RANKCARD: 'rank',
  // 杜邦分析图指标卡片
  DUPONTINDICATOR: 'dupontIndicator',
}

// 卡片预警类型连接符
export const WARNLINE_TYPE_CONNECT_STR = '@#$%'

export const PREVIEW_STATUS = {
  EDIT: 'EDIT',
  PREVIEW: 'PREVIEW',
  REMIND: 'remind', // 订阅提醒
  TENANT: 'tenant', // 租户看板(1.0看板启用)
  BROWSE: 'browse', // 看板浏览 收藏夹
  OPEN_SOURCE: 'openSource', // 企业开源(edit)
  DESIGNER: 'designer', // 企业设计器(edit)
  DASHBOARD: 'dashboard', // 仪表板编辑器(edit)
}

export const TYPE_LIST = {
  BOARD: 'board',
  GRID: 'grid',
  CHART: 'chart',
  CARD: 'card',
}
export const TYPE_RUN = {
  auto: 'auto',
  hand: 'hand',
}

// 支持的语言列表 目前仅支持中英文
export const LANGUAGE_LIST = ['en', 'zh']
export const THEME_MAPS = {
  classicWhite: 'sdp-classic-white',
  darkBlue: 'sdp-dark-blue',
  deepBlue: 'sdp-deep-blue',
} as const

export const THEME_VALUE_MAPS = {
  [THEME_MAPS.classicWhite]: '0',
  [THEME_MAPS.darkBlue]: '1',
  [THEME_MAPS.deepBlue]: '2',
} as const

export type ThemeType = typeof THEME_MAPS[keyof typeof THEME_MAPS]

export const THEME_TYPE = {
  default: THEME_MAPS.classicWhite,
  ...THEME_MAPS
} as const

/**
 * 参考线默认配置
 * @type {{LINE_WIDTH: number, LINE_DASH: number[], themeStyleMap: {}, VISIBLE: boolean}}
 */
export const REFERENCE_LINE_DFT_OPTS = {
  /**
   * 参考线是否显示
   */
  VISIBLE: false,
  /**
   * 参考线线宽
   */
  LINE_WIDTH: 1,
  /**
   * 参考线虚线配置
   */
  LINE_DASH: [5],

  themeStyleMap: {
    [THEME_NAME_MAP.THEME_LIGHT]: {
      // strokeStyle: '#bbb', // 参考线颜色
      strokeStyle: Theme.getThemeVariableValue(THEME_TYPE.classicWhite, '--sdp-xk-bks'),
    },
    [THEME_NAME_MAP.THEME_DARK]: {
      // strokeStyle: '#666',
      strokeStyle: Theme.getThemeVariableValue(THEME_TYPE.darkBlue, '--sdp-xk-bks'),
    },
    // !kyz js主题设ui确定
    [THEME_NAME_MAP.THEME_BLUE]: {
      strokeStyle: Theme.getThemeVariableValue(THEME_TYPE.deepBlue, '--sdp-xk-bks'),
    },
  }
}

export const REQUEST_APP_TYPE = {
  STATUS_BAR_COLOR: 'statusBarColor'
}
export const BG_MAP_STRING = {
  pc: 'pcBgc',
  mobile: 'mobileBgc',
}

export const THEME_TYPE_KEY = {
  '0': 'classicWhite',
  '1': 'darkBlue',
  '2': 'deepBlue',
}

// taro的类型
export const TARO_ENV_TYPE = {
  ALIPAY: 'ALIPAY',
  JD: 'JD',
  QQ: 'QQ',
  QUICKAPP: 'QUICKAPP',
  RN: 'RN',
  SWAN: 'SWAN',
  TT: 'TT',
  WEAPP: 'WEAPP',
  WEB: 'WEB',
  ANDROID: 'ANDROID',
  IOS: 'IOS'
}
// 4014 移动步长
export const GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY = 144
// export const GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY = 24
export const GRID_LAYOUT_ROW_HEIGHT_FOR_BORDER_DISPLAY = 5
// export const GRID_LAYOUT_ROW_HEIGHT_FOR_BORDER_DISPLAY = 70
export const GRID_LAYOUT_MARGIN_FOR_BORDER_DISPLAY = [8, 8]

export const GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS = 7

export const PC_BORDER_ELEMENT_COMMON_PADDING_BOTTOM_FOR = 10

export const LARGE_SCREEN_COL_NUM_FOR_BORDER_DISPLAY = 250

// 流式数据集标识
export const FLOW_MODEL_DATASET = '2'
export const EXTERNAL_FOLDER = '9'
// 自定义数据集 和企业数据集一致
export const EXTERNAL_CUSTOM = '9'
// 标准数据集
export const EXTERNAL_STANDARD = '10'
// 自定义看板 boardInfo.folderType类型
export const CUSTOM_FOLDER_TYPE = '4'
// 标准看板 boardInfo.folderType类型
export const STANDARD_FOLDER_TYPE = '11'
// 已发布看板 boardInfo.folderType类型
export const RELEASE_FOLDER_TYPE = '4'
// 发布给我看板 boardInfo.folderType类型
export const RELEASE_TO_ME_TYPE = '12'

export const CHART_TYPE = {
  VE_GRID_NORMAL: 've-grid-normal'
}

export const PARAMS_PANEL_TYPE = {
  NORMAL: 'normal',
  QUICK_SELECTION_BY_LOCATION: 'quick_selection_by_location',
  QUICK_SELECTION_BY_BUSSINESS_CALENDAR: 'quick_selection_by_bussiness_calendar',
  QUICK: 'quick',
}

export const VIRTUAL_DATASET_KEY = 'virtual' // 虚拟数据集id

export const PROJECT_MODULE = {
  REPORT_DESIGNER: 'ReportDesigner',
}

export const WARNING_ALERT_SUBSCRIPTION = {
  ALERT_SUBSCRIPTION: 'alertSubscription',
  WARNING_NAME: 'warningName',
  WARNING_FIELD: 'warningField',
  WARNING_TARGET_FIELD: 'warningTargetField',
  WARNING_CELL_COORDINATE: 'warningCellCoordinate',
  FIELD_NAME: 'fieldName',
}

export const EXPORT_TYPE = {
  PDF: 'PDF',
  OFD: 'OFD',
  Excel: 'Excel',
  CSV: 'CSV',
  Word: 'Word',
}

export const FIELD_CALC_TYPE_LIST = (vm) => {
  return [
    {
      value: 'SUM',
      label: vm.$t('sdp.views.SUM')
    },
    {
      value: 'SUM_MONTH',
      label: vm.$t('sdp.views.sumIgnoreDimension')
    },
    {
      value: 'AVG',
      label: vm.$t('sdp.views.average')
    },
    {
      value: 'AVG_MONTH',
      label: vm.$t('sdp.views.averageMonthDate')
    },
    {
      value: 'MAX',
      label: vm.$t('sdp.views.largest')
    },
    {
      value: 'MIN',
      label: vm.$t('sdp.views.MIN')
    },
  ]
}

export const EVENTS_TYPE = {
  DRILL_SUBMIT: 'drill_submit',
  // ... 其他事件类型
}
