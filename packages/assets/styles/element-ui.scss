@import './variable.scss'; //to reset element-ui default css
@import './sdp-standard.css';
[class^='icon-sdp'],
[class*='icon-sdp'] {
  font-family: 'sdpiconfont' !important;
  font-size: 18px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@import './sdp-scrollbar';

//  .sdp-board-design,
//  .sdp-board-preview,
//  .sdp-grid-design,
//  .sdp-grid-preview,
//  .sdp-dialog,
//  .sdp-popover,
//  .board-design-pop,
//  .sdp-grid-preview-popover,
// .sdp-theme-style {
//   @import './theme/index.scss';
//  }
 // 自定义样式抽出font 覆盖默认样式
 @import '../theme/calendar-picker-theme/index.scss';
 // 修改主題下提示框图标隐藏
 $self: (
   sdp-classic-white: (
           displayNone: none,
           paddingLeft: 0,
           cubeBorderColor: rgba(255, 255, 255, 0.9)
   ),
   sdp-dark-blue: (
           displayNone: none,
           paddingLeft: 0,
           cubeBorderColor: rgba(255, 255, 255, 0.2)
   )
 );
 .cube-toast-tip {
   max-height: 100% !important; // 设置最高高度的话文字会显示不全
 }
 // 提示框样式
//  .sdp-dialog.el-message-box{
//    .el-message-box__content {
//      .el-message-box__message {
//        @include sdp-mixin-style($type:(
//           color:(fontColor:true),
//           paddingLeft: (paddingLeft:true),
//        ), $self:$self)
//      }
//      .el-icon-warning{
//        @include sdp-mixin-style($type:(
//          display:(displayNone:true),
//        ), $self:$self)
//      }
//    }
//  }
 // 设置cube ui的滚动条
 .bscroll-indicator {
   @include sdp-mixin-style($type:(
      borderColor:(cubeBorderColor:true),
   ), $self:$self)
 }

.sdp-params-hover-theme-background:hover {
  background-color: var(--sdp-cszjxz-hs2) !important;
  color: var(--sdp-cszjsz-wzs2) !important;
}

// .sdp-bottom-theme-boder {
//   @include param-bottom-border_0();
// }
// .sdp-bottom-theme-boder::before {
//   @include param-bottom-border_0();
// }
.sdp-params-select-theme-background {
  background-color: var(--sdp-cszjxz-hs2) !important;
  color: var(--sdp-cszjsz-wzs2) !important;
}
.sdp-params-theme-border {
  border-color: var(--sdp-cszj-srbks) !important;
}
.sdp-params-quick-theme-style {
  color: var(--sdp-sztc-bts);
  border-color: var(--sdp-cszj-srbks);
}
.sdp-params-set-popover-style {
  background-color: var(--sdp-tb-bj);
  @include sdp-setting-params-border-0();
  .popper__arrow {
    @include sdp-borderColor_1('bottom');
    &::after {
      border-top-color: var(--sdp-bg-lhtwz) !important;
      border-bottom-color: var(--sdp-bg-lhtwz) !important;
    }
  }
}
.sdp-param-calendar-pri-style {
  .font {
    background-color: var(--sdp-fs1);
    width: fit-content;
    padding: 0 6px;
    height: 20px;
    line-height: 18px;
    margin-bottom: 5px;
    color: var(--sdp-cszjsz-wzs1);
    border: 1px solid var(--sdp-ycsz-srk-bcs);
  }
  .icon {
    border-radius: 100%;
    font-size: 12px;
    cursor: pointer;
    color: var(--sdp-cszjisc);
    &:hover{
      color: var(--sdp-cszjisc-hover);
    }
  }
}
.sdp-params-theme-radio {
  .el-radio__label {
    color: var(--sdp-zjbts);
  }
}
.sdp-run-theme-button {
  .el-button {
    @include sdp-background_0();
    color: var(--sdp-sztc-bts) !important;
  }
  .el-button:hover {
    color: var(--sdp-jys) !important;
    background-color: var(--sdp-szk-bjs) !important;
  }
  .el-button:focus {
    color: var(--sdp-jys) !important;
    background-color: var(--sdp-szk-bjs) !important;
  }
}
.sdp-params-calendar-theme-background {
  &.dialog-width-auto {
    width: auto;
  }
  .el-loading-mask {
    color: var(--sdp-bg-lhtwz) !important;
  }
  color: var(--sdp-bg-lhtwz) !important;
  .el-dialog__header {
    color: var(--sdp-bg-lhtwz) !important;
    .el-dialog__title {
      color: var(--sdp-bg-lhtwz) !important;
    }
  }
}
.sdp-param-calendar-select-background {
  background-color: var(--sdp-rl-hzts);
}
.sdp-param-calendar-block-background {
}
.sdp-param-calendar-select-border-bordercolor {
  @include param-calendar-select-border-bordercolor_0();
}
.sdp-param-calendar-noselect-border-bordercolor {
  border-color: var(--sdp-cszj-bkfgx);
}
.param-calendar-fintitle-color {
  background-color: var(--sdp-fs1) !important;
  color: var(--sdp-cszjsz-wzs1) !important;
}
.sdp-params-calendar-font {
  color: var(--sdp-xxbt2);
}
.sdp-params-day-color {
  color: var(--sdp-cszjsz-wzs1);
}
.param-calendar-ismonth-background {
  background-color: var(--sdp-szk-bjs) !important;
  color: var(--sdp-cszjsz-wzs1) !important;
}
.not-disabled-wk {
  @include not-disabled-wk();
}
.param-calendar-wkstyle-app {
  background: $color-RLZBJS !important;
  color: $color-RLZWZS !important;
}
.param-calendar-wkstyle-pc {
  @include param-calendar-ismonth-background('Pc');
}
.param-calendar-finhover-background:hover {
  background-color: var(--sdp-rl-hzts);
}
.sdp-params-day-ban-color {
  @include param-day-color_1();
}
.sdp-params-month-color {
  color: var(--sdp-cszjsz-wzs1);
}
.sdp-params-week-num-color {
  color: var(--sdp-cszjsz-wzs1) !important;
  background-color: var(--sdp-zxz-nns) !important;
}
.sdp-params-metric--theme-background {
  color: var(--sdp-bg-lhtwz) !important;
  border-color: var(--sdp-cszj-srbks) !important;
  .popper__arrow {
    @include sdp-borderColor_1('bottom');
    &::after {
      border-top-color: var(--sdp-bg-lhtwz) !important;
      border-bottom-color: var(--sdp-bg-lhtwz) !important;
    }
  }
  .el-scrollbar .el-select-dropdown__wrap .el-scrollbar__view {
    div {
      .all-style {
        color: var(--sdp-bg-lhtwz) !important;
      }
      .selected {
        background-color: var(--sdp-cszjxz-hs1) !important;
        color: var(--sdp-nngl) !important;
      }
      .hover {
        background-color: var(--sdp-cszjxz-hs1) !important;
        color: var(--sdp-nngl) !important;
      }
      .is-disabled {
        color: var(--sdp-jys);
      }
      :not(.selected):not(.hover):not(.is-disabled) {
        span {
          color: var(--sdp-cszjsz-wzs1);
        }
      }
    }
    .selected {
      background-color: var(--sdp-cszjxz-hs1) !important;
      color: var(--sdp-nngl) !important;
    }
    .hover {
      background-color: var(--sdp-cszjxz-hs1) !important;
      color: var(--sdp-nngl) !important;
    }
    .is-disabled {
      color: var(--sdp-jys);
    }
  }
}
.sdp-params-bussiness--theme-background {
  color: var(--sdp-bg-lhtwz) !important;
  border-color: var(--sdp-cszj-srbks) !important;
  .popper__arrow {
    @include sdp-borderColor_1('bottom');
    &::after {
      border-top-color: var(--sdp-bg-lhtwz) !important;
      border-bottom-color: var(--sdp-bg-lhtwz) !important;
    }
  }
  .el-scrollbar .el-select-dropdown__wrap .el-scrollbar__view {
    div {
      .all-style {
        color: var(--sdp-bg-lhtwz) !important;
      }
      .selected {
        background-color: var(--sdp-cszjxz-hs1) !important;
        color: var(--sdp-nngl) !important;
      }
      .hover {
        background-color: var(--sdp-cszjxz-hs1) !important;
        color: var(--sdp-nngl) !important;
      }
      .is-disabled {
        color: var(--sdp-jys);
      }
      :not(.selected):not(.hover):not(.is-disabled) {
        span {
          color: var(--sdp-cszjsz-wzs1);
        }
      }
    }
    .selected {
      background-color: var(--sdp-cszjxz-hs1) !important;
      color: var(--sdp-nngl) !important;
    }
    .hover {
      background-color: var(--sdp-cszjxz-hs1) !important;
      color: var(--sdp-nngl) !important;
    }
    .is-disabled {
      color: var(--sdp-jys);
    }
    :not(.selected):not(.hover):not(.is-disabled) {
      span {
        color: var(--sdp-cszjsz-wzs1);
      }
    }
  }
}
.sdp-calendar-tree {
  .el-tree-node:focus > .el-tree-node__content {
    @include select-hover_1();
  }
  .el-tree-node {
    .el-tree-node:focus > .el-tree-node__content {
      @include select-hover_1();
    }
    .el-tree-node__content:hover,
    .el-tree-node__content:focus {
      @include select-hover_1();
    }
    .el-tree-node__content:focus > .el-tree-node__content {
      @include select-hover_1();
    }
    .is-current {
      @include select-hover_1();
    }
    .el-tree-node__children {
      .el-tree-node:hover,
      .el-tree-node:focus {
      }
      .el-tree-node:focus > .el-tree-node__content {
        @include select-hover_1();
      }
      .is-current {
        @include select-hover_1();
      }
    }
  }
}
.sdp-params-theme-calendar-background {
  background-color: var(--sdp-cszj-bjds);
  border-color: var(--sdp-cszj-srbks);
}
.sdp-params-theme-background {
  color: var(--sdp-fhwzs) !important;
  // background-color: var(--sdp-szk-bjs);
  .like-placeholder {
    color: var(--sdp-fhwzs) !important;
  }
  &.disabledStyle .like-placeholder {
    color: var(--sdp-jys) !important;
  }
  .placeholder-color {
    color: var(--sdp-srk-bxwzs) !important;
  }
  .calendar-width {
    color: var(--sdp-bg-lhtwz) !important;
    border-color: var(--sdp-cszj-srbks) !important;
  }
  // .el-tree-node {
  //   .el-tree-node:focus>.el-tree-node__content
  //   .el-tree-node__content {
  //     color: var(--sdp-cszjsz-wzs1) !important;
  //   }
  //   .el-tree-node__children {
  //     color: var(--sdp-cszjsz-wzs1) !important;
  //   }
  //   .el-tree-node:focus>.el-tree-node__content, .el-tree-node__content:hover {
  //     background-color: var(--sdp-cszjxz-hs1) !important;
  //     color: var(--sdp-cszjsz-wzs1) !important;
  //   }
  //   .el-tree-node__children:hover {
  //     color: var(--sdp-cszjsz-wzs1) !important;
  //   }
  // }
  .el-scrollbar {
    color: var(--sdp-bg-lhtwz) !important;
  }
  border-color: var(--sdp-cszj-srbks) !important;
  input {
    background-color: transparent !important;
    border-color: var(--sdp-cszj-srbks) !important;
    color: var(--sdp-fhwzs) !important;
  }
  .el-input input {
    background-color: transparent !important;
    border-color: var(--sdp-cszj-srbks) !important;
    color: var(--sdp-fhwzs) !important;
  }

  .sdp-param-theme-search {
    .serach-input .el-input {
      color: var(--sdp-bg-lhtwz) !important;
    }
    .serach-input .el-input .el-input__inner {
      background-color: var(--sdp-fs1) !important;
    }
  }
  // 弹框里面的
  .playBox.el-input {
    input {
      background-color: var(--sdp-fs1) !important;
      // border: 1px solid var(--sdp-ycsz-srk-bcs);
      color: var(--sdp-zjbts);
    }
  }
  .el-scrollbar .el-select-dropdown__wrap {
    color: var(--sdp-bg-lhtwz) !important;
    border-color: var(--sdp-cszj-srbks);
  }
  .el-scrollbar .el-select-dropdown__wrap .el-scrollbar__view {
    div {
      .all-style {
        color: var(--sdp-bg-lhtwz) !important;
      }
      :not(.selected):not(.hover):not(.is-disabled) {
        span {
          color: var(--sdp-cszjsz-wzs1);
        }
      }
    }
  }
  // .el-tree {
  //   .el-tree-node__content:hover {
  //     background-color: var(--sdp-cszjxz-hs1) !important;
  //     color: var(--sdp-cszjsz-wzs1) !important;
  //     color: var(--sdp-cszjsz-wzs1);
  //   }
  //   .is-current > .el-tree-node__content {
  //     background-color: var(--sdp-cszjxz-hs1) !important;
  //     color: var(--sdp-cszjsz-wzs1) !important;
  //     color: var(--sdp-cszjsz-wzs1);
  //   }
  // }
  //.popper__arrow {
  //  @include sdp-borderColor_1('bottom');
  //  @include sdp-borderColor_1('top');
  //  &::after {
  //    border-top-color: var(--sdp-bg-lhtwz) !important;
  //    border-bottom-color: var(--sdp-bg-lhtwz) !important;
  //  }
  //}
}

@mixin sdp-params-theme-element-background() {
  border-color: var(--sdp-cszj-srbks) !important;
  .el-input input {
    @include chart-select-backgroundcolor();
    border-color: var(--sdp-cszj-srbks) !important;
    color: var(--sdp-cszjsz-wzs1);
  }
  .el-scrollbar .el-select-dropdown__wrap {
    border-color: var(--sdp-cszj-srbks) !important;
  }
  .el-select-dropdown__item {
    color: var(--sdp-cszjsz-wzs1);
    &.selected {
      @include chart-select-backgroundcolor-active();
      color: var(--sdp-cszjsz-wzs1);
      font-weight: normal;
    }
    &.hover,
    &:hover {
      @include chart-select-backgroundcolor-active();
      color: var(--sdp-cszjsz-wzs1);
    }
  }
  .el-scrollbar .el-select-dropdown__wrap .el-scrollbar__view {
    div {
      .all-style {
        @include chart-select-backgroundcolor();
      }
    }
  }
  .popper__arrow {
    display: none !important;
  }
}
.sdp-params-theme-element-background {
  @include sdp-params-theme-element-background();
}
.sdp-params-theme-element-top-background {
  @include sdp-params-theme-element-background();
  background-color: var(--sdp-gjys-bjs);
  .el-input input {
    background-color: var(--sdp-gjys-bjs) !important;
    @include sdp-fontcolor_1('important');
  }
  .el-select-dropdown__item {
    @include sdp-fontcolor_1('important');
    &.selected {
      @include sdp-fontcolor_1('important');
    }
    &.hover {
      @include sdp-fontcolor_1('important');
    }
  }
}
@media screen and (max-width: 1464px) {
  .sdp-calendar .calendar-width {
    width: 1119px !important;
  }
}
.org-select
  .el-scrollbar
  .el-select-dropdown__wrap
  .el-scrollbar__view
  .el-select-dropdown__item {
  font-size: 12px !important;
  font-family: NotoSansHans-Regular;
}
.sdp-calendar .calendar-width {
  // width: 1328px;
  .el-dialog__body {
    padding-left: 16px;
  }
}
.sdp-calendar .calendar-width-f {
  width: 1328px;
  .el-dialog__body {
    padding-left: 16px;
  }
}
.sdp-calendar .calendar-width-b {
  // width: 1474px;
  .el-dialog__body {
    padding-left: 16px;
  }
}
.sdp-calendar .el-dialog .el-dialog__body {
  padding: 16px;
}
.paramcommponents-select-pick {
  height: 20px !important;
  line-height: 20px !important;
  .el-input__icon {
    height: auto !important;
  }
  input {
    font-size: 12px !important;
    height: 18px !important;
    line-height: 18px !important;
  }
  .el-range-separator {
    line-height: 12px !important;
  }
}
.paramcommponents-select-style {
  input {
    height: 20px !important;
    line-height: 20px !important;
  }
  // input:active {
  //   border: 1px solid rgb(51, 51, 51) !important;
  // }
  // input:focus {
  //   border: 1px solid rgb(51, 51, 51) !important;
  // }
  // input:hover {
  //   border: 1px solid rgb(51, 51, 51) !important;
  // }
  .el-input {
    input {
      height: 20px;
    }
    .el-input__suffix {
      .el-input__suffix-inner {
        .el-select__caret {
          line-height: 20px;
        }
      }
    }
  }
}
.paramcommponents-cascader {
  margin-top: 7px;
  .el-cascader-node {
    height: 20px !important;
    line-height: 20px;
    text-overflow: clip;
    position: relative;
    padding: 0 30px 0 10px;
    font-size: 12px;
  }
  .el-icon-check + .el-cascader-node__label {
    padding: 0 30px 0 15px;
  }
  .el-checkbox {
    height: 20px;
    line-height: 20px;
  }
  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover {
    // background-color: #dee6f3 !important;
    // [data-theme='sdp-dark-blue'] & {
    //   .el-cascader-node__label {
    //     color: #e5ebf2 !important;
    //   }
    //   background-color: rgba(110, 130, 151, 0.2) !important;
    // }
  }
  .el-checkbox__inner:hover,
  .el-checkbox__inner:active,
  .el-checkbox__inner:focus,
  .el-checkbox__input.is-focus .el-checkbox__inner {
    border-color: var(--sdp-cszjdxbk) !important;
    background-color: var(--sdp-cszjdx0);
  }
  .is-active {
    // color: #606266 !important;
    // [data-theme='sdp-dark-blue'] & {
    //   color: #e5ebf2 !important;
    // }
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    border-color: var(--sdp-cszjdxtc) !important;
    background-color: var(--sdp-cszjdxtc) !important;
    &:before {
      top: 2px;
    }
  }
  .el-checkbox__input.is-checked .el-checkbox__inner::after{
    border-color: var(--sdp-cszjdxg);
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner::before{
    background-color: var(--sdp-cszjdxg);
  }
  .el-cascader-menu .el-cascader-node:not(.is-disabled).in-active-path {
    // color: #606266 !important;
    // [data-theme='sdp-dark-blue'] & {
    //   color: #a7b9cf !important;
    // }
  }
  .el-cascader-node__label {
    color: var(--sdp-cszjsz-wzs1);
    font-family: 'PingFangSC-Regular';
  }
  .el-checkbox__inner {
    width: 8px;
    height: 8px;
    margin: 5px 0;
    border-radius: 0;
    border-color: var(--sdp-cszjdxbk) !important;
    background-color: var(--sdp-cszjdx0) !important;
    &:after {
      width: 3px;
      height: 5px;
      left: 1px;
      top: -1px;
    }
    &:before {
      top: 2px;
    }
  }
}
.sdp-select-style {
  .el-select__caret {
    line-height: 20px;
  }
  .el-scrollbar {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.3) !important;
    }
  }
}
.bussiness-select .el-scrollbar {
  max-height: 300px;
  margin-top: 12px;
  margin-bottom: 12px;
  .el-scrollbar__wrap {
    overflow-y: scroll;
    overflow-x: hidden;
    margin-bottom: 0px !important;
  }
  .is-horizontal {
    display: none;
  }
  .el-select-dropdown__wrap .el-select-dropdown__list {
    padding: 0px;
  }
  // .el-select-dropdown__wrap .el-select-dropdown__list .selected {
  //   background: #dee6f3;
  // }
  // .el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
  //   background: #dee6f3;
  // }
  // .el-select-dropdown__wrap .el-select-dropdown__list .hover {
  //   background-color: #dee6f3 !important;
  // }
  // ::-webkit-scrollbar-track {
  //   background: white;
  // }
}
.sdp-dialog {
  .el-input--small .el-input__inner {
    font-size: 12px;
    //line-height: 12px;;
  }
}
.sdp-board-design,
.sdp-board-preview,
.sdp-grid-design,
.sdp-grid-preview,
.sdp-dialog,
.sdp-popover {
  .el-upload {
    input[type='file'] {
      display: none !important;
    }
  }
  .paramcomponent {
    .params-font {
      .el-radio-group:not(.is-disabled) {
        .el-radio {
          .el-radio__input {
            .el-radio__inner {
              width: 12px;
              height: 12px;
            }
          }
          .el-radio__label {
            font-size: 12px !important;
            color: var(--sdp-zjbxx-wzs);
          }
        }
        // .is-checked {
        //   .is-checked {
        //     .el-radio__inner {
        //       background-color: #333;
        //       border-color: #333;
        //       width: 12px;
        //       height: 12px;
        //       // background-color: red!important;
        //       // border-color: red!important;
        //     }
        //   }
        //   .el-radio__label {
        //     color: #444;
        //     font-size: 12px !important;
        //   }
        // }
      }
      .el-input {
        .el-input__inner {
          line-height: 12px;
          font-size: 12px;
          border-radius: 2px;
        }
      }
      .el-checkbox-group {
        .el-checkbox {
          .el-checkbox__label {
            font-size: 12px !important;
            line-height: normal !important;
          }
        }
      }
      .el-select {
        .el-input__inner {
          font-size: 12px !important;
          font-family: NotoSansHans-Regular;
        }
      }
    }
  }
  .el-upload__input {
    display: none;
  }
  .el-submenu__title {
    height: 37px !important;
    line-height: 37px !important;
    padding: 0 12px 0 12px !important;
    //  padding-left:26px
  }

  .el-submenu .el-menu-item {
    height: 37px !important;
    line-height: 37px !important;
  }

  .navbar .avatar-container .avatar-wrapper {
    margin-top: 1px;
    position: relative;
  }

  /*修改table的标题栏背景色*/

  .el-container {
    background-color: var(--sdp-szk-bjs);
  }

  /*修改进度条样式*/

  .el-progress-bar__outer {
    border-radius: 7px !important;
  }

  .el-progress-bar__inner {
    border-radius: 0 !important;
  }

  /*修改Sidebar鼠标移动的背景色*/

  .el-menu-item:hover {
    background-color: $elementPrimary !important;
  }

  .el-menu-item:focus,
  .el-menu-item:active,
  .el-menu-item:focus-within {
    background-color: $elementPrimary !important;
  }

  .el-submenu__title:hover {
    background-color: $elementPrimary !important;
  }

  img:hover {
    cursor: pointer;
  }

  // 修改sideBar收起状态li边距
  ul.el-menu.el-menu--popup.el-menu--popup-right-start {
    margin-left: 0px;
  }

  /*修改顶部导航样式 字体居中 选中颜色 底部线条颜色*/

  .el-menu--horizontal {
    border-bottom: 2px solid #f7f7f8 !important;
    margin-bottom: 15px !important;
  }

  .el-menu--horizontal > .el-menu-item {
    height: 47px !important;
    line-height: 47px !important;
  }

  // 修改单选框尺寸
  //  .el-checkbox__inner {
  //    width: 18px !important;
  //    height: 18px !important;
  //  }

  // 修改单选框选中状态
  //  .el-checkbox__inner::after {
  //    height: 10px !important;
  //    left: 6px !important;
  //  }

  /*弹出对话框样式修改*/

  /*修改左右边距*/

  .el-dialog__body {
    padding: 0 24px 20px;
  }
  .el-dialog__footer {
    padding-right: 24px;
  }

  /*修改标题居中*/

  .el-dialog__header {
    text-align: left;
    color: $color-main;
    font-size: 20px;
  }
  .el-dialog__title {
    color: var(--sdp-sztc-bts);
  }

  /*新定义的公共样式 start*/

  /*修改图标大小*/
  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  /*修改按钮间距*/

  .btn-space {
    margin-right: 10px;
  }

  .btn-left-space {
    margin-left: 10px;
  }

  .btn-space-top30 {
    margin-top: 10px;
  }

  .btn-space-bottom {
    margin-bottom: 20px;
  }

  /*修改按钮颜色--确定*/

  .btn-sure-color {
    background-color: $elementPrimary;
    border-color: $elementPrimary;
  }

  /*修改按钮颜色--取消*/

  .btn-cancel-color {
    border-color: rgb(113, 113, 114);
  }

  /*修改对话框内容居中显示*/

  .tool-dialog-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /*新定义的公共样式 end*/

  //暂时性解决diolag 问题 https://github.com/ElemeFE/element/issues/2461
  //  .el-dialog {
  //    transform: none;
  //    left: 0;
  //    position: relative;
  //    margin: 0 auto;
  //  }

  //element ui upload
  .upload-container {
    .el-upload {
      width: 100%;

      .el-upload-dragger {
        width: 100%;
        height: 200px;
      }
    }
  }

  .el-upload {
    input[type='file'] {
      display: none !important;
    }
  }

  .el-upload__input {
    display: none;
  }

  /*表格文字居中*/

  //  .el-table .cell {
  //    display: -webkit-flex;
  //    justify-content: space-around;
  //  }

  //  /*修改表格标题行文字居中*/
  //  .el-table th>.cell {
  //    text-align: center;
  //  }

  /*底部内容区域背景色及边框间距*/

  .bottom-cotainer-bg {
    background-color: #efefef;

    .bottom-cotainer-box {
      border-top: 1px solid #fff;
      border: 10px solid #efefef;
      border-radius: 5px;
    }
  }

  /*按钮宽度*/

  .btn-width {
    //width: 90px;
    //font-size: 14px;
    //padding: 10px 0 !important;
  }

  /*输入框宽度*/

  .input-width {
    width: 200px !important;
  }

  .input-width240 {
    width: 240px !important;
  }

  /*下拉框宽度*/

  .select-width {
    width: 120px;
  }

  /*顶部间距*/

  .btn-space-top {
    margin-top: 15px;
  }

  .btn-space-left {
    margin-left: 6px;
  }

  /* 对话框文字默认不换行 */
  .el-dialog .el-dialog__body .el-form-item .el-form-item__label {
    white-space: nowrap;
  }

  //弹出框底部按钮组
  .dialog-footer {
    margin-top: 20px;
    text-align: right;
  }

  /*图标大小*/

  .icon-size {
    width: 18px;
    height: 18px;
  }

  /*左边菜单子节点*/

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }

  .dropdown-box {
    .sidebar {
      text-align: center;
      display: block !important;
      padding-right: 5px;
    }
  }

  //暂时性解决diolag 问题 https://github.com/ElemeFE/element/issues/2461
  .el-dialog {
    transform: none;
    left: 0;
    position: relative;
    margin: 15vh auto 0;
    &.is-fullscreen {
      margin-top: 0;
    }
  }

  //表格操作栏按钮专用
  .el-button--specific {
    padding: 0px !important;
    vertical-align: middle;
  }

  //查询框居右
  .search-place {
    float: right;
    margin-right: 10px;
  }

  /* 重新订制element颜色变量 */

  /* 主题色 */

  // $--color-primary: $elementPrimary;
  /* 改变 icon 字体路径变量，必需 */

  .el-menu--collapse > .el-menu-item [class^='el-icon-'],
  .el-menu--collapse > .el-submenu > .el-submenu__title [class^='el-icon-'] {
    font-size: 12px;
  }

  .el-menu--popup {
    min-width: 120px;
  }

  .el-menu--vertical {
    left: 31px !important;

    //  top: 49px !important;
    .el-menu-item {
      height: 38px;
      line-height: 38px;
    }
  }
}
.sdp-paramsetting .el-dialog .el-dialog__body {
  padding: 24px 24px 24px;
}
.sdp-board-design,
.sdp-board-preview,
.sdp-grid-design,
.sdp-grid-preview,
.sdp-dialog,
.sdp-popover,
.board-design-pop,
.sdp-grid-preview-popover {
  font-family: $default-font-family;
  //background-color: var(--sdp-gjys-bjs) !important;

  .el-select .el-input.is-focus .el-input__inner {
    border-color: $color-main;
  }

  .el-select .el-input__inner:focus {
    border-color: $color-main-hover;
  }

  .el-select-dropdown__item.selected {
    color: $color-main !important;
  }
  // input类
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus,
  .el-textarea__inner:focus,
  .el-input-number__decrease:hover:not(.is-disabled)
    ~ .el-input
    .el-input__inner:not(.is-disabled),
  .el-input-number__increase:hover:not(.is-disabled)
    ~ .el-input
    .el-input__inner:not(.is-disabled) {
    border-color: $color-main-hover;
  }

  .el-input-number__decrease:hover,
  .el-input-number__increase:hover {
    color: $color-main-hover;
  }

  // tab类
  .el-tabs {
    .el-tabs__header {
      .el-tabs__item {
        color: var(--sdp-cszjsz-wzs1);
      }
      .el-tabs__item.is-active,
      .el-tabs__item:hover,
      .el-tabs__new-tab:hover {
        color: $color-main;
      }
    }
    .el-tabs__nav-wrap::after {
      background-color: var(--sdp-cszj-bkfgx);
    }
    .el-tabs__active-bar {
      background-color: $color-main;
    }
  }

  // 级联选择器
  .el-cascader-node.in-active-path,
  .el-cascader-node.is-active,
  .el-cascader-node.is-selectable.in-checked-path {
    color: $color-main;
    font-weight: 400;
  }

  // 滑条类
  .el-slider__bar {
    background-color: $color-main;
  }

  .el-slider__button {
    border-color: $color-main;
  }

  // 下拉按钮类
  .el-dropdown-menu__item:focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: $color-main-bac;
    color: $color-main;
  }
}

.sdp-theme-calendar {
  background-color: var(--sdp-ycsz-srk-bgs) !important;
  border: 1px solid var(--sdp-ycsz-srk-bcs) !important;
  .el-date-table td.in-range div {
    @include param-week-box-background_0();
  }
  .el-date-table td div span {
    color: var(--sdp-ycsz-glwzs) !important;
  }
  .el-picker-panel__content {
    background-color: var(--sdp-ycsz-srk-bgs) !important;
    border: 1px solid var(--sdp-ycsz-srk-bcs) !important;
  }
  .el-picker-panel__icon-btn {
    color: var(--sdp-ycsz-glwzs) !important;
  }
  .el-date-table th {
    border-color: #293644;
  }
}
// 表格预览 表头控制popover
.sdp-grid-preview-popover {
  .el-popover__title {
    font-family: NotoSansHans-Regular;
    font-weight: 800;
    font-size: 20px;
    letter-spacing: 0;
    line-height: 20px;
    margin-left: 12px;
    margin-top: 24px;
    color: var(--sdp-xxbt2);
  }

  .btn-opera {
    text-align: right;
    margin-right: 19px;

    .el-button + .el-button {
      margin-left: 12px;
    }

    .sdp-table-header-ensure {
      border-radius: 2px;
      background: $color-preview;
      border-color: transparent;
      span {
        color: #ffffff;
      }
      &:hover {
        border-color: $color-preview !important;
        background-image: linear-gradient(
          -180deg,
          $color-preview,
          $color-preview
        ) !important;
      }
      [data-theme='sdp-dark-blue'] & {
        background: #553cce;
        span {
          color: #ffffff;
        }
      }
      [data-theme='sdp-dark-blue'] &:hover {
        border-color: #553cce !important;
        background-image: linear-gradient(-180deg, #553cce, #553cce) !important;
        span {
          color: #ffffff;
        }
      }
    }

    .sdp-table-header-cancel {
      margin-left: 12px;
      border-radius: 2px;
      background: #eef0f5;
      border-color: #eef0f5;
      span {
        color: $color-preview;
      }

      &:hover {
        border-color: #eef0f5 !important;
        background-image: linear-gradient(
          -180deg,
          $color-preview,
          $color-preview
        ) !important;
        span {
          color: #fff;
        }
      }
      [data-theme='sdp-dark-blue'] & {
        background: #24303e;
        border-color: #24303e;
        span {
          color: #553cce;
        }
      }
      [data-theme='sdp-dark-blue'] &:hover {
        background: #24303e !important;
        border-color: #24303e !important;
        span {
          color: #553cce;
        }
      }
    }
  }

  .el-tree-node__content {
    height: 38px;
    line-height: 38px;
  }
  .el-tree-node:focus > .el-tree-node__content {
    background-color: transparent;
  }

  .el-tree-node__content:hover {
    li.item {
      background-color: var(--sdp-qxnnds);
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
      border-radius: 2px;
    }
  }
}

// 订阅计划
//.sdp-task-dialog {
//  width:640px !important;
//  height:480px !important;
//  /deep/ .el-dialog__header{
//    padding: 24px 21px;
//    text-align: left;
//    .el-dialog__title{
//      font-size: 20px;
//      color: $color-preview;
//      letter-spacing: 0;
//      line-height: 20px;
//      font-weight: 800;
//    }
//    .el-dialog__headerbtn{
//      color: #B6C1D6;
//      font-size: 24px;
//    }
//  }
//  /deep/ .el-dialog__body{
//    padding: 0 21px 24px;
//    .input {
//      display: inline-block;
//      .el-input__inner {
//        background: #FFFFFF;
//        border: 1px solid #DDDDDD;
//        border-radius: 2px;
//        border-radius: 2px;
//      }
//    }
//    .el-table {
//      .ascending .sort-caret.ascending {
//        border-bottom-color: $color-preview;
//      }
//      .descending .sort-caret.descending {
//        border-top-color: $color-preview;
//      }
//      .warning-row {
//        background: #F7F7F7;
//        box-shadow: inset 0 -1px 0 0 #EBEEF5;
//      }
//      .el-table__row{
//        height: 44px;
//      }
//      .el-table__header-wrapper {
//        tr {
//          height: 50px;
//          th{
//            background: #F3F5F7;
//            box-shadow: inset 0 -1px 0 0 #E8E8E8;
//            border: none;
//            font-size: 14px;
//            color: #222222;
//          }
//        }
//      }
//    }
//    // 多选框，单选框类
//    .el-checkbox__inner:hover,
//    .el-checkbox__inner:focus,
//    .el-radio__inner:hover {
//      border-color: $color-preview;
//    }
//    .el-checkbox__input:not(.is-disabled).is-checked  .el-checkbox__inner,
//    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
//      background-color: $color-preview;
//      border-color: $color-preview;
//    }
//    .el-radio__input:not(.is-disabled).is-checked .el-radio__inner {
//      background-color: $color-preview;
//      border-color: $color-preview;
//    }
//    .el-radio__input:not(.is-disabled).is-checked + .el-radio__label {
//      color: $color-preview;
//    }
//
//    .el-checkbox__input.is-focus {
//      .el-checkbox__inner {
//        border-color: $color-preview;
//      }
//    }
//  }
//}
/*图标大小*/

.icon-size {
  width: 18px;
  height: 18px;
}

/* 重新订制element颜色变量 */

/* 主题色 */

$--color-primary: $elementPrimary;
/* 改变 icon 字体路径变量，必需 */

$--font-path: '~element-ui/lib/theme-chalk/fonts';

// 调转默认确认取消按钮
.el-message-box__btns {
  display: flex;
  flex-direction: row-reverse;

  > .el-button--small {
    margin-left: 10px;
  }

  > .el-button--primary {
    margin-left: 0;
  }
}
micro-app-body[sdp-only-Index="1"] .el-message-box__btns{
  flex-direction: row;
  justify-content: end;
}
micro-app-body[sdp-only-Index="1"] .el-dialog__footer{
  .el-button--primary {
    float: right;
    margin-left: 10px;
  }
}
micro-app-body[sdp-only-Index="1"] {
  .el-button.el-button--sdp-cancel, .el-button.el-button--default:not(.el-button--primary):not(.el-button--sdp-ensure) {
    --button-background-color: var(--sdp-qxnnds);
    --button-color: var(--sdp-zs);
    --button-border-color: var(--sdp-qxnnds);
    &:hover, &:focus, &:active {
      opacity: 0.8;
    }
  }
}

//.el-message {
//  top: 100px !important;
//}
.sdp-title-start {
  .el-dialog__header {
    padding-bottom: 25px;
    padding-left: 20px;
  }
}
.sdp-paramsetting .el-dialog__header {
  padding: 24px 24px 0px 24px;
}

.el-button.el-button--sdp-cancel, .el-button.el-button--sdp-ensure{
  border-radius: 2px;
  transition: all 100ms;
  span {
    transition: all 100ms;
  }
}

// location
// .sdp-popover {
//   // .locationTree {
//   //   /deep/ .el-tree-node__content{
//   //     height:37px;
//   //   }
//   // }
//   // 修改单选框尺寸
//   // /deep/ .el-checkbox__inner {
//   //   width: 18px !important;
//   //   height: 18px !important;
//   // }
//   // 修改单选框选中状态
//   // /deep/ .el-checkbox__inner::after {
//   //   height: 10px !important;
//   //   left: 6px !important;
//   // }
// }

// 修改元素及参数组件按钮tooltip样式
.el-tooltip__popper.databoard-btn-tooltip {
  padding: 3px 6px;
  border-radius: 1px;
  color: #F6F6F6 !important;
  background: #121D33 !important;
  //color: var(--sdp-wzgl) !important;
  //background: var(--sdp-cy-bjs) !important;
  //@include sdp-mixin-style(
  //  $type: (
  //    background: (
  //      toolTipBgc: true,
  //    ),
  //  )
  //);
  .popper__arrow {
    border: none;
  }
  .popper__arrow::after {
    border-bottom-color: #121D33 !important;
    //border-bottom-color: var(--sdp-cy-bjs) !important;
    //@include sdp-mixin-style(
    //  $type: (
    //    borderBottomColor: (
    //      toolTipBgc: true,
    //    ),
    //  )
    //);
  }
}
.el-tooltip__popper {
  word-break: break-word !important;
}

// 订阅计划
//.sdp-task-dialog {
//  width:640px !important;
//  height:480px !important;
//  /deep/ .el-dialog__header{
//    padding: 24px 21px;
//    text-align: left;
//    .el-dialog__headerbtn{
//      color: #B6C1D6;
//      font-size: 24px;
//    }
//  }
//  /deep/ .el-dialog__body{
//    padding: 0 21px 24px;
//    .input {
//      display: inline-block;
//      .el-input__inner {
//        background: #FFFFFF;
//        border: 1px solid #DDDDDD;
//        border-radius: 2px;
//        border-radius: 2px;
//      }
//    }
//    .el-table {
//      .ascending .sort-caret.ascending {
//        border-bottom-color: #455964;
//      }
//      .descending .sort-caret.descending {
//        border-top-color: #455964;
//      }
//      .warning-row {
//        background: #F7F7F7;
//        box-shadow: inset 0 -1px 0 0 #EBEEF5;
//      }
//      .el-table__row{
//        height: 44px;
//      }
//      .el-table__header-wrapper {
//        tr {
//          height: 50px;
//          th{
//            background: #F3F5F7;
//            box-shadow: inset 0 -1px 0 0 #E8E8E8;
//            border: none;
//            font-size: 14px;
//            color: #222222;
//          }
//        }
//      }
//    }
//    // 多选框，单选框类
//    .el-checkbox__inner:hover,
//    .el-checkbox__inner:focus,
//    .el-radio__inner:hover {
//      border-color: #455964;
//    }
//    .el-checkbox__input:not(.is-disabled).is-checked  .el-checkbox__inner,
//    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
//      background-color: #455964;
//      border-color: #455964;
//    }
//    .el-radio__input:not(.is-disabled).is-checked .el-radio__inner {
//      background-color: #455964;
//      border-color: #455964;
//    }
//    .el-radio__input:not(.is-disabled).is-checked + .el-radio__label {
//      color: #455964;
//    }
//
//    .el-checkbox__input.is-focus {
//      .el-checkbox__inner {
//        border-color: #455964;
//      }
//    }
//  }
//}


.loading-icon-content .sdp-loading-info {
  display: inline-block;
  width: 50px;
  height: 50px;
  position: relative;
  @include loading_background_track();
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center center;
  &::after {
    @include loading_background_car();
    position: absolute;
    content: '';
    z-index: 10;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    animation: animateRotate 1s steps(12, end) infinite;
    -webkit-animation: animateRotate 1s steps(12, end) infinite;
  }
}
@keyframes animateRotate {
  0% {
    transform: rotateZ(0);
    -webkit-transform: rotateZ(0);
  }
  100% {
    transform: rotateZ(360deg);
    -webkit-transform: rotateZ(360deg);
  }
}
.remarkTooltip {
  word-break: break-all !important;
  max-width: 900px;
}
.remarkTooltip-hide {
  display: none !important;
}
.remark-angle {
  z-index: 999;
  position: absolute;
  left: 0px;
  top: 0px;
  border-top: 20px solid #553cce;
  border-right: 20px solid transparent;
}
.sdp-dataSet-dialog {
  background-color: var(--sdp-tb-bj);
  .el-dialog__title {
    background-color: var(--sdp-tb-bj);
  }
}

.sdp-location-quick-popover-style {
  padding: 0px !important;
  color: var(--sdp-bg-lhtwz) !important;
  @include sdp-mixin-style(
    $type: (
      borderColor: (
        'borderColor1': true,
      ),
    )
  );
  .scrollbar-style {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  .popper__arrow {
    @include sdp-borderColor_1('bottom');
    &::after {
      border-top-color: var(--sdp-bg-lhtwz) !important;
      border-bottom-color: var(--sdp-bg-lhtwz) !important;
    }
  }
}
[data-theme='sdp-dark-blue'] {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
}
// 解决开启大屏模式时闪现白条的问题
body[data-theme='sdp-classic-white'] {
  background-color: #fff;
}
body[data-theme='sdp-dark-blue'] {
  background-color: #1f2a37;
}
body[data-theme='sdp-deep-blue'] {
  background-color: #121D33;
}

.el-popper .sdp-location-el-select-option {
  width: 100%;
  font-size: 12px;
  height: 20px;
  line-height: 20px;

  &.selected {
    font-weight: 700;
  }
}

.sdp-screen-tool-material {
  padding: 0 0 !important;
  border: none !important;
  .tool-material-content {
    width: 100%;
    height: 276px;
    display: flex;
    padding: 24px 0;
    .tool-material-content-menu {
      height: 100%;
      display: inline-block;
      overflow: auto;
      border-right: 1px solid var(--sdp-cszj-bkfgx);
      .tool-material-content-menu-item {
        width: 100%;
        height: 28px;
        line-height: 28px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: var(--sdp-cszj-tgl);
        text-indent: 17px;
        transition: all 150ms ease;
        cursor: pointer;
        &:hover {
          background-color: var(--sdp-ycsz-hgls);
        }
      }
      .active {
        background-color: var(--sdp-ycsz-hgls);
        color: var(--sdp-zs);
      }
    }
    .tool-material-content-element {
      height: 100%;
      display: inline-block;
      overflow: auto;
      .tool-material-content-element-item {
        display: inline-block;
        margin: 12px 0 0 12px;
        background-color: transparent;
        cursor: pointer;
        border: 1px solid var(--sdp-cszj-bkfgx);
      }
    }
  }
  // .popper__arrow {
  //   @include sdp-borderColor_4('arrow');
  //   &::after {
  //     @include sdp-borderColor_4('arrow');
  //   }
  // }
  &.popover_material-library {
    .tool-material-content-menu {
      width: 108px;
      padding: 6px 0;
    }
    .tool-material-content-element {
      width: calc(100% - 108px);
    }
    .tool-material-content-element-item {
      width: 88px;
      height: 60px;
    }
  }
  &.popover_element-title {
    .tool-material-content {
      height: 351px;
    }
    .tool-material-content-menu {
      width: 229px;
      padding: 0 16px;
    }
    .tool-material-content-element-item {
      width: 237px;
      height: 85px;
    }
    .tool-material-content-element {
      width: calc(100% - 229px);
      padding: 0 4px;
    }
  }
}
.sdp-select-popper-wrapper {
  .el-scrollbar {
    width: 100%;
    .el-select-dropdown__wrap {
      width: calc(100% + 6px);
      .el-select-dropdown__item {
        width: 100% !important;
      }
    }
  }
}
.sdp-remark-dialog {
  .el-tabs__header {
    [data-theme='sdp-dark-blue'] & {
      border-bottom: 1px solid #273341 !important;
    }

    .is-active {
      [data-theme='sdp-dark-blue'] & {
        border: 1px solid #273341 !important;
        border-bottom-color: #1f2a37 !important;
      }
    }
  }
}

.el-scrollbar__thumb {
  background-color: rgba(144, 147, 153, 0.3) !important;
}
.input_in_select {
  height: 34px;
  padding: 0px 20px 6px;
}

.sdp-auto-hide-input-number {
  input {
    text-align: left;
  }
  .el-input-number {
    width: 100%;
  }
  .el-input-number.is-controls-right .el-input__inner {
    padding-left: 15px;
    padding-right: 10px;
  }
  // %
  .el-input {
    &:after {
      content: '%';
      position: absolute;
      right: 6px;
      top: 0;
      font-size: 12px;
    }
  }

  // 增减按钮 默认隐藏
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }

  // 增减按钮 鼠标悬浮时显示
  &:hover {
    .el-input-number__decrease,
    .el-input-number__increase {
      display: block;
    }
  }
}
.vue-grid-layout .vue-grid-item.vue-grid-placeholder{
  background: skyblue;
  opacity: 0.2;
}
// ---------------------新的主题相关内容--------------------------------------------
  // 默认的，带边框的
  .el-dialog,
  .el-popover,
  .el-popper{
    --input-border-color: var(--sdp-srk-bks);
    --input-background-color: transparent;
    --input-color: var(--sdp-fhwzs);
    --input-placeholder-color: var(--sdp-srk-bxwzs);
    --input-disabled-background-color: transparent;
    --input-disabled-color: var(--sdp-jys);
    --input-disabled-border-color: var(--sdp-jys);
  }
  // 编辑界面，带背景的
  .el-dialog.is-fullscreen,
  .sdp-grid-design {
    --input-border-color: var(--sdp-ycsz-srk-bcs);
    --input-background-color: var(--sdp-ycsz-srk-bgs);
    --input-color: var(--sdp-fhwzs);
    --input-placeholder-color: var(--sdp-ycsz-srtswz);
    --input-disabled-background-color: var(--sdp-srjybjs);
    --input-disabled-color: var(--sdp-jys);
    --input-disabled-border-color: var(--sdp-ycsz-srk-bcs);
  }
  // 顶部输入框
  .top-input {
    --input-border-color: var(--sdp-fs2);
    --input-background-color:  var(--sdp-tb-srbj);
    --input-color: var(--sdp-fhwzs);
    --input-placeholder-color: var(--sdp-cszjsz-wzs1);
    --input-disabled-background-color: var(--sdp-tb-srbj);
    --input-disabled-color: var(--sdp-jys);
    --input-disabled-border-color: var(--sdp-fs2);
  }
body .sdp-theme-container, micro-app-body .sdp-theme-container, .sdp-classic-white, .sdp-dark-blue, .sdp-deep-blue, [sdp-eng-theme-type]{
  // 注意：以下的样式，尽量以最低匹配度写，不要定制
  // -------------------checkbox\radio\相关样式--------开始------------------------------------------
  .el-radio, .el-radio-group {
    .el-radio__label{
      color: var(--sdp-fx-mrwzs);
      font-size: 12px;
    }
    &.is-checked, .el-radio__input.is-checked{
      .el-radio__label, &+.el-radio__label{
        color: var(--sdp-zs);
      }
      .el-radio__inner {
        background: var(--sdp-zs);
        border-color: var(--sdp-zs);
        &::after {
          background: var(--sdp-szk-bjs);
        }
      }
      &.is-disabled{
        .el-radio__input .el-radio__inner{
          background: var(--sdp-jys);
          border-color: var(--sdp-jys);
          &::after{
            background: var(--sdp-szk-bjs);
            border-color: var(--sdp-szk-bjs);
          }
        }
      }
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner{
      background: var(--sdp-zs);
      border-color: var(--sdp-zs);
      color: #fff;
    }
    &.is-disabled{
      .el-radio__label{
        color: var(--sdp-jys);
      }
      .el-radio__input .el-radio__inner{
        // background: transparent;
        background: var(--sdp-check-disable-fill);
        border-color: var(--sdp-jys);
      }
    }
    &.el-radio.is-disabled{
      .el-radio__label{
        color: var(--sdp-jys);
      }
    }
    .el-radio__inner {
      border-color: var(--sdp-xk-bks);
      background: var(--sdp-xk-bcs);
    }
  }
  .el-checkbox{
    .el-checkbox__label{
      color: var(--sdp-fx-mrwzs);
      font-size: 12px;
    }
    .el-checkbox__inner{
      background: var(--sdp-xk-bcs);
      border-color: var(--sdp-xk-bks);
    }
    .el-checkbox__input.is-checked,
    .el-checkbox__input.is-indeterminate {
      .el-checkbox__inner {
        background: var(--sdp-zs);
        border-color: var(--sdp-zs);
      }
      &+.el-checkbox__label{
        color: var(--sdp-zs);
      }
      &.is-disabled{
        .el-checkbox__inner{
          background: var(--sdp-jys);
          border-color: var(--sdp-jys);
          &::after{
            border-color: var(--sdp-szk-bjs);
          }
        }
      }
    }
    &.is-disabled{
      .el-checkbox__input+.el-checkbox__label{
        color: var(--sdp-jys);
      }
      .el-checkbox__input .el-checkbox__inner{
        // background: transparent;
        background: var(--sdp-check-disable-fill);
        border-color: var(--sdp-jys);
      }
    }
  }
  // -------------------checkbox相关样式--------结束------------------------------------------

  // -------------------tabs相关样式--------开始------------------------------------------
  .el-tabs.el-tabs--card {
    > .el-tabs__header {
      border-color: var(--sdp-cszj-bkfgx);

      .el-tabs__item {
        background: var(--sdp-szk-bjs);
        border: 1px solid transparent;
        border-bottom-color: var(--sdp-cszj-bkfgx);
        padding: 0 8px;
        line-height: 36px;
        height: 36px;
        color: var(--sdp-xxbt2);
        font-size: 12px;
      }
      .el-tabs__item.is-active {
        border-left-color: var(--sdp-cszj-bkfgx);
        border-top-color: var(--sdp-cszj-bkfgx);
        border-right-color: var(--sdp-cszj-bkfgx);
        border-bottom-color: var(--sdp-szk-bjs);
        color: var(--sdp-zs);
      }
      .el-tabs__item.is-active:first-child {
        border-left-width: 1px;
        border-left-style: solid;
      }
    }
    > .el-tabs__header .el-tabs__nav {
      border: none;
    }
  }
  // -------------------taba相关样式--------结束------------------------------------------

  // -------------------popover相关样式--------开始----------------------------------------
  &.el-popper {
    .el-autocomplete-suggestion__list {
      li {
        padding: 0;
        color: var(--sdp-cszjsz-wzs1);
        background: var(--sdp-szk-bjs);
        text-align: center;
        &:hover {
          background: var(--sdp-ycsz-hgls);
          color: var(--sdp-ycsz-glwzs);
        }
      }
    }
    .popover-title {
      color: var(--sdp-sztc-bts);
      font-size: 18px;
      margin-bottom: 10px;
      line-height: 24px;
      height: 24px;
    }
    .el-input__inner {
      border: 1px solid var(--sdp-srk-bks);
      background: var(--sdp-ycsz-srk-bgs);
      color: var(--sdp-nngl);
    }
    // 取色盘
    .el-color-picker__trigger {
      border-color: var(--sdp-srk-bks);
      height: 32px;
      width: 32px;
    }
    &[x-placement^='left'] .popper__arrow {
      border-left-color: var(--sdp-ycfgx);
      &::after {
        border-left-color: var(--sdp-szk-bjs);
      }
    }
    &[x-placement^='right'] .popper__arrow {
      border-right-color: var(--sdp-ycfgx);
      &::after {
        border-right-color: var(--sdp-szk-bjs);
      }
    }
    &[x-placement^='top'] .popper__arrow {
      border-top-color: var(--sdp-ycfgx) !important;
      &::after {
        border-top-color: var(--sdp-szk-bjs) !important;
      }
    }
    &[x-placement^='bottom'] .popper__arrow {
      border-bottom-color: var(--sdp-ycfgx) !important;
      &::after {
        border-bottom-color: var(--sdp-szk-bjs) !important;
      }
    }
  }
  // -------------------popover相关样式--------结束----------------------------------------

  // -------------------input相关样式--------开始----------------------------------------
  .el-input__inner {
    border-radius: 2px;
  }
  &,
  &.el-popper.is-multiple,
  .sdp-grid-design{
    ::-webkit-input-placeholder,
    input::placeholder {
      color: var(--input-placeholder-color) !important;
    }
    .el-date-editor .el-range-separator {
      color: var(--input-color);
    }
    .el-select:hover,
    .el-select:hover .el-input {
      .el-input__inner {
        border-color: var(--sdp-zs);
      }
    }
    .el-input,
    .el-input-number,
    .el-select .el-input,
    .el-textarea,
    & {
      .el-input__inner,
      .el-range-input,
      .el-date-editor,
      .el-textarea__inner {
        border-color: var(--input-border-color);
        background: var(--input-background-color);
        color: var(--input-color);
        &:hover,
        &:focus,
        &:active {
          border-color: var(--sdp-zs);
        }
      }
      .el-input__icon,
      .el-input-number__increase,
      .el-input-number__decrease {
        color: var(--input-color);
        &.is-disabled {
          color: var(--input-disabled-color);
        }
      }
      &.is-controls-right,
      & {
        .el-input-number__increase {
          border-radius: 0 2px 0 0;
          border-color: var(--input-border-color);
          background: var(--input-background-color);
        }
        .el-input-number__decrease {
          border-radius: 0 0 2px 0;
          border-color: var(--input-border-color);
          background: var(--input-background-color);
        }
      }
      .el-input-group__append,
      .el-input-group__prepend {
        background: var(--input-background-color);
        padding: 0 8px;
        width: fit-content;
      }
      &.is-disabled{
        .el-input__inner, .el-textarea__inner{
          border-color: var(--input-disabled-border-color);
          background: var(--input-disabled-background-color);
          color: var(--input-disabled-color);
          &:hover, &:focus, &:active{
            border-color: var(--input-disabled-border-color);
          }
        }
        .el-input__icon {
          color: var(--input-disabled-color);
        }
        .el-input-number__increase, .el-input-number__decrease, .el-input-group__append, .el-input-group__prepend{
          border-color: var(--input-disabled-border-color);
          background: var(--input-disabled-background-color);
          color: var(--input-disabled-color);
        }
      }
    }
    .el-cascader > .el-input.is-focus {
      .el-input__inner {
        border-color: var(--sdp-zs);
      }
    }
    .el-input-group--append {
      border: 1px solid var(--input-border-color);
      border-radius: 2px;
      &:hover,
      &:focus,
      &:active {
        border-color: var(--sdp-zs);
      }
      .el-input__inner,
      .el-input-group__append {
        border: 0;
      }
      &.is-disabled{
        border-color: var(--input-disabled-border-color);
      }
    }
  }
  // -------------------input相关样式--------结束----------------------------------------

  // -------------------下拉框、select、dropdown等相关样式--------开始----------------------------------------
  &.el-popper,
  &.el-popper.is-multiple,
  &.el-popover.el-select-dropdown.is-multiple{
    .el-select-dropdown__item,
    .el-dropdown-menu__item,
    .el-cascader-node:not(.is-disabled) {
      color: var(--sdp-cszjsz-wzs1);
      &:hover,
      &:focus,
      &:active,
      &.hover {
        background: var(--sdp-ycsz-hgls);
        color: var(--sdp-ycsz-glwzs);
      }
      &.selected,
      &.selected.hover {
        color: var(--sdp-zs);
        background: var(--sdp-ycsz-hgls);
        font-weight: 700;
      }
    }
    &.preview-select{
      .el-select-dropdown__item,
      .el-dropdown-menu__item,
      .el-cascader-node:not(.is-disabled) {
        &.selected,
        &.selected.hover {
          color: var(--sdp-ycsz-glwzs);
        }
      }
    }
    .el-cascader-node, .el-select-dropdown__item{
      &.is-disabled{
        color: var(--sdp-jys);
      }
    }
    .el-cascader-menu {
      border-color: var(--sdp-ycfgx);
      .el-cascader-node:not(.is-disabled).in-active-path {
        color: var(--sdp-zs);
      }
    }
  }
  // -------------------下拉框、select、dropdown等相关样式--------开始----------------------------------------

  // ----------------------------各类弹框的背景颜色-----开始--------------------------
  &.el-dialog,
  .el-dialog,
  &.el-popper,
  &.el-message-box,
  &.el-popper.is-multiple,
  &.el-color-dropdown.el-color-picker__panel {
    background: var(--sdp-szk-bjs);
    border-color: var(--sdp-ycfgx);
    color: var(--sdp-sztc-srkbt);
    .el-icon-close {
      color: var(--sdp-srk-bxwzs);
      &:hover, &:focus, &:active {
        color: var(--sdp-zs);
      }
    }
    .el-dialog__body{
      color: var(--sdp-sztc-srkbt);
    }
  }
  &.el-popper,
  &.el-popper.is-multiple{
    .el-date-range-picker__content.is-left {
      border-color: var(--sdp-cszj-bkfgx);
    }
    &[x-placement^='top-start'] .popper__arrow {
      border-top-color: var(--sdp-ycfgx) !important;
      &::after {
        border-top-color: var(--sdp-szk-bjs) !important;
      }
    }
    // &[x-placement^=right] .popper__arrow{
    //   border-right-color: var(--sdp-ycfgx);
    //   &::after{
    //     border-right-color: var(--sdp-szk-bjs);
    //   }
    // }
    &[x-placement^='bottom-start'] .popper__arrow {
      border-bottom-color: var(--sdp-ycfgx) !important;
      &::after {
        border-bottom-color: var(--sdp-szk-bjs) !important;
      }
    }
  }
  // ----------------------------各类弹框的背景颜色-----结束--------------------------

  // ----------------------------el-date-range-picker-----开始--------------------------
  .el-date-range-picker,
  .el-date-picker,
  &.el-date-range-picker,
  &.el-date-picker {
    .el-date-table {
      th {
        border-color: var(--sdp-cszj-bkfgx);
        color: var(--sdp-rl-wzjys);
      }
      td.next-month,
      td.prev-month {
        color: var(--sdp-rl-wzjys);
      }
      td.today span {
        color: var(--sdp-rl-hzts);
        border: 1px solid var(--sdp-rl-bxys);
      }
      td.in-range div,
      td.in-range div:hover,
      &.is-week-mode .el-date-table__row.current div,
      &.is-week-mode .el-date-table__row:hover div,
      td.in-range.today span {
        background: var(--sdp-rl-hzts);
        color: var(--sdp-wzgl);
        border: none;
      }
      td.end-date span,
      td.start-date span {
        background: transparent;
      }
      td.available{
        &.current span{
          background-color: var(--sdp-rl-hzts);
          color: var(--sdp-nngl);
        }
        &:hover{
          color: var(--sdp-zs);
        }
      }
      td.disabled{
        div {
          background: transparent;
          color: var(--sdp-rl-wzjys);
        }
      }
    }
    .el-date-picker__header-label{
      &:hover{
        color: var(--sdp-zs);
      }
    }
    &.el-picker-panel,
    .el-date-picker__header-label,
    .el-picker-panel__icon-btn {
      color: var(--sdp-cszjsz-wzs1);
    }
    .el-time-panel {
      color: var(--sdp-bg-lhtwz) !important;
      background: var(--sdp-szk-bjs);
      .has-seconds::before {
        border-color: var(--sdp-cszj-srbks);
      }
      .has-seconds::after {
        border-color: var(--sdp-cszj-srbks);
      }
      border-color: var(--sdp-cszj-srbks);
      .el-time-panel__content
        .el-time-spinner
        .el-time-spinner__wrapper
        .el-scrollbar__wrap
        .el-scrollbar__view {
        .active {
          color: var(--sdp-nngl);
        }
        .el-time-spinner__item:not(.active) {
          color: var(--sdp-cszjsz-wzs1);
        }
      }
      .el-time-panel__content
        .el-time-spinner
        .el-time-spinner__wrapper
        .el-scrollbar__wrap
        .el-scrollbar__view
        .el-time-spinner__item:hover {
        background: var(--sdp-fs1);
      }
      .el-time-panel__footer {
        border-color: var(--sdp-cszj-srbks);
        .cancel {
          color: var(--sdp-tsgls);
        }
        .confirm {
          color: var(--sdp-zs);
        }
      }
    }
  }
  // ----------------------------el-date-range-picker-----结束--------------------------

  // ----------------------------el-message-----开始--------------------------
  &.el-message-box {
    .el-message-box__headerbtn:hover {
      .el-icon-close {
        color: var(--sdp-zs);
      }
    }
    .el-message-box__title {
      color: var(--sdp-sztc-bts);
    }
    .el-message-box__content {
      .el-message-box__message {
        color: var(--sdp-xxbt2);
      }
    }
    .el-message-box__content .el-message-box__status{
      display: none;
      &+.el-message-box__message{
        padding: 0;
        color: var(--sdp-xxbt2);
      }
    }
  }
  // ----------------------------el-message-----结束--------------------------

  // ----------------------------el-tree-----开始--------------------------
  &.el-dialog .el-tree, &.el-popper .el-tree, &.sdp-dialog-root .el-tree{
    background: transparent;
    color: var(--sdp-cszjsz-wzs1);
    .el-tree-node {
      &:focus {
        & > .el-tree-node__content {
          background: var(--sdp-ycsz-hgls);
        }
      }
    }
    .el-tree-node__expand-icon {
      font-size: 16px;
    }
    .el-tree-node__content {
      height: 30px;
      span {
        @include ellipsis;
      }
      span.el-checkbox__input {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &:hover,
      &:focus,
      &:active {
        background: var(--sdp-ycsz-hgls);
      }
    }
    &.el-tree--highlight-current
      .el-tree-node.is-current
      > .el-tree-node__content {
      background: var(--sdp-ycsz-hgls);
    }
  }
  .el-tree__empty-text {
    color: var(--sdp-jys) !important;
  }
  // ----------------------------el-tree-----结束--------------------------

  // ----------------------------el-table-----开始--------------------------
  .el-table {
    background: var(--sdp-fs1);
    color: var(--sdp-cszjsz-wzs1);
    th {
      background: var(--sdp-fs1);
      color: var(--sdp-xxbt1);
    }
    td {
      background: var(--sdp-fs1);
      color: var(--sdp-xxbt2);
    }
    th,
    td,
    th.is-leaf,
    &.el-table--border,
    &.el-table--group {
      border-color: var(--sdp-bg-bks);
    }
    &.el-table--border::after,
    &.el-table--group::after,
    &::before {
      background: var(--sdp-bg-bks);
    }
    &.el-table--enable-row-hover .el-table__body tr:hover > td {
      background: var(--sdp-fs2);
    }
  }
  // ----------------------------el-table-----结束--------------------------

  // ----------------------------el-button-----开始--------------------------
  .el-button.el-button--sdp-cancel, .el-button.el-button--default:not(.el-button--primary):not(.el-button--sdp-ensure) {
    --button-background-color: var(--sdp-qxnnds);
    --button-color: var(--sdp-zs);
    --button-border-color: var(--sdp-qxnnds);
    &:hover, &:focus, &:active {
      opacity: 0.8;
    }
  }
  .el-button.el-button--sdp-ensure, .el-button.el-button--primary{
    --button-background-color: var(--sdp-zs);
    --button-color: var(--sdp-nngl);
    --button-border-color: var(--sdp-zs);
  }
  .el-button {
    --button-background-color: var(--sdp-zs);
    --button-color: var(--sdp-nngl);
    --button-border-color: var(--sdp-zs);

    background: var(--button-background-color);
    color: var(--button-color);
    border-color: var(--button-border-color);

    &.el-button--sdp-cancel, &.el-button.el-button--default, &.el-button--sdp-ensure, &.el-button--primary{
      background-color: var(--button-background-color);
      background-image: unset;
      color: var(--button-color);
      border-color: var(--button-border-color);
      &:hover,
      &:focus,
      &:active {
        background-color: var(--button-background-color);
        background-image: unset;
        color: var(--button-color);
        border-color: var(--button-border-color);
      }
    }

    &:hover,
    &:focus,
    &:active {
      opacity: 0.95;
    }
    &.is-plain:focus,
    &.is-plain:hover{
      color: var(--sdp-zs);
      border-color: var(--sdp-zs);
      background-color: transparent;
    }
    &.is-disabled.is-plain,
    &.is-disabled.is-plain:focus,
    &.is-disabled.is-plain:hover{
      background: transparent;
      border-color: var(--sdp-jys);
    }
    &.is-disabled.el-button--text,
    &.is-disabled.el-button--text:focus,
    &.is-disabled.el-button--text:hover,
    &.is-disabled.is-plain,
    &.is-disabled.is-plain:focus,
    &.is-disabled.is-plain:hover{
      color: var(--sdp-jys);
    }
    &.el-button--sdp-cancel, &.el-button--sdp-ensure{
      height: 28px;
      line-height: 28px;
      border-radius: 2px;
      padding-top: 0;
      padding-bottom: 0;
      font-size: 12px;
    }
    &.el-button--text{
      background: transparent;
      color: var(--sdp-zs);
      border-color: transparent;
    }
  }
  &.el-message-box .el-message-box__btns{
    .el-button{
      &.el-button--sdp-cancel, &.el-button--sdp-ensure{
        height: 28px;
        line-height: 26px;
        border-radius: 2px;
        padding-top: 0;
        padding-bottom: 0;
        font-size: 12px;
      }
      &.el-button--default:not(.el-button--primary):not(.el-button--sdp-ensure){
        @extend .el-button--sdp-cancel;
      }
    }
  }
// ----------------------------el-button-----结束--------------------------
// ----------------------------el-pagination-----开始--------------------------
  // 实例
  .el-pagination {
    .btn-prev, .btn-next, .el-pager li  {
      background: transparent;
    }
    .btn-prev, .btn-next, .el-pager li:hover {
      color: var(--sdp-xxbt1);
    }
    .btn-prev[disabled], .btn-next[disabled] {
      color: var(--sdp-jys);
      background-color: transparent;
    }
    .el-pagination__total, .el-pagination__jump, .el-pager .number:not(.active) {
      color: var(--sdp-xxbt1);
    }
    .el-pager li.active {
      color: var(--sdp-zs);
    }
  }
// ----------------------------el-pagination-----结束--------------------------
// ----------------------------el-collapse-----开始--------------------------
  .el-collapse{
    .el-collapse-item__header{
      min-height: 48px;
      line-height: 1rem;
      height: auto;
      .el-collapse-item__arrow{
        color: var(--sdp-xlk-jts);
      }
    }
    .el-collapse-item__content{
      color: var(--sdp-sztc-srkbt);
    }
  }
// ----------------------------el-collapse-----结束--------------------------

// ----------------------------el-switch-----开始--------------------------
  .el-switch{
    height: 16px;
    line-height: 16px;
    .el-switch__core{
      width: 30px !important;
      height: 16px;
      &:after {
        margin-left: -1px !important;
        width: 14px;
        height: 14px;
        position: absolute;
        top: 0;
      }
    }
    &.is-checked .el-switch__core:after{
      margin-left: -14px !important;
    }
  }
  .el-switch:not(.is-disabled).is-checked span.el-switch__core {
    background-color: var(--sdp-zs);
    border-color: var(--sdp-zs);
  }
  .el-switch__label{
    line-height: 16px;
    font-size: 12px;
    height: 16px;
    color: var(--sdp-ycsz-rskbt);
    * {
      font-size: 12px;
    }
  }
  .el-switch.is-disabled .el-switch__core{
    &::after{
      background-color: var(--sdp-wz-jys);
    }
  }
  .el-color-picker, .el-color-picker__trigger{
      height: 22px;
    }
  .el-switch:not(.is-checked) .el-switch__core{
    background-color: var(--sdp-kgwxzbj);
    border-color: var(--sdp-kgwxzbj);
  }
// ----------------------------el-switch-----结束--------------------------
  // -------------------color-picker相关样式--------开始------------------------------------------
  &.el-color-dropdown.el-color-picker__panel {
    .el-button--default {
      background: var(--sdp-zs);
      border-color: var(--sdp-zs);
      color: #fff;
      height: 24px;
      line-height: 24px;
      padding-top: 0;
      padding-bottom: 0;
      margin-top: 2px;
      &:hover,
      &:active,
      &:focus {
        opacity: 0.9;
        background: var(--sdp-zs);
        color: #fff;
      }
    }
    .el-input__inner {
      border: 1px solid var(--sdp-srk-bks);
      background: var(--sdp-ycsz-srk-bgs);
      color: var(--sdp-fhwzs);
    }
  }
// -------------------color-picker相关样式--------结束------------------------------------------

// ---------------------------- sdp-input & sdp-select -----开始--------------------------
  .sdp-params-theme-background.sdp-input,
  .sdp-params-theme-background.sdp-select{
    background-color: transparent !important;
  }
// ---------------------------- sdp-input & sdp-select -----结束--------------------------
// ---------------------------- el-loading -----开始--------------------------
  &.el-loading-mask,
  .el-loading-mask {
    background-color: var(--sdp-gjys-bjs) !important;
    opacity: 0.9;
    .el-loading-text {
      margin-top: 15px;
      color: var(--sdp-zs) !important;
    }
    .el-loading-spinner{
      .path{
        color: var(--sdp-zs) !important;
      }
    }
    &.sdp-board-explore {
      z-index: 20000 !important;
      background-color: var(--sdp-gjys-bjs);
    }
    .sdp-loading-gif {
      display: inline-block;
      width: 50px;
      height: 50px;
      // background-image: url(https://sdp-cdn.shijicloud.com/static/images/sdp/sdp-loading.gif);
      // background-repeat: no-repeat;
      // background-size: contain;
      // background-position: center center;
    }
    .sdp-export-loading-gif {
      width: 50px;
      height: 50px;
      perspective: 780px;
      position: relative;
      display: inline-block;
      &::before {
        content: '';
        display: block;
        // .loader-child {
        position: absolute;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        border-radius: 50%;
        left: 0%;
        top: 0%;
        animation: rotate-one 1.15s linear infinite;
        border-bottom: 3px solid var(--sdp-zs);
      }

      @keyframes rotate-one {
        0% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
        }
        100% {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(360deg);
        }
      }
      // }
      // background-image: url(https://sdp-cdn.shijicloud.com/static/images/sdp/sdp-export-loading.gif);
      // @include sdp-mixin-style($type:(
      //    backgroundImage:(bgImg:true)
      // ), $self: (
      //     sdp-classic-white: (
      //         bgImg: url(https://sdp-cdn.shijicloud.com/static/images/sdp/sdp-export-loading.gif)
      //     ),
      //     sdp-dark-blue: (
      //         bgImg: url(https://sdp-cdn.shijicloud.com/static/images/sdp/sdp-export-loading-dark.gif)
      //     )
      // ));
      // background-repeat: no-repeat;
      // background-size: contain;
      // background-position: center center;
    }
  }
// ---------------------------- el-loading -----结束--------------------------
// 参数组件部分---- 开始
&.el-popper.belongto-paramsarea{
  .el-select-dropdown__item{
    height: 20px;
    line-height: 20px;
    &.selected,&.hover,&.selected.hover{
      background-color: var(--sdp-cszjxz-hs1);
      color: var(--sdp-nngl) !important;
    }
  }
  .el-select .el-input.is-focus .el-input__inner, .el-select .el-input__inner, .el-input .el-input__inner {
    border-color: var(--sdp-srk-bks);
    &:hover,&:focus,&:active{
      border-color: var(--sdp-srk-bks);
    }
  }
}
&.sdp-paramsetting-select {
  background-color: var(--sdp-szk-bjs);
  border-color: var(--sdp-ycfgx);
  max-width: 600px;
  .el-scrollbar {
    .el-select-dropdown__wrap {
      .el-select-dropdown__item{
      }
      .hover {
      }
      .selected {
      }
      .el-select-dropdown__item:not(.selected):not(.is-disabled) {

      }
    }
  }
  .popper__arrow {
    @include sdp-borderColor_1('bottom');
    @include sdp-borderColor_1('top');
    &::after {
      @include sdp-borderBottomColor_2('important');
    }
  }
}
&.belongto-paramsarea.el-date-picker,
&.belongto-paramsarea.el-date-range-picker {
  .el-date-table th{
    color: var(--sdp-cszjsz-wzs1);
  }
  .el-date-table td.available{
    &:hover{
      span{
        background-color: var(--sdp-rl-hzts);
        color: var(--sdp-nngl);
      }
    }
  }
  .el-input__inner{
    border-color: var(--sdp-srk-bks);
    &:hover,&.active,&.focus{
      border-color: var(--sdp-srk-bks);
    }
  }
  .el-date-picker__header-label{
    &:hover{
      color: var(--sdp-cszj-onns);
    }
  }
  .el-year-table td .cell:hover, .el-year-table td.current:not(.disabled) .cell{
    color: var(--sdp-cszj-onns);
  }
}
&.el-dialog.belongto-paramsarea{
  .el-button.button-confirm{
    background-color: var(--sdp-cszj-onns) !important;
    border-color: var(--sdp-cszj-onns) !important;
    color: var(--sdp-nngl) !important;
  }
  .el-button.button-cancel{
    color: var(--sdp-tsgls);
    background-color: transparent;
    border-color: transparent;
  }
}
// 参数组件部分---- 结束


}

body .el-tooltip__popper.sdp-theme-container {
  //background: red;

  .popper__arrow::after {
    //border-bottom-color: red;
  }
}


// 注意：以上的样式，尽量以最低匹配度写，不要定制


// # 样式强制修改
// 地图类型，禁用选项 hover 样式
.sdp-select-hover-bg-transparent .el-select-dropdown__item.is-disabled:hover {
  background: transparent !important;
}

.global_drag_select_color{
  background-color: var(--sdp-bg-rl-current)!important;
  .node-label{
    color: var(--sdp-nngl)!important;
  }
}
.global_drag_select_color.el-checkbox{
  background-color: var(--sdp-bg-rl-current)!important;
  .el-checkbox__label{
    color: var(--sdp-nngl)!important;
  }
}
.sdp-only-index .el-dialog__body{
  padding: 24px 24px 0 24px !important;
  .content {
    height: 328px !important;
  }
  .border {
    border: none;
  }
  .left {
    margin-right: 24px !important;
    width: 200px !important;
  }
  .right {
    padding-left: 24px !important;
    .searchName .el-input-group__append{
      box-shadow: none !important;
    }
    .top {
      margin-bottom: 16px !important;
    }
  }
  .sdp-only-index-sle {
    .el-input--mini .el-input__inner {
      height: 30px !important;
      line-height: 30px !important;
    }
  }
  .sdp-only-index-table {
    .cell {
      line-height: 24px !important;
    }
    th {
      padding: 3px 0 !important;
    }
  }
}
.sdp-only-index-option {
  padding: 0 !important;
  .is-horizontal , .is-vertical {
    display: none !important;
  }
}

.sdp-tip-popover {
  z-index: 20000 !important;
}

.el-message-box__btns > .el-button--small {
  margin-left: 10px !important;
}