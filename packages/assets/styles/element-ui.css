@charset "UTF-8";
/* 颜色变量定义 */
/* border 样式 */
.sdp-board-design, .sdp-board-preview, .sdp-grid-design, .sdp-grid-preview, .sdp-dialog, .sdp-popover {
  /*修改table的标题栏背景色*/
  /*修改进度条样式*/
  /*修改Sidebar鼠标移动的背景色*/
  /*修改顶部导航样式 字体居中 选中颜色 底部线条颜色*/
  /*弹出对话框样式修改*/
  /*修改左右边距*/
  /*修改标题居中*/
  /*新定义的公共样式 start*/
  /*修改图标大小*/
  /*修改按钮间距*/
  /*修改按钮颜色--确定*/
  /*修改按钮颜色--取消*/
  /*修改对话框内容居中显示*/
  /*新定义的公共样式 end*/
  /*表格文字居中*/
  /*底部内容区域背景色及边框间距*/
  /*按钮宽度*/
  /*输入框宽度*/
  /*下拉框宽度*/
  /*顶部间距*/
  /* 对话框文字默认不换行 */
  /*图标大小*/
  /*左边菜单子节点*/
  /* 重新订制element颜色变量 */
  /* 主题色 */
  /* 改变 icon 字体路径变量，必需 */
}

.sdp-board-design .el-upload input[type="file"], .sdp-board-preview .el-upload input[type="file"], .sdp-grid-design .el-upload input[type="file"], .sdp-grid-preview .el-upload input[type="file"], .sdp-dialog .el-upload input[type="file"], .sdp-popover .el-upload input[type="file"] {
  display: none !important;
}

.sdp-board-design .el-upload__input, .sdp-board-preview .el-upload__input, .sdp-grid-design .el-upload__input, .sdp-grid-preview .el-upload__input, .sdp-dialog .el-upload__input, .sdp-popover .el-upload__input {
  display: none;
}

.sdp-board-design .el-submenu__title, .sdp-board-preview .el-submenu__title, .sdp-grid-design .el-submenu__title, .sdp-grid-preview .el-submenu__title, .sdp-dialog .el-submenu__title, .sdp-popover .el-submenu__title {
  height: 37px !important;
  line-height: 37px !important;
  padding: 0 12px 0 12px !important;
}

.sdp-board-design .el-submenu .el-menu-item, .sdp-board-preview .el-submenu .el-menu-item, .sdp-grid-design .el-submenu .el-menu-item, .sdp-grid-preview .el-submenu .el-menu-item, .sdp-dialog .el-submenu .el-menu-item, .sdp-popover .el-submenu .el-menu-item {
  height: 37px !important;
  line-height: 37px !important;
}

.sdp-board-design .navbar .avatar-container .avatar-wrapper, .sdp-board-preview .navbar .avatar-container .avatar-wrapper, .sdp-grid-design .navbar .avatar-container .avatar-wrapper, .sdp-grid-preview .navbar .avatar-container .avatar-wrapper, .sdp-dialog .navbar .avatar-container .avatar-wrapper, .sdp-popover .navbar .avatar-container .avatar-wrapper {
  margin-top: 1px;
  position: relative;
}

.sdp-board-design .el-container, .sdp-board-preview .el-container, .sdp-grid-design .el-container, .sdp-grid-preview .el-container, .sdp-dialog .el-container, .sdp-popover .el-container {
  background-color: #efefef;
}

.sdp-board-design .el-progress-bar__outer, .sdp-board-preview .el-progress-bar__outer, .sdp-grid-design .el-progress-bar__outer, .sdp-grid-preview .el-progress-bar__outer, .sdp-dialog .el-progress-bar__outer, .sdp-popover .el-progress-bar__outer {
  border-radius: 7px !important;
}

.sdp-board-design .el-progress-bar__inner, .sdp-board-preview .el-progress-bar__inner, .sdp-grid-design .el-progress-bar__inner, .sdp-grid-preview .el-progress-bar__inner, .sdp-dialog .el-progress-bar__inner, .sdp-popover .el-progress-bar__inner {
  border-radius: 0 !important;
}

.sdp-board-design .el-menu-item:hover, .sdp-board-preview .el-menu-item:hover, .sdp-grid-design .el-menu-item:hover, .sdp-grid-preview .el-menu-item:hover, .sdp-dialog .el-menu-item:hover, .sdp-popover .el-menu-item:hover {
  background-color: #13b5b1 !important;
}

.sdp-board-design .el-menu-item:focus,
.sdp-board-design .el-menu-item:active,
.sdp-board-design .el-menu-item:focus-within, .sdp-board-preview .el-menu-item:focus,
.sdp-board-preview .el-menu-item:active,
.sdp-board-preview .el-menu-item:focus-within, .sdp-grid-design .el-menu-item:focus,
.sdp-grid-design .el-menu-item:active,
.sdp-grid-design .el-menu-item:focus-within, .sdp-grid-preview .el-menu-item:focus,
.sdp-grid-preview .el-menu-item:active,
.sdp-grid-preview .el-menu-item:focus-within, .sdp-dialog .el-menu-item:focus,
.sdp-dialog .el-menu-item:active,
.sdp-dialog .el-menu-item:focus-within, .sdp-popover .el-menu-item:focus,
.sdp-popover .el-menu-item:active,
.sdp-popover .el-menu-item:focus-within {
  background-color: #13b5b1 !important;
}

.sdp-board-design .el-submenu__title:hover, .sdp-board-preview .el-submenu__title:hover, .sdp-grid-design .el-submenu__title:hover, .sdp-grid-preview .el-submenu__title:hover, .sdp-dialog .el-submenu__title:hover, .sdp-popover .el-submenu__title:hover {
  background-color: #13b5b1 !important;
}

.sdp-board-design img:hover, .sdp-board-preview img:hover, .sdp-grid-design img:hover, .sdp-grid-preview img:hover, .sdp-dialog img:hover, .sdp-popover img:hover {
  cursor: pointer;
}

.sdp-board-design ul.el-menu.el-menu--popup.el-menu--popup-right-start, .sdp-board-preview ul.el-menu.el-menu--popup.el-menu--popup-right-start, .sdp-grid-design ul.el-menu.el-menu--popup.el-menu--popup-right-start, .sdp-grid-preview ul.el-menu.el-menu--popup.el-menu--popup-right-start, .sdp-dialog ul.el-menu.el-menu--popup.el-menu--popup-right-start, .sdp-popover ul.el-menu.el-menu--popup.el-menu--popup-right-start {
  margin-left: 0px;
}

.sdp-board-design .el-menu--horizontal, .sdp-board-preview .el-menu--horizontal, .sdp-grid-design .el-menu--horizontal, .sdp-grid-preview .el-menu--horizontal, .sdp-dialog .el-menu--horizontal, .sdp-popover .el-menu--horizontal {
  border-bottom: 2px solid #f7f7f8 !important;
  margin-bottom: 15px !important;
}

.sdp-board-design .el-menu--horizontal > .el-menu-item, .sdp-board-preview .el-menu--horizontal > .el-menu-item, .sdp-grid-design .el-menu--horizontal > .el-menu-item, .sdp-grid-preview .el-menu--horizontal > .el-menu-item, .sdp-dialog .el-menu--horizontal > .el-menu-item, .sdp-popover .el-menu--horizontal > .el-menu-item {
  height: 47px !important;
  line-height: 47px !important;
}

.sdp-board-design .el-checkbox__inner, .sdp-board-preview .el-checkbox__inner, .sdp-grid-design .el-checkbox__inner, .sdp-grid-preview .el-checkbox__inner, .sdp-dialog .el-checkbox__inner, .sdp-popover .el-checkbox__inner {
  width: 18px !important;
  height: 18px !important;
}

.sdp-board-design .el-checkbox__inner::after, .sdp-board-preview .el-checkbox__inner::after, .sdp-grid-design .el-checkbox__inner::after, .sdp-grid-preview .el-checkbox__inner::after, .sdp-dialog .el-checkbox__inner::after, .sdp-popover .el-checkbox__inner::after {
  height: 10px !important;
  left: 6px !important;
}

.sdp-board-design .el-dialog__body, .sdp-board-preview .el-dialog__body, .sdp-grid-design .el-dialog__body, .sdp-grid-preview .el-dialog__body, .sdp-dialog .el-dialog__body, .sdp-popover .el-dialog__body {
  padding: 0 0 20px 0;
}

.sdp-board-design .el-dialog__header, .sdp-board-preview .el-dialog__header, .sdp-grid-design .el-dialog__header, .sdp-grid-preview .el-dialog__header, .sdp-dialog .el-dialog__header, .sdp-popover .el-dialog__header {
  text-align: left;
}

.sdp-board-design .text-left, .sdp-board-preview .text-left, .sdp-grid-design .text-left, .sdp-grid-preview .text-left, .sdp-dialog .text-left, .sdp-popover .text-left {
  text-align: left;
}

.sdp-board-design .text-right, .sdp-board-preview .text-right, .sdp-grid-design .text-right, .sdp-grid-preview .text-right, .sdp-dialog .text-right, .sdp-popover .text-right {
  text-align: right;
}

.sdp-board-design .text-center, .sdp-board-preview .text-center, .sdp-grid-design .text-center, .sdp-grid-preview .text-center, .sdp-dialog .text-center, .sdp-popover .text-center {
  text-align: center;
}

.sdp-board-design .btn-space, .sdp-board-preview .btn-space, .sdp-grid-design .btn-space, .sdp-grid-preview .btn-space, .sdp-dialog .btn-space, .sdp-popover .btn-space {
  margin-right: 10px;
}

.sdp-board-design .btn-left-space, .sdp-board-preview .btn-left-space, .sdp-grid-design .btn-left-space, .sdp-grid-preview .btn-left-space, .sdp-dialog .btn-left-space, .sdp-popover .btn-left-space {
  margin-left: 10px;
}

.sdp-board-design .btn-space-top30, .sdp-board-preview .btn-space-top30, .sdp-grid-design .btn-space-top30, .sdp-grid-preview .btn-space-top30, .sdp-dialog .btn-space-top30, .sdp-popover .btn-space-top30 {
  margin-top: 10px;
}

.sdp-board-design .btn-space-bottom, .sdp-board-preview .btn-space-bottom, .sdp-grid-design .btn-space-bottom, .sdp-grid-preview .btn-space-bottom, .sdp-dialog .btn-space-bottom, .sdp-popover .btn-space-bottom {
  margin-bottom: 20px;
}

.sdp-board-design .btn-sure-color, .sdp-board-preview .btn-sure-color, .sdp-grid-design .btn-sure-color, .sdp-grid-preview .btn-sure-color, .sdp-dialog .btn-sure-color, .sdp-popover .btn-sure-color {
  background-color: #13b5b1;
  border-color: #13b5b1;
}

.sdp-board-design .btn-cancel-color, .sdp-board-preview .btn-cancel-color, .sdp-grid-design .btn-cancel-color, .sdp-grid-preview .btn-cancel-color, .sdp-dialog .btn-cancel-color, .sdp-popover .btn-cancel-color {
  border-color: #717172;
}

.sdp-board-design .tool-dialog-center, .sdp-board-preview .tool-dialog-center, .sdp-grid-design .tool-dialog-center, .sdp-grid-preview .tool-dialog-center, .sdp-dialog .tool-dialog-center, .sdp-popover .tool-dialog-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.sdp-board-design .el-dialog, .sdp-board-preview .el-dialog, .sdp-grid-design .el-dialog, .sdp-grid-preview .el-dialog, .sdp-dialog .el-dialog, .sdp-popover .el-dialog {
  -webkit-transform: none;
          transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.sdp-board-design .upload-container .el-upload, .sdp-board-preview .upload-container .el-upload, .sdp-grid-design .upload-container .el-upload, .sdp-grid-preview .upload-container .el-upload, .sdp-dialog .upload-container .el-upload, .sdp-popover .upload-container .el-upload {
  width: 100%;
}

.sdp-board-design .upload-container .el-upload .el-upload-dragger, .sdp-board-preview .upload-container .el-upload .el-upload-dragger, .sdp-grid-design .upload-container .el-upload .el-upload-dragger, .sdp-grid-preview .upload-container .el-upload .el-upload-dragger, .sdp-dialog .upload-container .el-upload .el-upload-dragger, .sdp-popover .upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.sdp-board-design .el-upload input[type="file"], .sdp-board-preview .el-upload input[type="file"], .sdp-grid-design .el-upload input[type="file"], .sdp-grid-preview .el-upload input[type="file"], .sdp-dialog .el-upload input[type="file"], .sdp-popover .el-upload input[type="file"] {
  display: none !important;
}

.sdp-board-design .el-upload__input, .sdp-board-preview .el-upload__input, .sdp-grid-design .el-upload__input, .sdp-grid-preview .el-upload__input, .sdp-dialog .el-upload__input, .sdp-popover .el-upload__input {
  display: none;
}

.sdp-board-design .bottom-cotainer-bg, .sdp-board-preview .bottom-cotainer-bg, .sdp-grid-design .bottom-cotainer-bg, .sdp-grid-preview .bottom-cotainer-bg, .sdp-dialog .bottom-cotainer-bg, .sdp-popover .bottom-cotainer-bg {
  background-color: #efefef;
}

.sdp-board-design .bottom-cotainer-bg .bottom-cotainer-box, .sdp-board-preview .bottom-cotainer-bg .bottom-cotainer-box, .sdp-grid-design .bottom-cotainer-bg .bottom-cotainer-box, .sdp-grid-preview .bottom-cotainer-bg .bottom-cotainer-box, .sdp-dialog .bottom-cotainer-bg .bottom-cotainer-box, .sdp-popover .bottom-cotainer-bg .bottom-cotainer-box {
  border-top: 1px solid #fff;
  border: 10px solid #efefef;
  border-radius: 5px;
}

.sdp-board-design .input-width, .sdp-board-preview .input-width, .sdp-grid-design .input-width, .sdp-grid-preview .input-width, .sdp-dialog .input-width, .sdp-popover .input-width {
  width: 200px !important;
}

.sdp-board-design .input-width240, .sdp-board-preview .input-width240, .sdp-grid-design .input-width240, .sdp-grid-preview .input-width240, .sdp-dialog .input-width240, .sdp-popover .input-width240 {
  width: 240px !important;
}

.sdp-board-design .select-width, .sdp-board-preview .select-width, .sdp-grid-design .select-width, .sdp-grid-preview .select-width, .sdp-dialog .select-width, .sdp-popover .select-width {
  width: 120px;
}

.sdp-board-design .btn-space-top, .sdp-board-preview .btn-space-top, .sdp-grid-design .btn-space-top, .sdp-grid-preview .btn-space-top, .sdp-dialog .btn-space-top, .sdp-popover .btn-space-top {
  margin-top: 15px;
}

.sdp-board-design .btn-space-left, .sdp-board-preview .btn-space-left, .sdp-grid-design .btn-space-left, .sdp-grid-preview .btn-space-left, .sdp-dialog .btn-space-left, .sdp-popover .btn-space-left {
  margin-left: 6px;
}

.sdp-board-design .el-dialog .el-dialog__body .el-form-item .el-form-item__label, .sdp-board-preview .el-dialog .el-dialog__body .el-form-item .el-form-item__label, .sdp-grid-design .el-dialog .el-dialog__body .el-form-item .el-form-item__label, .sdp-grid-preview .el-dialog .el-dialog__body .el-form-item .el-form-item__label, .sdp-dialog .el-dialog .el-dialog__body .el-form-item .el-form-item__label, .sdp-popover .el-dialog .el-dialog__body .el-form-item .el-form-item__label {
  white-space: nowrap;
}

.sdp-board-design .dialog-footer, .sdp-board-preview .dialog-footer, .sdp-grid-design .dialog-footer, .sdp-grid-preview .dialog-footer, .sdp-dialog .dialog-footer, .sdp-popover .dialog-footer {
  margin-top: 20px;
  text-align: right;
}

.sdp-board-design .icon-size, .sdp-board-preview .icon-size, .sdp-grid-design .icon-size, .sdp-grid-preview .icon-size, .sdp-dialog .icon-size, .sdp-popover .icon-size {
  width: 18px;
  height: 18px;
}

.sdp-board-design .custom-tree-node, .sdp-board-preview .custom-tree-node, .sdp-grid-design .custom-tree-node, .sdp-grid-preview .custom-tree-node, .sdp-dialog .custom-tree-node, .sdp-popover .custom-tree-node {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.sdp-board-design .dropdown-box .sidebar, .sdp-board-preview .dropdown-box .sidebar, .sdp-grid-design .dropdown-box .sidebar, .sdp-grid-preview .dropdown-box .sidebar, .sdp-dialog .dropdown-box .sidebar, .sdp-popover .dropdown-box .sidebar {
  text-align: center;
  display: block !important;
  padding-right: 5px;
}

.sdp-board-design .el-dialog, .sdp-board-preview .el-dialog, .sdp-grid-design .el-dialog, .sdp-grid-preview .el-dialog, .sdp-dialog .el-dialog, .sdp-popover .el-dialog {
  -webkit-transform: none;
          transform: none;
  left: 0;
  position: relative;
  margin: 15vh auto 0;
}

.sdp-board-design .upload-container .el-upload, .sdp-board-preview .upload-container .el-upload, .sdp-grid-design .upload-container .el-upload, .sdp-grid-preview .upload-container .el-upload, .sdp-dialog .upload-container .el-upload, .sdp-popover .upload-container .el-upload {
  width: 100%;
}

.sdp-board-design .upload-container .el-upload .el-upload-dragger, .sdp-board-preview .upload-container .el-upload .el-upload-dragger, .sdp-grid-design .upload-container .el-upload .el-upload-dragger, .sdp-grid-preview .upload-container .el-upload .el-upload-dragger, .sdp-dialog .upload-container .el-upload .el-upload-dragger, .sdp-popover .upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.sdp-board-design .el-button--specific, .sdp-board-preview .el-button--specific, .sdp-grid-design .el-button--specific, .sdp-grid-preview .el-button--specific, .sdp-dialog .el-button--specific, .sdp-popover .el-button--specific {
  padding: 0px !important;
  vertical-align: middle;
}

.sdp-board-design .search-place, .sdp-board-preview .search-place, .sdp-grid-design .search-place, .sdp-grid-preview .search-place, .sdp-dialog .search-place, .sdp-popover .search-place {
  float: right;
  margin-right: 10px;
}

.sdp-board-design .el-menu--collapse > .el-menu-item [class^="el-icon-"], .sdp-board-design .el-menu--collapse > .el-submenu > .el-submenu__title [class^="el-icon-"], .sdp-board-preview .el-menu--collapse > .el-menu-item [class^="el-icon-"], .sdp-board-preview .el-menu--collapse > .el-submenu > .el-submenu__title [class^="el-icon-"], .sdp-grid-design .el-menu--collapse > .el-menu-item [class^="el-icon-"], .sdp-grid-design .el-menu--collapse > .el-submenu > .el-submenu__title [class^="el-icon-"], .sdp-grid-preview .el-menu--collapse > .el-menu-item [class^="el-icon-"], .sdp-grid-preview .el-menu--collapse > .el-submenu > .el-submenu__title [class^="el-icon-"], .sdp-dialog .el-menu--collapse > .el-menu-item [class^="el-icon-"], .sdp-dialog .el-menu--collapse > .el-submenu > .el-submenu__title [class^="el-icon-"], .sdp-popover .el-menu--collapse > .el-menu-item [class^="el-icon-"], .sdp-popover .el-menu--collapse > .el-submenu > .el-submenu__title [class^="el-icon-"] {
  font-size: 12px;
}

.sdp-board-design .el-menu--popup, .sdp-board-preview .el-menu--popup, .sdp-grid-design .el-menu--popup, .sdp-grid-preview .el-menu--popup, .sdp-dialog .el-menu--popup, .sdp-popover .el-menu--popup {
  min-width: 120px;
}

.sdp-board-design .el-menu--vertical, .sdp-board-preview .el-menu--vertical, .sdp-grid-design .el-menu--vertical, .sdp-grid-preview .el-menu--vertical, .sdp-dialog .el-menu--vertical, .sdp-popover .el-menu--vertical {
  left: 31px !important;
}

.sdp-board-design .el-menu--vertical .el-menu-item, .sdp-board-preview .el-menu--vertical .el-menu-item, .sdp-grid-design .el-menu--vertical .el-menu-item, .sdp-grid-preview .el-menu--vertical .el-menu-item, .sdp-dialog .el-menu--vertical .el-menu-item, .sdp-popover .el-menu--vertical .el-menu-item {
  height: 38px;
  line-height: 38px;
}

/*图标大小*/
.icon-size {
  width: 18px;
  height: 18px;
}

/* 重新订制element颜色变量 */
/* 主题色 */
/* 改变 icon 字体路径变量，必需 */
.el-message-box__btns {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

.el-message-box__btns > .el-button--small {
  margin-left: 10px !important;
}

.el-message-box__btns > .el-button--primary {
  margin-left: 0px;
}
/*# sourceMappingURL=element-ui.css.map */