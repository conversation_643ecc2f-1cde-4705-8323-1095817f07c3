import { ThemeType } from '../constant'

// !导入新主题需要主要的变量
const UI_NO_VAR = {
    '--sdp-resizable-handle-icon': '',
}

// 字典对应ui的设计 --- （新变量需要在字典里添加）
const DICTIONARY = {
  /**
   * #553CCE/#553CCE
   * rgb(85, 60, 206)/rgb(85, 60, 206)
   * #553CCE/rgb(85, 60, 206)
   * rgb(85, 60, 206)/#553CCE
  */
 '--sdp-zs': '主色',
 '--sdp-el-b': '元素边框',
 /**
  * #DCDFD6/#425060
  * rgb(220, 223, 214)/rgb(66, 80, 96)
  * #DCDFD6/rgb(66, 80, 96)
  * rgb(220, 223, 214)/#425060
 */
    '--sdp-kgwxzbj': '开关未选中背景',
    /**
     * #FFFFFF/#283341
     * rgb(255, 255, 255)/rgb(40, 51, 65)
     * #FFFFFF/rgb(40, 51, 65)
     * rgb(255, 255, 255)/#283341
    */
    '--sdp-fs1': '辅色-01',
    /**
     * #EAE9EB/#2B3A4B
     * rgb(234, 233, 235)/rgb(43, 58, 75)
     * #EAE9EB/rgb(43, 58, 75)
     * rgb(234, 233, 235)/#2B3A4B
    */
    '--sdp-fs2': '辅色-02',
    /**
     * #263A61/#263A61
     * rgb(38, 58, 97)/rgb(38, 58, 97)
     * #263A61/rgb(38, 58, 97)
     * rgb(38, 58, 97)/#263A61
    */
    '--sdp-hgls': 'hover 高亮色',
    /**
     * #263A61/#263A61
     * rgb(38, 58, 97)/rgb(38, 58, 97)
     * #263A61/rgb(38, 58, 97)
     * rgb(38, 58, 97)/#263A61
    */
    '--sdp-hgls2': 'hover 高亮色', // 81014 bug新加色号
    /**
     * #F95046/#F95046
     * rgb(249, 80, 70)/rgb(249, 80, 70)
     * #F95046/rgb(249, 80, 70)
     * rgb(249, 80, 70)/#F95046
    */
    '--sdp-qcgls': '清除高亮红',
    /**
     * #5980BB/#5980BB
     * rgb(89, 128, 187)/rgb(89, 128, 187)
     * #5980BB/rgb(89, 128, 187)
     * rgb(89, 128, 187)/#5980BB
    */
    '--sdp-tsgls': '特殊-高亮色',
    /**
     * #F7F7F7/#1C2630
     * rgb(255, 255, 255)/rgb(28, 38, 48)
     * #F7F7F7/rgb(28, 38, 48)
     * rgb(255, 255, 255)/#1C2630
    */
    '--sdp-ysbjc': '编辑看板-元素背景色',
    /**
     * #222222/#CDCDCD
     * rgb(34, 34, 34)/rgb(205, 205, 205)
     * #222222/rgb(205, 205, 205)
     * rgb(34, 34, 34)/#CDCDCD
    */
    '--sdp-zxz-wz': '周选择-文字',
    /**
     * #DCDCDC/#303E4E
     * rgb(220, 220, 220)/rgb(48, 62, 78)
     * #DCDCDC/rgb(48, 62, 78)
     * rgb(220, 220, 220)/#303E4E
    */
    '--sdp-bg-bcbk': '表格-标尺边框',
    /**
     * #777777/#777777
     * rgb(119, 119, 119)/rgb(119, 119, 119)
     * #777777/rgb(119, 119, 119)
     * rgb(119, 119, 119)/#777777
    */
    '--sdp-bg-bcsz': '表格-标尺数字',
    /**
     * #2C3D51/#EAEBED
     * rgb(44, 61, 81)/rgb(234, 235, 237)
     * #2C3D51/rgb(234, 235, 237)
     * rgb(44, 61, 81)/#EAEBED
    */
    '--sdp-bg-lhtwz': '表格-列行头文字',
    /**
     * #FFFFFF/#212D3B
     * rgb(255, 255, 255)/rgb(33, 45, 59)
     * #FFFFFF/rgb(33, 45, 59)
     * rgb(255, 255, 255)/#212D3B
    */
    '--sdp-bg-tcbjs': '表格-填充背景色',
    /**
     * #FFFFFF/#1D2631
     * rgb(255, 255, 255)/rgb(29, 38, 49)
     * #FFFFFF/rgb(29, 38, 49)
     * rgb(255, 255, 255)/#1D2631
    */
    '--sdp-jddlsrbj': '纬度度量输入背景',
    /**
     * #553CCE/#D6E0EB
     * rgb(85, 60, 206)/rgb(214, 224, 235)
     * #553CCE/rgb(214, 224, 235)
     * rgb(85, 60, 206)/#D6E0EB
    */
    '--sdp-rldx-xz': '日历多选-选中',
    /**
     * #F3F2FB/#283341
     * rgb(243, 242, 251)/rgb(40, 51, 65)
     * #F3F2FB/rgb(40, 51, 65)
     * rgb(243, 242, 251)/#283341
    */
    '--sdp-zdbjs': '字段背景色',
    /**
     * #EAE9EB/#283645
     * rgb(234, 233, 235)/rgb(40, 54, 69)
     * #EAE9EB/rgb(40, 54, 69)
     * rgb(234, 233, 235)/#283645
    */
    '--sdp-tb-srbj': '头部-输入背景',
    /**
     * #FFFFFF/#1F2A37
     * rgb(255, 255, 255)/rgb(31, 42, 55)
     * #FFFFFF/rgb(31, 42, 55)
     * rgb(255, 255, 255)/#1F2A37
    */
    '--sdp-tb-bj': '头部-背景',
    /**
     * #222222/#ACB0B4
     * rgb(34, 34, 34)/rgb(172, 176, 180)
     * #222222/rgb(172, 176, 180)
     * rgb(34, 34, 34)/#ACB0B4
    */
    '--sdp-cyfhwzs': '次要返回文字色',
    /**
     * #EEF0F5/#24303E
     * rgb(238, 240, 245)/rgb(36, 48, 62)
     * #EEF0F5/rgb(36, 48, 62)
     * rgb(238, 240, 245)/#24303E
    */
    '--sdp-qxnnds': '取消按钮底色',
    /**
     * #606266/#BBBABA
     * rgb(96, 98, 102)/rgb(187, 186, 186)
     * #606266/rgb(187, 186, 186)
     * rgb(96, 98, 102)/#BBBABA
    */
    '--sdp-sztc-srkbt': '设置弹层-输入框标题',
    /**
     * #553CCE/#FFFFFF
     * rgb(85, 60, 206)/rgb(255, 255, 255)
     * #553CCE/rgb(255, 255, 255)
     * rgb(85, 60, 206)/#FFFFFF
    */
    '--sdp-sztc-bts': '设置弹框-标题色',
    /**
     * #2C3E50/#F1F4F8
     * rgb(44, 62, 80)/rgb(241, 244, 248)
     * #2C3E50/rgb(241, 244, 248)
     * rgb(44, 62, 80)/#F1F4F8
    */
    '--sdp-zjbts': '组件标题色',
    /**
     * #444444/#A7B9CF
     * rgb(68, 68, 68)/rgb(167, 185, 207)
     * #444444/rgb(167, 185, 207)
     * rgb(68, 68, 68)/#A7B9CF
    */
    '--sdp-zjbxx-wzs': '组件备选项-文字色',
    /**
     * #444444/#A7B9CF
     * rgb(68, 68, 68)/rgb(167, 185, 207)
     * #444444/rgb(167, 185, 207)
     * rgb(68, 68, 68)/#A7B9CF
    */
    '--sdp-zjbxx-wzs-bt' : 'pc和移动标题默认色',
    /**
     * #606266/#FFFFFF
     * rgb(96, 98, 102)/rgb(255, 255, 255)
     * #606266/rgb(255, 255, 255)
     * rgb(96, 98, 102)/#FFFFFF
    */
    '--sdp-cszj-tswzs': '参数组件-提示文字色',
    /**
     * #FFFFFF/#1F2A37
     * rgb(255, 255, 255)/rgb(31, 42, 55)
     * #FFFFFF/rgb(31, 42, 55)
     * rgb(255, 255, 255)/#1F2A37
    */
    '--sdp-cszj-yxibj': '参数组件-已选 icon 背景',
    /**
     * #DFDDEF/#2D3D50
     * rgb(223, 221, 239)/rgb(45, 61, 80)
     * #DFDDEF/rgb(45, 61, 80)
     * rgb(223, 221, 239)/#2D3D50
    */
    '--sdp-sjj-hs': '数据集-hover 色',
    /**
     * #5980BB/#5980BB
     * rgb(89, 128, 187)/rgb(89, 128, 187)
     * #5980BB/rgb(89, 128, 187)
     * rgb(89, 128, 187)/#5980BB
    */
    '--sdp-wdzd-h': '维度字段- Hover',
    /**
     * #D2DBF4/#2C3F5D
     * rgb(210, 219, 244)/rgb(44, 63, 93)
     * #D2DBF4/rgb(44, 63, 93)
     * rgb(210, 219, 244)/#2C3F5D
    */
    '--sdp-sjjdxbj': '数据集待选背景',
    /**
     * #FFFFFF/#1C242F
     * rgb(255, 255, 255)/rgb(28, 36, 47)
     * #FFFFFF/rgb(28, 36, 47)
     * rgb(255, 255, 255)/#1C242F
    */
    '--sdp-zcsjj-bj': '左侧数据集-背景',
    /**
     * #F5F7FA/#F5F7FA
     * rgb(245, 247, 250)/rgb(245, 247, 250)
     * #F5F7FA/rgb(245, 247, 250)
     * rgb(245, 247, 250)/#F5F7FA
    */
    '--sdp-ysxzbks': '颜色选择边框色',
    /**
     * #232323/#FFFFFF
     * rgb(35, 35, 35)/rgb(255, 255, 255)
     * #232323/rgb(255, 255, 255)
     * rgb(35, 35, 35)/#FFFFFF
    */
    '--sdp-xxbt1': '选项标题-01',
    /**
     * #232323/#B2C1D2
     * rgb(35, 35, 35)/rgb(178, 193, 210)
     * #232323/rgb(178, 193, 210)
     * rgb(35, 35, 35)/#B2C1D2
    */
    '--sdp-xxbt2': '选项标题-02',
    /**
     * #999999/#999999
     * rgb(153, 153, 153)/rgb(153, 153, 153)
     * #999999/rgb(153, 153, 153)
     * rgb(153, 153, 153)/#999999
    */
    '--sdp-ycsz-srtswz': '右侧设置- 输入提示文字',
    /**
     * #222222/#FFFFFF
     * rgb(34, 34, 34)/rgb(255, 255, 255)
     * #222222/rgb(255, 255, 255)
     * rgb(34, 34, 34)/#FFFFFF
    */
    '--sdp-ycsz-rskbt': '右侧设置- 输入框标题',
    /**
     * #F4F7FA/#354254
     * rgb(244, 247, 250)/rgb(53, 66, 84)
     * #F4F7FA/rgb(53, 66, 84)
     * rgb(244, 247, 250)/#354254
    */
    '--sdp-ycsz-hgls': '右侧设置- Hover高亮色',
    /**
     * #FFFFFF/#232F3F
     * rgb(255, 255, 255)/rgb(35, 47, 63)
     * #FFFFFF/rgb(35, 47, 63)
     * rgb(255, 255, 255)/#232F3F
    */
    '--sdp-ycsz-srk-bgs': '右侧设置- 输入框-背景色',
    /**
     * #DCDFE6/#232F3F
     * rgb(220, 223, 230)/rgb(35, 47, 63)
     * #DCDFE6/rgb(35, 47, 63)
     * rgb(220, 223, 230)/#232F3F
    */
    '--sdp-ycsz-srk-bcs': '右侧设置- 输入框-边框色',
    /**
     * #63768D/#63768D
     * rgb(99, 118, 141)/rgb(99, 118, 141)
     * #63768D/rgb(99, 118, 141)
     * rgb(99, 118, 141)/#63768D
    */
    '--sdp-ysxz-i': '颜色选择- icon',
    /**
     * #425060/#425060
     * rgb(66, 80, 96)/rgb(66, 80, 96)
     * #425060/rgb(66, 80, 96)
     * rgb(66, 80, 96)/#425060
    */
    '--sdp-szl-is': '设置类- icon色',
    /**
     * #EEEDED/#273341
     * rgb(238, 237, 237)/rgb(39, 51, 65)
     * #EEEDED/rgb(39, 51, 65)
     * rgb(238, 237, 237)/#273341
    */
    '--sdp-ycfgx': '右侧分割线',
    /**
     * #FFFFFF/#1D2732
     * rgb(255, 255, 255)/rgb(29, 39, 50)
     * #FFFFFF/rgb(29, 39, 50)
     * rgb(255, 255, 255)/#1D2732
    */
    '--sdp-szk-bjs': '设置框-背景色',
    /**
     * #222222/#FFFFFF
     * rgb(255, 255, 255)/rgb(255, 255, 255)
     * #FFFFFF/rgb(255, 255, 255)
     * rgb(255, 255, 255)/#FFFFFF
    */
    '--sdp-ycsz-glwzs': '右侧设置- 高亮文字色FFF',
    /**
     * #FFFFFF/#182B4B
     * rgb(255, 255, 255)/rgb(24, 43, 75)
     * #FFFFFF/rgb(24, 43, 75)
     * rgb(255, 255, 255)/#182B4B
    */
    '--sdp-cszj-ds': '参数组件-底色',
    /**
     * #B3B1B1/#B3B1B1
     * rgb(179, 177, 177)/rgb(179, 177, 177)
     * #B3B1B1/rgb(179, 177, 177)
     * rgb(179, 177, 177)/#B3B1B1
    */
    '--sdp-hxx': 'Hover虚线',
    /**
     * #F6F6F6/#1C2631
     * rgb(246, 246, 246)/rgb(28, 38, 49)
     * #F6F6F6/rgb(28, 38, 49)
     * rgb(246, 246, 246)/#1C2631
    */
    '--sdp-cszj-bjds': '参数组件-背景底色',
    /**
     * #E4E6EC/#333F4D
     * rgb(228, 230, 236)/rgb(51, 63, 77)
     * #E4E6EC/rgb(51, 63, 77)
     * rgb(228, 230, 236)/#333F4D
    */
    '--sdp-cszj-srbks': '参数组件-输入边框色',
    /**
     * #333333/#FFFFFF
     * rgb(51, 51, 51)/rgb(255, 255, 255)
     * #333333/rgb(255, 255, 255)
     * rgb(51, 51, 51)/#FFFFFF
    */
    '--sdp-cszj-tgl': '参数组件-Tab 高亮',
    /**
     * #263A61/#263A61
     * rgb(38, 58, 97)/rgb(38, 58, 97)
     * #263A61/rgb(38, 58, 97)
     * rgb(38, 58, 97)/#263A61
    */
    '--sdp-cszj-onns': '参数组件- OK按钮色',
    /**
     * #E5E7ED/#273341
     * rgb(229, 231, 237)/rgb(39, 51, 65)
     * #E5E7ED/rgb(39, 51, 65)
     * rgb(229, 231, 237)/#273341
    */
    '--sdp-cszj-bkfgx': '参数组件-边框分割线',
    /**
     * #444444/#A7B9CF
     * rgb(68, 68, 68)/rgb(167, 185, 207)
     * #444444/rgb(167, 185, 207)
     * rgb(68, 68, 68)/#A7B9CF
    */
    '--sdp-cszjsz-wzs1': '参数组件设置-文字色1',
    /**
     * #444444/#A7B9CF
     * rgb(68, 68, 68)/rgb(167, 185, 207)
     * #444444/rgb(167, 185, 207)
     * rgb(68, 68, 68)/#A7B9CF
    */
    '--sdp-cszjsz-wzs2': '参数组件设置-文字色2', // 81014 bug新加色号
    /**
     * #D8D8D8/#2E3A4C
     * rgb(216, 216, 216)/rgb(46, 58, 76)
     * #D8D8D8/rgb(46, 58, 76)
     * rgb(216, 216, 216)/#2E3A4C
    */
    '--sdp-zxz-nns': '周选择-按钮色',
    /**
     * #465864/#465864
     * rgb(70, 88, 100)/rgb(70, 88, 100)
     * #465864/rgb(70, 88, 100)
     * rgb(70, 88, 100)/#465864
    */
    '--sdp-cszjxz-hs1': '参数组建选择- hover色-01',
    /**
     * #465864/#465864
     * rgb(70, 88, 100)/rgb(70, 88, 100)
     * #465864/rgb(70, 88, 100)
     * rgb(70, 88, 100)/#465864
    */
    '--sdp-cszjxz-hs2': '参数组建选择- hover色-02', // 81014 bug新加色号
    /**
     * #465864/#4E5C6E
     * rgb(70, 88, 100)/rgb(78, 92, 110)
     * #465864/rgb(78, 92, 110)
     * rgb(70, 88, 100)/#4E5C6E
    */
    '--sdp-rl-hzts': '日历-hover主题色',
    /**
     * #0F203D/#0F203D
     * rgb(15, 32, 61)/rgb(15, 32, 61)
     * #0F203D/rgb(15, 32, 61)
     * rgb(15, 32, 61)/#0F203D
    */
    '--sdp-rl-bmxjg': '日历-斑马线间隔',
    /**
     * #446494/#446494
     * rgb(68, 100, 148)/rgb(68, 100, 148)
     * #446494/rgb(68, 100, 148)
     * rgb(68, 100, 148)/#446494
    */
    '--sdp-rl-bxys': '日历-单选颜色',
    /**
     * #344767/#344767
     * rgb(52, 71, 103)/rgb(52, 71, 103)
     * #344767/rgb(52, 71, 103)
     * rgb(52, 71, 103)/#344767
    */
    '--sdp-rl-wzjys': '日历-文字禁用色',
    /**
     * #FFFFFF/#1D2631
     * rgb(255, 255, 255)/rgb(29, 38, 49)
     * #FFFFFF/rgb(29, 38, 49)
     * rgb(255, 255, 255)/#1D2631
    */
    '--sdp-gjys-bjs': '编辑元素-背景色',
    /**
     * #F3F3F3/#1F2A37
     * rgb(243, 243, 243)/rgb(31, 42, 55)
     * #F3F3F3/rgb(31, 42, 55)
     * rgb(243, 243, 243)/#1F2A37
    */
    '--sdp-cy-bjs': '通用-背景色',
    /**
     * #052633/#052633
     * rgb(5, 38, 51)/rgb(5, 38, 51)
     * #052633/rgb(5, 38, 51)
     * rgb(5, 38, 51)/#052633
    */
    '--sdp-cy-bjfzs': '通用-背景辅助色',
    /**
     * transparent/#394858
     * transparent/rgb(57, 72, 88)
     * transparent/rgb(57, 72, 88)
     * transparent/#394858
    */
    '--sdp-xk-bcs': '选框-填充色',
    /**
     * #B8B8B8/#405062
     * rgb(184, 184, 184)/rgb(64, 80, 98)
     * #B8B8B8/rgb(64, 80, 98)
     * rgb(184, 184, 184)/#405062
    */
    '--sdp-xk-bks': '选框-边框色',
    /**
     * #222222/#F6F6F6
     * rgb(34, 34, 34)/rgb(246, 246, 246)
     * #222222/rgb(246, 246, 246)
     * rgb(34, 34, 34)/#F6F6F6
    */
    '--sdp-fx-mrwzs': '复选-默认文字色',
    /**
     * #DCDCDB/#304154
     * rgb(220, 220, 219)/rgb(48, 65, 84)
     * #DCDCDB/rgb(48, 65, 84)
     * rgb(220, 220, 219)/#304154
    */
    '--sdp-bg-bks': '表格-边框色',
    /**
     * #999999/#2F3D4D
     * rgb(153, 153, 153)/rgb(153, 153, 153)
     * #999999/rgb(153, 153, 153)
     * rgb(153, 153, 153)/#2F3D4D
    */
    '--sdp-jys': '禁用色',
    /**
     * #F6F6F6/#F6F6F6
     * rgb(246, 246, 246)/rgb(246, 246, 246)
     * #F6F6F6/rgb(246, 246, 246)
     * rgb(246, 246, 246)/#F6F6F6
    */
    '--sdp-wzgl': '文字高亮- F6',
    /**
     * #F6F6F6/#F6F6F6
     * rgb(246, 246, 246)/rgb(246, 246, 246)
     * #F6F6F6/rgb(246, 246, 246)
     * rgb(246, 246, 246)/#F6F6F6
    */
    '--sdp-wzgl2': '文字高亮- F6', // 81014 bug新加色号
    /**
     * #AAAAAA/#AAAAAA
     * rgb(170, 170, 170)/rgb(170, 170, 170)
     * #AAAAAA/rgb(170, 170, 170)
     * rgb(170, 170, 170)/#AAAAAA
    */
    '--sdp-is': 'icon色- AAA',
    /**
     * #999999/#999999
     * rgb(153, 153, 153)/rgb(153, 153, 153)
     * #999999/rgb(153, 153, 153)
     * rgb(153, 153, 153)/#999999
    */
    '--sdp-srk-bxwzs': '输入框-备选文字色',
    /**
     * #DDDFE6/#2F3D4D
     * rgb(221, 223, 230)/rgb(47, 61, 77)
     * #DDDFE6/rgb(47, 61, 77)
     * rgb(221, 223, 230)/#2F3D4D
    */
    '--sdp-srk-bks': '输入框-边框色',
    /**
     * #BBBABA/#BBBABA
     * rgb(187, 186, 186)/rgb(187, 186, 186)
     * #BBBABA/rgb(187, 186, 186)
     * rgb(187, 186, 186)/#BBBABA
    */
    '--sdp-srk-bt': '输入框-标题',
    /**
     * #C0C4CC/#979797
     * rgb(192, 196, 204)/rgb(151, 151, 151)
     * #C0C4CC/rgb(151, 151, 151)
     * rgb(192, 196, 204)/#979797
    */
    '--sdp-xlk-jts': '下拉框-箭头色',
    /**
     * #8B8B8B/#8B8B8B
     * rgb(139, 139, 139)/rgb(139, 139, 139)
     * #8B8B8B/rgb(139, 139, 139)
     * rgb(139, 139, 139)/#8B8B8B
    */
    '--sdp-qhi': '切换icon',
    /**
     * #777777/#AEBAC8
     * rgb(119, 119, 119)/rgb(174, 186, 200)
     * #777777/rgb(174, 186, 200)
     * rgb(119, 119, 119)/#AEBAC8
    */
    '--sdp-cszjq-is': '参数组件区- icon色',
    /**
     * #E6E6E6/#19222C
     * rgb(230, 230, 230)/rgb(25, 34, 44)
     * #E6E6E6/rgb(25, 34, 44)
     * rgb(230, 230, 230)/#19222C
    */
    '--sdp-qhqbj': '切换区背景',
    /**
     * #F1F1F1/#1C2731
     * rgb(241, 241, 241)/rgb(28, 39, 49)
     * #F1F1F1/rgb(28, 39, 49)
     * rgb(241, 241, 241)/#1C2731
    */
    '--sdp-zjbj': '组件背景',
    /**
     * #FFFFFF/#FFFFFF
     * rgb(255, 255, 255)/rgb(255, 255, 255)
     * #FFFFFF/rgb(255, 255, 255)
     * rgb(255, 255, 255)/#FFFFFF
    */
    '--sdp-nngl': '按钮高亮- FFF',
    /**
     * #EFF1F6/#232F3F
     * rgb(239, 241, 246)/rgb(35, 47, 63)
     * #EFF1F6/rgb(35, 47, 63)
     * rgb(239, 241, 246)/#232F3F
    */
    '--sdp-nn-fzds': '按钮-辅助底色',
    /**
     * #222222/#FFFFFF
     * rgb(34, 34, 34)/rgb(255, 255, 255)
     * #EFF1F6/rgb(255, 255, 255)
     * rgb(34, 34, 34)/#232F3F
    */
    '--sdp-fhwzs': '输入框/返回文字色',
    /**
     * #F5F7FA/#232F3F
     * rgb(245, 247, 250)/rgb(35, 47, 63)
     * #F5F7FA/rgb(35, 47, 63)
     * rgb(245, 247, 250)/#232F3F
    */
    '--sdp-srjybjs': '输入禁用背景色',
    /**
     * #FFFFFF/#2B3A4B
     * rgb(255, 255, 255)/rgb(43, 58, 75)
     * #EAE9EB/rgb(43, 58, 75)
     * rgb(255, 255, 255)/#2B3A4B
    */
    '--sdp-ysds1': '元素底色01',
    /**
     * #F7F7F7/#1D2732/#031636
    */
    '--sdp-zzlbjs': '元素放大/左侧栏背景色',
    /**
     * #465864/#465864
    */
    '--sdp-loading': 'loading',
    /**
     * #EDEFF2/#394858/#193E4D
    */
    '--sdp-cszjdx0': '参数组件-编辑/待多选填充',
    /**
     * #EDEFF2/#405062/#236387
    */
    '--sdp-cszjdxbk': '参数组件-编辑/多选边框',
    /**
     * #465864/#D6E0EB/#428AFF
    */
    '--sdp-cszjdxtc': '参数组件-编辑/多选填充',
    /**
     * #FFFFFF/#1D2732/#FFFFFF
    */
    '--sdp-cszjdxg': '参数组件-编辑/多选勾',
    /**
     * #BDBEC0/#283341/#495978
    */
    '--sdp-cszjisc': '参数组件-删除图标默认色',
    /**
     * #465864/#BDBEC0/#FFFFFF
    */
    '--sdp-cszjisc-hover': '参数组件-删除图标悬浮色',
    /**
     * #2C3E50/#E6E7EC/#E6E7EC
    */
    '--sdp-bgwz': '表格-文字颜色',
    /**
     * #BEBEC1/#425164/#495978
     */
    '--sdp-tagsc-default': 'tag-删除icon-默认',
    /**
     * #553CCE/#FFFFFF/#FFFFFF
     */
    '--sdp-tagsc-hover': 'tag-删除icon-hover',
    /**
     * #D5D9E5/#2D3743/#26334E
     */
    '--sdp-tabnn-bg': 'tab-按钮背景色',
    /**
     * 请不要删除噢
     */
    '--sdp-gradient-mask-layer': '移动看板 Picker 渐变遮罩层',
    /**
     * #E8E6E6/#5E6975/#446193
    */
    '--sdp-dcxxnn-bg': '导出选项按钮背景',
    /**
     * #920F0Ced/#B7CAE2/#B7CAE2
    */
    '--sdp-PDF': 'PDF',
    /**
     * #343434/#B7CAE2/#B7CAE2
    */
    '--sdp-CSV': 'CSV',
    /**
     * #1E74CF/#B7CAE2/#B7CAE2
    */
    '--sdp-Excel-97': 'Excel-97',
    /**
     * #0B325B/#B7CAE2/#B7CAE2
    */
    '--sdp-Excel-07': 'Excel-07',
    /*
    * #F1F1F1/#27323E/#1E2D47
    */
    '--sdp-check-disable-fill': '#F1F1F1/#27323E/#1E2D47多（单）选选框/禁用选择填充色',
    /*
    * #CCCCCC/#4D5F76/#405F98
    */
    '--sdp-jys2': '禁用色-2',
    /*
    * #CCCCCC/#2F3D4D/#404B62
    */
    '--sdp-wz-jys': '文字/禁用色',
    '--sdp-bg-rl-current': '#465864/#4D5C6E/#428AFF 日历-本期背景色',
    '--sdp-bg-rl-period': '#7993ED/#2959BE/#3E5C8D 日历-上期背景色',
    '--sdp-sjwybj': '#ecebfc/#4f7ed7/#182B4B 日历/事件物业填充色',
    '--sdp-sjwyzt': '#465864/#ffffff/#428AFF 日历/事件物业文字色',
    '--sdp-text-caret': '#000/#fff/#fff',
    '--sdp-app-bgs': 'APP-元素背景',
    '--sdp-rxwz': '参数组件区/运行文字',
    '--sdp-rxbg': '参数组件区/运行背景',
    '--sdp-rxbc': '参数组件区/运行边框',
    '--sdp-kbysbjs': '看板元素背景色',

    '--sdp-theme-green': '看板主题色 绿',
    '--sdp-theme-yellow': '看板主题色 黄',

    // Roc Begin
    '--dataFilterTextColor': '',
    '--settingPanelCheckColor': '',
    '--checkboxCheckedColor': '',
    '--tableSwitchBgColorBorderColor': '',
    '--chartsetTitleBarBorderColor': '',
    '--chartsetDatasetColor': '',
    // Roc End

    // Ryan Begin
    /**
     * #F6F6F6/#222D38/#0B1D3D
     */
    '--sdp-szk-btds': '右侧-设置框/New标题底色',
    '--mobileIndicatorBackgroundColor': '移动端图形卡片背景色',
    '--sdp-dh-bg': '顶部条背景',
    '--sdp-dh-biaoti': '顶部条标题颜色',
    '--sdp-dh-bottom-bg': '顶部条按钮背景颜色',
    '--sdp-dh-bottom-border': '顶部条按钮边框颜色',
    '--sdp-dh-bottom-color': '顶部条按钮颜色',
    '--sdp-dh-bottom-hover-bg': '顶部条按钮Hover背景颜色',
    '--sdp-dh-bottom-hover-border': '顶部条按钮Hover边框颜色',
    '--sdp-dh-bottom-hover-color': '顶部条按钮Hover颜色',
    '--sdp-dh-xiala-border-color': '顶部条下拉菜单边框颜色',
    '--sdp-dh-xiala-yingying': '顶部条下拉菜单阴影颜色',
    '--sdp-dh-xiala-bg': '顶部条下拉菜单背景颜色',
    '--sdp-dh-xiala-color': '顶部条下拉菜单文字颜色',
    '--sdp-dh-xiala-hover-bg': '顶部条下拉菜单Hover背景颜色',
    '--sdp-dh-xiala-hover-color': '顶部条下拉菜单Hover文字颜色',
    '--sdp-app-tabs-color': 'APP Tabs 颜色',
    '--sdp-app-tabs-bg-color': 'APP Tabs 背景颜色',
    '--sdp-app-tabs-selected-color': 'APP Tabs 选中颜色',
    '--sdp-app-tabs-selected-bg-color': 'APP Tabs 选中背景颜色',
    // Ryan End
    '--cursor-pointer-background':'卡片交互其他元素背景色',
    '--sdp-panel-active-bgc': '切换组件active背景色',

} as const

type VariableKeyType = keyof typeof DICTIONARY | keyof typeof UI_NO_VAR

type Variable = {
    [key in ThemeType]: {
        [key in VariableKeyType]: string
    }
}

const variable: Variable = {
    'sdp-classic-white': {
        '--sdp-zs': '#553CCE',
        '--sdp-el-b': '#EEEEEE',
        '--sdp-kgwxzbj': '#DCDFD6',
        '--sdp-fs1': '#FFFFFF',
        '--sdp-fs2': '#EAE9EB',
        '--sdp-hgls': '#263A61',
        '--sdp-hgls2': '#F5F7FA',
        '--sdp-qcgls': '#F95046',
        '--sdp-tsgls': '#5980BB',
        '--sdp-ysbjc': '#F7F7F7',
        '--sdp-zxz-wz': '#222222',
        '--sdp-bg-bcbk': '#DCDCDC',
        '--sdp-bg-bcsz': '#777777',
        '--sdp-bg-lhtwz': '#2C3D51',
        '--sdp-bg-tcbjs': '#FFFFFF',
        '--sdp-jddlsrbj': '#FFFFFF',
        '--sdp-rldx-xz': '#553CCE',
        '--sdp-zdbjs': '#F3F2FB',
        '--sdp-tb-srbj': '#EAE9EB',
        '--sdp-tb-bj': '#FFFFFF',
        '--sdp-cyfhwzs': '#222222',
        '--sdp-qxnnds': '#EEF0F5',
        '--sdp-sztc-srkbt': '#606266',
        '--sdp-sztc-bts': '#553CCE',
        '--sdp-zjbts': '#2C3E50',
        '--sdp-zjbxx-wzs': '#444444',
        '--sdp-zjbxx-wzs-bt': '#444444',
        '--sdp-cszj-tswzs': '#606266',
        '--sdp-cszj-yxibj': '#FFFFFF',
        '--sdp-sjj-hs': '#DFDDEF',
        '--sdp-wdzd-h': '#5980BB',
        '--sdp-sjjdxbj': '#D2DBF4',
        '--sdp-zcsjj-bj': '#FFFFFF',
        '--sdp-ysxzbks': '#F5F7FA',
        '--sdp-xxbt1': '#232323',
        '--sdp-xxbt2': '#232323',
        '--sdp-ycsz-srtswz': '#999999',
        '--sdp-ycsz-rskbt': '#222222',
        '--sdp-ycsz-hgls': '#DEE6F4', // 20230928 8770 需求 UI 修改白色主题颜色 #F4F7FA -> #DEE6F4
        '--sdp-ycsz-srk-bgs': '#FFFFFF',
        '--sdp-ycsz-srk-bcs': '#DCDFE6',
        '--sdp-ysxz-i': '#63768D',
        '--sdp-szl-is': '#425060',
        '--sdp-ycfgx': '#EEEDED',
        '--sdp-szk-bjs': '#FFFFFF',
        '--sdp-ycsz-glwzs': '#222222',
        '--sdp-cszj-ds': '#FFFFFF',
        '--sdp-hxx': '#B3B1B1',
        '--sdp-cszj-bjds': '#F6F6F6',
        '--sdp-cszj-srbks': '#E4E6EC',
        '--sdp-cszj-tgl': '#333333',
        '--sdp-cszj-onns': '#465864',
        '--sdp-cszj-bkfgx': '#E5E7ED',
        '--sdp-cszjsz-wzs1': '#444444',
        '--sdp-cszjsz-wzs2': '#455964',
        '--sdp-zxz-nns': '#D8D8D8',
        '--sdp-cszjxz-hs1': '#465864',
        '--sdp-cszjxz-hs2': '#DEE6F3',
        '--sdp-rl-hzts': '#465864',
        '--sdp-rl-bmxjg': '#0F203D',
        '--sdp-rl-bxys': '#446494',
        '--sdp-rl-wzjys': '#cccccc',
        '--sdp-gjys-bjs': '#FFFFFF',
        '--sdp-cy-bjs': '#F3F3F3',
        '--sdp-cy-bjfzs': '#052633',
        '--sdp-xk-bcs': 'transparent',
        '--sdp-xk-bks': '#B8B8B8',
        '--sdp-fx-mrwzs': '#222222',
        '--sdp-bg-bks': '#DCDCDB',
        '--sdp-jys': '#CCCCCC',
        '--sdp-wzgl': '#F6F6F6',
        '--sdp-wzgl2': '#333333',
        '--sdp-is': '#AAAAAA',
        '--sdp-srk-bxwzs': '#999999',
        '--sdp-srk-bks': '#DDDFE6',
        '--sdp-srk-bt': '#BBBABA',
        '--sdp-xlk-jts': '#C0C4CC',
        '--sdp-qhi': '#8B8B8B',
        '--sdp-cszjq-is': '#777777',
        '--sdp-qhqbj': '#E6E6E6',
        '--sdp-zjbj': '#F1F1F1',
        '--sdp-nngl': '#FFFFFF',
        '--sdp-nn-fzds': '#EFF1F6',
        '--sdp-fhwzs': '#222222',
        '--sdp-srjybjs': '#F5F7FA',
        '--sdp-ysds1': '#FFFFFF',
        '--sdp-zzlbjs': '#F7F7F7',
        '--sdp-resizable-handle-icon': 'url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAApSURBVHgB5ZChEQAACALB/SdhST2DWYONLxTuwxMLkrI3cMTzyOn0ZixgQwYNmIGoJwAAAABJRU5ErkJggg==)',
        '--sdp-cszjdxtc': '#465864',
        '--sdp-cszjdxg': '#FFFFFF',
        '--sdp-cszjisc': '#BDBEC0',
        '--sdp-cszjisc-hover': '#465864',
        '--sdp-bgwz': '#2C3E50',
        '--sdp-tagsc-default': '#BEBEC1',
        '--sdp-tagsc-hover': '#553CCE',
        '--sdp-tabnn-bg': '#D5D9E5',
        '--sdp-loading': '#465864',
        '--sdp-gradient-mask-layer': 'linear-gradient(to top, rgba(255,255,255,0.4), rgba(255,255,255,0.8))',
        '--sdp-dcxxnn-bg': '#E8E6E6',
        '--sdp-PDF': '#920F0Ced',
        '--sdp-CSV': '#343434',
        '--sdp-Excel-97': '#1E74CF',
        '--sdp-Excel-07': '#0B325B',
        '--sdp-cszjdx0': '#EDEFF2',
        '--sdp-cszjdxbk': '#EDEFF2',
        '--sdp-check-disable-fill': '#F1F1F1',
        '--sdp-jys2': '#CCCCCC',
        '--sdp-wz-jys': '#CCCCCC',
        '--sdp-bg-rl-current': '#465864',
        '--sdp-bg-rl-period': '#7993ED',
        '--sdp-sjwybj': '#ecebfc',
        '--sdp-sjwyzt': '#465864',
        '--sdp-text-caret': '#000000',
        '--sdp-app-bgs': '#ffffff',
        '--sdp-rxwz': '#2C3E50',
        '--sdp-rxbg': '#F4F4F4',
        '--sdp-rxbc': '#444444',
        '--sdp-kbysbjs': '#FFFFFF',
        '--sdp-theme-green': '#4EB38F',
        '--sdp-theme-yellow': '#F49B48',

        // Roc Begin
        '--dataFilterTextColor': '#222222',
        '--settingPanelCheckColor': '#606266',
        '--checkboxCheckedColor': '#553CCE',
        '--tableSwitchBgColorBorderColor': '#D6D6D6',
        '--chartsetTitleBarBorderColor': '#eeeeee',
        '--chartsetDatasetColor': '#606266',
        // Roc End

        // Ryan Begin
        '--sdp-szk-btds': '#F6F6F6',
        '--mobileIndicatorBackgroundColor': '#FFFFFF',
        '--sdp-dh-bg': 'rgb(245,247,250)',
        '--sdp-dh-biaoti': '#333',
        '--sdp-dh-bottom-bg': '#fff',
        '--sdp-dh-bottom-border': '#EDF0F5',
        '--sdp-dh-bottom-color': '#5c646b',
        '--sdp-dh-bottom-hover-bg': '#fff',
        '--sdp-dh-bottom-hover-border': '#4136A8',
        '--sdp-dh-bottom-hover-color': '#4136A8',
        '--sdp-dh-xiala-border-color': '#EDF0F5',
        '--sdp-dh-xiala-yingying': 'rgba(65,106,156,0.12)',
        '--sdp-dh-xiala-bg': '#fff',
        '--sdp-dh-xiala-color': '#333',
        '--sdp-dh-xiala-hover-bg': '#ECEBFC',
        '--sdp-dh-xiala-hover-color': '#4136A8',
        '--sdp-app-tabs-color': '#666666',
        '--sdp-app-tabs-bg-color': '#fffffe',
        '--sdp-app-tabs-selected-color': '#f8f8f8',
        '--sdp-app-tabs-selected-bg-color': '#1d4ed8',
        // Ryan End
        '--cursor-pointer-background':'rgba(0, 0, 0, 0.1)',
        '--sdp-panel-active-bgc': '#f8f8f8',
    },
    'sdp-dark-blue': {
        '--sdp-zs': '#553CCE',
        '--sdp-el-b': '#273341',
        '--sdp-kgwxzbj': '#425060',
        '--sdp-fs1': '#283341',
        '--sdp-fs2': '#2B3A4B',
        '--sdp-hgls': '#263A61',
        '--sdp-hgls2': '#263A61',
        '--sdp-qcgls': '#F95046',
        '--sdp-tsgls': '#5980BB',
        '--sdp-ysbjc': '#1C2630',
        '--sdp-zxz-wz': '#CDCDCD',
        '--sdp-bg-bcbk': '#303E4E',
        '--sdp-bg-bcsz': '#777777',
        '--sdp-bg-lhtwz': '#EAEBED',
        '--sdp-bg-tcbjs': '#212D3B',
        '--sdp-jddlsrbj': '#1D2631',
        '--sdp-rldx-xz': '#D6E0EB',
        '--sdp-zdbjs': '#283341',
        '--sdp-tb-srbj': '#283645',
        '--sdp-tb-bj': '#1F2A37',
        '--sdp-cyfhwzs': '#ACB0B4',
        '--sdp-qxnnds': '#24303E',
        '--sdp-sztc-srkbt': '#BBBABA',
        '--sdp-sztc-bts': '#FFFFFF',
        '--sdp-zjbts': '#F1F4F8',
        '--sdp-zjbxx-wzs': '#A7B9CF',
        '--sdp-zjbxx-wzs-bt': '#ffffff',
        '--sdp-cszj-tswzs': '#FFFFFF',
        '--sdp-cszj-yxibj': '#1F2A37',
        '--sdp-sjj-hs': '#2D3D50',
        '--sdp-wdzd-h': '#5980BB',
        '--sdp-sjjdxbj': '#2C3F5D',
        '--sdp-zcsjj-bj': '#1C242F',
        '--sdp-ysxzbks': '#232F3F',
        '--sdp-xxbt1': '#FFFFFF',
        '--sdp-xxbt2': '#B2C1D2',
        '--sdp-ycsz-srtswz': '#999999',
        '--sdp-ycsz-rskbt': '#FFFFFF',
        '--sdp-ycsz-hgls': '#354254',
        '--sdp-ycsz-srk-bgs': '#232F3F',
        '--sdp-ycsz-srk-bcs': '#232F3F',
        '--sdp-ysxz-i': '#63768D',
        '--sdp-szl-is': '#425060',
        '--sdp-ycfgx': '#273341',
        '--sdp-szk-bjs': '#1D2732',
        '--sdp-ycsz-glwzs': '#FFFFFF',
        '--sdp-cszj-ds': '#283341',
        '--sdp-hxx': '#B3B1B1',
        '--sdp-cszj-bjds': '#1C2631',
        '--sdp-cszj-srbks': '#333F4D',
        '--sdp-cszj-tgl': '#FFFFFF',
        '--sdp-cszj-onns': '#5A6A89',
        '--sdp-cszj-bkfgx': '#273341',
        '--sdp-cszjsz-wzs1': '#A7B9CF',
        '--sdp-cszjsz-wzs2': '#A7B9CF',
        '--sdp-zxz-nns': '#2E3A4C',
        '--sdp-cszjxz-hs1': '#465864',
        '--sdp-cszjxz-hs2': '#465864',
        '--sdp-rl-hzts': '#4E5C6E',
        '--sdp-rl-bmxjg': '#0F203D',
        '--sdp-rl-bxys': '#446494',
        '--sdp-rl-wzjys': '#344767',
        '--sdp-gjys-bjs': '#1D2631',
        '--sdp-cy-bjs': '#1F2A37',
        '--sdp-cy-bjfzs': '#052633',
        '--sdp-xk-bcs': '#394858',
        '--sdp-xk-bks': '#405062',
        '--sdp-fx-mrwzs': '#F6F6F6',
        '--sdp-bg-bks': '#304154',
        '--sdp-jys': '#2F3D4D',
        '--sdp-wzgl': '#F6F6F6',
        '--sdp-wzgl2': '#F6F6F6',
        '--sdp-is': '#AAAAAA',
        '--sdp-srk-bxwzs': '#999999',
        '--sdp-srk-bks': '#2F3D4D',
        '--sdp-srk-bt': '#BBBABA',
        '--sdp-xlk-jts': '#979797',
        '--sdp-qhi': '#8B8B8B',
        '--sdp-cszjq-is': '#AEBAC8',
        '--sdp-qhqbj': '#19222C',
        '--sdp-zjbj': '#242b32',
        '--sdp-nngl': '#FFFFFF',
        '--sdp-nn-fzds': '#232F3F',
        '--sdp-fhwzs': '#FFFFFF',
        '--sdp-srjybjs': '#232F3F',
        '--sdp-ysds1': '#2B3A4B',
        '--sdp-zzlbjs': '#1D2732',
        '--sdp-resizable-handle-icon': 'url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAApSURBVHgB5ZChEQAACALB/RdkGj2DWYONLxTuwxMLkrI3cMTzyOn0ZixbegcnTrhEqwAAAABJRU5ErkJggg==)',
        '--sdp-cszjdxtc': '#D6E0EB',
        '--sdp-cszjdxg': '#1D2732',
        '--sdp-cszjisc': '#425164',
        '--sdp-cszjisc-hover': '#ffffff',
        '--sdp-bgwz': '#E6E7EC',
        '--sdp-tagsc-default': '#425164',
        '--sdp-tagsc-hover': '#FFFFFF',
        '--sdp-tabnn-bg': '#2D3743',
        '--sdp-loading': '#465864',
        '--sdp-gradient-mask-layer': 'transparent',
        '--sdp-dcxxnn-bg': '#5E6975',
        '--sdp-PDF': '#B7CAE2',
        '--sdp-CSV': '#B7CAE2',
        '--sdp-Excel-97': '#B7CAE2',
        '--sdp-Excel-07': '#B7CAE2',
        '--sdp-cszjdx0': '#394858',
        '--sdp-cszjdxbk': '#405062',
        '--sdp-check-disable-fill': '#27323E',
        '--sdp-jys2': '#4D5F76',
        '--sdp-wz-jys': '#2F3D4D',
        '--sdp-bg-rl-current': '#4D5C6E',
        '--sdp-bg-rl-period': '#2959BE',
        '--sdp-sjwybj': '#4f7ed7',
        '--sdp-sjwyzt': '#ffffff',
        '--sdp-text-caret': '#ffffff',
        '--sdp-app-bgs': '#1C1C1E',
        '--sdp-rxwz': '#F1F4F8',
        '--sdp-rxbg': '#1D2631',
        '--sdp-rxbc': ' #444444',
        '--sdp-kbysbjs': '#212D3B',
        '--sdp-theme-green': '#0AFFAB',
        '--sdp-theme-yellow': '#F49B48',

        // Roc Begin
        '--dataFilterTextColor': '#f6f6f6e6',
        '--settingPanelCheckColor': '#F6F6F6',
        '--checkboxCheckedColor': '#F6F6F6',
        '--tableSwitchBgColorBorderColor': '#425060',
        '--chartsetTitleBarBorderColor': '#1D2631',
        '--chartsetDatasetColor': '#B2C1D2',
        // Roc End

        // Ryan Begin
        '--sdp-szk-btds': '#222D38',
        '--mobileIndicatorBackgroundColor': 'rgb(28, 28, 30)',
        '--sdp-dh-bg': 'rgba(33,45,59,1)',
        '--sdp-dh-biaoti': '#fff',
        '--sdp-dh-bottom-bg': '#283341',
        '--sdp-dh-bottom-border': '#304154',
        '--sdp-dh-bottom-color': '#AEBAC8',
        '--sdp-dh-bottom-hover-bg': '#283341',
        '--sdp-dh-bottom-hover-border': '#3C526C',
        '--sdp-dh-bottom-hover-color': '#4136A8',
        '--sdp-dh-xiala-border-color': '#1D2732',
        '--sdp-dh-xiala-yingying': 'rgba(0,0,0,1)',
        '--sdp-dh-xiala-bg': '#1D2732',
        '--sdp-dh-xiala-color': '#A7B9CF',
        '--sdp-dh-xiala-hover-bg': '#33404D',
        '--sdp-dh-xiala-hover-color': '#A7B9CF',
        '--sdp-app-tabs-color': '#999999',
        '--sdp-app-tabs-bg-color': '#1d1d1d',
        '--sdp-app-tabs-selected-color': '#111111',
        '--sdp-app-tabs-selected-bg-color': '#0affab',
        // Ryan End
        '--cursor-pointer-background':'#384453',
        '--sdp-panel-active-bgc': '#1D2631',
    },
    'sdp-deep-blue': {
        '--sdp-zs': '#428AFF',
        '--sdp-el-b': '#24375c',
        '--sdp-kgwxzbj': '#193E4D',
        '--sdp-fs1': '#182B4B',
        '--sdp-fs2': '#25385F',
        '--sdp-hgls': '#263A61',
        '--sdp-hgls2': '#263A61',
        '--sdp-qcgls': '#F95046',
        '--sdp-tsgls': '#5980BB',
        '--sdp-ysbjc': '#121D33',
        '--sdp-zxz-wz': '#F6F6F6',
        '--sdp-bg-bcbk': '#303E4E',
        '--sdp-bg-bcsz': '#777777',
        '--sdp-bg-lhtwz': '#F6F6F6',
        '--sdp-bg-tcbjs': '#182B4B',
        '--sdp-jddlsrbj': '#182B4B',
        '--sdp-rldx-xz': '#428AFF',
        '--sdp-zdbjs': '#263248',
        '--sdp-tb-srbj': '#25385F',
        '--sdp-tb-bj': '#182B4B',
        '--sdp-cyfhwzs': '#BDC0C3',
        '--sdp-qxnnds': '#182B4B',
        '--sdp-sztc-srkbt': '#BBBABA',
        '--sdp-sztc-bts': '#FFFFFF',
        '--sdp-zjbts': '#B7CAE2',
        '--sdp-zjbxx-wzs': '#A7B9CF',
        '--sdp-zjbxx-wzs-bt': '#A7B9CF',
        '--sdp-cszj-tswzs': '#4D75B9',
        '--sdp-cszj-yxibj': '#21417A',
        '--sdp-sjj-hs': '#1D345B',
        '--sdp-wdzd-h': '#5980BB',
        '--sdp-sjjdxbj': '#263A61',
        '--sdp-zcsjj-bj': '#1E283F',
        '--sdp-ysxzbks': '#2D2D2D',
        '--sdp-xxbt1': '#DCE6F2',
        '--sdp-xxbt2': '#A3B5CA',
        '--sdp-ycsz-srtswz': '#4D5E79',
        '--sdp-ycsz-rskbt': '#BBBABA',
        '--sdp-ycsz-hgls': '#263A61',
        '--sdp-ycsz-srk-bgs': '#182B4B',
        '--sdp-ycsz-srk-bcs': '#182B4B',
        '--sdp-ysxz-i': '#63768D',
        '--sdp-szl-is': '#425060',
        '--sdp-ycfgx': '#0B3342',
        '--sdp-szk-bjs': '#031636',
        '--sdp-ycsz-glwzs': '#FFFFFF',
        '--sdp-cszj-ds': '#182B4B',
        '--sdp-hxx': '#5980BB',
        '--sdp-cszj-bjds': '#182B4B',
        '--sdp-cszj-srbks': '#253B5F',
        '--sdp-cszj-tgl': '#F6F6F6',
        '--sdp-cszj-onns': '#428AFF',
        '--sdp-cszj-bkfgx': '#143948',
        '--sdp-cszjsz-wzs1': '#A7B9CF',
        '--sdp-cszjsz-wzs2': '#A7B9CF',
        '--sdp-zxz-nns': '#25385F',
        '--sdp-cszjxz-hs1': '#263A61',
        '--sdp-cszjxz-hs2': '#263A61',
        '--sdp-rl-hzts': '#428AFF',
        '--sdp-rl-bmxjg': '#0F203D',
        '--sdp-rl-bxys': '#446494',
        '--sdp-rl-wzjys': '#344767',
        '--sdp-gjys-bjs': '#142038',
        '--sdp-cy-bjs': '#121D33',
        '--sdp-cy-bjfzs': '#052633',
        '--sdp-xk-bcs': '#092B4A',
        '--sdp-xk-bks': '#236387',
        '--sdp-fx-mrwzs': '#F6F6F6',
        '--sdp-bg-bks': '#2B4375',
        '--sdp-jys': '#404B62',
        '--sdp-wzgl': '#F6F6F6',
        '--sdp-wzgl2': '#F6F6F6',
        '--sdp-is': '#AAAAAA',
        '--sdp-srk-bxwzs': '#4D5E79',
        '--sdp-srk-bks': '#253B5F',
        '--sdp-srk-bt': '#BBBABA',
        '--sdp-xlk-jts': '#999999',
        '--sdp-qhi': '#8B8B8B',
        '--sdp-cszjq-is': '#B0BCCA',
        '--sdp-qhqbj': '#203351',
        '--sdp-zjbj': '#132442',
        '--sdp-nngl': '#FFFFFF',
        '--sdp-nn-fzds': '#25385F',
        '--sdp-fhwzs': '#FFFFFF',
        '--sdp-srjybjs': '#182B4B',
        '--sdp-ysds1': '#263A61',
        '--sdp-zzlbjs': '#031636',
        '--sdp-resizable-handle-icon': 'url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAApSURBVHgB5ZChEQAACALB/RdkGj2DWYONLxTuwxMLkrI3cMTzyOn0ZixbegcnTrhEqwAAAABJRU5ErkJggg==)',
        '--sdp-cszjdxtc': '#428AFF',
        '--sdp-cszjdxg': '#FFFFFF',
        '--sdp-cszjisc': '#495978',
        '--sdp-cszjisc-hover': '#FFFFFF',
        '--sdp-bgwz': '#E6E7EC',
        '--sdp-tagsc-default': '#495978',
        '--sdp-tagsc-hover': '#FFFFFF',
        '--sdp-tabnn-bg': '#26334E',
        '--sdp-loading': '#428AFF',
        '--sdp-gradient-mask-layer': 'transparent',
        '--sdp-dcxxnn-bg': '#446193',
        '--sdp-PDF': '#B7CAE2',
        '--sdp-CSV': '#B7CAE2',
        '--sdp-Excel-97': '#B7CAE2',
        '--sdp-Excel-07': '#B7CAE2',
        '--sdp-cszjdx0': '#193E4D',
        '--sdp-cszjdxbk': '#236387',
        '--sdp-check-disable-fill': '#1E2D47',
        '--sdp-jys2': '#405F98',
        '--sdp-wz-jys': '#404B62',
        '--sdp-bg-rl-current': '#428AFF',
        '--sdp-bg-rl-period': '#3E5C8D',
        '--sdp-sjwybj': '#182B4B',
        '--sdp-sjwyzt': '#428AFF',
        '--sdp-text-caret': '#ffffff',
        '--sdp-app-bgs': '#182B4B',
        '--sdp-rxwz': '#F6F6F6',
        '--sdp-rxbg': '#182B4B',
        '--sdp-rxbc': ' #253B5F',
        '--sdp-kbysbjs': '#182B4B',
        '--sdp-theme-green': '#4EB38F',
        '--sdp-theme-yellow': '#F49B48',

        // Roc Begin
        '--dataFilterTextColor': 'var(--邓鹏--dataFilterTextColor, red)',
        '--settingPanelCheckColor': 'var(--邓鹏--settingPanelCheckColor, red)',
        '--checkboxCheckedColor': 'var(--邓鹏--checkboxCheckedColor, red)',
        '--tableSwitchBgColorBorderColor': 'var(--邓鹏--tableSwitchBgColorBorderColor, red)',
        '--chartsetTitleBarBorderColor': 'var(--邓鹏--chartsetTitleBarBorderColor, red)',
        '--chartsetDatasetColor': 'var(--邓鹏--chartsetDatasetColor, red)',
        // Roc End

        // Ryan Begin
        '--sdp-szk-btds': '#0B1D3D',
        '--mobileIndicatorBackgroundColor': 'rgb(28, 28, 30)',
        '--sdp-dh-bg': 'rgba(24,43,75,1)',
        '--sdp-dh-biaoti': '#fff',
        '--sdp-dh-bottom-bg': '#182B4B',
        '--sdp-dh-bottom-border': '#2B4375',
        '--sdp-dh-bottom-color': '#B0BCCA',
        '--sdp-dh-bottom-hover-bg': '#182B4B',
        '--sdp-dh-bottom-hover-border': '#3B5895',
        '--sdp-dh-bottom-hover-color': '#428AFF',
        '--sdp-dh-xiala-border-color': '#031636',
        '--sdp-dh-xiala-yingying': 'rgba(0,0,0,1)',
        '--sdp-dh-xiala-bg': '#031636',
        '--sdp-dh-xiala-color': '#A7B9CF',
        '--sdp-dh-xiala-hover-bg': '#263A61',
        '--sdp-dh-xiala-hover-color': '#A7B9CF',
        '--sdp-app-tabs-color': '#94a6bb',
        '--sdp-app-tabs-bg-color': '#182b4b',
        '--sdp-app-tabs-selected-color': '#121d33',
        '--sdp-app-tabs-selected-bg-color': '#4ca5ff',
        // Ryan End
        '--cursor-pointer-background':'#35486A',
        '--sdp-panel-active-bgc': '#1D2631',
    }
}

const repeatKeys = [] as Array<string>
const themeList = ['sdp-classic-white', 'sdp-dark-blue', 'sdp-deep-blue']

function extTheme(varTheme: Record<string, Record<'sdp-classic-white' | 'sdp-dark-blue' | 'sdp-deep-blue', string>>, developerName: string, devColorFlag = 'red') {
  for (const varKey in varTheme) {
    if (Object.prototype.hasOwnProperty.call(varTheme, varKey)) {
      if (Object.prototype.hasOwnProperty.call(variable['sdp-classic-white'], varKey)) {
        console.error('变量名重复: ' + varKey)
        repeatKeys.push(varKey)
      } else {
        themeList.forEach(themeKey => {
          variable[themeKey][varKey] = varTheme[varKey][themeKey] || `var(--${developerName}--${varKey}, ${devColorFlag})`
        })
      }
    }
  }
}

extTheme({
  '--warnLineNameListFontColor': {
    'sdp-classic-white': '#666666',
    'sdp-dark-blue': '#67798D',
    'sdp-deep-blue': '',
  },
  '--chartsetHeaderInputBackground': {
    'sdp-classic-white': '#E8E7E9',
    'sdp-dark-blue': '#2B3A4B',
    'sdp-deep-blue': '',
  },
  '--chartsetHeaderInputColor': {
    'sdp-classic-white': '#888888',
    'sdp-dark-blue': '#979797',
    'sdp-deep-blue': '',
  },
  '--chartsetInputInnerBorderColor': {
    'sdp-classic-white': '#C0C4CC',
    'sdp-dark-blue': '#232F3F',
    'sdp-deep-blue': '',
  },
  '--displayDataSetButtonBoxShadow': {
    'sdp-classic-white': '-2px 6px 6px 0 #0000001a',
    'sdp-dark-blue': '0 2px 4px 0 #0000001a',
    'sdp-deep-blue': 'var(--邓鹏--displayDataSetButtonBoxShadow, 0 2px 4px 0 red)',
  },
  '--displayDataSetIconColor': {
    'sdp-classic-white': '#606266',
    'sdp-dark-blue': '#8E8E8E',
    'sdp-deep-blue': '',
  },
  '--tableScrollBar': {
    'sdp-classic-white': '#F2F4F8',
    'sdp-dark-blue': '#26323F',
    'sdp-deep-blue': '',
  },
  '--dataSetNodeDisabledFont': {
    'sdp-classic-white': '#cccccc',
    'sdp-dark-blue': '#2f3d4d',
    'sdp-deep-blue': '',
  },
  '--dataSetTreeParentDeleteIcon': {
    'sdp-classic-white': '#553CCE',
    'sdp-dark-blue': '#FA342F',
    'sdp-deep-blue': '',
  },
  '--cardStyleSettingFontColor': {
    'sdp-classic-white': '#222222',
    'sdp-dark-blue': '#EFEFEF',
    'sdp-deep-blue': '',
  },
  '--inputNumberArrowColor': {
    'sdp-classic-white': '#606266',
    'sdp-dark-blue': '#919191',
    'sdp-deep-blue': '',
  },
  '--inputNumberArrowBorderColor': {
    'sdp-classic-white': '#DCDFE6',
    'sdp-dark-blue': '#2B3746',
    'sdp-deep-blue': '',
  },
  '--inputNumberArrowBackgroundColor': {
    'sdp-classic-white': '#f5f7fa',
    'sdp-dark-blue': '#00000000',
    'sdp-deep-blue': '',
  },
  '--disabledFontSettingBgColor': {
    'sdp-classic-white': '#f5f7fa',
    'sdp-dark-blue': '#2F3D4D',
    'sdp-deep-blue': '',
  },
  '--dialogFontSettingColor': {
    'sdp-classic-white': '#606266',
    'sdp-dark-blue': '#EAEBED',
    'sdp-deep-blue': '',
  },
  '--fourQuadrantFontColor': {
    'sdp-classic-white': '#333333',
    'sdp-dark-blue': '#BBBABA',
    'sdp-deep-blue': '',
  },
  '--fourQuadrantAddTabsBtnBorderColor': {
    'sdp-classic-white': '#E4E7ED',
    'sdp-dark-blue': '#273341',
    'sdp-deep-blue': '',
  },
  '--chartsetGrayColor': {
    'sdp-classic-white': '#303133',
    'sdp-dark-blue': '#C8CBCF',
    'sdp-deep-blue': '',
  },
  '--elementTextSelectBgc': {
    'sdp-classic-white': '#ffffff',
    'sdp-dark-blue': '#2B3A4B',
    'sdp-deep-blue': '',
  },
  '--paramsSettingBorderColor': {
    'sdp-classic-white': '#dddddd',
    'sdp-dark-blue': '#273341',
    'sdp-deep-blue': '',
  },
  '--selectHoverBgc': {
    'sdp-classic-white': '#F5F7FA',
    'sdp-dark-blue': '#6e829733',
    'sdp-deep-blue': '',
  },
  '--paramsQuickCalendarSelectColor': {
    'sdp-classic-white': '#455964',
    'sdp-dark-blue': '#E5EBF2',
    'sdp-deep-blue': '',
  },
  '--selectActiveColor': {
    'sdp-classic-white': '#553CCE',
    'sdp-dark-blue': '#E5EBF2',
    'sdp-deep-blue': '',
  },
  '--cardSettingFontColor': {
    'sdp-classic-white': '#333333',
    'sdp-dark-blue': '#FFFFFF',
    'sdp-deep-blue': '',
  },
  '--mobileParamasCalendarSelectFont': {
    'sdp-classic-white': '#444444',
    'sdp-dark-blue': '#FFFFFF',
    'sdp-deep-blue': '',
  },
  '--mobileParamasCalendarSelectBgc': {
    'sdp-classic-white': '#e9ebf2',
    'sdp-dark-blue': '#272727',
    'sdp-deep-blue': '',
  },
  '--textSettingBarBg': {
    'sdp-classic-white': '#f3f3f3',
    'sdp-dark-blue': '#273341',
    'sdp-deep-blue': '',
  },
  '--dialogBorderColor': {
    'sdp-classic-white': '#dddddd',
    'sdp-dark-blue': '#25313F',
    'sdp-deep-blue': '',
  },
  '--dialogBorderBgc': {
    'sdp-classic-white': '#ffffff',
    'sdp-dark-blue': '#25313F',
    'sdp-deep-blue': '',
  },
  '--chartsetRightSecondColor': {
    'sdp-classic-white': '#333333',
    'sdp-dark-blue': '#B2C1D2',
    'sdp-deep-blue': '',
  },
  '--roc-inherit-dark-resizable-handle-icon': {
    'sdp-classic-white': 'url(data:image/svg+xml;base64,PHN2ZyBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjojZmZmZmZmMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjYiIGhlaWdodD0iNiI+PHBhdGggZD0iTTYgNkgwVjQuMmg0LjJWMEg2djZ6IiBvcGFjaXR5PSIuMzAyIi8+PC9zdmc+)',
    'sdp-dark-blue': 'url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDx0aXRsZT7jgJE8L3RpdGxlPgogICAgPGcgaWQ9Iumhtemdoi0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iMjYyNyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTY2Ny4wMDAwMDAsIC05NDIuMDAwMDAwKSIgZmlsbD0iIzVENUU2MSIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPGcgaWQ9IuOAkSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNjY3LjAwMDAwMCwgOTQyLjAwMDAwMCkiPgogICAgICAgICAgICAgICAgPHBvbHlnb24gaWQ9Iui3r+W+hCIgcG9pbnRzPSI2IDYgMCA2IDAgNC4yIDQuMiA0LjIgNC4yIDAgNiAwIj48L3BvbHlnb24+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==)',
    'sdp-deep-blue': 'url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDx0aXRsZT7jgJE8L3RpdGxlPgogICAgPGcgaWQ9Iumhtemdoi0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iMjYyNyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTY2Ny4wMDAwMDAsIC05NDIuMDAwMDAwKSIgZmlsbD0iIzVENUU2MSIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPGcgaWQ9IuOAkSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNjY3LjAwMDAwMCwgOTQyLjAwMDAwMCkiPgogICAgICAgICAgICAgICAgPHBvbHlnb24gaWQ9Iui3r+W+hCIgcG9pbnRzPSI2IDYgMCA2IDAgNC4yIDQuMiA0LjIgNC4yIDAgNiAwIj48L3BvbHlnb24+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==)',
  },
  '--roc-inherit-dark-container-el-bg-color': {
    'sdp-classic-white': '#ffffff',
    'sdp-dark-blue': '#25313F',
    'sdp-deep-blue': '#25313F',
  },
  '--roc-inherit-dark-container-el-border-color': {
    'sdp-classic-white': '#E8E8E8',
    'sdp-dark-blue': '#273341',
    'sdp-deep-blue': '#273341',
  },
  '--roc-inherit-dark-container-el-disable-color': {
    'sdp-classic-white': '#C8C8C8',
    'sdp-dark-blue': '#444444',
    'sdp-deep-blue': '#444444',
  },
  '--roc-inherit-dark-container-el-disable-bg-color': {
    'sdp-classic-white': '#F9F9F9',
    'sdp-dark-blue': '#25313F',
    'sdp-deep-blue': '#25313F',
  },
}, '邓鹏', 'red')

extTheme({
  // 操作指引/box-shadow
  '--sdp-guide-panel-box-shadow': {
    'sdp-classic-white': '0px 2px 4px 0px rgba(0, 0, 0, 0.1)',
    'sdp-dark-blue': '0px 2px 4px 0px rgba(0, 0, 0, 0.2)',
    'sdp-deep-blue': 'unset',
  },
  // 操作指引/步骤背景色
  '--sdp-guide-entry-bg': {
    'sdp-classic-white': '#F4F2FF',
    'sdp-dark-blue': '#1F2429',
    'sdp-deep-blue': '#182B4B',
  },
  // 操作指引/大步骤
  '--sdp-guide-entry-color': {
    'sdp-classic-white': '#552CCE',
    'sdp-dark-blue': '#FFFFFF',
    'sdp-deep-blue': '#FFFFFF',
  },
  // 操作指引/提示区域背景色
  '--sdp-guide-tips-bg': {
    'sdp-classic-white': 'rgba(85, 60, 206, 0.15)',
    'sdp-dark-blue': 'rgba(85, 60, 206, 0.15)',
    'sdp-deep-blue': 'rgba(66, 138, 255, 0.15)',
  },
  // 操作指引/知道了底色
  '--sdp-guide-got-btn-bg': {
    'sdp-classic-white': '#DBD5FA',
    'sdp-dark-blue': '#272D32',
    'sdp-deep-blue': '#182B4B',
  },
  // 操作指引/框背景色
  '--sdp-guide-panel-bg': {
    'sdp-classic-white': '#FFFFFF',
    'sdp-dark-blue': '#101923',
    'sdp-deep-blue': '#031636',
  },
  // 操作指引/小步骤已选
  '--sdp-操作指引/小步骤已选': {
    'sdp-classic-white': '#553CCE',
    'sdp-dark-blue': '#D3CBF9',
    'sdp-deep-blue': '#C5D9FA',
  },
  // 操作指引/已选边框
  '--sdp-guide-entry-border-color-by-done': {
    'sdp-classic-white': 'rgba(85, 60, 206, 0.5)',
    'sdp-dark-blue': 'rgba(85, 60, 206, 0.5)',
    'sdp-deep-blue': 'rgba(66, 138, 255, 0.5)',
  },
  // 弹窗中操作指引大步骤边框
  '--sdp-guide-content-box-border': {
    'sdp-classic-white': '#553CCE80',
    'sdp-dark-blue': '#553CCE80',
    'sdp-deep-blue': '#428AFF',
  },
  // 弹窗中操作指引中间的小背景
  '--sdp-guide-content-box-background': {
    'sdp-classic-white': '#F4F2FF',
    'sdp-dark-blue': 'unset',
    'sdp-deep-blue': 'unset',
  },
  // 操作指引小点点
  '--sdp-chart-set-step-dot':{
    'sdp-classic-white': '#dbd5fa',
    'sdp-dark-blue': '#272d32',
    'sdp-deep-blue': '#272d32',
  },
  '--sdp-chart-set-step-dot-active':{
    'sdp-classic-white': '#553CCE',
    'sdp-dark-blue': '#553CCE',
    'sdp-deep-blue': '#428AFF',
  },
  // 操作指引文字
  '--sdp-chart-set-step-text':{
    'sdp-classic-white': '#999999',
    'sdp-dark-blue': '#999999',
    'sdp-deep-blue': '#4D5E79',
  },
  // 操作指引文字高亮状态
  '--sdp-chart-set-step-text-active':{
    'sdp-classic-white': '#553CCE',
    'sdp-dark-blue': '#D3CBF9',
    'sdp-deep-blue': '#C5D9FA',
  },
}, '操作指引')

repeatKeys.length && console.error(['请注意! 请注意! 以下变量名重复了:', ...repeatKeys].join('\n'))

// 修改变量颜色
export function changeVarible(themes: ThemeType[] = [], {
  color,
  changeColor
} = {
  color: '',
  changeColor: ''
}) {
  if (!color || !changeColor) return

  Object.keys(variable).forEach((key: any) => {
    if (themes.includes(key)) {
      for (const k in variable[key]) {
        if (Object.prototype.hasOwnProperty.call(variable[key], k)) {
            if (variable[key][k] === color) {
              variable[key][k] = changeColor
            }
        }
      }
    }
  })
}

// 自测
// changeVarible(['sdp-classic-white'], {
//   color: '#553CCE',
//   changeColor: '#3F7BAB'
// })

export default variable
