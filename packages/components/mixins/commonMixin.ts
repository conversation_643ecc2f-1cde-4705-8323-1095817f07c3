import { DATA_THEME, THEME_TYPE } from 'packages/assets/constant'
import { generateUUID } from '../../assets/utils/globalTools'
import Bus from 'vue'
import { BoardRecord } from '../../base/board/mixins/boardRecord'
import { clearColumnValues } from 'packages/base/board/displayPanel/params/paramElement/selectOrdinary/api'
import fieldAliasMixin from './fieldAliasMixin'
import onlyFieldMixin from './onlyFieldMixin'
import displayDataMixin from './displayDataMixin'
import themeMixin from './themeMixin'
import { EVENT_BUS } from '../../base/board/displayPanel/constants'
export const ALL_PROJECT_NAME = {
  SUBSYSTEM: 'SUBSYSTEM',
  INFRASYS_ER: 'INFRASYS_ER',
  EMS: 'EMS',
  OMS: 'OMS',
  SBI: 'SBI',
  APP_KUNLUN_BI: 'KunlunBi'
}
// ems和infrsys
export const PROJECT_NAME = [ALL_PROJECT_NAME.INFRASYS_ER, ALL_PROJECT_NAME.EMS]

export default {
  mixins: [fieldAliasMixin, onlyFieldMixin, displayDataMixin, themeMixin],
  props: {
    env: {
      type: Object,
      default: () => ({
        // 应用项目的项目名称
        projectName: '',
        // 模块 一级菜单国际码
        modelI18Key: '',
        // 页面 二级菜单国际码
        menuI18Key: '',
        // 渠道 PC APP SUB
        channel: '',
        // 是否为线上版本
        production: true,
        // 应用项目的项目模块
        projectModule: '',
        // 畅联传入的userId
        externalUserId: '',
        // 是否开启数据集别名
        isOpenAliasDict: false
      })
    },
    // 是否为模板看板
    isTemplateBoard: {
      type: Boolean,
      default: false,
    },
    isTemplatePreview: {
      type: Boolean,
      default: false
    },
    isNewTemplateBoard: {
      type: Boolean,
      default: false
    },
    // 当前使用的项目
    useProject: {
      type: Object,
      default: () => ({}),
    },
    skipLinks: {
      type: Object,
      default: () => ({
        // 导出管理url
        exportManagement: ''
      }),
    },
    // 计划任务勾选id
    planId: {
      type: String,
      default: ''
    },
    // webSocket的Url
    webSocketUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      sdpBus: new Bus(),
      boardRecord: new BoardRecord({ limit: 5, vm: this, isMobile: this.isMobile })
    }
  },
  inject: {
    kanbanIdByParent: { type: String, default: '', from: 'kanbanId' }
  },

  provide() {
    return Object.assign({
      sdpBus: this.sdpBus,
      boardRecord: this.boardRecord,
      isTemplateBoard: this.isTemplateBoard,
      isTemplatePreview: this.isTemplatePreview,
      isNewTemplateBoard: this.isNewTemplateBoard,
      skipLinks: this.skipLinks,
      webSocketUrl: this.webSocketUrl,
      getRootDom: () => this.$el,
    }, this.kanbanIdByParent ? {} : {
      kanbanId: 'kanbanId_' + (this.boardInfo?.id || generateUUID()) + '_'
    })
  },

  beforeDestroy() {
    const sbiOptions = this.utils?.sbiOptions || {}
    const isSbiAddPreview = sbiOptions.isSbiDashPreview && sbiOptions.isAddDailyConcernElement
    if (isSbiAddPreview) {
      clearColumnValues()
      this.sdpBus.$off(EVENT_BUS.GET_TOP_DATA, this.getTopData)
      return
    }
    // document.body.setAttribute(DATA_THEME, '')
    if (PROJECT_NAME.includes(this.env.projectName)) {
      document.body.setAttribute(DATA_THEME, '')
    } else {
      document.body.setAttribute(DATA_THEME, THEME_TYPE.classicWhite)
    }
    clearColumnValues()

    this.sdpBus.$off(EVENT_BUS.GET_TOP_DATA, this.getTopData)
    this.sdpBus.$off(EVENT_BUS.EXTERNAL_CALL, this.externalCall)
  },

  created() {
    this.sdpBus.$on(EVENT_BUS.GET_TOP_DATA, this.getTopData)
    this.sdpBus.$on(EVENT_BUS.EXTERNAL_CALL, this.externalCall)
  },

  methods: {
    externalCall(data) {
      console.log(data, 'externalCall')
      this.$emit('externalCall', data)
    },
    getTopData(obj, type) {
      if (typeof type === 'string') {
        return Object.assign(obj, {
          [type]: this[type] || ''
        })
      } else if (Array.isArray(type)) {
        return Object.assign(obj, type.reduce((pre, key) => {
          pre[key] = this[key]
          return pre
        }, {}))
      }
    }
  }
}
