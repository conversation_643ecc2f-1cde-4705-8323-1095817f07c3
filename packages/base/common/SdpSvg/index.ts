/**
 * @Description:
 * <AUTHOR> Deng
 * @date 2019/7/11
*/

import SvgFontSize from './src/grid/SvgFontSize' // Font Size
import SvgFontColor from './src/grid/SvgFontColor' // Font Color
import SvgFontWeight from './src/grid/SvgFontWeight' // Font Weight
import SvgFontStyle from './src/grid/SvgFontStyle' // Font Style
import SvgFontDecoration from './src/grid/SvgFontDecoration' // Font Decoration
import SvgJustifyContentLeft from './src/grid/SvgJustifyContentLeft' // 左对齐
import SvgJustifyContentRight from './src/grid/SvgJustifyContentRight' // 右对齐
import SvgJustifyContentCenter from './src/grid/SvgJustifyContentCenter' // 水平居中对齐
import SvgAlignItemsTop from './src/grid/SvgAlignItemsTop' // 上对齐
import SvgAlignItemsMiddle from './src/grid/SvgAlignItemsMiddle' // 垂直居中对齐
import SvgAlignItemsBottom from './src/grid/SvgAlignItemsBottom' // 下对齐
import SvgRemoveRow from './src/grid/SvgRemoveRow' // 删除行
import SvgRemoveCol from './src/grid/SvgRemoveCol' // 删除列
import SvgSplitMergeCells from './src/grid/SvgSplitMergeCells' // 拆分合并单元格
import SvgBorderOption from './src/grid/SvgBorderOption' // Border Option
import SvgBorderOptionTop from './src/grid/SvgBorderOptionTop' // Border Top
import SvgBorderOptionBottom from './src/grid/SvgBorderOptionBottom' // Border Bottom
import SvgBorderOptionLeft from './src/grid/SvgBorderOptionLeft' // Border Left
import SvgBorderOptionRight from './src/grid/SvgBorderOptionRight' // Border Right
import SvgBorderOptionNone from './src/grid/SvgBorderOptionNone' // Border None
import SvgBorderOptionOuter from './src/grid/SvgBorderOptionOuter' // Border Outer
import SvgBorderWidth from './src/grid/SvgBorderWidth' // Border Width
import SvgBackgroundColor from './src/grid/SvgBackgroundColor' // Background Color
import SvgClearCells from './src/grid/SvgClearCells' // 清除单元格
import SvgInsetRow from './src/grid/SvgInsetRow' // 插入行
import SvgInsetCol from './src/grid/SvgInsetCol' // 插入列
import SvgUndo from './src/grid/SvgUndo' // 撤销
import SvgRedo from './src/grid/SvgRedo' // 恢复
import SvgExtendCells from './src/grid/SvgExtendCells' // 扩展单元
import SvgDataFilter from './src/grid/SvgDataFilter' // 数据过滤
import SvgGridDefine from './src/grid/SvgGridDefine' // 表头定义
import SvgSort from './src/grid/SvgSort' // 排序
import SvgFoldHide from './src/grid/SvgFoldHide' // 折叠隐藏

export const GridSVG = {
  SvgFontSize,
  SvgFontColor,
  SvgFontWeight,
  SvgFontStyle,
  SvgFontDecoration,
  SvgJustifyContentLeft,
  SvgJustifyContentRight,
  SvgJustifyContentCenter,
  SvgAlignItemsTop,
  SvgAlignItemsMiddle,
  SvgAlignItemsBottom,
  SvgRemoveRow,
  SvgRemoveCol,
  SvgSplitMergeCells,
  SvgBorderOption,
  SvgBorderOptionTop,
  SvgBorderOptionBottom,
  SvgBorderOptionLeft,
  SvgBorderOptionRight,
  SvgBorderOptionNone,
  SvgBorderOptionOuter,
  SvgBorderWidth,
  SvgBackgroundColor,
  SvgClearCells,
  SvgInsetRow,
  SvgInsetCol,
  SvgUndo,
  SvgRedo,
  SvgExtendCells,
  SvgDataFilter,
  SvgGridDefine,
  SvgSort,
  SvgFoldHide,
}
