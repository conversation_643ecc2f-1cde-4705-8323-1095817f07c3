import Vue from 'vue'
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin'
import { PUBLISH_TO_ME, SIM_TYPE } from './constants'

// 树查询
export function getList(api, params) {
  return api.get('/bi/metaFolder/getDateSetTree', { params })
}

// 表格查询
export function getListTable(api, params) {
  const { projectName, ..._params } = params
  if (_params.folderId === PUBLISH_TO_ME) {
    return getSbiMyFolderDateSetTree(api, params)
  }
  if (projectName === ALL_PROJECT_NAME.SBI) {
    return api.post('/bi/sbiBoardElement/list/dataset', { ..._params })
  }
  return api.get('/bi/metaDataset/pageListForPerson', { params: _params })
}

// 查询是否有智能填报数据集
export function getSmartFillDataSet(api) {
  return api.post('/bi/datasetInfo/countTenantDatasetInfo')
}

// 表格查询-兼容指标预览
export function getListTablePreview(api, params) {
  const { projectName, ..._params } = params
  if (_params.folderId === PUBLISH_TO_ME) {
    return getSbiMyFolderDateSetTree(api, params)
  }
  if (projectName === ALL_PROJECT_NAME.SBI) {
    // return api.post('/bi/sbiBoardElement/list/dataset', { ..._params })
    return api.post('/bi/sbiBoardElement/list/allDataset', { ..._params }) // 8654需求后端换成这个接口
  }
  return api.get('/bi/metaDataset/pageListForPerson', { params: _params })
}

// 指标树查询
export function getIndicatorList(api, params) {
  const { ..._params } = params
  const apiIndexTreeUrl = Vue.prototype?.$getSystemConfig?.('api.urls.indexTree')
  if (apiIndexTreeUrl) {
     return api.post(apiIndexTreeUrl, _params)
  }
  if (SIM_TYPE.isSim) {
    return api.post('/query/sim/index/classify/list', _params)
  }
  return api.post('/query/entIndex/classify/list', _params).then((res) => {
     const list = res?.filter(item => item.datasourceName) || []
     return list.map(item => {
        return {
          ...item,
          id: item.datasourceId || ''
        }
      })
  })
}

// 指标表格查询
export function getIndicatorListTable(api, params) {
  const { projectName, ..._params } = params
  const apiIndexListUrl = Vue.prototype?.$getSystemConfig?.('api.urls.indexList')
  if (apiIndexListUrl) {
    return api.post(apiIndexListUrl, _params)
  }
  if (SIM_TYPE.isSim) {
    return api.post('/query/sim/index/list', _params)
  }
  return api.post('/query/entIndex/list', _params)
}

// 发布给我数据集列表
export function getSbiMyFolderDateSetTree(api, params) {
  const { page, limit, name, } = params
  return api.post('/bi/sbiBoardElement/page/custom/dataset', { page, limit, name, })
}

// 树查询
export function getTemplateList(api, params) {
  return api.post('/bi/datasetInfo/page/template', params)
}

// 获取企业数据树形结构
export function getMetaFolderTree(api, params) {
  return api.get('/bi/metaFolder/getDateSetTree', { params })
}

// 查询租户文件夹树形结构
export function getTenantFolderTree(api, folderType, channel) {
  return api.get(`bi/metaFolder/getTenantFolderTree/${folderType}/${channel}`)
}

// 获取企业数据集
export function getExternalTemplateList(api, params) {
  return api.post('/bi/external/dataset/pageList', params)
}

// sbi获取自定义数据集和标准数据集
// folderType=9&manage=1(自定义）  folderType=10&manage=1（标准）
export function getSbiMetaFolderDateSetTree(api, params) {
  const { projectName, ..._params } = params
  if (projectName === ALL_PROJECT_NAME.SBI) {
    return api.post('/bi/sbiMetaFolder/getDatasetFolderTree', { ..._params })
  }
  return api.get('bi/sbiMetaFolder/getDateSetTree', { params: _params })
}
