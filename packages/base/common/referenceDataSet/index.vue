<template>
  <div
    class="referenceDataSet"
    v-if="hackReset"
    data-cy-DatasetReferenceDialog
  >
    <DialogRoot
      :title="onlyIndex ? this.$t('sdp.views.PleaseSelectIndicator') : title"
      :visible="dialogVisible"
      :width="onlyIndex ? '1017px' : isHttpMode ? '1000px' : '780px'"
      :class="onlyIndex ? 'sdp-only-index' : ''"
      @confirm="confirm"
      @close="closeDia"
    >
      <div class="content">
        <div class="left" v-if="isHttpMode">
          <div class="left-tab-group-content" v-if="hasIndicatorTab">
            <div
              :class="[
                'radio-item',
                'radio-item-icon',
                {
                  'selected': treeTabsActive === 'dataset' ,
                },
                {'full-radio-item': !hasIndicatorTab}]"
              @click="handelTreeTabsChange('dataset')"
            >
              <span >{{ $t('sdp.views.dataSet') }}</span>
            </div>
            <div
              :class="[
                'radio-item',
                'radio-item-icon',
                {
                  'selected': treeTabsActive === 'indicator' ,
                }]"
              style="50%"
              @click="handelTreeTabsChange('indicator')"
              v-if="hasIndicatorTab"
            >
              <span >{{ $t('sdp.views.Index') }}</span>
            </div>
          </div>
          <folder-tree
            ref="tree"
            :empty-text="$t('sdp.views.noData')"
            :treeData="treeData"
            @updateTable="getTableData"
            v-show="treeTabsActive==='dataset'"
          ></folder-tree>
          <indicator-folder-tree
            ref="indicatorTree"
            :empty-text="$t('sdp.views.noData')"
            :treeData="treeData"
            @updateTable="getIndicatorTableData"
            v-show="treeTabsActive==='indicator' && !onlyIndex"
          ></indicator-folder-tree>
          <sim-indicator-folderTree
            ref="indicatorTree"
            :empty-text="$t('sdp.views.noData')"
            :treeData="treeData"
            @updateTable="getIndicatorTableData"
            v-show="treeTabsActive==='indicator' && onlyIndex"
          >
          </sim-indicator-folderTree>

        </div>
        <div class="border" v-if="isHttpMode"></div>
        <div class="right">
          <div class="top" v-if="isHttpMode">
            <el-select v-show="treeTabsActive === 'indicator' && !onlyIndex" :popper-class="getCurrentThemeClass()"  style="width: 120px;margin-right: 5px;" size="mini" v-model="indicatorSearchType">
              <el-option v-for="item in indicatorSearchTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-select v-show="treeTabsActive === 'indicator' && onlyIndex" :popper-class="getCurrentThemeClass() + ' sdp-only-index-option'" clearable :placeholder="$t('sdp.views.IndicatorType')" class="sdp-only-index-sle"  style="width: 160px;margin-right: 12px;" size="mini" v-model="indicatorType" @change="updateIndicatorTable">
              <el-option v-for="item in indicatorTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-input
              :placeholder="onlyIndex ? $t('sdp.views.IndicatorNameCode') : $t('sdp.views.search')"
              @keyup.enter.native="getSearchData"
              v-model.trim="searchName"
              class="searchName"
              size="mini"
              :disabled="searchDisabled"
            >
              <i
                slot="append"
                :class="['iconfont icon-sdp-shujutianbaosousuo', searchDisabled ? 'is-disabled': '']"
                @click="getSearchData"
              ></i>
            </el-input>
          </div>
          <div v-show="treeTabsActive==='dataset'">
            <el-table
              v-if="!isHttpMode || !isTemplateBoard"
              v-show="!isShowTemplate"
              ref="multipleTable"
              :data="isHttpMode ? tableData : boardDatasetList"
              :empty-text="$t('sdp.views.noData')"
              :height="tableHeight"
              class="tabel-lazy"
              :class="{'header-no-checkbox': isHttpMode, 'hideCheckAll': dataSets.selection === 'single'}"
              @selection-change="handleSelectionChange"
              @select="rowSelect"
              v-loadMore="{ loadMore, isHttpMode }"
            >
              <el-table-column
                type="selection"
                width="55"
                :selectable="checkSelectableDataset"
              ></el-table-column>
              <el-table-column
                :prop="isHttpMode ? 'name' : 'labeName'"
                :label="$t('sdp.placeholder.name')"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                v-if="isHttpMode"
                prop="is_fact"
                :label="$t('sdp.views.dataSetType')"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <span>{{ $t(`sdp.views.${scope.row.is_fact === '0' ? 'FactDataset': 'dimensionDataset'}`) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                prop="isFact"
                :label="$t('sdp.views.dataSetType')"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <span>{{ $t(`sdp.views.${scope.row.isFact === '0' ? 'FactDataset': 'dimensionDataset'}`) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="isHttpMode"
                prop="model_type"
                :label="$t('sdp.views.source')"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.data_source_name || scope.row.dataSourceName">{{scope.row.data_source_name || scope.row.dataSourceName}}</span>
                  <span v-else>{{ $t(`sdp.views.${scope.row.model_type === FLOW_MODEL_DATASET ? 'streamingModel' : 'batchModel'}`) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                prop="modelType"
                :label="$t('sdp.views.source')"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.data_source_name || scope.row.dataSourceName">{{scope.row.data_source_name || scope.row.dataSourceName}}</span>
                  <span v-else>{{ $t(`sdp.views.${scope.row.modelType === FLOW_MODEL_DATASET ? 'streamingModel' : 'batchModel'}`) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-show="treeTabsActive==='indicator' && !tableLoading && !onlyIndex">
            <el-table
              ref="indicatorTable"
              :data="indicatorTableData"
              :empty-text="$t('sdp.views.noData')"
              :height="tableHeight"
              :class="{'header-no-checkbox': isHttpMode}"
              @selection-change="handleSelectionChangeIndicator"
              v-loadMoreIndicator="{ loadMoreIndicator }"
              class="indicatorTable"
              v-loading="tableLoading"
            >
            <!-- @select="rowSelect" -->
              <el-table-column
                type="selection"
                :selectable="checkSelectable"
                width="55"
              ></el-table-column>
              <el-table-column
                prop="indexName"
                :label="$t('sdp.placeholder.name')"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <i class="icon-sdp-paishengzhibiao indicator-icon" v-if="isSim ? scope.row.indexType === 1 : scope.row.indexType === 2"></i>
                  <i class="icon-sdp-fuhezhibiao indicator-icon" v-else-if="isSim ? scope.row.indexType === 2 : scope.row.indexType === 3"></i>
                  <i class="icon-sdp-yuanzizhibiao indicator-icon" v-else-if="isSim && scope.row.indexType === 0"></i>
                  {{(langCode === 'zh' ? (isSim ? scope.row.indexNameZh : scope.row.indexAlias) : (isSim ? scope.row.indexNameEn : scope.row.define)) || scope.row.indexName}}
                  <!-- indexAlias 别名 -->
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="indexAlias"
                :label="$t('sdp.views.alias')"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ scope.row.indexAlias}}
                </template>
              </el-table-column> -->

              <el-table-column
                :label="$t('sdp.views.indexType')"
              >
                <template slot-scope="scope">
                  <!-- 0是原子 1派生 2复合 -->
                  {{ isSim ? (
                        scope.row.indexType === 0 ? `${$t('sdp.views.atomicIndex')}` : scope.row.indexType === 1 ? `${$t('sdp.views.derivedIndex')}`:`${$t('sdp.views.compositeIndex')}`
                      ) : (
                        scope.row.indexType === 2 ? `${$t('sdp.views.derivedIndex')}`:`${$t('sdp.views.compositeIndex')}`
                      )
                  }}
                </template>
             </el-table-column>

              <el-table-column
                prop="dataSourceName"
                :label="$t('sdp.views.source')"
                show-overflow-tooltip
              >

              </el-table-column>
            </el-table>
          </div>
          <!-- SIM系统 -->
          <div v-show="treeTabsActive==='indicator' && !tableLoading && onlyIndex" class="sim-indicator-container">
            <div style="height: 248px;overflow-y: auto;">
              <el-table
                ref="indicatorTable"
                :data="indicatorTableData"
                :empty-text="$t('sdp.views.noData')"
                :height="'100%'"
                :class="{'header-no-checkbox': isHttpMode}"
                @selection-change="handleSelectionChangeIndicator"
                @select-all="handleSelectAllIndicator"
                class="indicatorTable sdp-only-index-table"
                v-loading="tableLoading"
                @sort-change="sortChange"
              >
              <!-- @select="rowSelect" -->
                <el-table-column
                  type="selection"
                  width="46"
                  :selectable="checkSelectable"
                ></el-table-column>
                <el-table-column
                  prop="indexName"
                  :label="$t('sdp.views.indexName')"
                  show-overflow-tooltip
                  sortable="custom"
                  width="257"
                >
                  <template slot-scope="scope">
                    <i class="icon-sdp-paishengzhibiao indicator-icon" v-if="scope.row.indexType === 1"></i>
                    <i class="icon-sdp-fuhezhibiao indicator-icon" v-else-if="scope.row.indexType === 2"></i>
                    <i class="icon-sdp-yuanzizhibiao indicator-icon" v-else-if="scope.row.indexType === 0"></i>
                    {{(langCode === 'zh-CN' ? scope.row.indexNameZh : scope.row.indexNameEn) || scope.row.indexName}}
                    <!-- indexAlias 别名 -->
                  </template>
                </el-table-column>

                <el-table-column
                  prop="indexCode"
                  :label="$t('sdp.views.indexCode')"
                  sortable="custom"
                  width="257"
                ></el-table-column>

                <el-table-column
                  prop="timeType"
                  :label="$t('sdp.views.IndicatorGranularity')"
                  show-overflow-tooltip
                  sortable="custom"
                >
                  <template slot-scope="scope">
                    {{[$t('sdp.views.calendarDay'), $t('sdp.views.week'), $t('sdp.views.calendarMonth'), $t('sdp.views.season'), $t('sdp.views.Years')][scope.row.timeType]}}
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <el-pagination
              v-if="indicatorPageObj.pageTotal > indicatorPageObj.pageSize"
              @size-change="handleIndicatorSizeChange"
              @current-change="handleIndicatorCurrentChange"
              :current-page="indicatorPageObj.currentPage"
              :page-size="indicatorPageObj.pageSize"
              :layout="layout"
              :total="indicatorPageObj.pageTotal"
              class="sim-indicator-pagination"
            >
            </el-pagination>
          </div>
          <dataSetTable
            v-show="isShowTemplate && isDataWarehouseModelingSwitchShowTemplate"
            ref="dataSetTable"
            :dataSets="dataSets"
            :templateData="templateData"
            @loadMore="getTemplateList"
            @clearRowIds="clearRowIds"
          />
        </div>
      </div>
    </DialogRoot>
  </div>
</template>
<script>
import folderTree from './components/folderTree'
import indicatorFolderTree from './components/indicatorFolderTree'
import simIndicatorFolderTree from './components/simIndicatorFolderTree'
import dataSetTable from './components/dataSetTable'
import { getList, getIndicatorList, getListTable, getIndicatorListTable, getTemplateList, getSmartFillDataSet, getMetaFolderTree, getSbiMetaFolderDateSetTree, getExternalTemplateList } from './api'
import { FLOW_MODEL_DATASET, TYPE_LIST, PREVIEW_STATUS, EXTERNAL_FOLDER, EXTERNAL_CUSTOM, EXTERNAL_STANDARD, CUSTOM_FOLDER_TYPE, STANDARD_FOLDER_TYPE } from 'packages/assets/constant'
import { STORAGE_KEY, getStorageKey } from '@/environmentOption'
import { ALL_PROJECT_NAME } from '../../../components/mixins/commonMixin'
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog'
import * as api from './api'
import { PUBLISH_TO_ME, SIM_TYPE } from './constants'

// 文件夹类型常量
const TYPE_DATASET = '1'
// 企业数据集文件夹类型

// 空间类型常量
const PRIVATE = '1'
const TEMPLATE_DATASET = 'templateDataset'
const FILLED_DATASET = 'fillDatasetInfo'
const isDev = process.env.NODE_ENV === 'development'
export default {
  name: 'referenceDataSet',
  mixins: [mixin_dialog],
  inject: {
    utils: {},
    configs: {
      default() {
        return {}
      }
    },
    commonData: {
      default() {
        return {}
      }
    },
    isTemplateBoard: {
      default: false
    },
    getCurrentThemeClass: {
      default: () => () => {}
    },
    langCode: { default: '' }
  },
  components: {
    folderTree,
    indicatorFolderTree,
    dataSetTable,
    simIndicatorFolderTree
  },
  data () {
    return {
      isSim: SIM_TYPE.isSim,
      treeTabsActive: 'dataset', // dataset indicator
      hasIndicatorTab: false, // 是否存在指标tab
      FLOW_MODEL_DATASET,
      EXTERNAL_FOLDER,
      EXTERNAL_CUSTOM,
      EXTERNAL_STANDARD,
      selectedFolder: {}, // 选中的文件夹
      title: this.$t('sdp.placeholder.plsInput'), // 标题
      hackReset: true, // 是否销毁组件
      treeData: [], // 树形结构
      searchName: '', // 搜索0
      folderType: TYPE_DATASET,
      externalFolder: '',
      spaceType: PRIVATE,
      pageObj: {
        currentPage: 1, // 选中页数
        pageSize: 40, // 选中页数的数量
        pageTotal: 1, // 总数
      },
      indicatorPageObj: {
        currentPage: 1,
        pageSize: 50,
        pageTotal: 0
      },
      indicatorSearchType: 1,
      indicatorSearchTypeList: [
        {
          value: 1,
          label: this.$t('sdp.views.name')
        },
        // {
        //   value: 5,
        //   label: this.$t('sdp.views.alias')
        // },
        {
          value: 2,
          label: this.$t('sdp.views.code')
        }
      ],
      indicatorType: '',
      indicatorTypeList: [
        {
          value: 0,
          label: this.$t('sdp.views.atomicIndex')
        },
        {
          value: 1,
          label: this.$t('sdp.views.derivedIndex')
        },
        {
          value: 2,
          label: this.$t('sdp.views.compositeIndex')
        }
      ],
      orderInfo: {},
      layout: 'total, prev, pager, next',
      tableLoading: false, // 表格loading
      tableData: [], // 表格列表数据
      tableHeight: 340, // 表格头固定
      rowIds: [], // 数据集id
      dataSetArray: [], // 数据集
      searchDisabled: false,
      // 模板数据
      TEMPLATE_DATASET,
      // 企业数据
      isShowTemplate: false,
      templateData: {
        rowIds: [],
        dataSetArray: [],
        tableLoading: false,
        tableData: [],
        total: 0,
        params: {
          'condition': '',
          'limit': 40,
          'orderBy': '',
          'orderField': '',
          'page': 1,
          projectName: this.utils.env?.projectName || this.platform,
          folderType: this.boardInfo?.folderType || '',
        },
      },
      platform: getStorageKey(STORAGE_KEY.environmentOptionKeyItem),

      indicatorTableData: [] //指标表格
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dataSets: {
      type: Object
    },
    boardInfo: {
      type: Object,
      default: () => {},
    },
    boardDatasetList: Array,
    defaultAuthorityBoardId: {
      type: String,
      default: ''
    },
    dataWarehouseModelingSwitch: {
      type: String,
      default: '0'
    }
  },
  computed: {
    isEnterprise() {
      return this.utils.editType === PREVIEW_STATUS.OPEN_SOURCE || this.utils.editType === PREVIEW_STATUS.DESIGNER
    },
    api () {
      return this.utils.api || function () { }
    },
    rows () {
      return this.dataSets.datasetList ? this.dataSets.datasetList.map(item => item.id) : []
    },

    isRefMode() {
      return Boolean(this.commonData.isBoardDesign)
    },

    isHttpMode() {
      return !this.isRefMode
    },
    boardId() {
      return this.defaultAuthorityBoardId
    },
    isDataWarehouseModelingSwitchShowTemplate() {
      return this.dataWarehouseModelingSwitch !== '0'
    },
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs.options?.onlyIndex
    }
  },
  created () {
    this.open()
  },
  directives: {
    loadMore: {
      bind (el, binding) {
        if (!binding.value.isHttpMode) return
        const selectWrap = el.querySelector('.el-table__body-wrapper')
        function wrapperScroll() {
          const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight
          if (scrollDistance < 100) {
            binding.value.loadMore()
          }
        }
        selectWrap['wrapperScroll'] = wrapperScroll
        selectWrap.addEventListener('scroll', selectWrap['wrapperScroll'])
      },
      unbind(el, binding) {
        if (!binding.value.isHttpMode) return
        const selectWrap = el.querySelector('.el-table__body-wrapper')
        if (selectWrap['wrapperScroll']) {
          selectWrap.removeEventListener('scroll', selectWrap['wrapperScroll'])
          selectWrap['wrapperScroll'] = null
        }
      },
    },
    loadMoreIndicator: {
      bind (el, binding) {
        const selectWrap = el.querySelector('.indicatorTable .el-table__body-wrapper')
        function wrapperScrollIndicator() {
          const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight
          if (scrollDistance < 100) {
            binding.value.loadMoreIndicator()
          }
        }
        selectWrap['wrapperScrollIndicator'] = wrapperScrollIndicator
        selectWrap.addEventListener('scroll', selectWrap['wrapperScrollIndicator'])
      },
      unbind(el, binding) {
        const selectWrap = el.querySelector('.indicatorTable .el-table__body-wrapper')
        if (selectWrap['wrapperScrollIndicator']) {
          selectWrap.removeEventListener('scroll', selectWrap['wrapperScrollIndicator'])
          selectWrap['wrapperScrollIndicator'] = null
        }
      },
    }
  },
  methods: {
    // 处理SIM系统指标表格每页显示条数变化
    handleIndicatorSizeChange(pageSize) {
      this.indicatorPageObj.pageSize = pageSize
      this.indicatorPageObj.currentPage = 1
      this.updateIndicatorTable()
    },
    // 处理SIM系统指标表格页码变化
    handleIndicatorCurrentChange(currentPage) {
      this.indicatorPageObj.currentPage = currentPage
      const params = this.getIndicatorTableParams()
      this.refreshIndicatorTable(params)
    },
    sortChange ({
      prop,
      order
    }) {
      order = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null
      this.orderInfo = {
        orderByColumn: prop,
        isAsc: order
      }
      this.updateIndicatorTable()
    },
    // 数据集跟指标tab切换
    handelTreeTabsChange(tab) {
      this.treeTabsActive = tab
      if (tab === 'indicator') {
        this.getIndicatorTreeData()
      } else {
        this.getTreeData()
      }
    },
    checkSelectableDataset(row) {
      return !this.rows.includes(row.code_id || row.id)
    },
    checkSelectable(row) {
      return this.onlyIndex ? !this.rows.includes(row.id) : row.datasetId
    },
    // 表格懒加载
    loadMore () {
      if (this.tableData.length === this.pageObj.pageTotal) return
      if (!this.tableLoading) {
        if ([EXTERNAL_CUSTOM, EXTERNAL_STANDARD, EXTERNAL_FOLDER].includes(this.externalFolder)) {
          this.templateData.params.page++
          this.getExternalTemplateList({ name: this.searchName, folderId: this.getFolderId(), folderType: this.externalFolder })
          return
        }
        this.pageObj.currentPage++
        const params = this.getTableParams()
        this.refreshTable(params)
      }
    },
    // 指标表格懒加载
    loadMoreIndicator () {
      if (this.indicatorTableData.length === this.indicatorPageObj.pageTotal) return
      if (!this.tableLoading) {
        this.indicatorPageObj.currentPage++
        const params = this.getIndicatorTableParams()
        this.refreshIndicatorTable(params)
      }
    },
    getFolderId() {
      const folderId = this.selectedFolder.id || ''
      return folderId.indexOf('-') === 0 ? '' : folderId
    },
    // 关闭组件
    closeDia () {
      this.$emit('closeDia')
    },
    // 初始化数据
    open () {
      if (this.onlyIndex) {
        this.treeTabsActive = 'indicator'
        this.getIndicatorTreeData()
      } else {
         this.isHttpMode ? this.getTreeData() : this.setMultipleTable(this.boardDatasetList)
      }
      let projectName = this.utils.env?.projectName || this.platform
      if (projectName === ALL_PROJECT_NAME.SBI) {
         getIndicatorList(this.api).then(async (res) => {
          res?.length && (this.hasIndicatorTab = true)
        })
      }
    },
    // 获取树形结构数据
    getTreeData () {
      const params = {
        folderType: this.folderType,
        spaceType: this.spaceType,
        boardId: this.boardId
      }
      // 获取文件夹
      this.$nextTick(async () => {
        this.$refs.tree.loading = true
        const templateItem = {
          id: TEMPLATE_DATASET,
          label: this.$t('sdp.views.TemplateDatasets'),
          parentId: '-1',
        }
        const filledItem = {
          id: FILLED_DATASET,
          label: this.$t('sdp.views.SmartDataEntryDatasets'),
          parentId: '-1',
        }
        if (this.isTemplateBoard) {
          this.treeData = [templateItem]
          this.$refs.tree.setHighlight(TEMPLATE_DATASET)
          this.updateTable(true)
          this.$refs.tree.loading = false
          return
        }

        let externalTemplate = {} // 企业数据集
        let datasetTree = []
        let projectName = this.utils.env?.projectName || this.platform
        if (projectName === ALL_PROJECT_NAME.SBI) {
          let externalParams = {
            folderType: (this.configs && this.configs.type === 'template') ? CUSTOM_FOLDER_TYPE : (this.boardInfo?.folderType || (isDev ? '11' : '')),
            projectName: this.utils.env?.projectName || this.platform,
          }
          const res = await getSbiMetaFolderDateSetTree(this.api, externalParams)
          res.forEach((rootFolder, rootIndex) => {
            if (!rootFolder.id) rootFolder.id = '-' + (rootIndex + 1)
            if (rootFolder.folderType === EXTERNAL_STANDARD) {
              rootFolder.label = this.$t('sdp.views.StandardDatasets')
            } else if (rootFolder.folderType === EXTERNAL_CUSTOM) {
              rootFolder.label = this.$t('sdp.views.CustomDatasets')
            }
          })
          if (this.utils.sbiOptions?.folderType === CUSTOM_FOLDER_TYPE) {
            // 发布给我的数据集支持被自定义看板引用，
            // 在仪表板，数据大屏，移动看板的自定义看板引用数据集页面增加发布给我数据集文件夹，
            // 展示自定义数据集中的所有发布给我数据集；
            const result = await api.getSbiMyFolderDateSetTree(this.api, this.getTableParams())
            const rows = result?.rows || []
            if (rows.length) {
              res.push({
                id: PUBLISH_TO_ME,
                label: this.$t('sdp.views.PublishToMeBoard'),
                parentId: "-1",
              })
            }
          }
          datasetTree = res
        } else if (projectName === ALL_PROJECT_NAME.EMS) {
          let externalParams = {
            folderType: EXTERNAL_FOLDER,
            manage: this.spaceType,
          }
          externalTemplate = await this.getFolderTreeList('getMetaFolderTree', externalParams, EXTERNAL_FOLDER)
          externalTemplate.label = this.$t('sdp.views.EnterpriseDatasets')
        }
        getList(this.api, params).then(async (res) => {
          const platDatasetList = res?.filter(item => item.label) || []
          let treeData = platDatasetList.length ? [
            {
              id: '-PlatformDatasets',
              rolecheck: true,
              label: this.$t('sdp.views.dataSet'),
              parentId: '-1',
              children: platDatasetList,
            }
          ] : []

          const { params: curParams } = this.templateData
          if (projectName === ALL_PROJECT_NAME.SBI) {
            treeData = treeData.concat(datasetTree)
          } else if (projectName === ALL_PROJECT_NAME.EMS) {
            let externalTable = await getExternalTemplateList(this.api, curParams, { folderType: EXTERNAL_FOLDER })
            if (externalTable?.rows?.length) {
              treeData.push(externalTemplate)
            }
          } else if (platDatasetList.length) {
            const platParams = this.getTableParams()
            let platTable = await getListTable(this.api, platParams)
            if (!platTable?.rows?.length) treeData = []
          }
          this.treeData = treeData
          let templateTable = await getTemplateList(this.api, curParams)
          if (templateTable?.rows?.length && this.isDataWarehouseModelingSwitchShowTemplate) {
            this.treeData.push(templateItem)
          }
          try {
            let filledItemTable = await getSmartFillDataSet(this.api)
            if (filledItemTable > 0) {
              this.treeData.push(filledItem)
            }
          } catch (err) {
            console.log('%c [ err ]-591', 'font-size:13px; background:#c36599; color:#ffa9dd;', err)
          }
          if (this.treeData.length) {
            this.getTableData(this.treeData[0].id, this.treeData[0])
            this.treeData[0]?.id && this.$refs.tree.setHighlight(this.treeData[0].id)
          }
          // 全选
          // 表格查询
          // this.getListTable()
          this.$refs.tree.loading = false
        })
      })
    },
    // 获取指标树数据
    getIndicatorTreeData() {
        // 获取文件夹
      this.$nextTick(async () => {
        this.$refs.indicatorTree.loading = true
        getIndicatorList(this.api, {
          onlyIndex: this.onlyIndex,
          pageNum: 1,
          pageSize: 9999,
          filterEmptyIndexTree: true,
          hasPriv: true,
          indexMarket: true,
          needIndexRcaTree: false,
          onlyFolder: true,
          releaseFlag: 1
        }).then(async (res) => {
          if (this.onlyIndex) {
            const iterate = (list) => {
              list.forEach(item => {
                item.id = item.classifyId
                item.children = iterate(item.children)
              })
              return list
            }
            const list = iterate(res.data || [])
            this.treeData = [
              {
                id: '-1',
                classifyId: '-1',
                classifyName: this.$t('sdp.views.All'),
                children: list
              }
            ]
          } else {
            this.treeData = res
          }
          if (this.treeData.length) {
            this.getIndicatorTableData(this.treeData[0].id, this.treeData[0])
            this.treeData[0]?.id && this.$refs.indicatorTree.setHighlight(this.treeData[0].id)
          }
          // 全选
          // 表格查询
          // this.getListTable()
          this.$refs.indicatorTree.loading = false
        })
      })
    },
    async getFolderTreeList(apiType, params, folderType) {
      let template = {}
      await api[apiType](this.api, params).then((res) => {
        template = {
          id: '',
          folderType,
          rolecheck: true,
          // label: this.$t('sdp.views.BusinessDatasets'),
          label: this.$t('sdp.views.CustomDatasets'),
          parentId: '-1',
          children: res.filter(item => item.label)
        }
      })
      return template
    },
    getTableParams () {
      return {
        page: this.pageObj.currentPage,
        limit: this.pageObj.pageSize,
        spaceType: this.spaceType,
        folderId: this.getFolderId(),
        name: this.searchName,
        isEnable: this.isEnterprise ? '0' : '',
        boardId: this.boardId,
        projectName: this.utils.env?.projectName || this.platform,
        folderType: this.selectedFolder.folderType,
      }
    },
    getChildrenIds(list = [], children, type = 'id') {
      children?.forEach((item) => {
        list.push(item[type])
        if (item.children?.length) {
          this.getChildrenIds(list, item.children, type)
        }
      })
      return list
    },
    getIndicatorTableParams () {
      if (this.onlyIndex) {
        const params = {
          saveSearchValueFlag: false,
          hasFavorite: false,
          hasPriv: true,
          releaseFlag: '1'
        }
        if ([0, 1, 2].includes(this.indicatorType)) {
          params.indexType = this.indicatorType
        }
        return Object.assign({
          // 使用真实分页
          pageSize: this.indicatorPageObj.pageSize,
          pageNum: this.indicatorPageObj.currentPage,
          searchValue: this.searchName,
          classifyIds: this.selectedFolder.classifyId === '-1' ? [] : [this.selectedFolder.classifyId, ...this.getChildrenIds([], this.selectedFolder.children, 'classifyId')]
         }, {
          ...params
         }, this.orderInfo)
      }
      const isSim = SIM_TYPE.isSim
      return Object.assign({
        pageSize: this.indicatorPageObj.pageSize,
        conditionType: isSim ? (this.indicatorSearchType === 1 ? 'indexName' : 'indexCode') : this.indicatorSearchType, // 查询条件只能按名称查询
        [isSim ? 'searchValue' : 'condition']: this.searchName,
        classifyId: isSim ? this.selectedFolder.id : this.selectedFolder.classifyId, // 文件夹ID
      }, isSim ? {
        saveSearchValueFlag: false,
        pageNum: this.indicatorPageObj.currentPage,
        classifyIds: [this.selectedFolder.id, ...this.getChildrenIds([], this.selectedFolder.children)],
       } : {
        datasourceId: this.selectedFolder.datasourceId,
        page: this.indicatorPageObj.currentPage,
      })
    },
    getTableData(folderId, folder) {
      if (folderId === this.selectedFolder.id) return
      this.selectedFolder = folder
      // 发现一个俩年前bug， 如果是模板数据集需要调用另外一个接口
      if (folderId === 'templateDataset') {
        this.updateTable(true)
      } else {
        this.updateTable()
      }
    },
    getIndicatorTableData(folderId, folder) {
      if (folderId === this.selectedFolder.id) return
      this.selectedFolder = folder
      this.updateIndicatorTable()
    },
    // 更新列表数据
    updateTable(isShowTemplate) {
      this.isShowTemplate = isShowTemplate
      if (this.isShowTemplate) {
        this.templateData.params.page = 1
        this.templateData.params.condition = ''
        this.getTemplateList({ page: 1, condition: this.searchName })
      } else {
        this.pageObj.currentPage = 1
        const params = this.getTableParams()
        this.refreshTable(params, true)
      }
    },
    // 更新指标列表数据
    updateIndicatorTable() {
      this.indicatorPageObj.currentPage = 1
      const params = this.getIndicatorTableParams()
      this.refreshIndicatorTable(params, true)
    },
    getTemplateList(changeParams = {}, callBack) {
      this.templateData.tableLoading = true
      const { params } = this.templateData
      getTemplateList(this.api, Object.assign(params, changeParams)).then(({ rows, total }) => {
        if (params.page === 1) {
          this.templateData.tableData = rows || []
        } else {
          this.templateData.tableData.push(...rows)
        }
        this.setMultipleTable(this.templateData.tableData)
        this.templateData.total = total || 0
        this.templateData.tableLoading = false
        this.searchDisabled = false
        callBack && callBack()
      }, () => {
        this.templateData.tableLoading = false
        this.searchDisabled = false
        callBack && callBack()
      })
    },
    getExternalTemplateList(changeParams = {}, isFolder = false) {
      this.templateData.tableLoading = true
      let { params } = this.templateData
      getExternalTemplateList(this.api, Object.assign(params, changeParams, { draftFlag: 0 })).then(res => {
        // console.log(res, '等哈就发生的纠纷')
        const result = res?.rows || []
        this.pageObj.pageTotal = res?.recordTotal || 1
        if (!isFolder) {
          this.tableData.push(...result)
        } else {
          this.initScrollTop()
          this.tableData = result
        }
        this.setMultipleTable(this.tableData)
        this.tableLoading = false
        this.searchDisabled = false
      })
    },
    // 查询列表
    getListTable () {
      const params = this.getTableParams()
      this.refreshTable(params)
    },
    // 设置选中
    setMultipleTable (tableData) {
      this.$nextTick(() => {
        const rows = tableData.filter(item => this.rows.includes(item.id))
        rows.forEach(row => {
          const type = this.isHttpMode ? (this.isShowTemplate ? 'dataSetTable' : 'multipleTable') : 'multipleTable'
          this.$refs[type].toggleRowSelection(row, true)
        })
      })
    },
    // 设置选中
    setMultipleIndicatorTable (tableData) {
      this.$nextTick(() => {
        const rows = tableData.filter(item => this.rows.includes(this.onlyIndex ? item.id : item.datasetId))
        rows.forEach(row => {
          this.$refs.indicatorTable.toggleRowSelection(row, true)
        })
      })
    },
    // 刷新列表数据
    refreshTable (params, isFolder = false) {
      this.tableLoading = true
      getListTable(this.api, params).then(res => {
        const result = res?.rows || []
        this.pageObj.pageTotal = res?.recordTotal || 1
        if (!isFolder) {
          this.tableData.push(...result)
        } else {
          this.initScrollTop()
          this.tableData = result
        }
        this.setMultipleTable(this.tableData)
        this.tableLoading = false
        this.searchDisabled = false
      })
    },
    refreshIndicatorTable(params, isFolder = false) {
      this.tableLoading = true
      getIndicatorListTable(this.api, params).then(res => {
        const result = this.onlyIndex ? this.$_deepClone(res?.data?.rows || []) : this.$_deepClone(res?.rows || [] )
        const result1 = result.filter(item => !item.datasetId) || []
        this.indicatorPageObj.pageTotal =  this.onlyIndex ? (res.data?.total || 0) : (res?.total || 0)
        if (!isFolder) {
          // 分页加载时不需要追加数据，直接替换
          this.indicatorTableData = result
        } else {
          this.initScrollTop('indicatorTable')
          this.indicatorTableData = result
        }
        this.setMultipleIndicatorTable(this.indicatorTableData)
        this.tableLoading = false
        this.searchDisabled = false
      })
    },
    // 选中行
    handleSelectionChange (rows) {
      if (this.dataSets.selection !== 'single') {
        this.rowIds = rows.map(item => item.code_id || item.id)
        this.dataSetArray = [rows]
      }
    },
    handleSelectAllIndicator(rows) {
      if (rows.length > 50) {
        this.$refs.indicatorTable.clearSelection()
        rows.forEach((row, index) => {
          if (index < 50) {
            this.$refs.indicatorTable.toggleRowSelection(row, true)
          }
        })
      }
    },
    handleSelectionChangeIndicator(rows) {
      if (this.onlyIndex) {
        this.rowIds = rows.map(item => item.id)
        this.dataSetArray = [rows]
      } else {
        this.rowIds = rows.map(item => item.datasetId)
        this.dataSetArray = [rows]
      }
    },
    clearRowIds() {
      if (this.isShowTemplate) {
        this.rowIds = []
        this.dataSetArray = []
      } else {
        this.templateData.rowIds = []
        this.templateData.dataSetArray = []
      }
    },
    // 单选
    rowSelect(selection, row) {
      if (this.dataSets.selection === 'single') {
        const rowId = row.code_id || row.id
        selection.forEach(sel => {
          const selId = sel.code_id || sel.id
          if (selId !== rowId) {
            this.$refs.multipleTable.toggleRowSelection(sel, false)
          }
        })
        this.$refs.multipleTable.toggleRowSelection(row, true)
        this.rowIds = [rowId]
        this.dataSetArray = [row]
        this.clearRowIds()
      }
    },
    // 提交参数
    confirm () {
      const rowIds = [...this.rowIds, ...this.templateData.rowIds]
      const datasetType = this.dataSets?.type?.split('-')[0] || ''
      if (this.dataSets && ([TYPE_LIST.GRID, TYPE_LIST.CHART, TYPE_LIST.CARD].includes(datasetType))) {
        let datasetModelTypeSet = new Set()

        const isRefMode = this.isRefMode
        const datasetList = this[isRefMode ? 'boardDatasetList' : 'tableData']
        const modelTypeKey = isRefMode ? 'modelType' : 'model_type'
        const defaultModelType = '1'
        const datasetIdList = this.$_removeRepetition([...rowIds, ...this.rows])

        datasetIdList.forEach(datasetId => {
          const datasetItem = datasetList.find(({ id }) => id === datasetId)
          datasetItem && datasetModelTypeSet.add(datasetItem[modelTypeKey] || defaultModelType)
        })

        if (datasetModelTypeSet.size > 1) {
          this.$message.warning(this.$t('sdp.views.gridDifferentModelType'))
          return
        } else if (datasetModelTypeSet.has(FLOW_MODEL_DATASET) && datasetIdList.length > 1) {
          this.$message.warning(this.$t('sdp.views.onlySupportOneStreamingModelDataset'))
          return
        }
      }
      this.$emit('updateDaSetIds', rowIds, this.boardId)
      this.closeDia()
    },
    initScrollTop (type='multipleTable') {
      this.$nextTick(() => {
        const multipleTable = this.$refs[type].$el
        const selectWrap = multipleTable.querySelector('.el-table__body-wrapper')
        selectWrap.scrollTop = 0
      })
    },
    getTemplateSearchData() {
      this.$nextTick(() => {
        this.$refs.dataSetTable.initScrollTop()
      })
      this.getTemplateList({ page: 1, condition: this.searchName }, () => {
        this.searchDisabled = false
      })
    },
    getSearchData () {
      if (this.searchDisabled) return
      this.searchDisabled = true
      if (this.isShowTemplate) {
        this.getTemplateSearchData()
        return
      }
      // this.updateTable()
      if (this.treeTabsActive === 'indicator') {
        this.initScrollTop('indicatorTable')
        this.updateIndicatorTable()
        this.indicatorPageObj.currentPage = 1
      } else {
        this.updateTable()
        this.initScrollTop()
        this.pageObj.currentPage = 1
      }
      // getListTable(this.api, {
      //   page: this.pageObj.currentPage,
      //   limit: this.pageObj.pageSize,
      //   name: this.searchName,
      //   folderId: this.selectedFolderId || null,
      //   isEnable: this.isEnterprise ? '0' : '',
      //   boardId: this.boardId
      // }).then(res => {
      //   this.tableData = res.rows || []
      //   this.pageObj.pageTotal = res.recordTotal
      //   this.total = res.recordTotal
      //   this.setMultipleTable(this.tableData)
      //   this.searchDisabled = false
      // })
    },
  },
  // 销毁组件
  destroyed () {
    this.$nextTick(() => {
      console.log('销毁组件')
      this.hackReset = false
    })
  }
}
</script>
<style lang="scss" scoped>
@import "../../grid/theme/skin";
  .content {
    .el-table, .el-table:not(.sdp-only-index-table) /deep/ th, .el-table /deep/ td , .el-table /deep/ tr {
      background-color: transparent !important;
    }
    height: 420px;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    padding-left: 0;
    .icon-sdp-shujutianbaosousuo{
      color: var(--sdp-zs);
      line-height: 26px;
      cursor: pointer;
      &.is-disabled{
        color: var(--sdp-jys);
        cursor: not-allowed;
      }
    }
    .left {
      // display: inline-flex;
      display: flex;
      flex-direction: column;
      margin-right: 16px;
      width: 220px;


      .left-tab-group-content{
        width:100%;
        display: flex;

        margin-top: 5px;
        margin-bottom: 10px;

        .radio-item{
        width: 50%;
        border: 1px solid var(--sdp-zs);
        color: var(--sdp-zs);
        cursor: pointer;
        text-align: center;
        font-size: 12px;
        height: 24px;
        line-height: 22px;
        box-sizing: border-box;
        &+.radio-item{
          border-left: 0;
        }
        &:first-child{
          border-radius: 3px 0 0 3px;
        }
        &:last-child {
          border-radius: 0 3px 3px 0;
        }
        &.disable{
          border-color: var(--sdp-jys);
          cursor: not-allowed;
          &.selected{
            background: var(--sdp-jys);
          }
        }
        &.selected {
          background: var(--sdp-zs);
          color: #fff !important;
          position: relative;
          [class*='icon-sdp'] {
            color: #fff !important;
          }
          &+.selected::before{
            content: "";
            position: absolute;
            border-left: 1px solid var(--sdp-srk-bxwzs);
            left: -1px;
            top: 1px;
            bottom: 1px;
            width: 1px;
          }
        }
        }
        .full-radio-item{
          width: 100%;
        }
      }
    }
    .border {
      display: inline-flex;
      width: 1px;
      background-color: var(--sdp-cszj-bkfgx);
    }
    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-left: 10px;
      overflow: hidden;
      .top {
        margin-bottom: 8px;
        text-align: right;
        .searchName {
          display: inline-flex;
          width: 237px;
        }
      }
      .indicator-icon{
        font-size:16px;color:var(--sdp-zs);vertical-align: bottom;margin-right:7px;
      }
    }
  }
.hideCheckAll{
  /deep/ .el-table__header-wrapper .el-checkbox{
    display: none;
  }
}
.sim-indicator-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.sim-indicator-pagination {
  text-align: right;
}
</style>
