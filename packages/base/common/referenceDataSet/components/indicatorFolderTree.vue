<template>
  <div class="custom-tree-container">
    <div class="block">
      <el-tree
        :data="treeData"
        v-loading="loading"
        element-loading-spinner="sdp-loading-gif"
        node-key="id"
        ref="Tree"
        :default-expanded-keys="defaultExpandedKeys"
        :render-content="renderContent"
        :empty-text="$t('sdp.views.noData')"
        @node-click="onNodeSelect"
        :expand-on-click-node="false"
        :highlight-current="true"
      >
      </el-tree>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    treeData: {
      type: Array,
      default: () => [],
    }
  },
  data () {
    return {
      loading: false,
      defaultExpandedKeys: []
    }
  },
  watch: {
    treeData(val = []) {
      this.defaultExpandedKeys = val.map(item => item.datasourceId)
    }
  },
  methods: {
    renderContent (h, { node, data, store }) {
      console.log('%c [ node ]-42', 'font-size:13px; background:#8b81af; color:#cfc5f3;', node)
      return (<span title={`${node.data.folderName || node.data.classifyName || node.data.datasourceName}`}>{node.data.folderName || node.data.classifyName || node.data.datasourceName}</span>)
    },
    // 选中文件夹
    onNodeSelect (node) {
      console.log('node', node)
      this.selectNode = node
      this.$emit('updateTable', node.id, node)
    },
    // 设置高亮
    setHighlight (param = '') {
      this.$nextTick(() => {
        this.$refs.Tree.setCurrentKey(param)
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.custom-tree-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: NotoSansHans-Regular;
}
.custom-tree-container {
  border: 0 node;
  outline: none;
}
.custom-tree-container::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 0 !important; /*高宽分别对应横竖滚动条的尺寸*/
  height: 0 !important;
}
.custom-tree-container::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: none;
}
.custom-tree-container::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: #ffffff;
}
</style>
