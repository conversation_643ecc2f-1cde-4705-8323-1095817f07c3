<template>
  <div class="loadingMaskBox" :class="['middle-ware',isError? 'arrow_box': '']"
  :style="{'border-radius': element.style.borderRadius, ...loadingInfoStyle}"
  >
<!--    <div ref="setRange">-->
<!--      <FullFilterSorter-->
<!--        style="margin-left: 24px"-->
<!--        v-if="isShowFullFilterSorter || themeData.themeFullScreen"-->
<!--        :element="element"-->
<!--        :elList="elList"-->
<!--        :boxHeadRange.sync="boxHeadRange"-->
<!--        :boardInfo="boardInfo"-->
<!--        :fullscreen="true"-->
<!--        :is-loading="loading"-->
<!--        @eventBus="eventBus"-->
<!--        :is-mobile="loadingInfo.isMobile"/>-->
<!--    </div>-->
<!--    <div :style="{ height: boxHeadRange ? `calc(100% - ${boxHeadRange}px)` : '100%'}">-->
    <div v-if="asyncComponent" style="height: 100%;" :style="[...paddingStyle]">
      <el-tooltip
        placement="top"
        :disabled="!hasUpdate || invalidTipShow"
        v-if="isDailyConcerned"
      >
        <component
          ref="element"
          :is="componentName"
          :style="[...elStyle, loadingInfoStyle]"
          v-bind="{element,elList,...$attrs,errorIds,active,boardInfo, init}"
          v-on="$listeners"
        />

        <div slot="content" style="position:relative;">
          <div style="padding-top: 16px;">
            <div style="font-size: 12px;">
              {{$t('sdp.views.elementIsUpdated')}}
            </div>

            <div
              style="position: absolute;right: 0;top: -3px;font-size: 18px;cursor: pointer;"
              @click="handleClearUpdate"
            >
              <i class="sdp-icon icon-sdp-Nguanbi"/>
            </div>
          </div>
        </div>
      </el-tooltip>

      <component
        v-else
        ref="element"
        :is="componentName"
        :style="[...elStyle, loadingInfoStyle]"
        v-bind="{element,elList,...$attrs,errorIds,active,boardInfo, init}"
        v-on="$listeners"
      >
        <slot></slot>
      </component>
      <!-- :popper-options="{ boundariesElement: '#supernatant'}"-->
      <el-tooltip
        v-if="isPreview && !isContainer && commonData.isTenantUser && getRemarkDataById(element).length > 0"
        :popper-class="getRemarkPopperClass(element)"
        placement="top-start"
        :content="getRemarkDataById(element)"
      >
        <div class="remark-angle"></div>
      </el-tooltip>
      <div class="sdp-large-screen-enlarge" :style="{ right: radiusPos + 'px', top: radiusPos + 'px' }">
        <div
          v-if="isShowLargeScreenExport"
          @click="handleScreenExport">
          <i class="icon sdpiconfont icon-sdp-dapingyuansudaochu"></i>
        </div>
        <div
          v-if="showLargeScreen"
          @click="handleScreenEnlarge"
        >
          <i class="icon sdpiconfont icon-sdp-yulanshifangda"></i>
        </div>
      </div>

      <!-- SBI已失效 左上角标签-->
      <div class="invalid-tip" v-if="invalidTipShow">
        {{$t('sdp.views.invalid')}}
      </div>
    </div>

    <sLoading
      v-if="loading"
      :loadingText="$t('sdp.views.loadingInfo')"
      :isMobile="loadingInfo.isMobile"
      :maskTitleInfo="maskTitleInfo"
      :showTitle="(enlargeVisible && isMobile) ? false : showTitle"
      :style="{...modalBg,...loadingInfo.style, ...(loadingInfo.style.background === 'transparent' ? {'background':  undefined} : {})}"
    />

    <div class="sdp-daily-element-checkbox" v-if="isAddDailyPreview">
      <el-checkbox
        v-model="addDailyElementSelected"
        :disabled="addDailyElementDisabled"
        @change="handleAddDailyElementChange"
      />
    </div>
  </div>
</template>

<script>// @ts-nocheck

/** 标题原则上应该是看板元素传递过来，但是目前传过来会有延时，当前组件无法判断其标题正确性所以显示会有误差 **/
import sLoading from './loading'
import boardEl from './boardEl'
import LoadingFn from './loadingFn'
import { THEME_TYPE, EXPORT_TYPE } from 'packages/assets/constant'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import { GRID_TITLE_TITLE } from 'packages/base/grid/helpers/constants/index'
import { getThemeConfig } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/theme'
import { BOARD_ELEMENT_THEME } from '../../board/displayPanel/supernatant/boardElements/BoardElement'
import FullFilterSorter from 'packages/base/board/displayPanel/supernatant/filterDialog/FullFilterSorter'
import eventBus from 'packages/assets/eventBus'
import elementResizeDetectorMaker from 'element-resize-detector'
import EventData from '../../../assets/EventData'
import { elStyleFormatter } from '../../board/displayPanel/utils'
import { IS_SHOW_LARGE_SCREEN } from 'packages/base/board/displayPanel/supernatant/boardElements/constant'
import { SBI_ATTR } from '../../board/dailyConcernMetrics/utils'
import { clearHasUpdate } from 'packages/base/board/dailyConcernMetrics/api'
import { ELEMENT_EXPORT_TYPE, ELEMENT_EXPORT_DATA_TYPE } from 'packages/base/board/settingPanel/components/exportComponents/constants'
import { isRealScreenFn } from 'packages/helpers'

function judgeArrayStatus(statusArray) {
  if (Array.isArray(statusArray)) {
    return statusArray.some(el => el.loading)
  }
  return false
}

const titleObj = {
  [TYPE_ELEMENT.CONTAINER]: val => val.settings.title,
  [TYPE_ELEMENT.TABLE]: val => val.tableDefaultConfig?.title?.text || '',
  [TYPE_ELEMENT.CHART]: val => val.chartUserConfig.title.text,
  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD]: val => val.optionArray.map(card => card.cardName).join('--'),
  [TYPE_ELEMENT.FOUR_QUADRANT]: val => val.response.map(table => table.name).join('--'),
  [TYPE_ELEMENT.COMBINE_CARD]: val => val.cardList.map(card => titleObj[TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD](card.content)).join('--'),
}
const titleStyleObj = {
  [TYPE_ELEMENT.CHART]: val => {
    const currentStyle = val.chartUserConfig.title.textStyle || {}
    if (currentStyle.hasOwnProperty('fontSize') && (currentStyle.fontSize + '').indexOf('px') === -1) {
      return { ...currentStyle, fontSize: currentStyle.fontSize + 'px' }
    }
    return { ...currentStyle }
  },
  [TYPE_ELEMENT.TABLE]: (val, themeType) => {
    const { tableDefaultConfig: { title, gridThemeStyleMap } } = val
    if (!title) return {}
    // let titleStyle = gridThemeStyleMap[themeType].title ? gridThemeStyleMap[themeType].title : { color: GRID_TITLE_TITLE.style(themeType)['color'] }
    return { ...title.style }
  }
}
export default {
  name: 'loadingMask',
  components: {
    sLoading,
    ...boardEl,
    FullFilterSorter,
  },
  provide() {
    return {
      setLoadingStatus: this.setLoadingStatus,
      setErrorStatus: this.setErrorStatus,
    }
  },
  inject: {
    themeData: { default: {} },
    elBgColorFormatter: { default: () => ({}) },
    runningIds: { default: () => { return { value: [] } } },
    newBoardContent: {
      default: () => ({
        metaDashboardLanguageList: [],
        metaDashboardElementLanList: []
      })
    },
    utils: { default: {} },
    langCode: { default: 'zh' },
    commonData: { default: {} },
    remarkData: { default: { remarkData: '[]' } },
    fullscreenData: { default: {} },
    addCollectRender: { default: () => [] },
  },
  props: {
    enlargeVisible: {
      type: Boolean,
      default: false,
    },
    boardInfo: {
      type: Object,
      default: () => [{}],
    },
    componentName: {
      type: String
    },
    loadingInfo: {
      type: Object,
      default: () => ({ title: '', style: {}, isMobile: false })
    },
    element: {
      type: Object
    },
    elList: {
      type: Array
    },
    elStyle: {
      type: Array,
      default: () => ([])
    },
    paddingStyle: {
      type: Array,
      default: () => ([])
    },
    addDailyElementSelectedList: {
      type: Array,
      default: () => ([])
    },
    active: {
      type: Boolean,
      default: false,
    },
    isFinish: {
      type: Boolean,
      default: false,
    },
    init: {
      type: Boolean,
      default: true,
    },
    isShowWatermark: {
      type: [Boolean, String],
      default: false,
    }
  },
  data() {
    return {
      loading: false,
      loadingMaskIsReady: true,
      elementContainerLoadingStatus: false,
      maskTitleInfo: {
        title: '',
        style: {},
        className: ''
      },
      showTitle: true,
      isShowTip: false,
      errorIds: [],
      runningTimer: false,
      boxHeadRange: 0,
      asyncComponent: false,
      allowCheck: false,
      isOpenWatermark: false,
      addDailyElementSelected: false,
      isDuplicateName: false
    }
  },
  computed: {
    isRealScreen() {
      return isRealScreenFn(this.utils)
    },
    loadingInfoStyle() {
      return this.enlargeVisible ? this.loadingInfo.style : {}
    },
    isDailyConcerned() {
      return this.utils.isDailyConcerned || false
    },
    invalidTipShow() {
      return (this.utils.isDailyConcerned && this.isInvalid) || false
    },
    isInvalid() {
      if (this.element[SBI_ATTR]?.isInvalid) {
        return this.element[SBI_ATTR].isInvalid === '1'
      }
      return false
    },
    hasUpdate() {
      if (this.element[SBI_ATTR]?.hasUpdate) {
        return this.element[SBI_ATTR].hasUpdate === '1'
      }
      return false
    },
    modalBg() {
      const isDarkTheme = this.themeType === THEME_TYPE.darkBlue
      return { backgroundColor: isDarkTheme ? 'rgb(33, 45, 59)' : 'rgb(255, 255, 255)' }
    },
    isAdvanceContainer() { // 是否为高级容器
      return this.element.subType === TYPE_ELEMENT.ADVANCE_CONTAINER
    },
    isChart() { // 是否为图形
      return this.element.type === TYPE_ELEMENT.CHART
    },
    isTable() {
      return this.element.type === TYPE_ELEMENT.TABLE
    },
    themeType() {
      return this.$_getProp(this.themeData, 'themeType', '')
    },
    themeFullScreen() {
      return this.$_getProp(this.themeData, 'themeFullScreen', '')
    },
    textTitleDefaultStyle() {
      const { chartTitleConfig: { textStyle } } = getThemeConfig(
        this.themeType,
        {
          attributes: ['chartTitleConfig'],
        },
      )
      let resultStyle = {}
      for (const key in textStyle) {
        if (textStyle[key] !== '') {
          resultStyle[key] = key === 'fontSize' ? textStyle[key] + 'px' : textStyle[key]
        }
      }
      return resultStyle
    },
    currentLang() {
      let lang = this.commonData.boardSlectLang() || this.langCode
      return lang
    },
    isPreview() {
      return this.commonData.isPreview
    },
    isMobile() {
      return this.utils.isMobile
    },
    isShowFullFilterSorter() {
      const { enlargeVisible, enlargeEl } = this.fullscreenData
      return enlargeVisible && enlargeEl === this
    },
    isShowFullFilterSorterBig() {
      const { enlargeEl } = this.fullscreenData
      return this.themeFullScreen && enlargeEl === this
    },
    isError() {
      if (this.element.subType === TYPE_ELEMENT.ADVANCE_CONTAINER && !this.isDuplicateName) {
        return false
      } else {
        if (this.errorIds.length > 0) {
          return true
        } else {
          return false
        }
      }
    },
    isContainer() {
      return [TYPE_ELEMENT.CONTAINER, TYPE_ELEMENT.ADVANCE_CONTAINER].includes(this.element.type)
    },
    remarkDataParse() {
      try {
        return JSON.parse(this.remarkData.remarkData)
      } catch (e) {
        return []
      }
    },
    isScreen() {
      return this.utils.isScreen
    },
    // 真实的大屏
    isLargeScreen() {
      return !!this.utils.isLargeScreen
    },
    elNormalStyle() {
      const { style } = elStyleFormatter(this.element, this.themeData.themeType, this.utils.isMobile)
      return { ...style }
    },
    radiusPos() {
      if (this.elNormalStyle) {
        if (this.elNormalStyle.borderRadius) {
          return parseInt(this.elNormalStyle.borderRadius) / 2 || 0
        }
      }
      return 0
    },
    isShowLargeScreenExport() {
      let isShow = false
      const exportType = this.boardInfo.elementExport.exportType
      if (!this.isPreview || this.fullscreenData.enlargeVisible || !this.isLargeScreen || !exportType.includes(ELEMENT_EXPORT_TYPE.formatExport)) return isShow

      const { type, content } = this.element
      const tabList = content.tabList || []
      const isAdvanceContainer = this.isAdvanceContainer
      if (type === TYPE_ELEMENT.TABLE) {
        isShow = true
      } else if (isAdvanceContainer && tabList.length) {
        let includedElIdList = content.includedElIds || []

        tabList.forEach(tab => {
          includedElIdList = [...includedElIdList, ...tab.content.includedElsIdList]
        })

        isShow = this.elList.some(el => includedElIdList.includes(el.id) && el.type === TYPE_ELEMENT.TABLE)
      }

      return isShow
    },

    showLargeScreen() {
      const isZoomBtn = typeof this.element?.style?.zoomBtn === 'boolean' ? this.element?.style?.zoomBtn : true

      if (!(this.isPreview &&
        !this.loading &&
        !this.fullscreenData.enlargeVisible &&
        !this.runningTimer &&
        this.isRealScreen &&
        !this.isMobile && isZoomBtn)) {
        return false
      }

      const isElType = IS_SHOW_LARGE_SCREEN.includes(this.element.type)

      let isRunning = false
      // console.log('this.runningIds.value.length', this.runningIds.value)
      if (this.element.type === TYPE_ELEMENT.CONTAINER && this.runningIds.value.length && this.element?.content?.tabList?.length) {
        let includedElIdList = this.element.content.includedElIds || []
        // eslint-disable-next-line no-unused-expressions
        this.element.content?.tabList?.forEach(tab => {
          includedElIdList = [...includedElIdList, ...tab.content.includedElsIdList]
        })
        // console.log('includedElIdList>', includedElIdList)
        includedElIdList.forEach(id => {
          if (this.runningIds.value.includes(id)) {
            isRunning = true
          }
        })
      }

      return this.isPreview &&
             !this.loading &&
             !this.fullscreenData.enlargeVisible &&
             isElType &&
             !isRunning &&
             !this.runningTimer &&
             this.isRealScreen &&
             !this.isMobile && isZoomBtn
    },

    isAddDailyPreview() {
      const sbiOptions = this.utils?.sbiOptions || {}
      // return this.isFinish &&
      return this.allowCheck &&
        !this.init &&
        !this.loading &&
        this.asyncComponent &&
        sbiOptions.isSbiDashPreview &&
        sbiOptions.isAddDailyConcernElement
    },
    addDailyElementDisabled() {
      const sbiOptions = this.utils?.sbiOptions || {}
      return sbiOptions?.dailySelectedElement?.includes(this.element.id) || false
    },
  },
  watch: {
    'element.content': {
      handler(val) {
        this.elementContainerLoadingStatus = this.getElementContainerLoadingStatus(val)
        this.maskTitleInfo.className = this.element.type
        if (this.isChart) {
          this.maskTitleInfo.title = this.loadingInfo.isMobile ? this.formatterTitle(this.element.type, val) : ''
          this.maskTitleInfo.titleWarpStyle = {
            padding: this.loadingInfo.isMobile ? (this.fullscreenData.enlargeVisible ? '0' : '11px 11px 0 11px') : '16px 16px 0 16px',
          }
          this.maskTitleInfo.style = {
            ...this.textTitleDefaultStyle,
            ...this.formatterTitleStyle(this.element.type, val),
          }
        }
        if (this.isTable && this.loadingInfo.isMobile) {
          const { tableDefaultConfig: { title } } = val
          if (title) {
            this.maskTitleInfo.title = title.text
            this.maskTitleInfo.style = { ...this.formatterTitleStyle(this.element.type, val) }
            this.maskTitleInfo.titleWarpStyle = {
              padding: this.loadingInfo.isMobile ? (this.fullscreenData.enlargeVisible ? '0' : '11px 11px 0 11px') : '16px 16px 0 16px',
            }
          }
        }

      },
      immediate: true,
      deep: true
    },
    'element.content.includedElIds'(val) {
      if (this.element.type === TYPE_ELEMENT.CONTAINER && this.element.subType !== TYPE_ELEMENT.ADVANCE_CONTAINER) {
        this.errorIds = this.errorIds.filter(id => {
          let is = this.elementContainerLoadingStatus.includes(id)
          return is
        })
      }
    },
    isPreview() {
      if (this.element.type === TYPE_ELEMENT.CONTAINER) {
        this.runningTimer = true
        setTimeout(() => {
          this.runningTimer = false
        }, 2000)
      }
    }
  },
  mounted() {
    let callbackResolve = () => {}
    const render = new Promise((resolve) => {
      const time = setTimeout(resolve, this.utils.isMobile ? 1500 : 500)
      callbackResolve = () => {
        clearTimeout(time)
        resolve()
      }
    }).then(() => {
      this.asyncComponent = true
      console.log(`渲染debug ${ new Date().toTimeString().slice(0, 8) } son`)

      setTimeout(() => {
        this.allowCheck = true
      }, 1000)

      return '开始渲染组件'
    })

    this.addCollectRender(render, this.element.id, callbackResolve)

    this.addDailyElementSelected = this.addDailyElementDisabled ? true : !!this.addDailyElementSelectedList.includes(this.element.id)
  },
  beforeDestroy() {
    // if (this.$refs.setRange) {
    //   this.erd.uninstall(this.$refs.setRange)
    // }
  },
  methods: {
    eventBus,
    callEmit(data, bool = true) {
      this.commonData.setExportLoading(bool)
      const eventData = new EventData({
        source: this.$options.name,
        target: 'displayPanel',
        targetFn: 'runByForce',
        ...data
      })
      this.$emit('eventBus', eventData)
    },
    handleScreenExport() {
      const { type, content } = this.element
      const tabList = content.tabList || []
      const isAdvanceContainer = this.isAdvanceContainer
      let includedElIdList = tabList.reduce((pre, next) => {
        return [...pre, ...next.content.includedElsIdList]
      }, Array.isArray(content.includedElIds) ? [...content.includedElIds] : [])

      const advanceContainerInTables = this.elList.filter(el => includedElIdList.includes(el.id) && el.type === TYPE_ELEMENT.TABLE)

      const isTable = type === TYPE_ELEMENT.TABLE

      const singlePageExport = isTable || (isAdvanceContainer && tabList.length === 1 && advanceContainerInTables.length === 1)

      if (singlePageExport) {
        const isShowWatermark = this.isShowWatermark
        const h = this.$createElement
        const self = this
        this.isOpenWatermark = false
        const msg = h('p', null, [
          h('p', null, this.$t('sdp.views.exportBoardEleExcel')),
          isShowWatermark
            ? h('p', null, [
                h('el-checkbox', {
                  attrs: {
                    checked: false
                  },
                  key: Date.now(),
                  on: {
                    input: function (val) {
                      self.isOpenWatermark = val
                    }
                  }
                }, [this.$t('sdp.button.exportWatermark'),
                h('el-tooltip', {
                  props: {
                    placement: 'top',
                    content: this.$t('sdp.tooltips.watermarkTip')
                  }
                }, [
                  h('i', {
                      class: {
                        'icon-sdp-info': true,
                      },
                      style: {
                        color: 'var(--sdp-zs)',
                        position: 'relative',
                        top: '2px'
                      }
                    })
                  ])
                ]),
              ])
          : ''
        ])

        const id = isTable ? this.element.id : advanceContainerInTables[0].id
        const element = isTable ? this.element : advanceContainerInTables[0]
        this.$sdp_eng_confirm(msg, `${this.$t('sdp.dialog.ExportConfirmation')}`, {
          confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
          cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
          closeOnHashChange: false,
          type: 'warning',
          closeOnClickModal: false,
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog',
        }).then(() => {
          this.callEmit(
            {
              data: { ids: [ id ] },
              options: {
                derivedFun: () => {
                  const eventData = new EventData({
                    target: ['displayPanel'],
                    targetFn: 'doExportExcel',
                    data: {
                      element,
                      format: EXPORT_TYPE.Excel,
                      type: ELEMENT_EXPORT_DATA_TYPE.singlePage,
                      isExportAllDateData: false,
                      isOpenWatermark: this.isOpenWatermark
                    },
                  })
                  this.$emit('eventBus', eventData)
                }
              }
            }
          )
        }).catch(() => {})
      } else {
        const eventData = new EventData({
          target: ['supernatant'],
          targetFn: 'callExportDialog',
          data: this.element,
        })
        this.$emit('eventBus', eventData)
      }
    },
    handleAddDailyElementChange(val) {
      this.$emit('elementCheck', this.element.id, val)
    },
    runByForce(eventData) {
      console.log('ding', eventData)
    },
    // metric参数组件会调用该方法
    metricSwitch() {
      console.log(8080, this.$refs.element)
      const metricSwitch = this.$refs.element.metricSwitch || this.$refs.element.element.vm.metricSwitch
      metricSwitch && metricSwitch(...arguments)
    },
    getRemarkPopperClass(element) {
      return this.getRemarkDataById(element).length > 0 ? 'remarkTooltip' : 'remarkTooltip remarkTooltip-hide'
    },
    getRemarkDataById(element) {
      const eleId = element.id
      const type = element.type
      let tabId = ''
      if (type === TYPE_ELEMENT.CHART) {
        if (element.content?.chioceTab && element.content?.chioceTab.length) {
          tabId = element.content.chioceTab[element.content.saveIndex].id
        }
      }
      if (this.remarkDataParse) {
        let v
        if (tabId !== '') {
          v = this.remarkDataParse.find(e => { return e.id === eleId && e.tabId === tabId })
        } else {
          v = this.remarkDataParse.find(e => { return e.id === eleId })
        }
        return Object.assign({ name: '', value: '', id: '', tabId: '' }, v).value
      }
      return { name: '', value: '', id: '', tabId: '' }.value
    },
    setErrorStatus(e, elId = '') {
      if (e) {
        if (!this.errorIds.includes(elId)) {
          this.errorIds.push(elId)
        }
      } else {
        this.errorIds = this.errorIds.filter(item => item !== elId)
      }
      return this.error
    },
    setDuplicateNameStatus(e){
      this.isDuplicateName = e
    },
    setLoadingStatus(v, elId = '') {
      if (this.elementContainerLoadingStatus) {
        const symbolKey = Symbol.for(elId)
        if (v) {
          const el = document.querySelector(`[loadTarget='${elId}']`)
          const currentElStatus = this.formatterList(this.elementContainerLoadingStatus).find(el => el.id === elId)
          !this[symbolKey] && (this[symbolKey] = LoadingFn(
            {
              target: el,
              customStyle: currentElStatus.style,
            },
            {
              isMobile: this.loadingInfo.isMobile,
              showTitle: this.loadingInfo.isMobile, // 仅移动端显示标题
              loadingText: this.$t('sdp.views.loadingInfo'),
              maskTitleInfo: { ...currentElStatus.maskTitleInfo } // 在容器内部的图形 设置了指标选择器 标题不会显示会有点问题
            }
          ))
        } else {
          if (this[symbolKey]) {
            this[symbolKey].close()
            this[symbolKey] = null
          }
        }
        return this.loadingMaskIsReady
      }

      this.loading = v

      return this.loadingMaskIsReady
    },
    getElementContainerLoadingStatus(content) {

      if (this.componentName === 'element-container') {
        if (this.isAdvanceContainer) {
          const tabList = this.$_getProp(content, 'tabList', [])
          const advanceIncludeEL = tabList.reduce((result, pre) => {
            const tabItemInclud = this.$_getProp(pre.content, 'includedElsIdList', [])
            return [...result, ...tabItemInclud]
          }, [])
          return advanceIncludeEL.length > 0 && advanceIncludeEL
        }
        const includedElIds = this.$_getProp(content, 'includedElIds', [])
        const len = includedElIds.length

        if (len > 0) {
          return includedElIds
        }
      }
      return false
    },
    formatterList(list) {
      return list.map(id => {
        const currentEl = this.elList.find(el => el.id === id)
        // 高级容器内部可能会有一些数据没有清除  需要判空处理
        if (!currentEl?.type) return {}
        let maskTitleInfo = {
          title: '', titleWarpStyle: {}, style: {}, className: ''
        }
        maskTitleInfo.className = currentEl.type
        if (currentEl.type === TYPE_ELEMENT.CHART) {
          maskTitleInfo.title = this.formatterTitle(currentEl.type, currentEl.content)
          maskTitleInfo.titleWarpStyle = { padding: this.isAdvanceContainer ? '8px 0 0 8px' : '0' }
          maskTitleInfo.style = { ...this.textTitleDefaultStyle, ...this.formatterTitleStyle(currentEl.type, currentEl.content) }
        }

        if (currentEl.type === TYPE_ELEMENT.TABLE) {
          maskTitleInfo.title = this.formatterTitle(currentEl.type, currentEl.content)
          maskTitleInfo.titleWarpStyle = { padding: this.isAdvanceContainer ? '8px 0 0 8px' : '0' }
          maskTitleInfo.style = { ...this.formatterTitleStyle(currentEl.type, currentEl.content) }
        }
        return {
          id,
          loading: true,
          style: { ...this.elBgColorFormatter(currentEl) },
          saveIndex: currentEl.content.saveIndex || 0,
          maskTitleInfo: maskTitleInfo,
        }
      })
    },
    getElement() {
      return this.$refs.element
    },
    formatterTitle(type, val) {
      return titleObj[type] && titleObj[type](val)
    },
    formatterTitleStyle(type, val) {
      return titleStyleObj[type] && titleStyleObj[type](val, this.themeType)

    },
    handleScreenEnlarge() {
      const eventData = new EventData({
        data: { id: this.element.id },
        source: 'largeScreen-loadingMask',
        target: 'supernatant',
        targetFn: 'handleScreenEnlargeEl'
        // targetFn: 'removeEl'
      })
      this.$emit('eventBus', eventData)
    },
    handleClearUpdate() {
      if (this.element[SBI_ATTR]?.dataId) {
        const id = this.element[SBI_ATTR].dataId
        clearHasUpdate(this.utils.api, { id })
          .then(res => {
            // 我的评价是这里没必要加 但是懒得删了 甚至还多加一条注释
            this.element[SBI_ATTR].hasUpdate = '0'
          })
          .catch(err => {
            // 我的评价是这里没必要加 但是懒得删了 甚至还多加一条注释
            this.element[SBI_ATTR].hasUpdate = '0'
          })
          .finally(res => {
            this.element[SBI_ATTR].hasUpdate = '0'
          })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "packages/base/board/displayPanelMobile/components/variable.scss";
.is-force {
  .loadingMaskBox {
    border-radius: 0 !important;
  }
}
.invalid-tip {
  z-index: 999;
  position: absolute;
  left: 0;
  top: 0;
  background: #E2E5EB;
  font-size: 12px;
  color: #333333;
  height: 20px;
  line-height: 20px;
  width: 80px;
  text-align: center;
  transform: rotate(-45deg) translate(-21px, -6px);
}
.middle-ware{
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.icon {
  font-size: 14px;
  cursor: pointer;
  align-self: flex-end;
  @include sdp-mixin-style($type:(
    color:(customInputMobileDateColor:true),
  ), $self: $self);
}
.arrow_box {
  border: 1px solid;
  animation: glow 1200ms;
  animation-iteration-count: infinite;
}
.loadingMaskBox {
  .sdp-daily-element-checkbox {
    position: absolute;
    right: 8px;
    top: 8px;
    z-index: 20001;
    /deep/ {
      .el-checkbox__inner {
        background-color: var(--sdp-fs1);
      }
    }
  }

  &:hover {
    .sdp-large-screen-enlarge {
      display: flex;
    }
  }
}
.sdp-large-screen-enlarge {
  display: none;
  z-index: 999;
  position: absolute;
  right: 5px;
  top: 5px;
  &:hover {
    display: block;
  }
  > div {
    margin-left: 5px;
    cursor: pointer;
  }
  /*width: 20px;*/
  /*height: 20px;*/
  /*background: #c61e75;*/
}
@keyframes glow {
  10% {
    border-color: #ffecec;
  }
  20% {
    border-color: #ffd2d2;
  }
  30% {
    border-color: #ffb5b5;
  }
  40% {
    border-color: #ff9797;
  }
  50% {
    border-color: #ff7575;
  }
  60% {
    border-color: #ff5151;
  }
  70% {
    border-color: #ff2d2d;
  }
  80% {
    border-color: #ff0000;
  }
  90% {
    border-color: #ea0000;
  }
  100% {
    border-color: #DC0600;
  }

}
</style>
