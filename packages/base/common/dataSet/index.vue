<template>
  <div
    class="dataSet-wrapper"
    :class="[ isGrid || isChart || isCard ? 'dataset-bg-grid': 'dataset-bg-board', {'board': type === TYPE_LIST.BOARD }]"
    :style="{ width: style.width +'px', padding: style.padding }"
    id="dataSets"
    data-cy-Dataset
    ref="dataSetsRef"
  >
    <i
      class="top-icon top-grid"
      :style="{'pointer-events': isAdvanceContainerEdit ? 'none' : '',
      'cursor': isAdvanceContainerEdit ? 'not-allowed' : 'col-resize'}"
      v-if="!close"
      @mousedown="drag"
    ></i>
    <div
      class="close"
      :style="right"
    >
      <i
        class="icon-sdp-zuojiantou"
        :class="[{ 'reverse-icon': close }, isGrid || isChart || isCard ? 'icon-bg-grid': 'dataset-bg-board']"
        :title="$t('sdp.views.dataSet')"
        @click.stop="closeDataSet"
        data-cy-datasetSwitch
      ></i>
    </div>
    <transition name="fade">
      <div :style="{'cursor': isAdvanceContainerEdit ? 'not-allowed' : 'pointer'}">
        <div
          class="dataSet"
          :class="[{'closeDataSet': close }]"
          :style="{'pointer-events': isAdvanceContainerEdit ? 'none' : ''}"
        >
          <div
            class="row top"
            :class="[{'grid-w': isGrid || isChart || isCard}]"
          >
            <div class="left">
              {{$t('sdp.views.dataSet')}}
            </div>
            <div class="right">
              <!-- 数据集替换 -->
              <dataset-replace
                :boardInfo="displayBoardInfo"
                :paramsPanelList="paramsPanelList"
                :elList="elList"
                :datasetList="(dataSets.datasetList || []).filter(item => !item.indexFlag)"
                @updateDaSetIds="updateDaSetIds"
                v-if="!(isGrid || isChart || isCard)"
                style="margin-right: 0px"/>
              <GuidePopover
                v-if="!this.isCard"
                :content="$t('sdp.guide.clickToAddDataset')"
                :value="isShowStepTipsByAddDatasets"
                :step="stepEntryAddDatasets"
                :markPaddingLeft="9"
                :markPaddingRight="9"
                :markPaddingTop="9"
                :markPaddingBottom="9"
                :arrowOffsetX="15"
                :tipsOffsetY="-18"
              >
                <i
                  class="right-icon"
                  :class="['icon-sdp-yinyongshujuji']"
                  @click="openLinkDatasetDialog"
                  data-cy-datasetAddBtn
                ></i>
              </GuidePopover>

              <i
                class="right-icon"
                :class="['icon-sdp-sdp-canshuzujianguanlian',{'association-disable':disableDatasetJoin}]"
                v-if="isGrid || isChart || isCard"
                @click="!disableDatasetJoin&&$emit('open-dataset-association')">
              </i>
            </div>
            <div class="new-template-tooltip">
              <el-tooltip
                v-if="isNewTemplateBoard"
                class="tooltip"
                effect="dark"
                :content="$t('sdp.views.replaceDataSetFirst')"
                placement="top"
                v-model="tooltipVisible"
              >
                <div style="width: 2px;height: 2px;opacity: 0"></div>
              </el-tooltip>
            </div>
          </div>
          <div
            class="row bottom"
            :class="[{'grid-w': isGrid}]"
            v-loading="treeLoading"
            element-loading-spinner="sdp-loading-gif"
            style="display: flex;flex-direction: column; height: calc(100% - 31px);"
          >
          <!-- max-height: 150px;  -->
            <div style="overflow-y: auto; flex-shrink: 0;" :style="{ height: dragParam.topHeight + 'px' }">
              <div
                class="dataset-item"
                :class="{
                'node-disable': datasetItem.enableFlag === '0',
                'active': selectedDataset.id === datasetItem.id,
                'dataset-high-light': datasetItem.highLight,
                'disabled': isCommonDatasetDisabled || datasetItem.enableFlag === '0',
              }"
                v-for="(datasetItem, dsIndex) in dataSets.datasetList" :key="dsIndex"
                @click="!isCommonDatasetDisabled && datasetClick(datasetItem)"
              >
                <span class="dataset-name" :title="getDatasetLabel(datasetItem)">
                  <i v-if="!(isGrid || isChart || isCard)" class="icon-sdp-yinyongshujuji cited-data-icon" :class="dataSets.quoteIds.includes(datasetItem.id) ? 'cited-data-active-icon': ''" @click.prevent="dataSets.quoteIds.includes(datasetItem.id)  && checkReferenced(datasetItem)"></i>
                  <i class="indicator-icon" :class="
                  isSim ? {
                    1: 'icon-sdp-paishengzhibiao',
                    2: 'icon-sdp-fuhezhibiao',
                    0: 'icon-sdp-yuanzizhibiao'
                  }[datasetItem.indexType]
                  : datasetItem.indexType===2 ? 'icon-sdp-paishengzhibiao':'icon-sdp-fuhezhibiao'" v-if="datasetItem.indexFlag"></i>
                  {{ getDatasetLabel(datasetItem) }}
                </span>
                <div class="dataset-oprate">
                  <i class="icon-sdp-yulan" :class="{ disabledIcon: datasetItem.enableFlag === '0', 'is-card': isCard }" :style="datasetItem.id === VIRTUAL_DATASET_KEY ? 'display: none' : ''" @click="openDataDetailPreview(datasetItem)"></i>
                  <i v-if="!isCard" class="el-icon-delete-solid" @click="e => handleDeleteDataset(e, datasetItem)"></i>
                </div>
              </div>
              <div
                v-if="(elementType === '2' || elementType === '3') && isAssociatedDataset"
                class="dataset-item"
                :class="{
                'node-disable': associatedDataset.enableFlag === '0',
                'active': selectedDataset.id === associatedDataset.id,
                'dataset-high-light': associatedDataset.highLight,
              }"
                @click="datasetClick(associatedDataset)"
              >
                <span class="dataset-name" :title="getDatasetLabel(associatedDataset)">
                  {{ getDatasetLabel(associatedDataset) }}
                </span>
                <div class="dataset-oprate">
                  <i class="el-icon-delete-solid" @click="e => handleDeleteDataset(e, associatedDataset)"></i>
                </div>
              </div>
            </div>
            <DatasetContent
              v-if="selectedDataset && selectedDataset.id"
              ref="DatasetContent"
              :highLightItem="selectedDataset"
              :elementType="elementType"
              :needDrag="elementType !== '-1'"
              :boardInfo="displayBoardInfo"
              :datasetTenantData="datasetTenantData"
              :elList="elList"
              :showFieldReference="!(isGrid || isChart || isCard)"
              @eventBus="eventBus"
              @pointChange="pointChange"
              @openFieldReference="openFieldReferenceDialog"
              :dragParam="dragParam"
            />
          </div>
        </div>
      </div>
    </transition>
    <DataSetPreview
      :dataSets="dataSets"
      :visible.sync="dataSetPreviewShow"
    />
    <DataSetIndicatorPreview
      :dataSets="dataSets"
      :visible.sync="dataSetIndicatorPreviewShow"
    />
    <fieldReferenceDialog
      :visible.sync="fieldReferenceVisible"
      :boardInfo="displayBoardInfo"
      :paramsPanelList="paramsPanelList"
      :dynamicTags="dynamicTags"
      :elList="elList"
      :datasetList="dataSets.datasetList"
      :dataSets="dataSets"
      @eventBus="eventBus"
    />
    <component
      :is="componentId"
      :boardDatasetList="boardDatasetList"
      :boardInfo="boardInfo"
      @closeDia="closeDia"
      :dialogVisible.sync="dialogVisible"
      :visible.sync="dialogVisible"
      @updateDaSetIds="updateDaSetIds"
      :dataSets="dataSets"
      :gridTable="gridTable"
      :defaultAuthorityBoardId="defaultAuthorityBoardId"
      :dataWarehouseModelingSwitch="dataWarehouseModelingSwitch"
      @removeDataset="removeDataset"
    ></component>
  </div>
</template>
<script>
import RemoveDatasetDialog from './dialogComponents/RemoveDatasetDialog'
import ReferenceDataSet from '../referenceDataSet'
import EventData from 'packages/assets/EventData'
import { TYPE_LIST, VIRTUAL_DATASET_KEY } from 'packages/assets/constant'
import { TYPE_ELEMENT, EVENT_BUS, EXTERNAL_CALL_TYPE } from 'packages/base/board/displayPanel/constants'
import { substring15 } from 'packages/base/board/displayPanel/utils'
import { getParamsPanelLisDataSetIds } from './util'
import { getDataSetTreeByIds } from '../../board/mixins/api'
import DatasetContent from './datasetContent.vue'
import DataSetPreview from './dialogComponents/dataSetPreview'
import DataSetIndicatorPreview from './dialogComponents/dataSetIndicatorPreview'
import { getDataWarehouseModelFlag } from '../../board/displayPanel/params/api'
import { GET_PLAT_VALUE_BY_CODE_PID_TYPE, getElListDataSetIds, getSingleElListDataSetIds } from '../../board/displayPanel/constants'
import DatasetReplace from 'packages/base/board/displayPanel/datasetReplace/datasetReplace.vue'
// import EventData from '../../../assets/EventData'
import eventBus from 'packages/assets/eventBus'
import { kanbanGuideStepEntryAddDatasetsMixin } from '../../KanbanGuide'
import FieldReferenceDialog from "packages/base/common/dataSet/dialogComponents/fieldReferenceDialog/index";
import { SIM_TYPE } from 'packages/base/common/referenceDataSet/constants'

export default {
  name: 'dataSets',
  inject: {
    utils: { default: () => ({}) },
    aliasDict: { default: () => ({}) },
    datasetList: { default: () => [] },
    getDictAlias: { default: () => () => {} },
    configs: { default: () => () => {} },
    langCode: { default: '' },
    isNewTemplateBoard: { default: false },
    sdpBus: { default: () => () => {} },
  },
  mixins: [kanbanGuideStepEntryAddDatasetsMixin],
  components: {
    FieldReferenceDialog,
    RemoveDatasetDialog,
    ReferenceDataSet,
    DataSetPreview,
    DataSetIndicatorPreview,
    DatasetContent,
    DatasetReplace,
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    cellDatas: {
      type: Array,
      default: () => [],
    },
    boardInfo: {
      type: Object,
      default: () => {},
    },

    // 表格组件的数据集
    gridDatasetList: {
      type: Array,
      default: null,
    },

    boardDatasetList: {
      type: Array,
      default() {
        return []
      },
    },

    gridTable: {
      type: Object,
      default: () => { },
    },
    selectionList: {
      type: Array,
      default: () => [],
    },
    tenantId: {
      type: String,
      default: ''
    },
    chartUserConfig: {
      type: Object,
      default: () => {},
    },
    currentEditData: {
      type: Object,
      default: () => {},
    },
    // 关联后生成的结果数据集
    associatedDataset: {
      type: [Object, String],
    },
  },
  data () {
    return {
      isSim: SIM_TYPE.isSim,
      VIRTUAL_DATASET_KEY,
      tooltipVisible: false,
      selectedDataset: {},
      displayBoardInfo: {},
      componentId: '',
      paramsPanelList: null,
      dataSets: {},
      close: false,
      elList: [],
      dynamicTags: [],
      right: {
        'right': '-16px',
        'fontSize': '30px',
      },
      style: {
        width: '212',
        padding: '16px 0 16px 16px'
      },
      dialogVisible: false,
      dataSetPreviewShow: false,
      dataSetIndicatorPreviewShow: false,
      fieldReferenceVisible: false,
      styleObj: {
        parentStyle: {
          'display': 'inline-block',
          'position': 'absolute',
          'right': '0',
          'padding-right': '12px',
          'padding-top': '4px',
          'line-height': '25px',
          'color': '#999999',
        },
        childrenStyle: {
          fontSize: '12px',
          display: 'inline-block',
          marginLeft: '6px',
        }
      },
      parentStyleHidden: {
        'display': 'inline-block',
        'overflow': 'hidden',
        'text-overflow': 'ellipsis',
        'white-space': 'nowrap',
        'width': '65%',
        'padding-top': '3px',
      },
      oldWith: '0',
      data: null,
      treeLoading: false,
      TYPE_LIST,
      currentNode: null,
      ids: [],
      defaultAuthorityBoardId: '',
      dataWarehouseModelingSwitch: '0',
      isAdvanceContainerEdit: false,
      initCall: (row) => {
        if (!row.data.boardInfo.id || this.isNewTemplateBoard) {
          this.close = false
          this.style.width = '212'
          this.right.right = '-16px'
          this.style.padding = '16px 0 16px 16px'
          this.$nextTick(() => {
            this.isNewTemplateBoard && (this.tooltipVisible = true)
          })
          this.initCall = () => {}
        }
      },
      datasetTenantData: {},
      dragParam: {
        topHeight: 100,
        middleHeight: 200,
        bottomHeight: 200,
        startY: 0,
        startTopHeight: 0,
        startMiddleHeight: 0,
        startBottomHeight: 0,
        resizing: null,
        topMinHeight: 60,
        middleMinHeight: 100
      }
    }
  },
  watch: {
    cellDatas(val, oldVal) {
      if (JSON.stringify(val) === JSON.stringify(oldVal) || (!val && !oldVal)) {
        return
      }
      if (this.isGrid || this.isChart || this.isCard) {
        this.updateGRidIds(this.isGrid ? this.cellDatas.filter(id => id !== VIRTUAL_DATASET_KEY) : this.cellDatas)
      }
    },
    selectionList (val) {
      val && this.setHighlightDataset()
    },
    type: {
      handler (val, oldVal) {
        val && this.$set(this.dataSets, 'type', val)
      },
      immediate: true,
    },
    elList: {
      handler (val){
        this.getElListDataSetsQuoteIds()
      },
      deep: true
    },
    // 时间紧急先这样写着
    paramsPanelList: {
      handler(val) {
        // 获取参数组件引用的数据集
        this.getElListDataSetsQuoteIds()
      },
      deep: true
    }
  },
  computed: {
    api () {
      return this.utils.api || function () { }
    },
    tenant () {
      return this.utils.tenantId || this.tenantId
    },
    // 判断是否是看板元素页面
    isTemplateBoard() {
      return (this.configs && this.configs.type === 'template') || false
    },
    disableDatasetJoin() {
      if (this.dataSets.hasOwnProperty('datasetList')) {
        return this.dataSets.datasetList && this.dataSets.datasetList.filter(item => {
          return !item.hasOwnProperty('dataSourceId')
        })?.length < 1
      }
      return true
    },
    isGrid() {
      return this.type === TYPE_LIST.GRID
    },
    isChart() {
      return this.type === TYPE_LIST.CHART
    },
    isCard() {
      return this.type === TYPE_LIST.CARD
    },
    elementType() {
      const elementMap = {
        [TYPE_LIST.GRID]: '1',
        [TYPE_LIST.CHART]: '2',
        [TYPE_LIST.CARD]: '3',
      }
      return elementMap[this.type] || '-1'
    },
    isAssociatedDataset() {
      return this.associatedDataset && Object.keys(this.associatedDataset).length
    },
    isCommonDatasetDisabled() {
      return (this.elementType === '2') && (this.associatedDataset || (this.dataSets?.datasetList?.length >= 2))
    }
  },
  created () {
    this.setStyle()
    if (this.isGrid || this.isChart || this.isCard) {
      this.updateGRidIds(this.isGrid ? this.cellDatas.filter(id => id !== VIRTUAL_DATASET_KEY) : this.cellDatas)
    }
    getDataWarehouseModelFlag(this.api).then(res => {
      // 0 数据建模  1 数仓建模
      this.dataWarehouseModelingSwitch = res
    }, () => {
      this.dataWarehouseModelingSwitch = '0'
    })

  },
  mounted() {
    document.addEventListener('click', this.handlerCheckReferenced)
  },
  methods: {
    eventBus,
    checkReferenced(data) {
      event.stopPropagation()
      this.handlerCheckReferenced(data)
      // this.datasetClick(data)
    },
    handlerCheckReferenced(data) {
      if(!this.elList?.length) return
      if(!data || !data.id || !this.dataSets.quoteIds.includes(data.id)) {
        this.elList.forEach(el => {
          const {drillSettings = {} } = el.content || {}
          this.$set(drillSettings, 'datasethighLightItemFlag', false)
        })
      } else {
        this.elList.forEach(el => {
          const _ids = getSingleElListDataSetIds(el, [], true)
          const {drillSettings = {} } = el.content || {}
          if(_ids?.length && _ids.includes(data.id)) {
            this.$set(drillSettings, 'datasethighLightItemFlag', true)
          } else {
            this.$set(drillSettings, 'datasethighLightItemFlag', false)
          }
          this.$set(el.content, 'drillSettings', drillSettings)
        })
      }
    },
    getElListDataSetsQuoteIds () {
      let paramsPanelIds = []
      // 先清空quoteIds，再赋值elList里面被引用的
      this.$set(this.dataSets, 'quoteIds', [])
      if (this.paramsPanelList) paramsPanelIds = getParamsPanelLisDataSetIds(this.paramsPanelList)
      const TYPE = [
          TYPE_ELEMENT.CHART,
          TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
          TYPE_ELEMENT.FOUR_QUADRANT,
          TYPE_ELEMENT.TABLE,
          TYPE_ELEMENT.COMBINE_CARD,
          TYPE_ELEMENT.CUSTOMER_ELEMENT,
          TYPE_ELEMENT.DUPONT_ANALYSIS,
          TYPE_ELEMENT.TEXT,
        ]
        const ids = getElListDataSetIds(this.elList, TYPE)
        if(this.dataSets?.quoteIds) {
          this.dataSets.quoteIds = [this.dataSets.quoteIds, ...ids, ...paramsPanelIds]
        }else {
          this.$set(this.dataSets, 'quoteIds', [...ids, ...paramsPanelIds])
        }
        this.dataSets.quoteIds = [...new Set(this.dataSets.quoteIds)]
    },
    pointChange(data) {
      // this.dragParam.datasetHeight = data
    },
    getDatasetLabel(data) {
      return data.comment ? `${data.labeName}   (${substring15(data.comment)})` : data.labeName
    },
    setStyle () {
      const type = this.type
      switch (type) {
        case TYPE_LIST.GRID: {
          Object.assign(this.right, {
            'right': '-16px',
          })
          break
        }
        case TYPE_LIST.BOARD: {
          this.close = true
          this.style.width = '0'
          this.right.right = '-16px'
          this.style.padding = '0'
          break
        }
        case TYPE_LIST.CHART: {
          Object.assign(this.style, {
            padding: '16px',
            width: '232',
          })
        }
      }
    },
    nodeCollapse (data, node, self) {
      if (data.parentId === '-1') this.ids = []
    },
    datasetClick(datasetItem) {
      if (this.isCommonDatasetDisabled && datasetItem.dateType !== 'associateDataset') return
      if (datasetItem.enableFlag === '0' || (!this.isCommonDatasetDisabled && (this.selectedDataset.id === datasetItem.id))) return
      this.selectedDataset = this.$_deepClone(datasetItem)
      this.$refs.DatasetContent && this.$refs.DatasetContent.reset(datasetItem)
      if (this.elementType === '2' || this.elementType === '3') {
        const drillSettings = this.currentEditData.content.drillSettings
        !drillSettings.dataSetId && (drillSettings.dataSetId = datasetItem.id)
      }
      this.resetHeights()
      this.$nextTick(() => {
        this.$refs.DatasetContent && this.$refs.DatasetContent.updateHeights()
      })
    },
    resetHeights() {
      this.dragParam= {
        topHeight: 100,
        middleHeight: 200,
        bottomHeight: 200,
        startY: 0,
        startTopHeight: 0,
        startMiddleHeight: 0,
        startBottomHeight: 0,
        resizing: null,
        topMinHeight: 60,
        middleMinHeight: 100
      }
    },
    updateAdvanceContainerEdit({ data }) {
      this.isAdvanceContainerEdit = data?.val
    },
    drag (e) {
      const left = e.target.parentNode
      const leftW = left.offsetWidth
      const startX = e.clientX
      const _this = this
      document.onmousemove = (e) => {
        this.closeDurativeWithAddDatasets()
        e.preventDefault()
        const distX = e.clientX - startX
        _this.style.width = leftW + distX
        if (_this.style.width <= 212) {
          _this.style.width = 212
        }
      }
      document.onmouseup = () => {
        document.onmousemove = null
      }
    },
    // 设置数据集高亮
    setHighlightDataset () {
      const selectionList = this.selectionList
      if (!selectionList.length) return

      const id = this.selectionList[0].content.dataSetId || null
      if (this.dataSets.datasetList?.length) {
        this.dataSets.datasetList.forEach(d => {
          this.$set(d, 'highLight', d.id === id)
        })
      }
    },
    // getBoardResIds (row, result = { datasetIds: [], needsIds: [] }) {
    //   this.elList = row.data.elList
    //   result.datasetIds = row.data.boardDatas
    //   this.elList.forEach(element => {
    //     switch (element.type) {
    //       case TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD: {
    //         const ids = element.content.optionArray ? element.content.optionArray
    //           .map(item => item.dataSetId).filter(item => { return item }) : []
    //         result.needsIds.push(...ids)
    //         break
    //       }
    //       case TYPE_ELEMENT.FOUR_QUADRANT: {
    //         const ids = element.content.fourQuadrantOptions.tables.length ? element.content.fourQuadrantOptions.tables.map(item => item.dataSetId) : []
    //         result.needsIds.push(...ids)
    //         break
    //       }
    //       case TYPE_ELEMENT.CHART: {
    //         const ids = element.content.drillSettings.dataSetId
    //         result.needsIds.push(ids)
    //       }
    //     }
    //   })
    //   return result
    // },
    // TODO更新看板的数据集
    updateBoardRes (row) {
      if (row.data) {
        // 参数组件信息
        this.paramsPanelList = row.data.paramsPanelList
        this.elList = row.data.elList
        this.dynamicTags = row.data.dynamicTags
        // let datasetIds = []
        // let needsIds = []
        // if (!row.data.boardInfo.id) {
        //   this.close = false
        //   this.style.width = '212'
        //   this.right.right = '-16px'
        //   this.style.padding = '16px 0 16px 16px'
        // }
        this.initCall(row)
        this.defaultAuthorityBoardId = row.data.authorityBoardId
        this.displayBoardInfo = row.data.boardInfo
        this.datasetTenantData = row.data.tenantData
        // if (row.data.boardDatas) {
        //   const result = this.getBoardResIds(row)
        //   datasetIds = result.datasetIds.filter(item => item)
        //   needsIds = result.needsIds.filter(item => item)
        // }
        // if (datasetIds.length) {
        //   this.$set(this.dataSets, 'quoteIds', needsIds)
        //   this.updateDaSetIds(datasetIds)
        // }
      }
    },
    // 是否收拢
    closeDataSet () {
      if (this.style.width > 0) this.oldWith = this.style.width
      this.tooltipVisible = false
      if (!this.close) {
        this.close = true
        this.style.width = '0'
        this.right.right = '-16px'
        this.style.padding = '0'
      } else {
        this.close = false
        switch (this.type) {
          case TYPE_LIST.GRID: {
            this.style.width = this.oldWith
            this.right.right = '-16px'
            this.style.padding = '16px 0 16px 16px'
            break
          }
          case TYPE_LIST.BOARD: {
            this.style.width = (this.oldWith > 0) ? this.oldWith : '212'
            this.right.right = '-16px'
            this.style.padding = '16px 0 16px 16px'
            break
          }
          case TYPE_LIST.CHART: {
            Object.assign(this.style, {
              padding: '16px',
              width: this.oldWith || '232',
            })
            break
          }
          case TYPE_LIST.CARD: {
            this.style.width = this.oldWith
            this.right.right = '-16px'
            this.style.padding = '16px 0 16px 16px'
            break
          }
        }
      }
    },
    // 更新表格的数据集
    updateGRidIds (val) {
      if (val && val.length) {
        this.$set(this.dataSets, 'quoteIds', val)
        this.updateDaSetIds(val)
      }
    },
    addDataSetItem({ data }) {
      const datasetList = this.dataSets?.datasetList
      if (datasetList) {
        const ids = datasetList.map(item => item.id)
        data = data.filter(item => !ids.includes(item.id))
        if (data.length) {
          datasetList.push(...data)
        }
      } else {
        this.$set(this.dataSets, 'datasetList', data)
        let selectedDataset = null
        if (this.elementType === '2' || this.elementType === '3') {
          selectedDataset = (data.length > 1 || !this.associatedDataset) ? { dateType: 'associateDataset' } : this.associatedDataset
        } else if (this.elementType !== '1') {
          selectedDataset = data[0]
        }
        this.datasetClick(selectedDataset || {})
      }
    },
    // 更新数据集id
    // 该方法被其它组件使用
    // 修改此函数时, 请通知:
    // packages/components/boardDesign/index.vue --> updateDataset 函数
    // 进行相应修改
    updateDaSetIds (Ids, type = '') {
      this.treeLoading = true
      const ids = []
      const keys = Object.keys(this.dataSets)
      const arrkeys = ['datasetList']
      if (keys.includes(arrkeys[0]) && type !== 'replace') {
        const tempDataSetListIds = this.dataSets.datasetList ? this.dataSets.datasetList.map(item => item.id) : []
        const tempIds = Array.from(new Set([...tempDataSetListIds, ...Ids]))
        ids.push(...tempIds)
      } else {
        ids.push(...Ids)
      }
      const paramsBoardId = this.type === TYPE_LIST.BOARD ? { boardId: this.defaultAuthorityBoardId } : {}
      const params = {
        ids: Array.from(new Set(ids)),
        tenantId: this.tenant || 'a018',
        ...paramsBoardId
      }

      const callback = (res = [], virtualDataSet = null) => {
        this.treeLoading = false
        // 更改返回的数据类型
        // if (res.length) {
        //   res.map(item => {
        //     let children = []
        //     if (item.children.length) {
        //       // 改变为空的数组
        //       item.children = item.children.map(e => {
        //         if (e.columnTpe === '') e.columnTpe = 'string'
        //         return e
        //       })
        //     }
        //     return item
        //   })
        // }
        this.$set(this.dataSets, 'datasetList', res)
        if (virtualDataSet) {
          this.dataSets.datasetList.push(virtualDataSet)
        }
        if (this.elementType === '2' || this.elementType === '3') {
          if (res.length === 1 && !this.associatedDataset) {
            this.datasetClick(res[0])
          } else if (this.associatedDataset) {
            this.datasetClick(this.associatedDataset)
          } else {
            this.datasetClick({ dateType: 'associateDataset' })
          }
        } else if (this.elementType !== '1') {
          this.datasetClick(res[0])
        }
        if (this.dataSets.datasetList.length === 1) {
          this.ids = [res[0]?.id]
        } else {
          if (this.ids.length && !this.ids[0]) this.ids = []
        }
        if (this.dataSets.type) this.updateRes(this.dataSets.type)
      }

      if (this.type === 'grid' && !this.isTemplateBoard) {
        const virtual = 'virtual'
        let virtualDataSet = null
        if (ids.includes(virtual)) {
          const virtualData = this.dataSets.datasetList.find(e => e.id === virtual)
          if (virtualData) {
            const virIds = Array.from(new Set(virtualData.children.map(e => e.dataSetId)))
            const flag = virIds.every(id => ids.includes(id))
            if (flag) {
              virtualDataSet = virtualData
            }
          }
        }
        const res = this.datasetList.filter((item) => params.ids.includes(item.id))
        callback(this.$_JSONClone(res), this.$_JSONClone(virtualDataSet))
        return
      }

      getDataSetTreeByIds(this.api, params, this.aliasDict).then(callback)
    },
    // 更新外部数据
    updateRes (type) {
      switch (type) {
        case TYPE_LIST.BOARD: {
          // const ids = this.dataSets.datasetList.map(item => item.id)
          // this.data.datasetIds = JSON.stringify(ids)
          this.setDatasetList()
          break
        }
        case TYPE_LIST.GRID: {
          // 更新表格组件的数据集
          this.$emit('update:gridDatasetList', this.dataSets.datasetList)
          // 告知表格组件新增了数据集
          this.$emit('add-dataset-list', this.dataSets.datasetList)
          break
        }
        case TYPE_LIST.CHART: {
          this.$emit('update:gridDatasetList', this.dataSets.datasetList)
          break
        }
        case TYPE_LIST.CARD: {
          this.$emit('add-dataset-list', this.dataSets.datasetList)
          this.$emit('update:gridDatasetList', this.dataSets.datasetList)
          break
        }
      }
    },
    setDatasetList () {
      const eventData = new EventData({
        type: 'setDataSetList',
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'setDataSetList',
        data: this.$_JSONClone(this.dataSets.datasetList),
      })
      this.$emit('eventBus', eventData)
    },
    // 根据不同字段渲染不同的图标
    selectType (columnTpe) {
      let sdpiconfont = ''
      switch (columnTpe) {
        case 'string':
          sdpiconfont = 'icon-sdp-Fonttype'
          break
        case 'number':
          sdpiconfont = 'icon-sdp-Numerical'
          break
        case 'date':
          sdpiconfont = 'icon-sdp-Calendar'
          break
        default:
          sdpiconfont = 'icon-sdp-Fonttype'
          break
      }
      return sdpiconfont
    },
    // removeDataset  删除数据集
    removeDataset () {
      const datasetList = this.dataSets.datasetList
      const removeObjId = this.dataSets.removeObj.id
      if (this.ids.length && (this.ids[0] === this.dataSets.removeObj.id)) this.ids = []
      const differenceList = datasetList.filter(item => ![removeObjId].includes(item.id))
      this.$set(this.dataSets, 'datasetList', differenceList)
      if (this.selectedDataset?.id === removeObjId) {
        this.datasetClick({})
      }
      this.$set(this.dataSets, 'removeObj', {})

      // 更新表格组件的数据集
      this.$emit('update:gridDatasetList', this.dataSets.datasetList)
      // 告知表格组件被移除的数据集ID
      this.$emit('remove-dataset-list', removeObjId)

      this.setDatasetList()
    },
    openFieldReferenceDialog(data) {
      Object.assign(this.dataSets, { 'activeObj': data })
      this.fieldReferenceVisible = true
    },
    // 数据集具体数据查询
    openDataDetailPreview (data) {
      if (data.enableFlag === '0') return void ''
      Object.assign(this.dataSets, { 'activeObj': data })
      if (data.indexFlag) {
        if (SIM_TYPE.isSim) {
          this.sdpBus.$emit(EVENT_BUS.EXTERNAL_CALL, {
            type: EXTERNAL_CALL_TYPE.INDICATOR_RREVIEW,
            data: {
              id: data.indexId
            }
          })
        } else {
          this.dataSetIndicatorPreviewShow = true
        }
      } else {
        this.dataSetPreviewShow = true
      }
    },
    // 打开引用数据集弹窗
    openLinkDatasetDialog () {
      // if (this.isChart && !this.boardDatasetList.length) return
      this.$nextTick(() => {
        this.$set(this, 'dialogVisible', true)
        this.componentId = ReferenceDataSet
      })
    },
    // 关闭弹窗
    closeDia () {
      this.$nextTick(() => {
        this.$set(this, 'dialogVisible', false)
        this.componentId = ''
      })
    },
    handleDeleteDataset(event, datasetItem) {
      // 阻止事件冒泡
      event.stopPropagation()
      const callback = () => {
        if (this.type === TYPE_LIST.BOARD) {
          // 获取参数组件引用的数据集
          let paramsPanelIds = []
          if (this.paramsPanelList) paramsPanelIds = getParamsPanelLisDataSetIds(this.paramsPanelList)
          const TYPE = [
            TYPE_ELEMENT.CHART,
            TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
            TYPE_ELEMENT.FOUR_QUADRANT,
            TYPE_ELEMENT.TABLE,
            TYPE_ELEMENT.COMBINE_CARD,
            TYPE_ELEMENT.CUSTOMER_ELEMENT,
            TYPE_ELEMENT.DUPONT_ANALYSIS,
            TYPE_ELEMENT.TEXT,
          ]
          const ids = getElListDataSetIds(this.elList, TYPE)
          // 确保没有引用数据集时，可以删除
          this.$set(this.dataSets, 'quoteIds', [...ids, ...paramsPanelIds])
        }
        Object.assign(this.dataSets, { 'removeObj': datasetItem })
        this.openRemoveDataset()
      }
      if (this.elementType === '2' || this.elementType === '3') {
        // 删除结果数据集时，需要二次确认
        if (datasetItem.dateType === 'associateDataset') {
          const text = this.elementType === '3'
            ? this.$t('sdp.message.deleteResultDataset_card_msg')
            : this.$t('sdp.message.deleteResultDataset_msg')
          this.$sdp_eng_confirm(`${text}`, this.$t('sdp.dialog.hint'), {
            confirmButtonText: this.$t('sdp.button.ensure'),
            cancelButtonText: this.$t('sdp.button.cancel'),
            cancelButtonClass: 'el-button--sdp-cancel',
            confirmButtonClass: 'el-button--sdp-ensure',
            customClass: 'theme-messagebox sdp-dialog',
            type: 'warning',
          }).then(() => {
            this.$emit('delete-result-dataset')
            this.datasetClick({ dateType: 'associateDataset' })
          })
        } else if (this.currentEditData.content.associatedData?.referenceDatasetId?.includes(datasetItem.id)) {
          this.$message(this.$t('sdp.message.deleteAssociationDataset_msg'))
        } else {
          callback()
        }
      } else {
        callback()
      }

    },
    // 打开删除弹窗
    openRemoveDataset () {
      this.$nextTick(() => {
        this.$set(this, 'dialogVisible', true)
        this.componentId = RemoveDatasetDialog
      })
    },
    callChartDataSetMethod(param, datasetId = this.selectedDataset?.id) {
      if (datasetId !== this.selectedDataset?.id) return
      this.selectedDataset && (this.selectedDataset.children = param)
    },
  },
  destroyed() {
    document.removeEventListener('click', this.handlerCheckReferenced)
  }
}
</script>
<style lang="scss" scoped>
.dataset-bg-grid {
  background-color: var(--sdp-szk-bjs);
}
.icon-bg-grid {
  background-color: var(--sdp-tb-bj);
}
.grid-w {
  min-width: 190px;

  .right{
    //padding-right: 14px;
  }
}
.icon-sdp-zuojiantou {
  color: var(--sdp-xlk-jts)
}
.dataset-bg-board {
  background-color: var(--sdp-szk-bjs);
}
.board {
  box-shadow: -4px 0 4px 6px rgba(0, 0, 0, 0.06);
  /deep/ .right {
    padding-right: 12px;
  }
}
.dataSet-wrapper {
  margin: 0 auto;
  position: relative;
  height: 100%;
  font-family: NotoSansHans-Regular;
  background-color: var(--sdp-szk-bjs);

  // opacity: 0.9;
  .top-icon {
    position: absolute;
    right: 0;
    top: 0;
    opacity: 0;
    bottom: 0;
    width: 10px;
    cursor: col-resize;
    z-index: 1;
  }
  .top-grid {
    right: -10px;
  }
  .top-board {
    right: -6px;
  }
  /deep/ .close {
    position: absolute;
    right: -16px;
    top: 50%;
    width: 18px;
    opacity: 1;
    i {
      cursor: pointer;
      box-shadow: 3px 6px 6px rgba(0, 0, 0, 0.1);
      position: absolute;
      right: -2px;
      left: 2px;
      top: 0;
      height: 64px;
      line-height: 64px;
      z-index: 1;
    }
    .reverse-icon {
      transform: rotate(-180deg);
      opacity: 1;
    }
    &+div{
      height: 100%;
    }
  }
  .dataSet {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    .row {
      flex: 1;
    }
    .top {
      flex: none;
      display: flex;
      padding-bottom: 12px;
      flex-direction: row;
      .left {
        align-self: center;
        flex: 1;
        font-family: NotoSansHans-Regular;
        font-weight: 600;
        font-size: 14px;
        color: var(--sdp-cszjsz-wzs1);
        text-align: left;
        line-height: 14px;
      }
      .right {
        align-self: center;
        flex: 1;
        font-weight: bold;
        text-align: right;
        span {
          cursor: pointer;
        }
        .right-icon {
          margin-left: 8px;
        }
        i {
          font-weight: normal;
          //color: var(--sdp-is);
          color: var(--sdp-zs);
          font-size: 14px;
          &:hover {
            color: var(--sdp-zs);
          }
          .icon-sdp-yulan:hover {
            color: var(--sdp-zs) !important;
          }
          &.association-disable{
            color: var(--sdp-jys);
            cursor: not-allowed;
          }
        }
      }
      .new-template-tooltip {
        position: absolute;
        left: 50%;
        top: 20px;
      }
    }
    .bottom {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
  }
  .closeDataSet {
    display: none;
  }
}
.dataSet-wrapper:hover {
  .close {
    opacity: 1;
  }
  .top-icon {
    opacity: 1;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-active {
  opacity: 0;
}
.bottom {
  overflow-y: scroll;
  border: 0 none;
  outline: none;
  /*兼容火狐*/
  scrollbar-width: none;
  /* 兼容IE10+ */
  -ms-overflow-style: none;
}
.bottom::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 0 !important; /*高宽分别对应横竖滚动条的尺寸*/
  height: 0 !important;
}
.bottom::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: none;
}
.bottom::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: #ffffff;
}
.data-set-trees {
  background: transparent !important;
}
.dataset-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px 0 12px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  font-size: 12px;
  color: var(--sdp-cszjsz-wzs1);
  &:not(.disable):hover, &:not(.disable).active{
    background-color: var(--sdp-sjj-hs);
    color: var(--sdp-zs);
  }
  .dataset-name{
    @include ellipsis;
    font-weight: bold;
    .indicator-icon{
      font-size:16px;color:var(--sdp-zs);vertical-align: bottom;margin-right: 3px;
    }
    .cited-data-icon {
      color: var(--sdp-jys);
      font-size: 14px;
      margin-right: 6px;
    }
    .cited-data-active-icon {
      color: var(--sdp-zs);
    }
  }
  .dataset-oprate{
    flex-shrink: 0;
    height: 100%;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .is-card {
      margin-right: 0;
    }
    i{
      font-size: 12px;
      cursor: pointer;
    }
  }
  .icon-sdp-yulan{
    margin-right: 12px;
    color: var(--sdp-zs);
  }
  .el-icon-delete-solid{
    color: var(--sdp-qcgls);
  }
  &:not(.disable).dataset-high-light {
    color: var(--sdp-zs) !important;
  }
  &.disabled{
    cursor: not-allowed;
    .dataset-name{
      color: var(--sdp-jys)
    }
  }
  .disabledIcon {
    cursor: not-allowed !important;
    color: var(--sdp-jys)
  }
}
</style>
