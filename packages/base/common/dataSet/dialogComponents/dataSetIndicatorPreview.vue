<template>
  <DialogRoot
    :visible="dialogVisible"
    width="1200px"
    dialogType="no-footer"
    @close="closeDia"
    @open="handlePreview"
  >
  <template #title v-if="dataSets.activeObj">
      <i :class="['indicator-icon', dataSets.activeObj.indexType === 3 ? 'icon-sdp-fuhezhibiao' : (dataSets.activeObj.indexType === 2 ? 'icon-sdp-paishengzhibiao' : 'icon-extend-yuanzizhibiao')]"></i>
      <span style="margin-left: 11px;" :title="property.labeName" >{{dataSets.activeObj.labeName}}</span>
  </template>
    <div
      class="content table-header-user-select-text main"
      element-loading-spinner="sdp-loading-gif"
      v-loading="loading || tableLoading || optionLoading"
    >
      <div
        :class="{
          'search': true,
          'unfold': !hasFold
        }"
      >
        <div class="search-row">
          <div class="search-title">
            {{ $t('sdp.views.statisticsDate') }}
          </div>
          <el-date-picker
            :disabled="loading || tableLoading || optionLoading"
            v-model="searchParams['dateRange']"
            type="daterange"
            range-separator="~"
            value-format="yyyy-MM-dd"
            :start-placeholder="$t('sdp.views.startDate')"
            :end-placeholder="$t('sdp.views.endDate')"
            :popper-class="`sdp-params-customdata-style ${getCurrentThemeClass()}`"
            @change="val => getTableData('dateRange', val)"
          >
          </el-date-picker>
        </div>

        <div
          class="search-row"
          v-for="item in searchObjs"
          :key="item.fieldAlias"
        >
          <div
            class="search-title"
            :title="langCode === 'zh' ? item.nameZh || item.fieldName : item.nameEn || item.fieldName"
          >
            {{ langCode === 'zh' ? item.nameZh || item.fieldName : item.nameEn || item.fieldName }}
          </div>
          <el-select
            class="search-item"
            v-model="searchParams[item.fieldName]"
            multiple
            collapse-tags
            clearable
            :popper-class="'board-design-pop '+getCurrentThemeClass()"
            @change="val => getTableData(item.fieldName, val)"
          >
            <el-option
              v-loading="optionLoading"
              v-for="item in options[item.fieldAlias]"
              :key="item.value"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="line" v-if="searchObjs && searchObjs.length > 1">
        <el-button class="button" type="text" @click="changeLine">
          {{ hasFold ? $t('sdp.views.expand') : $t('sdp.views.collapse') }}
        </el-button>
      </div>
      <el-table
        height="420px"
        class="fix-table"

        v-if="tableData.length"
        :data="tableData"
      >
        <el-table-column
          v-for="header in tableHeader"
          :key="header.prop"
          :label="header.label"
          :prop="header.prop"
          min-width="120px"
          show-overflow-tooltip
        >
          <span slot="header" class="table-title" :title="header.label">
            <i :class="getPropIcon(header)" :title="header.type" class="item-icon"></i>
            {{ header.label }}
          </span>
        </el-table-column>
      </el-table>
      <div v-if="!tableData.length" class="nodate-style">
        {{ loading ? '' : $t('sdp.views.noData') }}
      </div>
    </div>
  </DialogRoot>
</template>
<script>
// import { getDataPreview, getDatasetInfo, getExternalDatasetInfo } from '../../../board/mixins/api'
import {
  getComplexIndex,
  getDeriveIndex,
  valueView,
  indexPreview,
  complexPreview,
} from '../../../board/mixins/api';
import { ALL_PROJECT_NAME } from '../../../../components/mixins/commonMixin';
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog';
export const getFieldIcon = (columnTpe) => {
    let type = columnTpe.toLowerCase()
    if (type === 'number') return 'icon-sdp-Numerical'
    if (type === 'date') return 'icon-sdp-Calendar'
    return 'icon-sdp-Fonttype'
}
export default {
  mixins: [mixin_dialog],
  name: 'dataSetIndicatorPreview',
  data() {
    return {
      loading: false,
      tableLoading: false,
      optionLoading: false,

      params: {},
      searchParams: {},
      tableData: [],
      searchObjs: [],
      options: {},
      tableHeader: [],
      hasFold: true,

      tableColumns: [],
      dateSetInfo: []
    };
  },
  inject: ['utils', 'langCode','getCurrentThemeClass'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dataSets: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    projectName() {
      return this.utils.env?.projectName || ALL_PROJECT_NAME.OMS;
    },
    api() {
      return this.utils.api || function() {};
    },
    property() {
      return this.dataSets?.activeObj || {};
    }
  },
  methods: {
    changeLine() {
      this.hasFold = !this.hasFold;
    },
    // 预览
    handlePreview() {
      this.loading = true;
      console.log(this.dataSets);
      const property = this.dataSets?.activeObj || {};

      if (property.indexType === 2) {
        // 派生指标详情
        getDeriveIndex(this.api, {
          id: property.indexId
        })
          .then(data => {
            const sourceTableId = data.outputId;
            this.$set(this.searchParams, 'dateRange', []);
            if (
              data &&
              data.dimensionFieldVOS &&
              data.dimensionFieldVOS.length
            ) {
              this.searchObjs = [...data.dimensionFieldVOS];
              const temp = [];
              data.dimensionFieldVOS.forEach(item => {
                this.$set(this.searchParams, item.fieldAlias, []);
                temp.push(
                  valueView(this.api, {
                    fieldName: item.fieldAlias,
                    sourceTableId
                  }).then(res => {
                    if (res && res.rows) {
                      this.$set(
                        this.options,
                        item.fieldAlias,
                        res.rows.map(i => ({
                          value: i[item.fieldAlias.toUpperCase()]
                        }))
                      );
                    }
                  })
                );
              });
              this.optionLoading = true;
              Promise.all(temp)
                .then()
                .finally(() => {
                  this.optionLoading = false;
                });
            }
            this.params = {
              ...data
            };
            this.getTableData();
          })
          .finally(() => {
            this.loading = false;
            this.updateTable();
          });
      } else if (property.indexType === 3) {
        getComplexIndex(this.api, {
          id: property.indexId
        })
          .then(data => {
            const sourceTableId = data.outputId;
            // const sourceTableId = data.outputSchema;
            this.$set(this.searchParams, 'dateRange', []);
            if (
              data &&
              data.dimensionFieldVOS &&
              data.dimensionFieldVOS.length
            ) {
              this.searchObjs = [...data.dimensionFieldVOS];
              const temp = [];
              data.dimensionFieldVOS.forEach(item => {
                this.$set(this.searchParams, item.fieldAlias, []);
                temp.push(
                  valueView(this.api,{
                    fieldName: item.fieldAlias,
                    sourceTableId
                  }).then(res => {
                    if (res && res.rows) {
                      this.$set(
                        this.options,
                        item.fieldAlias,
                        res.rows.map(i => ({
                          value: i[item.fieldAlias.toUpperCase()]
                        }))
                      );
                    }
                  })
                );
              });
              this.optionLoading = true;
              Promise.all(temp)
                .then()
                .finally(() => {
                  this.optionLoading = false;
                });
            }
            this.params = {
              ...data
            };
            if (
              data &&
              data.indexModelSub &&
              data.indexModelSub.atomIndexConfigs
            ) {
              this.params.indexModelSub.atomIndexConfigs = JSON.parse(
                data.indexModelSub.atomIndexConfigs
              );
            }

            this.getTableData();
          })
          .finally(() => {
            this.loading = false;
            this.updateTable();
          });
      }
    },
    getColumnTypeIcon(type) {
      if (type === 'string') return 'icon-sdp-Fonttype';
      if (type === 'number') return 'icon-sdp-Numerical';
      if (type === 'date') return 'icon-sdp-Calendar';
      return 'icon-sdp-Fonttype';
    },
    getAliasNameLan(label) {
      if (this.projectName === ALL_PROJECT_NAME.OMS) return label;
      let curItem =
        this.dateSetInfo?.length &&
        this.dateSetInfo.find(item => item.name === label.toLocaleLowerCase());
      if (curItem) {
        if (curItem.aliasNameLan && this.langCode) {
          let lan = JSON.parse(curItem.aliasNameLan);
          return lan?.[this.langCode] || curItem?.aliasName || label;
        }
        return curItem?.aliasName || label;
      }
      return label;
    },
    closeDia() {
      this.$emit('update:visible', false);
      this.$emit('closeDia');
    },
    updateTable() {
      // this.$nextTick(() => {
      //   this.$refs.table.doLayout()
      // })
    },
    getTableData(key, data = []) {
      const jsonParamsList = [];
      if (key === 'dateRange') {
        if (data && data.length) {
          this.params.indexModelSub.statCycleStart = data[0];
          this.params.indexModelSub.statCycleEnd = data[1];
        } else {
          this.params.indexModelSub.statCycleStart = '';
          this.params.indexModelSub.statCycleEnd = '';
        }
        this.searchObjs.forEach(i => {
          jsonParamsList.push({
            fieldName: i.fieldName,
            fieldid: i.id,
            filterType: '2',
            dimensionField: key === i ? data : this.searchParams[i.fieldName],
            fieldAlias:i.fieldAlias
          });
        });
      } else if (key && data) {
        this.searchObjs.forEach(i => {
          jsonParamsList.push({
            fieldName: i.fieldName,
            fieldid: i.id,
            filterType: '2',
            dimensionField: key === i ? data : this.searchParams[i.fieldName],
            fieldAlias:i.fieldAlias
          });
        });
      }
      console.log('jsonParamsList: ', jsonParamsList);
      console.log(this.dataSets.activeObj);
      console.log(this.dataSets.activeObj.indexType);
      this.params.indexModelSub.limitContent = JSON.stringify(jsonParamsList);
      this.tableLoading = true;
      if (this.dataSets?.activeObj.indexType === 2) {
        // 派生指标预览
        indexPreview(this.api, {
          ...this.params,
          isResultPreview: true
        })
          .then(data => {
            console.log(
              '%c [ data ]-254',
              'font-size:13px; background:#c62925; color:#ff6d69;',
              data
            );
            // 置空
            this.tableHeader = []
            if (data.rowTypes) {
              const keys = Object.keys(data.rowTypes);
              if (keys && keys.length) {
                let rowTypes = JSON.parse(JSON.stringify(data.rowTypes));
                for (const key in rowTypes) {
                  this.tableHeader.push(
                    {
                      prop: key,
                      label: key,
                      type:rowTypes[key]
                    }
                  )
                }
              } else {
                this.tableHeader = [];
              }
            } else {
              this.tableHeader = [];
            }
            if (data.rows) {
              this.tableData = data.rows;
            } else {
              this.tableData = [];
            }
          })
          .finally(() => {
            this.tableLoading = false;
          });
      } else if (this.dataSets.activeObj.indexType === 3) {
        if (
          this.params &&
          this.params.indexModelSub &&
          this.params.indexModelSub.content
        ) {
          const obj = JSON.parse(this.params.indexModelSub.content);
          if (obj.undoList && obj.undoList.length) {
            let referIndexIds = [];
            obj.undoList.forEach(outer => {
              if (outer.datas && outer.datas.length) {
                referIndexIds = [
                  ...referIndexIds,
                  ...outer.datas
                    .filter(i => i.indexType && i.indexType === 2)
                    .map(i => i.nodeValue)
                ];
              }
            });
            this.params.referIndexIds = referIndexIds;
          }
        }
        if (this.params && this.params.dimensionFieldVOS) {
          this.params.dimensionFieldDTOList = this.params.dimensionFieldVOS;
        }
        complexPreview(this.api, {
          ...this.params,
          isResultPreview: true
        })
          .then(data => {
            // 置空
            this.tableHeader = []
            if (data.rowTypes) {
              const keys = Object.keys(data.rowTypes);
              if (keys && keys.length) {
                let rowTypes = JSON.parse(JSON.stringify(data.rowTypes));
                for (const key in rowTypes) {
                  this.tableHeader.push(
                    {
                      prop: key,
                      label: key,
                      type:rowTypes[key]
                    }
                  )
                }
              } else {
                this.tableHeader = [];
              }
            }
            if (data.rows) {
              this.tableData = data.rows;
            } else {
              this.tableData = [];
            }
          })
          .finally(() => {
            this.tableLoading = false;
          });
      }
    },
    getPropIcon(propItem) {
      return getFieldIcon(propItem.type)
    },
  }
};
</script>

<style scoped lang="scss">
@import 'packages/base/board/displayPanelMobile/components/variable.scss';

/deep/ .sdp-dialog {
  .el-dialog__header {
    .el-dialog__title {
      color: $color-main;
      font-weight: bold;
      margin: 4px !important;
      font-size: 20px !important;
    }
  }
  .el-dialog__body {
    padding: 10px 24px 24px !important;
  }
}
.content {
  //min-height: 200px;
  padding-left: 0;
  margin-bottom: 20px;
  //overflow: auto;
  .preview-title {
    font-size: 14px;
    font-weight: 600;
    display: block;
    margin-bottom: 10px;
  }
  .table-container {
    padding: 0px !important;
    /deep/ .el-table__body-wrapper {
      overflow: scroll;
      height: 432px;
    }
  }
  .nodate-style {
    width: 100%;
    height: 472px;
    line-height: 472px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: var(--sdp-cszj-tswzs) !important;
  }
  .title {
    width: 100%;
    padding: 0;
  }
  i {
    display: inline-block;
    font-size: 12px;
    font-weight: 400;
  }

}
// /deep/ .el-table {
//   th.gutter {
//     width: 6px !important;
//     display: block !important;
//   }
// }
// /deep/ .el-table--border th.gutter:last-of-type {
//   border-color: var(--sdp-bg-bks);
// }

.table-header-user-select-text {
  /deep/ .el-table th {
    user-select: text !important;
  }
}


.main{
  margin-bottom: 20px;
  .search{
    // max-height: 150px;
    // margin-bottom: 20px;
    height: 88px;
    overflow: hidden;

    display: flex;
    flex-wrap: wrap;

    .search-row{
      margin:12px 24px 12px 0;
      .search-item{
        width: 263px;
      }
      .search-title{
        // color: #333;
        color: var(--sdp-sztc-bts);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 12px;
        margin-bottom: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

    }
    &.unfold{
      height: auto;
    }
  }
  .line{
    position: relative;
    height: 1px;
    background-color: var(--sdp-bg-bks);
    margin-bottom: 20px;
    .button{
      position: absolute;
      top: -6px;
      margin-left: 50%;
      transform: translate(-50%);
      background-color: var(--sdp-szk-bjs);
      // background: var(--sdp-szk-bjs);
      padding: 0;
    }
  }

  // /deep/ .el-table {
  //   th.gutter {
  //     width: 6px !important;
  //     display: block !important;
  //   }
  // }
  // /deep/ .el-table--border th.gutter:last-of-type {
  //   border-color: var(--sdp-bg-bks);
  // }
}
</style>
