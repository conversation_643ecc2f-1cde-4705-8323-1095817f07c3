<template>
  <div class="dataset-content-container" :class="['dataset-elementType__'+ elementType]">
    <div :class="['resizer', {'noResizer': searchVisible}]" @mousedown="startResize('top')" v-if="resizingTop"><span></span></div>
    <div v-if="!highLightItem.commonAndIndependent" class="dataset-search-box">
      <!-- elementType -->
      <div class="dataset-search-box-label-tooltip">
        <ItemLabel :label="$t('sdp.views.field')" :level="1"></ItemLabel>
        <el-tooltip :content="$t('sdp.views.doubleClickIndicatorOrDrag')" placement="top" v-if="needShowTooltipInfo">
          <i class="icon-sdp-info"></i>
        </el-tooltip>
      </div>
      <div>
        <i v-if="showFieldReference" class="icon-sdp-yinyongshujuji" style="margin-right: 8px;" @click="handleOpenFieldReferenceDialog"></i>
        <i class="icon-sdp-shujutianbaosousuo" @click="startSearch(true)"></i>
        <el-dropdown v-if="showCustomFieldField('addIcon') && !isIndicator" trigger="click" placement="right-start">
          <i class="icon-sdp-add"></i>
          <el-dropdown-menu class="sdp-dropdown" :class="getCurrentThemeClass()" slot="dropdown">
            <el-dropdown-item @click.native="openCustomFieldDialog('add', 'metricGroup')">{{ $t('sdp.prop.createProp', { prop: $t('sdp.views.metricGroupField') }) }}</el-dropdown-item>
            <el-dropdown-item @click.native="openCustomFieldDialog('add', 'customComputed')">{{ $t('sdp.prop.createProp', { prop: $t('sdp.views.countField') }) }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="dataset-search-box sdp-grid-design" v-if="searchVisible">
      <el-input v-model="searchText" @change="searchHandler"></el-input>
      <i class="icon-sdp-tuxingquxiaojiaohu" @click="startSearch(false)"></i>
    </div>
    <div class="nodata-box" v-if="!fieldContent.dimension.length && !fieldContent.metric.length && !fieldContent.indicator.length && !fieldContent.independentDimensions.length && !fieldContent.commonDimensions.length">
      <ItemLabel :label="$t('sdp.views.noData')" :level="4"></ItemLabel>
    </div>
    <div :class="['dataset-content-box']" v-if="fieldContent.dimension.length" :style="{ height: dragParam.middleHeight + 'px' }">
      <div class="dataset-content-head">
        <ItemLabel :label="$t('sdp.views.dimensions')" :level="1"></ItemLabel>
      </div>
      <!-- 维度字段 -->
      <div class="scroll-box" ref="scrollElement_dimension" @scroll="scrollEvent('scrollElement_dimension', 'scroll')">
        <component
          :is="componentId"
          class="dimension-container"
          v-model="fieldContent.dimension"
          :options="{
            group: {
              name: 'dataset-dimension',
              pull:'clone',
              put: false,
            },
            sort: false,
          }"
        >
          <div
            v-for="(itemData) in fieldContent.dimension"
            :key="(itemData.alias || itemData.labeName) + itemData.id"
            :class="{
              'content-item': true,
              'node-disable': highLightItem.enableFlag === '0',
              drag: canDrag,
              'metric-group-field': ['metricGroup', 'customComputed'].includes(itemData.webFieldType),
              'active': customFieldDialogId === itemData.id,
            }"
            :draggable="elementType === '1' && canDrag"
            v-sdp-dataset-drag="{ ...itemData, fieldType: 'dimension' }"
            :title="getPropLabel(itemData, 1, 0)"
            @click="$emit('clickField', itemData)"
          >
            <i :class="getPropIcon(itemData)" :title="itemData.columnTpe" class="item-icon"></i>
            <span class="item-label">{{ getPropLabel(itemData, 1, 0) }}</span>
            <span
              class="item-label"
              :title="getPropLabel(itemData, 1, 0)"
            >{{ itemData.tagName ? `(${ itemData.tagName })` : ''}}</span>
            <el-dropdown v-if="showCustomFieldField('settingicon', itemData)" trigger="click" placement="right-start" @visible-change="dropdownVisibleChange">
              <i class="icon-sdp-guizeyinqing item-icon" @click="customFieldDialogId = itemData.id"></i>
              <el-dropdown-menu class="sdp-dropdown" :class="getCurrentThemeClass()" slot="dropdown">
                <el-dropdown-item @click.native="openCustomFieldDialog(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.edit') }}</el-dropdown-item>
                <el-dropdown-item @click.native="copyCustomFieldhandler(itemData)">{{ $t('sdp.button.copy') }}</el-dropdown-item>
                <el-dropdown-item @click.native="deleteCustomFieldItem(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.menuDelete') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </component>
      </div>
    </div>
    <div :class="['dataset-content-box']" v-if="fieldContent.commonDimensions.length" :style="{ height: dragParam.middleHeight + 'px' }">
      <div class="dataset-content-head">
        <ItemLabel :label="$t('sdp.views.CommonDimension')" :level="1"></ItemLabel>
      </div>
      <!-- 维度字段 -->
      <div class="scroll-box" ref="scrollElement_dimension" @scroll="scrollEvent('scrollElement_dimension', 'scroll')">
        <component
          :is="componentId"
          class="dimension-container"
          v-model="fieldContent.commonDimensions"
          :options="{
            group: {
              name: 'dataset-dimension',
              pull:'clone',
              put: false,
            },
            sort: false,
          }"
        >
          <div
            v-for="(itemData) in fieldContent.commonDimensions"
            :key="(itemData.alias || itemData.labeName) + itemData.id"
            :class="{
              'content-item': true,
              'node-disable': highLightItem.enableFlag === '0',
              drag: canDrag,
              'metric-group-field': ['metricGroup', 'customComputed'].includes(itemData.webFieldType),
              'active': customFieldDialogId === itemData.id,
            }"
            :draggable="elementType === '1' && canDrag"
            v-sdp-dataset-drag="{ ...itemData, fieldType: 'dimension' }"
            :title="getPropLabel(itemData, 1, 0)"
            @click="$emit('clickField', itemData)"
          >
            <i :class="getPropIcon(itemData)" :title="itemData.columnTpe" class="item-icon"></i>
            <span class="item-label">{{ getPropLabel(itemData, 1, 0) }}</span>
            <span
              class="item-label"
              :title="getPropLabel(itemData, 1, 0)"
            >{{ itemData.tagName ? `(${ itemData.tagName })` : ''}}</span>
            <el-dropdown v-if="showCustomFieldField('settingicon', itemData)" trigger="click" placement="right-start" @visible-change="dropdownVisibleChange">
              <i class="icon-sdp-guizeyinqing item-icon" @click="customFieldDialogId = itemData.id"></i>
              <el-dropdown-menu class="sdp-dropdown" :class="getCurrentThemeClass()" slot="dropdown">
                <el-dropdown-item @click.native="openCustomFieldDialog(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.edit') }}</el-dropdown-item>
                <el-dropdown-item @click.native="copyCustomFieldhandler(itemData)">{{ $t('sdp.button.copy') }}</el-dropdown-item>
                <el-dropdown-item @click.native="deleteCustomFieldItem(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.menuDelete') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </component>
      </div>
    </div>
    <div :class="['resizer', {'noResizer': searchVisible}]" @mousedown="startResize('middle')" v-if="((fieldContent.metric.length && fieldContent.dimension.length) || (fieldContent.independentDimensions.length && fieldContent.commonDimensions.length)) && resizingMiddle"><span></span></div>
    <div :class="['dataset-content-box']" v-if="fieldContent.metric.length" :style="{ height: dragParam.bottomHeight + 'px' }">
      <div class="dataset-content-head">
        <ItemLabel :label="$t('sdp.views.measure')" :level="1"></ItemLabel>
      </div>
      <!-- 度量字段 -->
      <div class="scroll-box" ref="scrollElement_metric" @scroll="scrollEvent('scrollElement_metric', 'scroll')">
        <component
          :is="componentId"
          class="metric-container"
          v-model="fieldContent.metric"
          :options="{
            group: {
              name: 'dataset-metric',
              pull:'clone',
              put: false,
            },
            sort: false,
          }"
        >
          <div
            v-for="(itemData) in fieldContent.metric"
            :key="(itemData.alias || itemData.labeName) + itemData.id"
            :class="{
              'content-item': true,
              'node-disable': highLightItem.enableFlag === '0',
              drag: canDrag,
              'metric-group-field': ['metricGroup', 'customComputed'].includes(itemData.webFieldType),
              'active': customFieldDialogId === itemData.id,
            }"
            :draggable="elementType === '1' && canDrag"
            v-sdp-dataset-drag="{ ...itemData, fieldType: 'metric' }"
            :title="getPropLabel(itemData, 1, 0)"
            @click="$emit('clickField', itemData)"
          >
            <i :class="getPropIcon(itemData)" :title="itemData.columnTpe" class="item-icon"></i>
            <span class="item-label">{{ getPropLabel(itemData, 1, 0) }}</span>
            <span
              class="item-label"
              :title="getPropLabel(itemData, 1, 0)"
            >{{ itemData.tagName ? `(${ itemData.tagName })` : ''}}</span>
            <el-dropdown v-if="showCustomFieldField('settingicon', itemData)" trigger="click" placement="right-start" @visible-change="dropdownVisibleChange">
              <i class="icon-sdp-guizeyinqing item-icon" @click="customFieldDialogId = itemData.id"></i>
              <el-dropdown-menu class="sdp-dropdown" :class="getCurrentThemeClass()" slot="dropdown">
                <el-dropdown-item @click.native="openCustomFieldDialog(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.edit') }}</el-dropdown-item>
                <el-dropdown-item @click.native="copyCustomFieldhandler(itemData)">{{ $t('sdp.button.copy') }}</el-dropdown-item>
                <el-dropdown-item @click.native="deleteCustomFieldItem(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.menuDelete') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </component>
      </div>
    </div>
    <div :class="['dataset-content-box']" v-if="fieldContent.independentDimensions.length" :style="{ height: dragParam.bottomHeight + 'px' }">
      <div class="dataset-content-head">
        <ItemLabel :label="$t('sdp.views.IndependentDimension')" :level="1"></ItemLabel>
      </div>
      <!-- 度量字段 -->
      <div class="scroll-box" ref="scrollElement_metric" @scroll="scrollEvent('scrollElement_metric', 'scroll')">
        <component
          :is="highLightItem.isMoreIndex ? 'div' : 'draggable'"
          class="metric-container"
          v-model="fieldContent.independentDimensions"
          :options="{
            group: {
              name: 'dataset-metric',
              pull:'clone',
              put: false,
            },
            sort: false,
          }"
        >
          <div
            v-for="(itemData) in fieldContent.independentDimensions"
            :key="(itemData.alias || itemData.labeName) + itemData.id"
            :class="{
              'content-item': true,
              'node-disable': highLightItem.enableFlag === '0',
              drag: canDrag,
              'metric-group-field': ['metricGroup', 'customComputed'].includes(itemData.webFieldType),
              'active': customFieldDialogId === itemData.id,
            }"
            :draggable="elementType === '1' && canDrag"
            v-sdp-dataset-drag="{ ...itemData, fieldType: 'metric' }"
            :title="getPropLabel(itemData, 1, 0)"
            @click="$emit('clickField', itemData)"
          >
            <i :class="getPropIcon(itemData)" :title="itemData.columnTpe" class="item-icon"></i>
            <span class="item-label">{{ getPropLabel(itemData, 1, 0) }}</span>
            <span
              class="item-label"
              :title="getPropLabel(itemData, 1, 0)"
            >{{ itemData.tagName ? `(${ itemData.tagName })` : ''}}</span>
            <el-dropdown v-if="showCustomFieldField('settingicon', itemData)" trigger="click" placement="right-start" @visible-change="dropdownVisibleChange">
              <i class="icon-sdp-guizeyinqing item-icon" @click="customFieldDialogId = itemData.id"></i>
              <el-dropdown-menu class="sdp-dropdown" :class="getCurrentThemeClass()" slot="dropdown">
                <el-dropdown-item @click.native="openCustomFieldDialog(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.edit') }}</el-dropdown-item>
                <el-dropdown-item @click.native="copyCustomFieldhandler(itemData)">{{ $t('sdp.button.copy') }}</el-dropdown-item>
                <el-dropdown-item @click.native="deleteCustomFieldItem(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.menuDelete') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </component>
      </div>
    </div>
    <div :class="['dataset-content-box']" v-if="fieldContent.indicator.length" style="padding-bottom: 2px;">
      <!-- <div class="dataset-content-head">
        <ItemLabel :label="highLightItem.labeName" :level="1"></ItemLabel>
      </div> -->
      <!-- 指标 -->
      <div class="scroll-box" style="height: 101%;max-height: 600px;" ref="scrollElement_metric" @scroll="scrollEvent('scrollElement_metric', 'scroll')">
        <component
          :is="componentId"
          class="metric-container indicator-container"
          v-model="indicatorDragList"
          :options="{
            group: {
              name: 'dataset-metric',
              pull:'clone',
              put: false,
            },
            sort: false,
          }"
        >
        <div class="dataset-content-head-indicator no-indicator-title-drag  no-indicator-title-drag-not-allowed" v-sdp-dataset-drag="{ ...highLightItem, fieldType: 'indicator' }"
            :class="{ 'content-item': true,
              'node-disable': highLightItem.enableFlag === '0',
              drag: canDrag,
              'active': customFieldDialogId === highLightItem.id,}"
              :draggable="elementType === '1' && canDrag"
              @dblclick="dblclickIndicator(highLightItem)">
              <!-- hbw -->
          <svg v-if="!indicatorShow" width="7" height="7" viewBox="0 0 4 6" fill="none" class="svg-icon-tree-before" xmlns="http://www.w3.org/2000/svg" @click="indicatorShow=!indicatorShow">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M4 3L0 6L0 0L4 3Z" fill="#C0C4CC"/>
          </svg>
          <svg v-else width="7" height="7" viewBox="0 0 7 4" fill="none" class="svg-icon-tree-before"  xmlns="http://www.w3.org/2000/svg" @click="indicatorShow=!indicatorShow">
             <path opacity="0.9" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H7L3.5 4" fill="#C0C4CC"/>
          </svg>
          <i class="indicator-icon" :class="isSim ? {
                1: 'icon-sdp-paishengzhibiao',
                2: 'icon-sdp-fuhezhibiao',
                0: 'icon-sdp-yuanzizhibiao'
              }[highLightItem.indexType] : highLightItem.indexType===2 ? 'icon-sdp-paishengzhibiao':'icon-sdp-fuhezhibiao'"></i>
          <span  class="labeName">{{highLightItem.labeName}}</span>
        </div>
        <!-- <div class="content-list"> -->
        <template  v-if="indicatorShow">
          <div
              v-for="(itemData) in fieldContent.indicator"

              :key="(itemData.alias || itemData.labeName) + itemData.id"
              :class="{
                'content-item': true,
                'indicator-item': true,

                'node-disable': highLightItem.enableFlag === '0',
                drag: canDrag,
                'metric-group-field': ['metricGroup', 'customComputed'].includes(itemData.webFieldType),
                'active': customFieldDialogId === itemData.id,
              }"
              :draggable="elementType === '1' && canDrag"
              v-sdp-dataset-drag="{ ...itemData, fieldType: 'metric' }"
              :title="getPropLabel(itemData, 1, 1)"
              @click="$emit('clickField', itemData)"
            >
              <i :class="getPropIcon(itemData)" :title="itemData.columnTpe" class="item-icon"></i>
              <span class="item-label">{{ getPropLabel(itemData, 1, 2) }}</span>
              <span
                class="item-label"
                :title="getPropLabel(itemData, 1, 0)"
              >{{ itemData.tagName ? `(${ itemData.tagName })` : ''}}</span>
              <el-dropdown v-if="showCustomFieldField('settingicon', itemData)" trigger="click" placement="right-start" @visible-change="dropdownVisibleChange">
                <i class="icon-sdp-guizeyinqing item-icon" @click="customFieldDialogId = itemData.id"></i>
                <el-dropdown-menu class="sdp-dropdown" :class="getCurrentThemeClass()" slot="dropdown">
                  <el-dropdown-item @click.native="openCustomFieldDialog(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.edit') }}</el-dropdown-item>
                  <el-dropdown-item @click.native="copyCustomFieldhandler(itemData)">{{ $t('sdp.button.copy') }}</el-dropdown-item>
                  <el-dropdown-item @click.native="deleteCustomFieldItem(itemData.id, itemData.webFieldType)">{{ $t('sdp.button.menuDelete') }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
          </div>
        </template>
        <!-- </div> -->
        </component>
      </div>
    </div>

    <!-- 度量分组 -->
    <MetricGroupSettingDialog :visible.sync="metricGroupDialogShow" :currentDataset="highLightItem" :boardInfo="displayBoardInfo" :tenantData="tenantInfo" :customFieldId="customFieldDialogId" @eventBus="eventBus" @close="customFieldDialogId = 'add'"></MetricGroupSettingDialog>
    <!-- 计算字段 -->
    <CustomComputedDialog :visible.sync="customComputedDialogShow" :currentDataset="highLightItem" :boardInfo="displayBoardInfo" :tenantData="tenantInfo" :customFieldId="customFieldDialogId" @eventBus="eventBus" @close="customFieldDialogId = 'add'"></CustomComputedDialog>
  </div>
</template>
<script>
import { ItemLabel } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { substring15 } from 'packages/base/board/displayPanel/utils'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import sdpDatasetDrag from './directives'
import MetricGroupSettingDialog from './dialogComponents/metricGroupDialog'
import CustomComputedDialog from './dialogComponents/customComputedDialog'
import eventBus from 'packages/assets/eventBus'
import elementResizeDetectorMaker from 'element-resize-detector'
import { EVENT_BUS } from 'packages/base/board/displayPanel/constants'
import { SIM_TYPE } from 'packages/base/common/referenceDataSet/constants'

export const getFieldIcon = (columnTpe) => {
    if (columnTpe === 'number') return 'icon-sdp-Numerical'
    if (columnTpe === 'date') return 'icon-sdp-Calendar'
    return 'icon-sdp-Fonttype'
}

export default {
  name: 'DatasetContent',
  mixins: [datasetMixin],
  inject: {
    getBoardInfo: { default: () => () => (null) },
    getCurrentThemeClass: { default: () => () => '' },
    tenantData: { default: {} },
    sdpBus: { default: () => () => {} },
  },
  directives: { sdpDatasetDrag },
  components: {
    ItemLabel,
    MetricGroupSettingDialog,
    CustomComputedDialog
  },
  props: {
    highLightItem: {
      type: Object,
      default: () => ({}),
    },
    boardInfo: {
      type: Object,
      default: () => ({}),
    },
    elList: {
      type: Array,
      default: () => ([]),
    },
    elementType: {
      type: String,
      default: '-1', // -1:看板、1：表格、2：图形、3：卡片
    },
    needDrag: {
      type: Boolean,
      default: false,
    },
    showFieldReference: {
      type: Boolean,
      default: false,
    },
    datasetTenantData: {
      type: Object,
      default: () => ({}),
    },
    dragParam: {
      type: Object,
      default: () => ({
        topHeight: 66,  // 默认图形编辑页面的高度
        middleHeight: 200,
        bottomHeight: 200,
        startY: 0,
        startTopHeight: 0,
        startMiddleHeight: 0,
        startBottomHeight: 0,
        resizing: null,
        topMinHeight: 60,
        middleMinHeight: 100
      }),
    },
    needResizing: {
      type: String,
      default: () => 'all'
    }
  },
  data() {
    return {
      isSim: SIM_TYPE.isSim,
      indicatorShow:true,
      searchText: '',
      searchVisible: false,
      metricGroupDialogShow: false,
      customComputedDialogShow: false,
      customFieldDialogId: 'add',
    }
  },
  computed: {
    // 指标拖拽的list
    indicatorDragList(){
      let arr = []
      arr.push(this.highLightItem)
      console.log(arr.concat(this.fieldContent.indicator));
      return  arr.concat(this.fieldContent.indicator)
      // return  Object.values(this.highLightItem).concat(this.fieldContent.indicator);
    },
    needShowTooltipInfo(){
      // 图形，卡片，表格，杜邦分析图左侧数据集字段列表区域增加提示信息
      return ['1','2','3'].includes(this.elementType) && this.isIndicator
    },
    canDrag() {
      return this.needDrag && this.highLightItem.enableFlag !== '0' && !this.dragParam.resizing
    },
    contentList() {
      let customFieldList = []
      if (this.highLightItem.id) {
        if (this.showCustomFieldField('field', { webFieldType: 'metricGroup' })) {
          customFieldList = customFieldList.concat(this.getCustomFieldByDatasetId(this.highLightItem.id, 'metricGroup'))
        }
        if (this.showCustomFieldField('field', { webFieldType: 'customComputed' })) {
          customFieldList = customFieldList.concat(this.getCustomFieldByDatasetId(this.highLightItem.id, 'customComputed'))
        }
      }
      return (this.highLightItem.children || []).concat(customFieldList)
    },
    componentId() {
      // hbw
      return this.elementType === '1' ? 'div' : 'draggable'
    },
    isIndicator(){
      return this.highLightItem.indexFlag
    },
    // hbw
    fieldContent() {
      let dimensionDatasetItem = []
      let metricDatasetItem = []
      let metricGroupItem = []
      let dimensionCustomComputed = []
      let metricCustomComputed = []

      let indicatorDatasetItem = []

      let commonDimensions = []
      let independentDimensions = []

      const searchText = (this.searchText || '').toLocaleLowerCase()
      this.contentList.forEach(c => {
          // 如果不包含搜索词，不管
          if (!this.getPropLabel(c, 1, 0).toLocaleLowerCase().includes(searchText)) return
          // 如果选的是指标类型的数据
          if (this.isIndicator) {
            const commonAndIndependent = this.highLightItem.commonAndIndependent
            if (commonAndIndependent) {
              const { common, independent } = commonAndIndependent
              common.some(item => item.labeName === c.labeName) && commonDimensions.push(c)
              independent.some(item => item.labeName === c.labeName) && independentDimensions.push(c)
            } else {
              indicatorDatasetItem.push(c)
            }
          }
          // 如果是度量分组字段，放到维度里面
          else if (c.webFieldType === 'metricGroup') {
            metricGroupItem.push(c)
          } else if (c.webFieldType === 'customComputed' && c.customExpressionType === 'dimension') {
            dimensionCustomComputed.push(c)
          } else if (c.webFieldType === 'customComputed' && c.customExpressionType === 'metric') {
            metricCustomComputed.push(c)
          } else if (c.webFieldFrom === 'associationDataset' && !!c?.customExpressionSet) {
            // customFieldType
            if (c.customExpressionSet.customFieldType === 'variable') { // 维度
              dimensionCustomComputed.push(c)
            } else if (c.customExpressionSet.customFieldType === 'metric') { // 度量
              metricCustomComputed.push(c)
            }

          } else if (c.columnTpe === 'number') {
            metricDatasetItem.push(c)
          } else {
            dimensionDatasetItem.push(c)
          }
      })

      return {
        dimension: dimensionDatasetItem.concat(metricGroupItem).concat(dimensionCustomComputed),
        metric: metricDatasetItem.concat(metricCustomComputed),
        indicator: indicatorDatasetItem,
        commonDimensions,
        independentDimensions,
      }
    },
    // metricContent() {
    //   const metricContent = this.contentList.filter(c => c.columnTpe === 'number')
    //   if (this.searchText) {
    //     const searchText = this.searchText.toLocaleLowerCase()
    //     return metricContent.filter(c => this.getPropLabel(c, 1, 0).toLocaleLowerCase().includes(searchText))
    //   }
    //   return metricContent
    // },
    tenantInfo() {
      if (Object.keys(this.tenantData).length) return this.tenantData
      return this.datasetTenantData || {}
    },
    resizingTop() {
      return ['top', 'all'].includes(this.needResizing)
    },
    resizingMiddle() {
      return ['middle', 'all'].includes(this.needResizing)
    }
  },
  mounted() {
    // this.scrollEvent = this.$_debounce(this.scrollEvent)
    this.erd = elementResizeDetectorMaker()
    this.erd.listenTo(this.$el, (element) => {
      this.scrollEvent('scrollElement_dimension', 'size')
      this.scrollEvent('scrollElement_metric', 'size')
    })
    this.$nextTick(() => {
      this.updateHeights()
      window.addEventListener('resize', this.updateHeights)
    })
  },
  methods: {
    eventBus,
    updateHeights() {
      // const itemDOM = document.querySelector('#dataSets')
      const datasetDOM = document.querySelector('.dataset-drag-wrap')
      const _height = this.resizingTop ? ( datasetDOM ? 240 : 220 ) : 166
      let totalHeight = window.innerHeight - _height
      this.dragParam.topHeight = Math.max(this.dragParam.topMinHeight, this.dragParam.topHeight)
      const metricLength = this?.fieldContent?.metric?.length || this?.fieldContent?.independentDimensions?.length
      const dimensionLength = this?.fieldContent?.dimension?.length || this?.fieldContent?.commonDimensions?.length
      if(metricLength > 0 && dimensionLength > 0) {
        this.dragParam.bottomHeight = this.dragParam.middleHeight = Math.max(this.dragParam.middleMinHeight, (totalHeight - this.dragParam.topHeight) / 2)
      } else if(metricLength >  0 && !dimensionLength ) {
        totalHeight +=  36
        this.dragParam.bottomHeight = Math.max(this.dragParam.middleMinHeight, totalHeight - this.dragParam.topHeight)
        this.dragParam.middleHeight = 0
      } else if(!metricLength && dimensionLength > 0 ) {
        totalHeight +=  36
        this.dragParam.middleHeight = Math.max(this.dragParam.middleMinHeight, totalHeight - this.dragParam.topHeight)
        this.dragParam.bottomHeight = 0
      } else {
        totalHeight +=  36
        this.dragParam.middleHeight = 0
        this.dragParam.bottomHeight = 0
      }
    },
    startResize(area) {
      if(this.searchVisible) return
      this.dragParam.resizing = area
      this.dragParam.startY = event.clientY
      this.dragParam.startTopHeight = this.dragParam.topHeight
      this.dragParam.startMiddleHeight = this.dragParam.middleHeight
      this.dragParam.startBottomHeight = this.dragParam.bottomHeight
      document.addEventListener('mousemove', this.onMouseMove)
      document.addEventListener('mouseup', this.stopResize)
    },
    onMouseMove(event) {
      const itemDOM = document.querySelector('#dataSets')
      if(itemDOM) itemDOM.style.userSelect='none'
      const datasetDOM = document.querySelector('.dataset-drag-wrap')
      if(datasetDOM) datasetDOM.style.userSelect='none'
      const deltaY = event.clientY - this.dragParam.startY
      const metricLength = this?.fieldContent?.metric?.length || this?.fieldContent?.independentDimensions?.length
      const dimensionLength = this?.fieldContent?.dimension?.length || this?.fieldContent?.commonDimensions?.length
      // 是否有度量
      const _bottomFlag = metricLength > 0
      const _middleFlag = dimensionLength > 0

      const _itemHeight = datasetDOM ? 156 : 184
      const _height1 = this.resizingTop ? ( datasetDOM ? 240 : 218 ) : 166
      if (this.dragParam.resizing === 'top') {
        if((deltaY < 0 &&  this.dragParam.startTopHeight + deltaY <= this.dragParam.topMinHeight)
          || (deltaY > 0 && !_bottomFlag && this.dragParam.middleHeight <= this.dragParam.middleMinHeight)
          || (deltaY > 0 && _bottomFlag && _middleFlag && this.dragParam.middleHeight <= this.dragParam.middleMinHeight && this.dragParam.bottomHeight <= this.dragParam.middleMinHeight)
          || (deltaY > 0 && _bottomFlag && !_middleFlag && this.dragParam.bottomHeight <= this.dragParam.middleMinHeight)
        ) {
          return
        } else {
          const newTopHeight = Math.max(this.dragParam.topMinHeight, this.dragParam.startTopHeight + deltaY)
          const newMiddleHeight = _middleFlag ? Math.max(this.dragParam.middleMinHeight, this.dragParam.startMiddleHeight - deltaY) : 0
          // let totalHeight =_bottomFlag ? window.innerHeight - 218 : window.innerHeight - 184
          let totalHeight =_bottomFlag ? window.innerHeight - _height1 : window.innerHeight - _itemHeight
          let _height = _bottomFlag ? this.dragParam.middleMinHeight : -1
          if (totalHeight - (newTopHeight + newMiddleHeight) > _height) {
            this.dragParam.topHeight = newTopHeight
            this.dragParam.middleHeight = newMiddleHeight
            if(metricLength > 0 && dimensionLength > 0) {
              this.dragParam.bottomHeight = this.dragParam.bottomHeight >= this.dragParam.middleMinHeight ? totalHeight - this.dragParam.topHeight - this.dragParam.middleHeight : this.dragParam.middleMinHeight
            } else if(metricLength > 0 && !dimensionLength) {
              this.dragParam.middleHeight = 0
              this.dragParam.bottomHeight = totalHeight - this.dragParam.topHeight
            } else if(!metricLength && dimensionLength > 0) {
              this.dragParam.middleHeight = totalHeight - this.dragParam.topHeight
              this.dragParam.bottomHeight = 0
            } else {
              this.dragParam.middleHeight = 0
              this.dragParam.bottomHeight = 0
            }
          }
        }
      } else if (this.dragParam.resizing === 'middle') {
        if((deltaY < 0 && this.dragParam.middleHeight <= this.dragParam.middleMinHeight && this.dragParam.topHeight <= this.dragParam.topMinHeight)
           || deltaY > 0 && this.dragParam.bottomHeight <= this.dragParam.middleMinHeight
          ) {
          return
        } else {
          const newMiddleHeight = Math.max(this.dragParam.middleMinHeight, this.dragParam.startMiddleHeight + deltaY)
          const newBottomHeight = Math.max(this.dragParam.middleMinHeight, this.dragParam.startBottomHeight - deltaY)
          const totalHeight = window.innerHeight - _height1
          if(totalHeight - (newBottomHeight + newMiddleHeight) > this.dragParam.topMinHeight) {
            this.dragParam.middleHeight = newMiddleHeight
            this.dragParam.bottomHeight = newBottomHeight
            this.dragParam.topHeight = deltaY < 0 && this.dragParam.middleHeight <= this.dragParam.middleMinHeight ? (this.dragParam.topHeight >= this.dragParam.topMinHeight ? totalHeight - this.dragParam.middleHeight - this.dragParam.bottomHeight : this.dragParam.topMinHeight ) : this.dragParam.topHeight
          }
        }
      }
    },
    stopResize() {
      document.removeEventListener('mousemove', this.onMouseMove)
      document.removeEventListener('mouseup', this.stopResize)
      this.dragParam.resizing = null
      const itemDOM = document.querySelector('#dataSets')
      if(itemDOM) itemDOM.style.userSelect='auto'
      const datasetDOM = document.querySelector('.dataset-drag-wrap')
      if(datasetDOM) datasetDOM.style.userSelect='auto'
    },
    dblclickIndicator(data){
      // setDimension
      // 不管拖拽到哪一个，只填充度量到拖拽的那一行,在此处找出度量的数据
      let current = data.children.find(item => item.columnTpe === 'number');
      this.sdpBus.$emit(EVENT_BUS.DBLCLICK_INDICATOR,current)
    },
    showCustomFieldField(type = 'field', itemData = {}) {
      if (type === 'settingicon') {
        return this.elementType === '-1' && (itemData.webFieldType === 'metricGroup' || itemData.webFieldType === 'customComputed')
      } else if (type === 'addIcon') {
        return this.elementType === '-1'
      } else if (type === 'field') {
        if (itemData.webFieldType === 'metricGroup') {
          return this.elementType === '-1' || this.elementType === '2'
        } else if (itemData.webFieldType === 'customComputed') {
          const elementIncludes = ['-1', '1', '2', '3', '4', '5']
          return elementIncludes.includes(this.elementType)
        }
      }
      return false
    },
    scrollEvent(refName, eventType) {
      const refComponent = this.$refs[refName]
      if(!refComponent || !refComponent[`clientWidth`]) return
      const refWidth = refComponent[`clientWidth`]
      let offsetLeft = 152

      if (eventType === 'size') {
        refComponent.scrollTo(0, 0)
        offsetLeft = refWidth - 28
      } else {
        const scrollLeft = refComponent.scrollLeft
        offsetLeft = scrollLeft + refWidth - 28
      }
      refComponent.style.setProperty('--scroll-element-left', offsetLeft + 'px')
    },
    getPropIcon(propItem) {
      // if (propItem.columnTpe === 'number') return 'icon-sdp-Numerical'
      // if (propItem.columnTpe === 'date') return 'icon-sdp-Calendar'
      // return 'icon-sdp-Fonttype'
      return getFieldIcon(propItem.columnTpe)
    },
    copyCustomFieldhandler(itemData) {
      const boardInfoKey = itemData.webFieldType === 'metricGroup' ? 'metricGroupMap' : 'customComputedMap'
      const metricGroupList = this.displayBoardInfo[boardInfoKey]?.[this.highLightItem.id] || []
      const groupIndex = metricGroupList.findIndex(m => m.id === itemData.id)
      const newItem = this.$_deepClone(metricGroupList[groupIndex])
      newItem.id = this.$_generateUUID(metricGroupList.length)
      newItem.customFieldName = this.repeatValidate(itemData.customFieldName)
      metricGroupList.splice(groupIndex + 1, 0, newItem)
    },
    repeatValidate(fieldName, repeatTimes = 1) {
      // 字段名称重复
      let datasetNameList = new Set()
      ;(this.highLightItem?.children || []).forEach(d => {
        datasetNameList.add(d.labeName)
        datasetNameList.add(this.getUnknownName(d.parentId, d.labeName))
      })
      const customComputedFieldList = this.getCustomFieldByDatasetId(this.highLightItem.id, 'ALL')
      customComputedFieldList.forEach(m => {
        // if (m.id === this.cloneCustomField.id) return
        datasetNameList.add(m.customFieldName)
      })
      datasetNameList = Array.from(datasetNameList).map(s => s.toLowerCase())
      let newFieldName = `${ fieldName }(${repeatTimes})`
      while (datasetNameList.includes(newFieldName.toLowerCase())) {
        repeatTimes = repeatTimes + 1
        newFieldName = `${ fieldName }(${repeatTimes})`
      }
      return newFieldName
    },
    deleteCustomFieldItem(id, webFieldType) {
      this.customFieldDialogId = id
      const included = this.elList.find(el => {
        if (!el.vm?.getCustomFieldsUsedInElement) return
        const metricGroupIds = el.vm.getCustomFieldsUsedInElement([webFieldType])
        return metricGroupIds.find(m => m.id === id)
      })
      if (included) {
        // 已经被引用，无法被删除
        this.$sdp_eng_confirm(this.$t('sdp.prop.referencedDeleteProp', { prop: this.$t('sdp.views.thisField') }), `${this.$t('sdp.message.tipsConfirmTitle')}`, {
          // customClass: 'sdp-grid-design',
          cancelButtonText: this.$t('sdp.message.cancelButton'),
          cancelButtonClass: 'el-button--sdp-cancel',
          showConfirmButton: false,
          customClass: 'sdp-dialog',
          type: 'warning',
          closeOnClickModal: false,
          beforeClose: (action, instance, done) => { // bug 13203 表格编辑已经关闭，但是此弹窗还是会存在 延迟半秒才消失，和看板退出做同样处理
            instance.$el.style.zIndex = -1
            done()
          },
        }).then(() => {}).catch(() => {
          this.customFieldDialogId = 'add'
        })
        return
      }
      this.$sdp_eng_confirm(this.$t('sdp.message.confirmDelete'), this.$t('sdp.button.menuDelete'), {
        confirmButtonText: this.$t('sdp.button.ensure'),
        cancelButtonText: this.$t('sdp.button.cancel'),
        cancelButtonClass: 'confirm-cancel el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog',
        beforeClose: (action, instance, done) => {
          instance.$el.style.zIndex = -1
          done()
        },
        closeOnClickModal: false,
        type: 'warning',
      }).then(() => {
        const boardInfoKey = webFieldType === 'metricGroup' ? 'metricGroupMap' : 'customComputedMap'
        const metricGroupList = this.displayBoardInfo[boardInfoKey]?.[this.highLightItem.id] || []
        const groupIndex = metricGroupList.findIndex(m => m.id === id)
        metricGroupList.splice(groupIndex, 1)
      }).catch(() => {
        this.customFieldDialogId = 'add'
      })
    },
    getPropLabel(propItem, labelTrim = 1, commentTrim = 0) {
      // 0表示不用显示，1表示要显示全名，2表示显示钱15个字符
      let label = labelTrim > 0 ? this.getDatasetLabel(propItem, true) : ''
      if (labelTrim > 1) label = substring15(label)
      let comment = commentTrim > 1 ? substring15(propItem.comment) : propItem.comment
      if (commentTrim > 0 && comment) label = `${ label } (${ comment })`
      return label || ''
    },
    handleOpenFieldReferenceDialog() {
      this.$emit('openFieldReference', this.highLightItem)
    },
    searchHandler() {
    },
    startSearch(flag) {
      this.searchText = ''
      this.searchVisible = flag
    },
    reset() {
      this.startSearch(false)
    },
    dropdownVisibleChange(val) {
      if (!val) this.customFieldDialogId = 'add'
    },
    openCustomFieldDialog(id, webFieldType) {
      this.customFieldDialogId = id
      if (webFieldType === 'metricGroup') {
        this.metricGroupDialogShow = true
      } else if (webFieldType === 'customComputed') {
        this.customComputedDialogShow = true
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateHeights)
  },
}
</script>
<style lang="scss" scoped>
.resizer {
  cursor: n-resize;
  position: relative;
  z-index: 10;
  &.noResizer {
    cursor: not-allowed;
  }
  span {
    display: inline-block;
    width: 100%;
    height: 1px;
    margin: 16px 0;
    background-color:  var(--sdp-ycfgx);
  }
}
.dataset-content-container{
  --dataset-hover-bg: #553CCE;
  --scroll-element-left: 152px;
  overflow: hidden;
  // margin-top: 16px;
  padding-top: 8px;
  // height: 100%;
  display: flex;
  flex-direction: column;
  .metric-group-field{
    color: #FF9900 !important;
    position: relative;
    padding-right: 28px;
    box-sizing: border-box;
    .icon-sdp-guizeyinqing{
      color: #fff;
      line-height: 24px;
      height: 24px;
      cursor: pointer;
      margin-right: 0;
      outline: none;
      vertical-align: top;
    }
    /deep/ .el-dropdown {
      position: absolute;
      text-align: center;
      top: 0;
      left: var(--scroll-element-left);
      width: 28px;
      visibility: hidden;
    }
  }
}
.dataset-search-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  height: 30px;
  line-height: 30px;
  flex-shrink: 0;
  .dataset-search-box-label-tooltip{
    display: flex;
    gap: 5px;
    .icon-sdp-info{
      font-size: 20px;
      font-weight: 400;
      color: var(--sdp-zs);
      display: inline-block;
      vertical-align: middle;
    }
  }
  .icon-sdp-yinyongshujuji,
  .icon-sdp-shujutianbaosousuo, .icon-sdp-add{
    font-size: 14px;
    color: $color-main;
    font-weight: bold;
    cursor: pointer;
  }
  .icon-sdp-add{
    margin-left: 8px;
  }
  .icon-sdp-tuxingquxiaojiaohu{
    font-size: 14px;
    cursor: pointer;
    color:var(--sdp-qcgls);
  }
  /deep/ .el-input{
    width: calc(100% - 24px);
  }
}
.nodata-box{
  display: flex;
  justify-content: center;
  align-items: center;
}
.dataset-content-box{
  background-color: var(--sdp-zdbjs);
  padding: 2px 4px 8px;
  height: 100%;
  flex-grow: 1;
  width: 100%;
  overflow: hidden;
  position: relative;
  .dataset-content-head{
    padding: 0 12px;
    line-height: 32px;
    height: 32px;
  }
  .dataset-content-head-indicator{
    padding: 0 12px;
    line-height: 32px;
    height: 32px;
    display:flex;
    align-items: center;
    transition: all 0.3s amount;
    .labeName{
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 500;
      line-height: 12px;
      letter-spacing: 0em;
      text-align: left;
      // color: var(--sdp-cszjsz-wzs1);
      color: var(--sdp-xxbt1);
    }
    .indicator-icon{
      font-size:16px;color:var(--sdp-zs);vertical-align: bottom;margin: 0 7px;
    }
      &:hover{
      background: var(--dataset-hover-bg) !important;
      color: #fff;
      .indicator-icon{
        color: #fff;
      }
      .labeName{
        color: #fff;
      }
      .el-dropdown{
          background: var(--dataset-hover-bg);
          visibility:visible;
      }
    }
  }
  &+.dataset-content-box{
    margin-top: 8px;
  }
}
.content-item{
  margin-top: 2px;
  height: 24px;
  line-height: 24px;
  color: var(--sdp-cszjsz-wzs1);
  padding: 0 8px;
  cursor: default;
  &.drag{
    cursor: pointer;
  }
  &.node-disable{
    color: var(--sdp-srk-bxwzs);
    pointer-events: none;
    cursor: not-allowed;
  }
  .item-label{
    white-space: nowrap;
  }
  &:hover, &.active{
    background: var(--dataset-hover-bg) !important;
    color: #fff;
    .el-dropdown{
        background: var(--dataset-hover-bg);
        visibility:visible;
    }
  }
}
.scroll-box{
  height: calc(100% - 32px);
  overflow: auto;
}
.dimension-container{
  width: max-content;
  min-width: 100%;
  .content-item{
    --dataset-hover-bg: #4F7ED8;
    background: var(--sdp-sjjdxbj);
  }
}
.metric-container{
  width: max-content;
  min-width: 100%;
  .content-item{
    background: rgba(85, 60, 206, 0.2);
  }
}
.indicator-container{
  .dataset-content-head-indicator{
    padding: 0 8px;
  }
    .indicator-item{
      padding-left: 42px;
    }
}
.item-icon {
  font-size: 12px;
  margin-right: 8px;
}
.dataset{
  &-elementType__-1{
    padding-right: 16px;
  }
  &-elementType__2, &-elementType__4{
    padding-right: 0;
    border-top: 0;
    margin-top: 0;
    .dataset-search-box{
      color: var(--sdp-ycsz-glwzs);
      background-color: var(--sdp-zcsjj-bj);
      box-shadow: none !important;
    }
    .dataset-content-box{
      background-color: var(--sdp-zdbjs);
    }
  }
}
// .content-item.sortable-chosen{
//   .hbw-svg{
//       opacity: 0!important;
//   }
// }
</style>
