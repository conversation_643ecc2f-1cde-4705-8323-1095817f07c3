import { getBrowserInfo } from 'packages/assets/utils/system'
const bindFn = function name(el, binding, vnode, oldVnode) {
  const { fieldType, ...contextData } = binding.value

  function createDragImg() {
    const txt = fieldType === 'indicator' ? (el.children[2] || el.children[1]).innerHTML :  el.children[1].innerHTML
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    canvas.width = 600
    ctx.font = '10px sans-serif'
    const txtW = ctx.measureText(txt).width
    const txtX = 12
    const rectW = txtW + txtX * 2

    ctx.fillStyle = fieldType === 'metric' ? 'rgba(85, 60, 206, 1)' : 'rgba(79, 126, 216, 1)'
    ctx.fillRect(0, 0, rectW, 26)

    ctx.fillStyle = '#fff'
    ctx.font = '10px sans-serif'
    ctx.fillText(txt, txtX, 16)

    const image = new Image()
    image.src = canvas.toDataURL('image/png')
    return image
  }
  let img: any = null

  el.onmousedown = function (e) {

    if (getBrowserInfo().browser !== 'Safari') {
      e.stopPropagation()
    }
    img = createDragImg()
  }

  el.ondragstart = function (e) {
    e.dataTransfer.setData('text', JSON.stringify({
      data: contextData,
    }))
    e.dataTransfer.setDragImage(img, -20, 0)
  }

  el.ondragend = function (e) {
    e.target.style.backgroundColor = ''
    // binding.value.dragEndFn()
  }
}

export default {
  bind(el, binding, vnode, oldVnode) {
    bindFn(el, binding, vnode, oldVnode)
  },
  update(el, binding, vnode, oldVnode) {
    bindFn(el, binding, vnode, oldVnode)
  }
}
