<template>
  <div
    v-if="showComponent"
    :class="['warning-bell-container', 'warning-bell_' + element.type, 'warning-bell_' + (utils.isMobile ? 'mobile' : 'pc')]"
  >
    <i
      v-popover:warningList
      :class="['icon', isWarning ? 'icon-sdp-yujingtianchonghongse' : 'icon-sdp-yujing']"
      @click.stop="openSubscribeWarningDialog"
    ></i>
    <el-popover
      ref="warningList"
      :popper-class="'themes-popover sdp-warn-popover ' + getCurrentThemeClass()"
      placement="bottom-end"
      trigger="click"
      :disabled="!(element.content.chartUserConfig || {}).warnExample || !warningList.length || (utils.isMobile && !isChartSet)"
    >
      <el-scrollbar style="max-height: 100px; overflow-y: auto; overflow-x: hidden;" class="scrollbar">
        <div v-for="(item, i) in warningList" :key="i" class="warn-example-list">
          <span class="circle-style" :style="{ backgroundColor: item.chartColor }"></span>
          <span class="length-limit" :title="item.name">{{item.name}}</span>
        </div>
      </el-scrollbar>
    </el-popover>
    <DialogRoot
      :title="$t('sdp.views.alertSubscription')"
      :visible.sync="subscribeWarningDialogVisible"
      :confirmLoading="subscribeWarningDialogLoading"
      width="980px"
      @confirm="subscribeWarningDialogConfirmHandler"
      @close="closeDialog"
    >
      <div class="content">
        <WarningItem
          v-for="(warningItem, wIndex) in elementWarningData.warningList"
          :key="wIndex"
          :ref="'ref_' + warningItem.webWarningId"
          :warningItem="warningItem"
          :currentElementSubscribeLimit="currentElementSubscribeLimit"
          :elementWarningData="elementWarningData"
          :style="{ marginTop: wIndex ? '20px' : '10px' }"
        ></WarningItem>
      </div>
    </DialogRoot>
  </div>
</template>

<script>
import eventBus from 'packages/assets/eventBus'
import { DIMENSION_WARNING_CHART, WARNING_LINE_CHART, DIMENSION_VALUE_INDICATOR_WARNING_CHART } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { PROJECT_MODULE } from 'packages/assets/constant'
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog'
import mixin_warningBell from './scripts/mixin'
import WarningItem from './components/warningItem.vue'
import EventData from 'packages/assets/EventData'
export default {
  name: 'WarningBell',
  inject: {
    isEdit: { default: false },
  },
  mixins: [mixin_dialog, mixin_warningBell],
  components: { WarningItem },
  props: {
    contentItem: {
      type: Object,
      default: () => null,
    },
    isChartSet: {
      type: Boolean,
      default: false,
    },
    isWarning: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      subscribeWarningDialogVisible: false,
      subscribeWarningDialogLoading: false,
      elementWarningData: { warningList: [] },
      currentElementSubscribeLimit: 0,
    }
  },
  watch: {

  },
  computed: {
    isMobile() {
      return this.utils.isMobile
    },
    noWarningBell() {
      if (this.boardWarningSubscribeData?.noWarningBellComponent) {
        return this.boardWarningSubscribeData.noWarningBellComponent(this.isChartSet)
      }
      return true
    },
    showComponent() {
      if (this.utils?.sbiOptions?.isAddDailyConcernElement) return false
      if (this.utils.isPcMobile || (this.commonData.isMobileDataReport && this.commonData.isMobileDataReport())) {
        return false
      }
      if (this.element.type === 'chart') {
        const { chartAlias, warnExample } = this.element.content.chartUserConfig || {}
        if (!WARNING_LINE_CHART.includes(chartAlias)) return false
        if (warnExample && this.warningList.length) return true
      }
      if (this.noWarningBell) return false

      const subscribeLength = this.boardWarningSubscribeData.allowSubscribeWarningLength
      if (subscribeLength[this.element.id]) {
        if (this.contentItem) {
          return subscribeLength[`${ this.element.id }_indicator`][this.contentItem.id]
        }
        return true
      }
      return false
    },
    warningList() {
      if (this.element.type !== 'chart') return []
      const { dimensionWarningList = [], warnLineSettingList = [], dimensionList = [], extendDimensionList = [], chartAlias, warningMethod = {}, dimensionValueIndicatorWarningList = [] } = this.element.content.chartUserConfig
      let orderList = [];
      [...dimensionList, ...extendDimensionList].forEach(d => {
        if (d.order === 'customSort' && d.orderList?.length) {
          orderList = orderList.concat(d.orderList)
        }
      })

      // 指标维度预警
      const indicatorWarnDimenison = [...dimensionList, ...extendDimensionList, ...orderList]
      const dimensionWarningArray = []
      let dimensionValueIndicatorWarningArray = []

      if (DIMENSION_WARNING_CHART.includes(chartAlias) && warningMethod.dimensionMethod === 3) {
        dimensionWarningList.forEach(item => {
          item.warningList.forEach(w => {
            if (indicatorWarnDimenison.find(d => d.keyName === w.fieldKeyName)) {
              dimensionWarningArray.push(w)
            }
          })
        })
      } else if (warningMethod.dimensionMethod === 4 && DIMENSION_VALUE_INDICATOR_WARNING_CHART.includes(chartAlias)) {
        dimensionValueIndicatorWarningArray = dimensionValueIndicatorWarningList
      }

      const warningArray = []
      ;[...warnLineSettingList, ...dimensionValueIndicatorWarningArray].forEach(w => {
        if (!w.allowSubscribe || this.noWarningBell) {
          warningArray.push(w)
        } else {
          const elementWarningData = this.getElementWarningBellData()
          if (elementWarningData.warningList?.find(innerW => innerW.webWarningId === w.id)) {
            warningArray.push(w)
          }
        }
      })

      return [...warningArray, ...dimensionWarningArray]
    },
  },
  methods: {
    eventBus,
    openSubscribeWarningDialog() {
      if (!this.getFinishResult()) {
        return this.$message({
          message: this.$t('sdp.views.componentUndone'),
          duration: 1000,
        })
      }

      const popoverShow = isShowPopover.call(this)
      if (popoverShow.showPopover && !popoverShow.showSubscribePopover) {
        if (this.utils.isMobile) {
          if (this.isChartSet) return
          const eventData = new EventData({
            type: 'openWarn',
            target: ['displayPanel', 'displayPanelMobile'],
            targetFn: 'openWarn',
            data: this.warningList
          })
          this.$emit('eventBus', eventData)
        }
      } else if (popoverShow.showSubscribePopover) {
        if (this.isMobile) {
          // 移动端需调用eventBus
          const eventData = new EventData({
            type: 'openAlertSubscriptionPage',
            target: ['displayPanelMobile', 'displayPanelPcToMobile'],
            targetFn: 'openAlertSubscriptionPage',
            data: {
              elementWarningData: this.elementWarningData,
              currentElementSubscribeLimit: this.currentElementSubscribeLimit,
              element: this.element,
              parentElement: this.parentElement,
            },
          })
          this.$emit('eventBus', eventData)
        } else {
          this.subscribeWarningDialogVisible = true
        }
      }

      function isShowPopover() {
        let showPopover = false
        let showSubscribePopover = false
        if (this.element.type === 'chart') {
          const { warnExample } = this.element.content.chartUserConfig || {}
          // 预警图示
          if (warnExample && this.warningList.length) showPopover = true
        }
        if (this.noWarningBell) {
          showSubscribePopover = false
        } else {
          const { currentElementSubscribeCount, ...elementWarningData } = this.getElementWarningBellData()
          this.elementWarningData = elementWarningData
          // 本元素可订阅个数=用户预警订阅个数-已订阅的个数+本元素已订阅的个数
          this.currentElementSubscribeLimit = (this.boardWarningSubscribeData?.subscribecLimit || 0) - (this.boardWarningSubscribeData?.subscribecCount || 0) + currentElementSubscribeCount

          if (!this.elementWarningData.warningList.length) {
            showSubscribePopover = false
          } else {
            showSubscribePopover = true
          }
        }
        return {
          showPopover,
          showSubscribePopover,
        }
      }
      setTimeout(() => {
        this.$refs.warningList.updatePopper();
      })
    },
    getElementWarningBellData() {
      let result = { warningList: [] }
      if (this.boardWarningSubscribeData?.loadingStatus !== 'loaded') return result
      let elementWarningData = this.$_deepClone((this.boardWarningSubscribeData.getWarningDataByElementId && this.boardWarningSubscribeData.getWarningDataByElementId(this.element.id)) || result)
      let currentElementSubscribeCount = 0
      const warningList = elementWarningData.warningList.map(w => {
        typeof w.ruleParameter === 'string' && (w.ruleParameter = JSON.parse(w.ruleParameter))
        if (['isNull', 'isNotNull'].includes(w.ruleParameter.calcMethod)) {
          w.ruleParameter.fixedValue = w.ruleParameter.calcMethod
          w.ruleParameter.calcType = 'fixed'
        } else if (w.ruleParameter.calcMethod === 'range') {
          w.ruleParameter.calcType = 'fixed'
        }
        if (!w.hasOwnProperty('emsMessageWarningFlag')) this.$set(w, 'emsMessageWarningFlag', '0')
        if (!w.hasOwnProperty('subscribeFlag')) this.$set(w, 'subscribeFlag', '0')
        return w
      }).filter(w => {
        if (this.contentItem && w.ruleParameter.indicatorConfig) {
          if (w.ruleParameter.indicatorConfig.id !== this.contentItem.id) return false
        }
        if (Number(w.subscribeFlag) === 1) {
          currentElementSubscribeCount++
        }
        return true
      })
      elementWarningData.warningList = warningList
      elementWarningData.currentElementSubscribeCount = currentElementSubscribeCount
      return elementWarningData
    },
    closeDialog() {
      const stashedWarning = this.boardWarningSubscribeData?.warningList_stash?.find(w => w.webElementId === this.elementWarningData?.webElementId)
      if (stashedWarning) {
        // 如果能找到，说明订阅失败了
        this.boardWarningSubscribeData.updateWarningData(stashedWarning.webElementId, { type: 'clearStash', })
        this.callEmit({
          data: { ids: [stashedWarning.webElementId], useLazy: false },
          options: {
            getRequestData: () => {},
          },
        })
      }
      this.subscribeWarningDialogVisible = false
    },
    subscribeWarningDialogConfirmHandler() {
      let warningConditionFlagPercent = []
      let validateRequired = this.elementWarningData.warningList.every(w => {
        const ruleParameter = typeof w.ruleParameter === 'string' ? JSON.parse(w.ruleParameter) : w.ruleParameter
        if (Number(ruleParameter.compareMethod) === 4) {
          if (ruleParameter.dimensionSetting.some(ds => !ds.selectValues?.length)) return false
        }
        const refComponent = this.$refs[`ref_${ w.webWarningId }`][0]
        let inputConfigArray = refComponent.getLineConfig_1(w)
        if (refComponent.messageStepWarningComponentShow(w)) {
          inputConfigArray.push(...refComponent.getLineConfig_2(w))
        }
        inputConfigArray = inputConfigArray.filter(c => {
          if (['warningConditionFlag', 'warningConditionValue'].includes(c.key)) {
            return Number(w.emsMessageWarningFlag) === 1
          }
          return c.type === 'input'
        })
        Number(w.emsMessageWarningFlag) === 1 && Number(w.warningConditionFlag) === 1 && warningConditionFlagPercent.push(w.warningConditionValue)
        return inputConfigArray.every(c => {
          let dataObject = c.model || w
          return String(dataObject[c.key]) === '0' || Boolean(dataObject[c.key])
        })
      })
      if (!validateRequired) {
        this.$message.warning(this.$t('sdp.views.plsFillComplete'))
        return
      }
      if (warningConditionFlagPercent.some(v => {
        const intV = parseInt(v)
        if (String(intV) !== String(v)) return true
        if (intV > 100 || intV < 1) return true
        return false
      })) {
        this.$message.warning(this.$t('sdp.views.plsInZeroTohundred')) // 百分比请输入1-100的整数！
        return
      }

      this.subscribeWarningApi(() => {
        this.closeDialog()
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.warning-bell_chart{
  &.warning-bell_mobile{
    // margin-right: 15px;
    margin-right: 25px;
  }
  &.warning-bell_pc{
    margin-right: 25px;
  }
}
.icon{
  font-size: 12px;
}
.icon-sdp-yujing {
  color: var(--sdp-xxbt2);
  cursor: pointer;
}
.icon-sdp-yujingtianchonghongse{
  color: var(--sdp-qcgls);
  cursor: pointer;
}
.circle-style {
  width: 10px;
  height: 10px;
  border: none;
  border-radius: 12px;
  margin-right: 8px;
}
.warn-example-list {
  height: 20px;
  line-height: 20px;
  max-width: 184px;
  overflow: hidden;
  display: flex;
  align-items: center;
  .length-limit{
    max-width: 166px;
    padding-right: 8px;
    @include ellipsis;
    color: var(--sdp-xxbt2);
  }
}
.scrollbar /deep/{
  .el-scrollbar__wrap {
    margin-bottom: 0px !important;
    overflow-x: hidden;
  }
}
.content{
  height: 432px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
<style lang="scss">
.sdp-warn-popover {
    font-family: PingFang-SC-Medium;
    padding: 8px!important;
    min-width: 0px!important;
    max-width: 200px!important;
}
</style>
