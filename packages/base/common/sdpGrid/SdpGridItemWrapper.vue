<template>
  <div
    class="sdp-grid-item-wrapper"
    data-cy-gridLayoutItemWrapper
    @mouseenter="!shareDataParent && (shareData.isMouseenter = true)"
    @mouseleave="!shareDataParent && (shareData.isMouseenter = false)"
    @touchmove="handleTouchMove"
    v-is-mouseenter="shareData.isMouseenter"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  inject: {
    shareDataParent: {
      from: 'shareData',
      default: null,
    }
  },

  provide() {
    return {
      shareData: this.shareData
    }
  },

  directives: {
    isMouseenter: {
      update(el, { value }) {
        el.dataset.isMouseenter = value
      }
    }
  },

  data () {
    return {
      shareData: this.shareDataParent || { isMouseenter: false }
    }
  },

  methods: {
    handleTouchMove(e) {
      if (this.$el.classList.contains('is-force')) {
        e.preventDefault()
      }
    }
  }
}
</script>

<style scoped>
.sdp-grid-item-wrapper {
  height: 100%;
  position: relative;
  box-sizing: border-box;
}
</style>
