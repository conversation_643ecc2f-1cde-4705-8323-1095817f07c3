<script>
import { GridItem } from 'vue-grid-layout'
import SdpGridItemWrapper from './SdpGridItemWrapper.vue'

export default {
  functional: true,
  components: { SdpGridItemWrapper },
  render: function(createElement, context) {
    // console.log('context', context)
    context.props.dragIgnoreFrom += ', .sdp-grid-item-drag-ignore, .el-scrollbar__thumb'
    return createElement(
      GridItem,
      {
        ref: context.data.ref,
        key: context.data.key,
        props: context.props,
        attrs: {
          id: context.data.key
        },
        on: {
          move: (i, newX, newY) => {
            context.listeners.move && context.listeners.move(i, newX, newY)
          },
          resize: (i, newH, newW, newHPx, newWPx) => {
            context.listeners.resize && context.listeners.resize(i, newH, newW, newHPx, newWPx)
          },
          moved: (i, newX, newY) => {
            context.listeners.moved && context.listeners.moved(i, newX, newY)
          },
          resized: (i, newH, newW, newHPx, newWPx) => {
            context.listeners.resized && context.listeners.resized(i, newH, newW, newHPx, newWPx)
          }
        },
        nativeOn: {
          click: event => {
            context.listeners.click && context.listeners.click(event)
          },
          mousedown: event => {
            context.listeners.mousedown && context.listeners.mousedown(event)
          }
        }
      },
      [
        createElement(SdpGridItemWrapper, {
          style: context.data.style
        }, context.children)
      ]
    )
  }
}
</script>
<style lang="scss" scoped>
  .vue-grid-item {
    /deep/ .vue-resizable-handle {
      display: none;
      background-image: unset !important;
      &::after {
        position: absolute;
        content: ' ';
        width: 6px;
        height: 6px;
        border-right: 2px solid var(--sdp-is);
        border-bottom: 2px solid var(--sdp-is);
        right: 3px;
        bottom: 3px;
      }
    }

    &:hover /deep/ .vue-resizable-handle {
      display: block;
    }
  }
</style>
