import { getGlobalParameterList } from 'packages/base/board/displayPanel/utils/helpers/api'
import Vue from 'vue'
export default class TemplateServer {
  api: any
  constructor(api) {
    this.api = api
  }
  addDataTable(params) {
    return this.api.post('/bi/talbe/add', params)
  }

  updateDataTable(params) {
    return this.api.post('/bi/talbe/update', params)
  }

  tenantDataView(params) {
    if (!params) {
      return Promise.resolve({})
    }
    return this.api.post(`/admin/busiDataView/query/tenantDataView?tenantId=${params}`)
  }

  getTableConfigDataById(id, languageCode) {
    const onlyIndex = Vue.prototype?.$getSystemConfig?.('onlyIndex')
    if(onlyIndex) {
      const apiUrl = Vue.prototype?.$getSystemConfig?.('api.urls.detail')
      return this.api.post(apiUrl, {id: id})
    }
    const str = languageCode ? `/bi/talbe/get/table/${id}/${languageCode}` : `/bi/talbe/get/table/${id}`
    return this.api.get(str)
  }
  getGlobalParameterList(params) {
    return getGlobalParameterList(this.api, params)
  }

  // 记录日志
  operationalLog(params) {
    return this.api.post('/system/sysOperLog/recordOperLog', params)
  }
}
