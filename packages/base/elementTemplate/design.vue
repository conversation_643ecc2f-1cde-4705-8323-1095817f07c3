<template>
  <div class="sdp-element-template-design-container">
    <component
      :is="componentName"
      :ref="componentName"
      @eventBus="eventBus"
    ></component>
  </div>
</template>

<script>
import { gridTemplateDesign, chartTemplateDesign, cardTemplateDesign, customElementTemplateDesign } from './modules'
import eventBus from 'packages/assets/eventBus'
import commonMixin from './mixins/common.ts'
export default {
  mixins: [commonMixin],
  components: {
    gridTemplateDesign,
    chartTemplateDesign,
    cardTemplateDesign,
    customElementTemplateDesign,
  },
  data() {
    return {
      suffix: 'Design'
    }
  },
  methods: {
    eventBus,
    resetElement(params) {
      this.$refs[this.componentName]?.resetElement && this.$refs[this.componentName].resetElement(params)
    },
  },
}
</script>
<style lang="scss" scoped>
.sdp-element-template-design-container{
  height: 100%;
  width: 100%;
}
</style>
