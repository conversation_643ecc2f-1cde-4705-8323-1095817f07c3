import eventType from './modules/eventType'
import * as ELEMENT_TYPE from './modules/elementType'

export const EVENT_TYPE = eventType
export const ELEMENT_TYPE_CODE = ELEMENT_TYPE.ELEMENT_TYPE_CODE
// operateType: 新增add, 修改edit, 预览preview, 搜索元素 element
export enum OPERATE_TYPE {
  element = 'element',
  preview = 'preview',
  add = 'add',
  edit = 'edit'
}
// 表格1，图形2，卡片3
export enum EL_TYPE {
  table = 1,
  chart = 2,
  card = 3,
  custom = 4
}

export const FOLDER_ID = '无文件id'
