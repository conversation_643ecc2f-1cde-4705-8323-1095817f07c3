<template>
  <div class="sdp-element-template-preview-container">
    <component
      :is="componentName"
      :ref="componentName"
      :isPreview="isPreview"
      @close="closeHandler"
    ></component>
  </div>
</template>

<script>
import { gridTemplatePreview, chartTemplatePreview, cardTemplatePreview, customElementTemplatePreview } from './modules'
import { EVENT_TYPE } from 'packages/base/elementTemplate/constants'
import commonMixin from './mixins/common.ts'
export default {
  mixins: [commonMixin],
  components: {
    gridTemplatePreview,
    chartTemplatePreview,
    cardTemplatePreview,
    customElementTemplatePreview,
  },
  inject: ['sdpBus'],
  props: {
  },
  data() {
    return {
      suffix: 'Preview',
      isPreview: false,
    }
  },
  methods: {
    renderComponentCallback() {
      if (this.componentName !== 'gridTemplatePreview') {
        this.isPreview = true
      }
    },
    closeHandler() {
      this.isPreview = false
      this.sdpBus.$emit(EVENT_TYPE.CLOSE)
    }
  },
}
</script>
<style lang="scss" scoped>
.sdp-element-template-preview-container{
  width: 100%;
  height: 100%;
  &>*{
    width: 100%;
    height: 100%;
  }
}
</style>
