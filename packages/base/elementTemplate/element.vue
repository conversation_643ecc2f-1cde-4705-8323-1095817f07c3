<template>
  <div class="sdp-element-template-container-box">
    <component
      :is="componentName"
      ref="templateElement"
      @eventBus="eventBus"
    ></component>
  </div>
</template>

<script>
import { gridTemplateElement, chartTemplateElement, cardTemplateElement, customElementTemplateElement } from './modules'
import eventBus from 'packages/assets/eventBus'
import commonMixin from './mixins/common.ts'
export default {
  components: { gridTemplateElement, chartTemplateElement, cardTemplateElement, customElementTemplateElement },
  mixins: [commonMixin],
  data() {
    return {
      suffix: 'Element',
    }
  },
  created() {
    this.renderComponent()
  },
  methods: {
    eventBus,
  },
}
</script>
<style lang="scss" scoped>
.sdp-element-template-container-box{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  &>*{
    width: 100%;
    height: 100%;
  }
}
</style>
