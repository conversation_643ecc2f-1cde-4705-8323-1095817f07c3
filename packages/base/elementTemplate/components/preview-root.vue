<template>
  <el-dialog
    v-if="configs.options.viewType !== 'asElement'"
    :class="['is-fullscreen sdp-fullscreenDialog sdp-dialog', getCurrentThemeClass()]"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    :show-close="true"
    :modal="false"
    :customClass="customClass"
    :close-on-click-modal="false"
    @close="closeHandler"
  >
    <div class="sdp-template-preview-container" :style="elBgColorFormatter(element.themeMap)">
      <div class="placeholder-block" :style="containerPadding.chart" v-if="elementLoading">
        <loading :isLoading="true" />
      </div>
      <slot></slot>
    </div>
  </el-dialog>
  <div class="asElement-container" v-else>
    <slot></slot>
  </div>

</template>

<script>
import Loading from 'packages/base/common/loading/loading'
import { EVENT_TYPE } from 'packages/base/elementTemplate/constants'
import { BG_MAP_STRING, THEME_TYPE } from 'packages/assets/constant'
import { BOARD_ELEMENT_THEME } from 'packages/base/board/displayPanel/supernatant/boardElements/BoardElement'

export default {
  inject: ['configs', 'sdpBus', 'themeData', 'utils', 'getCurrentThemeClass'],
  provide() {
    return {
    }
  },
  components: {
    Loading,
  },
  props: {
    element: {
      type: Object,
      default: () => ({}),
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    customClass: {
      type: String,
      default: '',
    }
  },
  computed: {
    visible: {
      get() {
        return this.isPreview
      },
      set(val) {
        this.$emit('update:isPreview', val)
      },
    },
    containerPadding() {
      let chartKey = ''
      let gridKey = ''
      if (this.configs) {
        chartKey = 'isChartSet'
        gridKey = this.element.content?.alias === 've-grid-normal' ? 'gridInTemplatePreview' : 'isChartSet'
      }
      return {
        chart: chartKey ? { padding: this.chartPadding[chartKey] } : {},
        grid: gridKey ? { padding: this.chartPadding[gridKey] } : {},
      }
    },
  },
  data() {
    return {
      elementLoading: false,
      // 修改该值时需考虑简单表格，计算简单表格的大小时有用到下面的值，padding值的格式必须为'上 右 下 左'
      chartPadding: {
        isChartSet: '16px 0 0 16px',
        gridInChartSet: '16px 16px 16px 16px',
        // 看板元素界面
        noPadding: '0 0 0 0',
        isBoardElement: '16px 0 10px 16px',
        gridInBoardElement: '16px 16px 10px 16px',
        gridInTemplatePreview: '16px 16px 16px 16px',
        isInAdvanceContainer: '8px 0 0 8px',
        gridInAdvanceContainer: '8px 8px 0 8px',
        isThemeFullScreen: '6px 0px 10px 10px',
        gridInThemeFullScreen: '6px 10px 10px 10px',
      },
    }
  },
  methods: {
    setLoading(val) {
      this.elementLoading = val
    },
    elBgColorFormatter(themeMap) { // 背景色设置
      const appThemeType = this.themeData.appThemeType || this.themeData.themeType
      let themeType = !this.utils.isMobile && appThemeType === THEME_TYPE.deepBlue ? THEME_TYPE.darkBlue : appThemeType
      const bgStr = this.utils.isMobile ? BG_MAP_STRING['mobile'] : BG_MAP_STRING['pc']
      const bgObj = { backgroundColor: themeMap?.[themeType]?.[bgStr] || BOARD_ELEMENT_THEME[themeType][bgStr] }
      return bgObj
    },
    closeHandler() {
      this.$emit(EVENT_TYPE.CLOSE)
    },
  },
}
</script>

<style lang="scss" scoped>
.sdp-template-preview-container{
  height: 100%;
  width: 100%;
  position: relative;
  .placeholder-block{
    position: relative;
    height: 100%;
    width: 100%;
  }
}
.asElement-container{
  background-color: var(--sdp-tb-bj);
  width: 100%;
  height: 100%;
}
</style>
