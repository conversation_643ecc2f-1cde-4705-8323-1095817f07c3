<template>
  <preview-root
    ref="previewRoot"
    :element="element"
    :custom-class="configs.options.onlyIndex && configs.options.operateType === 'preview' ? 'isNoPreviewRoot' : ''"
    :isPreview="isPreview"
    @close="closeHandler"
  >
    <template v-if="showElementChart">
      <element-chart
        ref="elementChart"
        :isChartSet="false"
        :element="element"
        terminal="PC"
        previewType="preview-template"
        @refreshEl="refreshEl"
      ></element-chart>
    </template>
  </preview-root>
</template>

<script>
import { cancelToken } from 'packages/assets/utils/cancelToken'
import ElementChart from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart'
import PreviewRoot from 'packages/base/elementTemplate/components/preview-root'
import bridge from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/bridge'
import mediator from 'packages/base/board/displayPanel/utils/mediator'
import requestHelper from 'packages/base/board/displayPanel/utils/helpers/requestHelper'
import { EVENT_TYPE } from 'packages/base/elementTemplate/constants'
import { setThemeConfig } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/theme'
import { updateReportLogObj } from 'packages/assets/utils/reportLog'
import { checkLangElement, getPreviewContentList } from '../../../board/displayPanel/boardLanguage'

export default {
  inject: ['configs', 'TemplateServer', 'sdpBus', 'utils', 'commonData'],
  components: {
    ElementChart,
    PreviewRoot,
  },
  props: {
    enlargeEl: {
      type: Object,
      default: () => null,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    // 监控图形设置看板的打开，需要刷新数据
    isPreview: {
      handler(val) {
        this.$nextTick(() => {
          val && this.renderChartComponent()
        })
      },
      immediate: true,
    },
  },
  data() {
    return {
      api: this.configs.api,
      element: {},
      boardData: {},
      cancelApi: [],
      showElementChart: false,
    }
  },
  created() {},
  methods: {
    async renderChartComponent() {
      this.showElementChart = false
      if (this.configs.options.operateType === 'preview') {
        this.$refs.previewRoot.setLoading(true)
        const response = await this.TemplateServer.getTableConfigDataById(
          this.configs.element.id
        )
        const isOnlyIndex = this.$getSystemConfig?.('onlyIndex')
        const res = isOnlyIndex ? response.data : response
        const contentObj = JSON.parse(res.content)
        if (isOnlyIndex) {
          this.newBoardContent = {
            metaDashboardElementLanList: contentObj.tableControlsElementLanList || [],
            metaDashboardLanguageList: []
          }
          delete contentObj.tableControlsElementLanList
          delete contentObj.tableControlsLanguageList
        }
        res && (this.element = contentObj)
        this.$refs.previewRoot.setLoading(false)

        this.showElementChart = true
        this.$nextTick(() => {
          this.getElementData()
        })
      } else {
        const element = this.$_deepClone(this.enlargeEl)
        setThemeConfig(element.content.chartUserConfig, this.configs.themeType)
        this.element = element
        if (this.$getSystemConfig?.('onlyIndex')) {
          this.newBoardContent = this?.$parent?.$refs?.languageDialog?.saveEditLanguage() || {
            metaDashboardElementLanList: [],
            metaDashboardLanguageList: []
          }
        }
        this.showElementChart = true
        this.$nextTick(() => {
          this.$refs.elementChart.setHasChartData(true)
        })
      }
    },
    async getElementData() {
      const content = this.element.content
      if (!content.chartUserConfig.pagination.rankOf) {
        content.drillSettings.pageInfo.pageSize = (this.isChartSet || this.configs.options.operateType !== 'preview') ? 100 : 9999
      } else {
        content.drillSettings.pageInfo.pageSize = content.chartUserConfig.pagination.pageSize
      }
      if (this.element.content.alias === 've-grid-normal') {
        this.$refs.elementChart.setHasChartData(true)
        this.$refs.elementChart.createTable({ preventRequest: false, from: 'getElementData', hasChartData: true, })
        return
      }
      const requestData = this.requestAdapter()
      const reqElementData = mediator.collectsAdapter(requestData, {})
      const requestParams = reqElementData[0]
      requestParams.tenantId = this.configs.options.tenantId
      requestParams.reportLog = updateReportLogObj({
        id: this.element.id,
        name: this.element.elName,
        type: 'chart',
        langCode: this.configs.options.langCode,
      })

      await this.request(requestParams)
    },
    requestAdapter() {
      const data = {}
      const requestData = this.$refs.elementChart.requestAdapter({
        type: 'chart',
      })
      const elementId = this.element.id
      if (requestData) {
        const { requestKey, requestParams } = requestData
        requestParams._containerId = ''
        data[requestKey] = data[requestKey] || {
          ids: new Set(),
          datas: [],
        }
        data[requestKey].ids.add(elementId)
        data[requestKey].datas.push(requestParams)
      } else {
        setTimeout(() => {
          this.$refs.elementChart.setElementLoadState(false)
        }, 1000)
      }
      return data
    },
    async request(requestParams) {
      const { id } = mediator.distributeAdapter(requestParams) || {}
      await requestHelper.requestEl
        .call(this, cancelToken.call(this), requestParams)
        .then((res) => {
          let _res = res
          if (this.$getSystemConfig?.('onlyIndex')) {
            _res = res.data
          }
          const response = mediator.distributeAdapter(_res)
          const params =
            response.response.length === 1
              ? response.response[0]
              : response.response
          this.$refs.elementChart.responseAdapter(params)

          const arr = getPreviewContentList.call(this, this.configs.options.langCode, this.newBoardContent)
          checkLangElement[this.element.type].call(this, { el: this.element, arr, contentKey: 'key' })
        })
        .catch((err) => {
          console.log(
            'async request(requestParams) {-------------------------------------------',
            err
          )
          this.$refs.elementChart?.setElementLoadState(false)
          this.$refs.elementChart?.setHasChartData(false)
          return false
        })
    },
    refreshEl(data) {
      if (data.element) {
        this.element = data.element
      }
      this.$nextTick(() => {
        if (!data.preventTemplateRequest) {
          this.getElementData(data.element)
        } else {
          !this.showElementChart && this.renderChartComponent()
        }
      })
    },
    closeHandler() {
      this.showElementChart = false
      if (this.configs.options.operateType === 'preview') {
        this.sdpBus.$emit(EVENT_TYPE.BACK)
      } else {
        this.$emit(EVENT_TYPE.CLOSE)
      }
    },
  },
}
</script>

<style lang="scss">
.isNoPreviewRoot {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0 !important;
    height: 100% !important;
  }
}
</style>
