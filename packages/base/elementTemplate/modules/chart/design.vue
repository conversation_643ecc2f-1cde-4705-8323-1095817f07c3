<template>
  <div>
    <loading :isLoading="elementLoading" />
    <chartDesign
      v-if="componentReset"
      v-model="componentShow"
      :elId="element.id"
      ref="elementDesign"
      @eventBus="eventBus"
      @preview="previewHandler"
      @on-back="backHandler"
      @on-save="saveElement"
    />
    <chartPreview :isPreview="showChartPreview" :enlargeEl="enlargeEl" @close="closeHandler"></chartPreview>
    <language-dialog
      ref="languageDialog"
      :newBoardContent="newBoardContent"
    />
  </div>
</template>

<script>
import { THEME_TYPE_KEY, THEME_TYPE } from 'packages/assets/constant'
import { getChartsList } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/charts'
import { ELEMENTS_TAG, TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import { generateElNames } from 'packages/base/board/displayPanel/supernatant/utils/supernatantHelper'
import { BoardElement, BOARD_ELEMENT_THEME } from 'packages/base/board/displayPanel/supernatant/boardElements/BoardElement'
import chartDesign from 'packages/base/board/displayPanel/supernatant/chartSet/index'
import { EVENT_TYPE, FOLDER_ID } from 'packages/base/elementTemplate/constants'
import { OPERATION_LOG } from 'packages/base/grid/helpers/constants/index'
import chartPreview from './preview'
import Loading from 'packages/base/common/loading/loading'
import { setThemeChartUserConfig } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/theme'
import languageDialog from '../../components/languageDialog.vue'
import compatibilitiChartData from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/compatibilitiChart'
import { operationalLog } from '../../../board/displayPanel/components/api'
import eventBus from 'packages/assets/eventBus'
import { filterElementLans } from 'packages/base/board/displayPanel/utils'
import {checkLangElement, getPreviewContentList} from "../../../board/displayPanel/boardLanguage";
import { MicroApp } from '../../../../../src/pages/elementTemplate/utils.ts'

export default {
  name: 'chartTemplateDesign',
  inject: ['configs', 'sdpBus', 'TemplateServer', 'utils', 'getCurrentThemeClass', 'commonData'],
  provide() {
    return {
    }
  },
  components: {
    chartDesign,
    chartPreview,
    Loading,
    languageDialog
  },
  props: {
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    }
    // configData() {
    //   const { api, element, options } = this.configs
    //   return {
    //     api,
    //     operateType: options.operateType,
    //     operateId: options.operateType === 'add' ? element.folderId : element.id,
    //     langCode: options.langCode,
    //     tenantId: options.tenantId,
    //     themeType: THEME_TYPE[THEME_TYPE_KEY[options.themeCode]],
    //   }
    // },
  },
  data() {
    return {
      componentShow: false,
      componentReset: true,
      showChartPreview: false,
      element: {},
      enlargeEl: {},
      elementBefore: {},
      viewLevelData: {
        viewLevelList: [],
        dataViewType: true,
      },
      elementLoading: false,
      oldDataSetId: '',
      languageDialogVisible: false,
      boardTreeData: [],
      languageFrom: '',
      newBoardContent: {
        metaDashboardLanguageList: [],
        metaDashboardElementLanList: [],
      }
    }
  },
  created() {
    if (!this.onlyIndex) {
      this.getViewLevelData()
    }
    const { options, element } = this.configs

    this.elementLoading = true

    if (options.operateType === 'add') {
      const element = this.addEl()
      // 其他页面跳转，可能会携带指标ID参数，在这里做初始化
      const initDataIndicatorId = this.$getSystemConfig?.('initData.indicatorId')
      if (initDataIndicatorId) {
        element.content.drillSettings.dataSetId = initDataIndicatorId
        element.content.drillSettings.indexFlagIds = [initDataIndicatorId]
      }
      this.setChartSet(element)
    } else if (options.operateType === 'edit') {
      this.TemplateServer.getTableConfigDataById(element.id).then(res => {
          res = this.onlyIndex ? res.data : res
          this.sdpBus.$emit(EVENT_TYPE.CHANNEL, { channel: res.channel || '1' })
          this.elementBefore.name = res.name // roc todo: 整合一个操作日志对象
          const contentObj = JSON.parse(res.content)
          if (this.onlyIndex) {
            res.tableControlsElementLanList = contentObj.tableControlsElementLanList || []
            res.tableControlsLanguageList = contentObj.tableControlsLanguageList || []
            delete contentObj.tableControlsElementLanList
            delete contentObj.tableControlsLanguageList
          }
          res && this.initElement(contentObj, res)
        }).catch(error => {
          console.error(error.message)
          this.elementLoading = false
        })
    }
  },
  methods: {
    eventBus,
    openLanguage(data) {
      this.$refs.languageDialog.openLanguage(data)
    },
    saveEditLanguage() {
      this.newBoardContent = this.$refs.languageDialog.saveEditLanguage()
    },
    getLanObjList() {
      const metaDashboardElementLanList = this.newBoardContent.metaDashboardElementLanList

      const tableControlsElementLanList = metaDashboardElementLanList
      const tableControlsLanguageList = filterElementLans(metaDashboardElementLanList, this.element.type, this.element.id)

      return {
        tableControlsElementLanList,
        tableControlsLanguageList
      }
    },
    initElement(content, res) {
      content.elName = res.name
      if (this.onlyIndex) {
        // 副本
        content.analysisType = res?.analysisType || undefined 
      }
      this.newBoardContent.metaDashboardElementLanList = res.tableControlsElementLanList || []
      this.setChartSet(content)
    },
    resetElement() {
      this.elementLoading = true
      this.componentReset = false

      const element = this.addEl({ needSetTheme: true, })
      setTimeout(() => {
        this.componentReset = true
        this.setChartSet(element, true)
      })
    },
    setChartSet(element, needSetTheme) {
      this.element = element
      this.componentShow = true

      this.$nextTick(() => {
        this.$refs.elementDesign.setData(this.element)
        this.elementLoading = false
        if (needSetTheme) {
          this.$refs.elementDesign.drillSettings.dataSetId = this.oldDataSetId
          this.$refs.elementDesign.getSelections(this.oldDataSetId)
          this.oldDataSetId = ''
        }
      })
    },
    async saveElement(element, vm) {
      const { element: configElement, options } = this.configs
      const { content, elName } = element
      const { drillSettings = {}, chartUserConfig, chioceTab = [] } = content
      let remark = ''
      if (chioceTab?.length > 1) {
        remark = chioceTab[0].saveObj.chartUserConfig.chartDescription
      } else {
        remark = chartUserConfig.chartDescription
        const fn = vm && vm.mapSchemeSwitch
        fn && fn(0, element.content)
      }

      const lanMap = this.getLanObjList()
      let params = {}
      try {
        if (this.onlyIndex) {
          // 多语言字段暂时写死
          const nameEn = Array.isArray(lanMap.tableControlsLanguageList)
            ? lanMap.tableControlsLanguageList.find(t => t.type === 'name' && t.languageCode === 'en')?.value || ''
            : ''
          const nameZh = Array.isArray(lanMap.tableControlsLanguageList)
            ? lanMap.tableControlsLanguageList.find(t => t.type === 'name' && t.languageCode === 'zh')?.value || ''
            : ''
          Object.assign(element, {
            ...lanMap
          })
          params = {
            type: options.operateType,
            content: JSON.stringify(element),
            classifyId: configElement.folderId,
            remark: element.remark || '',
            indexIds: drillSettings.indexFlagIds,
            name: elName,
            chartType: chartUserConfig.chartAlias || '',
            id: configElement.id,
            nameEn: nameEn,
            nameZh: nameZh,
            // elementType: '2',
          }
          // this.microAppClass.dispatch(params)
          this.sdpBus.$emit(EVENT_TYPE.SAVE, params)
        } else {
          params = {
            contentJson: JSON.stringify(element),
            datasetIds: JSON.stringify([drillSettings.dataSetId]),
            elementType: '2',
            folderId: configElement.folderId,
            name: elName,
            remark,
            channel: this.utils.isMobile ? '2' : '1',
            spaceType: '1',
            id: configElement.id,
            ...lanMap
          }
          if (configElement.folderId === FOLDER_ID) {
            this.sdpBus.$emit(EVENT_TYPE.SAVE, params)
          } else {
            const res = options.operateType === 'add'
            ? await this.TemplateServer.addDataTable(params)
            : await this.TemplateServer.updateDataTable(params)
            this.saveSuccess(res)
          }
        }
      } catch (error) {
        console.error(error.message)
      }
    },
    saveSuccess(res) {
      const { id, name } = res
      const { options } = this.configs
      this.$message({
        message: options.operateType === 'add'
          ? this.$t('sdp.message.updateSuccessProp', { prop: this.$t('sdp.views.specChart') })
          : this.$t('sdp.message.saveSuccessProp', { prop: this.$t('sdp.views.specChart') }),
        type: 'success',
      })

      const params = Object.assign({}, OPERATION_LOG, {
        objectId: id,
        operateType: options.operateType === 'add' ? '1' : '2',
        operatePageId: id,
        superNodeName: '',
        objectName: name,
        menuI18Key: 'OMSCD01BBSJ02BBSJ03BGSJ',
        operateContent: JSON.stringify([{
          name: 'name',
          before: this.elementBefore.name,
          now: name
        }])
      })
      this.TemplateServer.operationalLog(params)

      this.sdpBus.$emit(EVENT_TYPE.SAVE, res)
    },
    async getViewLevelData() {
      const { viewLevelList = [], dataViewType = '' } = await this.TemplateServer.tenantDataView(this.configs.options.tenantId)
      this.viewLevelData = {
        viewLevelList,
        // 平台视角类型('0' 单视角，'1' 多视角, '' 没有视角)(字符串)
        dataViewType: dataViewType !== '0',
      }
    },
    addEl(params = {}) {
      const chartlist = getChartsList()
      const chartContent = chartlist[0]
      const element = this.addElement(TYPE_ELEMENT.CHART, { content: chartContent }, params)
      return element
    },
    addElement(elType, options = {}, params = {}) {
      const defaultOptions = {
        isMobile: this.utils.isMobile || false,
        isScreen: this.utils.isScreen || false,
      }
      if (defaultOptions.isMobile) {
        options.style = { boxShadow: '0 0 0 0 rgba(0,0,0,0.10)' }
      }
      // 移动端和pc端默认不需要border
      options.style = { border: 'none' }
      Object.assign(options, defaultOptions)
      const element = new BoardElement({
        type: elType,
        ...options,
      })
      element.elName = (this.configs.options.operateType === 'add' && this.configs.element.elName) || ''
      if (params.needSetTheme) {
        this.oldDataSetId = this.$refs?.elementDesign?.drillSettings?.dataSetId || ''
        setThemeChartUserConfig.call(this, element.content.chartUserConfig, this.configs.themeType, element.content)
        compatibilitiChartData.call(this, { content: element.content })
      }
      return element
    },
    backHandler() {
      this.$sdp_eng_confirm(this.onlyIndex ? this.$t('sdp.message.selfServiceAnalysisTipsConfirmTitle') : `${this.$t('sdp.message.chartTipsConfirmTitle')}`, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
        // customClass: 'sdp-grid-design',
        confirmButtonText: this.$t('sdp.message.confirmButton'),
        cancelButtonText: this.$t('sdp.message.cancelButton'),
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
        type: 'warning',
        closeOnClickModal: false,
        beforeClose: (action, instance, done) => { // bug 13203 表格编辑已经关闭，但是此弹窗还是会存在 延迟半秒才消失，和看板退出做同样处理
          instance.$el.style.zIndex = -1
          done()
        },
      }).then(() => {
        this.sdpBus.$emit(EVENT_TYPE.BACK)
      }).catch(() => {})
    },
    previewHandler(element, previewCode = '') {
      const el = element
      if (previewCode) {
        this.saveEditLanguage()
        const newBoardContent = this.newBoardContent
        const arr = getPreviewContentList.call(this, previewCode, newBoardContent)
        checkLangElement[el.type].call(this, { el: el, arr, contentKey: 'key' })
      }
      this.changeCommonPreview(true)

      this.enlargeEl = el
      this.showChartPreview = true
    },
    closeHandler() {
      this.showChartPreview = false
      this.changeCommonPreview()
    },
    changeCommonPreview(flag = false) {
      if (this.configs && this.configs.type === 'template') {
        this.commonData.isPreview = flag
      }
    },
    // operationalLogHandler(params) {
    //   this.configs.api.post('/system/sysOperLog/recordOperLog', assemblyData(params))
    // }
  },
}
</script>
