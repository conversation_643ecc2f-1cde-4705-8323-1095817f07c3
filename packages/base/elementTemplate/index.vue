<template>
  <div v-loading="loading" element-loading-spinner="sdp-loading-gif" class="sdp-element-template-container">
    <component
      :is="componentName"
      :ref="componentName"
    ></component>
  </div>
</template>

<script>
import templatePreview from './preview'
import templateDesign from './design'
import templateElement from './element'
import { THEME_TYPE_KEY, THEME_TYPE, DATA_THEME, LANGUAGE_LIST } from 'packages/assets/constant'
import { EVENT_TYPE, OPERATE_TYPE } from './constants'
import TemplateServer from './api'
import commonMixin from 'packages/components/mixins/commonMixin'
import { removeTokenTimer, setTokenTimer } from 'packages/assets/utils/refreshToken'
import { Communication } from 'packages/base/board/displayPanel/supernatant/boardElements/elementCustomer/utils'
import { EVENT_BUS } from 'packages/base/board/displayPanel/constants'
export default {
  name: 'ElementTemplate',
  components: {
    templatePreview,
    templateDesign,
    templateElement,
  },
  mixins: [commonMixin],
  provide () {
    const { api, element, options, themeType } = this
    const realThemeType = THEME_TYPE[THEME_TYPE_KEY[options.themeCode]] || THEME_TYPE.default
    return {
      TemplateServer: this.TemplateServer,
      configs: {
        api,
        element,
        options,
        themeType,
        type: 'template',
      },
      utils: this.utils,
      commonData: {
        isSubscribe: false,
        isPreview: false,
        isRemind: false,
        isMobileApp: false,
        isMobile: options.isMobile,
        getViewLevelData: () => this.viewLevelData,
        boardSlectLang: () => {
          return options.langCode
        },
      },
      sdpLangcode: options.langCode,
      boardData: {
        paramsPanelList: [],
      },
      langCode: options.langCode,
      themeData: {
        enableTheme: true,
        screenMode: false,
        themeType,
        appThemeType: realThemeType,
      },
      tenantData: this.tenantData,
      communication: this.communication,
      fullscreenData: {
        enlargeVisible: true,
      },
      setElementLoading: (value) => {
        this.loading = value
      }
    }
  },
  props: {
    api: {
      type: [Object, Function],
      required: true,
    },
    element: {
      type: Object,
      required: true,
      validator: function(value) {
        // type: 表格1，图形2，卡片3
        const { id, elType, folderId } = value
        if ((!id && !folderId) || !elType) return false
        return true
      },
    },
    options: {
      type: Object,
      required: true,
      validator: function(value) {
        // operateType: 新增add, 修改edit, 预览preview
        const { operateType, themeCode, langCode, tenantId } = value
        if (!operateType || !langCode) return false
        return true
      }
    },
  },
  computed: {
    themeType() {
      const { options } = this
      const realThemeType = THEME_TYPE[THEME_TYPE_KEY[options.themeCode]] || THEME_TYPE.default
      return realThemeType
    },
  },
  data() {
    const { api, element, options } = this
    return {
      communication: new Communication(this.receiveIframePassData),
      TemplateServer: new TemplateServer(this.api),
      loading: false,
      componentName: '',
      utils: {
        isMobile: options.isMobile,
        api,
        tenantId: options.tenantId,
        isReference: options.isReference,
        isEnterprise: options.isEnterprise,
        languageList: [],
        themeParameters: {
          themeType: THEME_TYPE[THEME_TYPE_KEY[options.themeCode]] || THEME_TYPE.default,
          isOpen: true,
        },
        env: this.env
      },
      tenantData: {
        globalParameterList: [],
      },
    }
  },
  beforeDestroy() {
    this.options.onlyIndex || removeTokenTimer()
    this.communication.cancal()
  },
  async created() {
    this.options.onlyIndex || setTokenTimer.call(this, this.api)
    this.setGlobalTheme()
    if (!this.options.onlyIndex) {
      this.getParams()
      await this.getLanguageList()
    } else {
      this.utils.languageList = this?.$getSystemConfig?.('languageList') || []
    }
    // 自定义色系配置
    const customColorSchemeHidden = this.$getFeatureConfig?.('customColorScheme.hidden')
    if (!customColorSchemeHidden) {
      await this.getStandardChartSchemeList()
    }
    this.renderComponent()
    this.addListener()
  },
  methods: {
    // 接收数据
    receiveIframePassData(params) {
      this.sdpBus.$emit(EVENT_BUS.CUSTOMR_DISPATACH_DATA, params)
    },
    addListener() {
      this.sdpBus.$on(EVENT_TYPE.BACK, this.backHandler)
      this.sdpBus.$on(EVENT_TYPE.SAVE, this.saveHandler)
      this.sdpBus.$on(EVENT_TYPE.CLOSE, this.closeHandler)
      this.sdpBus.$on(EVENT_TYPE.CHANNEL, this.channelHandler)
    },
    renderComponent() {
      const { operateType } = this.options
      if (operateType === OPERATE_TYPE.element) {
        this.loading = true
        this.componentName = 'templateElement'
      } else if (operateType === OPERATE_TYPE.preview) {
        this.componentName = 'templatePreview'
      } else {
        this.componentName = 'templateDesign'
      }
    },
    setGlobalTheme() {
      document.body.setAttribute(DATA_THEME, this.themeType)
    },
    // 获取各种数据
    getParams() {
      this.getGlobalParameter()
    },
    getGlobalParameter() {
      this.TemplateServer.getGlobalParameterList({ tenantId: this.utils.tenantId })// todo 此处暂时是'1'日期类型, 之后需求会增加字符串树治等其他类型, 需要修改
        .then(res => {
          this.tenantData.globalParameterList = res
        })
        .catch(err => {
          this.tenantData.globalParameterList = []
          console.log('GlobalParameter Error: ', err)
        })
    },
    backHandler() {
      this.$emit('on-back')
    },
    closeHandler() {
      this.$emit('on-back')
    },
    channelHandler(params) {
      this.utils.isMobile = (params.channel === '2')

      if (!params.resetElement) return
      this.$refs[this.componentName]?.resetElement && this.$refs[this.componentName].resetElement(params)
    },
    saveHandler(res) {
      this.$emit('update', res)
    },
    async getLanguageList() {
      await this.utils.api
        .get('/admin/tenantlanguage/getLanguageListByUserTenantId')
        .then(arr => {
          this.$set(this.utils, 'languageList', arr)
        })
        .catch(e => {
          console.error(e, '多语言列表获取失败')
        })
    },
    // 获取图形标准色系列表
    async getStandardChartSchemeList() {
      const res1 = await this.utils.api
        .post('/bi/colourScheme/list')
      const list = (res1 || []).map(item => {
          return {
            ...item,
            colors: item.values
          }
        })
      this.sdpBus.standardChartSchemeList = list
      const res2 = await this.utils.api
        .post(`/bi/colourScheme/getThemeColour`)
      const result = {
          [THEME_TYPE.default]: { colorScheme: '' },
          [THEME_TYPE.darkBlue]: { colorScheme: '' },
          [THEME_TYPE.deepBlue]: { colorScheme: '' }
        }
        if (Array.isArray(res2)) {
          res2.forEach(item => {
            if (item.theme && item.cid) {
              result[item.theme] = { colorScheme: item.cid }
            }
          })
        }
        this.sdpBus.busiChartColorScheme = result
    },
  },
}
</script>

<style lang="scss" scoped>
.sdp-element-template-container{
  height: 100%;
  width: 100%;
  background-color: var(--sdp-bg-tcbjs) !important;
  border-radius: 8px;
  border: 1px solid var(--sdp-el-b);
}
</style>
