// import { selectedTenantId } from 'assets/methodParams'

import { updateReportLogObj } from 'packages/assets/utils/reportLog'
import { formatAlertData, formatExcludeData, formatMarkData, lgeTypeFormatter } from './utils'
import { CALC_FNS, DATA_FILTER, FORMAT } from '../../grid/helpers/constants'
import { globalTools } from 'packages/assets/utils/globalTools'
import { SUB_AGG_TYPE, CELL_TYPE } from '../common/constant'
import { isCurrencyField_fnForGrid } from '../../grid/utils/cellTool'
import { previewMergeApi } from '../../board/displayPanel/utils/helpers/api'
import { TYPE_VALUE, RULE_TYPE } from '../../grid/components/features-bar/dataBar/constant'
import { DATE_VALUE } from '../../grid/components/attributes-panel/dateDimensionSwitch/constant'
import has = Reflect.has;
import {generateCoordinateForCell} from "../../grid/helpers/utils/gridTable";

const STATISTIC_TYPE = [CELL_TYPE.row_statistic, CELL_TYPE.column_statistic]
interface CommonInterface {
  [key: string]: any;
}
interface CommonArray {
  [index: number]: any;
}
// const STATISTIC_TYPE = ['row_statistic', 'column_statistic']
const CURRENTPAGE = 1
const PAGESIZE = 40
// 全部导出时，限制请求数量
const PRINT_LIMIT_NUN = {
  tree: 200,
  common: 2000,
}
// 数据条参数类型对应值
const DataBarTypeDIC = {
  [TYPE_VALUE.AUTOMATIC]: '0',
  [TYPE_VALUE.NUMBER]: '1',
  [TYPE_VALUE.PERCENTAGE]: '2',
}
// 适配排序对象
const adapterOrder = (arr) => {
  arr = tableOrder(arr)
  const cellOrder = []
  for (const key in arr) {
    const formatterArr: [] = arr[key].map((v, i) => ({ ...v, orderByIndex: i }))
    cellOrder.push(...formatterArr)
  }
  if (cellOrder.length) {
    // 拆分数据集排序和单元格排序
    return cellOrder
  } else {
    return []
  }
}

const tableOrder = (arr) => {
  // 兼容性代码，9月24号后删除
  if (!Array.isArray(arr)) {
    return arr
  } else if (!arr.length) {
    return {}
  } else {
    const orderFields = {}
    // arr就是orderFields
    arr.filter(item => item.value.toString() === '2').forEach(item => {
      const { dataSetId, columnName, cell } = item
      orderFields[dataSetId] = [{ columnName, ...cell }]
    })
    return orderFields
  }
}
// 表格排序合并项
const adapterOrderFieldsMerge = (fieldsMerge = {}) => {
  const resultArr = []
  for (const key in fieldsMerge) {
    const item = fieldsMerge[key]
    resultArr.push({ ...item, datasetId: key })
  }
  return resultArr
}

const filtersFormatter = (filters = [], caculationMethodType = DATA_FILTER.defaultCaculationMethod, disabledCalendarFnFilter = false) => {
  // 数据过滤补充日期函数
  const referToCalendar = caculationMethodType === DATA_FILTER.targetCaculationMethod
  return filters.map(v => ({
    ...v,
    referToCalendar,
    disabledCalendarFnFilter: referToCalendar ? disabledCalendarFnFilter : undefined,
  }))
}

export const getVariableData = ({
  tableData = [[]], options = {}, gridInfo = {}, drillDimensions,
  isAdapter = false, getOnlyParams = false, warningMap, excludeObj, simpleTable = false,
  commonData, utils, tableConfig, language = undefined, subscribeWarningData = {}, vm,
  api,
}:CommonInterface) => {
  const {
    orderFields, lineNumber, maxCountNum,
    groupSearch, pageSize, currentPage, filters = [],
    rowFilterMap = {}, caculationMethodType, disabledCalendarFnFilter, orderFieldsMerge, dataSetJoinsConfig = {},
    tableExtend, tableExtendMulti, extendLimit,
    hierarchyLimit = false, hierarchyLimitInfo = [],
    polymericFilters = [],
  } = options
  const cellOrder = adapterOrder(orderFields)
  // const { dataSetOrder, cellOrder } = adapterOrder(orderFields)
  let headerOrder = false
  const previewParams:CommonInterface = Object.assign({}, {
    currentPage: CURRENTPAGE,
    pageSize: PAGESIZE,
    params: {},
    simpleTable,
  }, { pageSize, currentPage, filters: filtersFormatter(filters, caculationMethodType, disabledCalendarFnFilter) }, lineNumber !== '1' ? { maxCountNum } : {})
  const cells = []
  const obj = {
    'type': '',
    'dataSetId': '',
    'columnName': '',
    'columnType': 'string',
    'aggType': '',
    'filterType': '',
    'values': [], // 搜索内容
    'order': '',
    'extend': 0,
  }

  const table = filterHideCell(tableData)
  const cellKeyNames = {} // 防止单元格重复发送
  console.log(tableConfig)
  const { row: metricHideRow = [] } = tableConfig?.metricHide || {}

  // 合并单元格
  const mergeCellList = table.reduce((list, row) => {
    list = list.concat(row.filter(cell => cell.isMergeCell))
    return list
  }, [])
  // 跨行维度合并单元格
  const dataMergeCellList = mergeCellList.filter(e => e.content.dataSetId && e.start[0] !== e.end[0])

  table.forEach((row, rowIndex) => {
    if (metricHideRow.includes(rowIndex)) {
      return false
    }
    row.forEach((cell, colIndex) => {
      if (cell.i === '1420') {
      }
      if (!metricFilter(cell, tableConfig.metricHide, 'col', colIndex)) {
        return false
      }
      // 处理mergeCell时，需要拆分
      const cellList = Array.isArray(cell.mergeColumnList) ? cell.mergeColumnList : [cell]
      cellList.forEach(item => {
        // version: 05; bug: 10585;
        // 处理表格设计 grayed 字段异常的问题
        // 跑完脚本后可将其删除( 暂未编写升级脚本 )
        (() => {
          const isCurrencyField = isCurrencyField_fnForGrid(item.content)
          const isStringType = item.content.columnType === 'string' && item.content.aggType !== CALC_FNS.COUNT
          const isRanking = Boolean(item.content.rankType)
          const isGrayed = globalTools.$_getProp(item.content, 'cellFormat.value.grayed', false)
          !(isCurrencyField || isStringType || isRanking) &&
          isGrayed &&
          delete item.content.cellFormat.value.grayed
        })()

        if (cellKeyNames[item.i]) {
          return void '防止重复单元格'
        }

        // 自定义计算，不能过滤隐藏的单元格
        if (item.type === 'variable') {
          cellKeyNames[item.i] = true
          // if (item.type === 'variable' && !item.softHide) {
          const content = item.content
          const { subAggType, customFnData, timeDimensionSplittingRule } = content
          const columnName = subAggType === SUB_AGG_TYPE.CUSTOMER_EXPRESSION
            ? customFnData.expression
            : (content.columnName || content.text)
            // : (content.columnName || content.alias || content.text) // 不再使用 alias 字段
          let order = content.order

          // 只取变量单元格
          const itemData:CommonInterface = {
            rowIndex,
            'filters': filtersFormatter(content.filters, caculationMethodType),
            'filterType': '', // 模糊搜索
            'coordinate': content.coordinate,
            'type': content.isExpression
              ? content.subAggType
                ? content.subAggType
                : 'expression'
              : item.type,
            'dataSetId': content.dataSetId,
            'columnName': columnName,
            'keyName': content.coordinate || content.keyName || '',
            'uuid': content.keyName,
            'columnType': cellContentPropsHelper.getColumnType(content),
            'aggType': content.aggType,
            'order': order,
            'extend': !content.extend ? 0 : (content.extend - 0),
            // 'alias': content.alias || '', // 后台确定不再传该子弹
            'rankOrder': content.rankOrder || '',
          }
          // 扩展单元格
          if (tableExtendMulti) {
            tableExtend && item.extendUnit && (itemData.areaId = '0')
          } else {
            tableExtend && (itemData.areaId = '0')
          }

          // 9489 聚合过滤
          if (polymericFilters && polymericFilters.length) {
            // const targetPolymericList = polymericFilters.filter(e => e.cell === content.keyName)
            const targetPolymericList = []
            polymericFilters.forEach((e, index) => {
              if (e.cell === content.keyName) {
                targetPolymericList.push({
                  ...e,
                  index
                })
              }
            })
            if (targetPolymericList.length) {
              const aggFilters = targetPolymericList.map(e => {
                return {
                  groupFilterType: e.groupFilterType,
                  filterType: e.filterType,
                  // filterValueType: e.filterValueType,
                  columnType: e.columnType,
                  values: [String(e.values)],
                  filterIndex: e.index,
                }
              })

              // 处理第一条的groupFilterType
              // aggFilters[0] && (aggFilters[0].groupFilterType = 'and')

              Object.assign(itemData, {
                aggFilters
              })
            }
          }

          // 日期维度切换
          timeDimensionSplittingRule && (itemData.timeDimensionSplittingRule = timeDimensionSplittingRule)
          // 域的整合
          const domain = JSON.parse(JSON.stringify({
            'timeArea': content.timeArea || undefined,
            'compareRule': content.compareRule || undefined,
            'locationArea': content.locationArea || undefined,
            'compareShowNameType': content.isExpression ? undefined : (content.compareShowNameType || undefined),
            'type': content.isExpression ? undefined : (content.textDomainType || undefined),
          }))
          Object.assign(itemData, domain)

          // 文本域计算域为上期扩展时
          if (((domain.compareShowNameType && domain.compareShowNameType === 'periodtext_expand_prevyear') ||
               (domain.timeArea && domain.timeArea === 'period_expand_prevyear')) && !content.compareRule) {
            itemData.compareRule = 'year-on-year'
            // 文本域日期上期扩展后台不支持横向 故固定纵向扩展 bug 56972
            itemData.extend = 1
          }

          Object.assign(itemData, content.rankType ? {
            type: 'rank',
            rankOrder: content.rankOrder,
            parallelRanking: content.parallelRanking === undefined ? true : Boolean(content.parallelRanking)
          } : {})
          Object.assign(itemData, content.isStack ? { type: 'stack' } : {})

          if (content.compareShowNameType === 'dimension_alias') {
            changeDimensionAliasCell(cell, table, drillDimensions, vm, itemData)
          }


          // 去除
          content.exclude && Object.assign(itemData, { exclude: true })

          measureDimensionExtensionChange(item, itemData, dataMergeCellList, table)

          if (content.lgeType) {
            const lgeParams = lgeTypeFormatter(content, tableData)
            Object.assign(itemData, lgeParams)
            // Object.assign(itemData, content.lge && content.lgeType ? {
            //   type: content.lgeType,
            //   lge: {
            //     moneyColumnName: content.lge.moneyColumnName,
            //     moneyDataSetId: content.lge.moneyDataSetId,
            //     lgeType: content.lge.lgeType }
            // } : {})
            // Object.assign(itemData, content.peer && content.lgeType ? {
            //   type: content.lgeType,
            //   peer: {
            //     peerDimensionName: content.peer.peerDimensionName,
            //     peerMetricName: content.peer.peerMetricName,
            //     datasetId: content.peer.datasetId }
            // } : {})
            // Object.assign(itemData, content['curated_aggregation'] && content.lgeType ? {
            //   type: content.lgeType,
            //   curatedAggregation: {
            //     ...content['curated_aggregation']
            //   }
            // } : {})
          }
          // 特殊处理处理lge脏数据
          // if (content.lgeType !== 'peer' && content.lgeType !== 'curated_aggregation') {
          //   if ((content.lgeType && !content.lge) || (content.aggType === 'lge' && (!content.lgeType || !content.lge))) {
          //     delete content.lgeType
          //     delete content.lge
          //     itemData.aggType = ''
          //   }
          // }

          if (content.isGroup) {
            itemData.isGroup = true
          }
          if (content.isTotal) {
            itemData.isTotal = true
          }

          if (content.webFieldType) {
            const isDimension = item.content.customExpressionType === 'dimension'
            itemData[isDimension ? 'customerExprDim' : 'customerExprField'] = true
            itemData.columnName = content.customExpression
            !isDimension && (itemData.type = SUB_AGG_TYPE.CUSTOMER_EXPRESSION)
            itemData.aggType = CALC_FNS.CUSTOM
          }

          // 设置日期维度切换参数
          let dateCellFormat = {}
          if (content.dateDimension === true) {
            if (!simpleTable) {
              const dateFormat = content.dateFormat
              let v = 'customize_day_vs_day' // 为空
              const DIM = {
                [DATE_VALUE.Day]: 'customize_day_vs_day',
                [DATE_VALUE.Wk]: 'week_vs_week',
                [DATE_VALUE.Mth]: 'month_vs_month',
                [DATE_VALUE.Q]: 'quarter_vs_quarter',
                [DATE_VALUE.HYr]: 'half_year_vs_half_year',
                [DATE_VALUE.Yr]: 'year_vs_year',
              }
              if (content.dateSwitchChose) {
                v = DIM[content.dateSwitchChose]
              }
              if (content.dateSwitchChose === DATE_VALUE.Day) {
                if (dateFormat?.dateCustomFormat === 'd') {
                  v = `${v}_dayofmonth`
                  previewParams.needTitleCalendarCacheMap = true
                } else if (content.dayType) {
                  v = `${v}${content.dayType}`
                  previewParams.needTitleCalendarCacheMap = true
                } else if (dateFormat) {
                  dateCellFormat = {
                    dateCustomFormat: dateFormat.dateCustomFormat || undefined,
                    timeCustomFormat: dateFormat.timeCustomFormat || undefined
                  }
                }
              }
              itemData.timeDimensionSplittingRule = v
            }
            if (content.dateSwitchChose === 'Wk' || (simpleTable && itemData.timeDimensionSplittingRule === 'week_vs_week')) {
              const typeList = dateFormat ? dateFormat.weekCustomFormat : content.weekTypeList
              if (!typeList) {
                itemData.timeDimensionSplittingRemark = 'NO_REMARK'
              } else if (typeList.length === 1) {
                if (typeList[0] === 'Wkxx') {
                  itemData.timeDimensionSplittingRemark = 'NO_REMARK'
                } else if (typeList[0] === 'Week Range') {
                  itemData.timeDimensionSplittingRemark = 'ONLY_REMARK'
                }
              } else if (typeList.length === 2) {
                if (typeList[0] === 'Wkxx') {
                  itemData.timeDimensionSplittingRemark = 'DIM_REMARK'
                } else if (typeList[0] === 'Week Range') {
                  itemData.timeDimensionSplittingRemark = 'REMARK_DIM'
                }
              }
            }
            if (dateFormat && content.dateSwitchChose !== 'Wk' && content.dateSwitchChose !== DATE_VALUE.Day) {
              if (content.dateSwitchChose === DATE_VALUE.Mth) {
                dateCellFormat = {
                  monthCustomFormat: dateFormat.monthCustomFormat || undefined,
                }
              }
              if (content.dateSwitchChose === DATE_VALUE.Q) {
                dateCellFormat = {
                  quarterCustomFormat: dateFormat.quarterCustomFormat || undefined,
                }
              }
              if (content.dateSwitchChose === DATE_VALUE.HYr) {
                dateCellFormat = {
                  halfYearCustomFormat: dateFormat.halfYearCustomFormat || undefined,
                }
              }
              if (content.dateSwitchChose === DATE_VALUE.Yr) {
                dateCellFormat = {
                  yearCustomFormat: dateFormat.yearCustomFormat || undefined,
                }
              }
            }
            itemData.filterMetricsNullValue = content.hasOwnProperty('isShowEmptyValue') ? content.isShowEmptyValue : true
          }

          // 跨年合并
          itemData.needYearsMerge = content.hasOwnProperty('needYearsMerge') ? content.needYearsMerge : false

          // 设置关联的合并单元格
          if (item.extendParent) {
            itemData.parentKey = item.extendParent.content.coordinate
          }

          // 格式： 千分位
          const cellFormat = item.content.cellFormat
          if (cellFormat) {
            itemData.cellFormat = {}
            if (cellFormat.zeroFormat === true || cellFormat.zeroFormat === 'open') {
              itemData.cellFormat.zeroFormat = true
            } else if (cellFormat.zeroFormat === 'close') {
              itemData.cellFormat.zeroFormat = false
            }
            // itemData.cellFormat.zeroFormat = cellFormat.zeroFormat
            // v3.11.01 task4080 日期维度支持设置格式  后修改增加判断（2023年4月13日 14:02:58）
            cellFormat.dateCustomFormat && (itemData.cellFormat.dateCustomFormat = cellFormat.dateCustomFormat)
            cellFormat.timeCustomFormat && (itemData.cellFormat.timeCustomFormat = cellFormat.timeCustomFormat)
            if (cellFormat.type === FORMAT.CURRENCY_Unit) {
              const smallShort = cellFormat.hasOwnProperty('smallShort') ? cellFormat.smallShort : false
              itemData.cellFormat[cellFormat.type] = { ...cellFormat.value, smallShort }
            } else {
              cellFormat.type !== undefined && (itemData.cellFormat[cellFormat.type] = cellFormat.value)
            }

            if (Object.keys(itemData.cellFormat).length === 0) {
              delete itemData.cellFormat
            }
          }

          // 日期维度切换兼容格式数据
          if (itemData.cellFormat) {
            Object.assign(itemData.cellFormat, dateCellFormat)
          } else if (Object.keys(dateCellFormat).length) {
            itemData.cellFormat = dateCellFormat
          }

          // 排序搜索
          if (item.previewParams) {
            const params = item.previewParams
            if (params.order) {
              headerOrder = true
            }
            params.order && (itemData.order = params.order)
            if (params.search) {
              itemData.values = [params.search]
              itemData.filterType = itemData.columnType === 'number' ? '=' : 'like'
            }
            params.extendOrderColumn && (itemData.extendOrderColumn = params.extendOrderColumn)
          }

          // 处理行、列统计
          if (STATISTIC_TYPE.includes(content.type)) {
            itemData.type = content.type
            itemData.statisticType = content.statisticType
            if (content.type === CELL_TYPE.row_statistic) {
              itemData.columnName = ''
            }
          }

          // 处理复杂表格 列总计 todo
          if (content.columnTotal) {
            const targetCell = checkColumnTotalTargetCell(cell, table)
            if (targetCell) {
              const columnTotal = content.columnTotal
              itemData.type = 'column_statistic'
              itemData.columnName = targetCell.content.coordinate || columnTotal.targetCoordinate
              itemData.statisticType = columnTotal.calculationValue
              targetCell.content.dataSetId && (itemData.dataSetId = targetCell.content.dataSetId)
              targetCell.content.columnType && (itemData.columnType = targetCell.content.columnType)
            }
          }

          // 处理复杂表格 行总计
          if (content.rowTotal) {
            const targetCell = checkRowTotalTargetCell(cell, table)
            if (targetCell) {
              const rowTotal = content.rowTotal
              itemData.type = 'row_statistic'
              itemData.columnName = targetCell.content.coordinate || rowTotal.targetCoordinate
              itemData.statisticType = rowTotal.calculationValue
              targetCell.content.dataSetId && (itemData.dataSetId = targetCell.content.dataSetId)
              targetCell.content.columnType && (itemData.columnType = targetCell.content.columnType)
            }
          }

       // 自定义公式返回值
          if (customFnData && customFnData.expReturnType) {
            itemData.expReturnType = customFnData.expReturnType
            itemData.columnType = customFnData.expReturnType // 如果设置了自定义公式columnType 为自定义公式的返回值
            itemData.customerExprDim = customFnData.customFieldType === 'variable' ? true : undefined // 自定义维度标识
            itemData.type = customFnData.customFieldType === 'variable' ? 'variable' : itemData.type // 自定义维度标识
          }
          // 层级占比
          if (content.hierarchyProportion) {
            itemData.hierarchy = content.hierarchyProportionLayer || '0'
          }
          // 列数限制
          if (content.extendLimit) {
            const { lineNumber, baseRank, rankKeyName, rankCell, type } = content.extendLimit

            if (type === '2') {
              itemData.extendLimit = {
                limit: lineNumber,
                baseRank
              }
              if (baseRank && rankKeyName !== '') {
                itemData.extendLimit.rankCell = rankCell.content.coordinate
              }
            }
          }

          // 数据条设置
          if (content.dataBarSet) {
            const dataBarSet = content.dataBarSet
            const dataBar = {
              type: DataBarTypeDIC[dataBarSet.type],
              min: dataBarSet.min !== undefined ? String(dataBarSet.min) : undefined,
              max: dataBarSet.max !== undefined ? String(dataBarSet.max) : undefined,
            }
            itemData.dataBar = dataBar
          }

          // 色阶
          const colorGradationSet = content.colorGradationSet
          if (colorGradationSet) {
            itemData.colorGradation = {
              type: DataBarTypeDIC[colorGradationSet.type],
              min: colorGradationSet.colorGradationMin !== undefined ? String(colorGradationSet.colorGradationMin) : undefined,
              max: colorGradationSet.colorGradationMax !== undefined ? String(colorGradationSet.colorGradationMax) : undefined,
            }
          }

          // 最大最小值
          const markMaxMinSet = content.markMaxMinSet

          if (markMaxMinSet) {
            itemData.maximum = true
          }

          cells.push(Object.assign({}, obj, itemData))
        }
      })
    })
  })

  // 单元格额外配置
  // 排名设置datasetID
  cells.forEach((item) => {
    if (item.type === 'rank' && !item.dataSetId) {
      cells.some((val) => {
        if (val['coordinate'] === item.columnName) {
          item.dataSetId = val.dataSetId
        }
        return val['coordinate'] === item.columnName
      })
    }
  })
  // 堆叠累加设置数据集ID
  cells.forEach((item) => {
    if (item.type === 'stack' && !item.dataSetId) {
      const cell = cells.find(cell => cell.coordinate === item.columnName) || {}
      item.dataSetId = cell.dataSetId
    }
  })
  // 单元格自定义排序
  cells.forEach(item => {
    // 设置规则：
    // 1、如果存在表头排序，自定义排序失效, 但是横向扩展任然会生效
    if ((!headerOrder || item.extend === 2) && cellOrder.length) {
      const coordinate = item.coordinate
      const targetCell = cellOrder.filter(e => e.coordinate === coordinate)[0]
      if (targetCell) {
        if (targetCell.columnName) {
          const orderByIndexObj = simpleTable ? {} : { orderByIndex: targetCell.orderByIndex }
          // 处理单元格自定义排序
          item.order = ''
          item.custOrderField = { columnName: targetCell.columnName, order: targetCell.order, ...orderByIndexObj }
        } else {
          // 处理单元格排序
          item.order = targetCell.order
          if (!simpleTable) item.orderByIndex = targetCell.orderByIndex
        }
      }
    }
  })
  // "去除单元格"添加数据集ID
  cells.filter(item => item.exclude && !item.dataSetId)
    .forEach(excludeCell => {
      excludeCell.dataSetId = cells.find(({ rowIndex, dataSetId }) => rowIndex === excludeCell.rowIndex && dataSetId)?.dataSetId
    })
  // 层级分类汇总 参数设置
  const treeGroupCoordinate = options.treeGroupCoordinate || ['ALL']
  const selectValType = treeGroupCoordinate[0]
  const onlyShowTitle = selectValType === 'Only Title'
  const tempArr = (treeGroupCoordinate.includes('ALL') || selectValType === 'Only Title') ? [] : treeGroupCoordinate
  const treeGroup = tempArr.map(coordinate => {
    return {
      coordinate,
      aggType: '',
    }
  })
  const statisCell = {
    onlyShowTitle,
    cells: treeGroup,
  }

  previewParams.params = { cells, groupSearch, statisCell }

  // 表格设计预览时，钻取
  drillDimensions && (previewParams.params.drillDimensions = drillDimensions)

  // 行数据过滤
  const rowFilters = (() => {
    let res = []

    // rowFilterMap --> 表格的"行数据过滤"配置,
    // rowFilterMap 是一个对象 (key: String, value: Array<Object>),
    // 其键表示行索引 ( 从 0 开始 ),
    // 其值为一个数组, 表示某行"行数据过滤"的信息;
    // 这里先获取设置了行数据过滤的行的索引 ( Array<String> )
    const rowIndexList = Object.keys(rowFilterMap)

    // 通过双重循环, 将 rowFilterMap 中的行数据过滤信息依次加入 res
    rowIndexList.forEach(rowIndex => {
      // 获取该行"行数据过滤"的信息 ( Array<Object> )
      const filterList = JSON.parse(JSON.stringify(rowFilterMap[rowIndex]))

      filterList.forEach(item => {
        // 往"行数据过滤信息"中添加后台需要的信息 ( 行索引 )
        item.rows = [String(parseInt(rowIndex) + 1)]
        // 将此行的行数据过滤信息 (Array<Object>) 依次加入 res
        res.push(item)
      })
    })

    return res
  })()
  previewParams.rowFilters = filtersFormatter(rowFilters, caculationMethodType)

  // 层级表展开层级
  let { hierarchy = '0' } = options
  if (utils && utils.isMobile) hierarchy = 'all'
  previewParams.open = hierarchy === 'all'
  previewParams.openUpNum = parseInt(hierarchy)

  if (tableConfig?.hierarchicalExpand) {
    const hierarchicalExpand = tableConfig.hierarchicalExpand

    previewParams.open = hierarchicalExpand === 'all'
    previewParams.openUpNum = parseInt(hierarchicalExpand)
  }

  if (hierarchyLimit) {
    previewParams.hierarchyLimitList = hierarchyLimitInfo.map(e => {
      return {
        hierarchyNum: Number(e.hierarchy),
        orderCell: e.cell,
        orderType: e.sortType,
        limit: e.limit,
      }
    })
  }

  warningMap && formatAlertData(previewParams, warningMap, options.keyNameMap, subscribeWarningData, vm)
  formatExcludeData(previewParams, tableData, excludeObj)
  formatMarkData(previewParams, tableData, options.keyNameMap)

  previewParams.headerOrder = headerOrder
  previewParams.language = language
  previewParams.mergeInfos = adapterOrderFieldsMerge(orderFieldsMerge)

  // 行数限制
  if (extendLimit) {
    const { baseRank, rankKeyName, rankCell } = extendLimit
    previewParams.extendLimit = {
      baseRank
    }
    if (baseRank && rankKeyName !== '') {
      previewParams.extendLimit.rankCell = rankCell.content.coordinate
    }
  }

  if (Object.values(dataSetJoinsConfig).length > 0) {
    const { dataSetJoins, referenceDatasetId, dataSetJoinsColumns, associateDatasetName, sqlJoins, dataSetJoinsType } = dataSetJoinsConfig
    const [majorDatasetId, joinDatasetId] = referenceDatasetId
    const isLeftJoin = sqlJoins === 'LEFT JOIN'
    const isSingleDataSetJoins = dataSetJoinsType === '1'
    previewParams.virtualTable = {
      majorDatasetId,
      joinDatasetId: !isSingleDataSetJoins ? joinDatasetId : undefined,
      majorFields: !isSingleDataSetJoins && isLeftJoin ? [...dataSetJoins[majorDatasetId]] : [],
      joinFields: !isSingleDataSetJoins && isLeftJoin ? [...dataSetJoins[joinDatasetId]] : [],
      columns: dataSetJoinsColumns.map(item => {
        const { dataSetId, columnName, alias, aggType, columnTpe, customExpressionSet } = item
        const isDim = aggType === 'DIM'
        let name = (!!customExpressionSet && !!customExpressionSet.expression) ? customExpressionSet.expression : columnName
        let aggTypeTemp = isDim ? '' : (aggType === 'SUM' && !!customExpressionSet) ? 'expression' : aggType
        return { dataSetId, columnName: name, alias, aggType: aggTypeTemp, columnType: isDim ? columnTpe : 'number', customExpressionSet }
      })
    }
  }
  if (isAdapter) {
    /* 注意：
    *  看板中渲染时，后面代码将不会执行
    *  当添加请求参数时，请在elementTable文件的 bridge 适配器中
    *  添加对应字段
    */
    return previewParams
  }
  const tableElements = Object.assign({
    cells: previewParams.params.cells,
    filters: previewParams.filters,
    simpleTable,
    rowFilters: previewParams.rowFilters,
    extendLimit: previewParams.extendLimit,
    hierarchyLimitList: previewParams.hierarchyLimitList,
    groupSearch: previewParams.params.groupSearch,
    statisCell: previewParams.params.statisCell,
    alterLogic: previewParams.alterLogic,
    dataMarks: previewParams.dataMarks,
    pageInfo: {
      page: previewParams.currentPage,
      pageSize: previewParams.pageSize,
    },
    mergeInfos: previewParams.mergeInfos
  },
  lineNumber !== '1' ? { maxCountNum } : {},
  drillDimensions ? { drillDimensions, } : {},
  previewParams.excludes?.length ? { excludes: previewParams.excludes } : {},
  previewParams.virtualTable ? { virtualTable: previewParams.virtualTable } : {},
  previewParams.language ? { language: previewParams.language } : {}
  )
  // 懒加载时，设置isLazy，使后台特殊处理total行的数据
  options.isLazy && (tableElements.pageInfo.isLazy = true)
  const params:CommonInterface = {
    tenantId: options.tenantId,
    tableElements: [tableElements],
  }

  const { id, name } = gridInfo || {}
  const { isSubscribe, isRemind, isMobileApp } = commonData || {}

  const lang = gridInfo?.langCode || vm?.langCode

  params.languageCode = vm?.utils?.languageList?.find(item => item.isoCode === lang)?.languageCode || ''
  const { isLargeScreen, isDataReport } = vm?.utils || {}
  params.reportLog = updateReportLogObj({
    id,
    name,
    isSubscribe,
    isRemind,
    isMobileApp,
    isMobile: utils.isMobile,
    isLargeScreen,
    isDataReport,
    type: 'table',
    langCode: gridInfo.langCode || '',
  })

  // 如果不分页，pageInfo置空
  if (params.tableElements.page === -1) params.tableElements.pageInfo = {}
  params.tableElements.forEach(item => {
    if (item.pageInfo.page === -1) item.pageInfo = {}
  })

  if (getOnlyParams) {
    return params
  }
  return enginePreview(api, params)
}

/**
 * 判断单元格是否被metric隐藏
 * @param cell
 * @param metricHide
 * @param type
 * @param index
 * @returns {boolean} true: 没有隐藏， false： 隐藏
 */
function metricFilter(cell, metricHide, type, index) {
  const { row: metricHideRow, col: metricHideCol } = metricHide
  const metricHideArr = type === 'col' ? metricHideCol : metricHideRow
  const spanLen = 'col' ? cell.colspan : cell.rowspan
  if (metricHideArr.includes(index)) {
    if (spanLen > 1) {
      const endIndex = index + spanLen
      let needRequest = false
      for (let i = index; i < endIndex; i++) {
        if (!metricHideArr.includes(i)) {
          needRequest = true
        }
      }
      return needRequest
    } else {
      return false
    }
  }
  return true
}

/**
 * 更新文本域-维度别名值
 * @param cell
 * @param table
 * @param drillDimensions
 * @param vm
 * @param itemData
 */
export const changeDimensionAliasCell = (cell, table, drillDimensions, vm, itemData) => {
  const effectiveCellList = table.reduce((arr, row) => {
    arr = arr.concat(row)
    return arr
  }, []).filter(e => ['date', 'string'].includes(e.content.columnType) && e.content.aggType !== 'CUSTOM' && !e.content.webFieldType && e.content.text)

  const targetCell = effectiveCellList.find(e => e.content.keyName === cell.content.dimensionAliasKeyName)
  const coordinate = generateCoordinateForCell(targetCell)
  const targetDrillCell = Array.isArray(drillDimensions) ? drillDimensions.find(e => e.keyName === coordinate) : null

  if (vm?.$refs?.tableRender?.aliasDict) {
    if (targetDrillCell) {
      itemData.columnName = vm.$refs.tableRender.aliasDict?.updateExpressionFieldAlias(targetDrillCell.columnName, targetDrillCell.dataSetId, false, true) || targetDrillCell.columnName
    } else if (targetCell) {
      itemData.columnName = vm.$refs.tableRender.aliasDict?.updateExpressionFieldAlias(targetCell.content.text, targetCell.content.dataSetId, false, true) || targetCell.content.text
    }
  }
}
export const checkColumnTotalTargetCell = (cell, table) => {
  const columnCellList = table.reduce((arr, row) => {
    arr.push(row[cell.columnIndex])
    return arr
  }, []).filter(e => !e.isMergeCell && e.content.text)
  const targetCell = columnCellList.find(e => e.content.keyName === cell.content.columnTotal.targetKeyName)
  if (targetCell) {
    if (targetCell.rowIndex !== undefined) {
      if (table[targetCell.rowIndex].find(e => !e.isMergeCell && ['date', 'string'].includes(e.content.columnType))) {
        return targetCell
      }
    }
  }
  return false
}
export const checkRowTotalTargetCell = (cell, table) => {
  const effectiveCellList = table.reduce((arr, row) => {
    arr = arr.concat(row)
    return arr
  }, []).filter(e => e.content.text)

  const rowExtendCell = effectiveCellList.filter(e => {
    const isDimensionCell = ['date', 'string'].includes(e.content.columnType)
    const isExtend2 = e.content.extend === '2'

    if (e.isMergeCell) {
      const isRow = e.end[0] < cell.rowIndex
      return isRow && isDimensionCell && isExtend2
    } else {
      return e.rowIndex < cell.rowIndex && isDimensionCell && isExtend2
    }
  }) || []

  if (!rowExtendCell.length) return false

  const columnExtendCell = effectiveCellList.filter(e => {
    const isNoAggType = e.content.aggType === ''
    const isExtend1 = e.content.extend === '1'

    if (e.isMergeCell) {
      const isRow = e.start[0] === e.end[0] && e.start[0] === cell.rowIndex
      return isRow && !isNoAggType && isExtend1
    } else {
      return e.rowIndex === cell.rowIndex && !isNoAggType && isExtend1
    }
  }) || []

  const targetCell = columnExtendCell.find(e => e.content.keyName === cell.content.rowTotal.targetKeyName)
  if (targetCell) {
    return targetCell
  }
  return false
}

// 度量维度扩展
export const measureDimensionExtensionChange = (cell, itemData, mergeCellList, table) => {
  // if (cell.isMergeCell) {
  //   const hasMerge = mergeCellList.find(e => e.start[0] <= cell.start[0] && e.end[0] >= cell.end[0])
  //   if (!hasMerge) return
  //   Object.assign(itemData, { areaId: hasMerge.content.keyName })
  // }
  // if (cell.content.aggType === '') return
  const hasMerge = mergeCellList.find(e => e.start[0] <= cell.start[0] && e.end[0] >= cell.end[0])
  if (!hasMerge) return
  Object.assign(itemData, { areaId: hasMerge.content.keyName })
  if (cell.content.extend !== '1') return
  table.forEach(row => {
    row.forEach(e => {
      if (e.content.dataSetId && ['date', 'string'].includes(e.content.columnType) && e.start[1] <= cell.start[1] && e.end[1] >= cell.end[1] && e.content.extend === '2') {
        Object.assign(itemData, { parentKey: e.content.coordinate })

      }
    })
  })
}

export const enginePreview = (api, params, options:CommonInterface = {}) => {
  if (options.application) {
    params.reportLog = params.reportLog || null
  }
  return previewMergeApi({ api, params })
}

let drillParams:CommonInterface = {}
export const getDrillData = (api, { drillDimensions, id, options, isAdapter = false }) => {
  drillParams = Object.assign({}, {
    currentPage: CURRENTPAGE,
    pageSize: PAGESIZE,
    params: {},
  }, options)
  drillDimensions.id = id
  drillParams.params = drillDimensions

  const req = Object.assign(drillParams.params, {
    pageInfo: {
      page: drillParams.currentPage,
      pageSize: drillParams.pageSize,
    },
  })
  if (isAdapter) {
    return req
  }
  return previewMergeApi({ api,
params: {
    tableDrills: [req],
  } })
}

export function getTableConfigDataById(api, id) {
  return api.get(`/bi/talbe/get/table/${id}`)
}

// 获得看板数据
export function getBoardInfo(api, id) {
  return api.get(`/bi/metaDashboard/getBoard/${id}`)
}

// 层级表获取子层级数据, 传入id
export async function getGroupBySubNodes(api, params) {
  const res = await api.post('/report/engine/getGroupBySubNodes/id', params)
  return res
}

// 获取层级表 ids 数据
export async function getLevelBySubNodes(api, params) {
  const res = api.post('/report/engine/getGroupBySubNodes/ids', params)
  return res
}

// 看板跳转拿数据
export function getLinkValue(api, params) {
  return api.post('/bi/metaDashboard/getLinkValue', params)
}

// 多语言切换
export function translate(api, params) {
  return api.post(`/system/i18n/language/translate`, params)
}

// 表格标题多语言查询
export function getTableControlsLanguageList(api, tableId) {
  return api.get(`/bi/talbe/getTableControlsLanguageList/${tableId}`)
}

class cellContentPropsHelper {
  static getColumnType(content) {
    const defaultVal = content.isExpression ? 'number' : 'string'
    return content.columnType || defaultVal
  }
}

/**
 * 过滤隐藏单元格
 * cell._hide === true，则不需要发给后台
 * @param table
 */
function filterHideCell(table) {
  return table.map(rowData => {
    return rowData.filter((e, ci) => {
      return !e._hide
    })
  })
}
