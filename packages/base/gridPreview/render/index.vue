<template>
  <div class="gird-render-canvas" :class="`${element.id}_gird-render-canvas`">
    <!--  :class="{isShade: isHoldOn}"-->
    <div ref="renderContainer" class="render-container"></div>
    <div v-if="isShowRenderContainerHead" ref="renderContainerHead" class="render-container fixedStyle cover"
      :style="{
        '--container-head-bg': bgColor,
        '--container-bg': containerBgColor,
        '--large-screen-bg': `var(${isMobile ? '--sdp-color-KBBJS' : '--sdp-large-screen-bg'})`,
      }"></div>
  </div>
</template>

<script type="text/ecmascript-6">
import tableParser, { expandChildTree } from './parser'
import parseAfter, { setRowColWidth, setBorderMerge } from './parser/parseAfter'
import parseBefore from './parser/parseBefore'
import parser from './parser/parser'
import createSdpSheet from 'packages/base/gridPreview/render/canvasRenderer'
import { getLevelBySubNodes } from '../data/api'

import { removeChildTree } from './parser/treeTable'
import { TABLE_TYPE, CELL_TYPE, TABLE_STATUS } from '../common/constant'
import { CLICK_TYPE, CB_METHODS_NAME, SCROLL_TYPE } from './constant'
import { insertRows, removeRows, getRowsHeight, getAlertConfig, getTopCellFromRows, getParentCells, getExportCopyTable } from './parser/utils'
import zebraCrossing from 'packages/base/grid/helpers/constants/zebraCrossing'
import { useThemeStyle } from 'packages/base/grid/utils/theme'
import { HIERARCHY_STYLE, SCALE, getDefaultHierarchyStyleList } from 'packages/base/gridPreview/render/canvasRenderer/component/constant'
import alertMock from '../data/alertMock'
import { THEME_TYPE } from 'packages/assets/constant'
import { BOARD_ELEMENT_THEME } from '../../board/displayPanel/supernatant/boardElements/BoardElement'

import { EVENT_BUS, SUPERNATANT_LAYOUT } from 'packages/base/board/displayPanel/constants'
import * as utils from '../common/js/utils'
import { getCellRenderPos } from './parser/Cell'
import { ExcludeForTableRender } from '../mixins/exclude'
import { cellPercentFormatFormRes } from '../data/utils'
import CELL_WARNING from 'packages/base/grid/helpers/constants/cellWarning.ts'
import { WARN_GRAPHICAL_SYMBOL_LIST } from 'packages/base/grid/components/attributes-panel/CellWarning.ts'
import { STATIC_BASE_PATH } from 'packages/assets/constant.ts'
import { HYPER_LINK_STYLE } from '../../grid/helpers/constants'
import {generateCoordinateForCell} from "packages/base/grid/helpers/utils/gridTable";

const { WARNING_STYLE_TYPE_GRAPHICAL } = CELL_WARNING
export default {
  name: 'tableRender',
  mixins: [ExcludeForTableRender],
  inject: {
    utils: { default: {} },
    sdpBus: { default: {} },
    aliasDict: { default: null },
    element: {
      default: {}
    },
    api: {
      default: {}
    },
    setCellParams: {
      default: () => {}
    },
    setLoading: {
      default: () => {}
    },
    boardQuestParams: {
      default: {}
    },
    commonData: {
      default: {}
    },
    themeData: {
      default: {}
    },
    fullscreenData: {
      default: {}
    }
  },
  props: {
    originalTable: {
      type: Array,
      require: true,
    },
    store: {
      type: Object,
      default: () => ({ options: {} })
    },
    tableStyle: {
      type: Object
    },
    interactionClickRecord: {
      type: Object
    },
    tableConfig: {
      type: Object,
      require: true,
    },
    tableDefaultConfig: {
      type: Object,
    },
    pagination: {
      type: Object,
    },
    showBottomLoading: {
      type: Boolean,
      default: false,
    },
    // 简单表格编辑设置
    isChartSet: {
      type: Boolean,
      default: false
    },
    isChart: {
      type: Boolean,
      default: false
    },
    application: {
      type: String,
      default: TABLE_STATUS.preview
    },
    // groupSearchCell: {
    //   type: Object,
    // },
    elList: {
      type: Array,
      default: () => []
    },
    drillLevel: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    animationOption() {
      return this.tableConfig.animationOption
    },
    bottomLoadable() {
      // 底部可加载
      return this.showBottomLoading && !this.fullScreenPreview
    },
    // 全屏预览模式
    fullScreenPreview() {
      return this.isPreview && this.themeFullScreen
    },
    themeFullScreen() {
      return this.$_getProp(this.themeData, 'themeFullScreen', false)
    },
    themeType() {
      return this.$_getProp(this.themeData, 'themeType', THEME_TYPE.classicWhite)
    },
    displayData() {
      return this.originalTable || [[]]
    },
    tableType() {
      return this.tableConfig.type
    },
    resizeConfig() {
      return this.tableConfig.resizer
    },
    isMobile() {
      return this.$_getProp(this, 'utils.isMobile', false)
    },
    isMobileApp() {
      return this.$_getProp(this, 'commonData.isMobileApp', false)
    },
    isPreview() {
      return this.$_getProp(this, 'commonData.isPreview', false)
    },
    themeMap() {
      const themeType = this.themeData?.appThemeType || this.themeData.themeType || THEME_TYPE.classicWhite
      return this.$_getProp(this.element, `themeMap.${themeType}`, BOARD_ELEMENT_THEME[themeType])
    },
    bgColor() {
      const isMobile = this.isMobile
      return this.themeMap[isMobile ? 'mobileBgc' : 'pcBgc']
    },
    containerBgColor() {
      const id = this.element._containerId

      if (!this.isShowRenderContainerHead || !id) return ''

      const el = this.elList.find((item) => id === item.id)

      const themeType = this.themeData?.appThemeType || this.themeData.themeType || THEME_TYPE.classicWhite
      const themeMap = this.$_getProp(el, `themeMap.${themeType}`, BOARD_ELEMENT_THEME[themeType])
      const isMobile = this.isMobile

      return themeMap[isMobile ? 'mobileBgc' : 'pcBgc']
    },
    enlargeVisible() {
      return this.fullscreenData.enlargeVisible
    },
  },
  data() {
    return {
      sheetData: null,
      renderOptions: null, // 当前层级表渲染条件参数
      sdpSheet: null,
      sdpSheetHead: null,
      options: {}, // 表格解释器参数
      tempWidth: null,
      tempHeight: null,

      layoutRef: null,
      displayDataExpand: [],
      hideColumnList: [],
      hideRowList: [],
      imgExamples: {},
      isShowRenderContainerHead: false,
      canvasNoReset: false,
    }
  },
  watch: {
    // 交互点击记里
    interactionClickRecord(val) {
      const cellsConfig = this.sheetData.cellsConfig
      if (cellsConfig) {
        for (let cell of Object.values(cellsConfig)) {
          if (cell?.meta?.content) {
            const { keyName = '', jumper = false } = cell.meta.content
            if (Object.keys(val).includes(keyName) && jumper) {
              cell.meta.content.jumperText = val[keyName]
            } else {
              delete cell.meta.content.jumperText
            }
          }
        }
      }
    },
    // sheetData(val) {
    //   this.loadData(val)
    // },
    enlargeVisible(val) {
      this.retainCanvasTempData(true)
      this.sdpSheet.setCellAttr({
        isDisabled: val && {
          [CLICK_TYPE.jumper]: false,
          [CLICK_TYPE.link]: false,
        }
      }).sheetReset()
    },
    // fullScreenPreview(val) {
    //   this.sdpSheet.setCellAttr({
    //     isDisabled: val && {
    //       [CLICK_TYPE.jumper]: false,
    //       [CLICK_TYPE.drill]: false
    //     }
    //   }).sheetReset()
    // },
    themeFullScreen(val) {
      val || this.sdpSheet.sheetReset()
    },
    themeMap: {
      handler() {
        this.sdpSheet.render()
      },
      deep: true
    },
    'element.scale'(val) {
      if (this.sdpSheet) {
        this.sdpSheet.setScale(val).render()
      }
    },
    'element.isFocus'() {
      this.isShowScrollbarCall()
    },
    isPreview(val, old = false) {
      if (this.isMobile && val !== old) {
        this.sdpSheet.sheetReset()
      }
      this.isShowScrollbarCall()

      this.sdpSheet.setHeaderData()
    },
  },
  methods: {
    initOptions(tableConfig, other) {
      const config = tableConfig || this.tableConfig
      // 选取表格部分配置数据
      this.options = {
        api: this.api,
        displayData: [[]], // 展示表格的配置数据
        type: this.tableConfig.type, // 表格类型
        isExtendMulti: config.extendMulti || false, // 是否为扩展单元 且支持汇总行 表头设置(可以理解为是否为老看板 false 为老看板)
        totalRow: config.totalValue, // 表格Total行,
        groupByResponse: null, // 层级表后台返回数据
        treeCurConfig: {}, // 层级表相关配置和所需数据
        hideRows: this._getHideRow(config), // 隐藏行
        collapseCol: (config.collapseCol && Object.keys(config.collapseCol).length) ? config.collapseCol : null, // 折叠列
        collapseColHide: this._getHideCol(config), // 折叠藏、metric隐藏列
        groupExpandLevel: config.hierarchicalExpand || config.groupExpandLevel, // 层级表 展开层级，全部展开 'all'
        hierarchicalExpand: config.hierarchicalExpand || config.groupExpandLevel || '0',
        hierarchicalExpandActive: config.hierarchicalExpandActive || config.hierarchicalExpandActive || false,
        headerConfig: config.tableHeaderConfig || {}, // 表头配置
        freeze: config.freeze || null, // 固定行列,
        alertConfig: other ? getAlertConfig(config.warningMap, other) : null,
        statusFields: {
          isExport: false, // 数据导出
          // openTree: this.isMobile ? true : false, // 全部展开层级树,
          isMobile: this.isMobile, // 是否移动端,
          openTree: !!this.isMobile, // 全部展开层级树,
          isShowAllHeight: config.innerVerticalContainer || false // 是否显示全部高度
        },
        serialNumber: config.serialNumber,
        drillLevelAll: this.drillLevel?.all || [],
        aliasDict: this.aliasDict || null,
        store: {}, // 保存渲染的临时数据
      }
    },
    // 解析后台返回数据
    parseResData(resData = {}, option = {}) {
      let displayData
      let tempOptions = this.store.options || {}
      let dataMarkResponse
      let markConfig

      // 处理数据条 导出需要导出色阶
      // if (!this.options.statusFields.isExport) {
      this.options.percentResponse = cellPercentFormatFormRes(resData)
      this.options.themeType = this.themeType
      // }
      tempOptions.alertTableRes = this.tableType === TABLE_TYPE.tree ? resData.tableValuesAlert : resData.alertTableRes
      if (this.options.statusFields.isExport) {
        displayData = this.displayData
        this.changeDisplayDataTextTimeArea(this.$_deepClone(displayData), resData)
        // 做判断是为了避免对原有表格造成影响
        if (this.hasMulTextTimeArea(displayData)) {
          displayData = this.getDisplayDataByExpandYear(this.$_deepClone(displayData), resData, option)
          this.displayDataExpand = displayData
        } else {
        // displayData = this.getExportCopyTable(resData, this.displayData)
        displayData = getExportCopyTable(resData, this.displayData, this.tableType)
        }
      } else {
        displayData = this.displayData
        this.changeDisplayDataTextTimeArea(this.$_deepClone(displayData), resData)

        // 做判断是为了避免对原有表格造成影响
        if (this.hasMulTextTimeArea(displayData)) {
          displayData = this.getDisplayDataByExpandYear(this.$_deepClone(displayData), resData, option)
          this.displayDataExpand = displayData
        }

        dataMarkResponse = resData.dataMarkResponse
        if (Array.isArray(dataMarkResponse) && dataMarkResponse) {
          markConfig = dataMarkResponse.reduce((markConfig, item) => {
            const coords = item.markDisplayKeyName
            if (!markConfig.hasOwnProperty(coords)) markConfig[coords] = []
            markConfig[coords].push(JSON.parse(JSON.stringify(item)))
            return markConfig
          }, {})
        }
      }

      if (this.tableDefaultConfig?.isHideEmptyColumns && displayData.length && this.tableType !== TABLE_TYPE.tree) {
        const nullColList = []
        // 列循环 如果有表头则表头隐藏，否则则去除displayData列
        for (let col = 0; col < displayData[0].length; col++) {
          const hasConst =  displayData.some(row => {
            const target = row.find(e => e.start?.[1] <= col && e.end?.[1] >= col)
            if (!target) return false
            if (!target.content?.dataSetId && target.content?.text) return true
            return false
          })
          const hasVar =  displayData.some(row => {
            const target = row.find(e => e.start?.[1] <= col && e.end?.[1] >= col)
            if (!target) return false
            if (target.content?.dataSetId) return true
            return false
          })
          // console.log(`10311 列 ${col}: 常量 ${hasConst ? '有' : ''}  变量 ${hasVar ? '有' : ''}`)
          // 如果只存在常量 不处理
          // 如果只存在变量 不处理
          // if ((hasConst && !hasVar) || (!hasConst && hasVar) || (!hasConst && !hasVar)) {
          if ((hasConst && !hasVar) || (!hasConst && !hasVar)) {
            continue
          }
          // 如果存在常量 + 变量 处理判断
          // 如果是合并单元格，则需要判断都为空
          // 如果是表头，则表头也隐藏
          const isNull = displayData.every(row => {
            const target = row.find(e => e.start?.[1] <= col && e.end?.[1] >= col)
            if (!target) return false
            if (!target.content?.dataSetId) return true
            if (typeof target.data === 'string') return !target.data
            if (Array.isArray(target.data)) {
              const list = this.$_flatten(target.data)
              return !list.length
            }
            return true
          })
          if (isNull) {
            nullColList.push(col)
          }
        }
        // console.log('10311 nullColList', nullColList)

        const cellList = this.$_flatten(displayData)
        const mergeList = cellList.filter(e => e.isMergeCell)
        // console.log('10311 mergeList', mergeList)

        const mergeHasDataColList = []
        mergeList.forEach(cell => {
          const colStart = cell.start[1]
          const colEnd = cell.end[1]
          const colList = Array.from({ length: (colEnd - colStart) + 1 }, (_, i) => colStart + i)
          // console.log('10311 colList', colList)
          const isNullMerge = colList.every(e => nullColList.includes(e))
          if (!isNullMerge) {
            mergeHasDataColList.push(...colList)
          }
        })

        const nullColumnFilterList = nullColList.filter(e => !mergeHasDataColList.includes(e))

        // 行循环 去除displayData列
        if (!this.options.headerConfig) {
          displayData = displayData.map(row => {
            return [...row.filter((e, index) => !nullColumnFilterList.includes(index))]
          })
        }

        this.sethHeaderConfigHideColumn(nullColumnFilterList)
      } else {
        this.sethHeaderConfigHideColumn([])
      }

      // displayData = this.changeDisplayDataHideConfig(this.$_deepClone(displayData))

      const options = Object.assign({}, this.options, { displayData, dataMarkResponse, markConfig }, tempOptions)
      if (this.tableType === TABLE_TYPE.tree) {
        options.groupByResponse = resData
        Object.assign(options.treeCurConfig, {
          requestParams: {
            queryKey: resData.queryKey,
            elementId: this.element.id,
          }
        })
      } else if (options.statusFields.isExport) {
        // 在导出的时候，为了不隐藏渲染数据，需要将返回数据单独存储
        options.tableRespons = resData.tableRespons
      }

      displayData = this.changeDisplayDataHideConfig(this.$_deepClone(displayData), options, resData)
      Object.assign(options, { displayData })

      const data = tableParser(options)

      const exportStyle = this.getExportStyle()
      Object.assign(data, exportStyle)

      return {
        data, options
      }
    },
    sethHeaderConfigHideColumn(data) {
      this.$set(this, 'hideColumnList', data)
      if (this.options.headerConfig) {
        this.$set(this.options.headerConfig, 'hideColumnList', data)
      }
      if (this.options?.collapseColHide) {
        this.$set(this.options, 'collapseColHide', this._getHideCol(this.tableConfig))
      }
    },
    // 改变表格显示隐藏状态
    changeDisplayDataHideConfig(displayData, options, resData = {}) {
      const { dimensionNullDisplay, metricNullDisplay } = resData

      const isTree = this.tableType === TABLE_TYPE.tree

      const { hideEmptyColumns = [], hideEmptyRows = [] } = this.tableDefaultConfig
      const optionsClone = isTree ? this.$_deepClone(options) : options

      if (isTree) {
        parseBefore([], optionsClone)
      }

      const displayDataClone = isTree ? optionsClone.displayData : displayData

      if (displayDataClone.length && (hideEmptyColumns.length || hideEmptyRows.length)) {
        // 列空数据隐藏
        if (!isTree && !this.tableDefaultConfig?.isHideEmptyColumns && hideEmptyColumns.length) {
          const nullColList = []
          for (let col = 0; col < displayDataClone[0].length; col++) {
            if (!hideEmptyColumns.includes(col)) continue

            const hasVar = displayDataClone.some(row => {
              const target = row.find(e => e.start?.[1] <= col && e.end?.[1] >= col)
              if (!target) return false
              if (target.content?.dataSetId) return true
              return false
            })
            if (!hasVar) continue

            const isNull = displayDataClone.every(row => {
              const target = row.find(e => e.start?.[1] <= col && e.end?.[1] >= col)
              if (!target) return false
              if (!target.content?.dataSetId) return true
              if (typeof target.data === 'string') return !target.data

              const isDimension = ['date', 'string'].includes(target.content.columnType) && target.content.aggType !== 'CUSTOM' && !target.content.webFieldType
              const nullVal = isDimension ? dimensionNullDisplay : metricNullDisplay

              if (Array.isArray(target.data)) {
                const list = this.$_flatten(target.data).filter(e => e !== nullVal)
                return !list.length
              }
              return true
            })

            if (isNull) {
              nullColList.push(col)
            }
          }

          const cellList = this.$_flatten(displayDataClone)
          const mergeList = cellList.filter(e => e.isMergeCell)

          const mergeHasDataColList = []
          mergeList.forEach(cell => {
            const colStart = cell.start[1]
            const colEnd = cell.end[1]
            const colList = Array.from({ length: (colEnd - colStart) + 1 }, (_, i) => colStart + i)
            const isNullMerge = colList.every(e => nullColList.includes(e))
            if (!isNullMerge) {
              mergeHasDataColList.push(...colList)
            }
          })

          const nullColumnFilterList = nullColList.filter(e => !mergeHasDataColList.includes(e))

          // 行循环 去除displayData列
          // if (!this.options.headerConfig) {
          //   displayData = displayData.map(row => {
          //     return [...row.filter((e, index) => !nullColumnFilterList.includes(index))]
          //   })
          // }

          this.sethHeaderConfigHideColumn(nullColumnFilterList)

          Object.assign(options, {
            headerConfig: this.options.headerConfig,
          })
          Object.assign(options, {
            collapseColHide: this.options.collapseColHide,
          })
        }

        // 行空数据隐藏
        if (hideEmptyRows.length) {
          const nullRowList = []
          const nullValRowList = []
          for (let row = 0; row < displayDataClone.length; row++) {
            if (!hideEmptyRows.includes(row)) continue

            const hasVar = displayDataClone[row].some(cell => {
              if (cell.content?.dataSetId) return true
              return false
            })
            if (!hasVar) continue

            const isNull = displayDataClone[row].every(cell => {
              if (!cell.content?.dataSetId) return true
              if (typeof cell.data === 'string') return !cell.data

              const isDimension = ['date', 'string'].includes(cell.content.columnType) && cell.content.aggType !== 'CUSTOM' && !cell.content.webFieldType
              const nullVal = isDimension ? dimensionNullDisplay : metricNullDisplay

              if (Array.isArray(cell.data)) {
                const flattenList = this.$_flatten(cell.data)
                const list = flattenList.filter(e => e !== nullVal)
                if (flattenList.some(e => e === nullVal) && !list.length) {
                  nullValRowList.push(row)
                }
                return !list.length
              }
              return true
            })
            let hasVal = false
            if (isTree) {
              const { groupByResponse } = optionsClone
              const { columns = [], dataTree = [] } = groupByResponse
              hasVal = displayDataClone[row].some(cell => columns.includes(generateCoordinateForCell(cell)) && !!dataTree.length)
            }
            if (isNull && !hasVal) {
              nullRowList.push(row)
            }
          }

          const cellList = this.$_flatten(displayDataClone)
          const mergeList = cellList.filter(e => e.isMergeCell)

          const mergeHasDataColList = []
          mergeList.forEach(cell => {
            const rowStart = cell.start[0]
            const rowEnd = cell.end[0]
            const rowList = Array.from({ length: (rowEnd - rowStart) + 1 }, (_, i) => rowStart + i)
            const isNullMerge = rowList.every(e => nullRowList.includes(e))
            if (!isNullMerge) {
              mergeHasDataColList.push(...rowList)
            }
          })

          const nullRowFilterList = nullRowList.filter(e => !mergeHasDataColList.includes(e))

          // 修改页码-总数 手动去除空值转换的行数 例如空值为-、undefined的
          const nullValRow = Array.from(new Set(nullValRowList))
          if (nullValRow.length) {
            const total = this.pagination.total
            const totalNoNullCnt = total - nullValRow.length
            this.pagination.total = totalNoNullCnt < 0 ? 0 : totalNoNullCnt
          }

          this.$set(this, 'hideRowList', nullRowFilterList)
          Object.assign(options, {
            hideRows: this._getHideRow(this.tableConfig)
          })
          // 循环 去除displayData行
          // this.removeElementsFrom2DArray(displayData, nullRowFilterList)
        }
      } else {
        this.sethHeaderConfigHideColumn([])

        this.$set(this, 'hideRowList', [])
        Object.assign(options, {
          hideRows: this._getHideRow(this.tableConfig)
        })
      }
      return displayData
    },
    removeElementsFrom2DArray(arr, indexesToRemove) {
      // 从后往前删除避免索引错位
      const indexesToSort = [...indexesToRemove].sort((a,b) => b - a)
      for (const index of indexesToSort) {
        if (index >= 0 && index < arr.length) {
          arr.splice(index, 1)
        }
      }
      return arr
    },
    changeDisplayDataTextTimeArea(displayData, resData) {
      displayData.forEach((row, rowIndex) => {
        row.forEach((cell, colIndex) => {
          if (cell.content.compareShowNameType && cell.content.compareShowNameType !== 'periodtext_expand_prevyear') {
            let data = cell.data

            cell.type = 'cell-text'
            if (Array.isArray(data)) {
              cell.data = data.length ? String(data[0]) : ''
            } else {
              cell.data = String(data)
            }
          }
        })
      })
    },
    // 判断是否有文本域和计算域是日期上期扩展  注意：设置此属性只能是纵向扩展属性
    hasMulTextTimeArea(displayData) {
      let flag = false
      displayData.forEach(row => {
        row.forEach((cell, index) => {
          if (cell.content?.compareShowNameType === 'periodtext_expand_prevyear' || cell.content?.timeArea === 'period_expand_prevyear') {
            flag = true
          }
        })
      })
      return flag
    },
    getDisplayDataByExpandYear(displayData, resData, option) {
      /*
         1. 列循环 一列一列循环，查看每列存在的扩展合并单元格的colspan * data.length得到格子数 注：不支持下方有错位设置合并单元格
         2. 计算出上方循环出来的扩展数量
         3. 对单元格进行遍历，将坐标、合并数根据扩展数量偏移或者增加
         4. 复制列并插入列，调整对应的数据，坐标等
         5. 对合并单元格进行补充（此问题可能不存在）
      */
      const isExpandCell = (cell) => {
        const content = cell.content
        return content.compareShowNameType === 'periodtext_expand_prevyear' || content.timeArea === 'period_expand_prevyear'
      }
      const getDataFromDisplayDataExpand = (cell) => {
        for (let i = 0; i < this.displayDataExpand.length; i++) {
          for (let j = 0; j < this.displayDataExpand[i].length; j++) {
            const target = this.displayDataExpand[i][j]
            if (isExpandCell(target) && target.content.keyName === cell.content.keyName && target.variableKey === cell.variableKey && target.dataIndex === cell.dataIndex) {
              return target
            }
          }
        }
      }

      // 获取偏移位置
      const getOffsetList = (displayData) => {
        const offsetList = []
        const colspanList = []
        const dataList = []

        displayData.forEach((row, rowIndex) => {
          row.forEach((cell, colIndex) => {
            if (offsetList[colIndex] === undefined) {
              offsetList[colIndex] = 0
            }
            if (colspanList[colIndex] === undefined) {
              colspanList[colIndex] = 0
            }
            if (dataList[colIndex] === undefined) {
              dataList[colIndex] = 0
            }

            const content = cell.content
            if (isExpandCell(cell)) {
              const isInclude = colspanList.findIndex((e, index) => (e + index > colIndex && index < colIndex))

              if (isInclude === -1) {
                if (colspanList[colIndex] < cell.colspan) {
                  colspanList[colIndex] = cell.colspan || 0
                }

                if (content.compareShowNameType === 'periodtext_expand_prevyear') {
                  const len = cell.colspan * (cell.data.length - 1) || 0
                  if (offsetList[colIndex] < len) {
                    offsetList[colIndex] = len || 0
                  }

                  if (dataList[colIndex] < cell.data.length) {
                    dataList[colIndex] = cell.data.length || 0
                  }
                }

                if (content.timeArea === 'period_expand_prevyear') {
                  const datas = exCellsMap[cell.content.coordinate] || []
                  const len = cell.colspan * (datas.length - 1) || 0
                  if (offsetList[colIndex] < len) {
                    offsetList[colIndex] = len || 0
                  }

                  if (dataList[colIndex] < datas.length) {
                    dataList[colIndex] = datas.length || 0
                  }
                }
              }
            }
          })
        })

        return {
          offsetList,
          colspanList,
          dataList,
        }
      }

      // 计算并增加偏移
      const displayDataOffsetCalculation = (displayData) => {
        // 1. start end累加偏移
        // 2. 合并单元格colspan end 偏移

        // 累计偏移数组
        let sumTmp = 0
        const offsetSum = offsetList.map(e => {
          sumTmp += e
          return sumTmp
        })

        // 获取合并偏移量
        const getColspanSum = (start, end) => {
          let sumTmp = 0
          for (let i = start; i <= end; i++) {
            sumTmp += offsetList[i]
          }
          return sumTmp
        }

        displayData.forEach((row, rowIndex) => {
          row.forEach((cell, colIndex) => {
            const expandCol = colspanList.findIndex((e, index) => index < colIndex && e + index > colIndex)

            // 判断是普通单元格还是跨列单元格
            if (cell.colspan > 1 && !isExpandCell(cell)) {
              const colArr = []
              for (let i = cell.colIndex; i < cell.colIndex + cell.colspan; i++) {
                colArr.push(i)
              }

              const notAllInclude = colArr.some(col => colspanList.findIndex((e, index) => (e + index > col && index <= col)) === -1)

              if (notAllInclude) {
                const start = cell.start[1]
                const end = cell.end[1]
                const colspanSum = getColspanSum(start, end)

                cell.end && (cell.end[1] += colspanSum)
                cell.colspan += colspanSum
              } else {
                const isAll = colArr.every(col => colspanList.findIndex((e, index) => (e + index > col && index <= col)) !== -1)
                let spanCnt = 0
                colArr.forEach(col => {
                  spanCnt += colspanList[col] || 0
                })
                if (isAll && spanCnt === cell.colspan && cell.colspan !== colspanList[cell.start?.[1]]) {
                  const start = cell.start[1]
                  const end = cell.end[1]
                  const colspanSum = getColspanSum(start, end)

                  cell.end && (cell.end[1] += colspanSum)
                  cell.colspan += colspanSum
                }
              }
            }

            const offsetSumValue = expandCol !== -1 ? Number(offsetSum[expandCol - 1]) || 0 : Number(offsetSum[colIndex - 1]) || 0

            cell.start && (cell.start[1] += offsetSumValue)
            cell.end && (cell.end[1] += offsetSumValue)
            cell.colIndex += offsetSumValue
            cell.columnIndex += offsetSumValue
            cell.originalIndex = colIndex
          })
        })
      }

      // 增加列并计算位置 要根据数据量和colspan增加
      // 普通格子复制 改坐标  扩展格子额外还要改数据
      // 判断把超出长度的合并单元格长度改为1 todo
      const displayDataInsertColumnAndCalculation = (displayData) => {
        // 根据偏移量判断是否需要插入列
        for (let offsetIndex = 0; offsetIndex < offsetList.length; offsetIndex++) {
          if (offsetList[offsetIndex] <= 0) continue

          // 根据数据量插入多列 并调整数据
          for (let dataIndex = 1; dataIndex <= dataList[offsetIndex] - 1; dataIndex++) {
            // 以最长的扩展的单元格为单位插入列
            for (let i = 0; i < colspanList[offsetIndex]; i++) {

              displayData.forEach((row, rowIndex) => {
                // 根据数据量插入多列 并调整数据
                const cell = this.$_deepClone(row[offsetIndex + i])

                cell.start && (cell.start[1] += colspanList[offsetIndex] * dataIndex)
                cell.end && (cell.end[1] += colspanList[offsetIndex] * dataIndex)
                cell.colIndex += colspanList[offsetIndex] * dataIndex
                cell.columnIndex += colspanList[offsetIndex] * dataIndex
                cell.dataIndex = dataIndex

                if (!isExpandCell(cell) && cell.colspan > colspanList[offsetIndex] * dataList[offsetIndex]) {
                  cell.colspan = 1
                  this.$delete(cell, 'start')
                  this.$delete(cell, 'end')
                  this.$delete(cell, 'isMergeCell')
                  this.$delete(cell, 'x')
                  this.$delete(cell, 'y')
                  this.$delete(cell, 'i')

                  cell.type = 'merge-shadow-cell'
                }

                row.push(cell)
              })
            }
          }
        }

        displayData.forEach((row, rowIndex) => {
          row = row.sort((a, b) => a.colIndex - b.colIndex)
          row.forEach(cell => {
            const content = cell.content
            if (content.compareShowNameType === 'periodtext_expand_prevyear') {
              if (cell.dataIndex) {
                // cell.data = [[String(cell.data[cell.dataIndex])]]
                cell.data = String(cell.data[cell.dataIndex])
                cell.variableKey = cell.variableKey + '-' + cell.dataIndex
              } else {
                // cell.data = [[String(cell.data[0])]]
                cell.data = String(cell.data[0])
              }
              cell.type = 'cell-text'
            }

            if (content.timeArea === 'period_expand_prevyear') {
              const exCellsMapList = exCellsMap[cell.content.coordinate] || []
              const dataArr = exCellsMapList.map(e => resData.tableResponse[e]?.data || resData.tableResponse[e]?.datas || [])
              if (cell.dataIndex) {
                cell.data = dataArr[cell.dataIndex] || []
                cell.variableKey = cell.variableKey + '-' + cell.dataIndex
              } else {
                cell.data = dataArr[0] || []
              }

              if (option.moreData) {
                const target = getDataFromDisplayDataExpand(cell)
                cell.data = [...target.data, ...cell.data]
              }
            }
          })
        })
      }

      console.log('原始的数据', displayData)
      if (!resData.exCellsMap) return displayData

      // 返回的扩展数据
      const exCellsMap = resData.exCellsMap || {}
      const exCellsKeys = Object.keys(exCellsMap)

      // 偏移的数据
      const { offsetList = [], colspanList = [], dataList = [] } = getOffsetList(displayData)

      console.log('offsetList', offsetList)
      console.log('colspanList', colspanList)
      console.log('dataList', dataList)

      displayDataOffsetCalculation(displayData)
      console.log('偏移的数据', this.$_deepClone(displayData))

      displayDataInsertColumnAndCalculation(displayData)

      console.log('扩展的数据', displayData)

      return displayData
    },
    // 太丑了  后面再简化一下
    // 计算选择自定义同比年份时文本域和计算域的单元格扩展 原理是把一个单元格变成多个单元格  然后将其他单元格的坐标等进行偏移
    getDisplayDataWithTextTimeArea(displayData, resData) {
      console.log('原始的数据', displayData)

      // 返回的扩展数据
      const exCellsMap = resData.exCellsMap || {}
      const exCellsKeys = Object.keys(exCellsMap)
      // 文本域构建的单元格数据
      const periodTextCellList = []
      // 计算域构建的单元格数据
      const periodCellList = []

      // 每一列的最大偏移值
      const colOffset = []

      // 循环一遍表格 将文本域和计算域扩展出来的格子先存起来，后面再放进去  同时计算一下扩展的大小，存到偏移值上
      displayData.forEach(row => {
        row.forEach((cell, index) => {
          if (!colOffset[index]) colOffset[index] = 0

          if (cell.content.compareShowNameType && cell.content.compareShowNameType === 'periodtext_expand_prevyear') {
            if (Array.isArray(cell.data)) {
              if (colOffset[index] < cell.data.length - 1) {
                colOffset[index] = (cell.data.length - 1) * cell.colspan
              }

              cell.data.forEach((d, index) => {
                const newCell = this.$_deepClone(cell)
                newCell.offsetIndex = index
                newCell.variableKey = cell.variableKey + '-' + index
                newCell.content.coordinate = cell.content.coordinate + '-' + index
                newCell.content.keyName = cell.content.keyName + '-' + index
                newCell.content.oldKeyName = cell.content.keyName
                newCell.content.extend = '1'
                newCell.data = [String(d)]
                newCell.isTemp = true
                periodTextCellList.push(newCell)
              })
            }
          }

          if (exCellsKeys.includes(cell.content.coordinate)) {
            const exCellsMapList = exCellsMap[cell.content.coordinate] || []
            if (colOffset[index] < exCellsMapList.length - 1) {
              colOffset[index] = (exCellsMapList.length - 1) * cell.colspan
            }

            const dataArr = exCellsMapList.map(e => resData.tableResponse[e]?.data || resData.tableResponse[e]?.datas || [])
            dataArr.forEach((d, index) => {
              const newCell = this.$_deepClone(cell)
              newCell.offsetIndex = index
              newCell.variableKey = cell.variableKey + '-' + index
              newCell.content.coordinate = cell.content.coordinate + '-' + index
              newCell.content.keyName = cell.content.keyName + '-' + index
              newCell.content.oldKeyName = cell.content.keyName
              newCell.content.extend = '1'
              newCell.data = d
              newCell.isTemp = true
              periodCellList.push(newCell)
            })
          }
        })
      })

      // 包含额外扩展文本域数据的displayData
      let textExtCellDisplayData = []
      // 循环 插入数据
      displayData.forEach(row => {
        const list = row.filter(cell => cell.content.compareShowNameType && cell.content.compareShowNameType === 'periodtext_expand_prevyear' && !cell.isTemp)
        if (!list.length) {
          textExtCellDisplayData.push(row)
          return
        }

        const rowTmp = []
        row.forEach(cell => {
          if (cell.content.compareShowNameType && cell.content.compareShowNameType === 'periodtext_expand_prevyear' && !cell.isTemp) {
            periodTextCellList.forEach(e => {
              if (e.content.oldKeyName === cell.content.keyName) {
                rowTmp.push(e)
              }
            })
          } else {
            rowTmp.push(cell)
          }
        })
        textExtCellDisplayData.push(rowTmp)
      })

      console.log('偏移的数据', colOffset)
      // 包含额外扩展计算域文本域数据的displayData
      let timeTextExtCellDisplayData = []
      // 循环 插入数据
      textExtCellDisplayData.forEach(row => {
        const list = row.filter(cell => cell.content.timeArea && cell.content.timeArea === 'period_expand_prevyear' && !cell.isTemp)
        if (!list.length) {
          timeTextExtCellDisplayData.push(row)
          return
        }

        const rowTmp = []
        row.forEach(cell => {
          if (cell.content.timeArea && cell.content.timeArea === 'period_expand_prevyear' && !cell.isTemp) {
            periodCellList.forEach(e => {
              if (e.content.oldKeyName === cell.content.keyName) {
                rowTmp.push(e)
              }
            })
          } else {
            rowTmp.push(cell)
          }
        })
        timeTextExtCellDisplayData.push(rowTmp)
      })

      // 循环 根据偏移量更新单元格坐标
      timeTextExtCellDisplayData.forEach((row, rowIndex) => {
        row.forEach(cell => {
          if (!cell.start || !cell.end) return
          const start = cell.colIndex
          const end = cell.colIndex + cell.colspan - 1

          // 单元格范围内的偏移量
          let sum = 0
          for (let i = start; i <= end; i++) {
            colOffset[i] && (sum += colOffset[i])
          }
          // 需要计算前面已经偏移的量
          let offsetSum = 0
          for (let i = 0; i < start; i++) {
            colOffset[i] && (offsetSum += colOffset[i])
          }

          if (cell.isTemp) {
            cell.start && (cell.start[1] += (offsetSum + (cell.offsetIndex * cell.colspan)))
            cell.end && (cell.end[1] += (offsetSum + (cell.offsetIndex * cell.colspan)))
            cell.colIndex += (offsetSum + (cell.offsetIndex * cell.colspan))
            cell.columnIndex += (offsetSum + (cell.offsetIndex * cell.colspan))
            return
          }

          cell.start && (cell.start[1] += offsetSum)
          cell.end && (cell.end[1] += offsetSum)
          cell.colIndex += offsetSum
          cell.columnIndex += offsetSum
          cell.colspan += sum

          if (cell.content.extend === '2') {
            cell.end[1] += sum
            // 修改单元格数据
            for (let iRow = rowIndex; iRow < timeTextExtCellDisplayData.length; iRow++) {
              timeTextExtCellDisplayData[iRow].forEach(item => {
                if (item.content.compareShowNameType !== 'periodtext_expand_prevyear') return
                if (item.colIndex >= cell.colIndex && item.end[1] <= cell.end[1]) {
                  item.data = [cell.data.map(e => item.data[0])]
                  item.parentKey = cell.content.coordinate
                  item.extendParent = cell
                }
              })
            }
          }

          if (cell.colspan > 1) {
            cell.isMergeCell = true
            cell.end && (cell.end[1] = cell.start[1] + cell.colspan - 1)
          }
        })
      })

      // 把单元格扩展出来多余的空余补充
      const extDisplayData = []
      timeTextExtCellDisplayData.forEach((row, rowIndex) => {
        const rowTmp = []
        row.forEach((rowCell, index) => {
          rowCell.start && rowTmp.push(rowCell)
          for (let i = 0; i < rowCell.colspan; i++) {
            if (!rowTmp.find(e => e.colIndex === rowCell.colIndex + i)) {
              const cellTmp = this.$_deepClone(rowCell)
              cellTmp.start[1] += i
              cellTmp.end[1] = cellTmp.start[1]
              this.$delete(cellTmp, 'start')
              this.$delete(cellTmp, 'end')
              this.$delete(cellTmp, 'isMergeCell')
              this.$delete(cellTmp, 'x')
              this.$delete(cellTmp, 'y')
              this.$delete(cellTmp, 'i')

              cellTmp.type = 'merge-shadow-cell'
              cellTmp.extendParent = this.$_deepClone(rowCell)
              cellTmp.colIndex += i
              cellTmp.columnIndex += i
              cellTmp.data = ''
              cellTmp.colspan = 1
              rowTmp.push(cellTmp)
            }
          }
        })
        extDisplayData.push(rowTmp.sort((a, b) => a.colIndex - b.colIndex))
      })

      console.log('扩展出文本域和计算域的数据', timeTextExtCellDisplayData)
      console.log('其他格子适配后的数据', extDisplayData)
      return extDisplayData
    },
    getExportStyle() {
      const exportStyle = {}
      // 5941 导出时导出超链接 层级 斑马线样式
      const linkStyle = this.getHyperLink()
      exportStyle.linkStyle = Object.keys(linkStyle).length ? linkStyle : undefined

      exportStyle.zebraCrossingStyle = this.getZebraStripes({ isExport: true })

      const hierarchicalStyle = this.getHierarchyStyleList()
      if (hierarchicalStyle.length) {
        exportStyle.hierarchicalStyle = hierarchicalStyle
      } else {
        exportStyle.hierarchicalStyle = getDefaultHierarchyStyleList(this.themeType, this.isMobile)
      }

      const hierarchyStyleWithLeaf = this.getHierarchyStyleWithLeaf()
      exportStyle.hierarchyStyleWithLeaf = hierarchyStyleWithLeaf
      exportStyle.hierarchyVerticalBorder = this.tableConfig.hierarchyVerticalBorder

      const elementBackgroundColor = this.element?.themeMap?.[this.themeType]?.pcBgc
      // 从后台取
      const themeBg = {
        [THEME_TYPE.classicWhite]: 'rgba(255, 255, 255, 1)',
        [THEME_TYPE.darkBlue]: 'rgba(33, 45, 59, 1)',
        [THEME_TYPE.deepBlue]: 'rgba(24, 43, 75, 1)',
      }

      exportStyle.elementBackgroundColor = this.$_isTransparent(elementBackgroundColor) ? themeBg[this.themeType] : elementBackgroundColor

      return exportStyle
    },
    /**
     * 为了保证导出数据时，不影响已渲染表格，
     * 对表格配置进行数据重置
     */
    // getExportCopyTable(resData, displayData) {
    //   const { tableResponse } = resData
    //   // 浅拷贝数据，重置变量单元格的cell.data属性
    //   const copyTable = []
    //   displayData.forEach(rowData => {
    //     const cells = []
    //     rowData.forEach(cell => {
    //       if (cell.type === CELL_TYPE.variable) {
    //         // 清空data
    //         let data = []
    //         if (this.tableType !== TABLE_TYPE.tree) {
    //           data = this.$_getProp(tableResponse, `${cell.variableKey}.data`) ||
    //             this.$_getProp(tableResponse, `${cell.variableKey}.datas`) ||
    //             []
    //           // 数据后处理(比如 Group 处理)
    //           if (cell.content.aggType === 'GROUP') {
    //             let groupMergeData = (resData.tableResponse[`${cell.content.coordinate}_ID`] || {}).data
    //             data = utils.variableDataFormat(data, { cell, groupMergeData })
    //           }
    //         }
    //         const copyCell = Object.assign({}, cell, { data })
    //         cells.push(copyCell)
    //       } else {
    //         cells.push(cell)
    //       }
    //     })
    //     copyTable.push(cells)
    //   })
    //   return copyTable
    // },
    // 更新所有渲染配置
    updateDisplay(resData, others, option = {}) {
      // 清空临时数据
      this.initOptions(null, others)
      this.options.store = {}
      if (this.tableType === TABLE_TYPE.tree && resData.open) {
        // 层级表全部展开
        this.openTreeAllLevel(resData)
      } else {
        const { data, options } = this.parseResData(resData, option)
        this.sheetData = data
        this.renderOptions = options
        this.loadData(data)
        this.setLoading(false)
      }
    },
    /**
     * 没有更新单元格数据，仅更新部分配置，没有后台请求
     * @param resetConfig： 是否需要更新配置数据
     * @param canvasNoReset： 是否需要保留渲染信息，如滚动条位置
     */
    updateCanvasRender(resetConfig = false, canvasNoReset) {
      if (!this.sheetData || !this.renderOptions) {
        return void '没有渲染过，不能直接刷新表格渲染配置'
      }
      if (resetConfig) {
        this.resetCols()
        parseAfter(this.sheetData, this.renderOptions)
      }
      canvasNoReset && this.retainCanvasTempData(true)
      this.loadData(this.sheetData)
    },
    storeCols() {
      this.originalCols = { ...this.sheetData.cols }
    },
    resetCols() {
      this.sheetData.cols = Object.assign({}, this.originalCols)
    },
    updateRows() {
      // TODO 这个方法针对层级表存在问题
      if (!this.sheetData) {
        return {}
      }
      // 如果是层级表，默认仅解析第一层数据
      if ((this.tableType === TABLE_TYPE.tree) && this.renderOptions.groupByResponse) {
        this.renderOptions.statusFields.openTree = this.renderOptions.groupByResponse.open
      }
      this.sheetData.rows = {}
      // this.sheetData = tableParser(this.renderOptions)
      this.resetCols()
      parser(this.sheetData, this.renderOptions)
      parseAfter(this.sheetData, this.renderOptions)
      // this.retainCanvasTempData(true)
      this.loadData(this.sheetData)
      this.renderOptions.statusFields.openTree = false
    },
    updateCanvasSize() {
      this.retainCanvasTempData(true)
      this.originalCols && this.resetCols()
      this.sheetData && this.loadData(this.sheetData)
    },
    setImgExamples() {
      const imgs = []
      this.displayData.forEach(cells => {
        cells.forEach(cell => {
          const { content: { imageData, warningConditionList = [] } } = cell
          const createImg = (url) => new Promise((resolve) => {
                  url = this.$_getAssetsUrl(url)
                  const img = new Image()
                  img.src = url
                  img.crossOrigin = 'anonymous'
                  img.onload = () => {
                    this.imgExamples[url] = img
                    resolve(true)
                  }
                  img.onerror = (err) => {
                    console.error('表格图片加载失败')
                    resolve(err)
                  }
              })

          if (imageData) {
            const url = imageData.url
            if (this.imgExamples[url]) return
            imgs.push(createImg(url))
          }
          if (warningConditionList?.length) {
            warningConditionList.forEach(item => {
              if (item.warningStyleType !== WARNING_STYLE_TYPE_GRAPHICAL) return

              const { key } = item.warningGraphicalStyle
              if (!key) return

              const { name } = WARN_GRAPHICAL_SYMBOL_LIST.find(item => item.key === key) || { name: '' }
              if (!name) return

              imgs.push(createImg(`${STATIC_BASE_PATH.images}board/warnStyle/${name}`))
            })
          }
        })
      })
      return Promise.all(imgs)
    },
    // 是否显示滚动条
    isShowScrollbarCall() {
      let isFocus = this.isPreview && this.isMobile ? this.element.isFocus : true

      this.sdpSheet.isShowScrollbarCall(isFocus || this.enlargeVisible)
    },
    // 获取表头影响数据
    // getPreviewParams() {
    //   const data = {}
    //   if (this.groupSearchCell) {
    //     const { info } = this.groupSearchCell.cellEvt
    //     const { groupSearch, order, search } = this.groupSearchCell

    //     data[`${info.rIndex}-${info.cIndex}`] = groupSearch || {
    //        type: order, val: search || ''
    //     }
    //   }
    //   return data
    // },
    // sheet加载数据
    loadData(data, options = {}) {
      this.storeCols()
      let { height, width } = this.tableStyle || this.element.style
      this.tempWidth = this.$_getProp(this.element, 'style.width', '0px')
      this.tempHeight = this.$_getProp(this.element, 'style.height', '0px')
      if (this.options.statusFields.isShowAllHeight) {
        height = getRowsHeight(data.rows)
        // 最大限制2W 高度，防止canvas渲染奔溃
        height = Math.min(height, 30000)
        // data.isShowAllHeight = true
      }
      // console.log('width bug', 'loadData', this.element.id, this.sheetData.cols[0].width)
      // 宽高矫正
      if (parseInt(width) > 0) {
        this.tempWidth = width
      } else {
        width = this.tempWidth
      }
      if (parseInt(height) > 0) {
        this.tempHeight = height
      } else {
        height = this.tempHeight
      }

      let view = {
        height: () => parseInt(height),
        width: () => parseInt(width),
      }
      options.noReset = this.canvasNoReset || false
      // todo分页loading
      Object.assign(data, { bottomLoadable: this.bottomLoadable }, options)
      // cols: { 0: { width: 100 }, 1: { width: 500 }, 2: { width: 100 } }
      const defaultDat = {
        // [行, 列]
        freeze: [0, 0],
        [SCALE.key]: this.element.scale || window.devicePixelRatio,
        view,
        // 参数组件信息
        paramsComp: {
          typeComponents: this.boardQuestParams['typeComponents'] // 用于层级表样式区分
        },
        // 是否为大屏预览
        themeFullScreen: this.fullScreenPreview,
        // 斑马线
        zebraStripes: this.getZebraStripes(),
        // 表头样式
        tableHeaderStyle: this.getTableHeaderStyle(),
        // 层级是否使用单元格竖边框
        hierarchyVerticalBorder: this.tableConfig.hierarchyVerticalBorder,
        // 层级样式
        hierarchyStyleList: this.getHierarchyStyleList(),
        // 子层级样式跟随四层及以后层级样式
        hierarchyStyleWithLeaf: this.getHierarchyStyleWithLeaf(),
        isOpenRuler: this.getOpenRuler(),
        // 是否保留滚动条位置
        retainScroll: this.retainScroll,
        hyperLink: this.getHyperLink(),
        animationOption: this.animationOption,
        // getPreviewParams: this.getPreviewParams()
      }
      if (this.tableConfig.autoCalcColWidth) {
        const minWidth = this.tableConfig.minWidth || 155
        data = this.$_JSONClone(data)
        this.autoCalcColsWidth(data, { width, height }, minWidth)
      }
      setRowColWidth(data)

      this.setImgExamples().then(() => {
        let isHideFreezeHead = false
        const [ri] = data.freeze
        let fri = +ri
        let moveFn = (distance, evt) => {}
        // 处理容器表头表格
        if (this.sdpSheetHead) {
          // 没有冻结也产生一行表头数据用来处理预览时手动冻结表头
          if (!fri) fri += 1
          const cloneData = this.$_JSONClone(data)
          let rows = {
            len: fri
          }
          let totalHegth = 0

          for (let index = 0; index <= (fri - 1); index++) {
            rows[index] = cloneData.rows[index]
            totalHegth += cloneData.rows[index].height
          }
          cloneData.rows = rows

          this.sdpSheetHead.loadData(Object.assign({}, defaultDat, {
            view: {
              height: () => totalHegth,
              width: () => parseInt(width),
            },
          }, { ...this.$_JSONClone(cloneData) }))
          // 隐藏滚动条
          this.sdpSheetHead.isShowScrollbarCall(false)

          moveFn = this.sdpSheetHead.sheet.horizontalScrollbar.moveFn

          isHideFreezeHead = true
        }
        // 渲染表格
        if (this.sdpSheet) {
          const cloneData = this.$_JSONClone(data)

          // 处理表头固定的数据
          if (isHideFreezeHead) {
            for (let index = 0; index <= (fri - 1); index++) {

              Object.values(cloneData.rows[index].cells).forEach((cell) => {
                cell.text = ''
                cloneData.cellsConfig[cell.config].meta.type = CELL_TYPE.text
              })
            }
            // 联动横向滚动条
            const cloneMoveFn = this.sdpSheet.sheet.horizontalScrollbar.moveFn
            this.sdpSheet.sheet.horizontalScrollbar.moveFn = (distance, evt) => {
              cloneMoveFn(distance, evt)

              moveFn(distance, evt)
            }
          }

          this.sdpSheet.loadData(Object.assign({}, defaultDat, cloneData))

          this.isShowScrollbarCall()
        }
      })

      setTimeout(() => {
        // this.retainCanvasTempData(false)
        this.retainCanvasScroll(false)

        if (this.layoutRef === null) {
          this.layoutRef = this.$_getParentComponentByComponentName(SUPERNATANT_LAYOUT)
        }

        this.layoutRef && this.layoutRef.updateContainerHeight && this.layoutRef.updateContainerHeight()
      }, 300)
    },

    autoCalcColsWidth(SheetData, containerView, minWidth) {
      const { cols } = SheetData
      const columnWidths = []
      for (const key in cols) {
        if (cols[key]?.width !== undefined) {
          columnWidths.push(cols[key].width)
        }
      }
      const params = {
        columnWidths,
        minWidth,
        wrapperStyle: containerView
      }
      const colsArr = utils.getColWidth(params)
      const ret = {}
      colsArr.forEach((e, i) => {
        ret[i] = {
          width: e
        }
      })
      ret.len = colsArr.length
      console.log('containerView1', columnWidths, colsArr)
      console.log('containerView2', containerView.width, ret)
      SheetData.cols = ret
    },

    /**
     * canvas渲染后，调整单元格数据
     * @param cells [{ ri, ci. data: { text } }]
     */
    justCells(cells) {
      const { tableMap } = this.renderOptions || {}
      if (!tableMap.length) { return void 'canvas还没有开始渲染' }
      const options = this.renderOptions
      const targetCells = []
      cells.forEach(c => {
        let { sri, sci, rowLen, colLen, mergeColLen, mergeRowLen } = getCellRenderPos(c.ri, c.ci, options)
        for (let i = sri; i < sri + rowLen; i++) {
          for (let j = sci; j < sci + colLen; j++) {
            targetCells.push({ ri: i, ci: j, data: c.data })
          }
        }
      })
      this.sdpSheet && this.sdpSheet.justCells(targetCells)
    },
    // 获取序列
    getOpenRuler() {
      const { column = false, row = false } = this.$_getProp(this.tableConfig, 'showIndex', {})
      return {
        rulerX: row,
        rulerY: column,
      }
    },
    // 获取斑马线数组
    getZebraStripes(option = {}) {
      const { isExport = false } = option
      const themeType = this.themeType
      const isZebra = this.$_getProp(this.tableConfig, 'isZebra', false)
      const zebraType = this.$_getProp(this.tableConfig, 'zebraType', '')
      const gridThemeStyleMap = this.$_getProp(this.tableConfig, `gridThemeStyleMap.${themeType}`, {})

      if (isZebra) {
        let zebraCrossingStyle = null
        if (gridThemeStyleMap.zebraCrossingStyle && (zebraType !== 'standard' || this.isChart)) {
          zebraCrossingStyle = gridThemeStyleMap.zebraCrossingStyle
        } else if (typeof gridThemeStyleMap['zebraIndex'] === 'number') {
          zebraCrossingStyle = zebraCrossing[themeType][gridThemeStyleMap['zebraIndex']]
        } else {
          zebraCrossingStyle = zebraCrossing[themeType][0]
        }
        if (isExport) return zebraCrossingStyle
        const { oddLineColor, evenLineColor } = zebraCrossingStyle || {}
        if (oddLineColor && evenLineColor) {
          return [oddLineColor, evenLineColor]
        }
      }
      if (isExport) return undefined
      return []
    },
    getTableHeaderStyle() {
      const themeType = this.themeType
      const gridThemeStyleMap = this.$_getProp(this.tableConfig, `gridThemeStyleMap.${themeType}`, {})

      let tableHeaderStyle
      if (gridThemeStyleMap.tableHeaderStyle) {
        tableHeaderStyle = gridThemeStyleMap.tableHeaderStyle
      }
      return tableHeaderStyle
    },
    getHierarchyStyleList() {
      const themeType = this.themeType
      const hierarchyStyleList = this.$_JSONClone(this.$_getProp(this.tableConfig, 'hierarchyStyleList', []))
      const gridThemeStyleMap = this.tableConfig.gridThemeStyleMap

      if (!hierarchyStyleList.length || (themeType === THEME_TYPE.classicWhite && !gridThemeStyleMap)) {
        return hierarchyStyleList
      }

      const currentThemeStyleMap = this.$_getProp(gridThemeStyleMap, themeType, {})
      const hierarchyThemeStyleList = currentThemeStyleMap['hierarchyStyleList'] || []
      const { BACKGROUND_COLOR, COLOR_LEVEL_LIST, BORDER_COLOR_LEVEL_LIST } = HIERARCHY_STYLE['themeStyleMap'][themeType]

      hierarchyStyleList.forEach((styleObj, index) => {
        const themeStyle = hierarchyThemeStyleList[index] || {}
        const defaultStyle = {
          'color': COLOR_LEVEL_LIST[index],
          'background-color': BACKGROUND_COLOR,
          'border-color': BORDER_COLOR_LEVEL_LIST[index],
        }

        useThemeStyle(styleObj, themeStyle, defaultStyle)
      })
      return hierarchyStyleList
    },
    getHierarchyStyleWithLeaf() {
      const themeType = this.themeType
      const defaultValue = true
      const hierarchyStyleWithLeaf = this.$_getProp(this.tableConfig, 'hierarchyStyleWithLeaf', defaultValue)
      const gridThemeStyleMap = this.tableConfig.gridThemeStyleMap

      if (themeType === THEME_TYPE.classicWhite && !gridThemeStyleMap) {
        return hierarchyStyleWithLeaf
      }

      const currentThemeStyleMap = this.$_getProp(gridThemeStyleMap, themeType, {})
      const hierarchyStyleWithLeafCurrentTheme = currentThemeStyleMap.hasOwnProperty('hierarchyStyleWithLeaf') ? currentThemeStyleMap['hierarchyStyleWithLeaf'] : defaultValue

      return hierarchyStyleWithLeafCurrentTheme
    },
    getHyperLink() {
      const themeType = this.themeType
      const hyperlink = this.$_getProp(this.tableConfig, 'hyperlink', {})
      let linkStyle = {}
      if (Object.keys(hyperlink).length > 0 && hyperlink.enable) {
        const { style } = hyperlink
        const defaultHyperlinkStyle = this.isMobile ? HYPER_LINK_STYLE.MOBILE_STYLE : HYPER_LINK_STYLE.PC_STYLE
        Object.keys(style).forEach(v => {
          if (v === 'color') {
            linkStyle[v] = style[v][themeType] || defaultHyperlinkStyle.color[themeType]
          } else if (v === 'font-weight' || v === 'font-style') {
            linkStyle[v] = style[v] || 'normal'
          } else if (v === 'text-decoration') {
            linkStyle[v] = style[v] || 'none'
          } else {
            linkStyle[v] = style[v]
          }
        })
      }
      return linkStyle
    },
    // 展开子树
    async expandChildTree(arg) {
      const { cellEvt, options } = arg
      const path = [...cellEvt.cell.path]
      this.retainCanvasTempData(true)
      this.retainCanvasScroll(true)
      let parentTree = options.groupByResponse.dataTree[path.shift()]
      path.forEach(e => {
        parentTree = parentTree.children[e]
      })
      if (parentTree.showChildren) {
        // 隐藏子树
        removeChildTree(Object.assign(arg, { parentTree }))
        this.updateCanvasRender()
      } else {
        // 展开子树
        // this.setLoading(true)
        const { cell, info } = cellEvt
        const table = this.sdpSheet.sheet.table
        const loadingId = `${cell.id}_${info.rIndex}_${info.cIndex}`
        // 加载状态
        table.pushLoadingId(loadingId)
        // 禁用滚动
        this.sdpSheet.sheet.disableScroll()
        const newData = await expandChildTree(arg)
        if (newData) {
          this.loadData(newData)
        }
        setTimeout(() => {
          // this.setLoading(false)
          table.removeLoadingId(loadingId)
          if (!table.hasLoading()) {
            this.sdpSheet.sheet.restoreScroll()
          }
          this.sdpSheet.render()
        }, 300)
      }
    },
    clearAllLoading() {
      this.sdpSheet.sheet.table.clearLoading()
    },
    // 根据行索引获取rows中的指定行数据
    getRowByRIndex(rIndex) {
      const { cells } = this.sheetData.rows[rIndex] || {}
      if (cells) {
        const { merges = {} } = this.sdpSheet.data
        const rows = {}
        Object.entries(cells).forEach(([index, data]) => {
          const { meta, ...all } = this.sheetData.cellsConfig[data.config] || {}
          rows[index] = Object.assign({}, meta, all, data)
        })
        if (merges._) {
          merges._.filter(({ eri, sri }) => sri <= rIndex && eri >= rIndex).forEach(({ sri, sci, eci }) => {
            const { cells } = this.sheetData.rows[sri] || {}
            Object.entries(cells).forEach(([index, data]) => {
              if (sci <= index && eci >= index && !rows[index]) {
                const { meta, ...all } = this.sheetData.cellsConfig[data.config] || {}
                rows[index] = Object.assign({}, meta, all, data)
              }
            })
          })
        }
        return this.$_JSONClone(rows)
      }
      return null
    },
    getSheetData() {
      return this.sheetData || {}
    },
    getRenderOptions() {
      return this.renderOptions || {}
    },
    getCellConfigByProp(val, propName) {
      if (this.renderOptions) {
        for (let i = 0; i < this.renderOptions.tableMap.length; i++) {
          const rows = this.renderOptions.tableMap[i]
          for (let j = 0; j < rows.length; j++) {
            if (this.$_getProp(rows, `${j}.${propName}`) === val) {
              return rows[j]
            }
          }
        }
      } else {
        return {}
      }
    },
    /**
     * 导出数据
     * @param res 后台请求数据
     * @param others options 初始化数据
     * @param exportCallback 导出完成回调函数
     */
    exportRenderData(res, others, exportCallback) {
      others.isExport = true
      this.initOptions(null, others)
      const { statusFields } = this.options
      statusFields.isExport = true
      if (this.tableType === TABLE_TYPE.tree) {
        const { dataTree, queryKey } = res
        const params = {
          queryKey,
          elementId: this.element.id,
        }
        this.getAllTreeChildren(dataTree, params, () => {
          const { data, options } = this.parseResData(res)
          statusFields.isExport = false
          console.log(data)
          exportCallback({ data, options })
        })
      } else {
        const { data, options } = this.parseResData(res)
        statusFields.isExport = false
        exportCallback({ data, options })
      }
    },

    openTreeAllLevel(res) {
      const { statusFields } = this.options
      const { dataTree, queryKey } = res
      const params = {
        queryKey,
        elementId: this.element.id,
      }

      this.isMobile && (statusFields.openTree = true)

      this.getAllTreeChildren(dataTree, params, () => {
        const { data, options } = this.parseResData(res)
        statusFields.openTree = false
        this.sheetData = data
        this.renderOptions = options
        this.loadData(data)
        this.setLoading(false)
      })
    },
    // 获取所有层级树数据
    async getAllTreeChildren(dataTree, params, cb) {
      // 根据层级展开导出选择层级的数据
      // const { hierarchicalExpandActive = false, hierarchicalExpand = '10' } = this.tableConfig

      const ids = []
      dataTree.forEach(child => {
        !child.lastLevel && ids.push(child.id)

        // if (!hierarchicalExpandActive || hierarchicalExpand === 'all') {
        // !child.lastLevel && ids.push(child.id)
        // } else if (hierarchicalExpandActive && parseInt(child.level) < hierarchicalExpand) {
        //   !child.lastLevel && ids.push(child.id)
        // }
      })
      if (!ids.length) {
        cb()
        return false
      }
      params.ids = ids
      const resChildData = await getLevelBySubNodes(this.api, params)
      const nextLevelTree = []
      dataTree.forEach(item => {
        if (!item.lastLevel) {
          item.children = resChildData[item.id]
          nextLevelTree.push(...item.children)
        }
      })
      this.getAllTreeChildren(nextLevelTree, params, cb)
    },
    clickHandler(type, cellEvt) {
      if (!cellEvt) {
        console.error('没有单元格数据，退出执行下面代码')
        return false
      }
      // 解析层单独处理的点击事件：层级表展开
      if (type === CLICK_TYPE.tree) {
        // 保留表头状态
        this.retainCanvasTempData(true)
        this.retainCanvasScroll(true)
        // 保留滚动条状态
        if (cellEvt.cell.lastLevel) {
          console.warn('最后一层不允许被展开')
          return void '最后一层不允许被展开'
        }
        // TODO 加载子树, 多次点击
        this.expandChildTree({
          cellEvt, data: this.sheetData, options: this.renderOptions
        })
      } else if (type === CLICK_TYPE.fold) {
        this.retainCanvasTempData(true)
        this.retainCanvasScroll(true)
        // 折叠展开
        this.CollapseColHandler(cellEvt)
      } else if (type === CLICK_TYPE.exclude) {
        // 维护"去除"数据
        this.clickHandlerByExclude(cellEvt)
      } else {
        // 交互和跳转
        this.$emit('cell-click', {
          type,
          cellEvt,
        })
      }
    },
    // 是否保canvas表头状态
    retainCanvasTempData(val) {
      this.canvasNoReset = val
    },
    // 是否保留滚动条状态： 默认值： false
    // 目前数据懒加载，层级表展开需要保留, 表头列的隐藏，折叠展开
    retainCanvasScroll(val) {
      this.retainScroll = val
    },
    // 表头单元格交互点击事件，在render中调用
    headerClickHandler(type, cellEvt) {
      this.retainCanvasScroll(true)
      this.retainCanvasTempData(true)
      const { cell, info } = cellEvt
      const cellConfig = this.getCellConfigByProp(cell.id, 'id')

      // 层级表维度列
      const isTreeDimensionCol = this.tableType === TABLE_TYPE.tree && (info.cIndex === this.renderOptions.treeCurConfig.dimension_ci)
      const sendData = {
        columnIndex: cellConfig.columnIndex || info.cIndex,
        rowIndex: cellConfig.rowIndex || info.rIndex,
        isTreeDimensionCol,
        params: {
          search: info.value || '',
          order: info.sort || '',
        },
      }

      const topCells = getParentCells(this.sheetData.rows, info, this.sheetData?.cellsConfig, true) || []
      if (cellConfig.type === CELL_TYPE.variable) {
        topCells.push(cell)
      }
      const extendParentText = topCells.length ? topCells.map(cell => cell.text).filter(e => e).join('@##@') : 'SDP_None'

      // 处理横向扩展情况
      if (cellConfig.extend === '2') {
        sendData.extendParentText = extendParentText // 如果单元格内容为空字符串，根据后台要求传入特定字符
      } else if (this.renderOptions.extendMap.colData.size) {
        // 表格为被横向扩展单元格
        const { extendMap, mergeMap } = this.renderOptions
        let cIndex = cellConfig.columnIndex
        const mergeData = mergeMap.colRangeMap.get(cellConfig.columnIndex)
        mergeData && (cIndex = mergeData.start)
        const extendData = extendMap.colData.get(cIndex)
        if (extendData) {
          const data = extendData.data
          if (data && Array.isArray(data)) {
            // 空字符串和后台约定传特殊字符
            sendData.extendParentText = extendParentText
          }
        }
      }

      // 目前只允许对同一列进行搜索或排序，因此需要清空其余表头的状态
      // this.retainCanvasTempData(false)
      // this.updateCanvasSize()

      setTimeout(() => {
        // this.retainCanvasTempData(true)
        this.setCellParams({ sendData, cellConfig, cellEvt })
      }, 300)
    },
    // 折叠展开
    CollapseColHandler(cellEvt) {
      const { cell, info } = cellEvt
      const { rIndex, cIndex } = info
      const { rows, cellsConfig } = this.sheetData
      const foldCell = rows[rIndex].cells[cIndex]
      foldCell.folded = !foldCell.folded
      const { collapseHeaderRow, collapseRows } = this.renderOptions.store
      if (foldCell.folded) {
        // 展开
        const insetRows = {}
        let diff = 1 // 被隐藏行展开后的索引调整值
        if (collapseHeaderRow) {
          diff = 2
          insetRows['0'] = collapseHeaderRow
          insetRows['1'] = collapseRows[rIndex]
        } else {
          insetRows['0'] = collapseRows[rIndex]
        }
        // 调整隐藏行的索引
        const newCollapseRows = {}
        for (let i in collapseRows) {
          i = i - 0
          if ((i > rIndex) && rows.hasOwnProperty(i) && !isNaN(i)) {
            newCollapseRows[i + diff] = collapseRows[i]
          } else {
            newCollapseRows[i] = collapseRows[i]
          }
        }
        this.renderOptions.store.collapseRows = newCollapseRows
        insertRows(rows, rIndex + 1, insetRows)

        // Object.entries(insetRows).forEach(([r, { cells }]) => {
        //   Object.entries(cells).forEach(([c, cell]) => {
        //     if (cellsConfig[cell.config]?.meta?.content?.styleConfig?.lastBorderType === 'merge') {
        //       c = +c
        //       r = +r + rIndex + 1

        //       const config = JSON.parse(JSON.stringify(cellsConfig[cell.config]))
        //       const style = config.style
        //       const [rm, cm] = config.merge || [0, 0]
        //       style['border-style'] = `solid solid solid solid`

        //       // 上
        //       const rt = r - 1
        //       let tCell = rows?.[rt]?.cells?.[c]

        //       if (tCell) {
        //         const s = cellsConfig[tCell.config].style
        //         const bs = s['border-style'].split(' ')

        //         if (bs[2] !== 'none') {
        //             const borderStyle = style['border-style'].split(' ')
        //             style['border-style'] = `none ${borderStyle[1]} ${borderStyle[2]} ${borderStyle[3]}`
        //         }
        //       }

        //       // 右
        //       const cr = c + cm + 1
        //       let rCell = rows?.[r]?.cells?.[cr]

        //       if (rCell) {
        //         const s = cellsConfig[rCell.config].style
        //         const bs = s['border-style'].split(' ')

        //         if (bs[3] !== 'none') {
        //           const borderStyle = style['border-style'].split(' ')
        //           style['border-style'] = `${borderStyle[0]} none ${borderStyle[2]} ${borderStyle[3]}`
        //         }
        //       }
        //       // 下
        //       const rb = r + rm + 1
        //       let bCell = rows?.[rb]?.cells?.[c]

        //       if (bCell) {
        //         const s = cellsConfig[bCell.config].style
        //         const bs = s['border-style'].split(' ')

        //         if (bs[0] !== 'none') {
        //           const borderStyle = style['border-style'].split(' ')
        //           style['border-style'] = `${borderStyle[0]} ${borderStyle[1]} none ${borderStyle[3]}`
        //         }
        //       }
        //       // 左
        //       const cl = c - 1
        //       let lCell = rows?.[r]?.cells?.[cl]

        //       if (lCell) {
        //         const s = cellsConfig[lCell.config].style
        //         const bs = s['border-style'].split(' ')

        //         if (bs[1] !== 'none') {
        //           const borderStyle = style['border-style'].split(' ')
        //           style['border-style'] = `${borderStyle[0]} ${borderStyle[1]} ${borderStyle[2]} none`
        //         }
        //       }

        //       cellsConfig[cell.config] = config
        //     }
        //   })
        // })
      } else {
        let diff = collapseHeaderRow ? 2 : 1 // 被隐藏行展开后的索引调整值
        // 调整隐藏行的索引
        const newCollapseRows = {}
        for (let j in collapseRows) {
          j = j - 0
          if ((j > rIndex) && rows.hasOwnProperty(j) && !isNaN(j)) {
            newCollapseRows[j - diff] = collapseRows[j]
          } else {
            newCollapseRows[j] = collapseRows[j]
          }
        }
        this.renderOptions.store.collapseRows = newCollapseRows
        removeRows(rows, rIndex + 1, collapseHeaderRow ? 2 : 1)
      }
      this.updateCanvasRender(false)
    },
    // 滚动到底触发事件
    scrollEndHandler(data) {
      // 全屏预览下，禁止懒加载
      if (data.type === SCROLL_TYPE.y && !this.fullScreenPreview) {
        this.$emit('grid-event-bus', data)
      }
    },
    // canvas渲染完成
    canvasRenderFinish() {
      // console.log('canvas渲染完成')
    },
    _getHideCol(config) {
      const arr = [...(config.collapseColHide || []), ...(config?.metricHide?.col || []), ...(this.options?.headerConfig?.hideColumnList || []), ...this.hideColumnList]
      return Array.from(new Set(arr))
    },
    _getHideRow(config) {
      const hideRows = Array.isArray(config.hideRows) ? config.hideRows.map(e => e - 1) : []
      const arr = [...hideRows, ...(config?.metricHide?.row || []), ...this.hideRowList]
      return Array.from(new Set(arr))
    },
    // 初始化容器表头
    initTableHeader(isOpen = false) {
      this.isShowRenderContainerHead = isOpen
      this.$nextTick(() => {
          if (isOpen) {
            this.sdpSheetHead = Object.freeze(createSdpSheet(this.$refs.renderContainerHead, { showGrid: false, row: { height: this.isChart ? 32 : 20 } }, { vm: this, isShowAllHeight: this.options.statusFields.isShowAllHeight }))
          } else {
            // eslint-disable-next-line no-unused-expressions
            this.sdpSheetHead?.destroyed()
            this.sdpSheetHead = null
          }
      })
    },
    // 设置容器表头位置
    setTableHeader({ id, scrollTop, componentBox, isFreezeLineTopSuction }) {
      const renderContainerHead = this.$refs.renderContainerHead
      if (!renderContainerHead) return

      const [ri] = this?.sheetData?.freeze || [0, 0]
      const fri = +ri
      if (!isFreezeLineTopSuction || !fri) {
        renderContainerHead.style.top = 0
        return
      }

      if (this.element._containerId !== id) return

      const headerHeight = renderContainerHead.offsetHeight

      const active = componentBox.map(item => {
        const id = item.getAttribute('loadTarget')
        const [el] = item.getElementsByClassName(`${id}_container_header`)

        const top = item.offsetTop
        const range = item.offsetHeight

        const min = top + (el ? el.offsetTop : 0)
        const max = top + range - headerHeight

        return {
          showRange: [min, max],
          id,
          scrollTop: scrollTop - min
        }
      }).find(({ id, showRange: [min, max] }) => {
        return id === this.element.id && min <= scrollTop && max >= scrollTop
      })

      if (!active) {
        renderContainerHead.style.top = 0
        return
      }

      renderContainerHead.style.top = active.scrollTop + 'px'
    }
  },
  mounted() {
    const _this = this
    const el = this.$refs.renderContainer
    this.retainScroll = false
    this.initOptions()
    const sdpSheet = createSdpSheet(el, { showGrid: false, row: { height: this.isChart ? 32 : 20 } }, { vm: _this, isShowAllHeight: this.options.statusFields.isShowAllHeight })
    this.sdpSheet = Object.freeze(sdpSheet)
    // 简单表格编辑时禁用交互和跳转
    if (this.isChartSet) {
      this.sdpSheet.setCellAttr({ isDisabled: this.isChartSet })
      this.sdpSheet.setData({ isResizeCol: true, resizeColFinishCb: (cRect, distance) => { _this.$parent.$emit('handColResize', { cRect, distance }) } })
    }
    const cbs = {
      [CB_METHODS_NAME.scrollEnd]: (data) => {
        this.scrollEndHandler(data)
      }
    }
    // 挂载回调函数
    this.sdpSheet.cbMounted(cbs)

    // 异步处理，innerVerticalContainer改变在mounted还没确定设值
    setTimeout(() => {
      this.initTableHeader(this.tableConfig.innerVerticalContainer)
    })

    this.sdpBus.$on(EVENT_BUS.SET_TABLE_HEADER, this.setTableHeader)
  },
  beforeDestroy() {
    // eslint-disable-next-line no-unused-expressions
    this.sdpSheet?.destroyed()
    // eslint-disable-next-line no-unused-expressions
    this.sdpSheetHead?.destroyed()
    this.sdpBus.$off(EVENT_BUS.SET_TABLE_HEADER, this.setTableHeader)
  }
}
</script>

<style lang="scss" scoped>
  $sheet: 'sdp-sheet';
  .isShade:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
  }
  .fixedStyle {
    position: absolute;
    top: 0;
    z-index: 2;
    /deep/ .#{$sheet} {
      background-color: var(--container-head-bg)
    }
  }
  .cover {
    background-color: #fefefe;
    @include sdp-background_0($key: 'partScreenBgc');
  }
  .cover::before {
    content: '';
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: var(--container-bg);
    z-index: -1;
  }
  .cover::after {
    content: '';
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: var(--large-screen-bg);
    z-index: -2;
  }
  .render-container {
    /deep/ .disabled {
      pointer-events: none;
      cursor: default;
    }
    /deep/ .sheet {
        /* resizer **/
        .#{$sheet}-resizer {
          position: absolute;
          z-index: 11;
          .#{$sheet}-resizer-hover {
            background-color: rgba(75, 137, 255, .25);
          }
          .#{$sheet}-resizer-line {
            position: absolute;
          }
          &.horizontal {
            cursor: row-resize;
            .#{$sheet}-resizer-line {
              border-bottom: 2px dashed rgb(75, 137, 255);
              left: 0;
              bottom: 0;
            }
          }
          &.vertical {
            cursor: col-resize;
            .#{$sheet}-resizer-line {
              border-right: 2px dashed rgb(75, 137, 255);
              top: 0;
              right: 0;
            }
          }
        }
        .hide {
          display: none
        }
        position: relative;
        overflow: hidden;
        .#{$sheet}-tooltip {
          font-family: inherit;
          position: absolute;
          padding: 5px 10px;
          color: #222;
          border-radius: 2px;
          background:#fff;
          font-size: 12px;
          border: 1px solid #222;
          z-index: 201;
        }
       .#{$sheet}-moremenu, .#{$sheet}-contextmenu {
          position: absolute;
          box-shadow: 1px 2px 5px 2px rgba(51, 51, 51, 0.15);
          @include sdp-mixin-style($type:(
            backgroundColor:(contextmenuBg:true),
            color: (contextmenuColor:true),
          ), $self: (
            sdp-classic-white: (
              contextmenuBg: #fff,
              contextmenuColor: rgba(0, 0, 0, 0.9)
            ),
            sdp-dark-blue: (
              contextmenuBg: #07111D,
              contextmenuColor: #E6E7EC,
            )
          ));
          z-index: 100;
          width: 110px;
          height: 40px;
          pointer-events: auto;
          overflow: auto;
         .#{$sheet}-item {
           user-select: none;
           background: $color-DJSXBJS;
           color: $color-DJSXWZ;
           outline: none;
           height: 40px;
           line-height: 36px;
           list-style: none;
           padding: 2px 10px;
           cursor: default;
           text-align: left;
           font-size: 16px;
           .icon-sdp-sousuo{
             //color: $color-DJSXFDJ !important;
           }
         }
        }
        .#{$sheet}-contextmenu {
          height: 30px;
          width: 60px;
          .#{$sheet}-item {
            text-align: center;
            cursor: pointer;
            height: 30px;
            line-height: 26px;
            font-size: 14px;
          }
        }
        .#{$sheet}-moremenu {
          .#{$sheet}-item {
            // 设置图标大小和img大小
            i {
              font-size: 16px;
              padding-right: 2px;
            }
            img {
              height: 20px;
            }
          }
        }
      .#{$sheet}-selector-area {
          position: absolute;
          border: 2px solid #4b89ff;
          background: rgba(75, 137, 255, 0.1);
      }
      .#{$sheet}-search {
        width: 100%;
        height: 100%;
        position: absolute;
        background: $color-BTSXKDS !important;
        border: 1px solid $color-BTSXBK !important;
        color: $color-BTSXTSWZ !important;
        font-size: 16px;
        overflow: hidden;
        border-radius: 2px;
        padding: 0 10px;
        outline: none;
        user-select:text !important;
        -webkit-user-select:text !important;
      }
      input::-webkit-input-placeholder {
        color: $color-BTSXTSWZ !important;
      }
      .#{$sheet}-search-box-wrap {
        position: absolute;
      }
      .#{$sheet}-search-box {
        position: absolute;
        display: flex !important;
        width: 100%;
        height: 100%;
        align-items: center;
        input {
          padding-left: 42px;
        }
      }
      .#{$sheet}-search-icon {
        cursor: pointer;
        z-index: 1;
        font-size: 22px;
        color: $color-BTSXFDJ !important;
        //[data-theme = 'sdp-dark-blue'] & {
        //  color: #F6F6F6;
        //}
      }
      .#{$sheet}-search-tip {
        cursor: auto;
        z-index: 1;
        font-size: 22px;
        color: var(--sdp-color-MZS, --sdp-zs);
        //color: #553cce;
      }
      .#{$sheet}-load {
        width: 100%;
        height: 25px;
        position: absolute;
        bottom: 0;
        left: 0;
        text-align: center;
        img {
          width: 25px;
        }
      }
      .#{$sheet}-hover-area {
          position: absolute;
          border: 2px solid red;
          background: rgba(75, 137, 255, 0.1);
      }
      .#{$sheet}-overlayer {
        position: absolute;
        top: 0;
        left: 0;
      }
      .gestures {
        cursor: pointer!important;
      }
      .#{$sheet}-table {
        vertical-align: bottom;
      }
      /* scrollbar */
      .#{$sheet}-scrollbar {
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 10;
      }
      .#{$sheet}-scrollbar-bar {
        position: absolute;
        z-index: 11;
        /*background-color: #F2F4F8;*/
        background-color: transparent;
        opacity: 0.9;
        //@include table-scroll('bar');
        cursor: pointer;
        border-radius: 3px;
        .#{$sheet}-scrollbar-thumb {
          /*background-color: rgba(0, 0, 0, 0.5);*/
          background-color: rgba(111,128,148,.5);

          //@include table-scroll();
          border-radius: inherit;
          cursor: pointer;
        }
        &.horizontal-bar {
          width: 100%;
          right: 6px;
          bottom: 0;
        }
        &.vertical-bar {
          height: 100%;
          right: 0;
          bottom: 6px;
        }
      }
      .#{$sheet}-scrollbar-covering {
         position: absolute;
         z-index: 10;
         background: transparent;
         //background-color: #fff;
         //@include table-scroll('bar');
         border-radius: inherit;
         &.horizontal-covering {
           width: 100%;
           bottom: 0;
           right: 0;
         }
         &.vertical-covering {
           height: 100%;
           right: 0;
           bottom: 0;
         }
       }
      .#{$sheet}-scrollbar.horizontal {
        right: 6px;
        overflow-x: scroll;
        overflow-y: hidden;
      }
      .#{$sheet}-scrollbar.vertical {
        bottom: 6px;
        overflow-x: hidden;
        overflow-y: scroll;
      }
    }
  }
</style>
