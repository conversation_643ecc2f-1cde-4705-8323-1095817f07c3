import Vue from 'vue'
import { h, Element } from '../component/element'
import { cssPrefix } from '../config'
import Table from './table'
import Scrollbar from './scrollbar'
import { bind, unbind } from './event'
// import tooltip from './tooltip'
import MoreMenu from './moremenu'
import { HEADER_ICON } from './constant'
import { RENDER_CELL_TYPE, TABLE_STATUS } from '../../../common/constant'
import { CLICK_TYPE } from '../../constant'
import { debounce, off, on } from '../core/helper'
import { GetSlideDirection } from 'packages/assets/utils/methodParams'
import Scroll from '../core/scroll'
import { STATIC_BASE_PATH } from 'packages/assets/constant'
import Resizer from './resizer'
import ContextMenu from './contextmenu'
import DataProxy from '../core/data_proxy'
import Animation from './animation'
import {isMobileTabletFun} from '../../../../board/mixins/boardScaleMixin'
import {BOARD_SCALE_BASE_WIDTH, BOARD_SCALE_MIN_WIDTH} from '../../../../board/utils/constant'
import { EVENT_TYPE } from '../../../../../constants'

const hasTouch = isMobileTabletFun(BOARD_SCALE_MIN_WIDTH, BOARD_SCALE_BASE_WIDTH)
function getOffsetXY(evt) {
  const { isMobileApp, overlayerEl } = this
  const { rows, cols, isOpenRuler: { rulerX, rulerY } } = this.data
  const { x, y } = overlayerEl.el.getBoundingClientRect()
  let offsetX = evt.offsetX
  let offsetY = evt.offsetY
  if (isMobileApp) {
    evt?.touches?.[0]?.clientX && (offsetX = evt.touches[0].clientX - x)
    evt?.touches?.[0]?.clientY && (offsetY = evt.touches[0].clientY - y)
  }

  if (hasTouch && !isMobileApp) {
    const boardScale = (window.innerWidth / BOARD_SCALE_BASE_WIDTH).toFixed(4)

    evt?.touches?.[0]?.clientX && (offsetX = (evt.touches[0].clientX - x) / boardScale)
    evt?.touches?.[0]?.clientY && (offsetY = (evt.touches[0].clientY - y) / boardScale)
  }

  return {
    offsetRulerX: rulerX ? offsetX - cols.indexWidth : offsetX,
    offsetRulerY: rulerY ? offsetY - rows.indexHeight : offsetY,
    offsetX,
    offsetY,
  }
}

function setAreaOffset(v, cell) {
  const { ci, ri } = v
  const { data } = this
  const { merges = [0, 0] } = cell
  // 设置点击范围
  data.selector.setIndexes(ri, ci, merges)
  // TODO areaEl
  // this.areaEl.offset({
  //   width: width,
  //   height: height,
  //   left: left,
  //   top: top,
  // }).show()
}
// function searchWidth() {
//   const { cols: { indexWidth }, scroll: { x, cDifference }, isOpenRuler: { rulerX } } = this.data
//   let width = rulerX ? indexWidth : 0
//   width += x
// }
// 点击表头搜索
function searchClick({ cell, cellRect }) {
  const { data } = this
  const { style, type } = cell
  this.searchCellRect = cellRect
  if (type === RENDER_CELL_TYPE.header) {
    let { ri, ci, width: w, height: h, left: l, top: t } = cellRect
    if (style.border) {
      const { bottom, left, right, top } = style.border
      if (bottom) h -= bottom[0]
      if (left) {
        w -= left[0]
        l += left[0]
      }
      if (right) w -= right[0]
      if (top) {
        h -= top[0]
        t += top[0]
      }
    }
    const { iconShow } = cell.headerOptions || {}
    if (iconShow) {
      const { search, sort } = iconShow
      // if (search && sort) {
      //   w -= 40
      // } else if (search) {
      //   w -= 20
      // }
      if (search) {
        setTimeout(() => {
          const { left: elL, top: elT } = this.searchBoxWrapEl.offset()
          const { width: tableWidth } = this.tableEl.offset()
          if (elL === l && elT === t && this.searchBoxWrapEl.css('display') === 'block') {
            this.searchBoxWrapEl.hide()
          } else {
            const { val } = data.headerData[`${ri}-${ci}`] || {}
            this.searchBoxWrapEl.offset({
              width: w,
              height: h,
              left: l > tableWidth ? l - data.scroll.x : l,
              top: t,
            }).show()
            this.searchEl.val(val || '').focus()
          }
        })
      }
    }
  }
}

// 点击类型
function clickType(cell, cellRect, evt) {
  const { data, moreMenu, table, data: { vmSettings: { vm: { themeData = {} } } } } = this
  const { expandable, type, lastLevel, headerOptions = {}, clickable: { drill = false, jumper = false, link = false }, isSkip = false, content: { exclude } } = cell
  const { iconOrder } = headerOptions
  let { offsetRulerX, offsetRulerY, offsetY, offsetX } = getOffsetXY.call(this, evt)
  const { ri, ci } = cellRect
  const { x, y } = data.scroll
  const [rif, cif] = data.freeze
  const site = data.gesturesSite[`${ri}-${ci}`] || {}
  const offX = cif > ci ? offsetRulerX : offsetRulerX + x
  const offY = rif > ri ? offsetRulerY : offsetRulerY + y
  const typeArr = Object.keys(site).filter(key => {
    const { sx, sy, ex, ey } = site[key]
    return offX >= sx && offX <= ex && offY >= sy && offY <= ey
  })
  const { themeFullScreen } = themeData
  // 开启大屏主题颜色
  if (typeArr.length) {
    let func = {
      [RENDER_CELL_TYPE.treeCtrl]() {
        if (expandable && !lastLevel) {
          this.click(CLICK_TYPE.tree, { ri, ci })
        }
      },
      [HEADER_ICON.more]() {
        if (Array.isArray(iconOrder) && iconOrder[1]) {
          const { val, type } = data.headerData[`${ri}-${ci}`] || {}
          const moreMenu_this = moreMenu.setPosition(offsetX, offsetY).showEl(iconOrder[1]).hideEl(iconOrder[0])
          const { defaultThemeColor: { activeColor, defaultColor } } = this.table
          if (iconOrder[1] === HEADER_ICON.sort) {
            const dom = moreMenu_this[HEADER_ICON.sort].children()
            dom.forEach((el, i) => {
              let color = defaultColor
              if (type === 'asc' && !i) {
                color = activeColor
              } else if (type === 'desc' && i) {
                color = activeColor
              }
              el.style.color = color
            })
          } else {
            moreMenu_this[HEADER_ICON.search].css('color', val ? activeColor : defaultColor)
          }
          evt.stopPropagation()
        }
      },
      [HEADER_ICON.sort]() {
        // 排序
        const headerData = data.headerData
        let headerItem = headerData[`${ri}-${ci}`] || {}
        let sort = headerItem.type === 'desc' ? 'asc' : 'desc'
        // 清除单元格的排序，只能单列排序，可以多列搜索
        Object.values(headerData).forEach(item => {
          if (item['type']) {
            delete item['type']
          }
        })
        headerData[`${ri}-${ci}`] = Object.assign(headerItem, { type: sort })
        // asc 上 desc 下
        this.click(CLICK_TYPE.sort, { ri, ci, sort })
        // table.render()
      },
      [HEADER_ICON.search]() {
        // 点击搜索
        searchClick.call(this, { cell, cellRect, evt })
      },
      [HEADER_ICON.fold]() {
        // 折叠
        this.click(CLICK_TYPE.fold, { ri, ci })
      },
      [CLICK_TYPE.cellClick]() {
        // 超链接
        this.click(CLICK_TYPE.cellClick, { ri, ci })
      },
      [HEADER_ICON.exclude]() {
        // 删除
        this.click(CLICK_TYPE.exclude, { ri, ci })
      }
    }
    let typeFun = null
    if (typeArr.includes(RENDER_CELL_TYPE.treeCtrl) && type === RENDER_CELL_TYPE.treeCtrl) {
      typeFun = RENDER_CELL_TYPE.treeCtrl
    } else if (typeArr.includes(HEADER_ICON.more) && type === RENDER_CELL_TYPE.header) {
      // 点击标题更多
      typeFun = HEADER_ICON.more
    } else if (typeArr.includes(HEADER_ICON.sort) && type === RENDER_CELL_TYPE.header) {
      typeFun = HEADER_ICON.sort
    } else if (typeArr.includes(HEADER_ICON.search) && type === RENDER_CELL_TYPE.header) {
      typeFun = HEADER_ICON.search
    } else if (typeArr.includes(HEADER_ICON.fold) && type === RENDER_CELL_TYPE.fold) {
      // 折叠
      typeFun = HEADER_ICON.fold
    } else if (link) {
      // 超链接
      typeFun = CLICK_TYPE.cellClick
    } else if (typeArr.includes(HEADER_ICON.exclude) && exclude) {
      typeFun = HEADER_ICON.exclude
    }
    if (themeFullScreen && !(typeFun === CLICK_TYPE.cellClick || typeFun === RENDER_CELL_TYPE.treeCtrl)) return
    func[typeFun] && func[typeFun].call(this)
  } else if (link) {
    // 超链接
    if (type === RENDER_CELL_TYPE.treeCtrl) {
      // isSkip是否1可以点击
      isSkip && this.click(CLICK_TYPE.cellClick, { ri, ci })
    } else {
      this.click(CLICK_TYPE.cellClick, { ri, ci })
    }
  } else if (jumper || drill) {
    // if (themeFullScreen) return
    // 点击交互
    drill && this.click(CLICK_TYPE.cellClick, { ri, ci })
    if (jumper) {
      clickJumperCall.call(this, ri, ci, cell)
    }
  }
}

export function clickJumperCall(ri, ci, cell, isCanOpenAnimation = false) {
  const { data, table } = this
  const { vm } = data.vmSettings
  if (isCanOpenAnimation) {
    return new Promise((resolve) => {
      const rollPolling = () => {
        if (vm.commonData.getIsFinish()) return resolve()

        setTimeout(rollPolling, 1000)
      }
      rollPolling()
    }).then(() => {
      this.click(CLICK_TYPE.cellClick, { ri, ci }, isCanOpenAnimation)
      cell.content['jumperText'] = cell.text
      table.render()
    })
  }

  if (vm.commonData.getIsFinish()) {
    this.click(CLICK_TYPE.cellClick, { ri, ci })
    if (cell.content['jumperText'] === cell.text) {
      delete cell.content['jumperText']
    } else {
      cell.content['jumperText'] = cell.text
    }
    table.render()
  }
}

// 添加手势
function addGestures(cellRect, evt, cell) {
  const { overlayerEl, data, data: { vmSettings: { vm: { themeData = {} } } } } = this
  let { offsetRulerY, offsetRulerX } = getOffsetXY.call(this, evt)
  const { ri, ci } = cellRect
  const { clickable: { drill = false, jumper = false, link = false }, type, isSkip = false } = cell
  const { x, y } = data.scroll
  const [rif, cif] = data.freeze
  const site = data.gesturesSite[`${ri}-${ci}`]
  const offX = cif > ci ? offsetRulerX : offsetRulerX + x
  const offY = rif > ri ? offsetRulerY : offsetRulerY + y
  // 开启大屏主题颜色
  const { themeFullScreen } = themeData
  let isGestures = false
  if (site && Object.keys(site).some(type => {
    const { sx, sy, ex, ey } = site[type]
    return offX >= sx && offX <= ex && offY >= sy && offY <= ey
  })) {
    themeFullScreen || (isGestures = true)

    if (type === RENDER_CELL_TYPE.treeCtrl) {
      isGestures = true
    }
  } else if (drill || jumper) {
    isGestures = true
  } else if (link) {
    isGestures = type === RENDER_CELL_TYPE.treeCtrl ? isSkip : true
  }
  overlayerEl[isGestures ? 'addClass' : 'removeClass']('gestures')
}
// 点击事件
function overlayerMousedown(evt) {
  const { data } = this
  let { offsetX, offsetY } = getOffsetXY.call(this, evt)
  const cellRect = data.getCellRectByXY(offsetX, offsetY)
  const cell = data.getSelectedCell(cellRect)
  if (cell === null) return
  // 鼠标点击
  this.focusing = { cellRect, evt, cell }
  setAreaOffset.call(this, cellRect, cell)
  // 点击效果
  clickType.call(this, cell, cellRect, evt)
}
// 标题提示
function titleEl(evt) {
  const { data, overlayerEl } = this
  const { offsetX, offsetY } = getOffsetXY.call(this, evt)
  const cellRect = data.getCellRectByXY(offsetX, offsetY)
  const cell = data.getSelectedCell(cellRect)
  if (cell === null || !cell.text) {
    overlayerEl.attr('title', '')
    return
  }
  overlayerEl.attr('title', `${cell.text.replace(/^\s*|\s*$/g, '')}${overlayerEl.attr('title') === cell.text ? ' ' : ''}`)
  setTimeout(() => {
    overlayerEl.attr('title', `${cell.text.replace(/^\s*|\s*$/g, '')}`)
  })
}
const overlayerMouseMove = debounce(function overlayerMouseMove(evt) {
  const { data, overlayerEl } = this
  const { offsetX, offsetY } = getOffsetXY.call(this, evt)
  const cellRect = data.getCellRectByXY(offsetX, offsetY)
  const cell = data.getSelectedCell(cellRect)
  console.log('Mouse', evt)
  if (cell === null) {
    overlayerEl.removeClass('gestures')
    return
  }
  addGestures.call(this, cellRect, evt, cell)
}, 100)
function resizerFn(evt) {
  const { data, colResizer, tableEl } = this
  const { rows, cols, isResizeCol = false, isOpenRuler: { rulerX, rulerY } } = data
  // let { offsetRulerX, offsetRulerY } = getOffsetXY.call(this, evt)
  const { offsetX: offX, offsetY: offY } = getOffsetXY.call(this, evt)
  if (evt.buttons !== 0) return
  if (evt.target.className === `${cssPrefix}-resizer-hover`) return
  if (!isResizeCol) return
  if ((offX > cols.indexWidth && offY > rows.indexHeight)) {
    // rowResizer.hide()
    colResizer.hide()
    return
  }
  const tRect = tableEl.box()
  const cRect = data.getCellRectByXY(offX, offY)
  const flag = rulerY ? -1 : 0
  // if (cRect.ri >= 0 && cRect.ci === 0) {
  //   cRect.width = cols.indexWidth
  //   rowResizer.show(cRect, {
  //     width: tRect.width,
  //   })
  // } else {
  //   rowResizer.hide()
  // }
  if (cRect.ri === flag && cRect.ci >= 0) {
    if (cRect.ci === cols.len - 1) { // 简单表格最后一列不需要拖拽
      return
    }
    cRect.height = rulerY ? rows.indexHeight : cRect.height
    colResizer.show(cRect, {
      height: tRect.height,
    })
  } else {
    colResizer.hide()
  }
}

// 改变selected位置
function selectSetOffset() {
  const { data, areaEl, focusing, contextMenu: { el }, searchBoxWrapEl } = this
  if (!focusing) return
  el.hide()
  searchBoxWrapEl.hide()
}

function overlayerMousescroll(evt) {
  const { verticalScrollbar, data } = this
  const { y: top } = data.scroll
  let delta = evt.deltaY
  if (evt.detail) delta = evt.detail * 40
  const Y = top + delta
  verticalScrollbar.move({ top: Y })
  const scrollHeight = data.rows.totalHeight() - data.view.height()
  if (Y > 0 && Y < scrollHeight) {
    evt.preventDefault()
  }
}
// 复制文本
function copyText(text, el) {
  const oInput = document.createElement('input')
  oInput.value = text
  el.appendChild(oInput)
  oInput.select()
  document.execCommand('Copy')
  oInput.remove()
}
// 初始化事件
function sheetInitEvents() {
  // titleEl,, rowResizer, colResizer
  const { overlayerEl, verticalScrollbar, horizontalScrollbar, moreMenu, contextMenu, data, animation, searchBoxWrapEl, searchEl, searchIconEl, isMobileApp, isMobile, colResizer } = this
  const { isShowAllHeight = false } = data.vmSettings
  overlayerEl.on((isMobileApp || hasTouch) ? 'touchstart' : 'mousedown', (evt) => {
    const stateEvt = evt
    const { isPreview = false, application = TABLE_STATUS.preview } = data.vmSettings.vm
    // 点击右键
    if (evt.buttons === 2) {
      if ((isPreview && !isMobile) || application === TABLE_STATUS.preview) {
        const { offsetX, offsetY } = getOffsetXY.call(this, evt)
        const cellRect = data.getCellRectByXY(offsetX, offsetY)
        const cell = data.getSelectedCell(cellRect)
        if (cell === null || !cell.text) return
        contextMenu.cell = cell
        contextMenu.setPosition(offsetX, offsetY)
      }
    } else {
      contextMenu.hide()
      // 编辑状态不改调用手势滚动
      if (isPreview) {
        this.startDrag(evt, (evt) => {
          // 兼容app端，火狐处理evt有兼容问题
          if (isMobileApp || hasTouch) {
            evt = stateEvt
          }
          overlayerMousedown.call(this, evt)
        })
      } else {
        this.isClick = true
        overlayerMousedown.call(this, evt)
      }
    }
  }).on(`mousewheel`, (evt) => {
    const { isPreview = false } = data.vmSettings.vm
    if (this.mousewheelDisable) return
    if (!isPreview && this.isClick) return

    if (!isShowAllHeight) {
      evt.stopPropagation()
    }
    // 鼠标滚轮事件
    // evt.preventDefault()
    overlayerMousescroll.call(this, evt)
  }).on('mousemove', (evt) => {
    titleEl.call(this, evt)
    // 全屏禁用手势
    overlayerMouseMove.call(this, evt)
    resizerFn.call(this, evt)
  }).on('mouseout', (evt) => {
    const { offsetY } = evt
    if (offsetY <= 0) colResizer.hide()

  })
  this.el.on('mouseover', (evt) => {
    animation.stop()
  }).on('mouseout', (evt) => {
    animation.start()
  })
  bind(document, 'mouseup', this.mouseupHandler)
  bind(window, 'click', this.overallClick)
  colResizer.finishedFn = (cRect, distance) => {
    if (data.resizeColFinishCb) {
      data.resizeColFinishCb(cRect, distance)
    }
  }
  // scrollbar move callback
  verticalScrollbar.moveFn = (distance, evt) => {
    verticalScrollbarMove.call(this, distance, evt)
  }
  horizontalScrollbar.moveFn = (distance, evt) => {
    horizontalScrollbarMove.call(this, distance, evt)
  }

  verticalScrollbar.isScrollValid = isScrollValid.bind(this)
  horizontalScrollbar.isScrollValid = isScrollValid.bind(this)
  // contextMenu
  contextMenu.itemClick = (type, cell) => {
    switch (type) {
      case 'copy':
        copyText(cell.text, overlayerEl.el)
        break
      default:
        break
    }
  }
  // MoreMenu
  moreMenu.itemClick = (type) => {
    if (!this.focusing) return
    if (type === `${HEADER_ICON.sort}Item`) {
      const { cellRect: { ri, ci } } = this.focusing
      const headerItem = data.headerData[`${ri}-${ci}`] || {}
      // 清除单元格的排序，只能单列排序，可以多列搜索
      let sort = headerItem.type === 'desc' ? 'asc' : 'desc'
      Object.values(data.headerData).forEach(item => {
        if (item['type']) {
          delete item['type']
        }
      })
      data.headerData[`${ri}-${ci}`] = Object.assign(headerItem, { type: sort })
      // asc 上 desc 下
      this.click(CLICK_TYPE.sort, { ri, ci, sort })
    } else if (type === `${HEADER_ICON.search}Item`) {
      searchClick.call(this, this.focusing)
    }
  }
  // 表头搜索点击
  searchEl.on('keydown.stop', (evt) => {
    const keyCode = evt.keyCode || evt.which
    if (keyCode === 13 && this.searchCellRect) {
      const val = evt.target.value
      const { ri, ci } = this.searchCellRect
      data.headerData[`${ri}-${ci}`] = Object.assign(data.headerData[`${ri}-${ci}`] || {}, { val })
      this.click(CLICK_TYPE.search, { ri, ci, value: val })
      searchBoxWrapEl.hide()
    }
  })

  searchIconEl.on('click.stop', () => {
    if (this.searchCellRect) {
      const val = this.searchEl.val()
      const { ri, ci } = this.searchCellRect
      data.headerData[`${ri}-${ci}`] = Object.assign(data.headerData[`${ri}-${ci}`] || {}, { val })
      this.click(CLICK_TYPE.search, { ri, ci, value: val })
      searchBoxWrapEl.hide()
    }
  })
}
// 滚动verticalScroll
function verticalScrollbarMove(distance) {
  const { data, table, vLoadEl } = this
  const { width } = this.getTableOffset()
  data.scrolly(distance, () => {
    selectSetOffset.call(this)
    table.render()
  }, { vLoadEl, isHorizontalScroll: width < data.cols.totalWidth(), sheetEl: this.el })
}
// 滚动horizontal
function horizontalScrollbarMove(distance) {
  const { data, table } = this
  data.scrollx(distance, () => {
    selectSetOffset.call(this)
    table.render()
  })
}
// 滚动合法校验
function isScrollValid() {
  const { data: { vmSettings: { vm: { isPreview, isMobile, enlargeVisible, element: { isFocus } } } } } = this
  if (isPreview && isMobile) {
    return enlargeVisible || isFocus
  }
  return true
}
// 生成竖向的滚动条
function verticalScrollbarSet() {
  const { data, verticalScrollbar } = this

  const { height } = this.getTableOffset()
  const erth = data.exceptRowTotalHeight(0, -1)
  data.retainScroll || verticalScrollbar.resetScroll()
  verticalScrollbar.set(height, data.rows.totalHeight() - erth)
}
function horizontalScrollbarSet() {
  const { data, horizontalScrollbar } = this
  const { width } = this.getTableOffset()
  if (data) {
    // 生成水平的滚动条
    data.retainScroll || horizontalScrollbar.resetScroll()
    horizontalScrollbar.set(width, data.cols.totalWidth())
  }
}
function rowResizerFinished(cRect, distance) {
  const { ri } = cRect
  const { table, selector, data } = this
  data.rows.setHeight(ri, distance)
  table.render()
  // selector.resetAreaOffset()
  verticalScrollbarSet.call(this)
  // editorSetOffset.call(this)
}

// function colResizerFinished(cRect, distance) {
//   const { ci } = cRect
//   const { table, selector, data } = this
//   data.cols.setWidth(ci, distance)
//   table.render()
//   // selector.resetAreaOffset()
//   horizontalScrollbarSet.call(this)
//   // editorSetOffset.call(this)
// }
// 监听滑动事件
function slideHandler(startX, startY, endX, endY, result) {
  // const result = GetSlideDirection(endX, endY, startX, startY)
  let dy = startY - endY
  let dx = startX - endX
  const { verticalScrollbar, horizontalScrollbar } = this
  const { top } = verticalScrollbar.scroll()
  const { left } = horizontalScrollbar.scroll()
  switch (result) {
    case 'top':
      verticalScrollbar.move({ top: top + dy })
      break
    case 'down':
      verticalScrollbar.move({ top: top + dy < 0 ? 0 : top + dy })
      break
    case 'left':
      horizontalScrollbar.move({ left: left + dx })
      break
    case 'right':
      horizontalScrollbar.move({ left: left + dx < 0 ? 0 : left + dx })
      break
  }
}

// 重置
function sheetReset() {
  const {
    tableEl,
    overlayerEl,
    table,
    el,
    vLoadEl,
    data,
    data: {
      noReset = false,
      retainScroll = false,
    },
  } = this
  this.searchBoxWrapEl.hide()
  // 重置手势
  data.gesturesSite = {}
  // noReset false为重置 true保留
  noReset || (data.headerData = {})
  // noResetScroll false为重置 true保留
  retainScroll || (data.scroll = new Scroll())
  const vRect = this.getRect()
  tableEl.attr(vRect)
  overlayerEl.offset(vRect)
  el.css('width', `${vRect.width}px`)
  verticalScrollbarSet.call(this)
  horizontalScrollbarSet.call(this)
  el.removeClass('disabled')
  vLoadEl.hide()
  table.render()
}

class Sheet {
  el: Element
  tableEl: Element
  data: DataProxy
  isMobileApp: boolean
  isMobile: boolean
  isClick: boolean
  mousewheelDisable: boolean
  startX: number
  startY: number
  selectStartObj: {
    x: number,
    y: number,
    overlayerMousedown(evt: Event): void
  } | null
  verticalScrollbar: Scrollbar
  horizontalScrollbar: Scrollbar
  moreMenu: MoreMenu
  colResizer: Resizer
  overlayerEl: Element
  searchEl: Element
  searchIconEl: Element
  searchTipEl: Element
  searchBoxEl: Element
  searchBoxWrapEl: Element
  vLoadEl: Element
  contextMenu: ContextMenu
  table: Table
  focusing: boolean
  cursorDown: boolean
  animation: Animation
  constructor(targetEl, data, animation) {
    this.el = h('div', 'sheet')
    targetEl.children(this.el)
    this.data = data
    this.animation = animation
    const { isMobileApp = false, isMobile = false } = data.vmSettings.vm
    this.isMobileApp = isMobileApp
    this.isMobile = isMobile
    this.isClick = false
    this.mousewheelDisable = false
    this.startX = 0
    this.startY = 0
    this.selectStartObj = null

    // table
    this.tableEl = h('canvas', `${cssPrefix}-table`)
    // scrollbar
    this.verticalScrollbar = new Scrollbar(true, (isMobileApp || hasTouch))
    this.horizontalScrollbar = new Scrollbar(false, (isMobileApp || hasTouch))

    // 后期再开发
    this.moreMenu = new MoreMenu(() => this.getTableOffset())
    // resizer
    this.colResizer = new Resizer(true, data.cols.minWidth)
    // 提供表格交互功能
    this.overlayerEl = h('div', `${cssPrefix}-overlayer`)
    // 显示title
    // this.titleEl = h('div', `${cssPrefix}-tooltip`).html('').hide()
    // this.areaEl = h('div', `${cssPrefix}-selector-area`).hide()
    this.searchEl = h('input', `${cssPrefix}-search`).attr('placeholder', 'A-Z,1-100, 0,1,2....')
    this.searchIconEl = h('i', `${cssPrefix}-search-icon icon sdpiconfont icon-sdp-Search`)
    this.searchTipEl = h('i', `${cssPrefix}-search-tip el-tooltip icon-sdp-info`).attr('title', data.vmSettings.vm.$t('sdp.tooltips.tableSearchTips'))
    this.searchBoxEl = h('div', `${cssPrefix}-search-box`).children(this.searchEl.el, this.searchIconEl.el, this.searchTipEl.el)
    this.searchBoxWrapEl = h('div', `${cssPrefix}-search-box-wrap`).children(this.searchBoxEl.el).hide()
    // this.rowResizer = new Resizer(false, data.rows.height)
    this.contextMenu = new ContextMenu(() => this.getTableOffset(), data.$t)
    this.vLoadEl = h('div', `${cssPrefix}-load`).child(h('img').attr('src', `${STATIC_BASE_PATH.images}sdp-loading.gif`)).hide()
    // 点击事件
    this.overallClick = this.overallClick.bind(this)
    this.mouseupHandler = this.mouseupHandler.bind(this)

    this.mouseMoveDocumentHandler = this.mouseMoveDocumentHandler.bind(this)
    this.mouseUpDocumentHandler = this.mouseUpDocumentHandler.bind(this)
    // selector
    this.el.children(
      this.tableEl,
      this.overlayerEl.el,
      // this.rowResizer.el,
      this.colResizer.el,
      this.verticalScrollbar.el,
      this.verticalScrollbar.coveringEl,
      this.verticalScrollbar.barEl,
      this.horizontalScrollbar.el,
      this.horizontalScrollbar.coveringEl,
      this.horizontalScrollbar.barEl,
      this.moreMenu.el,
      this.contextMenu.el,
      // this.titleEl.el,
      // this.areaEl.el,
      this.searchBoxWrapEl.el,
      this.vLoadEl.el,
    )

    this.table = new Table(this.tableEl.el, data)
    // 焦点状态
    this.focusing = false
    // 初始化事件
    sheetInitEvents.call(this)
    sheetReset.call(this)

  }

  startDrag(evt, overlayerMousedown) {
    // 阻止移动滚动条触发
    this.cursorDown = true
    this.startX = evt.changedTouches ? evt.changedTouches[0].clientX : evt.screenX
    this.startY = evt.changedTouches ? evt.changedTouches[0].clientY : evt.screenY
    on(document, (this.isMobileApp || hasTouch) ? 'touchmove' : 'mousemove', this.mouseMoveDocumentHandler)
    on(document, (this.isMobileApp || hasTouch) ? 'touchend' : 'mouseup', this.mouseUpDocumentHandler)
    // 禁止选择
    document.onselectstart = () => false
    this.selectStartObj = {
      x: evt.changedTouches ? evt.changedTouches[0].clientX : evt.screenX,
      y: evt.changedTouches ? evt.changedTouches[0].clientY : evt.screenY,
      overlayerMousedown,
    }
  }

  mouseMoveDocumentHandler(evt) {
    if (this.cursorDown === false) return
    const moveX = evt.changedTouches ? evt.changedTouches[0].clientX : evt.screenX
    const moveY = evt.changedTouches ? evt.changedTouches[0].clientY : evt.screenY
    const { isTop, isEnd, isBottom, direction } = this.handMoveAdjust(evt, moveX, moveY)

    if (this.isMobileApp && ((isTop && (direction === 'top')) || (isBottom && direction === 'down'))) {
      Vue.prototype.$bus.$emit(EVENT_TYPE.CLEAR_ELEMENT_FOCUS)
    }

    slideHandler.call(this, this.startX, this.startY, moveX, moveY, direction)
    this.startX = moveX
    this.startY = moveY
  }

  handMoveAdjust(evt, moveX, moveY) {
    const { data } = this
    const { enlargeVisible = false } = data.vmSettings.vm
    const isCanScroll = isScrollValid.call(this)
    const { isTop, isEnd, isBottom } = data.verticalScrollStatus()
    const direction = GetSlideDirection(moveX, moveY, this.startX, this.startY)
    // console.log('isTop',isTop,'isEnd',isEnd,'isCanScroll',isCanScroll,'direction',direction);

    if (evt.stopPropagation && !enlargeVisible && isCanScroll) {
      if (([0, 'down'].includes(direction) && !isEnd) || ([0, 'top'].includes(direction) && !isTop)) {
        evt.stopPropagation()
      }
    }
    if (evt.preventDefault) evt.preventDefault()
    return { isTop, isEnd, isBottom, direction }
  }

  mouseUpDocumentHandler(evt) {
    this.cursorDown = false
    off(document, (this.isMobileApp || hasTouch) ? 'touchmove' : 'mousemove', this.mouseMoveDocumentHandler)
    const startX = evt.changedTouches ? evt.changedTouches[0].clientX : evt.screenX
    const startY = evt.changedTouches ? evt.changedTouches[0].clientY : evt.screenY
    if (this.selectStartObj) {
      const { x, y, overlayerMousedown } = this.selectStartObj
      const rangeNum = 5
      const rangeX = Math.abs(Math.floor(startX - x))
      const rangeY = Math.abs(Math.floor(startY - y))
      if (rangeX < rangeNum && rangeY < rangeNum) {
        overlayerMousedown(evt)
      }
      this.selectStartObj = null
    }
    document.onselectstart = null
  }

  click(type, { ri, ci, value = '', sort = '' }, isCanOpenAnimation = false) {
    const { vm, vm: { themeData = {} } } = this.data.vmSettings
    const { themeFullScreen } = themeData
    if (vm) {
      const header = [CLICK_TYPE.sort, CLICK_TYPE.search]
      const cell = this.data.getSelectedCell({ ri, ci })
      if (cell) {
        const data = {
          cell,
          info: {
            rIndex: ri,
            cIndex: ci,
            value,
            sort,
          },
          isCanOpenAnimation
        }
        const { clickHandler = () => {}, headerClickHandler = () => {} } = vm
        if (header.includes(type)) {
          themeFullScreen || headerClickHandler(type, data)
        } else {
          if (themeFullScreen) {
            if ((cell.content.link || cell.content.drillable || cell.content.jumper) && type === CLICK_TYPE.cellClick) {
              clickHandler(type, data)
            } if (type === RENDER_CELL_TYPE.tree) {
              clickHandler(type, data)
            }
          } else {
            clickHandler(type, data)
          }
        }
      }
    }
  }

  overallClick(evt) {
    const { searchBoxWrapEl, overlayerEl, moreMenu, contextMenu } = this
    // 点击是否包含在盒子里
    if (!overlayerEl.parent().contains(evt.target)) {
      searchBoxWrapEl && searchBoxWrapEl.hide()
      moreMenu && moreMenu.hide()
      contextMenu && contextMenu.hide()
    }
  }
  mouseupHandler() {
    this.isClick = false
  }
  disableScroll() {
    this.mousewheelDisable = true
  }
  restoreScroll() {
    this.mousewheelDisable = false
  }
  // freeze rows or cols
  freeze(ri, ci) {
    const { data } = this
    data.setFreeze(ri, ci)
    this.sheetReset()
    return this
  }
  // 表格重新渲染
  render() {
    this.table.render()
    return this
  }
  setScale(val) {
    this.table.setScale(val)
    return this
  }
  // 表格重新初始化渲染
  sheetReset() {
    this.animation.initCall()
    sheetReset.call(this)
    this.animation.reset()
    return this
  }
  // 加载数据方法
  loadData(data) {
    this.data.setData(data)
    this.sheetReset()
    return this
  }
  // 注销方法
  destroyed() {
    this.verticalScrollbar.destroyed()
    this.horizontalScrollbar.destroyed()
    unbind(document, 'mouseup', this.mouseupHandler)
    unbind(window, 'click', this.overallClick)
    off(document, (this.isMobileApp || hasTouch) ? 'touchend' : 'mouseup', this.mouseUpDocumentHandler)
    // removeSlide(`${cssPrefix}-overlayer-${this.id}`)
  }
  // 重置
  reload() {
    sheetReset.call(this)
    return this
  }

  getRect() {
    const { data } = this
    return { width: data.viewWidth(), height: data.viewHeight() }
  }

  getTableOffset() {
    const { rows, cols, isOpenRuler: { rulerX, rulerY } } = this.data
    const { width, height } = this.getRect()
    return {
      width: rulerX ? width - cols.indexWidth : width,
      height: rulerY ? height - rows.indexHeight : height,
      // width,
      // height,
      left: cols.indexWidth,
      top: rows.height,
    }
  }
}

export default Sheet
