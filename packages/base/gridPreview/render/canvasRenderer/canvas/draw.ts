/* global window */
import {
  levelSkew,
  HEADER_ICON,
  CHECKBOX_STYLE
} from '../component/constant'
import CELL_WARNING, { IMG_DEFAULT_STYLE } from '../../../../grid/helpers/constants/cellWarning'
import {
  ANGLE_TYPE,
  DATA_BAR_DEFAULT_COLOR,
  DEFAULT_VALUE,
  RULE_TYPE
} from '../../../../grid/components/features-bar/dataBar/constant'
import Vue from 'vue'
import { MATERIAL_COLOR_TYPE } from '../../../../board/displayPanel/supernatant/boardElements/elementMaterialLibrary/mixins/constant'
import { gradientColors, hexify } from 'packages/base/gridPreview/render/canvasRenderer/core/helper'
import { CELL_PADDING } from '../../parser/parseAfter'
import {deepClone} from "../../../../../assets/utils/globalTools";
import {THEME_NAME_MAP} from "../../../../grid/helpers/constants/themeConfig";
import { RENDER_CELL_TYPE } from '../../../common/constant'

const { WARNING_STYLE_TYPE_CELL_Fill_COLOR, WARNING_STYLE_TYPE_TEXT } = CELL_WARNING
// import { Element } from '../component/element'
function npxLine(px) {
  const n = this.npx(px)
  return n > 0 ? n - 0.5 : 0.5
}
type borderType = null | [number, string, string]

// 单元格盒子
class DrawBox {
  x: number
  y: number
  width: number
  height: number
  padding: { top: number, left: number, right: number, bottom: number }
  bgcolor: string
  levelInterval: number
  borderTop: borderType
  borderRight: borderType
  borderBottom: borderType
  borderLeft: borderType
  retract: number
  arrowWidth: number
  constructor(x, y, w, h, levelInterval, isArrow) {
    this.x = x
    this.y = y
    this.width = w
    this.height = h
    this.padding = Object.assign({}, CELL_PADDING)
    this.bgcolor = '#ffffff'
    this.levelInterval = levelInterval ? (parseInt(levelInterval) * levelSkew + levelSkew) : 0
    // border: [width, style, color]
    this.borderTop = null
    this.borderRight = null
    this.borderBottom = null
    this.borderLeft = null
    this.retract = 0
    // 箭头宽度
    this.arrowWidth = isArrow ? 20 : 0
  }

  setBorders({
    top, bottom, left, right,
  }) {
    if (top) this.borderTop = top
    if (right) this.borderRight = right
    if (bottom) this.borderBottom = bottom
    if (left) this.borderLeft = left
  }

  // 获取文字内容宽
  innerRight() {
    const { width, padding, levelInterval, borderRight, borderLeft, retract, arrowWidth } = this
    let num = padding.right
    if (borderRight) num += borderRight[0]
    return num
  }

  // 获取文字内容宽
  innerWidth() {
    const { width, padding, levelInterval, borderRight, borderLeft, retract, arrowWidth } = this
    let num = padding.left + padding.right + levelInterval + retract + arrowWidth
    if (borderRight) num += borderRight[0]
    if (borderLeft) num += borderLeft[0]
    const diff = width - num
    return diff > 0 ? diff : 0
  }

  // 获取文字高度
  innerHeight() {
    const { height, padding, borderTop, borderBottom } = this
    let num = padding.top + padding.bottom
    if (borderTop) num += borderTop[0]
    if (borderBottom) num += borderBottom[0]
    const diff = height - num
    return diff > 0 ? diff : 0
  }

  textx(align) {
    const { width, padding, levelInterval, borderRight, borderLeft, retract, arrowWidth } = this
    let { x } = this
    if (align === 'left') {
      x += padding.left
      if (borderLeft) {
        x += borderLeft[0]
      }
      return x + retract + levelInterval
    } else if (align === 'center') {
      x += width / 2
    } else if (align === 'right') {
      x += width - padding.right - arrowWidth
      if (borderRight) {
        x -= borderRight[0]
      }
    }
    return x
  }

  texty(align, fontSize, hoffset) {
    const { height, padding, borderTop, borderBottom } = this
    let { y } = this
    if (align === 'top') {
      y += padding.top
      if (borderTop) {
        y += borderTop[0]
      }
    } else if (align === 'middle') {
      const num = height / 2 - hoffset
      const t = padding.top + (borderTop ? borderTop[0] : 0) + fontSize / 2
      y = y + (num > t ? num : t)
    // y = y1;
    // const y2 = y + padding + fontSize / 2 + 1;
    // if (y1 < y2) y = y2;
    // else y = y1;
    } else if (align === 'bottom') {
      let num = height - hoffset * 2 - padding.bottom - padding.top
      if (borderBottom) {
        num -= borderBottom[0]
      }
      if (borderTop) {
        num += borderTop[0]
      }
      const t = padding.top + (borderTop ? borderTop[0] : 0) + fontSize
      y += (num > t ? num : t)
    }
    return y
  }

  // 文字平行获取高度
  getTriangleY(align, fontSize, hoffset) {
    const { height, padding, borderTop, borderBottom } = this
    let { y } = this
    if (align === 'top') {
      y += padding.top + fontSize / 2
      if (borderTop) {
        y += borderTop[0]
      }
    } else if (align === 'middle') {
      y = y + height / 2
    } else if (align === 'bottom') {
      let num = fontSize + hoffset * 2 + padding.bottom
      y += height - num
      if (borderBottom) {
        y -= borderBottom[0]
        num += borderBottom[0]
      }
      y += num / 2
    }
    return y
  }

  topxys() {
    const { x, y, width } = this
    return [[x, y], [x + width, y]]
  }

  rightxys() {
    const {
      x, y, width, height,
    } = this
    return [[x + width, y], [x + width, y + height]]
  }

  bottomxys() {
    const {
      x, y, width, height,
    } = this
    return [[x, y + height], [x + width, y + height]]
  }

  leftxys() {
    const {
      x, y, height,
    } = this
    return [[x, y], [x, y + height]]
  }
}

function drawFontLine(type, tx, ty, align, valign, blheight, blwidth) {
  const floffset = { x: 0, y: 0 }
  blwidth = blwidth / this.dpr()
  if (type === 'underline') {
    if (valign === 'bottom') {
      floffset.y = 0
    } else if (valign === 'top') {
      floffset.y = -(blheight + 2)
    } else {
      floffset.y = -blheight / 2
    }
  } else if (type === 'strike') {
    if (valign === 'bottom') {
      floffset.y = blheight / 2
    } else if (valign === 'top') {
      floffset.y = -((blheight / 2) + 2)
    }
  }

  if (align === 'center') {
    floffset.x = blwidth / 2
  } else if (align === 'right') {
    floffset.x = blwidth
  }
  this.line(
    [tx - floffset.x, ty - floffset.y],
    [tx - floffset.x + blwidth, ty - floffset.y],
  )
}

class Draw {
  el: HTMLCanvasElement
  ctx: CanvasRenderingContext2D
  defaultThemeColor: any
  devicePixelRatio: number
  // gradientColorsDataSave: object
  constructor(el, width, height) {
    this.el = el
    this.ctx = el.getContext('2d')
    this.resize(width, height)
    this.devicePixelRatio = window.devicePixelRatio
    // 比例设置canvas
    this.ctx.scale(this.dpr(), this.dpr())
    // this.img = new Image()
    this.defaultThemeColor = null

    // this.gradientColorsDataSave = {}
  }

  setDevicePixelRatio(devicePixelRatio) {
    this.devicePixelRatio = devicePixelRatio ? devicePixelRatio >= 1 ? Math.ceil(devicePixelRatio) : 2 : window.devicePixelRatio
  }

  dpr() {
    return this.devicePixelRatio || 1
  }

  npx(px) {
    return parseInt(<any>(px * this.dpr()), 10)
  }

  thinLineWidth() {
    return this.dpr() - 0.5
  }

  resize(width, height) {
    // console.log('dpr:', dpr)()
    this.el.style.width = `${width}px`
    this.el.style.height = `${height}px`
    this.el.width = this.npx(width)
    this.el.height = this.npx(height)
  }

  clear() {
    const { width, height } = this.el
    this.ctx.clearRect(0, 0, width, height)
    return this
  }

  attr(options) {
    Object.assign(this.ctx, options)
    return this
  }

  save() {
    this.ctx.save() // 保存当前的绘图状态
    this.ctx.beginPath() // 方法开始一条路径，或重置当前的路径
    return this
  }

  restore() {
    // 返回之前保存过的路径状态和属性
    this.ctx.restore()
    return this
  }

  beginPath() {
    // 开始一条路径，或重置当前的路径
    this.ctx.beginPath()
    return this
  }

  translate(x, y) {
    // 重新映射画布上的 (0,0) 位置
    this.ctx.translate(this.npx(x), this.npx(y))
    return this
  }
  clearRect(x, y, w, h) {
    // 清空给定矩形内的指定像素
    this.ctx.clearRect(x, y, w, h)
    return this
  }

  fillRect(x, y, w, h) {
    // 绘制"被填充"的矩形
    this.ctx.fillRect(this.npx(x) - 0.5, this.npx(y) - 0.5, this.npx(w), this.npx(h))
    return this
  }

  fillText(text, x, y) {
    // 绘制填色的文本
    this.ctx.fillText(text, this.npx(x), this.npx(y))
    return this
  }
  text(txt, box, attr = {}, textWrap = true, triangleFun, cell, getTextRightRangeData) {
    const { ctx } = this
    const {
      angle,
      gradientColor,
      type,
      align, valign, font, color, strike, underline, lineHeight
    }: any = attr
    const tx = box.textx(align)
    ctx.save()
    ctx.beginPath()
    this.attr({
      textAlign: align,
      textBaseline: valign,
      font: `${font.italic ? 'italic' : ''} ${typeof font.bold === 'boolean' ? font.bold ? 'bold' : 400 : font.bold} ${this.npx(font.size)}px ${font.name}`,
      fillStyle: color,
      strokeStyle: color,
    })
    // 计算是否折行
    const isEnter = /[\r\n]/g.test(txt)
    const enterArr = isEnter ? txt.split(/[\r\n]/).filter(e => e) : ['']
    // 9962 【数据看板】【sales004】表格指标显示的与设置对齐方式不一致
    if (!enterArr[enterArr.length - 1] && enterArr.length > 1) enterArr.pop()

    let txtWidth = ctx.measureText(txt).width
    if (isEnter) {
      let txtArr = enterArr.map(t => ctx.measureText(t).width)
      txtWidth = Math.max(...txtArr)
    }
    const innerWidth = this.npx(box.innerWidth())
    // number date count类型超出变成星号
    const { columnType = '', aggType = '', adaptive = '' } = cell.content
    if (!adaptive) {
      if (['number', 'date'].includes(columnType) || aggType.toLowerCase() === 'count') {
        textWrap = false
        if (txtWidth > innerWidth) {
          txt = '*********'

          txtWidth = ctx.measureText(txt).width
        }
      }
    }
    let hoffset = 0
    let baseLineHeight = lineHeight ? lineHeight - font.size : 0
    let bottom = box.height + box.y

    if (textWrap && (txtWidth > innerWidth || isEnter) && align !== 'top') {
      // let n = Math.ceil(txtWidth / innerWidth)
      // if (isEnter) {
      //   n += enterArr.length - 1
      // }
      // hoffset += ((n - 1) * (font.size + baseLineHeight)) / 2

      let resY = 0
      const calcTy = (cloneText) => {
        const textLine = { len: 0, start: 0 }
        for (let i = 0; i <= cloneText.length; i += 1) {
          textLine.len = ctx.measureText(cloneText.substring(textLine.start, i)).width
          const Difference = textLine.len - innerWidth
          if (Difference > 0 && bottom > resY) {
            resY += font.size + baseLineHeight
            textLine.start = --i
            textLine.len = 0
          }
        }
      }

      if (isEnter) {
        enterArr.forEach((txt, i) => {
          calcTy(txt)
          if (i) {
            resY += font.size + baseLineHeight
          }
        })
      } else {
        calcTy(txt)
      }

      hoffset = resY / 2
    }
    let ty = box.texty(valign, font.size, hoffset)

    // 表格文字设置渐变色 8679
    const setGradientColor = () => {
      // angle = 90 左右 tx, ty, txtWidth > innerWidth ? tx + innerWidth : tx + txtWidth , ty    0 1
      // angle = 180 上下 tx, ty, tx, resY  0 1
      // angle = 270 右左 tx, ty, txtWidth > innerWidth ? tx + innerWidth : tx + txtWidth , ty   1 0
      // angle = 0 下上 tx, ty, tx, resY  1 0

      if (type !== 'gradient' || !gradientColor) return

      const isLR = [90, 270].includes(angle)
      const colorIdx = [90, 180].includes(angle)

      // 上下需要计算高度
      let resY = ty
      const calcTy = (txt) => {
        if (textWrap && txtWidth > innerWidth) {
          const cloneText = deepClone(txt)
          const textLine = { len: 0, start: 0 }
          for (let i = 0; i <= cloneText.length; i += 1) {
            textLine.len = ctx.measureText(cloneText.substring(textLine.start, i)).width
            const Difference = textLine.len - innerWidth
            if (Difference > 0 && bottom > resY) {
              cloneText.substring(textLine.start, --i)
              resY += font.size + baseLineHeight
              textLine.len = 0
            }
            if (textLine.len > 0 && bottom > resY) {
              cloneText.substring(textLine.start)
            }
          }
        } else if (txtWidth <= innerWidth) {
          resY += font.size + baseLineHeight
        }
      }
      if (!isLR) {
        if (isEnter) {
          enterArr.forEach(txt => {
            calcTy(txt)
          })
        } else {
          calcTy(txt)
        }
      }

      let noStartY = ty
      let noEndY = resY
      const fontHeight = font.size + baseLineHeight
      if (valign === 'middle') {
        noStartY = ty - fontHeight / 2 < 0 ? box.y : ty - fontHeight / 2
        noEndY = resY - fontHeight / 2 < noStartY ? noStartY + fontHeight : resY - fontHeight / 2
      } else if (valign === 'bottom') {
        noStartY = ty - fontHeight < 0 ? box.y : ty - fontHeight
        noEndY = resY - fontHeight < noStartY ? noStartY + fontHeight : resY - fontHeight
      }

      const linearGradient = isLR
        // ctx.createLinearGradient(tx, ty, txtWidth > innerWidth ? tx + innerWidth : tx + txtWidth , ty) :
        // ctx.createLinearGradient(tx, ty, tx + Math.ceil(txtWidth / this.dpr()) , ty) :
        // ctx.createLinearGradient(tx, ty, tx, resY)
        ? ctx.createLinearGradient(this.npx(tx), this.npx(ty), this.npx(tx) + txtWidth, this.npx(ty))
        : ctx.createLinearGradient(this.npx(tx), this.npx(noStartY), this.npx(tx), this.npx(noEndY))
      linearGradient.addColorStop(0, gradientColor[colorIdx ? 0 : 1])
      linearGradient.addColorStop(1, gradientColor[colorIdx ? 1 : 0])

      this.attr({
        fillStyle: linearGradient,
      })
    }
    setGradientColor()

    const textFun = (txt) => {
      if (textWrap && txtWidth > innerWidth) {
        const textLine = { len: 0, start: 0 }
        for (let i = 0; i <= txt.length; i += 1) {
          textLine.len = ctx.measureText(txt.substring(textLine.start, i)).width
          const Difference = textLine.len - innerWidth
          if (Difference > 0 && bottom > ty) {
            this.fillText(txt.substring(textLine.start, --i), tx, ty)

            if (strike) {
              drawFontLine.call(this, 'strike', tx, ty, align, valign, font.size, innerWidth)
            }
            if (underline) {
              drawFontLine.call(this, 'underline', tx, ty, align, valign, font.size, innerWidth)
            }
            ty += font.size + baseLineHeight

            textLine.len = 0
            textLine.start = i
          }
        }
        if (textLine.len > 0 && bottom > ty) {
          this.fillText(txt.substring(textLine.start), tx, ty)
          if (strike) {
            drawFontLine.call(this, 'strike', tx, ty, align, valign, font.size, textLine.len)
          }
          if (underline) {
            drawFontLine.call(this, 'underline', tx, ty, align, valign, font.size, textLine.len)
          }
        }
      } else {
        this.fillText(txt, tx, ty)
        if (strike) {
          drawFontLine.call(this, 'strike', tx, ty, align, valign, font.size, txtWidth)
        }
        if (underline) {
          drawFontLine.call(this, 'underline', tx, ty, align, valign, font.size, txtWidth)
        }
      }
    }

    isEnter ? enterArr.forEach((txt) => {
      textFun(txt)
      ty += font.size + baseLineHeight
    }) : textFun(txt)

    ctx.restore()

    // if (cell.type === RENDER_CELL_TYPE.header) {
    //   debugger
    // }
    const txtW = txtWidth / this.dpr()
    const scopeW = box.innerWidth() - txtW

    const diffW = box.width - txtW

    // 调用是否绘画三角
    typeof triangleFun === 'function' && triangleFun(hoffset, ty, scopeW)
    typeof getTextRightRangeData === 'function' && getTextRightRangeData((cell.type === RENDER_CELL_TYPE.header && txt) ? {
      isBeyond: scopeW < 0 || align === 'right' || (align === 'center' && diffW / 2 < box.innerRight()),
      scope: align === 'left' ? scopeW : diffW / 2
    } : null, align === 'left')
    return this
  }

  border(px, style, color) {
    const { ctx } = this
    ctx.lineWidth = this.npx(px) || this.thinLineWidth()
    ctx.strokeStyle = color
    // console.log('style:', style);
    if (style === 'medium') {
      ctx.lineWidth = this.npx(2) - 0.5
    } else if (style === 'thick') {
      ctx.lineWidth = this.npx(3)
    } else if (style === 'dashed') {
      // 绘制虚线
      ctx.setLineDash([this.npx(3), this.npx(2)])
    } else if (style === 'dotted') {
      ctx.setLineDash([this.npx(1), this.npx(1)])
    } else if (style === 'double') {
      ctx.setLineDash([this.npx(2), 0])
    }
    return this
  }

  line(...xys) {
    const { ctx } = this
    if (xys.length > 1) {
      const [x, y] = xys[0]
      ctx.moveTo(npxLine.call(this, x), npxLine.call(this, y))
      for (let i = 1; i < xys.length; i += 1) {
        const [x1, y1] = xys[i]
        ctx.lineTo(npxLine.call(this, x1), npxLine.call(this, y1))
      }
      ctx.stroke()
    }
    return this
  }

  BLine(xys) {
    // todo
    const { ctx } = this
    if (xys.length > 1) {
      const [x, y] = xys[0]
      ctx.moveTo(x, y)
      for (let i = 1; i < xys.length; i += 1) {
        const [x1, y1] = xys[i]
        ctx.lineTo(x1, y1)
      }
      ctx.stroke()
    }
    return this
  }

  // 绘制边框
  strokeBorders(box) {
    const { ctx } = this
    // border
    const {
      borderTop, borderRight, borderBottom, borderLeft
    } = box
    let setBorder = (data, callbacks) => {
      ctx.save()
      ctx.beginPath()
      // @ts-ignore
      this.border(...data)
      callbacks(data)
      ctx.restore()
    }
    if (borderTop) {
      setBorder(borderTop, (data) => {
        const [ [x, y], [x1, y1] ] = box.topxys()
        this.BLine([ [this.npx(x), this.npx(y) + this.npx(data[0]) / 2], [this.npx(x1), this.npx(y1) + this.npx(data[0]) / 2] ])
      })
    }
    if (borderBottom) {
      setBorder(borderBottom, (data) => {
        const [ [x, y], [x1, y1] ] = box.bottomxys()
        this.BLine([ [this.npx(x), this.npx(y) - this.npx(data[0]) / 2], [this.npx(x1), this.npx(y1) - this.npx(data[0]) / 2] ])
      })
    }
    if (borderRight) {
      setBorder(borderRight, (data) => {
        let [ [x, y], [x1, y1] ] = box.rightxys()
        // if (borderTop) {
        //   y += data[0]
        // }
        // if (borderBottom) {
        //   y1 -= data[0]
        // }
        this.BLine([ [this.npx(x) - this.npx(data[0]) / 2, this.npx(y)], [this.npx(x1) - this.npx(data[0]) / 2, this.npx(y1)] ])
      })
    }
    if (borderLeft) {
      setBorder(borderLeft, (data) => {
        let [ [x, y], [x1, y1] ] = box.leftxys()
        // if (borderTop) {
        //   y += data[0]
        // }
        // if (borderBottom) {
        //   y1 -= data[0]
        // }
        this.BLine([ [this.npx(x) + this.npx(data[0]) / 2, this.npx(y)], [this.npx(x1) + this.npx(data[0]) / 2, this.npx(y1)] ])
      })
    }
  }

  // 绘制三角
  triangle(box, ty) {
    const { ctx, defaultThemeColor }: any = this
    const sx = box.textx('left') + 3 - levelSkew
    const sy = ty - 5
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(this.npx(sx), this.npx(sy))
    ctx.lineTo(this.npx(sx), this.npx(sy + 10))
    ctx.lineTo(this.npx(sx + 6), this.npx(sy + 5))
    ctx.closePath()
    ctx.fillStyle = defaultThemeColor['triangle']
    ctx.fill()
    ctx.restore()
    return this.addRange({ sx, sy, ex: sx + 6, ey: sy + 10 }, 5)
  }

  // 绘制倒三角
  downTriangle(box, ty) {
    const { ctx, defaultThemeColor }: any = this
    const sx = box.textx('left') - levelSkew
    const sy = ty - 3
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(this.npx(sx), this.npx(sy))
    ctx.lineTo(this.npx(sx + 10), this.npx(sy))
    ctx.lineTo(this.npx(sx + 5), this.npx(sy + 6))
    ctx.closePath()
    ctx.fillStyle = defaultThemeColor['triangle']
    ctx.fill()
    ctx.restore()
    return this.addRange({ sx, sy, ex: sx + 10, ey: sy + 6 }, 5)
  }

  // 绘制loading状态
  loading(box, ty, id, table, themeType) {
    const { ctx }: any = this

    const imgWidth = 10
    const imgHeight = 10
    let rotation = 0 // 当前旋转角度
    const speed = 0.1 // 旋转速度，可以根据需要调整
    const scrollY = table?.data?.scroll?.y || 0
    const freezeHeight = table?.data?.freezeTotalHeight?.() || 0

    const isFreezeRow = ty < freezeHeight

    const sx = box.textx('left') - levelSkew
    const sy = isFreezeRow? ty - 5 : ty - 5 - scrollY

    const image = new Image()
    // image.src = require('../../../../../assets/static/image/sdp/loading_w.png') // 替换为你的图片路径
    image.src = themeType === THEME_NAME_MAP.THEME_LIGHT ?
      require('../../../../../assets/static/image/sdp/loading_w.png') :
      require('../../../../../assets/static/image/sdp/loading_d.png') // 替换为你的图片路径

    const startLoadingAnimation = () => {
      const animate = () => {
        if (!table.hasLoadingId(id)) return
        const currentScrollY = table?.data?.scroll?.y || 0
        if (currentScrollY !== scrollY) return
        if (!isFreezeRow && (sy < freezeHeight || sy < 0)) return

        ctx.clearRect(this.npx(sx) - 1, this.npx(sy) - 1, this.npx(imgWidth) + 2, this.npx(imgHeight) + 2) // 清除画布

        // 绘制旋转的图片
        ctx.save()
        ctx.translate(this.npx(sx + (imgWidth / 2)), this.npx(sy + (imgHeight / 2))) // 移动到加载区域的中心
        ctx.rotate(rotation) // 旋转坐标系统
        ctx.drawImage(image, this.npx(-imgWidth / 2), this.npx(-imgHeight / 2), this.npx(imgWidth), this.npx(imgHeight)) // 绘制图片
        // ctx.drawImage(image, this.npx(sx), this.npx(sy), this.npx(imgWidth), this.npx(imgHeight)) // 绘制图片
        ctx.restore()

        // 更新旋转角度
        rotation += speed
        if (rotation >= 2 * Math.PI) {
          rotation = 0 // 如果旋转一圈后，重置旋转角度
        }

        // 请求下一帧动画
        requestAnimationFrame(animate)
      }

      if (!table.loadingIDList.includes(id)) {
        table.pushLoadingId(id)
      }
      // 开始动画循环
      animate()
    }

    image.onload = startLoadingAnimation

    // return this.addRange({ sx, sy, ex: sx + imgWidth, ey: sy + imgHeight }, 10)
    return { sx, sy, ex: sx + imgWidth, ey: sy + imgHeight }
  }

  // 右箭头
  appLinkArrowRight(box, ty, w) {
    const { ctx } = this
    let sx = box.textx('right') + 8
    if (w > 0) {
      sx -= w
    }
    const sy = ty - 3
    ctx.save()
    ctx.beginPath()
    ctx.lineWidth = this.npx(1)
    ctx.moveTo(this.npx(sx), this.npx(sy))
    ctx.lineTo(this.npx(sx + 4), this.npx(sy + 3))
    ctx.lineTo(this.npx(sx), this.npx(sy + 6))
    ctx.strokeStyle = '#aaa'
    ctx.stroke()
    ctx.restore()
    return { sx, sy, ex: sx + 6, ey: sy + 10 }
  }

  // 右箭头
  arrowRight(box, ty, w) {
    const { ctx } = this
    let sx = box.textx('right') + 8
    if (w > 0) {
      sx -= w
    }
    const sy = ty - 5
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(this.npx(sx), this.npx(sy))
    ctx.lineTo(this.npx(sx + 6), this.npx(sy + 5))
    ctx.lineTo(this.npx(sx), this.npx(sy + 10))
    ctx.strokeStyle = '#ccc'
    ctx.stroke()
    ctx.restore()
    return { sx, sy, ex: sx + 6, ey: sy + 10 }
  }

  // 下箭头
  arrowBottom(box, ty, w) {
    const { ctx } = this
    // const { x, y, height } = box
    let sx = box.textx('right') + 5
    if (w > 0) {
      sx -= w
    }
    const sy = ty - 3
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(this.npx(sx), this.npx(sy))
    ctx.lineTo(this.npx(sx + 5), this.npx(sy + 6))
    ctx.lineTo(this.npx(sx + 10), this.npx(sy))
    ctx.strokeStyle = '#ccc'
    ctx.stroke()
    ctx.restore()
    return { sx, sy, ex: sx + 10, ey: sy + 6 }
  }

  // 绘制排序按钮
  [`${HEADER_ICON.sort}Btn`](box, site = 0, type) {
    const { ctx, defaultThemeColor: { activeColor, defaultColor } }: any = this
    const {
      x, y, width, height
    } = box
    const sx = x + width - site
    const sy = y + height / 2
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(this.npx(sx - 9), this.npx(sy - 7))
    ctx.lineTo(this.npx(sx - 14), this.npx(sy - 1))
    ctx.lineTo(this.npx(sx - 4), this.npx(sy - 1))
    ctx.closePath()
    ctx.fillStyle = type === 'asc' ? activeColor : defaultColor
    ctx.fill()

    ctx.beginPath()
    ctx.moveTo(this.npx(sx - 9), this.npx(sy + 7))
    ctx.lineTo(this.npx(sx - 14), this.npx(sy + 1))
    ctx.lineTo(this.npx(sx - 4), this.npx(sy + 1))
    ctx.closePath()
    ctx.fillStyle = type === 'desc' ? activeColor : defaultColor
    ctx.fill()
    ctx.restore()
    return { sx: sx - 18, sy: sy - 7, ex: sx, ey: sy + 7 }
  }

  // 更多按钮
  [`${HEADER_ICON.more}Btn`](box, site = 0, bool) {
    const { ctx, defaultThemeColor: { activeColor, defaultColor } }: any = this
    const {
      x, y, width, height,
    } = box
    const sx = x + width - site
    const sy = y + height / 2
    ctx.save()
    for (let i = 0; i < 3; i++) {
      ctx.beginPath()
      ctx.arc(this.npx(sx - 9), (sy - 6.3 + 5.5 * i) * this.dpr(), 1.7 * this.dpr(), 0, 2 * Math.PI)
      ctx.fillStyle = bool ? activeColor : defaultColor
      ctx.fill()
    }
    ctx.restore()
    return { sx: sx - 18, sy: sy - 6.3, ex: sx, ey: sy + 6.3 }
  }

  // 绘制搜索
  [`${HEADER_ICON.search}Btn`](box, site = 0, bool) {
    const { ctx, defaultThemeColor: { activeColor, defaultColor } }: any = this
    const {
      x, y, width, height,
    } = box
    const sx = x + width - site
    const sy = y + height / 2
    ctx.save()
    ctx.beginPath()
    ctx.strokeStyle = bool ? activeColor : defaultColor
    ctx.lineWidth = this.npx(2)
    ctx.lineCap = 'round'
    ctx.arc(this.npx(sx - 11), this.npx(sy - 2), this.npx(6), 0.25 * Math.PI, 2.5 * Math.PI)
    ctx.lineTo(this.npx(sx - 2), this.npx(sy + 6))
    ctx.stroke()
    ctx.restore()
    return { sx: sx - 20, sy: sy - 7, ex: sx, ey: sy + 7 }
  }

  image(box, { editHeight, editWidth, url }, imgs) {
    url = Vue.prototype.$_getAssetsUrl(url)
    const { ctx } = this
    const {
      x, y, width, height,
    } = box

    const sx = x + (width - editWidth) / 2
    const sy = y + (height - editHeight) / 2

    const img = new Image()
    img.src = url
    img.crossOrigin = 'anonymous'

    const setImg = () => {
      ctx.restore()
      ctx.drawImage(imgs[url] || img, this.npx(sx), this.npx(sy), this.npx(editWidth), this.npx(editHeight))
      ctx.save()
    }
    if (imgs[url]) { // 如果图片已经存在缓存，直接调用回调函数
      setImg()
      return
    }
    img.onload = setImg
  }

  warningImage(box, attr, { url }, imgs) {
    const { ctx } = this
    const { align, valign } = attr

    const w = IMG_DEFAULT_STYLE.WIDTH
    const h = IMG_DEFAULT_STYLE.HEIGHT

    const { padding: { top, left, right, bottom } } = box
    const [x, y, width, height] = this.getRectRange(box)

    let sx = x + (width - w) / 2
    let sy = y + (height - h) / 2

    if (align === 'left') {
      sx = x + left
    } else if (align === 'right') {
      sx = x + width - w - right
    }
    if (valign === 'top') {
      sy = y + top
    } else if (valign === 'bottom') {
      sy = y + height - h - bottom
    }

    const img = new Image()
    img.src = url
    img.crossOrigin = 'anonymous'

    const setImg = () => {
      ctx.restore()
      ctx.drawImage(imgs[url] || img, this.npx(sx), this.npx(sy), this.npx(w), this.npx(h))
      ctx.save()
    }
    if (imgs[url]) { // 如果图片已经存在缓存，直接调用回调函数
      setImg()
      return
    }
    img.onload = setImg
  }

  // 绘画圆形
  circular(box, attr, style) {
    const { ctx } = this
    const {
      fillColor = CELL_WARNING.WARNING_DEFAULT_SHAPE_COLOR[0],
      radius = CELL_WARNING.RADIUS,
      strokeColor = CELL_WARNING.STROKE_COLOR,
      strokeWidth = CELL_WARNING.STROKE_WIDTH,
    } = style
    const { align, valign, font } = attr
    const tx = box.textx(align)
    const ty = box.texty(valign, font.size, 0)
    let size = this.npx(radius)
    ctx.save()
    if (align === 'left') {
      ctx.translate(size, 0)
    } else if (align === 'right') {
      ctx.translate(-size, 0)
    }
    if (valign === 'top') {
      ctx.translate(0, size)
    } else if (valign === 'bottom') {
      ctx.translate(0, -size)
    }
    ctx.beginPath()
    ctx.arc(this.npx(tx), this.npx(ty), size, 0, 2 * Math.PI)
    ctx.fillStyle = fillColor
    ctx.fill()
    if (strokeWidth && strokeColor) {
      ctx.lineWidth = strokeWidth
      ctx.strokeStyle = strokeColor
      ctx.stroke()
    }
    ctx.restore()
  }

  // 绘画圆形
  borderCycle(box, attr, style) {
    const { ctx } = this
    const {
      fillColor = CELL_WARNING.WARNING_DEFAULT_SHAPE_COLOR[0],
      radius = CELL_WARNING.RADIUS,
      strokeColor = CELL_WARNING.STROKE_COLOR,
      strokeWidth = CELL_WARNING.STROKE_WIDTH,
      angle = 0,
      fill = true,
    } = style
    const { align, valign, font } = attr
    const tx = box.textx(align)
    const ty = box.texty(valign, font.size, 0)
    let size = this.npx(radius)
    ctx.save()
    if (align === 'left') {
      ctx.translate(size, 0)
    } else if (align === 'right') {
      ctx.translate(-size, 0)
    }
    if (valign === 'top') {
      ctx.translate(0, size)
    } else if (valign === 'bottom') {
      ctx.translate(0, -size)
    }
    ctx.beginPath()
    ctx.arc(this.npx(tx), this.npx(ty), size, 0, 2 * Math.PI, false)
    ctx.lineWidth = this.npx(strokeWidth)
    ctx.strokeStyle = strokeColor
    ctx.stroke()

    if (fill) {
      ctx.moveTo(this.npx(tx), this.npx(ty))
      ctx.arc(this.npx(tx), this.npx(ty), size, -0.5 * Math.PI, angle * Math.PI, true)
      ctx.fillStyle = fillColor
      ctx.fill()
    }

    ctx.restore()
  }

  dropdown(box) {
    const { ctx } = this
    const {
      x, y, width, height,
    } = box
    const sx = x + width - 15
    const sy = y + height - 15
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(this.npx(sx), this.npx(sy))
    ctx.lineTo(this.npx(sx + 8), this.npx(sy))
    ctx.lineTo(this.npx(sx + 4), this.npx(sy + 6))
    ctx.closePath()
    ctx.fillStyle = 'rgba(0, 0, 0, .45)'
    ctx.fill()
    ctx.restore()
  }

  addRange(data, range = 0) {
    if (Object.prototype.toString.call(data) === '[object Object]') {
      Object.keys(data).forEach(key => {
        if (['sx', 'sy'].includes(key)) {
          data[key] -= range
        } else {
          data[key] += range
        }

      })
    }
    return data
  }
  checkbox(box, themeType, bool = false) {
    const { size, radius: r, hook } = CHECKBOX_STYLE
    const { noSelected, yesSelected }: any = CHECKBOX_STYLE[themeType]
    const { ctx } = this
    const { x, y, width: w, height: h } = box
    const ox = x + w / 2 - size / 2
    const oy = y + h / 2 - size / 2
    let bulk = this.npx(size)
    let radius = this.npx(r)
    let originX = this.npx(ox)
    let originY = this.npx(oy)
    ctx.save()
    ctx.translate(originX, originY)
    ctx.beginPath()
    ;[
      { arc: [bulk - radius, bulk - radius, radius, 0, Math.PI / 2], lineTo: [radius, bulk] },
      { arc: [radius, bulk - radius, radius, Math.PI / 2, Math.PI], lineTo: [0, radius] },
      { arc: [radius, radius, radius, Math.PI, Math.PI * 3 / 2], lineTo: [bulk - radius, 0] },
      { arc: [bulk - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2], lineTo: [bulk, bulk - radius] }
    ].forEach(({ arc, lineTo }) => {
      // @ts-ignore
      ctx.arc(...arc)
      // @ts-ignore
      ctx.lineTo(...lineTo)
    })
    const { strokeStyle, fillStyle } = bool ? yesSelected : noSelected
    ctx.strokeStyle = strokeStyle
    ctx.fillStyle = fillStyle
    ctx.stroke()
    ctx.fillRect(0, 0, bulk, bulk)
    ctx.closePath()
    ctx.restore()
    // 是否勾选
    if (bool) {
      const { size, strokeStyle } = hook
      ctx.save()
      ctx.translate(originX, originY)
      ctx.beginPath()
      ctx.moveTo(bulk / 5, bulk / 2)
      ctx.lineTo(bulk / 2.5, bulk / 1.3)
      ctx.lineTo(bulk / 1.2, bulk / 3)
      ctx.lineWidth = this.npx(size)
      ctx.strokeStyle = strokeStyle
      ctx.stroke()
      ctx.restore()
    }
    return { sx: ox, sy: oy, ex: ox + size, ey: oy + size }
  }
  tags(box, attr, types) {
    const { ctx, defaultThemeColor: { tagFillColor } }: any = this
    const { align, valign, font } = attr
    let num = 0
    if (align === 'center') {
      num = (types.length - 1) * 20 / 2
    } else if (align === 'right') {
      types = [...types].reverse()
    }
    let x = box.textx(align) - num
    let y = box.texty(valign, font.size, 0)
    types.forEach(type => {
      const site = { x: 0, y: 0 }
      const siteNum = this.npx(6)
      site.x = align === 'left' ? siteNum : align === 'right' ? -siteNum : 0
      site.y = valign === 'top' ? siteNum : valign === 'bottom' ? -siteNum : 0
      this[`${type}Tag`]({ x, y, ctx, site, fillColor: tagFillColor.find(item => item.type === type)['fillColor'] })
      if (align === 'left' || align === 'center') {
        x += 20
      } else if (align === 'right') {
        x -= 20
      }
    })
  }
  circleTag({ site, x, y, ctx, radius = 4, fillColor }) {
    ctx.save()
    ctx.translate(site.x, site.y)
    ctx.beginPath()
    ctx.arc(this.npx(x), this.npx(y), this.npx(radius), 0, 2 * Math.PI)
    // ctx.shadowColor = 'black'
    // ctx.shadowBlur = this.npx(4)
    ctx.fillStyle = fillColor
    ctx.fill()
    ctx.restore()
  }
  // 小旗子
  flagTag({ site, x, y, ctx, fillColor }) {
    ctx.save()
    ctx.translate(site.x, site.y)
    ctx.beginPath()
    ctx.moveTo(this.npx(x - 2), this.npx(y - 6))
    ctx.lineTo(this.npx(x), this.npx(y - 6))
    ctx.lineTo(this.npx(x + 6), this.npx(y - 2))
    ctx.lineTo(this.npx(x), this.npx(y + 2))
    ctx.lineTo(this.npx(x), this.npx(y + 6))
    ctx.lineTo(this.npx(x - 2), this.npx(y + 6))
    ctx.closePath()
    ctx.fillStyle = fillColor
    ctx.fill()
    ctx.restore()
  }

  // 三角标记
  triangleTag({ site, x, y, ctx, fillColor }) {
    ctx.save()
    ctx.translate(site.x, site.y)
    ctx.beginPath()
    ctx.moveTo(this.npx(x), this.npx(y - 4))
    ctx.lineTo(this.npx(x - 5), this.npx(y + 4))
    ctx.lineTo(this.npx(x + 5), this.npx(y + 4))
    ctx.closePath()
    ctx.fillStyle = fillColor
    ctx.fill()
    ctx.restore()
  }

  // 五角星
  fivePointedStarTag({ site, x, y, ctx, radius: r = 3, fillColor }) {
    ctx.save()
    ctx.translate(site.x, site.y)
    ctx.beginPath()
    for (let i = 0; i < 5; i++) {
      ctx.lineTo(this.npx(Math.cos((18 + 72 * i) / 180 * Math.PI) * r * 2 + x), this.npx(-Math.sin((18 + 72 * i) / 180 * Math.PI) * r * 2 + y))
      ctx.lineTo(this.npx(Math.cos((54 + 72 * i) / 180 * Math.PI) * r + x), this.npx(-Math.sin((54 + 72 * i) / 180 * Math.PI) * r + y))
    }
    ctx.closePath()
    ctx.fillStyle = fillColor
    ctx.fill()
    ctx.restore()
  }

  error(box) {
    const { ctx } = this
    const { x, y, width } = box
    const sx = x + width - 1
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(this.npx(sx - 8), this.npx(y - 1))
    ctx.lineTo(this.npx(sx), this.npx(y - 1))
    ctx.lineTo(this.npx(sx), this.npx(y + 8))
    // 创建从当前点到开始点的路径
    ctx.closePath()
    ctx.fillStyle = 'rgba(255, 0, 0, .65)'
    ctx.fill()
    ctx.restore()
  }

  rowRect(arr, bgcolor) {
    const { ctx } = this
    ctx.save()
    ctx.beginPath()
    ctx.fillStyle = bgcolor || '#fff'
    const arrs = arr.map(item => {
      return this.npx(item)
    })
    // @ts-ignore
    ctx.rect(...arrs)
    ctx.clip()
    ctx.fill()
    ctx.restore()
  }

  strokeDataBarBorders(data) {
    const { ctx } = this
    const {
      lineWidth, color, isDrawArr
    } = data
    const callbacks = {
        top: () => {
          const [ [x, y], [x1, y1] ] = data.top()
          this.BLine([ [x, y + lineWidth / 2], [x1, y1 + lineWidth / 2] ])
        },
        bottom: () => {
          const [ [x, y], [x1, y1] ] = data.bottom()
          this.BLine([ [x, y - lineWidth / 2], [x1, y1 - lineWidth / 2] ])
        },
        right: () => {
          let [ [x, y], [x1, y1] ] = data.right()
          y += lineWidth
          y1 -= lineWidth
          this.BLine([ [x - lineWidth / 2, y], [x1 - lineWidth / 2, y1] ])
        },
        left: () => {
          let [ [x, y], [x1, y1] ] = data.left()
          y += lineWidth
          y1 -= lineWidth
          this.BLine([ [x + lineWidth / 2, y], [x1 + lineWidth / 2, y1] ])
        }
    }

    Object.entries(callbacks).forEach(([key, callback]) => {
      if (isDrawArr.includes(key)) {
        ctx.save()
        ctx.beginPath()
        ctx.lineWidth = lineWidth
        ctx.strokeStyle = color
        callback()
        ctx.restore()
      }
    })
  }

  // 色阶
  colorGradation(rectBg, cell) {
    if (!(cell?.content.colorGradationSet && cell.cellColorGradation)) return

    // 预警等级高于色阶不做处理
    if (cell?.warn?.type === WARNING_STYLE_TYPE_CELL_Fill_COLOR) return
    // 没有数据不做处理
    if (cell?.text === '') return

    rectBg(cell.cellColorGradation)
  }

  // 设置最大最小
  setDataMaximum(rectBg, cell) {
    if (!cell.dataMaximum) return
    const warnType = cell?.warn?.type

    // 预警等级高于不做处理
    if (warnType !== WARNING_STYLE_TYPE_CELL_Fill_COLOR) {
      rectBg(cell.dataMaximum.background)
    }
  }

  // 数据条
  dataBar(originalRect, cell, themeType) {
    if (!(cell?.content.dataBarSet && cell.cellPercent)) return

    const { negative, data, ratio } = cell.cellPercent
    const isColInNegative = negative
    const percent = parseFloat(data)

    const range = this.npx(1)
    const diff = range * 2

    const [ox, oy, ow, oh] = originalRect

    const rect = [
      ox + range,
      oy + range,
      ow - diff,
      oh - diff
    ]
    const { ctx } = this

    let { border = false, borderColor, color, negativeColor }: typeof DEFAULT_VALUE = cell.content.dataBarSet || {}

    borderColor = Object.assign({}, DEFAULT_VALUE['borderColor'], borderColor)
    negativeColor = Object.assign({}, DEFAULT_VALUE['negativeColor'], negativeColor)
    color = Object.assign({}, DEFAULT_VALUE['color'], color)

    // 清空矩形
    const clear = (rect) => {
      ctx.save()
      // @ts-ignore
      this.clearRect(...rect)
      ctx.restore()
    }

    // 边框设置
    const isBorder = border
    const lineWidth = isBorder ? this.npx(1) || this.thinLineWidth() : 0

    const setContent = (percent, rect) => {
      const [x, y, w, h] = rect
      const isInter = percent > 0
      let bRect
      // 这列是否有负值
      if (isColInNegative) {
        const negativeW = parseInt(String(ratio ? w * ratio : 0))
        const interW = w - negativeW
        const negativeMoveNum = negativeW + negativeW * percent
        bRect = isInter ? [(x + negativeW), y, interW * percent, h] : [(x + negativeMoveNum), y, (negativeW - negativeMoveNum), h]
      } else {
        bRect = [x, y, w * percent, h]
      }
      if (!percent) return bRect
      clear(bRect)
      function drawContent({ pureColor, isGradient, gradientColor, bRect, angle }) {
        const getCreateLinearGradientData = (bRect, angle) => {
          const [bx, by, bw, bh] = bRect
          switch (angle) {
            case ANGLE_TYPE.angle90:
            case ANGLE_TYPE.angle270: {
              return {
                rect: [bx, by, bx + bw, by],
                isReverse: angle === ANGLE_TYPE.angle270
              }
            }
            case ANGLE_TYPE.angle315:
            case ANGLE_TYPE.angle135: {
              return {
                rect: [bx, by, bx + bw, by + bh],
                isReverse: angle === ANGLE_TYPE.angle315
              }
            }
            case ANGLE_TYPE.angle0:
            case ANGLE_TYPE.angle180: {
              return {
                rect: [bx, by, bx, by + bh],
                isReverse: angle === ANGLE_TYPE.angle0
              }
            }
            case ANGLE_TYPE.angle45:
            case ANGLE_TYPE.angle225: {
              return {
                rect: [bx, by + bh, bx + bw, by],
                isReverse: angle === ANGLE_TYPE.angle225
              }
            }
            default: {
              return {
                rect: [bx, by, bx + bw, by],
                isReverse: false
              }
            }
          }
        }

        ctx.save()
        ctx.beginPath()
        let fillStyle = pureColor
        if (isGradient) {
          const { rect, isReverse } = getCreateLinearGradientData(bRect, angle)
          // @ts-ignore
          const gradient = ctx.createLinearGradient(...rect)
          gradientColor = isReverse ? [gradientColor[1], gradientColor[0]] : gradientColor
          gradientColor.forEach((val, index) => {
            gradient.addColorStop(index, gradientColor[index])
          })
          fillStyle = gradient
        }

        ctx.fillStyle = fillStyle
        // @ts-ignore
        ctx.rect(...bRect)
        ctx.clip()
        ctx.fill()
        ctx.restore()
      }

      function CallDataBar(data, bRect) {
        const { type = MATERIAL_COLOR_TYPE.PURE, pureColor, gradientColor, angle } = data || {}
        const isGradient = type === MATERIAL_COLOR_TYPE.GRADIENT

        drawContent({
          pureColor: pureColor || (isInter ? DATA_BAR_DEFAULT_COLOR.COLOR[themeType] : DATA_BAR_DEFAULT_COLOR.NEGATIVE_COLOR[themeType]),
          isGradient,
          gradientColor,
          bRect,
          angle })
      }

      CallDataBar(isInter ? color[themeType] : negativeColor[themeType], bRect)
      return bRect
    }

    const bRect = setContent(percent, rect)

    const setBorder = (bRect, lineWidth, isColInNegative) => {
      const isRightOrLeft = percent > 0
      let [x, y, w, h] = bRect
      const { pureColor } = borderColor[themeType]
      const color = pureColor || DATA_BAR_DEFAULT_COLOR.BORDER_COLOR[themeType]
      this.strokeDataBarBorders({
        color,
        lineWidth,
        top: () => [[x, y], [x + w, y]],
        right: () => [[x + w, y], [x + w, y + h]],
        bottom: () => [[x, y + h], [x + w, y + h]],
        left: () => [[x, y], [x, y + h]],
        isDrawArr: isColInNegative ? isRightOrLeft ? ['top', 'right', 'bottom'] : ['top', 'bottom', 'left'] : ['top', 'right', 'bottom', 'left']
      })
    }

    isBorder && percent && setBorder(bRect, lineWidth, isColInNegative)

    const drawZeroLine = (originalRect, bRect) => {
      const [bx, , bw] = bRect
      const [, y, , h] = originalRect
      const lineSite = percent > 0 ? [[bx, y], [bx, y + h]] : [[bx + bw, y], [bx + bw, y + h]]
      ctx.save()
      ctx.beginPath()
      ctx.strokeStyle = '#A8B4E1'
      ctx.setLineDash([this.npx(3), this.npx(2)])
      this.BLine(lineSite)
      ctx.restore()
    }
    isColInNegative && drawZeroLine(originalRect, bRect)
  }
  getRectRange(box) {
    const {
      x, y, width, height, borderBottom, borderLeft, borderRight, borderTop,
    } = box
    const arr = [x, y, width, height]
    if (borderTop) {
      arr[1] += borderTop[0]
      arr[3] -= borderTop[0]
    }
    if (borderLeft) {
      arr[0] += borderLeft[0]
      arr[2] -= borderLeft[0]
    }
    if (borderRight) {
      arr[2] -= borderRight[0]
    }
    if (borderBottom) {
      arr[3] -= borderBottom[0]
    }
    return arr
  }
  rectBg(arr, bgcolor) {
    const { ctx } = this
    ctx.save()
    ctx.beginPath()
    ctx.fillStyle = bgcolor || '#fff'
    // @ts-ignore
    ctx.rect(...arr)
    ctx.clip()
    ctx.fill()
    ctx.restore()
  }

  rect(box, { cell, themeType }, dtextcb) {
    const {
      bgcolor
    } = box
    const arr = this.getRectRange(box).map(num => this.npx(num))
    // 设置背景色
    this.rectBg(arr, bgcolor)
    // 色阶
    this.colorGradation((bgcolor) => {
      this.rectBg(arr, bgcolor)
    }, cell)
    // 最大最小值
    this.setDataMaximum((bgcolor) => {
      this.rectBg(arr, bgcolor)
    }, cell)
    // 数据条
    this.dataBar(arr, cell, themeType)
    // 绘画字体
    dtextcb()
  }
}

export {
  Draw,
  DrawBox,
}
