import { CellRange } from './cell_range'

class Merges {
  _: any[]
  constructor(d = []) {
    this._ = d
  }

  forEach(cb) {
    this._.forEach(cb)
  }

  deleteWithin(cr) {
    this._ = this._.filter(it => !it.within(cr))
  }

  getFirstIncludes(ri, ci) {
    for (let i = 0; i < this._.length; i += 1) {
      const it = this._[i]
      if (it.includes(ri, ci)) {
        return it
      }
    }
    return null
  }

  filterIntersects(cellRange) {
    return new Merges(this._.filter(it => it.intersects(cellRange)))
  }

  intersects(cellRange) {
    for (let i = 0; i < this._.length; i += 1) {
      const it = this._[i]
      if (it.intersects(cellRange)) {
        // console.log('intersects')
        return true
      }
    }
    return false
  }

  union(cellRange) {
    let cr = cellRange
    this._.forEach((it) => {
      if (it.intersects(cr)) {
        cr = it.union(cr)
      }
    })
    return cr
  }

  add(cr) {
    this.deleteWithin(cr)
    this._.push(cr)
  }

  // type: row | column
  shift(type, index, n, cbWithin) {
    this._.forEach((cellRange) => {
      const {
        sri, sci, eri, eci,
      } = cellRange
      const range = cellRange
      if (type === 'row') {
        if (sri >= index) {
          range.sri += n
          range.eri += n
        } else if (sri < index && index <= eri) {
          range.eri += n
          cbWithin(sri, sci, n, 0)
        }
      } else if (type === 'column') {
        if (sci >= index) {
          range.sci += n
          range.eci += n
        } else if (sci < index && index <= eci) {
          range.eci += n
          cbWithin(sri, sci, 0, n)
        }
      }
    })
  }

  move(cellRange, rn, cn) {
    this._.forEach((it1) => {
      const it = it1
      if (it.within(cellRange)) {
        it.eri += rn
        it.sri += rn
        it.sci += cn
        it.eci += cn
      }
    })
  }

  setData(merges) {
    this._ = merges.map(merge => CellRange.valueOf(merge))
    return this
  }

  getData() {
    return this._.map(merge => merge.toString())
  }

  clear() {
    this._ = []
    return this
  }

  isColMerge() {
    return this._.some(({ sri, eri }) => sri !== eri)
  }

  init(rows, data) {
    // 初始化合并单元格
    this.clear()
    for (let [row, val] of Object.entries(data)) {
      // 获取隐藏行 14283 【隐藏行】隐藏行失效。（但是单纯文本行又隐藏）
      if (val['hide']) rows.hideRows.push(parseInt(row))
      for (let [col] of Object.entries(val['cells'])) {
        const [rn, cn] = rows.getCellMerge(row, col)
        if (rn || cn) {
          // @ts-ignore
          row = parseInt(row)
          // @ts-ignore
          col = parseInt(col)
          this.add(new CellRange(row, col, row + parseInt(rn), col + parseInt(cn)))
        }
      }
    }
  }
}

export default {}
export {
  Merges,
}
