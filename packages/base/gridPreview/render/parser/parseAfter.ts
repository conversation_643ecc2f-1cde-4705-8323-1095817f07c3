import { CELL_TYPE, RENDER_CELL_TYPE, TABLE_TYPE } from '../../common/constant'
import { removeRows, removeCols, getProp } from './utils'
import { getCellRenderPos } from './Cell/index'
import cellParser from './Cell/cell'
const THEME_MAPS = {
  classicWhite: 'sdp-classic-white',
  darkBlue: 'sdp-dark-blue',
  deepBlue: 'sdp-deep-blue',
}

const BORDER_COLOR = {
  [THEME_MAPS.classicWhite]: {
    'border-color': '#999999',
  },
  [THEME_MAPS.darkBlue]: {
    'border-color': '#313546',
  },
  [THEME_MAPS.deepBlue]: {
    'border-color': '#313546',
  },
}

const parseAfter = (data, options) => {
  // 注意函数调用顺序
  formatData(data, options)
  // gridFixed: 固定行列
  setFreeze(data, options)
  setHeaderIconOder(data, options)
  hideCol(data, options)
  setColAndRow(data, options)
  // 设置折叠列：需要用到col.len
  setCollapseCol(data, options)
  addSerialNum(data, options)
  setBorderMerge(data, options)
  exportHideRemove(data, options)
  setHeadRange(data, options)
  options.statusFields.isExport && setRowColWidth(data, options)
}

export default parseAfter

export enum ADAPTIVE_TYPE {
  WIDTH = '1',
  HEIGHT = '2'
}

export const CELL_PADDING = { top: 2, left: 3, right: 3, bottom: 2 }

function setBorderMerge(data, options) {
  const rows = data.rows
  const cellsConfig = data.cellsConfig

  const keys = Object.keys(cellsConfig).filter((key) => cellsConfig[key]?.meta?.content?.styleConfig?.lastBorderType === 'merge')
  if (!keys.length) return

  // const cloneKeys = new Set()
  const mergeRange: any[] = []
  const [fr, fc] = options.freeze
  const cLen = data.cols.len
  const rLen = data.rows.len
  // const styleKey = {}
  const hideCols = options.headerHideCol || []
  const themeType = options.themeType

  Object.entries(rows).forEach(([r, { cells }]) => {
    if (r === 'len') return
    Object.entries(cells).forEach(([c, cell]) => {
      if (keys.includes(cell.config) && cellsConfig[cell.config].style['border-style']) {
        c = +c
        r = +r

        const config = cellsConfig[cell.config]
        const style = config.style

        const [rm, cm] = config.merge || [0, 0]

        if (config.merge) {
          const [rm, cm] = config.merge
          mergeRange.push({
            row: [r, r + rm],
            col: [c, c + cm],
            r,
            c,
          })
        }

        // 上
        const rt = r - 1
        if (rt >= 0) {
          let tCell = rows?.[rt]?.cells?.[c]

          if (tCell && cellsConfig[tCell.config].meta.type === 'merge-shadow-cell') {
            const value = mergeRange.find((value) => {

              return rt >= value.row[0] && rt <= value.row[1] && c >= value.col[0] && c <= value.col[1]
            })

            if (value) {
              tCell = rows?.[value.r]?.cells?.[value.c]
            }

          }

          if (tCell && keys.includes(tCell.config)) {
            const s = cellsConfig[tCell.config].style
            const bs = s['border-style'].split(' ')

            if (bs[2] !== 'none') {
                const borderStyle = style['border-style'].split(' ')
                style['border-style'] = `none ${borderStyle[1]} ${borderStyle[2]} ${borderStyle[3]}`
            }
          }
        }
        // 右
        const cr = c + cm + 1
        if (cr < cLen && fc !== (c + 1)) {
          let rCell = rows?.[r]?.cells?.[cr]

          if (rCell && cellsConfig[rCell.config].meta.type === 'merge-shadow-cell') {
            const value = mergeRange.find((value) => {

              return r >= value.row[0] && r <= value.row[1] && cr >= value.col[0] && cr <= value.col[1]
            })

            if (value) {
              rCell = rows?.[value.r]?.cells?.[value.c]
            }

          }

          if (rCell && keys.includes(rCell.config)) {
            const s = cellsConfig[rCell.config].style
            const bs = s['border-style'].split(' ')

            if (bs[3] !== 'none') {
              const borderStyle = style['border-style'].split(' ')
              style['border-style'] = `${borderStyle[0]} none ${borderStyle[2]} ${borderStyle[3]}`
            }
          }
        }
        // 下
        const rb = r + rm + 1
        if (rb < rLen && fr !== (r + 1)) {
          let bCell = rows?.[rb]?.cells?.[c]

          if (bCell && cellsConfig[bCell.config].meta.type === 'merge-shadow-cell') {
            const value = mergeRange.find((value) => {

              return rb >= value.row[0] && rb <= value.row[1] && c >= value.col[0] && c <= value.col[1]
            })

            if (value) {
              bCell = rows?.[value.r]?.cells?.[value.c]
            }
          }

          if (bCell && keys.includes(bCell.config)) {
            const s = cellsConfig[bCell.config].style
            const bs = s['border-style'].split(' ')

            if (bs[0] !== 'none') {
              const borderStyle = style['border-style'].split(' ')
              style['border-style'] = `${borderStyle[0]} ${borderStyle[1]} none ${borderStyle[3]}`
            }
          }
        }
        // 左
        const cl = c - 1
        if (cl >= 0) {
          let lCell = rows?.[r]?.cells?.[cl]

          if (lCell && cellsConfig[lCell.config].meta.type === 'merge-shadow-cell') {
            const value = mergeRange.find((value) => {

              return r >= value.row[0] && r <= value.row[1] && cl >= value.col[0] && cl <= value.col[1]
            })

            if (value) {
              lCell = rows?.[value.r]?.cells?.[value.c]
            }

          }

          if (lCell && keys.includes(lCell.config)) {
            const s = cellsConfig[lCell.config].style
            const bs = s['border-style'].split(' ')

            if (bs[1] !== 'none') {
              const borderStyle = style['border-style'].split(' ')
              style['border-style'] = `${borderStyle[0]} ${borderStyle[1]} ${borderStyle[2]} none`
            }
          }
        }
        if (options.statusFields.isExport) {
          // 处理隐藏列
          const left = style['border-style'].split(' ').pop()
          if (left === 'none') {
            const cl = c - 1
            let lCell = rows?.[r]?.cells?.[cl]
            if (lCell && hideCols.includes(cl)) {
              const s = cellsConfig[lCell.config].style
              const bs = s['border-style'].split(' ')
              if (bs[1] !== 'none') {
                const bw = s['border-width'].split(' ')
                const bc = s?.['border-color']?.match(/rgba?\([^)]*\)|#[\d\w]{6}/g)
                const lBColor = bc?.[1] || style['border-color'] || BORDER_COLOR[themeType]['border-color']
                const borderStyle = style['border-style'].split(' ')
                const borderWidth = style['border-width'].split(' ')
                const borderColor = style?.['border-color']?.match(/rgba?\([^)]*\)|#[\d\w]{6}/g)
                style['border-style'] = `${borderStyle[0]} ${borderStyle[1]} ${borderStyle[2]} ${bs[1]}`
                style['border-width'] = `${borderWidth[0]} ${borderWidth[1] || borderWidth[0]} ${borderWidth[2] || borderWidth[0]} ${bw[1] || bw[0]}`
                const lBorderColor = borderColor?.[0] || style['border-color'] || BORDER_COLOR[themeType]['border-color']
                const rBorderColor = borderColor?.[1] || style['border-color'] || BORDER_COLOR[themeType]['border-color']
                const bBorderColor = borderColor?.[2] || style['border-color'] || BORDER_COLOR[themeType]['border-color']
                style['border-color'] = `${lBorderColor} ${rBorderColor} ${bBorderColor} ${lBColor}`
              }
            }
          }
        }

        // let site = `${cell.config}$$${r}$$${c}`
        // let isHasSite = false

        // Object.entries(styleKey).forEach(([key, val]) => {
        //   if (val === config.style['border-style']) {
        //     const [k] = key.split('$$')

        //     if (k === cell.config) {
        //       site = key
        //       isHasSite = true
        //     }
        //   }
        // })

        // cloneKeys.add(cell.config)
        // cell.config = site

        // if (isHasSite) return

        // styleKey[site] = config.style['border-style']
        // cellsConfig[site] = config
        // keys.push(site)
      }
    })
  })

  // ;[...cloneKeys].forEach((key) => {
  //   delete cellsConfig[key]
  // })
}

function calcCellTextWidthOrHeight(text, style = {}) {
  console.log('设置row col宽度 calcCellTextWidthOrHeight', text, style)

  const div = document.createElement('div')
  div.innerText = text
  div.style.visibility = 'hidden'
  div.style.position = 'absolute'

  Object.keys(style).forEach(key => {
    style[key] && (div.style[key] = style[key])
  })

  document.body.append(div)
  const width = div.offsetWidth
  const height = div.offsetHeight
  document.body.removeChild(div)

  return { width, height }
}

export function setRowColWidth(data, options) {
  const isNode = typeof global === 'object' && typeof window === 'undefined'
  if (isNode) {
    return void '导出不对数据做处理'
  }

  const cellsConfig = data.cellsConfig
  const cols = data.cols

  for (let rowData of Object.entries(data.rows)) {
    const [rIndex, rData]: any[] = rowData
    if (rIndex === 'len') continue

    const cells = rData?.cells || {}

    for (let cData of Object.entries(cells)) {
      const [cIndex, cell]: any[] = cData

      const { text = '', config }: any = cell || {}
      const configData = cellsConfig[config]
      const adaptive = configData?.meta?.content?.adaptive

      if (!text || !adaptive) continue

      let cWidth = cols[cIndex].width
      let cHeight = rData.height
      const { style } = configData

      // 数组[行,列]
      let mWidth = 0
      let mHeight = 0
      const merge = configData['merge'] || cell['merge'] || []
      merge.forEach((num, index) => {
        if (!num) return

        while (num) {
          if (index) {
            const width = cols[+cIndex + 1].width
            if (typeof width !== 'number') {
              console.log(`%c 行合并获取的宽度有问题value:${width}`, 'color: red')
            } else {
              mWidth += width
            }
          } else {
            const height = data.rows[+rIndex + 1].height
            if (typeof height !== 'number') {
              console.log(`%c 行合并获取的高度有问题value:${height}`, 'color: red')
            } else {
              mHeight += height
            }
          }

          num--
        }
      })

      const tWidth = cWidth + mWidth
      const tHeight = cHeight + mHeight

      const { width, height } = calcCellTextWidthOrHeight(`${text}`, {
        fontSize: style['font-size'] || '12px',
        fontFamily: style['font-family'] || 'NotoSansHans-Regular',
        lineHeight: style['line-height'] || style['font-size'] || '12px',
        paddingLeft: style['padding-left'],
        fontWeight: style['font-weight'],
        fontStyle: style['font-style'],
        borderColor: style[' border-color'],
        borderStyle: style['border-style'],
        borderWidth: style['border-width'],
        paddingRight: (CELL_PADDING.right + CELL_PADDING.left) + 'px',
        paddingTop: CELL_PADDING.top + 'px',
        paddingBottom: CELL_PADDING.bottom + 'px',
        width: adaptive === ADAPTIVE_TYPE.WIDTH ? undefined : `${tWidth}px`,
        height: adaptive === ADAPTIVE_TYPE.HEIGHT ? undefined : `${tHeight}px`,
        wordBreak: 'break-all'
      })

      if (tWidth < width && adaptive === ADAPTIVE_TYPE.WIDTH) {
        // 加1减去小数误差产生****的问题
        cols[cIndex].width = width - mWidth + 1
      }
      if (tHeight < height && adaptive === ADAPTIVE_TYPE.HEIGHT) {
        rData.height = height - mHeight
      }
    }
  }
}

/**
 * 导出处理表头范围
 * @param data
 * @param options
 * @returns
 */
function setHeadRange(data, options) {
  const tableHeaderDTO = {
    startRow: 0,
    endRow: 0,
    startColumn: 0,
    endColumn: 0,
  }
  const { cellsConfig = {}, rows = {} } = data

  const headerKeys = Object.entries(cellsConfig).filter(([key, { meta }]: any[]) => meta.type === RENDER_CELL_TYPE.header).map(([key]) => key)

  if (!headerKeys.length) {
    data.tableHeaderDTO = tableHeaderDTO
    return
  }

  Object.values(rows).forEach((row: any, r) => {
    const { cells = {} } = row || {}
    Object.values(cells).forEach((cell: any, c) => {
      if (headerKeys.includes(cell?.config)) {
        const { merge: [mRow, mCol] = [0, 0] } = cellsConfig[cell.config]
        // 后台规定行列从1开始
        const cRow = Number(r) + 1
        const cCol = Number(c) + 1

        const { startRow, endRow, startColumn, endColumn } = tableHeaderDTO

        if (!startRow || startRow > cRow) {
          tableHeaderDTO.startRow = cRow
        }
        if (!startColumn || startColumn > cCol) {
          tableHeaderDTO.startColumn = cCol
        }
        if (!endRow || endRow < cRow) {
          tableHeaderDTO.endRow = cRow + mRow
        }
        if (!endColumn || endColumn < cCol) {
          tableHeaderDTO.endColumn = cCol + mCol
        }
      }
    })
  })

  data.tableHeaderDTO = tableHeaderDTO
}

function formatData (data, options) {
  const { cellsConfig } = data
  const { groupMerge, extendDirect } = options
  const rows = data.rows
  const setMergeIndex = extendDirect === '2' ? 1 : 0 // 只有全部横向扩展的表格，才考虑列合并

  // 处理 group 分组
  if (groupMerge && groupMerge.length) {
    let colsFoRowGroupMerge = groupMerge.filter(e => e.extend === '1').map(e => e.columnIndex)
    let rowsForColGroupMerge = groupMerge.filter(e => e.extend === '2').map(e => e.rowIndex)

    if (rowsForColGroupMerge.length) {
      rowsForColGroupMerge.forEach(ri => {
        const cells = rows[ri].cells
        for (let ci in cells) {
          if (cells.hasOwnProperty(ci)) {
            const c = cells[ci]
            deleteGroupRepeatData(c, ci, cells, cellsConfig, 1)
          }
        }
      })
    }
    if (colsFoRowGroupMerge.length) {
      const rows = data.rows
      for (let ri in rows) {
        if (rows.hasOwnProperty(ri) && ri !== 'len') {
          const rowData = rows[ri]
          if (rowData) {
            const cells = rowData.cells
            colsFoRowGroupMerge.forEach(ci => {
              const c = cells[ci]
              deleteGroupRepeatData(c, ci, cells, cellsConfig, 0)
            })
          }
        }
      }
    }
  }
}

// 删除group分组的重复数据
function deleteGroupRepeatData(c, i, cells, cellsConfig, setMergeIndex) {
  if (c && (typeof c.text === 'object')) {
    if (c.text.rNum === 0) {
      delete cells[i]
    } else {
      const config = cellsConfig[c.config]
      const merge = config.merge || [0, 0]
      c.merge = [...merge]
      c.merge[setMergeIndex] = c.text.rNum * (merge[setMergeIndex] + 1) - 1 // 合并增加的数量
      c.text = c.text.text
    }
  }
}

/**
 * 设置固定行、列属性
 */
function setFreeze(data, options) {
  const { freeze } = options
  if (freeze) {
    data.freeze = freeze
  }
}

/**
 * 设置表头操作图标顺序
 */
function setHeaderIconOder(data, options) {
  const { iconOrder } = options
  const { cellsConfig } = data
  if (iconOrder) {
    for (let i in cellsConfig) {
      const config = cellsConfig[i]
      if (config.meta.headerOptions) {
        const headerIconData = iconOrder.filter(e => e.id === config.meta.id)[0] || { iconOrder: ['sort', 'search'] }
        config.meta.headerOptions.iconOrder = headerIconData.iconOrder
      }
    }
  }
}

/**
 * 设置行高、列宽等配置
 */
function setColAndRow(data, options) {
  const { rows, cols } = data
  const rowKeys = Object.keys(data.rows).filter(k => k !== 'len')
  // 行高
  rows.len = rowKeys.length ? Math.max.apply(Math, rowKeys) + 1 : 0
  // 列宽
  let maxCol = 0
  for (let i in data.rows) {
    if (i !== 'len') {
      const len = Math.max.apply(Math, Object.keys(data.rows[i].cells)) + 1
      maxCol = Math.max(maxCol, len)
    }
  }
  cols.len = maxCol
  // 从data中去矫正过的数据
  const { collapseColHide = [], headerHideCol = [] } = data
  // 非导出场景，需要计算每列的宽度
  if (collapseColHide.length || headerHideCol.length) {
    for (let cIndex = 0; cIndex < maxCol; cIndex++) {
      const colObj = cols[cIndex] = cols[cIndex] || {}
      if (options.statusFields.isExport) {
        // 导出时，已经过滤掉隐藏元素
        colObj.width = getColWidth(data, cIndex)
      } else {
        colObj.width = (collapseColHide.includes(cIndex) || headerHideCol.includes(cIndex))
          ? 0 : getColWidth(data, cIndex)
      }
    }
  } else {
    for (let cIndex = 0; cIndex < maxCol; cIndex++) {
      const colObj = cols[cIndex] = cols[cIndex] || {}
      colObj.width = getColWidth(data, cIndex)
    }
  }
}

/**
 * 添加序列号
 * 目前先不考虑Metric 和折叠列的影响
 * @param data
 * @param options
 */
function addSerialNum(data, options) {
  const { serialNumber } = options
  if (!serialNumber || !serialNumber.length) {
    return void ''
  }
  const { rows, cellsConfig } = data
  serialNumber.forEach(config => {
    const { displayColumn, numRange, numRange2, startNum } = config
    let { sri } = getCellRenderPos(numRange, displayColumn, options)
    let eri = numRange2 ? (getCellRenderPos(numRange2, displayColumn, options).sri || (rows.len + 1)) : (rows.len + 1)
    let n = startNum ? parseInt(startNum) : 1
    const emptyCell = { text: '', config: '_cell' }
    let needAddEmptyConfig = false
    // 序号设置是左开右闭区间
    for (;sri < eri; sri++) {
      if (rows[sri]) {
        const cell = rows[sri].cells[displayColumn]
        const text = n + ''
        if (cell) {
          cell.text = text
        } else {
          needAddEmptyConfig = true
          rows[sri].cells[displayColumn] = Object.assign({}, emptyCell, { text })
        }
      }
      n++
    }
    if (needAddEmptyConfig && !cellsConfig['_cell']) {
      cellsConfig['_cell'] = cellParser.createEmptyCell()
    }
  })
}

function getColWidth (data, cIndex) {
  const { rows, cellsConfig } = data
  for (let rIndex in rows) {
    if (rows.hasOwnProperty(rIndex) && rIndex !== 'len') {
      const curCell = getProp(rows, `${rIndex}.cells.${cIndex}`)
      // 过滤合并单元格影响的空单元格
      if (curCell && typeof curCell.config === 'string') {
        const cell = cellsConfig[curCell.config]
        if ((!cell.merge || cell.merge[0]) && // 不是列合并
          (cell.meta.type !== CELL_TYPE.mergeShadow) && // 不是合并区域的空单元格
          !cell.meta.isColMergeCell &&
          cell.style.width &&
          parseInt(cell.style.width) !== 155
          // 空单元格. bug :19586、19880 场景： 全列都为空， 被合并单元格
          // (cell.meta.type !== CELL_TYPE.empty) &&
          ) {
          return parseInt(cell.style.width)
        }
      }
    }
  }
  return 155 // 默认列宽 155
}

/**
 * 隐藏列
 * 范围：表头控制列、折叠隐藏列
 * 目前仅支持单列隐藏，不能被横向扩展，横向扩展出来得列先不管，已和产品确认
 * @param data
 * @param options
 */
function hideCol(data, options) {
  let { collapseColHide = [], headerHideCol = [] } = options
  data.hideCols = []
  // 表头隐藏列
  if (headerHideCol.length) {
    const { col } = options.extendMap
    headerHideCol = adjustmentColForExtend(headerHideCol, options, 'col')
    data.headerHideCol = headerHideCol
  }
  // 折叠隐藏列
  if (collapseColHide.length) {
    collapseColHide = adjustmentColForExtend(collapseColHide, options, 'col')
    data.collapseColHide = collapseColHide
  }
  data.hideCols = [...collapseColHide, ...headerHideCol].map(e => parseInt(e)).sort((a, b) => a - b)
}

/**
 * 将隐藏列的列宽设置为0
 */
export function setColWidthFromCell() {
}

/**
 * 折叠列
 * 折叠列的不考虑横向扩展
 * Tips: 目前仅考虑简单场景，不考虑表头隐藏的影响
 * @param data
 * @param options
 */
function setCollapseCol(data, options) {
  const { collapseCol: collapseColData, store, tableMap, collapseColHide } = options

  let collapseCol = collapseColData

  if (!collapseCol) {
    return false
  }

  collapseCol = Object.keys(collapseCol).filter((key) => !collapseColHide.includes(+key)).reduce((pre, next) => {
    pre[next] = collapseCol[next]
    return pre
  }, {})

  if (!Object.keys(collapseCol)?.length) return false

  const { rows, cols } = data
  const adjustCollapseCol: number[] = adjustmentColForExtend(Object.keys(collapseCol).map(e => parseInt(e)), options, 'col')
  // 特殊处理表头
  store.collapseHeaderRow = null
  store.collapseRows = {}

  const CollapseNum = {}
  let num = 0
  for (let i = 0; i < cols.len; i++) {
    CollapseNum[i] = num
    if (adjustCollapseCol?.includes(i)) {
      num++
    }
  }

  for (let rIndex in rows) {
    if (rows.hasOwnProperty(rIndex) && rIndex !== 'len') {
      const cells = rows[rIndex].cells || {}
      store.collapseRows[rIndex] = { cells: {}, height: rows[rIndex].height }
      const tempCells = {}
      const collapseCells = {}
      let collapseStart = 1
      for (let cIndex = 0; cIndex < cols.len; cIndex++) {
        if (adjustCollapseCol.includes(cIndex)) {
          collapseCells[collapseStart++] = cells[cIndex]
        } else {
          let index = cIndex - CollapseNum[cIndex]
          tempCells[index] = cells[cIndex]
        }
      }
      // 被隐藏的列
      store.collapseRows[rIndex].cells = collapseCells
      // 正常展示的列
      rows[rIndex].cells = tempCells
    }
  }

  cols.len -= adjustCollapseCol.length
  // 设置渲染单元格类型
  for (let rIndex = 0; rIndex < tableMap.length; rIndex++) {
    const rowData = tableMap[rIndex] || []
    if (rowData[0] && rowData[0].type === CELL_TYPE.variable) {
      data.cellsConfig[rowData[0].id].meta.type = RENDER_CELL_TYPE.fold
      if (rIndex !== 0) {
        store.collapseHeaderRow = store.collapseRows[rIndex - 1]
      }
      return void '目前不只支持同列，多个折叠'
    }
  }
}

/**
 * 调整列数据
 * 由于横向扩展，导致隐藏、折叠等列数据不正确
 */
function adjustmentColForExtend(arr, options, propName): number[] {
  const { extendMap, mergeMap } = options
  const { colRangeMap, rowRangeMap } = mergeMap
  if (!propName) {
    return []
  }
  // TODO 考虑设置行、列之前的扩展数据，导致设置数据需要调整
  // 考虑：扩展数据、横向扩展数据、合并单元格
  // getCellRenderPos函数性能后期可以优化
  let ret = []
  if (propName === 'col') {
    arr.forEach(cIndex => {
      const { sci, colLen } = getCellRenderPos(0, cIndex, options)
      if (colLen > 1) {
        const mergeLen = (colRangeMap.get(cIndex) || { len: 1 }).len
        for (let i = 0; i < colLen; i++) {
          ret.push(sci + i * mergeLen)
        }
      } else {
        ret.push(sci)
      }
    })
  } else if (propName === 'row') {
    // TODO 没有考虑层级表
    // row
    arr.forEach(rIndex => {
      const { sri, rowLen } = getCellRenderPos(rIndex, 0, options)
      if (rowLen > 1) {
        const mergeLen = (rowRangeMap.get(rIndex) || { len: 1 }).len
        for (let i = 0; i < rowLen; i++) {
          ret.push(sri + i * mergeLen)
        }
      } else {
        ret.push(sri)
      }
    })
  }

  ret.sort((a, b) => a - b)
  return ret
}

/**
 * 表格导出时，移除隐藏的数据
 * @param data
 * @param options
 * @returns {*}
 */
function exportHideRemove(data, options) {
  if (!options.statusFields.isExport) {
    return void '正常渲染不需要删除隐藏的数据'
  }

  // 需要调整merge的数据，row.cells。merge 和 config.merge
  let removeStartIndexDiff = 0
  for (let key in Object.keys(data.rows)) {
    if (key !== 'len') {
      let ri = parseInt(key) - removeStartIndexDiff
      if (data.rows[ri] && data.rows[ri].hide) {
        removeRows(data.rows, ri, 1)
        removeStartIndexDiff++
      }
    }
  }
  // 导出过滤隐藏行数据
  removeStartIndexDiff = 0

  // data.hideCols && data.hideCols.forEach(cIndex => {
  //   removeCols(data, cIndex - removeStartIndexDiff++, 1,)
  // })

  removeCols(data)
  // 调整隐藏后的列、行数量
  setColAndRow(data, options)
}
