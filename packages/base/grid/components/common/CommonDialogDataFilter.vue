<template>
  <el-dialog
      :custom-class="`sdp-dialog ${getCurrentThemeClass()}`"
      :class=[themeType]
      :visible.sync="dialogVisible"
      :modal="true"
      width="1010px"
      :close-on-click-modal="false"
      @open="handleDialogOpen"
      @close="closeHandler"
      append-to-body
  >
    <!-- Dialog Title -->
    <template slot="title">
      <span class="dialog-title">{{ dialogTitle }}</span>
      <!--<el-tooltip effect="dark" :content="$t('sdp.message.verification')" placement="top">-->
      <!--  <i class="icon-sdp-info"></i>-->
      <!--</el-tooltip>-->

      <el-tooltip
        v-if="!isPolymericType && !onlyIndex"
        effect="dark"
        :content="filterTipText"
        placement="top"
      >
        <div slot="content" style="width: 370px">
          <p
            v-for="(tip, i) in filterTipText.split(' \n ')"
            :key="i"
            :style="{ 'margin-top': i !== 0 ? '4px' : '0' }"
          >
            {{ tip }}
          </p>
        </div>
        <i class="el-tooltip icon-sdp-info"/>
      </el-tooltip>
    </template>

    <el-tabs
      v-model="activeTab"
      type="card"
      :class="{ 'isHideTabs': tabs.length <= 1 }"
    >
      <el-tab-pane
        :key="tabs[0].name"
        :name="tabs[0].name"
        :label="tabs[0].label"
      >
        <div slot="label" :title="tabs[0].label" class="tab-label">
          <div class="label">
            {{ tabs[0].label }}
          </div>

          <el-tooltip
            effect="dark"
            :content="$t('sdp.views.FilteringTheDataset')"
            placement="top"
          >
            <i class="el-tooltip icon-sdp-info"/>
          </el-tooltip>
        </div>

    <!-- Dialog Body -->
    <el-collapse v-if="!isPolymericType" accordion v-model="bookmark" style="width: 952px;" class="custom-collapse-content">
      <el-collapse-item :title="dataArr.labeName" v-for="(dataArr, index) in datasetLists" :key="dataArr.id" :name="index">
        <!-- 筛选条件 -->
        <el-row type="flex" justify="space-between" align="middle" class="tools-bar" :style="dataMap[dataArr.id].condition.length> 0 ? '' : 'margin-bottom: 0px'">
          <el-col :span="-1">
            <span class="radio-label">{{ $t('sdp.views.filterConditions') }}:</span>
            <el-radio-group v-model="dataMap[dataArr.id].type">
              <el-radio label="condition" style="margin-right: 20px;">{{ $t('sdp.views.filter') }}</el-radio>
              <el-radio label="expression" v-if="!$getFeatureConfig || !$getFeatureConfig('expFilter.hidden') ">{{ $t('sdp.views.expFilter') }}</el-radio>
            </el-radio-group>
          </el-col>

          <!-- 新增 -->
          <el-col :span="-1" v-if="dataMap[dataArr.id].type === 'condition'">
            <el-button
              class="add-button"
              type="text"
              @click="add(dataArr.id)"
            >
              <i class="icon-sdp-add"></i>
              {{ $t('sdp.button.add') }}
            </el-button>
          </el-col>
        </el-row>

        <!-- 条件过滤 -->
        <div v-if="dataMap[dataArr.id].type === 'condition'">
          <!-- Form-->
          <ConditionFilter
            v-for="(item, i) in dataMap[dataArr.id].condition"
            :key="i"
            v-bind="{
              filterItem:item,
              filterIndex:i,
              dateFnList: isTableCellFilter ? [] : dateFnList,
              dataArr,
              dataMap,
              offsetFn,
              }"
          />
        </div>

        <!-- 表达式过滤 -->
        <el-form
          v-else
          label-position="top"
          label-width="80px"
          size="small"
          class="expression-form"
        >
          <!-- 表达式编辑区域 -->
          <div style="margin-bottom: 12px">
            <el-form-item style="width: 918px">
              <el-input
                type="textarea"
                resize="none"
                :rows="6"
                :placeholder="$t('sdp.placeholder.pls')"
                :ref="'expressionRef_' + dataArr.id"
                v-model="dataMap[dataArr.id].expression"
                @change="checkExpression(dataMap[dataArr.id])"
              ></el-input>
            </el-form-item>
          </div>

          <!-- 选择区域 -->
          <div style="font-size: 0;display: flex;">
            <!-- 字段 -->
            <el-form-item
              :label="$t('sdp.views.field')"
              style="display: inline-block; width: 389px;"
              :style="{
                width: isTableCellFilter ? '458px' : '389px',
              }"
            >
              <calcOptions
                :list="getCalcFieldList(dataArr.children)"
                :isDataSet="true"
                @onClick="updateExpression($event, dataArr.id, index, 'field')"
              />
            </el-form-item>

            <!-- 操作符 -->
            <el-form-item
              :label="$t('sdp.views.operationWord')"
              style="display: inline-block; width: 198px; margin: 0 8px;"
              :style="{
               width: isTableCellFilter ? '458px' : '198px',
               margin: isTableCellFilter ? '0 0 0 8px' : '0 8px',
              }"
            >
              <calcOptions
                :list="sizeofList"
                @onClick="updateExpression($event, dataArr.id, index, 'operation')"
              />
            </el-form-item>

            <!-- 函数 -->
            <el-form-item
              :label="$t('sdp.views.fns')"
              style="display: inline-block; width: 329px;"
              v-if="!isTableCellFilter"
            >
              <calcOptions
                :list="fnList"
                @onClick="updateExpression($event, dataArr.id, index, 'date')"
              >
                <template slot-scope="{ item }">
                  <div v-if="item.value === 'dateSel'" style="height: 22px;">
                    <div class="mock-data-picker" style="left: 10px;">
                      <i class="el-icon-date">&nbsp; {{ item.label }}</i>
                    </div>
                    <el-date-picker
                      v-model="datePickerVal"
                      type="date"
                      :popper-class="`${getCurrentThemeClass()} sdp-params-customdata-style data-filter-date-popper`"
                      class="real-data-picker"
                      value-format="yyyy-MM-dd"
                      :placeholder="$t('sdp.placeholder.pleaseSelectDate')"
                      @change="updateExpression($event, dataArr.id, index, 'date-picker')"
                    ></el-date-picker>
                  </div>
                </template>
              </calcOptions>
            </el-form-item>
          </div>
        </el-form>
      </el-collapse-item>

      <div class="date-calculation-box" v-if="!isTableCellFilter && !onlyIndex">
        <el-tooltip effect="dark" v-if="!dateFnAble" :content="$t('sdp.views.dateFnCalculationMethodTips')" placement="bottom">
          <i class="icon-sdp-info"></i>
        </el-tooltip>
        <span class="date-calculation-radio-label">{{ $t('sdp.views.dateFnCalculationMethod') }}:
          <div v-if="showCalculateBase" style="width: 14px;height: 100%;display: inline-block;"/>
        </span>
        <el-radio-group v-if="dateFnAble" v-model="caculationMethodType" >
          <el-radio v-for="(item,index) in caculationMethodList" :label="item.value" :key="index" class="refer-radio">{{ item.label }}</el-radio>
        </el-radio-group>
        <p v-else class="date-calculation-prompt">{{ caculationMethodTypeTxt }}</p>
      </div>

      <div class="date-calculation-box" :class="{ 'is-en': isEn }" v-if="showCalculateBase && !onlyIndex">
        <div class="date-calculation-radio-label" style="display: flex;align-items: center;">
          {{ $t('sdp.views.CalculateDateBase') }}:

          <el-tooltip effect="dark" placement="bottom">
            <div slot="content">
              <p>1. {{ $t('sdp.views.DataFilteringTips') }}</p>
              <p style="margin-top: 4px;">2. {{ $t('sdp.views.DataFilteringAndCalendarTips') }}</p>
            </div>
            <!--<div style="width: 18px;height: 100%;display: inline-block;">-->
              <i class="icon-sdp-info" style="line-height: 14px;"></i>
            <!--</div>-->
          </el-tooltip>
        </div>

        <el-radio-group v-model="disabledCalendarFnFilter" >
          <el-radio
            v-for="(item,index) in caculationBaseList"
            :label="item.value"
            :key="index"
            :disabled="'system' === caculationMethodType"
            class="refer-radio"
            :style="{ minWidth: isEn ? '162px' : '120px' }"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </el-collapse>

        <!--注意！下面还有一个类似的-->
        <!--聚合过滤，用于图形-->
        <template v-else>
          <div slot="label" :title="$t('sdp.views.PolymericFilter')">
            {{ $t('sdp.views.PolymericFilter') }}

            <el-tooltip
              effect="dark"
              :content="$t('sdp.views.FilteringTheResults')"
              placement="top"
            >
              <i class="el-tooltip icon-sdp-info"/>
            </el-tooltip>
          </div>

          <div>
            <div style="width: 100%;text-align: right">
              <el-button
                class="add-button"
                type="text"
                @click="polymericAdd('metric')"
              >
                <i class="icon-sdp-add"></i>
                {{ $t('sdp.button.add') }}
              </el-button>
            </div>

            <div>
              <template v-for="(item, index) in polymericList">
                <condition-filter-simple
                  :key="item.id"
                  :firstKey="'metric'"
                  :firstLabel="$t('sdp.views.measure')"
                  v-bind="{
                    filterItem: item,
                    filterIndex: index,
                    firstOptions: chartPolymericCheckOptionList,
                  }"
                  @delete="polymericDelete"
                />
              </template>
            </div>
          </div>
        </template>

      </el-tab-pane>

      <el-tab-pane
        v-if="tabs.length > 1"
        :key="tabs[1].name"
        :name="tabs[1].name"
        :label="tabs[1].label"
      >
        <!--注意！上面还有一个类似的-->
        <!--tab第二页的聚合过滤，用于表格-->
        <div slot="label" :title="tabs[1].label" class="tab-label">
          <div class="label">
            {{ tabs[1].label }}
          </div>

          <el-tooltip
            effect="dark"
            :content="$t('sdp.views.FilteringTheResults')"
            placement="top"
          >
            <i class="el-tooltip icon-sdp-info"/>
          </el-tooltip>
        </div>

        <div>
          <div style="width: 100%;text-align: right">
            <el-button
              class="add-button"
              type="text"
              @click="polymericAdd"
            >
              <i class="icon-sdp-add"></i>
              {{ $t('sdp.button.add') }}
            </el-button>
          </div>

          <div>
            <template v-for="(item, index) in polymericList">
              <condition-filter-simple
                :key="item.id"
                :firstLabel="$t('sdp.views.cell')"
                v-bind="{
                  filterItem: item,
                  filterIndex: index,
                  firstOptions: polymericCheckOptionList,
                }"
                @delete="polymericDelete"
              />
            </template>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- Dialog Footer -->
    <div slot="footer">
      <el-button type="primary" @click="onAreaSubmit" :loading="loading">{{ $t('sdp.button.ensure') }}</el-button>
      <el-button @click="closeDialog">{{ $t('sdp.button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import calcOptions from './CalcOptions'

import { WORKSPACE, DATA_FILTER } from '../../helpers/constants'
import ConditionFilter from './filterItem/ConditionFilter'
import { VIRTUAL_DATASET_KEY } from '../../../../assets/constant'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import ConditionFilterSimple from "packages/base/grid/components/common/filterItem/ConditionFilterSimple.vue";
import {generateCoordinateForCell} from "packages/base/grid/helpers/utils/gridTable";
import { config } from '@vue/test-utils'

// 默认展开第一个
const defaultBookmark = [0]

function formatterVal(current) {
  const str = DATA_FILTER.defaultCaculationMethod
  return current || str
}

function formatterOffset(item) {
  if (item.hasOwnProperty('offset')) {
    if (!item.hasOwnProperty('rightOffset')) {
      const obj = {
        // leftClosed: true,
        // rightClosed: true,
        rightOffset: 0,
        offset: item.offset * -1
      }
      return { ...item, ...obj }
    }
  }
  return item
}

export default {
  name: 'CommonDialogDataFilter',

  components: {ConditionFilterSimple, calcOptions, ConditionFilter },

  inject: {
    langCode: {
      type: String,
      default: 'zh'
    },
    eventBus: {
      default: null
    },
    aliasDict: {
      default: undefined
    },
    getCurrentThemeClass: {
      default: () => {
        return ''
      }
    },
    utils: 'utils',
    configs: {default: () => false}
  },

  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    datasetList: {
      type: Array,
      default: () => [],
    },
    effectiveCellList: {
      type: Array,
      default: () => [],
    },
    // 聚合过滤的单元格
    polymericCellList: {
      type: Array,
      default: () => [],
    },
    // 根据字段和别名获取展示的名称
    getDatasetLabel: {
      type: Function,
      default: () => {},
    },
    highlightList: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      required: true,
    },
    drillSettings: {
      type: Object,
      default: () => {},
    },
    tableDefaultConfig: {
      type: Object,
      default: () => {},
    },

    // 行索引
    rowIndex: Number,
    // 行数据集ID列表
    rowDatasetIdList: Array,
    // 聚合过滤
    polymeric: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    const offsetFn = [
      { label: 'MxD', min: -90, max: 90, tooltip: 'sdp.tooltips.MxD' },
      { label: 'MxW', min: -52, max: 52, tooltip: 'sdp.tooltips.MxW' },
      { label: 'MxM', min: -36, max: 36, tooltip: 'sdp.tooltips.MxM' },
      { label: 'MxQ', min: -24, max: 24, tooltip: 'sdp.tooltips.MxQ' },
      { label: 'MxY', min: -10, max: 10, tooltip: 'sdp.tooltips.MxY' },
    ]
    const futureDate = [
      'Tomorrow',
      'Next 7 days',
      'Till Weekend',
      'Till Month-end',
      'Till Quarter-end',
      'Till Year-end',
      'Next 30 days',
      'Next 90 days',
      'Next Week',
      'Next Month',
      'Next Quarter',
      'Next Year',
      'Next 12 Months',
      'Next 7 Weeks'
    ]
    const dateFnList = [
      'Last 24 hours',
      'Today',
      'Yesterday',
      'Past 7 days',
      'WTD',
      'MTD',
      'QTD',
      'YTD',
      ...offsetFn.map(v => v.label),
      'Last 30 days',
      'Last 90 days',
      'Last Week',
      'Last Month',
      'Last Quarter',
      'Last Year',
      'Last 12 Months',
      'Last 7 Weeks',
      'YEAR',
      'QUARTER',
      'MONTH',
      'WEEK',
      ...futureDate

    ]
    const sizeofList = [
      {
        label: this.$t('sdp.views.equal'), // 等于
        value: '='
      },
      {
        label: this.$t('sdp.views.notEqual'), // 不等于
        value: '!='
      },
      {
        label: this.$t('sdp.views.empty'), // 为空
        value: 'is null'
      },
      {
        label: this.$t('sdp.views.notEmpty'), // 不为空
        value: 'is not null'
      },
      {
        label: this.$t('sdp.views.Morebig'), // 大于
        value: '>'
      },
      {
        label: this.$t('sdp.views.MorebEq'), // 大于等于
        value: '>='
      },
      {
        label: this.$t('sdp.views.moreLess'), // 小于
        value: '<'
      },
      {
        label: this.$t('sdp.views.LessEq'), // 小于等于
        value: '<='
      },
      {
        label: this.$t('sdp.views.beginIs'), // 开头是
        value: `like 'string%'`
      },
      {
        label: this.$t('sdp.views.EndIs'), // 结尾是
        value: `like '%string'`
      },
      {
        label: this.$t('sdp.views.containIs'), // 包含
        value: `like '%string%'`
      },
      {
        label: this.$t('sdp.views.notContainIs'), // 不包含
        value: `not like '%string%'`
      },
    ]

    const fnList = [
      {
        label: this.$t('sdp.views.dateSel'),
        value: 'dateSel'
      },
    ]

    dateFnList.forEach(fn => {
      const isOffsetFn = offsetFn.find(v => v.label === fn)
      const tooltips = isOffsetFn && isOffsetFn.tooltip
      fnList.push({ label: fn, value: fn, tooltips })
    })

    const caculationMethodList = DATA_FILTER.caculationMethodList.call(this)
    const caculationBaseList = DATA_FILTER.caculationBaseList.call(this)
    return {
      datasetLists: [],
      dataMap: null,

      // 日期选择器的值
      // 选择日期函数的时候会置空该值
      datePickerVal: '',
      // 日期函数列表
      dateFnList,

      // 折叠面板书签
      bookmark: defaultBookmark,

      // 操作符列表
      sizeofList,

      // 函数列表
      fnList,

      loading: false,

      offsetFn,

      caculationMethodList,
      caculationBaseList,

      caculationMethodType: DATA_FILTER.defaultCaculationMethod,

      disabledCalendarFnFilter: DATA_FILTER.disabledCalendarFnFilter,

      // 当前tab, 默认展示数据集过滤
      activeTab: 'DatasetFilter',

      polymericList: [],

      testVal: {"dataSetId":"624659010175356928610","groupFilterType":"and","columnName":"","columnType":"","filterType":"","filterValueType":"value","values":""}
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs.options?.onlyIndex
    },
    isEn() {
      return this.langCode === 'en'
    },
    themeType() {
      return this.utils.themeParameters.themeType
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },

    showCalculateBase() {
      return ['data', 'chart', 'card', 'elementText', TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(this.type)
    },

    // 是否为图形的聚合过滤
    isPolymericType() {
      return this.type === 'polymeric'
    },

    dialogTitle() {
      return (() => {
        switch (this.type) {
          case 'data':
          case 'chart':
          case 'card':
          case TYPE_ELEMENT.CUSTOMER_ELEMENT:
            return this.$t('sdp.views.dataFilter')
          case 'table':
            return this.$t('sdp.views.cellDataFilter')
          case 'row':
            return this.$t('sdp.views.rowDataFilter')
          case 'polymeric':
            return this.$t('sdp.views.PolymericFilter')
          default:
            return this.$t('sdp.views.dataFilter')
        }
      })()
    },

    isTableCellFilter() {
      return this.type === 'table'
    },

    filterTipText() {
      if (this.isTableCellFilter) {
        return this.$t('sdp.tooltips.dataFilterTips1')
      }
      return this.$t('sdp.tooltips.dataFilterTips')
    },

    dateFnAble() {
      return ['data', 'chart', 'card', 'elementText', TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(this.type)
    },

    caculationMethodTypeTxt() {
      return this.caculationMethodList.find((v) => v.value === this.caculationMethodType).label
    },

    // tab页数据， 非表格数据过滤时默认展示一条
    tabs() {
      const list = [{ label: this.$t('sdp.views.DatasetFilter'), name: 'DatasetFilter' }]
      if (this.polymeric) {
        list.push({ label: this.$t('sdp.views.PolymericFilter'), name: 'PolymericFilter' })
      }
      return list
    },

    // 表格聚合过滤可选数据
    polymericCheckOptionList() {
      return this.polymericCellList.map(e => {
        const { aggType, text, activeIndex, dataSetId, customFnData = {}} = e.content
        const textVal = this.aliasDict?.updateExpressionFieldAlias(text, dataSetId, false) || text

        let aggTypeVal = aggType.toUpperCase()
        if (aggTypeVal === 'LGE' && text) {
          const DIC = {
            0: 'Rev_Sqm',
            1: 'Seat_Occ_RT',
            2: 'Tbl_Turn_RT',
            3: 'PEER',
          }
          aggTypeVal = DIC[activeIndex] || ''
        }
        if (aggTypeVal === 'CUSTOM') aggTypeVal = ''

        let temp = aggTypeVal? `(${textVal || ''})` : textVal
        const isLge = ['Rev_Sqm', 'Seat_Occ_RT', 'Tbl_Turn_RT', 'PEER'].includes(aggTypeVal)
        const title = temp === '()' ? '' : isLge ? `${aggTypeVal + '(' + temp.replace('(', '').replace(')', '')})` : aggTypeVal + temp

        const coordinate = generateCoordinateForCell(e)

        const type = customFnData.expReturnType || 'number'
        return {
          label: coordinate, // 改成手动获取坐标
          // title: `${coordinate}: ${title}`,
          title,
          value: e.content.keyName,
          type
        }
      })
    },

    // 图形聚合过滤可选数据
    chartPolymericCheckOptionList() {
      if (this.drillSettings?.layers?.[0]?.metrics?.length) {
        return this.drillSettings.layers[0].metrics.map(item => {
          const label = this.getDatasetLabel(item)
          return {
            label: label,
            title: label,
            value: item.keyName,
            type: item.columnType
          }
        })
      }
      return []
    },
  },

  watch: {
    datePickerVal(v) {
      if (v) {
        setTimeout(() => {
          this.$nextTick(() => (this.datePickerVal = ''))
        }, 200)
      }
    }
  },

  methods: {

    // 确保 input 的值是 number 类型
    ensureNumber(it) {
      const columnTypeIsNumber = it.columnType.toLocaleString() === 'number'
      const typeofIsNumber = typeof it.values === 'number'
      if (columnTypeIsNumber && !typeofIsNumber) it.values = 0
    },
    handleDialogOpen() {
      // 获得数据集
      this.datasetLists = this.dataSetFun()

      this.dataMap = {}
      // 初始化聚合过滤数据和tab页
      this.$set(this, 'polymericList', [])
      this.activeTab = this.tabs[0].name

      switch (this.type) {
        // 单元格数据过滤
        case 'table': {
          const filters = this.highlightList[0].content.filters
          this.caculationMethodType = formatterVal(this.$parent.tableDefaultConfig.caculationMethodType)
          this.initData(filters)
          break
        }
        case 'chart':
        case 'card':
        case 'elementText':
        case TYPE_ELEMENT.CUSTOMER_ELEMENT:
        {
          const filters = this.drillSettings.filters
          this.caculationMethodType = formatterVal(this.drillSettings.caculationMethodType)
          this.disabledCalendarFnFilter = this.drillSettings.disabledCalendarFnFilter || false
          this.initData(filters)
          break
        }
        // 表格数据过滤
        case 'data': {
          const filters = this.tableDefaultConfig.filters
          this.caculationMethodType = formatterVal(this.tableDefaultConfig.caculationMethodType)
          this.disabledCalendarFnFilter = this.tableDefaultConfig.disabledCalendarFnFilter || false
          this.initData(filters)
          break
        }
        // 行数据过滤
        case 'row': {
          // 获取行数据过滤字段 ( Object<Array<Object>> )
          const rowFilterMap = this.tableDefaultConfig.rowFilterMap
          this.caculationMethodType = formatterVal(this.tableDefaultConfig.caculationMethodType)
          // 获取某行的数据过滤信息 ( Array<Object> )
          const filters = rowFilterMap[this.rowIndex]
          this.initData(filters)
          break
        }
        case 'polymeric': {
          // 恢复数据
          const polymericFilters = this.drillSettings.polymericFilters || []
          this.$set(this, 'polymericList', this.$_JSONClone(polymericFilters))
          // 没数据则新增一条
          if (this.polymericList <= 0) {
            this.polymericAdd('metric')
          }
          break
        }
      }

      // 表格聚合过滤初始化
      this.polymericInit()
    },
    // 还原数据
    restore(data) {
      const filters = this.$_JSONClone(data)

      this.datasetLists.forEach(item => {
        const arr = filters.filter(val => item.id === val.dataSetId)
        let type = 'condition'
        let condition = []
        let expression = ''

        if (arr.length === 1 && arr[0].expression) {
          type = 'expression'
          expression = arr[0].expression

          expression = this.aliasDict?.updateExpressionFieldAlias(expression, item.id) || expression
        } else {
          condition = arr.map(item => {
            item.values = item.values[0]
            return formatterOffset(item)
          })
        }

        this.$set(this.dataMap, item.id, { type, condition, expression })
      })
    },
    // 初始默认数据
    initData(filters) {
      if (filters && filters.length) {
        this.restore(filters)
        return
      }

      const res = {}

      this.datasetLists.forEach(item => {
        res[item.id] = {
          type: 'condition',
          condition: [],
          expression: ''
        }
      })

      this.dataMap = res
    },

    dataSetFun() {
      const datasetList = this.$_JSONClone(this.datasetList).filter(item => item.id !== VIRTUAL_DATASET_KEY)
      switch (this.type) {
        case 'table': {
          // 过滤掉表格使用的字段 ( 但是要保留当前字段 )
          const activeCell = this.highlightList[0]
          const activeDatasetId = activeCell.content.dataSetId
          const activeDataset = datasetList.find(dataset => dataset.id === activeDatasetId)

          // const datasetNamesUsed = this.effectiveCellList.filter(cell => {
          //   return cell !== activeCell && activeDatasetId === cell.content.dataSetId
          // }).map(item => item.content.text)

          // activeDataset['children'] = activeDataset['children'].filter(item => !datasetNamesUsed.includes(item.labeName))
          return [activeDataset]
        }
        case 'chart':
        case 'card':
        case 'elementText':
        case 'data':
        case TYPE_ELEMENT.CUSTOMER_ELEMENT: {
          return datasetList
        }
        case 'row': {
          // 筛选行数据集
          return datasetList.filter(item => this.rowDatasetIdList.includes(item.id))
        }
      }
    },
    add(id) {
      this.dataMap[id].condition.push({
        dataSetId: id,
        groupFilterType: 'and',
        columnName: '',
        columnType: '',
        filterType: '',
        /** value: 值; field: 字段. */
        filterValueType: 'value',
        values: '',
      })
    },

    // 聚合过滤 新增
    polymericAdd(key = 'cell') {
      this.polymericList.push({
        id: this.$_generateUUID(),
        groupFilterType: 'and',
        [key]: '',
        filterType: '',
        filterValueType: 'value',
        columnType: '',
        values: '',
      })
    },

    // 聚合过滤 删除
    polymericDelete(item, index) {
      this.$set(this, 'polymericList', this.polymericList.filter(e => e.id !== item.id))
      if (this.polymericList.length === 1) {
        this.polymericList[0].groupFilterType = 'and'
      }
    },

    // 表格聚合过滤初始化
    polymericInit() {
      if (!this.polymeric) return
      // 恢复数据  无法设置的数据不要，保存看板时额外做一次过滤
      if (this.tableDefaultConfig.polymericFilters) {
        const old = this.tableDefaultConfig.polymericFilters || []
        const list = this.$_JSONClone(old)
        const keyNameList = this.polymericCheckOptionList.map(e => e.value)
        const newList = list.filter(e => keyNameList.includes(e.cell))
        newList.forEach(item => {
          const target = this.polymericCellList.find(e => e.content.keyName === item.cell)
          if (target.content.aggType === 'CUSTOM') {
            item.columnType = target.content.customFnData?.expReturnType || 'number'
            if (item.columnType === 'string' || ['is_not_null', 'isnull'].includes(item.filterType)) {
              item.values = String(item.values)
            } else {
              const val = parseFloat(item.values)
              item.values = Number(val === NaN ? 0 : val)
            }
          } else {
            item.columnType = 'number'
          }
        })
        if (newList.length) {
          newList[0].groupFilterType = 'and'
        }
        this.$set(this, 'polymericList', newList)
      }

      if (this.polymericList <= 0) {
        this.polymericAdd()
      }
    },

    // 聚合过滤数据校验， key, filterType, values不能为空
    polymericValid(list, key = 'cell') {
      for (let item of list) {
        if (!item[key] || !item.filterType || (!item.values && item.values !== 0)) {
          return false
        }
      }
      return true
    },

    // 聚合过滤提交
    polymericSubmit() {
      // 筛选配置了的数据，未配置的过滤掉
      const checkList = this.polymericList.filter(e => {
        return e.cell || e.filterType || e.values || e.groupFilterType !== 'and'
      })

      const validFlag = this.polymericValid(checkList)

      if (!validFlag) {
        this.$message.warning(this.$t('sdp.views.plsFillData'))
        return { check: false }
      }

      // 设置数据到table
      const old = this.tableDefaultConfig.polymericFilters || []
      const valid = JSON.stringify(old) !== JSON.stringify(checkList)

      if (valid) {
        this.$set(this.tableDefaultConfig, 'polymericFilters', this.$_JSONClone(checkList))
      }

      return { check: true, valid }
    },
    // 图形聚合过滤数据提交
    polymericChartSubmit() {
      const checkList = this.polymericList.filter(e => {
        return e['metric'] || e.filterType || e.values || e.groupFilterType !== 'and'
      })

      const validFlag = this.polymericValid(checkList, 'metric')

      if (!validFlag) {
        this.$message.warning(this.$t('sdp.views.plsFillData'))
        return { check: false }
      }

      // 设置数据到
      const old = this.drillSettings.polymericFilters || []
      const valid = JSON.stringify(old) !== JSON.stringify(checkList)

      if (valid) {
        this.$set(this.drillSettings, 'polymericFilters', this.$_JSONClone(checkList))
      }

      return { check: true, valid }
    },

    // 获取有效的数据过滤信息
    getFilters() {
      let res = []
      const dataMap = this.dataMap

      for (const key in dataMap) {
        if (dataMap.hasOwnProperty(key)) {
          if (dataMap[key].type === 'condition') {
            const dataList = dataMap[key].condition

            if (dataList.length === 0) continue

            if (dataList.every(data => Object.keys(data).every(k => data[k] || k === 'referToCalendar' || data[k] === 0 || data[k] === false))) { // offset有可能等于undefined data[k] !=='' 不好判断
              res.push(dataList)
              continue
            }
          } else {
            const expression = dataMap[key].expression.trim()
            if (expression !== '') {
              res.push({ dataSetId: key, expression })
              continue
            }
          }

          return false
        }
      }

      return res
    },

    async onAreaSubmit() {
      if (this.isPolymericType) {
        // 校验数据
        // 提交数据
        const { check, valid } = this.polymericChartSubmit()
        // 关闭弹窗
        if (valid) {
          this.$emit('dataFilteringCallback', false)
        }
        if (check) {
          this.closeDialog()
        }
        return
      }

      const validData = this.getFilters()

      if (validData === false) {
        this.$message.warning(this.$t('sdp.views.plsFillData'))
        return
      }

      const dataList = this.$_JSONClone(validData)

      let arr = []
      // const conditionFilterRes = []
      const expressionFilterRes = []

      dataList.forEach(data => {
        if (Array.isArray(data)) {
          data.forEach(item => {
            item.groupFilterType || (item.groupFilterType = 'and')
            item.values = [item.values]
          })

          arr.push(...data)
        } else {
          const { dataSetId, expression } = data
          expressionFilterRes.push(Object.assign({}, data, {
            expression: this.aliasDict?.updateExpressionFieldAlias(expression, dataSetId, true) || expression
          }))
        }
      })

      // 校验表达式过滤语句
      const valid = expressionFilterRes.length ? await this.checkExpFilters(expressionFilterRes) : true

      if (!valid) return

      if (expressionFilterRes.length) {
        arr = expressionFilterRes
        arr.forEach(({ dataSetId, expression }) => this.dataMap[dataSetId].expression = expression)
      }

      let polymericValid = false
      if (this.polymeric) {
        const { check, valid: pValid } = this.polymericSubmit()
        if (!check) return
        polymericValid = pValid
      }


      // 给条件过滤批量 添加  /删除 日期函数标识
      arr.filter(item => this.isConditionFilter(item)) // 条件过滤
        .filter(item => item.columnType === 'date') // 日期字段
        .forEach(item => {
          this.dateFnList.includes(item.values[0]) ? item.functionType = 'date' : delete item.functionType
        })
      switch (this.type) {
        // 单元格过滤
        case 'table': {
          const content = this.highlightList[0].content
          const old = content.filters
          const cur = arr
          const valid = JSON.stringify(old) !== JSON.stringify(cur)
          if (valid) {
            this.$set(content, 'filters', cur)
            this.$emit('dataFilteringCallback')
          }
          break
        }
        case 'chart':
        case 'card':
        case 'elementText':
        case TYPE_ELEMENT.CUSTOMER_ELEMENT:
        {
          const old = this.drillSettings.filters || []
          const cur = arr
          const valid = JSON.stringify(old) !== JSON.stringify(cur)
          if (valid || this.drillSettings.caculationMethodType !== this.caculationMethodType || this.drillSettings.disabledCalendarFnFilter !== this.disabledCalendarFnFilter) {
            this.$set(this.drillSettings, 'filters', cur)
            this.$set(this.drillSettings, 'caculationMethodType', this.caculationMethodType)
            this.$set(this.drillSettings, 'disabledCalendarFnFilter', this.disabledCalendarFnFilter)
            this.$emit('dataFilteringCallback', false)
          }
          break
        }
        // 表格过滤
        case 'data': {
          const old = this.tableDefaultConfig.filters
          const cur = arr
          const valid = JSON.stringify(old) !== JSON.stringify(cur)
          if (valid || this.tableDefaultConfig.caculationMethodType !== this.caculationMethodType || this.tableDefaultConfig.disabledCalendarFnFilter !== this.disabledCalendarFnFilter) {
            this.$set(this.tableDefaultConfig, 'filters', cur)
            this.$set(this.tableDefaultConfig, 'caculationMethodType', this.caculationMethodType)
            this.$set(this.tableDefaultConfig, 'disabledCalendarFnFilter', this.disabledCalendarFnFilter)
            this.$emit('dataFilteringCallback')
          } else if (polymericValid) {
            this.$emit('dataFilteringCallback')
          }
          break
        }
        case 'row': {
          const old = this.tableDefaultConfig.rowFilterMap[this.rowIndex]
          const cur = arr
          const valid = JSON.stringify(old) !== JSON.stringify(cur)

          if (valid) {
            if (cur.length) {
              this.$set(this.tableDefaultConfig.rowFilterMap, this.rowIndex, cur)
            } else {
              this.$delete(this.tableDefaultConfig.rowFilterMap, this.rowIndex)
            }
            this.$emit('dataFilteringCallback')
            // 强制刷新 rounder 组件
            this.eventBus.$emit(WORKSPACE.FORCE_UPDATE_ROUNDER)
          }
          break
        }
      }

      this.closeDialog()
    },

    closeDialog() {
      this.dialogVisible = false
    },

    closeHandler() {
      // 恢复默认值
      this.bookmark = defaultBookmark
    },

    getCalcFieldList(children) {
      return children.map(item => {
        return {
          ...item,
          label: item.labeName,
          value: item.labeName,
          comment: item.comment,
        }
      })
      // return children.map(({ aliasName: label, labeName: value, comment }) => ({ label, value, comment }))
    },

    // 获取文本域节点
    getTextareaNode(datasetId) {
      return this.$refs['expressionRef_' + datasetId][0].$el.children[0]
    },

    updateExpression(value, datasetId, datasetIndex, special) {
      const expression = this.dataMap[datasetId].expression
      const textareaNode = this.getTextareaNode(datasetId)
      const { selectionStart, selectionEnd } = textareaNode

      /// /////////////////////////////////////////////////////
      // 适配 Edge 浏览器 //////////////////////////////////////
      const start = Math.min(selectionStart, selectionEnd) // /
      const end = Math.max(selectionStart, selectionEnd) /// //
      /// ///////////////////////////////////////////////////

      const valueLength = value.length
      const lText = expression.slice(0, start)
      const rText = expression.slice(end)

      let currentIndex = start + valueLength
      let insertText = value

      if (special && special === 'field') {
        const val = this.aliasDict?.getFieldName(datasetId, value) || value
        const item = this.datasetLists[datasetIndex].children.find(item => item.labeName === val)
        const isDate = item.columnTpe === 'date'

        insertText = this.aliasDict?.getDictAlias(item) || value
        currentIndex = start + insertText.length

        if (isDate) {
          insertText = `{${insertText}}`
          currentIndex += 1
        }
      } else if (special && special === 'operation') {
        if (value.includes('string')) {
          insertText = value.replace('string', '')
          currentIndex -= /%'$/.test(value) ? `string%'`.length : `string'`.length
        }
      } else if (special && special === 'date') {
        if (value === 'dateSel') {
          insertText = ''
          currentIndex -= 'dateSel'.length
        }
      } else if (special && special === 'date-picker') {
        insertText = `'${value}'`
        currentIndex += 2
      }

      this.dataMap[datasetId].expression = lText + insertText + rText
      this.$nextTick(() => this.setCaretPosition(textareaNode, currentIndex))
    },

    // 设置光标位置
    setCaretPosition(node, index) {
      if (node.setSelectionRange) {
        node.focus()
        node.setSelectionRange(index, index)
      } else if (node.createTextRange) {
        let range = node.createTextRange()
        range.collapse(true)
        range.moveEnd('character', index)
        range.moveStart('character', index)
        range.select()
      }
    },

    // 表达式非空校验
    checkExpression(item) {
      /\S/g.test(item.expression) || (item.expression = '')
    },

    // 校验表达式过滤语句
    async checkExpFilters(params) {
      this.loading = true

      const { api, tenantId } = this.utils

      const valid = await api.post(`/report/engine/checkExpFilters/${tenantId}`, params)
        .then(data => {
          return !!data
        })
        .catch(e => {
          console.log('校验表达式过滤语句:', e)
          return false
        })

      console.log('校验表达式过滤语句:', valid)

      this.loading = false

      return valid
    },

    // 是否为条件过滤
    isConditionFilter(item) {
      // 就目前的业务场景而言
      // 只要不含 expression 字段, 就是条件过滤
      return !item.expression
    }
  },
}
</script>

<style lang="scss" scoped>
  @import "../../theme/index";

  .custom-collapse-content{
    /deep/ .el-collapse-item{
      border: 1px solid var(--sdp-cszj-bkfgx);
      padding:0 16px;
      border-radius: 4px;
      margin-bottom: 16px;
      .el-collapse-item__header{
        border: none;
        font-weight: 600;
      }
      .el-collapse-item__wrap{
        border: none;
      }
    }
  }

  .icon-sdp-info {
    color: var(--sdp-zs);
  }

  /deep/ .el-tabs__header {
    margin-bottom: 8px !important;
  }
  .isHideTabs {
    /deep/ .el-tabs__header {
      visibility: hidden;
      height: 0px !important;
      margin: 0px !important;
    }
  }
  .tab-label {
    display: flex;
    align-items: center;
    margin-left: 4px;

    .icon-sdp-info {
      margin-left: 4px;
    }
  }


  // 模拟的日期选择器
  .mock-data-picker {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    right: 0;
  }

  // 真实的日期选择器
  .real-data-picker {
    opacity: 0;
    width: 100% !important;
    & /deep/ .el-input__inner {
      cursor: pointer;
    }
  }

  // 折叠面板
  .el-collapse {
    border-top: 0;
    border-bottom-width: 0;
    /deep/ {
      .el-collapse-item__header {
        font-size: 12px;
        color: #222222;
        height: 44px;
        line-height: 44px;
      }

      .el-collapse-item__content {
        padding-bottom: 16px;
      }
    }
  }

  .tools-bar {
    height: 18px;
    margin-bottom: 12px;

    .radio-label {
      font-size: 12px;
      color: var(--sdp-xxbt2);
      margin-right: 24px;
    }
  }
  .date-calculation-box{
    height: 36px;
    padding-top:20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .date-calculation-radio-label{
      margin-right: 24px;
      font-size: 12px;
      white-space: nowrap;
    }
    .date-calculation-prompt{
      font-size: 12px;
    }

    &.is-en {
      .date-calculation-radio-label {
        width: 199px;
      }
    }
  }

  .no-select {
    @include no-select;
  }

  /deep/ {
    .el-dialog__body {
      height: 450px;
      overflow: auto;
    }

    .el-input__inner {
      padding-left: 12px !important;
      text-align: left !important;
    }
    .el-date-editor .el-input__inner {
      padding-left: 30px !important;
    }
  }

  .dialog-title {
    @include dialog-title-has-icon;
  }

  .add-button {
    font-size: 12px;
    margin-right: 0;

    i {
      font-size: 12px;
    }
  }

  .el-divider {
    margin: 16px 0;
    border-color: #DDDDDD;
    width: 930px;
  }

  .el-form.expression-form {
    /deep/ {
      .el-form-item__label {
        padding-bottom: 8px !important;
        font-size: 12px;
        color: #333333;
        line-height: 1;
      }

      .el-form-item__content {
        line-height: 1;
      }
    }

    .el-form-item {
      margin: 0;
    }
  }
</style>
<style lang="scss" scoped>
.el-collapse{
  /deep/{
    .el-collapse-item__header{
      background-color: transparent;
      color: var(--sdp-fx-mrwzs);
    }
    .el-collapse-item__wrap{
      background-color: transparent;
      border-bottom-color: var(--sdp-cszj-bkfgx);
      .el-collapse-item__content{
        background-color: transparent;
      }
    }
  }
}
</style>
