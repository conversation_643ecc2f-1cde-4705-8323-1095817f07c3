<template>
  <div class="calc-option-container">
    <el-scrollbar v-sdp-el-scrollbar>
      <ul class="wrapper">
        <li
          style="position: relative;"
          v-for="item in list"
          :key="item.value"
          @click="clickHandler(isDataSet ? labelFormatter(item, item.value) : item.value)"
        >
          <slot :item="item">
            <el-tooltip
              effect="dark"
              placement="right"
              :disabled="!item.tooltips"
            >
              <div slot="content">
                <p
                  v-for="(tip, i) in $t(item.tooltips).split(' \n ')"
                  :key="i"
                  :class="{ tipTitle: !i, tipContent: i }"
                >{{ tip }}</p>
              </div>

              <div class="calc-option-content">
                <div
                  :title="getTitle(item)"
                >
                  {{getLabel(item)}}
                </div>

                <div class="calc-option-content" :style="{ width: item.level && item.detail ? '30%' : ''}">
                  <div class="content-detail">
                    <template v-if="isShowLevel && item.level">{{getLevel(item)}}</template>
                  </div>

                  <div
                    class="content-detail"
                    :class="{ 'content-detail-hover': item.detailHover }"
                    v-if="item.detail"
                    @click.stop="detailClick(item.value, item)"
                  >
                    {{item.detail}}
                  </div>
                </div>
              </div>
            </el-tooltip>
          </slot>
        </li>
      </ul>
    </el-scrollbar>
    <GlobalParamDetailDialog
      v-if="isGlobalParams"
      :visible.sync="globalParamDetailVisible"
      :id="globalParamDetailId"
    />
  </div>
</template>

<script>
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin'
import SdpElScrollbar from '../../../../directives/v-el-scrollbar'
import { substring15 } from '../../../board/displayPanel/utils'
import GlobalParamDetailDialog from 'packages/base/grid/components/attributes-panel/globalParam/globalParamDetailDialog'
export default {
  name: 'calcOptions',
  inject: ['aliasDict', 'utils'],

  directives: { SdpElScrollbar },
  components: { GlobalParamDetailDialog },
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    },
    isDataSet: {
      type: Boolean,
      default: false
    },
    isGlobalParams: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      globalParamDetailVisible: false,
      globalParamDetailId: '',
    }
  },

  computed: {
    // 与产品(章文鹤)沟通后确认SBI不需要展示全局参数的级别信息
    isShowLevel() {
      return this.utils.env?.projectName !== ALL_PROJECT_NAME.SBI
    }
  },

  methods: {
    substring15,
    clickHandler(value) {
      this.$emit('onClick', value)
    },
    detailClick(value, item) {
      this.$emit('detailClick', value, item)
      if (this.isGlobalParams) {
        this.globalParamDetailVisible = true
        this.globalParamDetailId = item.code
      }
    },
    labelFormatter(item, defaultValue) {
      return this.aliasDict?.getDictAlias(item) || defaultValue
    },
    getTitle(item) {
      // if (this.isDataSet) {
      //   return item.comment ? `${this.labelFormatter(item, item.label)}  (${item.comment})` : this.labelFormatter(item, item.label)
      // }
      // return item.comment ? `${item.label}  (${item.comment})` : item.label
      if (this.isDataSet) {
        return  this.labelFormatter(item, item.label)
      }
      return  item.label
    },
    getLabel(item) {
      // if (this.isDataSet) {
      //   return item.comment ? `${this.labelFormatter(item, item.label)}   (${substring15(item.comment)})` : this.labelFormatter(item, item.label)
      // }
      // return item.comment ? `${item.label}   (${substring15(item.comment)})` : item.label
      if (this.isDataSet) {
        return this.labelFormatter(item, item.label)
      }
      return  item.label
    },
    getLevel(item) {
      if (!item?.level) return ''
      return item?.level === '1' ? this.$t('sdp.views.platformLevel') : this.$t('sdp.views.tenantLevel')
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "../../theme/index";

  .tipTitle {
    font-size: 14px;
    line-height: 20px;
  }
  .tipContent {
    font-size: 12px;
    line-height: 22px;
  }

  .el-scrollbar {
    height: 100%;
    border: 1px solid var(--sdp-srk-bks);
    border-radius: 2px;
  }

  .wrapper {
    padding: 10px 0;
    line-height: 1.5;
    height: 140px;

    & > li {
      cursor: pointer;
      font-size: 12px;
      color: var(--sdp-xxbt2);
      line-height: 1.8em;
      padding: 0 10px;
    }
  }
  .calc-option-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .content-detail-hover {
      display: none;
    }
    &:hover {
      .content-detail-hover {
        display: block;
      }
    }
  }
.calc-option-container{
  .wrapper{
    & > li {
      &:hover{
        background-color: var(--sdp-ycsz-hgls);
        color: var(--sdp-zs);
      }
    }
  }
}
</style>
