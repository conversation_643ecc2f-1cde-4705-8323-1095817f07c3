<template>
  <div class="sdp-grid-design-attribute-panel-container">
    <div class="sdp-grid-design-attribute-panel">
      <div class="panel-header">
        <el-radio-group v-model="activeName">
          <el-radio-button label="first">{{ $t('sdp.views.unitAtrr') }}</el-radio-button>
          <el-radio-button label="second">{{ $t('sdp.views.tableAttribute') }}</el-radio-button>
        </el-radio-group>
      </div>

      <el-tabs v-model="activeName" class="panel-content">
        <!-- 单元格属性 -->
        <el-tab-pane name="first" data-cy-cell-attribute>
          <el-scrollbar v-sdp-el-scrollbar>
            <el-form label-position="top" size="mini" class="panel-form">
              <!-- 单元格 -->
              <el-row
                class="row--cell-size"
                :gutter="8"
              >
                <el-col :span="24">
                  <el-form-item :label="$t('sdp.views.cell')">
                    <el-input :value="highlightCellName" disabled class="attr-cell-name"></el-input>
                  </el-form-item>
                </el-col>

                <!-- 宽 -->
                <el-col :span="12" tag="el-form-item">
                  <el-input-number
                    v-model="highlightCellWidth"
                    :class="{ 'is-sel-zero-cells': isSelZeroCell }"
                    :disabled="noNativeCell || isMergeCellOfSelectStart"
                    size="mini"
                    controls-position="right"
                    :min="minWidth"
                    :max="1000"
                    :step="1"
                    @change="changeLayoutOptionsWidth"
                    @keyup.native.enter="changeLayoutOptionsWidth(highlightCellWidth)"
                    data-cy-cell-width
                  ></el-input-number>
                </el-col>

                <!-- 高 -->
                <el-col :span="12" tag="el-form-item">
                  <el-input-number
                    v-model="highlightCellHeight"
                    :class="{ 'is-sel-zero-cells': isSelZeroCell }"
                    :disabled="noNativeCell || isMergeCellOfSelectStart"
                    size="mini"
                    controls-position="right"
                    :min="minHeight"
                    :max="1000"
                    :step="1"
                    @change="changeLayoutOptionsHeigth"
                    @keyup.native.enter="changeLayoutOptionsHeigth(highlightCellHeight)"
                    data-cy-cell-height
                  ></el-input-number>
                </el-col>
              </el-row>

              <!-- 行高 -->
              <hr/>
              <el-form-item class="sdp-auto-hide-input-number sdp-cell-lineHeight" :label="$t('sdp.views.lineSpace')">
                <el-input-number
                  v-model="highlightCellLineHeight"
                  :disabled="isSelZeroCell || isImageCell"
                  controls-position="right"
                  style="width: 100%;"
                  :min="1"
                  :max="144"
                  :step="1"
                  :precision="0"
                  @change="changeCellLineHeight"
                  @keyup.native.enter="changeCellLineHeight(highlightCellLineHeight)"
                  data-cy-cell-height
                />
              </el-form-item>

              <hr v-if="isImageCell"/>
              <PictrueSet :currentImageCell="imageCell" v-if="isImageCell"/>
              <hr/>
              <!-- 扩展 -->
              <el-form-item :label="$t('sdp.views.extend')">
                <el-select
                  :popper-class="getCurrentThemeClass()"
                  v-model="selectedExtendType"
                  :placeholder="extendDisabled ? '' : undefined"
                  :disabled="extendDisabled || exclude || isExpandYear"
                  @change="changeExtendType"
                >
                  <el-option
                    v-for=" ({ value, label }) in extendTypeList"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <!-- 扩展合并 -->
              <el-form-item
                v-show="extendMergeShow"
                :label="$t('sdp.views.extensionMerge')"
                class="form-switch-item"
                style="margin-top: 14px;"
              >
                <el-switch v-model="extendMerge" @change="recordCellOperation('extendMerge')"></el-switch>
              </el-form-item>

              <!-- 列数限制 基于排名返回数据-->
              <cellColumnLimit
                :highlightCell="highlightCell"
                :highlightList="highlightList"
                :isExpandYear="isExpandYear"
                :effectiveCellList="effectiveCellList"
              />
              <hr>

              <!-- 计算公式 -->
              <el-form-item :label="$t('sdp.views.calculate')">
                <!-- 按钮组 -->
                <el-row class="row--calc-formula">
                  <el-radio-group v-model="selectedCalc">
                    <el-col
                      :span="12"
                      v-for="(calc, index) in calcTypeList"
                      :key="calc.value"
                    >
                      <el-radio
                        ref="radioRef"
                        :disabled="!!textValueDisable || !checkRadioStatus(calc) || exclude || isImageCell"
                        :label="calc.label"
                        @click.native="!$refs.radioRef[index].disabled && calcClick(calc.label, $event)"
                        @change="calcChange(calc.value)"
                      ></el-radio>
                    </el-col>
                  </el-radio-group>
                </el-row>

                <!-- 自定义公式输入框 -->
                <el-input
                  ref="customInputRef"
                  v-model="expression"
                  :title="expression"
                  :disabled="!isCustomExpression || ranking || isSelManyCells || exclude || isImageCell"
                  style="width: 150px;"
                  class="ellipsis-input"
                  :class="{ 'readonly-input': isAddCustomFn}"
                  :clearable="!(!isCustomExpression || ranking || isSelManyCells || exclude)"
                  @focus="cancelFormatBrush(customInputRefFocus)"
                  @keydown.native.enter="customExpression(SUB_AGG_TYPE.EXPRESSION)"
                  @clear="customInputClearHandler"
                >
                </el-input>
                <el-button type="text" @click="cancelFormatBrush(customInputClickHandler)" :disabled="!isCustomExpression || ranking || isSelManyCells || exclude">
                  <i class="icon-sdp-hanshu icon-fx"></i>
                </el-button>
              </el-form-item>

              <hr>
              <!-- 标记 -->
              <el-form-item :label="$t('sdp.views.b_flag')" class="form-switch-item">
                <MarkInfo  :highlight-list="highlightList" :dataset-list="datasetList" :gridTable="gridTable"/>
              </el-form-item>
              <hr>
              <!-- 预警 -->
              <el-form-item :label="$t('sdp.views.warning')" class="form-item--cell-warning">
                <CellWarning
                  :highlight-list="highlightList"
                  :dataset-list="datasetList"
                  :dimensionCellList="dimensionCellList"
                  :elId="elId"
                />
              </el-form-item>
              <el-form-item class="form-item--cell-warning-roc-todo">
                <CellWarningNameList :highlight-list="highlightList"/>
              </el-form-item>

              <hr>

              <!-- 文本域 -->
              <TextArea
                ref="textArea"
                :highlightList="highlightList"
                :datasetList="datasetList"
                :isImageCell="isImageCell"
                :isStack="isStack"
                :dimensionCellList="dimensionCellList"
                :compareTypeList="compareTypeList"/>

              <!-- 计算域 -->
              <time-area
                ref="timeArea"
                :highlightList="highlightList"
                :compareTypeList="compareTypeList"/>

              <!-- 层级占比 -->
              <hierarchy-proportion
                :highlightList="highlightList"
                :effectiveCellList="effectiveCellList"
              >
              </hierarchy-proportion>

              <hr v-if="!isMobile">
              <!-- 去除 -->
              <el-form-item  class="form-switch-item" v-if="!isMobile">
                <div slot="label" style="display: flex;align-items: center">
                  <span>{{$t('sdp.views.exclude')}}</span>
                  <el-tooltip  effect="dark" :content="$t('sdp.message.exclude_msg')" placement="top" >
                    <i class="el-tooltip icon-sdp-info" ></i>
                  </el-tooltip>
                </div>

                <el-switch
                  v-model="exclude"
                  :disabled="excludeDisabled"
                  @change="excludeChange"
                ></el-switch>
              </el-form-item>

              <hr>

              <!-- 排名 -->
              <el-form-item :label="$t('sdp.views.Ranking')" class="form-switch-item">
                <el-switch
                  v-model="ranking"
                  :disabled="rankingDisabled"
                  @change="rankingChange"
                ></el-switch>
              </el-form-item>

              <!-- 排名列 -->
              <el-form-item v-if="ranking" class="form-item--ranking-cell" :label="$t('sdp.views.RankingList')">
                <el-select :popper-class="getCurrentThemeClass()" v-model="rankingCell" @change="rankingCellUpdate">
                  <el-option
                    v-for="sortType in rankingCells"
                    :key="sortType.label"
                    :label="sortType.label"
                    :value="sortType.label"
                  ></el-option>
                </el-select>
              </el-form-item>

              <!-- 方式 -->
              <el-form-item v-if="ranking" class="form-item--ranking-way" :label="$t('sdp.views.Way')">
                <el-select :popper-class="getCurrentThemeClass()" v-model="rankingWay" @change="rankingWayChange">
                  <el-option
                    v-for="sortType in rankingFun"
                    :key="sortType.value"
                    :label="sortType.label"
                    :value="sortType.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <!-- 并列排名-->
              <el-form-item v-if="ranking" class="form-item--ranking-way">
                <el-checkbox v-model="highlightCell.content.parallelRanking" @change="recordCellOperation('parallelRanking')">{{ $t('sdp.views.tied_for_ranking') }}
                </el-checkbox>
              </el-form-item>
              <!-- 空值或0不参与排名-->
              <!-- <el-form-item v-if="ranking">
                 <el-checkbox v-model="nullChecked" @change="nullCheckedChange">{{ $t('sdp.views.null_not_rank') }}
                 </el-checkbox>
               </el-form-item>-->

              <hr>

              <el-form-item :label="$t('sdp.views.stackingAccumulation')" class="form-switch-item">
                <StackAccumulationSwitch :highlightList="highlightList" :isImageCell="isImageCell"/>
              </el-form-item>

              <StackAccumulationSelect :highlightList="highlightList"/>

              <template v-if="columnTotalCellVisible">
                <hr>
                <cellColumnTotal
                  :highlightList="highlightList"
                  :columnTotalCellList="columnTotalCellList"
                  :effectiveCellList="effectiveCellList"
                  :columnTotalMultipleCellList="columnTotalMultipleCellList"
                />
              </template>

              <template v-if="rowTotalCellVisible">
                <hr>
                <cellRowTotal
                  :highlightList="highlightList"
                  :rowTotalCellList="rowTotalCellList"
                  :rowTotalMultipleCellList="rowTotalMultipleCellList"
                  :effectiveCellList="effectiveCellList"
                />
              </template>
              <!-- 日期维度切换-->
              <dateDimensionSwitch
                ref="dateDimensionSwitch"
                :datasetList="datasetList"
                :highlightList="highlightList"
                :effectiveCellList="effectiveCellList"
                :tableDefaultConfig="tableDefaultConfig"
              />

              <hr>

              <!-- 行格数据筛选 -->
              <el-form-item :label="$t('sdp.views.rowDataFilter')" class="form-item--row-data-filter">
                <i @click="cancelFormatBrush(rowDataFiltering)" class="icon-sdp-shujukeshaixuanx" data-cy-row-data-filter :class="{ 'data-filter-disabled': rowCellDataFilteringDisabled }"></i>
              </el-form-item>

              <hr>

              <!-- 单元格数据筛选 -->
              <el-form-item :label="$t('sdp.views.cellDataFilter')" class="form-item--cell-data-filter">
                <i @click="cancelFormatBrush(dataFiltering)" :class="[filterIcon, this.cellDataFilteringDisabled ? 'data-filter-disabled' : '']" :style="{ cursor: isStack || ranking || exclude? 'not-allowed' : 'pointer' }"></i>
              </el-form-item>

              <!-- 自适应行高/列宽 -->
              <height-width-adaptive
                :highlightList="highlightList"
                :effectiveCellList="effectiveCellList"
                :tableDefaultConfig="tableDefaultConfig"
              />

              <hr>

              <!-- 格式 -->
              <el-form-item :label="$t('sdp.views.format')">
                <!-- 格式设置 -->
                <el-select
                  :popper-class="getCurrentThemeClass()"
                  v-model="selectedFormat"
                  :disabled="!highlightListValid || ranking || stringType || exclude"
                  @change="changeFormatType($event, true)">
                  <el-option
                    v-for=" ({ value, label }) in formatTypeList"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></el-option>
                </el-select>

                <!-- 小数位设置 -->
                <el-select
                  :popper-class="getCurrentThemeClass()"
                  v-if="!formatIsConvention"
                  v-model="selectedAccuracy"
                  style="margin-top: 8px;"
                  ref="precisionSelectRef"
                  :disabled="ranking || stringType || (isCurrencyField && formatIsPercentage) || exclude"
                  @change="val => changeAccuracyType(val, true)"
                >
                  <el-option
                    v-for=" ({ label, value }) in accuracyTypeList"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <MinUnitValue v-if="formatIsCurrencyUnit" :highlightList="highlightList" />
              <zeroFormatSwitch ref="zeroFormat" :highlightList="highlightList" />
            </el-form>
          </el-scrollbar>
        </el-tab-pane>
        <!-- 表格属性 -->
        <el-tab-pane name="second" data-cy-grid-attribute>
          <el-scrollbar v-sdp-el-scrollbar>
            <el-form label-position="top" size="mini" style="padding-top: 6px;">
              <!-- 表格分页 -->
              <el-form-item :label="$t('sdp.views.tablePage')" class="form-switch-item">
                <el-switch
                  v-model="tableDefaultConfig.isTablePage"
                  :disabled="
                    !!hasAdaptive ||
                  (hasDataBar && !isRowsLimit) ||
                  tableDefaultConfig.serialNumber.length > 0 ||
                  isOptionAnimation
                  "
                  @change="recordCellOperation('isTablePage')"></el-switch>
              </el-form-item>

              <!-- 表格空值省略 -->
              <!--空列-->
              <el-form-item :label="$t('sdp.views.HideEmptyColumns')" class="form-switch-item" style="margin-top: 14px;">
                <i class="cell-warning-icon el-icon-s-tools" @click="handleOpenRowColumnHideDialog('column')"></i>
                <!--<el-switch-->
                <!--  v-model="tableDefaultConfig.isHideEmptyColumns"-->
                <!--  @change="recordCellOperation('HideEmptyColumns')"></el-switch>-->
              </el-form-item>
              <!--空行-->
              <el-form-item :label="$t('sdp.views.HideEmptyRows')" class="form-switch-item" style="margin-top: 14px;">
                <i class="cell-warning-icon el-icon-s-tools" @click="handleOpenRowColumnHideDialog('row')"></i>
              </el-form-item>
              <!-- 单元格输入文字匹配数据集开关 -->
              <el-form-item :label="$t('sdp.views.b_fieldFuzzyMatching')" class="form-switch-item" style="margin-top: 14px;">
                <FieldFuzzySwitch :tableDefaultConfig="tableDefaultConfig" />
              </el-form-item>
              <el-form-item class="form-switch-item" style="margin-top: 14px;">
                <div slot="label" style="display: flex;align-items: center">
                  <span>{{$t('sdp.views.b_widthAdaptation')}}</span>
                  <el-tooltip effect="dark" :content="$t('sdp.message.b_widthAdaptation_tips')" placement="top" >
                    <i class="icon-sdp-info" ></i>
                  </el-tooltip>
                </div>
                <AutoCalcColWidthSwitch :tableDefaultConfig="tableDefaultConfig" :gridTable="gridTable" />
              </el-form-item>
              <!--  动画  -->
              <set-animation
                v-if="isScreen && !isDataReport && tableDefaultConfig.animationOption"
                :styles="{
                  marginTop: '14px'
                }"
                :isByTheInteraction="isByTheInteraction"
                :config="tableDefaultConfig.animationOption"
                :disabled="isDisabledAnimation"
              />
              <!--自适应最小宽度-->
              <el-form-item
                v-if="tableDefaultConfig.autoCalcColWidth"
                :label="$t('sdp.views.b_MinCellWidth')"
                class="form-switch-item"
                style="margin-top: 14px;"
              >
                <div class="min-auto-width">
                  <el-input-number
                    v-model="tableDefaultConfig.minWidth"
                    ref="minWidthInput"
                    size="mini"
                    controls-position="right"
                    :min="20"
                    :max="300"
                    :step="5"
                    @change="minWidthChange"
                    step-strictly
                    data-cy-cell-width
                  ></el-input-number>
                </div>
              </el-form-item>

              <!-- 固定列问题 -->
              <!-- <p>{{$t('sdp.views.ExtraWideDisplay')}}</p>
              <sdp-eng-select v-model="tableDefaultConfig.outOfLine">
                <el-option
                  v-for="item in outOfLineArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </sdp-eng-select> -->
              <hr>
              <!-- 冻结列 -->
              <el-row
                class="row--line-limit-setting"
                :gutter="8"
              >
                <el-col :span="24">
                  <el-form-item :label="$t('sdp.views.b_freezeColumns')"></el-form-item>
                </el-col>
                <FreezeColumns :tableDefaultConfig="tableDefaultConfig" :columWidth="layoutOptions.widths"/>
              </el-row>
              <!-- 冻结行 -->
              <el-row
                class="row--line-limit-setting"
                :gutter="8"
              >
                <el-col :span="24">
                  <el-form-item :label="$t('sdp.views.b_freezeRows')"></el-form-item>
                </el-col>
                <FreezeRows :freezeRow.sync="tableDefaultConfig.freezeRow" :rowsLeng="layoutOptions.heights"/>
              </el-row>

              <!-- 行数限制 -->
              <el-row
                class="row--line-limit-setting"
                :gutter="8"
              >
                <el-col :span="24">
                  <el-form-item :label="$t('sdp.views.LineNumberLimit')"></el-form-item>
                </el-col>

                <el-col :span="12" tag="el-form-item">
                  <el-select :popper-class="getCurrentThemeClass()" v-model="tableDefaultConfig.lineNumber" @change="cancelFormatBrush" :disabled="
                    (hasDataBar && !tableDefaultConfig.isTablePage) || isOptionAnimation
                  ">
                    <el-option value="1" :label="$t('sdp.views.none')">{{$t('sdp.views.none')}}</el-option>
                    <el-option value="2" :label="$t('sdp.views.ahead')">{{$t('sdp.views.ahead')}}</el-option>
                  </el-select>
                </el-col>

                <el-col :span="12" tag="el-form-item">
                  <el-input-number
                    v-model="tableDefaultConfig.maxCountNum"
                    :class="{ isZh: langCode === 'zh' }"
                    :disabled="(hasDataBar && !tableDefaultConfig.isTablePage) || tableDefaultConfig.lineNumber === '1'"
                    style="width: 100%"
                    size="mini"
                    :min="1"
                    :max="10000"
                    :step-strictly="true"
                    controls-position="right"
                    @focus="cancelFormatBrush"
                  ></el-input-number>
                </el-col>
              </el-row>

              <!-- 基于排名返回数据-->
              <tableColumnLimit
                :tableDefaultConfig="tableDefaultConfig"
                :effectiveCellList="effectiveCellList"
              />

              <hierarchyLimit
                :tableDefaultConfig="tableDefaultConfig"
                @openEdit="handleHierarchyLimitEdit"
              />

              <hr>

              <!-- 汇总行 -->
              <TotalValue @changTotal="changeTotal" :tableDefaultConfig="tableDefaultConfig" @getTotalArr="getTotalArr"/>
              <!-- 隐藏行 -->
              <el-form-item class="select-tree-wrapper" :label="$t('sdp.views.hiddenRow')" style="margin-top: 12px;">
                <div class="select-tree">
                  <span :class="[hideRowsNum ? 'like-placeholder':'like-placeholders']">{{hideRowsText}}</span>
                  <i :class="['el-icon-arrow-down','like-input',isShowSelectHide?'is-reverse':'']"></i>
                </div>

                <el-select
                  :popper-class="getCurrentThemeClass()"
                  v-model="tableDefaultConfig.hideRows"
                  multiple
                  collapse-tags
                  style="opacity: 0;"
                  @focus="getHide"
                  @change="changeHide"
                  @visible-change="showSelectHide"
                  :placeholder="$t('sdp.placeholder.pleaseChoose')"
                >
                  <el-option
                    v-for="item in hideArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <!-- 序列号 -->
              <el-form-item
                class="form-switch-item"
                style="margin-top: 12px">
                <div slot="label" style="display: flex;align-items: center">
                  <span>{{$t('sdp.views.seria_num')}}</span>
                  <el-tooltip  effect="dark" :content="$t('sdp.message.serialNumberInfo')" placement="top" >
                    <i class="icon-sdp-info" ></i>
                  </el-tooltip>

                </div>

                <i @click="cancelFormatBrush(serialNumberDialog)" class="icon-sdp-zhibiaoxuanzeqibianji cell-warning-icon" ></i>
              </el-form-item>

              <hr>

              <!-- 层级分类汇总 -->
              <el-form-item class="select-tree-wrapper" :label="$t('sdp.views.treeTableGroup')">
                <div class="select-tree" :class="{ 'select-tree-disabled': isDisableTreeTableGroup }">
                  <span class="like-placeholder">{{treeGroupCoordinateNum}}</span>
                  <i :class="['el-icon-arrow-down','like-input',isShowSelectTotal?'is-reverse':'']"></i>
                </div>

                <el-select
                  :popper-class="getCurrentThemeClass()"
                  v-model="tableDefaultConfig.treeGroupCoordinate"
                  :disabled="isDisableTreeTableGroup"
                  multiple
                  collapse-tags
                  style="opacity: 0;"
                  @visible-change="showSelectTotal"
                  :placeholder="$t('sdp.views.none')"
                  @remove-tag="clearTreeGroupCoordinate"
                >
                  <el-option
                    v-for="coordinate in treeTableGroupList"
                    :value="coordinate"
                    :key="coordinate"
                    :label="coordinate"
                    @click.native="clickTreeGroupCoordinate(coordinate)"
                  />
                </el-select>
              </el-form-item>

              <!-- 层级展开 移动端默认全部展开 -->
              <el-form-item :label="$t('sdp.views.hierarchicalExpand')" style="margin-top: 12px;">
                <el-select
                  :popper-class="getCurrentThemeClass()"
                  :disabled="isMobile"
                  :value="isMobile ? 'all' : tableDefaultConfig.hierarchy"
                  @change="changeHierarchy"
                >
                  <el-option
                    v-for=" ({label, value}) in hierarchyOptList"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <hr>

              <!-- 层级样式 -->
              <el-form-item :label="$t('sdp.views.hierarchicalStyle')" class="form-item--hierarchical-style">
                <HierarchyStyleList
                  :isMobile="isMobile"
                  :formList.sync="tableDefaultConfig.hierarchyStyleList"
                  :hierarchyStyleWithLeaf.sync="tableDefaultConfig.hierarchyStyleWithLeaf"
                  :hierarchyVerticalBorder.sync="tableDefaultConfig.hierarchyVerticalBorder"
                />
              </el-form-item>

              <hr>
              <!-- 超链接样式 -->
              <HyperLinkSty :tableDefaultConfig="tableDefaultConfig" :isMobile="isMobile" :themeData="themeData"/>

              <hr>
              <!-- 多语言 -->
              <el-form-item :label="$t('sdp.views.multilingual')" class="form-item--multilingual">
                <el-button type="text" @click="cancelFormatBrush(checkMoreLanguage)">
                  {{ $t('sdp.views.Setup') }}
                  <i class="el-icon-arrow-right"></i>
                </el-button>
              </el-form-item>

              <hr>

              <!-- 表格斑马线 -->
              <el-form-item :label="$t('sdp.views.b_gridZebraCrossing')" class="form-switch-item" :style="{ 'margin-bottom': tableDefaultConfig.isZebra ? '0' : '8px' }">
                <GridZebraCrossingSwitch :tableDefaultConfig="tableDefaultConfig" />
              </el-form-item>
              <GridZebraCrossingSet v-if="tableDefaultConfig.isZebra" :tableDefaultConfig="tableDefaultConfig"/>

              <hr v-if="!utils.isScreen || utils.isDataReport">
              <!-- 提示 -->
              <el-form-item v-if="!utils.isScreen || utils.isDataReport" :label="$t('sdp.views.Hint')" class="form-switch-item" style="margin-top: 12px; height: 24px; padding-bottom: 12px;">
                <i class="icon-sdp-zhibiaoxuanzeqibianji cell-warning-icon" @click="() => { tipSetDialogVisible = true }"></i>
              </el-form-item>
            </el-form>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </div>

    <tip-set-dialog
      :visible.sync="tipSetDialogVisible"
      :datasetList="isTemplate ? datasetList : boardDatasetList"
      :element="elementConfig"
      @close="() => { tipSetDialogVisible = false } "
    />

    <row-column-hide
      :visible.sync="rowColumnHideDialogVisible"
      :isColumn="rowColumnType === 'column'"
      :cellList="variableList"
      :tableDefaultConfig="tableDefaultConfig"
      @close="() => { rowColumnHideDialogVisible = false } "
    />

    <function-dialog
      :class="`${getCurrentThemeClass()}`"
      :visible.sync="functionDialogShow"
    />

    <add-plateau-computing-dialog
      :class="`${getCurrentThemeClass()}`"
      :visible.sync="addplateaucomputingDialogShow"
      :datasetList="datasetList"
      :highlightList="highlightList"
      :totalValue="tableDefaultConfig.totalValue"
      @submit="onAreaSubmit"
    />

    <!-- 单元格数据筛选 -->
    <add-data-filtering-dialog
      :class="`${getCurrentThemeClass()}`"
      type="table"
      :effectiveCellList="effectiveCellList"
      :visible.sync="dataFilteringShow"
      :datasetList="datasetList"
      :highlightList="highlightList"
      @dataFilteringCallback="record('cellFilter')"
    />

    <!-- 行数据筛选 -->
    <add-data-filtering-dialog
      :class="`${getCurrentThemeClass()}`"
      type="row"
      :rowIndex="theRowIndex"
      :rowDatasetIdList="rowDatasetIdList"
      :tableDefaultConfig="tableDefaultConfig"
      :visible.sync="rowFilteringShow"
      :datasetList="datasetList"
      :highlightList="highlightListWithMerge"
      @dataFilteringCallback="record('rowFilter')"
      data-cy-row-data-filter-dialog
    />

    <!-- 层级行数限制 -->
    <hierarchy-limit-dialog
      :visible.sync="hierarchyLimitVisible"
      :tableDefaultConfig="tableDefaultConfig"
      :variableList="variableList"
    />

    <CustomFormula
      :class="`${getCurrentThemeClass()}`"
      :visible.sync="addCustomDialogVisible"
      :datasetList="datasetList"
      :highlightList="highlightList"
      isShowFormulaName
      isShowReturnType
      @submit="addCustomDialogSubmitHandler"
    />
    <serial-number-dialog
      :class="`${getCurrentThemeClass()}`"
      :visible.sync="serialNumberDialogVisible"
      :highlight-list="layoutOptions.heights"
      :widths-list="layoutOptions.widths"
      :serial-num-list="tableDefaultConfig.serialNumber"
      @saveSerialNum="serialNumSave"
    />
  </div>
</template>

<script>
import { generateKeyName } from 'packages/base/gridPreview/common/js/utils'
import { CELL_TYPE } from 'packages/base/gridPreview/common/constant'

import { CALC_FNS, GRID_OPTIONS, HIERARCHY_EXPAND, RANKING, SUB_AGG_TYPE } from '../../helpers/constants'
import {
  calcCellWH,
  generateCoordinateForCell,
  isNoDataCell,
  spreadsheetColumnLabel
} from '../../helpers/utils/gridTable'
import { recordMixin } from '../../helpers/mixins'
import {
  getMergeCellsWithRowIndex,
  getRowIndex,
  getTheRowDatasetIdList,
  isSameRow,
  validDrillTheCell,
  getTheRowHasString,
  getTheRowHasStringAndNumber, getMergeCellsIncludeRowIndex,
} from '../../helpers/utils/utils'

import AddDataFilteringDialog from '../common/CommonDialogDataFilter'

import FunctionDialog from './AttributePanelDialogFunction'
import CustomFormula from './AttributePanelDialogCustom'
import AddPlateauComputingDialog from './AttributePanelDialogPlateauComputing'
import hierarchyProportion from './hierarchyProportion'
import cellColumnLimit from './ColumnLimit/cellColumnLimit'
import cellColumnTotal from './ColumnTotal/index'
import cellRowTotal from './RowTotal/index'

import formatMixins from './mixins/format'

import CellWarning from './CellWarning.vue'
import CellWarningNameList from './CellWarningNameList.vue'

import HierarchyStyleList from './HierarchyStyle'
import { FLOW_MODEL_DATASET } from 'packages/assets/constant'

import StackAccumulationSwitch from './StackAccumulationSwitch.vue'
import StackAccumulationSelect from './StackAccumulationSelect.vue'
import { getKeyNameByCoordinate } from '../../utils/dataCompatibility'
import { isCellForFxExpression } from '../../utils'

import GridZebraCrossingSwitch from './GridZebraCrossing/GridZebraCrossing'
import GridZebraCrossingSet from './GridZebraCrossing/GridZebraCrossingSet'

import FreezeColumns from './FreezeColumns/FreezeColumns'
import FreezeRows from './FreezeRow/index'
import FieldFuzzySwitch from './FieldFuzzy/FieldFuzzy'
import AutoCalcColWidthSwitch from './AutoCalcColWidth'
import TimeArea from './TimeArea/index'
import TextArea from './TextArea/index'
import TotalValue from './TotalValue/index'
import ZeroFormatSwitch from './zeroFormat/index'
import dateDimensionSwitch from './dateDimensionSwitch/index'
import MinUnitValue from './MinUnitValue/index'
import sdpElScrollbar from '../../../../directives/v-el-scrollbar'
import { isTreeTableGroupOption } from './helper'
import MarkInfo from './Marker/index'
import SerialNumberDialog from './AttributePanelDialogSerialNumber'
import { addExcludeCell, removeExcludeCell } from '../../../gridPreview/mixins/exclude'
import { getDatasetIdListByCoordinate } from 'packages/base/grid/components/workspace/CellDataCleaningForDatasetIdList'
import { translateExpression } from 'packages/base/board/displayPanel/utils'
import HyperLinkSty from './LinkStyle/index'
import PictrueSet from './PictureSet/index'
import picture_minxins from './PictureSet/picture_minxins'
import tableColumnLimit from './ColumnLimit/tableColumnLimit'
import SetAnimation from './SetAnimation'
import { TYPES } from '../../helpers/utils/tableOriginalData'
import HeightWidthAdaptive from './HeightWidthAdaptive/index'
import { ADAPTIVE_TYPE } from '../../../gridPreview/render/parser/parseAfter'
import HierarchyLimit from "./hierarchyLimit/index";
import HierarchyLimitDialog from "./hierarchyLimit/hierarchyLimitDialog";
import TipSetDialog
  from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/tipSetDialog.vue";
import RowColumnHide from "packages/base/grid/components/attributes-panel/RowColumnHide/index.vue";

export default {
  mixins: [recordMixin, formatMixins, picture_minxins],

  directives: { sdpElScrollbar },

  inject: {
    langCode: {},
    components: {},
    eventBus: {},
    utils: {},
    getCurrentThemeClass: { default: () => () => '' },
    configs: { default: () => false },
    aliasDict: {
      default() {
        return undefined
      }
    },
    commonData: {
      default() {
        return {}
      }
    },
    themeData: {
      default() {
        return {}
      }
    },
    isTemplateBoard: {
      default() {
        return false
      }
    },
  },

  components: {
    RowColumnHide,
    TipSetDialog,
    HierarchyLimitDialog,
    HierarchyLimit,
    HeightWidthAdaptive,
    SetAnimation,
    tableColumnLimit,
    FunctionDialog,
    AddPlateauComputingDialog,
    AddDataFilteringDialog,
    CustomFormula,
    CellWarning,
    CellWarningNameList,
    HierarchyStyleList,
    StackAccumulationSwitch,
    StackAccumulationSelect,
    GridZebraCrossingSwitch,
    GridZebraCrossingSet,
    FreezeColumns,
    FreezeRows,
    FieldFuzzySwitch,
    AutoCalcColWidthSwitch,
    TimeArea,
    TotalValue,
    hierarchyProportion,
    cellColumnLimit,
    cellColumnTotal,
    cellRowTotal,
    MarkInfo,
    TextArea,
    ZeroFormatSwitch,
    dateDimensionSwitch,
    SerialNumberDialog,
    HyperLinkSty,
    MinUnitValue,
    PictrueSet
  },
  props: {
    tableDefaultConfig: {
      type: Object
    },
    elementConfig: {
      type: Object,
      default: () => ({})
    },
    elId: {
      type: String,
      default: ''
    },
    boardDatasetList: {
      type: Array,
      default: () => []
    },
    datasetList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const hierarchyOptList = this.getHierarchyOptList()
    // 模板中业务函数禁用-表格
    const calcTypeList = [
        {
          // label: '取值',
          label: this.$t('sdp.views.getValue'),
          value: ''
        },
        {
          // label: '分组',
          label: this.$t('sdp.views.group'),
          value: 'GROUP'
        },
        {
          // label: '汇总',
          label: this.$t('sdp.views.SUM'),
          value: 'SUM'
        },
        {
          // label: '平均',
          label: this.$t('sdp.views.average'),
          value: 'AVG'
        },
        {
          // label: '中位数',
          label: this.$t('sdp.views.middleNum'),
          value: 'MEDIAN'
        },
        {
          // label: '最大',
          label: this.$t('sdp.views.largest'),
          value: 'MAX'
        },
        {
          // label: '最小',
          label: this.$t('sdp.views.MIN'),
          value: 'MIN'
        },
        {
          // label: '普通计数',
          label: this.$t('sdp.views.BoardCount'),
          value: 'count'
        },
        {
          label: 'B-FX',
          value: 'lge'
        },
        {
          // label: '去重计数',
          label: this.$t('sdp.views.removeRepeatCountNano'),
          value: 'count_distinct'
        },
        {
          // label: '自定义',
          label: this.$t('sdp.views.custom'),
          value: 'CUSTOM'
        }
    ].filter(item => {
      if (this.isTemplateBoard && item.value === 'lge') {
        return false
      }
      return true
    })
    return {
      // levelBindDatasetId: '', // 层级统计维度所绑定的数据集ID

      minWidth: GRID_OPTIONS.MIN_WIDTH_PX,
      minHeight: GRID_OPTIONS.MIN_HEIGHT_PX,
      // 隐藏行
      hideArr: [],
      isShowSelectHide: false,
      isShowSelectTotal: false,
      // 排名
      ranking: false,
      rankingDisabled: true, // 默认为禁用状态

      dataFilteringShow: false,

      // 行数据筛选
      // 弹窗是否显示
      rowFilteringShow: false,
      // 行数据集ID列表
      rowDatasetIdList: [],
      // 行索引
      theRowIndex: null,

      rankingWay: RANKING.RANKING_WAY.DEFAULT,
      rankingCell: '',
      rankingCells: [],
      rankingFun: [
        {
          label: this.$t('sdp.views.DescendingRank'),
          value: RANKING.RANKING_WAY.DESC
        },
        {
          label: this.$t('sdp.views.AscendingRank'),
          value: RANKING.RANKING_WAY.ASC
        }
      ],
      outOfLineArr: [
        {
          value: '1',
          label: this.$t('sdp.views.foldingDisplay')
        },
        {
          value: '2',
          label: this.$t('sdp.views.fullyDisplayed')
        }
      ],
      activeName: 'first',
      expression: '',
      isCustomExpression: false,
      selectedExtendType: '',
      extendTypeList: [
        {
          value: '1', // 纵向扩展
          label: this.$t('sdp.views.verticalExtension'),
        },
        {
          value: '2', // 横向扩展
          label: this.$t('sdp.views.horizontalExtension'),
        }
      ],
      // compareTypeList: [
      //   {
      //     // label: 'IFS 比较',
      //     label: `${this.$t('sdp.select.iFSCompare')}`,
      //     value: 'infrasys'
      //   },
      //   {
      //     // 同比
      //     label: `${this.$t('sdp.select.yearOnYear')}`,
      //     value: 'year-on-year'
      //   },
      //   {
      //     // 环比
      //     label: `${this.$t('sdp.select.monthOnMonth')}`,
      //     value: 'month-on-month'
      //   },
      // ],
      compareTypeList: [],

      selectedCalc: '',
      calcTypeList,
      sortTypeList: [
        {
          label: this.$t('sdp.views.default'),
          value: 'default'
        },
        {
          // label: '升序',
          label: this.$t('sdp.views.asc'),

          value: 'asc'
        },
        {
          // label: '降序',
          label: this.$t('sdp.views.desc'),

          value: 'desc'
        }
      ],
      functionDialogShow: false,
      tipSetDialogVisible: false,
      rowColumnHideDialogVisible: false,
      rowColumnType: 'column',
      highlightCellHeight: '',
      highlightCellWidth: '',
      highlightCellLineHeight: '',
      columnIndexArr: [],
      rowIndexArr: [],
      addplateaucomputingDialogShow: false,

      // 添加自定义公式弹窗相关数据
      addCustomDialogVisible: false,
      isAddCustomFn: false,
      SUB_AGG_TYPE,
      originRowHeight: {},

      hierarchyOptList,
      textValueDisable: '',
      serialNumberDialogVisible: false,
      hierarchyLimitVisible: false,
      exclude: false,
      excludeDisabled: true,
    }
  },
  computed: {
    isDisabledAnimation() {
      const tableDefaultConfig = this.tableDefaultConfig
      return tableDefaultConfig.isTablePage || tableDefaultConfig.lineNumber === '1' || this.isByTheInteraction
    },
    isTemplate() {
      return this.configs?.type === 'template'
    },
    isScreen() {
      return this.utils.isScreen
    },
    isDataReport() {
      return this.utils.isDataReport
    },
    isByTheInteraction() {
      return this.utils.isByTheInteraction
    },
    isOptionAnimation() {
      return this.isScreen && this.tableDefaultConfig?.animationOption?.isOptionAnimation
    },
    isStack() {
      return this.highlightList.length === 1 && Boolean(this.highlightList[0].content.isStack)
    },
    // 是否选中且仅选中了一个单元格, 并且该单元格为"非合并单元格"
    selectedSingleCell() {
      return this.highlightList.length === 1 && !this.isMergeCell
    },

    //是否包含自定义字段
    isCustomFieldCell() {
      return !!this.highlightList.find(e => e.content.webFieldType)
    },

    // 是否选中了多个单元格
    isSelManyCells() {
      return this.highlightList.length > 1
    },

    // 是否没有选中一个单元格
    isSelZeroCell() {
      return this.highlightList.length === 0
    },

    noNativeCell() {
      return this.highlightList.every(cell => !!cell.isMergeCell)
    },

    isMergeCellOfSelectStart() {
      return this.gridTable.boxSelectStart && this.gridTable.boxSelectStart.isMergeCell
    },
    isExpandYear() {
      const isExpandYear = this.highlightList && this.highlightList.length && this.highlightList.some(cell => {
        return cell.content.compareShowNameType === 'periodtext_expand_prevyear' || cell.content.timeArea === 'period_expand_prevyear'
      })
      return !!isExpandYear
    },
    // 单元格属性 - 扩展方式 - 禁用状态
    extendDisabled() {
      if (this.isStack) return true

      const isExpandYear = this.highlightList && this.highlightList.length && this.highlightList.some(cell => {
        return cell.content.compareShowNameType === 'periodtext_expand_prevyear' || cell.content.timeArea === 'period_expand_prevyear'
      })
      if (isExpandYear) return !!isExpandYear

      const disabled = this.highlightList && this.highlightList.length && this.highlightList.every(cell => {
        const { keyName, compareShowNameType } = cell.content
        return !keyName
      })
      return disabled !== false
    },
    treeGroupCoordinateNum() {
      const num = this.tableDefaultConfig.treeGroupCoordinate.length
      if (num === 1) {
        return this.tableDefaultConfig.treeGroupCoordinate[0]
      } else if (num === 0) {
        return ''
      } else {
        if (this.tableDefaultConfig.treeGroupCoordinate.includes('ALL')) {
          return 'ALL'
        } else {
          return this.$t('sdp.views.multiple')
        }
      }
    },
    hideRowsNum() {
      return this.tableDefaultConfig.hideRows ? this.tableDefaultConfig.hideRows.length : 0
    },
    hideRowsText() {
      const num = this.hideRowsNum
      if (num === 1) {
        return `${this.$t('sdp.views.at')} ${this.tableDefaultConfig.hideRows[0]} ${this.$t('sdp.views.row')}`
      } else if (num === 0) {
        return this.$t('sdp.placeholder.pleaseChoose')
      } else {
        return this.$t('sdp.views.multiple')
      }
    },
    filterIcon() {
      if (
        this.highlightList.length === 1 &&
        this.highlightList[0].content.filters &&
        this.highlightList[0].content.filters.length
      ) {
        return 'icon-sdp-shujushaixuanwanchengx'
      } else {
        return 'icon-sdp-shujukeshaixuanx'
      }
    },
    gridTable() {
      return this.activeInject.gridTable
    },
    effectiveCellList() {
      // 有效单元格
      return this.gridTable.layout.filter(
        cell => cell.content.text !== undefined
      )
    },
    variableList() {
      return this.effectiveCellList.filter(
        cell => cell.content.text && (cell.content.dataSetId || cell.content.isExpression)
      )
    },
    variableFilter() {
      const totalValue = this.tableDefaultConfig.totalValue
      if (totalValue && totalValue.length > 0) {
        return this.variableList.filter(
          cell => !totalValue.includes(cell.start[0] + 1)
        )
      }
      return this.variableList
    },
    treeTableGroupList() {
      // 层级分类归宗列表
      const ret = ['ALL', 'Only Title']
      const coordinateList = this.variableFilter
        .filter(isTreeTableGroupOption)
        .map(
          cell => `${spreadsheetColumnLabel(cell.start[1])}${cell.start[0] + 1}`
        )
      return ret.concat(coordinateList)
    },
    highlightCell() {
      return this.gridTable.highlightCell
    },
    highlightList() {
      return this.gridTable.highlightList
    },
    highlightListWithMerge() {
      if (typeof this.theRowIndex !== 'number') return []
      const cells = this.components.gridWorkspace.layoutOriginal[this.theRowIndex]
      const validMergeCells = getMergeCellsIncludeRowIndex.call(this, this.layoutOptions.mergeCell, this.theRowIndex)
      return [...validMergeCells, ...cells]
    },
    highlightListValid() {
      return this.highlightList.length > 0
    },
    layoutOptions() {
      return this.gridTable.layoutOptions
    },
    highlightCellName() {
      // 选中的第一个单元格
      const cell = this.gridTable.boxSelectStart

      if (!cell) return ''

      const [row, col] = cell.start
      return spreadsheetColumnLabel(col) + (row + 1)
    },
    isMergeCell() {
      return this.highlightList.length
        ? this.highlightList[0].isMergeCell
        : false
    },
    isMobile() {
      return Boolean(this.utils.isMobile)
    },

    isDisableTreeTableGroup() {
      return false
    },

    extendMerge: {
      get() {
        const cells = this.highlightList
        return cells.length === 1 ? Boolean(cells[0].content.extendMerge) : false
      },
      set(bool) {
        const cells = this.highlightList
        this[bool ? '$set' : '$delete'](cells[0].content, 'extendMerge', bool)
      }
    },
    extendMergeShow() {
      if (this.isStack || this.ranking || this.exclude) return false

      return this.highlightList.length === 1 && this.extendDisabled
    },
    extendMergeIsConflict() {
      const cells = this.highlightList

      if (cells.length !== 1) return false

      const { keyName, compareShowNameType, extendMerge } = cells[0].content

      if (!keyName || compareShowNameType) return false

      return Boolean(extendMerge)
    },

    isSingleDataView() {
      return this.components.gridDesign.isSingleDataView
    },

    hasFlowDataset() {
      return this.datasetList.some(item => item.modelType === FLOW_MODEL_DATASET)
    },

    hasDataBar() {
      const dataBarInfo = this.tableDefaultConfig.dataBarInfo || []
      return dataBarInfo.length > 0
    },

    hasAdaptive() {
      return this.tableDefaultConfig.isTablePage && this.effectiveCellList.find(e => e.content.adaptive && e.content.adaptive === ADAPTIVE_TYPE.WIDTH) || false
    },

    isRowsLimit() {
      return this.tableDefaultConfig.lineNumber === '2' || false
    },

    rowCellDataFilteringDisabled() {
      if (this.highlightList.length) {
        const normalCellIndexs = Array.from(new Set(this.highlightList.map(e => e.rowIndex).filter(e => e)))
        const mergeCellList = this.highlightList.filter(e => e.isMergeCell)
        const mergeCellIndexs = []
        mergeCellList.forEach(e => {
          for (let i = e.start[0]; i <= e.end[0]; i++) {
            mergeCellIndexs.push(i)
          }
        })
        const rowIndexs = Array.from(new Set([...normalCellIndexs, ...mergeCellIndexs]))
        for (let i = 0; i < rowIndexs.length; i++) {
          const rowCells = this.gridTable.layoutOriginal[rowIndexs[i]]
          if (rowCells.some(e => e.content.dataSetId && e.content.dataSetId === 'virtual')) {
            return true
          }
        }
      }
      return false
    },

    cellDataFilteringDisabled() {
      if (this.highlightCell) {
        if (this.highlightCell.content.dataSetId && this.highlightCell.content.dataSetId === 'virtual') {
          return true
        }
      }
      return false
    },

    // 列总计是否可见
    columnTotalCellVisible() {
      if (this.highlightList.length <= 1) {
      return this.columnTotalCellList.length > 0 && !this.highlightCell?.content?.exclude
      } else {
        const values = Object.values(this.columnTotalMultipleCellList)
        return !!values.length && values.every(e => e.length)
      }
    },

    columnTotalMultipleCellList() {
      if (this.highlightList.length <= 1) return {}
      if (this.highlightList.some(e => e.content.text || e.isMergeCell)) return {}

      const DIC = {}
      this.highlightList.forEach((cell, index) => {
        const columnTotalList = []
        // 获取当列变量
        const columnCellList = this.effectiveCellList.filter(e =>
          !e.isMergeCell &&
          e.columnIndex === cell.columnIndex &&
          e.content.aggType &&
          e.content.aggType !== '' &&
          !e.content.rowTotal
        )

        // 遍历变量当行是否有维度
        columnCellList.forEach(item => {
          const dimCell = this.effectiveCellList.find(e =>
            !e.isMergeCell &&
            e.rowIndex === item.rowIndex &&
            ['date', 'string'].includes(e.content.columnType)
          )
          // 放入数组
          if (dimCell) {
            columnTotalList.push(item)
          }
        })
        // 返回数据
        DIC[index] = columnTotalList
      })
      console.log('columnTotalMultipleCellList DIC', DIC)
      return DIC
    },

    // 列总计可选单元格
    columnTotalCellList() {
      if (this.highlightList.length !== 1) return []
      if (this.highlightCell.content.text || this.highlightCell.isMergeCell) return []
      const columnTotalList = []
      // 获取当列变量
      const columnCellList = this.effectiveCellList.filter(e =>
        !e.isMergeCell &&
        e.columnIndex === this.highlightCell.columnIndex &&
        e.content.aggType &&
        e.content.aggType !== '' &&
        !e.content.rowTotal
      )

      // 遍历变量当行是否有维度
      columnCellList.forEach(cell => {
        const dimCell = this.effectiveCellList.find(e => !e.isMergeCell && e.rowIndex === cell.rowIndex && ['date', 'string'].includes(e.content.columnType))
        // 放入数组
        if (dimCell) {
        // if (cell.content.columnType === 'number') {
          columnTotalList.push(cell)
        }
      })
      // 返回数据
      return columnTotalList
    },

    // 行总计是否可见
    rowTotalCellVisible() {
      if (this.highlightList.length <= 1) {
      return this.rowTotalCellList.length > 0 && !this.highlightCell?.content?.exclude
      } else {
        const values = Object.values(this.rowTotalMultipleCellList)
        return !!values.length && values.every(e => e.length)
      }
    },

    rowTotalMultipleCellList() {
      if (this.highlightList.length <= 1) return {}
      if (this.highlightList.some(e => e.content.text)) return {}
      const DIC = {}
      let success = true
      this.highlightList.forEach((cell, index) => {
        // 如果上方没有横向扩展的维度
        const rowExtendCell = this.effectiveCellList.filter(e => {
          const isDimensionCell = ['date', 'string'].includes(e.content.columnType)
          const isExtend2 = e.content.extend === '2'

          if (e.isMergeCell) {
            const isRow = e.end[0] < cell.rowIndex
            return isRow && isDimensionCell && isExtend2
          } else {
            return e.rowIndex < cell.rowIndex && isDimensionCell && isExtend2
          }
        }) || []

        if (!rowExtendCell.length) {
          DIC[index] = []
          success = false
          return false
        }

        // 如果左右没有纵向扩展的度量
        const columnExtendCell = this.effectiveCellList.filter(e => {
          const isNoAggType = e.content.aggType === ''
          const isExtend1 = e.content.extend === '1'

          if (e.isMergeCell) {
            const isRow = e.start[0] === e.end[0] && e.start[0] === cell.rowIndex
            return isRow && !isNoAggType && isExtend1
          } else {
            return e.rowIndex === cell.rowIndex && !isNoAggType && isExtend1
          }
        }) || []
        DIC[index] = columnExtendCell
      })

      console.log('rowTotalMultipleCellList DIC', DIC)
      return success ? DIC : {}
    },
    // 行总计可选单元格
    rowTotalCellList() {
      if (this.highlightList.length !== 1) return []
      if (this.highlightCell.content.text) return []
      // 如果上方没有横向扩展的维度
      const rowExtendCell = this.effectiveCellList.filter(e => {
        const isDimensionCell = ['date', 'string'].includes(e.content.columnType)
        const isExtend2 = e.content.extend === '2'

        if (e.isMergeCell) {
          const isRow = e.end[0] < this.highlightCell.rowIndex
          return isRow && isDimensionCell && isExtend2
        } else {
          return e.rowIndex < this.highlightCell.rowIndex && isDimensionCell && isExtend2
        }
      }) || []

      if (!rowExtendCell.length) return []
      // 如果左右没有纵向扩展的度量
      const columnExtendCell = this.effectiveCellList.filter(e => {
        const isNoAggType = e.content.aggType === ''
        const isExtend1 = e.content.extend === '1'

        if (e.isMergeCell) {
          const isRow = e.start[0] === e.end[0] && e.start[0] === this.highlightCell.rowIndex
          return isRow && !isNoAggType && isExtend1
        } else {
          return e.rowIndex === this.highlightCell.rowIndex && !isNoAggType && isExtend1
        }
      }) || []

      // 返回左右的度量单元格
      return columnExtendCell
    },

    // 维度单元格
    dimensionCellList() {
      return this.effectiveCellList.filter(e => ['date', 'string'].includes(e.content.columnType) && e.content.aggType !== 'CUSTOM' && !e.content.webFieldType)
    }
  },

  watch: {
    // 选中单元格更改时切换右侧属性
    highlightList: {
      handler(val) {
        const len = this.highlightList.length
        if (len === 1) {
          this.getCell()
          const cell = this.highlightList[0]
          const { content } = cell
          const columnIndexArr = cell.columnIndex
          const rowIndexArr = cell.rowIndex

          this.columnIndexArr = [columnIndexArr]
          this.rowIndexArr = [rowIndexArr]

          this.selectedExtendType = content.extend
          if (!this.selectedExtendType && content.keyName && content.compareShowNameType) {
            this.changeExtendType('1')
            this.selectedExtendType = '1'
          }

          this.isCustomExpression = content.aggType === 'CUSTOM'
          this.isAddCustomFn = cell.content.subAggType === this.SUB_AGG_TYPE.CUSTOMER_EXPRESSION
          this.expression = this.isCustomExpression ? this.updateExpressionFieldAlias(content.text, content.dataSetId) || '' : ''
          if (content.aggType === undefined) {
            this.selectedCalc = ''
          } else {
            this.calcTypeList.forEach(v => {
              if (v.value === content.aggType) {
                this.selectedCalc = v.label
              }
            })
          }

          // 排名
          this.rankingDisabled = (() => {
            if (content.rankType) return false
            if (this.isStack) return true

            return !isNoDataCell(cell)
          })()
          this.ranking = content.rankType || false
          this.exclude = content.exclude || false
          // this.nullChecked = content.nullChecked
          content.rankType && this.rankingChange(true)
          // 去除
          this.excludeDisabled = (() => {
            if (content.exclude) return false
            if (this.isStack) return true
            if (content.text) return true
            if (content.rowTotal) return true
            if (content.columnTotal) return true
            const rowIndex = getRowIndex(cell)
            if (!isNaN(rowIndex)) {
              if (this.isNoDataset(rowIndex)) return true
            }
            if (this.gridTable.layout.filter(cell => cell.content.exclude !== undefined)
              .some(cell => cell.content.exclude === true)) return true
            const hasNotString = getTheRowHasStringAndNumber(cell, rowIndex, this.components.gridWorkspace.layoutOriginal, this.layoutOptions.mergeCell)
            return !hasNotString
          })()
        }
        if (len > 1) {
          this.getCell()
          this.ranking = false
          // this.nullChecked = false
          this.rankingDisabled = true
          this.exclude = false
          this.excludeDisabled = true
          const columnIndexArr = [
            ...new Set(
              this.highlightList.map(highlightList => highlightList.columnIndex)
            )
          ]
          const rowIndexArr = [
            ...new Set(
              this.highlightList.map(highlightList => highlightList.rowIndex)
            )
          ]
          this.columnIndexArr = columnIndexArr.filter(index => index !== undefined)
          this.rowIndexArr = rowIndexArr.filter(index => index !== undefined)
        }
        const tableDefaultConfig = this.tableDefaultConfig
        if (
          tableDefaultConfig.collapseCol.length ||
          tableDefaultConfig.collapseColShows.length
        ) {
          // this.getExistenceColumn()
        }

        // 重置单元格属性
        if (!len) {
          this.highlightCellHeight = ''
          this.highlightCellWidth = ''
          this.highlightCellLineHeight = ''
          this.selectedExtendType = ''
          this.selectedCalc = ''
          this.expression = ''
          this.isCustomExpression = false
          this.selectedFormat = 'convention'
          this.ranking = false
          this.rankingDisabled = true
          this.rankingWay = RANKING.RANKING_WAY.DEFAULT
          this.rankingCell = ''
          this.exclude = false
          this.excludeDisabled = true
        }
      },
      deep: true
    },
    'treeTableGroupList.length': {
      handler(val) {
        this.reSetTreeGroupCoordinate()
      },
      immediate: true
    },
    isSelManyCells(bool) {
      if (bool) {
        // 空置自定义表达式
        this.expression = ''
      }
    },

    extendMergeIsConflict(bool) {
      bool && (this.extendMerge = false)
    }
  },

  methods: {
    checkMoreLanguage() {
      this.$emit('checkMoreLanguage')
    },
    recordCellOperation(type) {
      this.record(type)
    },
    cancelFormatBrush(callback) {
      this.store.isActiveFormatBrush = ''
      callback && callback.call(this)
    },
    handleHierarchyLimitEdit() {
      this.hierarchyLimitVisible = true
    },
    handleOpenRowColumnHideDialog(val) {
      this.rowColumnType = val
      this.rowColumnHideDialogVisible = true
    },
    minWidthChange(e) {
      if (e % 5 !== 0) {
        const minWidth = 5 * Math.ceil(e / 5)
        this.$set(this.tableDefaultConfig, 'minWidth', minWidth)

        const minWidthInput = this.$refs.minWidthInput
        if (minWidthInput) {
          setTimeout(() => {
            this.$set(minWidthInput, 'currentValue', minWidth)
          }, 20)
        }
      }
    },
    // 更新表达式，替换别名或本名
    updateExpressionFieldAlias(expression, datasetId, isAlias = false) {
      return this.aliasDict?.updateExpressionFieldAlias(expression, datasetId, isAlias) || expression

      // if (!expression || !datasetId) return expression
      //
      // let exp = expression
      // const fieldList = this.aliasDict?.getFieldList(datasetId, isAlias) || []
      // const dict = this.aliasDict?.dict[datasetId]?.currDict || {}
      // const fieldDict = fieldList.reduce((pre, cur) => {
      //   pre[cur] = dict[cur]
      //   return pre
      // }, {})
      //
      // exp = translateExpression(exp, fieldDict)
      //
      // console.log('%c%s', 'background: blue;color:#FFF;', exp)
      // return exp
    },
    rankingChange(bool) {
      const cell = this.highlightList[0]
      bool ? this.rankingInit(cell) : this.rankingDestroy(cell.content)
    },
    rankingInit(cell) {
      const content = cell.content
      const cells = []
      const columnNameList = this.gridTable.layout
        .filter(cell => cell.content.rankType && cell.content.columnName)
        .map(cell => cell.content.columnName)

      this.gridTable.layout.forEach(item => {
        const {
          coordinate,
          dataSetId,
          isExpression,
          isStack,
          rankType,
          keyName
        } = item.content

        if (
          !rankType &&
          !isStack &&
          coordinate &&
          // !isCellForFxExpression(item) &&   //自定义公式的单元格放开限制 by邓鹏 黄寅初 ：2020/9/25
          !columnNameList.includes(coordinate) &&
          (dataSetId || isExpression)
        ) {
          cells.push({
            label: item.content.coordinate,
            dataSetId: item.content.dataSetId || ''
          })
        }
      })
      const nCells = []
      cells.forEach(item => {
        if (!item.dataSetId) {
          const dataSetList = getDatasetIdListByCoordinate(item.label, this.gridTable.layout, [item.label])
          if (dataSetList.length === 1 && dataSetList[0]) {
            nCells.push({ label: item.label, dataSetId: dataSetList[0] })
          }
        } else {
          nCells.push(item)
        }
      })
      this.rankingCells.splice(0, this.rankingCells.length, ...nCells)
      if (!content.rankType) {
        this.$set(content, 'rankType', true)
        this.$set(content, 'parallelRanking', true)
        // this.$set(content, 'nullChecked', false)
        this.$set(content, 'coordinate', generateCoordinateForCell(cell))
        this.changeFormatType('numerical')
      } else {
        if (content.parallelRanking === undefined) {
          this.$set(content, 'parallelRanking', true)
        }
      }
      // 排序判断
      if (content.rankOrder) {
        this.rankingWay = content.rankOrder
      } else {
        this.$set(content, 'rankOrder', this.rankingWay)
      }
      // 位置判断
      this.rankingCell = content.columnName || ''

      this.selectedCalc = this.$t('sdp.views.getValue')
      this.calcChange('')
      this.expression = ''
    },
    rankingDestroy(content) {
      if (content.rankType) {
        this.$delete(content, 'coordinate')
        this.$delete(content, 'text')
        this.$delete(content, 'cellFormat')
        this.$delete(content, 'aggType')
        this.$delete(content, 'extend')
        this.$delete(content, 'columnType')
        this.$delete(content, 'keyName')
        this.$delete(content, 'dataSetId')
        this.$delete(content, 'targetCellKeyName')
        // this.$delete(content, 'nullChecked')
        // this.$delete(content, 'datasetIdList')
      }
      this.$delete(content, 'rankType')
      this.$delete(content, 'rankOrder')
      this.$delete(content, 'parallelRanking')
      this.$delete(content, 'columnName')
      this.rankingCell = ''
      this.rankingWay = RANKING.RANKING_WAY.DEFAULT
    },
    customInputRefFocus() {
      this.isAddCustomFn && this.$refs.customInputRef.blur()
    },
    // 检测单选按钮的可选状态
    checkRadioStatus({ value: radioType }) {
      const cellNum = this.highlightList.length

      // 没有选中单元格时, 不可以使用计算公式
      if (cellNum === 0) return false

      // 只针对自定义维度放开部分限制
      // 如果存在不满足的条件，则返回禁用，如果能使用，则走后面的判断
      if (this.isCustomFieldCell) {
        if (![CALC_FNS.VALUE, CALC_FNS.GROUP].includes(radioType)) {
          return false
        }
        const cellAbandon = this.highlightList.some(cell => {
          const { webFieldType, customExpressionType } = cell.content
          if (webFieldType !== 'customComputed' || customExpressionType !== 'dimension') {
            return true
          }
          return false
        })
        if (cellAbandon) return false
      }

      if (radioType === CALC_FNS.LGE && !this.isSingleDataView) return false

      // 流式数据集暂不支持业务函数
      if (radioType === CALC_FNS.LGE && this.hasFlowDataset) return false

      // 选中多个单元格时
      if (cellNum > 1) {
        // 过滤无效单元格
        const validCellList = this.highlightList.filter(cell => {
          const type = cell.content.aggType
          return type !== undefined && ![CALC_FNS.LGE, CALC_FNS.CUSTOM].includes(type)
        })

        const validLength = validCellList.length

        // 有效单元格数量为 0 时, 不可以使用计算公式
        if (validLength === 0) return false

        // 设置 selectedCalc 字段
        setSelByValidCellList.call(this, validCellList)

        // 选中多个单元格时, 只可以使用 除了 业务函数 和 自定义函数 的其他函数
        return !checkSpecial(radioType)
      }

      // 以下逻辑: 当且仅当选中一个单元格.

      // 单元格数据
      const { content: cellData } = this.highlightList[0]

      // 单元格类型
      const { aggType: cellType, isStack, rankType: isRank } = cellData

      if (isStack || isRank) return radioType === ''

      // “假”自定义函数的处理方式 或 默认类型的单元格: 可以使用 业务函数 和 自定义函数
      if ((cellType === CALC_FNS.CUSTOM && !cellData.isExpression) || cellType === undefined) {
        return checkSpecial(radioType)
      }

      if (radioType === 'MEDIAN' && cellData.columnType !== 'number') {
        return false
      }

      // 特殊类型的单元格: 只可以使用与单元格自身类型匹配的函数
      if (checkSpecial(cellType)) {
        return cellType === radioType
      }

      // /////////////////////////////////////////////////////////
      // 其他类型的单元格: 只可以使用 除了 业务函数 的其他函数
      // return radioType !== CALC_FNS.LGE
      // /////////////// ↓↓↓↓↓需求变更↓↓↓↓↓ ///////////////////////
      // 其他类型的单元格 ( 非特殊类型的普通类型 ) 可以使用所有函数
      return true

      /**
       * 检测某类型是否是特殊类型
       *
       * 特殊类型这里是指: LGE 和 CUSTOM
       *
       * @param {string} type
       * @returns {boolean}
       */
      function checkSpecial(type) {
        return [CALC_FNS.LGE, CALC_FNS.CUSTOM].includes(type)
      }

      /**
       * 根据有效单元格列表中的 aggType 是否全部一致来设置 selectedCalc 字段
       *
       * @param {Array} list 有效单元格列表
       */
      function setSelByValidCellList(list) {
        const length = list.length
        const typeOne = list[0].content.aggType
        let count = 1
        // 有效单元格列表的 aggType 是否全部一致的 标识
        let flag = true

        while (count < length) {
          const typeCount = list[count].content.aggType
          if (typeCount !== typeOne) {
            flag = false
            break
          }
          count++
        }

        const selectedCalc = flag ? this.getCalcTypeLabelByValue(typeOne) : ''
        this.selectedCalc = selectedCalc
      }
    },
    getLine(onlyVar) {
      const layoutOriginal = this.activeInject.gridTable.layoutOriginal
      const arr = layoutOriginal.reduce((previous, current, index) => {
        if (
          current.some(
            item => onlyVar
              ? (item.content.keyName && item.content.text)
              : (item.content.text || Object.keys(item.content.style).length)
          )
        ) {
          const label = `${this.$t('sdp.views.at')} ${index + 1} ${this.$t('sdp.views.row')}`
          previous.push({
            value: index + 1,
            label,
          })
        }
        return previous
      }, [])
      return Array.isArray(arr) ? arr : []
    },
    // 获得 hideArr
    getHide() {
      this.hideArr = this.getLine()
    },
    getTotalArr(cb) {
      const result = this.getLine()
      cb(result)
    },
    // 更新隐藏行下拉列表
    updateHideOpts() {
      const list = this.hideArr = this.getLine()
      let hideRows = this.tableDefaultConfig.hideRows
      this.tableDefaultConfig.hideRows = hideRows.filter((item) => {
        return list.map(({ value }) => value).includes(item)
      })
    },

    // 修改汇总行
    changeTotal(v) {
      // 汇总行变化时, 需要重置分类层级汇总
      // this.tableDefaultConfig.treeGroupCoordinate = ['ALL']
      let cur = this.treeTableGroupList.filter(item => item !== 'Only Title')
      this.$set(this.tableDefaultConfig, 'treeGroupCoordinate', cur)
      this.record('updateTotalValue')

      // const old = this.tableDefaultConfig.treeGroupCoordinate
      // let cur = null
      //
      // // 获取分类层级汇总设置的行
      // const coordinates = old
      //   .filter(v => /\w+\d+/.test(v))
      //   .map(coordinate => splitCoordinate(coordinate).digit)
      //
      // // 从分类层级汇总中剔除属于汇总行的单元格 ( 需求: 分类层级汇总不能是汇总行的单元格 )
      // if (coordinates.includes(+v)) {
      //   cur = old.filter(coords => splitCoordinate(coords).digit !== v)
      //   this.tableDefaultConfig.treeGroupCoordinate = cur.length ? cur : ['ALL']
      // }
    },
    changeHide() {
      this.record('updateHideRows')
    },
    showSelectHide(val) {
      this.isShowSelectHide = val
    },

    // 分类层级汇总
    showSelectTotal(val) {
      this.isShowSelectTotal = val
    },
    clearTreeGroupCoordinate() {
      if (!this.tableDefaultConfig.treeGroupCoordinate.length) {
        let cur = this.treeTableGroupList.filter(item => item !== 'Only Title')
        this.$set(this.tableDefaultConfig, 'treeGroupCoordinate', cur)
        // this.tableDefaultConfig.treeGroupCoordinate = ['ALL']
      }
    },
    clickTreeGroupCoordinate(clickVal) {
      const singleType = ['ALL', 'Only Title']
      let cur = []
      if (singleType.includes(clickVal)) {
        if (clickVal === 'ALL') {
          this.treeTableGroupList.forEach(item => {
            item !== 'Only Title' && cur.push(item)
          })
        } else {
          cur.push(clickVal)
        }
      } else {
        cur = this.tableDefaultConfig.treeGroupCoordinate.filter(val => !singleType.includes(val))
        if (this.treeTableGroupList.length - cur.length === 2 && !cur.includes('All')) {
          cur.push('ALL')
        }
        if (!cur.length) {
          cur = ['Only Title']
        }
      }
      this.tableDefaultConfig.treeGroupCoordinate = cur
      this.record('updateTreeGroupCoordinate')
    },
    // 数据过滤
    dataFiltering() {
      if (this.cellDataFilteringDisabled) return
      if (this.highlightList.length === 1) {
        if (this.isStack || this.ranking || this.exclude) return
        const currentCell = this.highlightList[0]
        if (currentCell.content.dataSetId) {
          const rowIndex = getRowIndex(currentCell)
          const hasNotString = getTheRowHasString.call(this, currentCell, rowIndex, this.components.gridWorkspace.layoutOriginal, this.layoutOptions.mergeCell)
          // 横向扩展 则放开限制
          const isHorizontalExtend = currentCell.content?.extend === '2'
          if (isHorizontalExtend || hasNotString) {
            this.dataFilteringShow = true
          } else {
            this.$message.warning(this.$t('sdp.views.cellFilterWarning'))
          }
        } else {
          this.$message(this.$t('sdp.views.theCell'))
        }
      } else if (this.highlightList.length) {
        this.$message(this.$t('sdp.views.exNotAllowOper'))
      } else {
        this.$message(this.$t('sdp.views.explsTableUnit'))
      }
    },
    serialNumberDialog() {
      this.serialNumberDialogVisible = true
    },
    serialNumSave(value) {
      this.tableDefaultConfig.serialNumber = value
      if (value) {
        this.tableDefaultConfig.isTablePage = false
      }
      this.record('serialNumSave')
    },
    /**
     * 判断某行是否无数据
     * @param index 行索引
     * @returns {boolean}
     */
    isNoDataset(index) {
      const cells = this.components.gridWorkspace.layoutOriginal[index]
      const validMergeCells = getMergeCellsWithRowIndex.call(this, this.layoutOptions.mergeCell, index)
      return [...validMergeCells, ...cells].every(cell => !cell.content.dataSetId)
    },
    /**
     * 判断某行是否无数据 合并单元格也会算在里面
     * @param index 行索引
     * @returns {boolean}
     */
    isNoDatasetIncludeMerge(index) {
      const cells = this.components.gridWorkspace.layoutOriginal[index]
      const validMergeCells = getMergeCellsIncludeRowIndex.call(this, this.layoutOptions.mergeCell, index)
      return [...validMergeCells, ...cells].every(cell => !cell.content.dataSetId)
    },

    // 行数据过滤
    rowDataFiltering() {
      if (this.rowCellDataFilteringDisabled) return
      const cells = this.highlightList
      const isEmpty = cells.length === 0

      // 0 行
      if (isEmpty) {
        this.$message(this.$t('sdp.views.plsSelRow'))
        return
      }

      // 多行
      if (!isSameRow.call(this, cells)) {
        this.$message(this.$t('sdp.views.noMulRowsOperation'))
        return
      }

      this.theRowIndex = getRowIndex(cells[0])

      if (this.isNoDatasetIncludeMerge(this.theRowIndex)) {
        this.theRowIndex = null
        this.$message(this.$t('sdp.views.theRow'))
        return
      }

      this.rowDatasetIdList = getTheRowDatasetIdList.call(this, this.theRowIndex, this.components.gridWorkspace.layoutOriginal, this.layoutOptions.mergeCell)
      this.rowFilteringShow = true
    },

    // 重置折叠和隐藏列
    getExistenceColumn() {
      const existenceCell = this.gridTable.layout.filter(
        e => e.content.text || Object.keys(e.content.style).length
      )
      const column = existenceCell
        .map(e => e.columnIndex)
        .sort((a, b) => a - b)
      const arr = Array.from(new Set(column))
      const tableDefaultConfig = this.tableDefaultConfig
      const fun = item => arr.includes(item) || arr[arr.length - 1] > item
      const arrCol = tableDefaultConfig.collapseCol.filter(fun)
      const arrShows = tableDefaultConfig.collapseColShows.filter(fun)
      this.$set(tableDefaultConfig, 'collapseCol', arrCol)
      this.$set(tableDefaultConfig, 'collapseColShows', arrShows)
    },
    // 'cellAttribute'
    getCell() {
      // 选中的第一个单元格
      const cell = this.gridTable.boxSelectStart

      if (!cell) return

      const { height, width } = calcCellWH(cell, 1)
      this.highlightCellHeight = parseInt(height)
      this.highlightCellWidth = parseInt(width)

      const fontSize = this.$_getProp(cell.content.style, 'font-size', '12px')
      this.highlightCellLineHeight = parseInt(this.$_getProp(cell.content.style, 'line-height', fontSize))
    },
    // 改变列的宽度
    changeLayoutOptionsWidth(value) {
      // const changeX = Math.round((value + 5) / 10)
      this.columnIndexArr.forEach(column => {
        const columnIndex = column
        const defalutX = this.highlightList.find(highlightList => {
          return highlightList.columnIndex === column
        }).w
        const distanceW = value - defalutX
        const distance = { distance: distanceW, index: columnIndex }
        this.gridTable.changeWidth(distance, 'changeWidth')
      })
    },

    // 改变行的高度
    changeLayoutOptionsHeigth(value) {
      // const changeY = Math.round((value + 5) / 5)
      this.rowIndexArr.forEach(row => {
        const rowIndex = row
        const defalutY = this.highlightList.find(highlightList => {
          return highlightList.rowIndex === row
        }).h
        const distanceH = value - defalutY

        const distance = { distance: distanceH, index: rowIndex }
        this.gridTable.changeHeight(distance, 'cell')
      })
    },
    changeCellLineHeight(lineHeight) {
      this.highlightList.forEach(cell => {
        if (cell.content.type === TYPES.IMAGE) {
          return
        }

        this.$set(cell.content.style, 'line-height', `${lineHeight}px`)
      })
    },
    // 修改扩展类型
    changeExtendType(val) {
      if (val === '2' && this.tableDefaultConfig.autoCalcColWidth) {
        this.$sdp_eng_confirm(`${this.$t('sdp.views.b_tableadAptationAndTableadAptation')}`, this.$t('sdp.dialog.hint'), {
            confirmButtonText: this.$t('sdp.button.ensure'),
            cancelButtonText: this.$t('sdp.button.cancel'),
            cancelButtonClass: 'el-button--sdp-cancel',
            confirmButtonClass: 'el-button--sdp-ensure',
            customClass: 'sdp-dialog',
            type: 'warning',
          }).then(() => {
            this.$delete(this.tableDefaultConfig, 'autoCalcColWidth')
            setExtendType.call(this, val)
          }).catch(e => {
            this.selectedExtendType = '1'
          })
      } else {
        setExtendType.call(this, val)
      }
      function setExtendType(value) {
        this.highlightList
          .filter(cell => cell.content.keyName)
          .forEach(cell => {
            if (cell.tableHeaderOptions) {
              if (val === '2') {
                // 单元格扩展属性为横向扩展的表头的表头定义的默认配置
                Object.assign(cell.tableHeaderOptions, {
                  show: true,
                  search: false,
                  sort: false,
                })
              } else {
                // 单元格扩展属性为纵向扩展的表头的表头定义的默认配置
                Object.assign(cell.tableHeaderOptions, {
                  show: true,
                  search: false,
                  sort: true
                })
              }
            }

            this.$set(cell.content, 'extend', val)
          })
          this.record('changeExtendType')
      }
    },
    // 修改排序
    changeOrder(val) {
      console.log(val)
      this.highlightList.forEach(cell => {
        this.$set(cell.content, 'order', val)
      })
    },
    // 排名位置
    rankingCellUpdate(label) {
      const { dataSetId } = this.rankingCells.find(item => {
        return item.label === label
      })
      // 添加默认属性
      this.highlightList.forEach(cell => {
        this.$set(cell.content, 'columnName', label)
        this.$set(cell.content, 'targetCellKeyName', getKeyNameByCoordinate(label, this.gridTable.layout))
        this.$set(cell.content, 'text', `Rank(${label})`)
        this.$set(cell.content, 'extend', '1')
        this.$set(cell.content, 'columnType', 'number')
        cell.content.keyName || this.$set(cell.content, 'keyName', generateKeyName())
        this.$set(cell.content, 'dataSetId', dataSetId)
      })
      this.record('rankingCellUpdate')
    },
    // 排名方式
    rankingWayChange(way) {
      this.$set(this.highlightCell.content, 'rankOrder', way)
      this.record('rankingWayChange')
    },
    // 计算
    calcChange(val) {
      // 选中单个单元格时
      if (this.highlightList.length === 1) {
        const cell = this.highlightList[0]

        // 从 非特殊 非空白 类型的单元格 切换至 业务函数类型的单元格 的逻辑
        if (val === CALC_FNS.LGE) return

        // 从 非特殊 非空白 类型的单元格 切换至 自定义类型的单元格 的逻辑
        if (val === CALC_FNS.CUSTOM) {
          this.isCustomExpression = true
          this.$nextTick(() => {
            this.$refs.customInputRef.focus()
          })
          return
        }

        this.updateDrillDataWhenAggTypeChanged(val, cell)

        commonHandler.call(this, cell)
      } else {
        // 过滤无效单元格
        const validCellList = this.highlightList.filter(cell => {
          const { aggType, isStack, rankType: isRank } = cell.content

          if (isStack || isRank) return false

          return aggType !== undefined && ![CALC_FNS.LGE, CALC_FNS.CUSTOM].includes(aggType)
        })

        validCellList.forEach(cell => commonHandler.call(this, cell))
      }

      this.record('calculate-formula')

      function commonHandler(cell) {
        this.$set(cell.content, 'aggType', val)
        this.isCustomExpression = val === 'CUSTOM'
        this.isAddCustomFn = cell.content.subAggType === this.SUB_AGG_TYPE.CUSTOMER_EXPRESSION
      }
    },
    calcClick(val, event) {
      // 只处理来自 input 元素冒泡的点击事件
      if (event && event.target.tagName !== 'INPUT') return
      this.store.isActiveFormatBrush = ''
      this.highlightList.forEach(cell => {
        if (val === 'B-FX') {
          if (!this.datasetList.length) {
            return this.$message.warning(
              this.$t('sdp.views.pleaseIntroduceDataSetsFirst')
            )
          }
          if (!cell.content.lgeType) this.$set(cell.content, 'lgeType', 'lge')
          this.addplateaucomputingDialogShow = true
        } else {
          this.$delete(cell.content, 'lge')
          this.$delete(cell.content, 'lgeType')
        }
      })
    },
    customExpression(subAggType, data) {
      let text = ''
      let alias = ''
      const cell = this.highlightList[0]
      this.$set(cell.content, 'aggType', CALC_FNS.CUSTOM)
      this.$set(cell.content, 'subAggType', subAggType)
      this.$set(cell.content, 'isExpression', true)
      cell.content.extend !== '2' && this.$set(cell.content, 'extend', '1')
      this.$set(cell.content, 'columnType', data?.expReturnType || 'number')
      alias = text = this.expression.replace(/\r|\n|\s/g, '').toUpperCase()
      if (subAggType === this.SUB_AGG_TYPE.EXPRESSION) {
        this.$delete(cell.content, 'dataSetId')
        this.$delete(cell.content, 'customFnData')
      } else if (subAggType === this.SUB_AGG_TYPE.CUSTOMER_EXPRESSION) {
        const {
          expression,
          displayExpression,
          datasetId,
          formulaName
        } = data
        const expr = displayExpression || expression
        alias = expr.replace(/\r|\n|\s/g, '')
        text = `${CALC_FNS.CUSTOM}(${formulaName || alias})`
        this.$set(cell.content, 'dataSetId', datasetId)
        if (cell.content.customFnData && cell.content.customFnData.expReturnType !== data.expReturnType) {
          this.$delete(cell.content, 'warningConditionList') // 如果改变了计算函数的返回值类型，需要清空预警
        }
        this.$set(cell.content, 'customFnData', { ...data })
      }
      this.$set(cell.content, 'text', text)

      cell.content.keyName || this.$set(cell.content, 'keyName', generateKeyName())
      if (!cell.content.coordinate) {
        const coordinate = `${spreadsheetColumnLabel(cell.start[1])}${cell.start[0] + 1}`
        this.$set(cell.content, 'coordinate', coordinate)
      }
      this.record('customExpression')
    },
    // selectType 坪效选中类型
    selectType(index) {
      if (index === 0) {
        return 'lge'
      } else if (index === 1) {
        return 'attendance_rate'
      } else if (index === 2) {
        return 'turnover_rate'
      }
    },
    // 坪效提交参数
    onAreaSubmit(data) {
      const cell = this.highlightList[0]
      if (data.activeIndex < 3) {
        this.$set(cell.content, 'aggType', CALC_FNS.LGE)
        this.$set(cell.content, 'extend', '1')
        this.$set(cell.content, 'columnType', 'number')
        this.$set(cell.content, 'text', data.moneyColumnName.toUpperCase())
        this.$set(cell.content, 'dataSetId', data.dataSetId)
        this.$set(cell.content, 'lgeType', this.selectType(data.activeIndex))
        this.$set(cell.content, 'activeIndex', data.activeIndex)
        this.$set(cell.content, 'lge', {
          moneyColumnName: data.moneyColumnName,
          moneyDataSetId: data.moneyDataSetId,
          lgeType: data.lgeType
        })
      }
      if (data.activeIndex === 3) {
        this.$set(cell.content, 'aggType', CALC_FNS.LGE)
        this.$delete(cell.content, 'lge')
        this.$delete(cell.content, 'curated_aggregation')
        this.$set(cell.content, 'extend', '1')
        this.$set(cell.content, 'lgeType', 'peer')
        this.$set(cell.content, 'columnType', 'number')
        this.$set(cell.content, 'text', data.peerSetMeasure.toUpperCase())
        this.$set(cell.content, 'dataSetId', data.dataSetId)
        this.$set(cell.content, 'activeIndex', data.activeIndex)
        // this.$set(cell, 'type', 'variable')
        this.$set(cell.content, 'peer', {
          peerDimensionName: data.peerSetDimension,
          peerMetricName: data.peerSetMeasure,
          datasetId: data.dataSetId,
          aggType: data.aggType
        })
      }
      if (data.activeIndex === 4) {
        const { text, activeIndex, ...item } = data
        this.$set(cell.content, 'aggType', CALC_FNS.LGE)
        // this.$delete(cell.content, 'lge')
        // this.$delete(cell.content, 'peer')
        this.$set(cell.content, 'extend', '1')
        this.$set(cell.content, 'dataSetId', data.dataSetId)
        this.$set(cell.content, 'lgeType', 'curated_aggregation')
        this.$set(cell.content, 'columnType', 'number')
        this.$set(cell.content, 'text', text)
        this.$set(cell.content, 'activeIndex', activeIndex)
        this.$set(cell.content, 'curatedAggregation', {
          ...item
        })
      }
      cell.content.keyName || this.$set(cell.content, 'keyName', generateKeyName())
      if (!cell.content.coordinate) {
        const coordinate = `${spreadsheetColumnLabel(cell.start[1])}${cell
          .start[0] + 1}`
        this.$set(cell.content, 'coordinate', coordinate)
      }
      this.record('onAreaSubmit')
    },
    // // 取消参数
    // cancelClose(value) {
    //   this.highlightList.forEach(cell => {
    //     if (!cell.content.keyName || !cell.content.lge) {
    //       this.selectedCalc = this.getCalcTypeLabelByValue(cell.content.aggType)
    //     } else {
    //       this.$set(cell.content, 'lgeType', this.selectType(value))
    //       this.calcClick('B-FX')
    //     }
    //   })
    //   this.addplateaucomputingDialogShow = false
    // },
    // 自定义公式弹窗 - 表单提交
    addCustomDialogSubmitHandler(data) {
      this.addCustomDialogVisible = false
      data.formulaName = data.formulaName.trim().replace(/\s+/g, ' ')
      this.customExpression(this.SUB_AGG_TYPE.CUSTOMER_EXPRESSION, data)
    },
    // 自定义公式输入框 - 点击事件
    customInputClickHandler() {
      if (!this.isCustomExpression || this.ranking) return
      this.addCustomDialogVisible = true
    },
    // 自定义公式输入框 - 清除事件
    customInputClearHandler() {
      this.isAddCustomFn = false
      this.$refs.customInputRef.focus()
    },
    // 获取计算公式单选按钮的标签信息
    getCalcTypeLabelByValue(value) {
      if (value === undefined) return undefined
      return this.calcTypeList.find(item => item.value === value).label
    },
    initCompareTypeList() {
      this.utils.api.get('/rule/quickCalendarConfig/getAuthConfig/Compare_Type/' + this.utils.tenantId)
        .then(res => {
          this.compareTypeList = res.map(item => {
            const { configName: label, configValue: value, isDefault } = item
            return { label, value, default: String(isDefault) === '1' }
          })
        })
        .catch(e => {
          console.log('获取比较方式列表接口发生错误:', e)
        })
    },

    updateDrillDataWhenAggTypeChanged(aggType, cell) {
      const drillData = this.tableDefaultConfig.drillData

      const validAggType = aggType === CALC_FNS.VALUE || aggType === CALC_FNS.GROUP
      if (validAggType) return

      const hasDrillOpt = validDrillTheCell(cell, drillData)
      if (!hasDrillOpt) return

      const { dataSetId, keyName } = cell.content
      this.$delete(drillData[dataSetId], keyName)
      Object.keys(drillData[dataSetId]).length || this.$delete(drillData, dataSetId)
    },

    getHierarchyOptList() {
      return [...this.getHierarchyOptListWithExtremumValue(), ...this.getHierarchyOptListWithMedianValue()]
    },
    getHierarchyOptListWithExtremumValue() {
      return HIERARCHY_EXPAND.EXTREMUM_VALUE_LIST.map(opt => {
        const label = this.$t(opt.label)
        Object.assign(opt, { label })
        return opt
      })
    },
    getHierarchyOptListWithMedianValue() {
      let label = ''
      let index = 0
      const optList = []

      for (index; index < HIERARCHY_EXPAND.MAX_EXPAND_NUMBER; index++) {
        const value = String(index + 1)

        label = this.$t('sdp.views.expandLayer')
        label = label.replace('$_Number', value)

        optList.push({ label, value })
      }

      return optList
    },
    changeHierarchy(hierarchyNumber) {
      this.tableDefaultConfig.hierarchy = hierarchyNumber
      this.record('changeHierarchy')
    },
    // 层级分类汇总
    reSetTreeGroupCoordinate() {
      const treeGroupCoordinate = this.tableDefaultConfig.treeGroupCoordinate

      if (treeGroupCoordinate.includes('Only Title')) return

      const isAll = treeGroupCoordinate.includes('ALL') || [...treeGroupCoordinate, 'Only Title', 'ALL'].length === this.treeTableGroupList.length

      if (isAll) {
        let cur = this.treeTableGroupList.filter(item => item !== 'Only Title')
        this.$set(this.tableDefaultConfig, 'treeGroupCoordinate', cur)
      }
    },
    // 去除
    excludeChange(val) {
      const cell = this.highlightList[0]
      if (cell) {
        // 去除是否有超链接
        if (!val) {
          this.eventBus.$emit('update-cell', cell, cell)
        }

        (val ? addExcludeCell : removeExcludeCell).call(this, cell.content)
        this.record('excludeChange')
      }
    },
  },

  mounted() {
    console.log(this.effectiveCellList)
    this.initCompareTypeList()
    this.updateHideOpts()

    this.eventBus.$on('clear-cells', () => {
      this.updateHideOpts()
    })
    this.eventBus.$on('delete-cols', () => {
      this.updateHideOpts()
    })
  }
}
</script>

<style lang="scss" scoped>
@import "../../theme/index";

// 解决 OMS 中图标颜色被篡改的问题
.is-disabled .icon-fx {
  color: #c0c4cc !important;
}

@mixin el-form-item__content($height: 28px) {
  .el-form-item__content {
    line-height: $height;

    .el-input-number {
      .el-input-number__increase,
      .el-input-number__decrease {
        height: $height / 2;
        line-height: $height / 2;
      }
    }

    .el-input {
      line-height: $height;
      height: $height;

      .el-input__inner {
        line-height: $height;
        height: $height;
      }
    }

    .el-select__caret {
      line-height: $height;
    }
  }
}

.sdp-grid-design-attribute-panel /deep/ {

  .el-switch {
    @include common-switch;
  }

  // 重置 ElInput 组件的 input 元素的左边距和对齐方式 ( ElInputNumber )
  .el-input__inner {
    padding-left: 8px !important;
    text-align: left !important;
  }

  .panel-content {
    height: 100%;
    overflow: hidden;

    .el-tabs__header {
      height: $sdp-grid-design-attribute-panel-header-height;
      margin-bottom: 12px;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__content {
      height: $sdp-grid-design-attribute-panel-content-height;

      .el-tab-pane {
        height: 100%;
      }

      .el-scrollbar {
        height: 100%;
        .el-scrollbar__wrap {
          overflow-x: hidden;
          padding: 0 12px 15px;
        }
      }
    }
    .panel-form{
      padding-bottom: 12px;
    }

    .el-form-item {
      padding: 0;
      margin-bottom: 0;

      .el-form-item__label {
        padding-bottom: 4px;
        font-size: 12px;
        line-height: 12px;
        font-weight: bold;
      }

      @include el-form-item__content;
    }

    .row--cell-size {
      .attr-cell-name.is-disabled {
        input {
          color: var(--sdp-ycsz-srtswz);
        }
      }

      .el-col:first-child {
        margin-bottom: 8px;
      }

      .el-col {
        @include el-form-item__content(24px);

        &:not(:first-child) {
          .el-input-number {
            width: 100%;
          }

          $disableColor: #C0C4CC;
          $activeColor: #525252;

          // w / h
          .el-input {
            input {
              color: $disableColor;
            }

            &:after {
              position: absolute;
              right: 6px;
              top: 0;
              font-size: 12px;
              color: $disableColor;
            }
          }

          .el-input:not(.is-disabled) {
            input {
              color: $activeColor;
            }

            // w / h
            &:after {
              color: $activeColor;
            }
          }

          // w / h -- disabled
          .is-sel-zero-cells .el-input {
            input,
            &:after {
              color: transparent;
            }
          }
        }

        &:nth-child(2) .el-input:after {
          content: 'w';
        }

        &:nth-child(3) .el-input:after {
          content: 'h';
        }

        // 增减按钮 默认隐藏
        .el-input-number__decrease,
        .el-input-number__increase {
          width: 15px;
          display: none;
        }

        // 增减按钮 鼠标悬浮时显示
        &:hover .el-input-number:not(.is-disabled) {
          .el-input-number__decrease,
          .el-input-number__increase {
            display: block;
          }
        }
      }
    }

    .row--line-limit-setting {
      margin-top: 8px;

      & .el-form-item {
        @include el-form-item__content(24px);
        .el-input__inner {
          height: 24px !important;
        }
      }

      .el-col {
        &:nth-child(3) {
          .el-input-number.is-disabled {
            > span {
              display: none;
            }
            input {
              color: transparent;
            }
          }

          .el-input:not(.is-disabled):after {
            position: absolute;
            right: 22px;
            top: 0;
            font-size: 12px;
            color: #777777;
          }

          .el-input-number .el-input:not(.is-disabled):after {
            content: 'Row';
          }

          .el-input-number.isZh .el-input:not(.is-disabled):after {
            content: '行';
          }

          .el-input-number__decrease,
          .el-input-number__increase {
            width: 15px;
          }
        }
      }
    }

    .min-auto-width {
      flex: 1;
      width: 90px;
      height: 24px;
      .el-input-number {
        width: 100%;
      }
      .el-input-number.is-controls-right .el-input__inner {
        padding-left: 10px;
        padding-right: 10px;
      }
      input {
        height: 24px !important;
        line-height: 24px !important;
      }
      .el-input {
        height: 24px !important;
        line-height: 24px !important;
        &:after {
          content: 'px';
          position: absolute;
          right: 5px;
          top: 0;
          font-size: 12px;
        }
      }

      // 增减按钮 默认隐藏
      .el-input-number__decrease,
      .el-input-number__increase {
        height: 12px !important;
        line-height: 12px !important;
        display: none;
      }

      // 增减按钮 鼠标悬浮时显示
      &:hover {
        .el-input-number__decrease,
        .el-input-number__increase {
          display: block;
        }
      }
    }

    .sdp-cell-lineHeight {
      .el-input.is-disabled {
        &:after {
          content: 'px' !important;
          color:var(--sdp-jys);
        }
      }
      .el-input:not(.is-disabled) {
        &:after {
          content: 'px' !important;;
          color:var(--sdp-xlk-jts);
        }
      }
    }
  }
}

.sdp-grid-design-attribute-panel-container {
  overflow-x: hidden;
  height: 100%;
}

.sdp-grid-design-attribute-panel {
  height: 100%;
  background: var(--sdp-szk-bjs);
  position: relative;

  .el-select {
    width: 100%;
  }
}

.panel-header {
  text-align: center;
  position: absolute;
  padding: 12px;
  background: #fff;
  width: 100%;
  z-index: 9;

  > .el-radio-group /deep/ .el-radio-button__inner {
    height: 24px;
    width: 94px;
    padding: 0;
    line-height: 24px;
    box-shadow: none;
    color: var(--sdp-ycsz-glwzs);
    background-color: var(--sdp-zs);
    border-color: var(--sdp-zs);
  }
}

.row--calc-formula {
  margin-top: 8px;

  .el-col {
    margin-bottom: 12px;
  }
}

// 超出...
.ellipsis-input /deep/ .el-input__inner {
  overflow: hidden;
  text-overflow: ellipsis;
}

// readonly 的字色
.readonly-input /deep/ .el-input__inner {
  color: #ccc;
}

// 水平分割线
hr {
  margin: 12px 0;
  background: var(--sdp-ycfgx);
  border: none;
  height: 1px;
}

.right{
  right: 0;
}

/deep/ {
  .form-switch-item,
  .form-item--cell-data-filter,
  .form-item--row-data-filter,
  .form-item--cell-warning,
  .form-item--hierarchical-style,
  .form-item--multilingual {
    position: relative;
    height: 12px;
    line-height: 12px;

    .el-form-item__label {
      padding: 0 !important;
    }

    .el-form-item__content {
      display: inline-block;
      position: absolute;
      right: 0;
      top: -8px;
      cursor: pointer;
    }
    .icon-sdp-info {
      color: var(--sdp-zs);
    }
  }
}
.form-item--serial-style{
  position: relative;
  height: 12px;
  line-height: 12px;

  /deep/ {
    .el-form-item__label {
      padding: 0 !important;
    }

    /deep/ .el-form-item__content {
      width: 100px;
      display: inline-flex;
      position: absolute;
      top: -8px;
      cursor: pointer;
    }
    .icon-sdp-info:hover {
      color: $color-main;
    }
  }
}

.form-switch-item {
  /deep/ .el-form-item__label {
    padding-top: 4px;
  }
}
.form-switch-item-exclude {
  /deep/ .el-form-item__label {
    padding-top: 4px;
  }
}

.form-item--multilingual /deep/ .el-form-item__content {
  top: -8px;
}

// 排名列和方式的上边距
.form-item--ranking-cell,
.form-item--ranking-way {
  margin-top: 12px;
}

/deep/.select-tree {
  position: absolute;
  width: 100%;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;

  &.select-tree-disabled {
    background-color: $input-disabled-background-color;

    .like-placeholder {
      color: $input-disabled-color !important;
    }
  }

  .like-placeholder {
    float: left;
    margin-left: 10px;
    color: #606266;
    font-size: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 80%;
  }
  .like-placeholders {
    float: left;
    margin-left: 10px;
    color: #ccc;
    font-size: 12px;
  }

  .like-input {
    float: right;
    margin-right: 8px;
    font-size: 14px;
    margin-top: 6px;
    color: #ccc;
    transition: transform .3s;
    transform: rotateZ(0);
  }
  .is-reverse{
    transform: rotateZ(180deg);
  }

  &:hover,
  &:active {
    border: 1px solid #333;
  }
}

.icon-sdp-hanshu {
  font-size: 24px;
  position: relative;
  top: 4px;
  left: 4px;
  cursor: pointer;

  &.disabled {
    color: #ccc;
  }
}
.select-tree-wrapper{
  /deep/ .el-select__tags{
    display: none;
  }
}
.cell-warning-icon {
  color: $color-main;
  font-size: 12px;
}
.data-filter-disabled {
  color: var(--sdp-jys);
  cursor: not-allowed !important;
}
</style>

<style lang="scss" scoped>
.sdp-grid-design-attribute-panel {
  .panel-header{
    background-color: var(--sdp-szk-bjs);

    /deep/.el-radio-button{
      .is-active {
        .el-radio-button__inner{
          color: var(--sdp-ycsz-glwzs);
          background-color: var(--sdp-zs);
          border-color: var(--sdp-zs);
        }
      }

      &:not(.is-active){
        .el-radio-button__inner{
          color: var(--sdp-zs);
          border-color: var(--sdp-zs);
          background-color: var(--sdp-tb-bj);
        }
      }
    }
  }
  .panel-content {
    /deep/.row--cell-size .el-col:not(:first-child) .el-input:not(.is-disabled) {
      input{
        color:var(--sdp-ycsz-glwzs);
      }
      &::after{
        color:var(--sdp-xlk-jts);
      }
    }
  }
  /deep/.select-tree-wrapper {
    .select-tree {
      border:  solid 1px var(--sdp-ycsz-srk-bcs);
      background-color: var(--sdp-ycsz-srk-bgs);
      .like-placeholders{
        color: var(--sdp-ycsz-srtswz);
      }
      .like-placeholder{
        color: var(--sdp-ycsz-glwzs);
      }
    }
  }
}

</style>
