/**
 * @Description: 单元格属性 - 单元格个数
 * <AUTHOR>
 * @date 2019/9/24
*/
import { FORMAT } from '../../../helpers/constants'
import { isCurrencyField_fnForGrid } from '../../../utils/cellTool'

export default {
  data() {
    // roc todo define constant
    const formatTypeList = [
      {
        label: this.$t('sdp.views.regular'),
        value: 'convention' // 常规
      },
      {
        label: this.$t('sdp.views.number'),
        value: 'numerical' // 数字
      },
      {
        label: this.$t('sdp.views.percentage'),
        value: 'percentage' // 百分比
      },
      {
        label: this.$t('sdp.views.currencyUnit'),
        value: FORMAT.CURRENCY_Unit // 货币单位简写
      }
    ]

    // roc todo: grayed && Fn?
    const formatData = {
      convention: 'normal',
      numerical: { decimalNum: '0' },
      percentage: { decimalNum: '0' }
    }

    /**
     * 获取精度下拉列表
     * @param accuracy {Number} 精度值
     * @returns {Array} 精度下拉列表
     */
    function getAccuracyTypeList(accuracy) {
      const list = []

      for (let i = 0; i <= accuracy; i++) {
        list.push({
          label: 0.0.toFixed(i),
          value: String(i)
        })
      }

      return list
    }

    return {
      // 默认单元格格式 - 常规格式
      selectedFormat: 'convention',

      // 单元格格式下拉列表
      formatTypeList,

      // 各类型格式的默认值
      formatData,

      // 小数位即精度
      selectedAccuracy: '0',

      // 精度下拉列表
      accuracyTypeList: getAccuracyTypeList(6),

      // highlightList 的首个单元格是否为货币字段
      isCurrencyField: false,
    }
  },

  computed: {
    formatIsConvention() {
      return this.selectedFormat === 'convention'
    },

    formatIsPercentage() {
      return this.selectedFormat === 'percentage'
    },

    formatIsCurrencyUnit() {
      return this.selectedFormat === FORMAT.CURRENCY_Unit
    },

    formatOptionDisabled() {
      return this.ranking || this.stringType
    },

    stringType() {
      if (this.highlightList[0]) {
        const { columnType, aggType } = this.highlightList[0].content
        return columnType === 'string' && aggType !== 'count' && aggType !== 'count_distinct'
      } else {
        return false
      }
    },

    grayedConflict() {
      const cellList = this.highlightList

      if (cellList.length === 1) {
        const grayed = this.$_getProp(cellList[0].content, 'cellFormat.value.grayed', false)
        return !((this.isCurrencyField && this.formatIsPercentage) || this.ranking || this.stringType) && grayed
      }

      return false
    },
  },

  methods: {
    // 修改格式
    changeFormatType(val, bool) {
      this.highlightList
        .forEach(cell => {
          // 常规 --> 删除 cellFormat 中除zeroFormat以外的字段
          // 非常规 --> 设置 cellFormat
          let cellFormat = cell.content.cellFormat || {}
          if (val === 'convention') {
            Object.keys(cellFormat).forEach(key => {
              if (!['zeroFormat', 'dateCustomFormat', 'timeCustomFormat'].includes(key)) this.$delete(cell.content.cellFormat, key)
            })
          } else {
            const decimalNum = this.formatIsCurrencyUnit ? '2' : '0'
            const format = {
              type: val,
              value: {},
              zeroFormat: cellFormat.zeroFormat,
              dateCustomFormat: cellFormat.dateCustomFormat || '',
              timeCustomFormat: cellFormat.timeCustomFormat || '',
            }
            Object.assign(format.value, this.formatData[val], { decimalNum })
            this.$set(cell.content, 'cellFormat', format)
          }

          // 数字格式的特殊处理
          // 暂无特殊处理...

          // 百分比格式的特殊处理
          if (val === 'percentage') {
            this.$nextTick(() => {
              // 精度下拉列表被禁用时, 设置 cell.content.cellFormat.value.grayed 为 true
              this.$refs.precisionSelectRef.disabled && this.$set(cell.content.cellFormat.value, 'grayed', true)
            })
          } else {
            cell.content?.cellFormat?.value && this.$delete(cell.content.cellFormat.value, 'grayed')
          }

          this.$nextTick(() => {
            // 精度下拉列表为禁用状态时, 删除精度字段, 添加置灰标识
            if (this.$refs.precisionSelectRef && this.$refs.precisionSelectRef.disabled) {
              this.$delete(cell.content.cellFormat.value, 'decimalNum')
              this.$set(cell.content.cellFormat.value, 'grayed', true)
            }
          })
        })
      if (bool) {
        this.record('changeFormatType')
      }
    },

    // 修改精度值
    changeAccuracyType(accuracy, bool) {
      this.highlightList.forEach(cell => {
        if (cell.content.cellFormat && !this.formatIsConvention) {
          this.$set(cell.content.cellFormat.value, 'decimalNum', accuracy)
          this.$delete(cell.content.cellFormat.value, 'grayed')
        }
      })
      if (bool) {
        this.record('changeAccuracyType')
      }
    },

    highlightListChangeHandler(highlightList) {
      this.isCurrencyField = highlightList.some(item => isCurrencyField_fnForGrid(item.content))
      const len = this.highlightList.length
      if (len === 1) {
        const cell = this.highlightList[0]
        const { content } = cell
        this.selectedFormat = content.cellFormat?.type
          ? content.cellFormat.type
          : 'convention'
        this.selectedAccuracy = content.cellFormat?.value
          ? content.cellFormat.value.decimalNum
          : '0'
      }
    }
  },

  watch: {
    highlightList: {
      handler(highlightList) {
        this.highlightListChangeHandler(highlightList)
      },
      deep: true
    },

    grayedConflict(bool) {
      if (bool) {
        if (this.isCurrencyField && this.formatIsCurrencyUnit && this.selectedAccuracy === undefined) {
          this.$set(this.highlightList[0].content.cellFormat.value, 'decimalNum', '2')
        } else {
          this.highlightList[0].content.cellFormat.value.grayed = false
        }
      }
    }
  }
}
