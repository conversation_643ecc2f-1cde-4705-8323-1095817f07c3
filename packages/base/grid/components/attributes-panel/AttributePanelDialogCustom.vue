<template>
  <el-dialog
    :custom-class="`sdp-dialog attribute-dialog ${getCurrentThemeClass()}`"
    class="grid-attr-custom"
    :class="themeType"
    :title="$t('sdp.views.customFn')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    append-to-body
    width="980px"
    @open="openHandler"
  >
    <div>
    <el-form
      label-position="top"
      :model="formData"
      ref="formRef"
      :rules="formRules"
      label-width="80px"
      size="small"
    >
      <div style="margin-top: 6px;">
        <!--  数据集下拉列表  -->
        <el-form-item prop="datasetId" :label="$t('sdp.views.dataSet')" style="display: inline-block; width: 300px" v-if="isShowDataSet && !onlyIndex">
          <ChartSelect
            v-model="formData.datasetId"
            :placeholder="$t('sdp.placeholder.plsInput')"
            clearable
            @change="datasetIdChangeHandler"
            style="width: 300px"
          >
            <el-option
              v-for="item in datasetOption"
              :key="item.id"
              :label="item.labeName"
              :title='item.labeName'
              :value="item.id"
            ></el-option>
          </ChartSelect>
        </el-form-item>

        <!--  名称  -->
        <el-form-item :label="$t('sdp.views.name')" style="display: inline-block; width: 608px; margin-left: 24px" v-if="isShowFormulaName">
          <el-input
            v-model="formData.formulaName"
            :placeholder="$t('sdp.placeholder.pleaseInputName')"
            clearable
          ></el-input>
        </el-form-item>
      </div>

      <!-- 返回值类型 -->
      <el-form-item :label="$t('sdp.views.expReturnType')" class="form-item--return_type" v-if="isShowReturnType">
        <el-radio-group v-model="formData.expReturnType">
          <el-radio label="number">{{ $t('sdp.views.numberCapitalizeFirstLetter') }}</el-radio>
          <el-radio label="string">{{ $t('sdp.views.stringCapitalizeFirstLetter') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 表达式编辑区域 -->
      <div style="margin: 24px 0;position: relative;">
        <div class="customFieldForm" v-if="!isChartCustom && !isWarning">
          <div style="margin-right: 12px">{{$t('sdp.views.customFn')}}</div>
          <el-radio-group v-model="formData.customFieldType">
            <el-radio label="metric">{{ $t('sdp.views.customMetric') }}</el-radio>
            <el-radio label="variable">{{ $t('sdp.views.customDimension') }}</el-radio>
          </el-radio-group>
        </div>
        <!-- 自定义公式 -->
        <div class="effectiveness" @click="checkSQL()" v-if="!isAssociationDataset" :style="{ top: (!isChartCustom && !isWarning) ? '0px' : '' }">
          <i class="icon icon-sdp-SQLxiaoyan"></i>
          {{$t('sdp.views.SQLCheck')}}
        </div>
        <el-form-item prop="expression" :label="(isChartCustom || isWarning) ? $t('sdp.views.customFn') : ''">
          <el-input
            type="textarea"
            resize="none"
            :rows="6"
            class="textarea-style"
            :placeholder="$t('sdp.placeholder.pls')"
            ref="expressionRef"
            v-model="formData.expression"
            @change="checkExpression"
          ></el-input>
        </el-form-item>
      </div>

      <!-- 选择区域 -->
      <div style="display: flex;flex-wrap: wrap;">
        <!-- 计算函数 -->
        <el-form-item prop="expression" style="display: inline-block; width: 180px;">
          <div slot="label" style="display: flex;align-items: center;height: 12px;">
            <span>{{$t('sdp.views.calFunc')}}</span>

            <el-tooltip
              effect="dark"
              :content="$t('sdp.tooltips.calcNotSupportFin')"
              placement="top"
              v-if="scour !== 'ResultingDatasetField' && !onlyIndex"
            >
              <div slot="content" style="width: 360px;">
                <p
                  v-for="(tip, i) in $t('sdp.tooltips.calcNotSupportFin').split(' \n ')"
                  :key="i"
                  :style="{ 'margin-top': i !== 0 ? '4px' : '0' }"
                >
                  {{ tip }}
                </p>
              </div>
              <i class="el-tooltip icon-sdp-info"/>
            </el-tooltip>
          </div>

          <calcOptions
            :list="scour === 'ResultingDatasetField' ? calcSmallSortList : computedCalcSortList"
            @onClick="updateExpression($event, true)"
          />

        </el-form-item>

        <!-- 计算字段 -->
        <el-form-item prop="expression" :label="$t('sdp.views.countField')" style="display: inline-block; width: 543px; margin: 0 37px 0 12px;">
          <calcOptions
            :list="calcFieldList"
            :isDataSet="true"
            @onClick="updateExpression"
          />
        </el-form-item>

        <!-- 计算方式 -->
        <el-form-item prop="expression" :label="$t('sdp.views.countSort')" style="display: inline-block; width: 160px;">
          <calcOptions
            :list="sizeofList"
            @onClick="updateExpression"
          />
        </el-form-item>

        <!-- 全局参数 -->
        <el-form-item prop="expression" style="display: inline-block; width: 100%;margin-top: 22px;" v-if="showGlobalParams && (!$getFeatureConfig || !$getFeatureConfig('globalParams.hidden'))">
          <div slot="label" style="display: flex;align-items: center">
            <span>{{$t('sdp.views.globalParam')}}</span>

            <el-tooltip
              effect="dark"
              :content="$t('sdp.tooltips.globalParameterExample')"
              placement="top"
            >
              <div slot="content">
                <p
                  v-for="(tip, i) in $t('sdp.tooltips.globalParameterExample').split(' \n ')"
                  :key="i"
                >{{ tip }}</p>
              </div>
              <i class="el-tooltip icon-sdp-info"/>
            </el-tooltip>
          </div>

          <calcOptions
            :list="globalParams"
            @onClick="updateExpression"
            @detailClick="globalParamDetailClick"
          />
        </el-form-item>
      </div>
    </el-form>
      <globalParamDetailDialog
        :visible.sync="globalParamDetailVisible"
        :id="globalParamDetailId"
      />
    </div>
    <div slot="footer">
      <el-button type="primary" @click="confirm">{{ $t('sdp.button.ensure') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('sdp.button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { CUSTOM_FNS, CUSTOM_FORMULA_LIST, SIZEOF_LIST, SUB_AGG_TYPE } from '../../helpers/constants'
import { validatePass } from '../../helpers/utils/utils'
import { translateExpression } from 'packages/base/board/displayPanel/utils'
import { STATIC_BASE_PATH } from 'packages/assets/constant'
import calcOptions from '../common/CalcOptions'
import GlobalParamDetailDialog from './globalParam/globalParamDetailDialog'
import ChartSelect from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/chart-select'
// 自定义公式返回值类型 string | number
const expReturnTypeDefaultValue = 'number'
// 自定义公式参数类型 metric | variable
const expFieldTypeDefaultValue = 'metric'

const defaultFormData = {
  datasetId: '',
  expression: '',
  formulaName: '',
  expReturnType: expReturnTypeDefaultValue,
  customFieldType: expFieldTypeDefaultValue, // 字段维度类型
}

export default {
  name: 'addCustomTextareaDialog',
  components: { GlobalParamDetailDialog, calcOptions, ChartSelect },
  inject:{
    utils: { default: () => ({}) },
    tenantData: { default: {} },
    aliasDict: { default: () => ({}) },
    getCurrentThemeClass: { default: () => () => '' },
    isCardSet: { default: false },
  },
  props: {
    isShowFormulaName: Boolean,
    isShowReturnType: Boolean,
    isShowDataSet: {
      type: Boolean,
      default: true,
    },
    showGlobalParams: {
      type: Boolean,
      default: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    datasetList: {
      type: Array,
      default: () => [],
    },
    highlightList: {
      type: Array,
      default: () => [],
      validator: list => Array.isArray(list)
    },
    isChartCustom: {
      type: Boolean,
      default: false,
    },
    isWarning: {
      type: Boolean,
      default: false,
    },
    isResultDataset: {
      type: Boolean,
      default: false,
    },
    warningForm: {
      type: Object,
      default: () => ({})
    },
    resultDatasetForm: {
      type: Object,
      default: () => ({}),
    },
    cascader: {
      type: Object,
      default: () => ({}),
    },
    // 卡片、图形当前字段使用数据集id
    datasetId: {
      type: String,
      default: ''
    },
    isAssociationDataset: {
      type: Boolean,
      default: false,
    },
    scour: {
      type: String,
      default: '',
    },
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex')
    },
    themeType() {
      return this.utils.themeParameters.themeType
    },
    api () {
      return this.utils.api || function () { }
    },
    textareaNode() {
      return this.$refs.expressionRef.$el.children[0]
    },
    cell() {
      return this.highlightList[0]
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    calcFieldList() {
      const datasetId = this.formData.datasetId
      let fieldList = []
      if (datasetId) {
        const curDataset = this.datasetList.find(dataset => dataset.id === this.formData.datasetId)
        if (!curDataset) return []
        fieldList = curDataset.children
        fieldList = fieldList.map(item => {
          return {
            ...item,
            label: item.labeName,
            value: item.labeName,
            comment: item.comment,
          }
        })
      }
      return fieldList
    },
    // 可选数据集
    datasetOption() {
      // TODO: 这里单独处理卡片关联数据集的逻辑
      if (this.isCardSet && this.isAssociationDataset) {
        return this.datasetId ? this.datasetList.filter(dataset => dataset.id === this.datasetId) : this.datasetList
      }
      return this.datasetId && !this.isAssociationDataset ? this.datasetList.filter(dataset => dataset.id === this.datasetId) : this.datasetList
    },
    computedCalcSortList() {
      const displayFns = this.$getFeatureConfig?.('customCalculation.fns')
      if (displayFns?.length) {
        return this.calcSortList.filter(item => displayFns.includes(item.value))
      }
      return this.calcSortList
    },
    checkSqlUrl() {
      return this.onlyIndex ? '/index/query/checkIndexExpr' : 'bi/check/customExpression'
    }
  },
  watch: {
    'formData.expression.length'() {
      this.SQLisTrue = false
    }
  },
  data() {
    return {
      calcField: '',
      calcFn: '',
      calcSizeof: '',
      formData: {},
      formRules: {
        datasetId: [
          { validator: validatePass, trigger: 'change' },
        ],
        expression: [
          { validator: validatePass, trigger: 'blur' },
        ],
      },
      calcSmallSortList: [
        {
          value: 'SUM',
          label: 'SUM',
          tooltips: 'sdp.tooltips.SUM'
        },
        {
          value: 'AVG',
          label: 'AVG',
          tooltips: 'sdp.tooltips.AVG'
        },
        {
          value: 'MAX',
          label: 'MAX',
          tooltips: 'sdp.tooltips.MAX'
        },
        {
          value: 'MIN',
          label: 'MIN',
          tooltips: 'sdp.tooltips.MIN'
        },
        {
          value: 'COUNT',
          label: 'COUNT',
          tooltips: 'sdp.tooltips.COUNT'
        }
      ],
      calcSortList: CUSTOM_FORMULA_LIST,
      sizeofList: SIZEOF_LIST,
      globalParams: [],
      globalParamDetailVisible: false,
      globalParamDetailId: '',
      SQLisTrue: false
    }
  },
  mounted() {
    this.initGlobalParam()
  },
  methods: {
    // Dialog 打开的回调
    openHandler() {
      this.$refs.formRef && this.$refs.formRef.resetFields()

      if (!this.globalParams.length) {
        this.initGlobalParam()
      }
      // 图形的初始化逻辑
      if (this.isChartCustom) {
        this.initChartCustom()
        return
      }

      // 预警
      if (this.isWarning) {
        this.initWarningCustom()
        return
      }
      // 结果数据集
      if (this.isResultDataset) {
        this.initResultDatasetCustom()
        return
      }
      const content = this.cell.content
      const { subAggType, dataSetId, customFnData } = content
      if (subAggType === SUB_AGG_TYPE.CUSTOMER_EXPRESSION) {
        // 修改逻辑
        const {
          datasetId = '',
          displayExpression = '',
          expression = '',
          formulaName = '',
          expReturnType = expReturnTypeDefaultValue,
          customFieldType = expFieldTypeDefaultValue,
        } = customFnData
        this.formData = {
          datasetId,
          expression,
          formulaName,
          expReturnType,
          customFieldType,
        }
        displayExpression && (this.formData.expression = displayExpression)
      } else if (!subAggType && dataSetId) {
        // 转换逻辑
        const { columnType, text, aggType } = content

        let type
        if (Object.values(CUSTOM_FNS).includes(aggType)) {
          type = aggType
        } else if (columnType === 'number') {
          type = CUSTOM_FNS.SUM
        } else {
          type = CUSTOM_FNS.COUNT
        }

        const expression = `${type}(${text})`
        this.formData = {
          datasetId: dataSetId,
          expression,
          formulaName: '',
          expReturnType: expReturnTypeDefaultValue,
          customFieldType: expFieldTypeDefaultValue,
        }
      } else {
        // 新建逻辑
        this.formData = Object.assign({}, defaultFormData)
      }
      this.formData.expression = this.updateExpressionFieldAlias()
    },
    // 更新表达式，替换别名或本名
    updateExpressionFieldAlias(expression = this.formData.expression, isAlias = false) {
      return this.aliasDict?.updateExpressionFieldAlias(expression, this.formData.datasetId, isAlias) || expression
      // if (!expression) return expression
      // let exp = expression
      // const fieldList = this.aliasDict?.getFieldList(this.formData.datasetId, isAlias) || []
      // const dict = this.aliasDict?.dict[this.formData.datasetId]?.currDict || {}
      // const fieldDict = fieldList.reduce((pre, cur) => {
      //   pre[cur] = dict[cur]
      //   return pre
      // }, {})
      //
      // exp = translateExpression(exp, fieldDict)
      // // fieldList.forEach(field => {
      // //   const reg = new RegExp(`${field}`, 'ig')
      // //   exp = exp.replace(reg, this.aliasDict?.getUnknownName(this.formData.datasetId, field) || field)
      // // })
      //
      // console.log('%c%s', 'background: green;color:#FFF;', exp)
      // return exp
    },
    // 全局参数 修改显示值
    compatibleGlobalParam(expression) {
      let exp = expression
      this.globalParams.forEach(item => {
        // eslint-disable-next-line no-useless-escape
        const reg = new RegExp(`'param\\\{${item.code}\\\}'`, 'ig')
        exp = exp.replace(reg, `'param{${item.level === '1' ? '' : 't_'}${item.label}}'`)
      })
      return exp
    },
    // 判断表达式是否跨数据集
    checkCrossExpression() {
      if (!this.isAssociationDataset) return []
      const curDataset = this.datasetList.find(dataset => dataset.id === this.formData.datasetId)
      if (curDataset) {
        let expressionDatasetId = []
        curDataset.children.forEach(item => {
          if (this.formData.expression.includes(item.labeName) && item.dataSetId) {
            expressionDatasetId.push(item.dataSetId)
          }
        })
        return expressionDatasetId
      }
      return []
    },
    initGlobalParam() {
      const globalParameterList = this.tenantData?.globalParameterList || []
      this.globalParams = globalParameterList.map((e, index) => {
        return {
          value: `'param{${e.level === '1' ? '' : 't_'}${e.name}}'`,
          code: e.id,
          label: e.name,
          level: e.level,
          detail: this.$t('sdp.views.pDetail'),
          detailHover: true,
        }
      })
    },
    checkSQL(isShowMessage = true) {
      if (!this.formData?.datasetId) {
        this.$message.warning(this.$t('sdp.views.pleaseDataSet'))
        return
      }

      let sql = this.formData.expression || ''
      let globalParamSql = this.getGlobalParamChangeValue(this.formData.expression)
      sql = this.updateExpressionFieldAlias(globalParamSql, true)
      let params = {
        'datasetId': this.formData.datasetId,
        'sql': sql
      }
      if (this.onlyIndex) {
        params = {
          dataSetId: this.formData.datasetId,
          exp: sql
        }
      }
      this.api.post(this.checkSqlUrl, params).then((res) => {
        this.SQLisTrue = this.onlyIndex ? res.data : res
        if (this.SQLisTrue && isShowMessage) {
          this.$message.success(this.$t('sdp.views.SQLStatement'))
        }
      }).catch((err) => {
        console.log(err)
      })
    },
    async customExpression(expressionDatasetId) {
      if (!this.formData?.datasetId) {
        this.$message.warning(this.$t('sdp.views.pleaseDataSet'))
        return false
      }

      let sql = this.formData.expression || ''
      let globalParamSql = this.getGlobalParamChangeValue(this.formData.expression)
      sql = this.updateExpressionFieldAlias(globalParamSql, true)

      let params = {
        'datasetId': this.formData.datasetId,
        'sql': sql
      }
      if (this.onlyIndex) {
        params = {
          dataSetId: this.formData.datasetId,
          exp: sql
        }
      }
      const res = await this.api.post(this.checkSqlUrl, params)
      return this.onlyIndex ? res.data : res
    },
    // 表单提交
    async confirm() {
      if (!this.isAssociationDataset && !this.SQLisTrue) {
        let check = await this.customExpression()
        if (!check) return
      }
      const formRef = this.$refs.formRef
      formRef.validate(valid => {
        if (!valid) return
        const expressionDatasetId = this.checkCrossExpression()
        this.formData.displayExpression = this.updateExpressionFieldAlias(this.formData.expression, true)
        this.formData.expression = this.getGlobalParamChangeValue(this.formData.expression)
        this.formData.expression = this.updateExpressionFieldAlias(this.formData.expression, true)
        this.$emit('submit', this.formData, expressionDatasetId.length > 1)
      })
    },
    // 数据集ID变化事件处理器
    datasetIdChangeHandler() {
      this.formData.expression = ''
    },
    // 获取计算字段类型 - 通过标签名称
    getColumnTpeByLabelName(labelName) {
      let type
      let count = 0
      const list = this.calcFieldList
      const length = list.length
      while (count < length) {
        const item = list[count]
        const name = item.labeName
        if (name === labelName) {
          type = item.columnTpe
          break
        }
        count++
      }
      return type
    },
    // 获取计算方式的默认值 - 通过计算字段类型
    getCalcFnByType(type) {
      let calcFn = ''
      if (type === 'number') {
        calcFn = CUSTOM_FNS.SUM
      } else if (type === 'string') {
        calcFn = CUSTOM_FNS.COUNT
      }
      return calcFn
    },

    updateExpression(value, isCalcFn) {
      const expression = this.formData.expression || ''

      const textareaNode = this.$refs.expressionRef.$el.children[0]
      const { selectionStart, selectionEnd } = textareaNode

      /// /////////////////////////////////////////////////////
      // 适配 Edge 浏览器 //////////////////////////////////////
      const start = Math.min(selectionStart, selectionEnd) // /
      const end = Math.max(selectionStart, selectionEnd) /// //
      /// ///////////////////////////////////////////////////

      const valueLength = value.length
      const lText = expression.slice(0, start)
      const rText = expression.slice(end)

      let currentIndex = start + valueLength
      let insertText = value

      const expressionLength = expression.length
      const selectionLength = end - start
      const cursorIndex = this.getCursorPosition(this.textareaNode)

      // 计算函数逻辑
      if (isCalcFn && (cursorIndex === expressionLength || selectionLength === expressionLength)) {
        currentIndex += 1
        insertText += '()'
      }

      this.formData.expression = lText + insertText + rText
      this.$nextTick(() => this.setCaretPosition(this.textareaNode, currentIndex))
    },
    // 设置光标位置
    setCaretPosition(node, index) {
      if (node.setSelectionRange) {
        node.focus()
        node.setSelectionRange(index, index)
      } else if (node.createTextRange) {
        var range = node.createTextRange()
        range.collapse(true)
        range.moveEnd('character', index)
        range.moveStart('character', index)
        range.select()
      }
    },
    // 获取光标位置
    getCursorPosition(node) {
      let CaretPos = 0

      if (document.selection) {
        // IE Support
        node.focus()
        const Sel = document.selection.createRange()
        Sel.moveStart('character', -node.value.length)
        CaretPos = Sel.text.length
      } else if (node.selectionStart || node.selectionStart === 0) {
        // Firefox` support
        CaretPos = node.selectionStart
      }
      return (CaretPos)
    },

    initWarningCustom() {
      const datasetId = this.datasetId || this.datasetList[0].id
      const expression = this.warningForm?.customExpressionSet?.expression || ''
      const returnType = this.warningForm?.customExpressionSet?.returnType || expReturnTypeDefaultValue
      const name = this.warningForm?.customExpressionSet?.name || ''
      this.formData = Object.assign({}, defaultFormData, {
        datasetId,
        expression,
        expReturnType: returnType,
        formulaName: name,
      })
      this.formData.expression = this.updateExpressionFieldAlias()
    },
    initResultDatasetCustom() {
      const datasetId = this.datasetId || this.datasetList[0].id
      const customFieldType = this.resultDatasetForm?.customExpressionSet?.customFieldType || expFieldTypeDefaultValue
      const expression = this.resultDatasetForm?.customExpressionSet?.expression || ''
      const expReturnType = this.resultDatasetForm?.customExpressionSet?.expReturnType || expReturnTypeDefaultValue
      this.formData = Object.assign({}, defaultFormData, {
        datasetId,
        customFieldType,
        expression: expression,
        expReturnType: expReturnType,
      })
      this.formData.expression = this.updateExpressionFieldAlias()
    },

    initChartCustom() {
      const { columnTpe, alias, aggType, labeName, exp, expression: _expression, parentId, _rawLabeName = '' } = this.cascader
      // TODO: 这里顺便做下卡片的兼容，之后再重新单独处理卡片的逻辑
      let datasetId = ''
      if (this.isCardSet) {
        datasetId = this.datasetId || this.datasetList[0].id
      } else {
        datasetId = this.isAssociationDataset ? this.datasetList[0].id : this.datasetId || this.datasetList[0].id
      }

      let expression = exp
      if (!exp && !_expression) {
        let type
        if (columnTpe === 'string') {
          type = CUSTOM_FNS.COUNT
        } else {
          type = CUSTOM_FNS.SUM
        }
        expression = `${type}(${_rawLabeName || labeName})`
      } else if (_expression && this.scour !== 'ResultingDatasetField') {
        // 老看板兼容
        const [a, b] = _expression
        expression = `${a.aggType}(${a.columnName})${a.operator}${b.aggType}(${b.columnName})`
      } else if (aggType === 'CUSTOM' && this.scour !== 'ResultingDatasetField') {  // 如果是新增时候自定义计算的结果数据集，这里置空
        expression = ``
      }
      const displayExpression = this.compatibleGlobalParam(expression)
      this.formData = Object.assign({}, defaultFormData, {
        datasetId,
        expression: displayExpression || expression,
      })
      this.formData.expression = this.updateExpressionFieldAlias()
    },
    // 表达式非空校验
    checkExpression(val) {
      if (!/\S/g.test(val)) {
        this.formData.expression = ''
      }
    },
    // 全局参数详情
    globalParamDetailClick(value, item) {
      this.globalParamDetailVisible = true
      this.globalParamDetailId = item.code
    },
    getGlobalParamChangeValue(displayValue) {
      let expression = displayValue
      this.globalParams.forEach(item => {
        const reg = new RegExp(`('param{${item.level === '1' ? '' : 't_'}${item.label}}')`, 'ig')
        expression = expression.replace(reg, `'param{${item.code}}'`)
      })
      return expression
    },
  },
}
</script>

<style lang="scss" scoped>
  .icon-sdp-SQLxiaoyan {
    font-size: 14px;
  }
  .customFieldForm {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    height: 24px;
    line-height: 24px;
  }
  .effectiveness {
    width: 94px;
    height: 24px;
    border: 1px solid var(--sdp-zs);
    line-height: 24px;
    padding-left: 8px;
    // padding-right: 8px;
    // background-color: white;
    color: var(--sdp-zs);
    margin-bottom: 8px;
    position: absolute;
    right: 0;
    top: -12px;
    font-size: 12px;
    cursor: pointer;
  }
  .textarea-style {
    height: 180px;
    /deep/ .el-textarea__inner {
      height: 180px;
    }
  }
  .el-textarea /deep/ textarea {
    font-family: sans-serif;
  }

  .form-item--return_type {
    margin-top: 24px !important;

    /deep/ .el-form-item__label {
      float: left !important;
      padding-bottom: 0 !important;
      padding-right: 12px !important;
    }

    /deep/ .el-radio__label {
      font-size: 12px;
      /*color: #222222;*/
      padding-left: 10px;
    }

    .el-radio:not(:last-child) {
      margin-right: 32px;
    }
  }

  /deep/ .attribute-dialog{
    margin-top: 5vh !important;
  }
  .grid-attr-custom {
    /deep/ .el-form-item__label {
      padding-bottom: 8px;
      font-size: 12px;
      color: #333333;
      line-height: 1;
    }

    /deep/ .el-form-item__content {
      line-height: 1;
    }

    .el-form-item {
      margin: 0;
    }
  }

  .icon-sdp-info {
    color: var(--sdp-zs);
    font-size: 20px;
    cursor: pointer;
    transition: color 150ms;
    vertical-align: text-bottom;
  }
</style>
