<template>
  <div>
    <el-form-item :label="$t('sdp.views.timeDomain')" style="margin-top: 12px;">
      <el-select
        :disabled="timeDisabled"
        v-model="timeValue"
        :popper-class="getCurrentThemeClass()"
        style="width: 100%"
        @change="updateCompareRuleByTimeValueChange"
      >
        <el-option
          v-for="calc in timeList"
          :key="calc.value"
          :label="calc.label"
          :value="calc.value"
          @click.native="timeChange(calc.value)"
        ></el-option>
      </el-select>
    </el-form-item>

    <!-- 比较方式 - 计算域 -->
    <el-form-item
      :label="$t('sdp.views.compareType')"
      style="margin-top: 12px;"
      v-if="compareRuleByTimeArea"
    >
      <el-select
        v-model="compareRuleByTimeArea"
        :disabled="timeDisabled"
        style="width: 100%"
        :popper-class="getCurrentThemeClass()"
        value
        @change="compareTypeChange"
      >
        <el-option
          v-for=" ({ label, value }) in compareTypeList"
          :key="value"
          :label="label"
          :value="value"
        ></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>

import { isNumberCellForFeature } from '../../../utils'
import { recordMixin } from '../../../helpers/mixins'
import { getTimeValue, fliterhighlightList, filterStackAndRank, timeDisabledValid, isActiveCompareRuleByTimeArea, isVariableCell, fliterhighlightListDimension } from './timeAreaHelper'
export default {
  inject: ['getCurrentThemeClass'],
  props: {
    compareTypeList: {
      type: Array,
      default() {
        return []
      }
    },
    highlightList: [Array],
  },
  mixins: [recordMixin],
  data() {
    return {
      timeValue: '',
      compareRuleByTimeArea: '',
      timeList: [
        {
          // label: '无',
          label: this.$t('sdp.views.none'),
          value: ''
        },
        {
          // label: '本期',
          label: `${this.$t('sdp.views.currentPeriod')}`,
          value: 'cur_period'
        },
        {
          // label: '上期',
          label: `${this.$t('sdp.views.priorPeriods')}`,
          value: 'pre_period'
        },
        {
          // label: '日期上期扩展',
          label: `${this.$t('sdp.views.PriorPeriodExpand')}`,
          value: 'period_expand_prevyear'
        },
        {
          // label: '增长值',
          label: `${this.$t('sdp.views.date')}${this.$t('sdp.views.growthValue')}`,
          value: 'growth_value'
        },
        {
          // label: '增长率',
          label: `${this.$t('sdp.views.date')}${this.$t('sdp.views.growthRate')}`,
          value: 'growth_rate'
        },
        {
          // label: '天（财务天）',
          label: this.$t('sdp.views.FinancialDay'),
          value: 'cur_day'
        },
        {
          // label: '周（财务周）',
          label: this.$t('sdp.views.FinancialWeek'),
          value: 'cur_week'
        },
        {
          // label: '月（财务月）',
          label: this.$t('sdp.views.FinancialCycle'),
          value: 'cur_month'
        },
        {
          // label: '季度（财务季度）',
          label: this.$t('sdp.views.FinancialQuarter'),
          value: 'cur_quarter'
        },
        {
          // label: '年（财务年）',
          label: this.$t('sdp.views.FinancialYear'),
          value: 'cur_year'
        },
        {
          // label: '本期',
          label: `Location ${this.$t('sdp.views.current')}`,
          value: 'cur_location'
        },
        {
          // label: '上期',
          label: `Location ${this.$t('sdp.views.prior')}`,
          value: 'pre_location'
        },
        {
          // label: '增长值',
          label: `Location ${this.$t('sdp.views.growthValue')}`,
          value: 'location_growth_value'
        },
        {
          // label: '增长率',
          label: `Location ${this.$t('sdp.views.growthRate')}`,
          value: 'location_growth_rate'
        },
        {
          // label: '百分比',
          label: `${this.$t('sdp.views.rulePercentage')}`,
          value: 'rule_percentage'
        },
        {
          // label: '规则当前值',
          label: `${this.$t('sdp.views.ruleCurrentValue')}`,
          value: 'rule_current_value'
        },
        {
          // label: 'Time组件维度切换',
          label: this.$t('sdp.views.timeDimensionSwitch'),
          value: 'time_dimension_switch'
        },
        {
          // label: 'Trends组件维度切换',
          label: this.$t('sdp.views.trendsDimensionSwitch'),
          value: 'trends_dimension_switch'
        }
      ],
    }
  },
  computed: {
    listLength() {
      return this.highlightList.length || 0
    },
    compareTypeDefaultVal() {
      return this.compareTypeList.find(item => item.default).value
    },
    timeDisabled() {
      const highlightList = this.highlightList
      const fnList = [
        () => true,
        () => !filterStackAndRank(highlightList[0]) || timeDisabledValid(highlightList[0]),
        () => (fliterhighlightList(highlightList).length === 0 || highlightList.some((v) => {
          return timeDisabledValid(v) || !isNumberCellForFeature(v)
        }))
      ]
      return (fnList[this.listLength] || fnList[2])()
    },
  },
  watch: {
    highlightList(n) {
      this.timeDomainUpdate(n)
    }
  },
  methods: {
    // 时间域
    timeChange(val) {
      // 满足数组里的添加locationArea属性
      const arr = ['cur_location', 'pre_location', 'location_growth_value', 'location_growth_rate']
      const name = arr.includes(val) ? 'locationArea' : 'timeArea'
      const oldName = !arr.includes(val) ? 'locationArea' : 'timeArea'
      this.highlightList.forEach(cell => {
        const flag = this.listLength === 1 ? true : isNumberCellForFeature(cell)
        if (isVariableCell(cell) && filterStackAndRank(cell) && flag) {
          if (val) {
            this.$set(cell.content, name, val)
            this.$delete(cell.content, oldName)
            // if (val === 'period_expand_prevyear') {
            //   this.$set(cell.content, 'extend', '2')
            // }
          } else {
            this.$delete(cell.content, 'locationArea')
            this.$delete(cell.content, 'timeArea')
          }
        }
      })
      this.record(name)
    },
    // 比较方式发生变化
    compareTypeChange(v) {
      this.store.isActiveFormatBrush = ''
      // 更新单元格比较方式的值
      if (this.listLength === 1) {
        this.$set(this.highlightList[0].content, 'compareRule', v)
        return
      }
      if (this.listLength > 1) {
        this.highlightList.forEach(cell => {
          if (filterStackAndRank(cell)) {
            this.$set(cell.content, 'compareRule', v)
          }
        })
      }
    },
    timeDomainUpdate(highlightList) { // 时间域初始化
      const len = highlightList.length
      if (this.listLength === 0) {
        this.timeValue = ''
        return
      }

      if (this.listLength === 1) {
        this.timeValue = getTimeValue(highlightList[0])
        // 更新计算域的比较类型
        this.compareRuleByTimeArea = this.getCompareRuleByTimeArea(highlightList[0])
        return
      }

      const filterResult = fliterhighlightListDimension(highlightList)
      const highlightHaveSameTimeValue = filterResult.length > 0 ? filterResult.every((currentValue) => getTimeValue(currentValue) === getTimeValue(filterResult[0])) : false
      this.timeValue = highlightHaveSameTimeValue && !this.timeDisabled ? getTimeValue(filterResult[0]) : ''

      // 更新计算域的比较类型
      if (filterResult.length > 0 && this.timeValue && !this.timeDisabled) {
        const initValue = this.getCompareRuleByTimeArea(filterResult[0])
        const compareRuleByTimeAreaFlag = filterResult.every(cell => this.getCompareRuleByTimeArea(cell) === initValue)
        const compareRuleByTimeAreaDefaultValue = filterResult.every(({ content: { timeArea = '' } }) => isActiveCompareRuleByTimeArea(timeArea))
        this.compareRuleByTimeArea = compareRuleByTimeAreaFlag ? initValue : (compareRuleByTimeAreaDefaultValue ? this.compareTypeDefaultVal : '')
      } else {
        this.compareRuleByTimeArea = ''
      }
    },
    updateCompareRuleByTimeValueChange(v) {
      this.store.isActiveFormatBrush = ''
      const isActiveCompareRuleByTimeAreaBoolen = isActiveCompareRuleByTimeArea(v)
      const invalid = Boolean(isActiveCompareRuleByTimeAreaBoolen && this.compareRuleByTimeArea)
      if (invalid) return

      // 显示比较方式时设置数据, 隐藏比较方式时清除数据
      this.compareRuleByTimeArea = isActiveCompareRuleByTimeAreaBoolen ? this.compareTypeDefaultVal : ''
      this.highlightList.forEach(cell => {
        if (filterStackAndRank(cell)) {
          const { content: cellData } = cell
          this.compareRuleByTimeArea ? this.$set(cellData, 'compareRule') : this.$delete(cellData, 'compareRule')
        }
      })
    },
    getCompareRuleByTimeArea({ content = { timeArea: '', compareRule: '' } }) {
      return content.timeArea ? content.compareRule || (isActiveCompareRuleByTimeArea(content.timeArea) ? this.compareTypeDefaultVal : '') : ''
    },
    updateTimeArea() {
      this.timeDomainInit(this.highlightList)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
