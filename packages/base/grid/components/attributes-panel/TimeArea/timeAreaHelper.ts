import { CELL_TYPE } from 'packages/base/gridPreview/common/constant'
import { SUB_AGG_TYPE } from '../../../helpers/constants'
import { getCellType } from 'packages/base/gridPreview/common/js/utils'
import { isStringCellForFeature } from '../../../utils'

// 获取文本域的值
export const getTimeValue = ({ content }) => content.timeArea || content.locationArea || ''

export const filterStackAndRank = ({ content }) => !content.isStack && !content.rankType
// 过滤掉设置了排名的 和堆叠累加的高亮单元格
export const fliterhighlightList = (highlightList) => highlightList.filter(filterStackAndRank)

// 过滤掉 设置了排名的 和堆叠累加以及维度单元格的高亮单元格
export const fliterhighlightListDimension = (highlightList) => highlightList.filter(v => filterStackAndRank(v) || isStringCellForFeature(v))
// 时间域disable单元格
export const timeDisabledValid = (cell) => {
  const { content } = cell
  return (!!content.compareShowNameType || !(content.timeArea || getCellType(cell) === CELL_TYPE.variable) || (content.isExpression && !content.subAggType) || content.subAggType === SUB_AGG_TYPE.EXPRESSION)
}
//
export const isVariableCell = (cell) => getCellType(cell) === CELL_TYPE.variable
//
export const isActiveCompareRuleByTimeArea = (timeArea) => {
  const conditionList = [ 'pre_period', 'growth_value', 'growth_rate' ] // 日期上期, 日期增长值, 日期增长率
  return conditionList.includes(timeArea)
}
