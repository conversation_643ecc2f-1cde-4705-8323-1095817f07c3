<template>
  <el-dialog
    :custom-class="`${getCurrentThemeClass()} sdp-dialog sdp-row-col-hide-dialog`"
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    append-to-body
    width="330px"
    @closed="closedHandler"
    @open="openHandler"
    @opened="initData"
  >
    <div class="hide-content">
      <el-table :data="list" :max-height="`400px`">
        <!-- 序号 -->
        <el-table-column :label="tableHeader" prop="label"/>

        <!-- 隐藏 -->
        <el-table-column :label="title">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.selected"/>
          </template>
        </el-table-column>
      </el-table>

      <div class="tips">
        {{ tips }}
      </div>
      <div class="tips" v-if="tips2" style="margin-top: 8px;">
        {{ tips2 }}
      </div>
    </div>

    <div slot="footer">
      <el-button type="sdp-ensure" @click="confirm">
        {{$t('sdp.button.ensure')}}
      </el-button>

      <el-button type="sdp-cancel" @click="close">
        {{$t('sdp.button.cancel')}}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import {spreadsheetColumnLabel} from "packages/base/grid/helpers/utils/grid";

export default {
  name: 'rowColumnHide',
  inject: {
    utils: { default: {} },
    getCurrentThemeClass: { default: () => () => '' },
    activeInject: { default: null },
  },
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isColumn: {
      type: Boolean,
      default: false
    },
    cellList: {
      type: Array,
      default: () => ([])
    },
    tableDefaultConfig: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      list: [],
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    title() {
      return this.isColumn ? this.$t('sdp.views.HideEmptyColumns') : this.$t('sdp.views.HideEmptyRows')
    },
    tableHeader() {
      return this.isColumn ? this.$t('sdp.views.Columns') : this.$t('sdp.views.Rows')
    },
    tips() {
      return this.isColumn ? this.$t('sdp.views.HideEmptyColumnTips') : this.$t('sdp.views.HideEmptyRowsTips')
    },
    tips2() {
      return this.isColumn ? this.$t('sdp.views.HideEmptyColumnTips2') : ''
    },
  },
  methods: {
    openHandler() {
      this.initData()
    },
    initData() {
      // 获取初始值
      const { column, row } = this.getRowAndColumnList()
      const targetList = this.isColumn ? column : row
      const list = targetList.map(e => {
        return {
          label: this.isColumn ? spreadsheetColumnLabel(e) : e + 1,
          value: e,
          selected: false,
        }
      })
      // 设置值
      const { hideEmptyColumns = [], hideEmptyRows = [] } = this.tableDefaultConfig
      const selectList = this.isColumn ? hideEmptyColumns : hideEmptyRows
      list.forEach(e => { e.selected = selectList.includes(e.value) })
      // 兼容
      if (this.isColumn && this.tableDefaultConfig.isHideEmptyColumns) {
        list.forEach(e => { e.selected = true })
      }

      this.$set(this, 'list', list.sort((a, b) => a.value - b.value))
    },
    closedHandler() {
    },
    // 初始化行列数据 合并单元格以第一个算
    getRowAndColumnList() {
      const row = []
      const column = []

      this.cellList.forEach(cell => {
        const { start } = cell
        if (!row.includes(start[0])) {
          row.push(cell.start[0])
        }
        if (!column.includes(start[1])) {
          column.push(cell.start[1])
        }
      })

      return { row, column }
    },
    confirm() {
      const valueList = this.list.filter(e => e.selected).map(e => e.value)
      console.log('valueList', valueList)
      const target = this.isColumn ? 'hideEmptyColumns' : 'hideEmptyRows'
      this.$set(this.tableDefaultConfig, target, valueList)

      if (this.isColumn && this.tableDefaultConfig.isHideEmptyColumns) {
        this.$delete(this.tableDefaultConfig, 'isHideEmptyColumns')
      }
      this.close()
    },
    close() {
      this.dialogVisible = false
    },
  }
}
</script>

<style scoped lang="scss">
.hide-content {
  max-height: 500px;
  overflow-y: auto;

  /deep/ {
    .el-dialog__body {
      padding-top: 14px;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: transparent;
    }

    .cell {
      text-align: center;
      font-size: 12px;
    }

    .el-table__header {
      margin-top: 12px;

      .cell {
        color: var(--sdp-color-SLCXZWZ);
        font-weight: normal;
      }
    }

    .el-table__body {
      .cell {
        color: var(--sdp-color-SLCXZWZ);
      }
    }
  }

  .tips {
    margin-top: 16px;
    color: var(--sdp-color-KXMRZT, #666);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
  }
}

</style>

<style lang="scss">
.tip-set-dialog {
  height: calc(100% - 30vh);

  .el-dialog__body{
    height: calc(100% - 54px);
    padding-bottom: 0px !important;
  }
}
</style>
