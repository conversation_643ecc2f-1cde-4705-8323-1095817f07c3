// 粘贴系统剪切板
import { globalTools } from 'packages/assets/utils/globalTools'
import { defaultCellContent } from 'packages/base/grid/helpers/utils/tableOriginalData'
import { CalcDeviation } from 'packages/base/grid/helpers/utils/utils'
import { generateKeyName } from 'packages/base/gridPreview/common/js/utils'

export const sysPasteData = async (eve) => {
  let clipboardData = (eve.clipboardData || window.clipboardData)
  let text = clipboardData ? clipboardData.getData('text') : await navigator.clipboard.readText()
  if (text.trim()) {
    const textRow = text.split(/\n/).filter(v => v)
    const positionCells = setPosition(textRow)
    const cellsFlatten = globalTools.$_flatten(positionCells)
    return { pasteArray: cellsFlatten, text }
  }
  return { pasteArray: undefined }
}

export const sysWriteText = async function() {
  const copyKeyName = `sdp${generateKeyName()}`
  await navigator.clipboard.writeText(copyKeyName)
  this.syscopyUUid = copyKeyName
}

const setPosition = (textRow = []) => {
  return textRow.map((item, index) => {
    const list = item.split(/\t/)
    return list.map((text, i) => {
      const position = [index, i]
      return {
        content:
        {
          ...combineCell(),
          text
        },
        start: [...position],
        end: [...position]
      }
    })
  })
}
const combineCell = () => {
  return globalTools.$_deepClone(defaultCellContent)
}

const handSysPaste = function(result = false) {
  if (result) {
    const startCell = this.highlightList[0]
    const deviation = CalcDeviation(startCell, result[0])
    this.operateIsPaste = true
    const formatterList = this.calcDeviation(result, deviation)
    this.updateTableCell(formatterList)
    this.updateHighLight() // 清空更新高亮单元格
    setTimeout(() => {
      this.operateIsPaste = false
    })
    this.record('paste')
  }
}
export default handSysPaste
