
import { isSameCol, isSameRow, CalcDeviation, adjustSameCell } from 'packages/base/grid/helpers/utils/utils'
import { OPERATE_MODAL_TYPES } from 'packages/base/grid/helpers/constants/index'
import { generateCoordinateForCell, getSurround } from 'packages/base/grid/helpers/utils/gridTable'
import { generateKeyName } from 'packages/base/gridPreview/common/js/utils'
import { validProps, checkDataBarSetCopy } from './constant'
import { isValidRound, getCellColRange, cellWordWrapHeightChange } from '../../../utils/cellTool'
import { resetCellListContent } from '../../../utils'
import handSysPaste, { sysPasteData, sysWriteText } from './sysEvent'
import { getBrowserInfo } from 'packages/assets/utils/system'
import { updateDataBarSetInfoByLayout } from 'packages/base/grid/components/features-bar/dataBar/constant'

const InvalidStr = 'Invalid'
export default {
  data() {
    return {
      visibleOperate: false,
      operateIsPaste: false, // 控制单元格bounce过度效果
    }
  },
  methods: {
    handCopy() {
      const { CELL, ROWCELLS, COLUMNCELLS, NONE } = OPERATE_MODAL_TYPES.COPYINFO
      if (this.highlightList.length > 0) {
        const mergeCells = this.highlightList.filter(v => v.isMergeCell)
        const normalCells = this.highlightList.filter(v => !v.isMergeCell)
        const mergeRectCell = mergeCells.reduce((result, { start, end }) => {
          const currentCells = this.getMergeCells({ start, end })
          return [...result, ...currentCells]
        }, [])
        if (isValidRound([...normalCells, ...mergeRectCell])) {
          this.stackSelectionList = this.$_deepClone(this.highlightList)
          this.copyType = this.$_deepClone(CELL)
          sysWriteText.call(this)
          return
        }
      }
      this.$message(this.$t('sdp.views.b_plzChooseValid'))
      this.copyType = NONE
    },
    async handPaste(e = {}, shortcutKey = false) {
      const canSelfCopy = this.copyType === OPERATE_MODAL_TYPES.COPYINFO.NONE
      try {
        const { pasteArray, text } = await sysPasteData(e)
        if (text === this.syscopyUUid) {
          this.adapterCopy()
        } else {
          this.handSysPaste(pasteArray)
          this.clipBoardText = text
        }
      } catch (err) {
        const agent = getBrowserInfo()
        const isChorme = agent.browser === 'Chrome' && agent.ver > 65
        if (!isChorme && !shortcutKey && !canSelfCopy) {
          this.adapterCopy()
        } else {
          this.$message.warning(this.$t('sdp.views.b_pasteWarning'))
        }
      }
    },
    parsteCellContent(newCell, pasteCell) { // 目标单元格，粘贴板需要复制的单元格 返回复制内容
      let pasteCellContent = this.$_deepClone(pasteCell.content)
      let targetCellContent = this.$_deepClone(newCell.content)
      let cellSameRow = isSameRow.call(this, [newCell, pasteCell])
      if (pasteCellContent.hasOwnProperty('coordinate')) {
        pasteCellContent.coordinate = generateCoordinateForCell(newCell)
      }
      if (pasteCellContent.hasOwnProperty('keyName')) { // keyName 判断条件

        if (targetCellContent.hasOwnProperty('keyName')) { // 目标单元格有keyName 取之前的keyName 没有则重新获取
          pasteCellContent.keyName = targetCellContent.keyName
        } else {
          const currentKeyName = this.setKeyName()
          if (currentKeyName !== InvalidStr) {
            pasteCellContent.keyName = currentKeyName
          } else {
            return targetCellContent
          }
        }

      }

      return validProps.call(this, pasteCellContent, cellSameRow, newCell, this.layoutOriginal)
    },
    adapterCopy() {
      const { CELL, ROWCELLS, COLUMNCELLS, NONE } = OPERATE_MODAL_TYPES.COPYINFO
      if (this.copyType === NONE) {
        return
      }
      if (this.highlightList.length === 0 || !this.stackSelectionList) {
        return
      }

      const hasRule = this.stackSelectionList.some(e => e.content.dataBarSet || e.content.colorGradationSet || e.content.markMaxMinSet)
      const dataBarInfo = this.tableDefaultConfig.dataBarInfo || []
      if (checkDataBarSetCopy(this.stackSelectionList, dataBarInfo)) {
        this.$message.warning(this.$t('sdp.views.noMoreThan10RuleCell'))
        return
      }

      const startCell = this.highlightList[0]
      const firstCell = this.stackSelectionList.reduce((result, nextCell) => {
        if (nextCell.start[0] <= result.start[0]) {
          if (nextCell.start[0] === result.start[0] && nextCell.start[1] > result.start[1]) {
            return result
          }
          return nextCell
        }
        return result
      }, { start: [+Infinity, +Infinity] })
      const deviation = CalcDeviation(startCell, firstCell) // 计算当前单元格与复制的内容的偏移量
      this.operateIsPaste = true
      if (this.copyType) {
        const mergeCells = this.stackSelectionList.filter(v => v.isMergeCell)
        const normalCells = this.stackSelectionList.filter(v => !v.isMergeCell)
        if (this.checkAlertSubscriptionWarning()) {
          return false
        }
        if (!this.setMergeCellPaste(mergeCells, deviation)) {
          return false
        }
        const formatterList = this.calcDeviation(normalCells, deviation)
        this.updateTableCell(formatterList)
        this.updateHighLight() // 清空更新高亮单元格
        hasRule && updateDataBarSetInfoByLayout(this.layoutOriginal, this.tableDefaultConfig)
        formatterList.forEach(cell => {
          cellWordWrapHeightChange(cell, this)
        })
        setTimeout(() => {
          this.operateIsPaste = false
        })
        this.record('paste')
        return true
      }
      return false
    },
    checkAlertSubscriptionWarning() {
      const copyWarnNumber = this.stackSelectionList.reduce((sum, cell) => {
        if (cell.content.warningConditionList) {
          sum += cell.content.warningConditionList.filter(e => e.alertSubscription).length
        }
        return sum
      }, 0)
      if (copyWarnNumber !== 0 && this.lastElementAlertNumber - copyWarnNumber < 0) {
        this.$message.warning(this.$t('sdp.message.alertSubscriptionsLimit'))
        return true
      }
      return false
    },
    calcDeviation(list, deviation) { // 获取偏移后的数组处理非合并单元格
      const result = list.map(cell => { // 获取组装好的数组（绑定偏移后的位置）
        const start = [cell.start[0] + deviation.start[0], cell.start[1] + deviation.start[1]]
        const end = [cell.end[0] + deviation.end[0], cell.end[1] + deviation.end[1]]
        return { ...cell, start, end }
      })
      return result
    },
    updateTableCell(formatterList) { // 批量更新单元格
      const cleanCells = this.cellRemoveConflictMerge(formatterList) // 先删除目标区域合并的单元格
      this.layout.forEach(cell => {
        const filterLength = formatterList.filter(item => adjustSameCell(item, cell))
        if (filterLength.length > 0) {
          const contentResult = this.parsteCellContent(cell, filterLength[0])
          const cellClone = this.$_deepClone(cell)
          this.$set(cell, 'content', contentResult)
          this.eventBus.$emit('update-cell', cell, cellClone)
        }
      })
      if (cleanCells.length) { // 清空合并单元格需要做清空处理
        this.eventBus.$emit('paste-cell', cleanCells)
      }
    },
    getCellsRect(cellRectList) {
      return cellRectList.reduce(
        (prev, next) => {
          return getSurround(prev, next)
        },
        {
          start: [],
          end: []
        }
      )
    },
    getMergeCells({ start, end }) { // 获取合并单元格所覆盖的单元格
      let cells = []

      for (let i = start[0]; i <= end[0]; i++) {
        for (let j = start[1]; j <= end[1]; j++) {
          const area = {
            start: [i, j],
            end: [i, j]
          }
          cells.push(area)
        }
      }
      return cells
    },
    cellRemoveConflictMerge(formatterList) { // 当前单元格是否包含在合并单元格内部
      let cleanCells = []
      const resultMergeCell = this.layoutOptions.mergeCell.filter((cell) => {
        const { start: mergeStart, end: mergeEnd } = cell
        const index = formatterList.findIndex(({ start, end }) => {
          if (start[0] >= mergeStart[0] && start[1] >= mergeStart[1] && end[0] <= mergeEnd[0] && end[1] <= mergeEnd[1]) {
            return true
          }
          return false
        })
        index >= 0 && cleanCells.push(cell)
        return index < 0
      })
      this.layoutOptions.mergeCell = [...resultMergeCell]
      return cleanCells
    },
    setMergeCellPaste(mergeCells, deviation) {
      let resAreaCells = []
      let mergeCellColRange = new Set()
      const reultMergeCells = mergeCells.map((item) => {
        const { start, end } = item
        const currentCells = this.getMergeCells({ start, end }) // 拆分合并单元格
        const areaCells = this.calcDeviation(currentCells, deviation) // 拆分的单元格位置偏移

        // 校验是否在单元格坐标内
        const heightLen = this.layoutOptions.heights.length || 0
        const widthLen = this.layoutOptions.widths.length || 0
        for (let i = 0; i < areaCells.length; i++) {
          const { start: cellStart, end: cellEnd } = areaCells[i]
          if (cellStart[0] > heightLen || cellEnd[0] > heightLen) return
          if (cellStart[1] > widthLen || cellEnd[1] > widthLen) return
        }
        resAreaCells.push(...areaCells)
        const resultMergeRect = this.getCellsRect(areaCells)
        let cellData = {
          ...this.$_JSONClone(resultMergeRect),
          i: this.createI()
        }
        cellData = this.addMergeCell(cellData)
        cellData.content = this.parsteCellContent(cellData, item)
        this.checkMerged(cellData) // 清空冲突的合并单元格
        mergeCellColRange = new Set([...getCellColRange(cellData), ...mergeCellColRange])
        return cellData
      })
      if (resAreaCells.length > 0) {
        let cells = this.cleanPositionCells(resAreaCells)
        this.eventBus.$emit('paste-cell', cells)
        resetCellListContent.call(this, cells)
      }
      if (this.collapseColConflict(mergeCellColRange)) {
        this.$message({
          message: this.$t('sdp.views.b_pasteMergeCellConflictForFolded'),
          type: 'error'
        })
        return false
      }
      reultMergeCells.forEach(cell => {
        cellWordWrapHeightChange(cell, this)
      })
      this.layoutOptions.mergeCell.push(...reultMergeCells.filter(e => e))
      return true
    },
    updateHighLight() {
      this.highlightList = []
    },
    cleanPositionCells(cellsPs) { // 传入单元格list的坐标
      return this.layout.filter(cell => {
        return cellsPs.findIndex(({ start, end }) => {
          return cell.start[0] === start[0] && cell.start[1] === start[1] && cell.end[0] === end[0] && cell.end[1] === end[1]
        }) > -1
      })
    },
    setKeyName(paramsKeyName) { // 批量粘贴的时候 生成的keyName会一样 所以需要做判断
      try {
        let keyName = paramsKeyName || generateKeyName()
        const keyNameIdx = this.layout.findIndex(({ content }) => {
          return content.keyName === keyName
        })
        if (keyNameIdx >= 0) {
          return this.setKeyName()
        } else {
          return keyName
        }
      } catch (err) {
        return InvalidStr
      }

    },
    collapseColConflict(mergeCellColRange) {
      const { collapseCol } = this.tableDefaultConfig
      if (collapseCol && collapseCol.length) {
        return collapseCol.some(v => mergeCellColRange.has(v))
      }
      return false
    },
  },
  mounted() {
    this.handSysPaste = handSysPaste.bind(this)
    this.clipBoardText = undefined
  },
  created() {
    this.copyType = OPERATE_MODAL_TYPES.COPYINFO.NONE
  }
}
