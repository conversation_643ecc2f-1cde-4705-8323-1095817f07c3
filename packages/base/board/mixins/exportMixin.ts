// import html2canvas from 'html2canvas'
import html2canvas from './html2canvas.esm.js'
import { Canvg } from 'canvg'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import { TYPE_PARAM_ELEMENT } from 'packages/base/board/displayPanel/params/utils/constants'
import {
  exportExcel,
  exportExcelSheet,
  exportExcelSheet2,
  exportPdf,
  exportOfd,
  exportWord,
  getDownUrl,
  download
} from 'packages/base/board/displayPanel/params/api'
import { getParamElementLabels } from 'packages/base/board/displayPanel/components/utils'
import emitter from 'packages/assets/emitter'
import { RUN_TYPE, THEME_TYPE, } from 'packages/assets/constant'
import {
  EVENT_BUS,
  TABLE_TITLE_CLASS_NAME_BY_EXPORT,
  TABLE_TITLE_TEXT_CLASS_NAME_BY_EXPORT
} from '../displayPanel/constants'
import { languageChangeWithAll } from '../displayPanel/params/utils/utils'
import axios from 'axios'
import { previewMergeApi } from '../displayPanel/utils/helpers/api'
import { getToken } from '../../../../src/utils/auth'
import { ELEMENT_EXPORT_DATA_TYPE } from '../settingPanel/components/exportComponents/constants'
import { getDateDimensionNum, isHasDateDimension } from '../displayPanel/components/utils'
import { PageFormat, PageSize } from '../displayPanel/supernatant/dataReport/ReportSettings'
import { CAN_ELEMENT_EXPORT, CAN_ELEMENT_EXPORT_CHART } from '../displayPanel/supernatant/boardElements/constant'
import { ELEMENT_TITLE_SONTYPE } from '../displayPanel/supernatant/boardElements/elementTitle/helpers/constant'
import { getPX } from '../displayPanel/supernatant/dataReport/helper/cm2px'
import EventData from '../../../assets/EventData'
import {sortLayout} from "../../common/sdpGrid/utils";

export function isSupportCSV(el) {
  try {
    return el.type === TYPE_ELEMENT.TABLE || el.content.alias === 've-grid-normal'
  } catch (e) {
    console.log(e)
    return false
  }
}
const themeBg = {
  [THEME_TYPE.classicWhite]: 'rgba(255, 255, 255, 1)',
  [THEME_TYPE.darkBlue]: 'rgba(33, 45, 59, 1)',
  [THEME_TYPE.deepBlue]: 'rgba(24, 43, 75, 1)',
}
function handleGridTitleEllipsis(html) {
  const tableTitleList = html.getElementsByClassName(TABLE_TITLE_CLASS_NAME_BY_EXPORT)
    ;[...tableTitleList].forEach(titleElem => {
      const w = titleElem.offsetWidth
      const textElem = titleElem.getElementsByClassName(TABLE_TITLE_TEXT_CLASS_NAME_BY_EXPORT)?.[0]

      if (!textElem || textElem.offsetWidth <= w) return

      const ellipsis = document.createElement('span')
      ellipsis.innerHTML = '...'
      ellipsis.style['letter-spacing'] = '1px'

      function isNeedClip_fx(titleElem, textElem) {
        console.log('textElem.offsetWidth', textElem.offsetWidth)
        return textElem.offsetWidth > w
      }

      function tryClip(titleElem, textElem) {
        let str = textElem.innerHTML
        str = str.substring(0, str.length - 1)
        textElem.innerHTML = str
        textElem.appendChild(ellipsis)

        if (isNeedClip_fx(titleElem, textElem)) {
          textElem.removeChild(ellipsis)
          tryClip(titleElem, textElem)
        }
      }

      isNeedClip_fx(titleElem, textElem) && tryClip(titleElem, textElem)
    })

}

const IMG_ELEMENT_LIST = [
  TYPE_ELEMENT.IMAGE,
]
const TO_IMG_ELEMENT_LIST = [
  TYPE_ELEMENT.CHART,
  TYPE_ELEMENT.TABLE,
  TYPE_ELEMENT.COMBINE_CARD,
  TYPE_ELEMENT.ELEMENT_TAG_CARD,
  TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
  TYPE_ELEMENT.ELEMENT_YEAR_CARD,
  TYPE_ELEMENT.FOUR_QUADRANT,
  TYPE_ELEMENT.CONTAINER,
  TYPE_ELEMENT.ADVANCE_CONTAINER,
  TYPE_ELEMENT.CUSTOMER_ELEMENT,
  TYPE_ELEMENT.DUPONT_ANALYSIS,
]
const TO_TEXT_ELEMENT_LIST = [
  TYPE_ELEMENT.TEXT,
  TYPE_ELEMENT.ELEMENT_TITLE,
]

const EXPORT_TEXT_TITLE_LIST = [
  TYPE_ELEMENT.CHART,
  TYPE_ELEMENT.TABLE,
  TYPE_ELEMENT.CUSTOMER_ELEMENT,
  TYPE_ELEMENT.DUPONT_ANALYSIS,
]

export function cloneHandler(html) {
  if (!html) return

  console.log('html:', html)

  // if (secondBase64s.length) {
  //   const imgBase64 = secondBase64s.reduce((pre, next) => {
  //     return Object.assign(pre, next)
  //   }, {})
  //   html.querySelectorAll(`[class*='exportDOM'] .${EXPORT_CLASS}`).forEach(element => {
  //     const iframe = element.getElementsByTagName('iframe')[0]
  //     const tag = iframe.dataset.tag
  //     const img = document.createElement('img')
  //     img.src = imgBase64[tag]

  //     element.replaceChild(img, iframe)
  //   })
  // }

  html.querySelectorAll(`[class*='exportDOM'] .el-input`).forEach(item => {
    const input = item.querySelector('.el-input__inner')
    const type = input.getAttribute('type')
    const { height, width } = item.getBoundingClientRect()
    const div = document.createElement('div')
    const paddingLeft = window.getComputedStyle(input, null)['paddingLeft']
    const color = window.getComputedStyle(input, null)['color']
    const value = input.value
    input.value = ' '
    if (type === 'text') {
      Object.assign(div.style, {
        position: 'absolute',
        top: '0',
        width: width + 'px',
        height: height + 'px',
        lineHeight: height + 'px',
        paddingLeft,
        backgroundColor: 'transparent',
        color
      })
    } else if (type === 'number') {
      const diff = parseInt(paddingLeft) * 2
      Object.assign(div.style, {
        position: 'absolute',
        top: '0',
        width: (width - diff) + 'px',
        height: (height - diff) + 'px',
        lineHeight: (height - diff) + 'px',
        backgroundColor: 'transparent',
        color
      })
    }
    div.innerText = value

    item.appendChild(div)
  })

  // CLASS_NAME_FOR_ELEMENT_CONTAINER_FOR_ADVANCE_GRID_ITEM_CONTENT
  html.querySelectorAll(`[class*='exportDOM']`)
    .forEach(item => {
      Object.assign(item.parentElement.style, { boxShadow: 'unset' })
      Object.assign(item.style, { boxShadow: 'unset' })
    })

  handleGridTitleEllipsis(html)

  const circles = html.querySelectorAll('.el-progress-circle')
  // console.log('circles:', circles)

  circles.forEach(circle => {
    const circleWidth = parseFloat(circle.style.width)
    // console.log('circleWidth:', circleWidth)

    const svgNode = circle.querySelector('svg')
    // console.log('svgNode:', svgNode)

    if (!svgNode) return
    const viewBoxWidth = svgNode.getAttribute('viewBox').split(' ')[2]
    // console.log('viewBoxWidth:', viewBoxWidth)

    svgNode.removeAttribute('viewBox')
    svgNode.style.transform = `scale(${circleWidth / viewBoxWidth})`
    svgNode.style.transformOrigin = 'top left'
  })
}
const FILE_TYPE_KEY = {
  csv: 'csv',
  pdf: 'pdf',
  ofd: 'ofd',
  excel: 'excel',
  word: 'word',
}
const FILE_TYPE = {
  [FILE_TYPE_KEY.csv]: '3',
  [FILE_TYPE_KEY.pdf]: '1',
  [FILE_TYPE_KEY.ofd]: '1',
  [FILE_TYPE_KEY.excel]: '2',
  [FILE_TYPE_KEY.word]: '4',
  PDF_DATA_REPORT: '5',
}
// 导出调试接口
class Debugging {
  // 是否打开调试
  static _isOpen = false
  static _url = 'http://10.13.52.10:7002/table/parser'
  // static _url = 'https://sdptest.shijicloud.com/midway/table/parser'
  // static _url = 'https://sdp.shijicloud.com/midway/table/parser'
  static _type = ''
  static log(str) {
    if (typeof str === 'string') {
      str = JSON.parse(str)
    }

    console.log(`data-kyz-----${Debugging._type}`, str)
  }
  static getTableParams(dashboardTabList) {
    return dashboardTabList.map(item => item.parserParams)
  }

  static callApi(params, api) {
    previewMergeApi({ api, params: params.tableParams }).then(res => {
      axios({
        method: 'post',
        url: Debugging._url,
        data: {
          tableOptions: params.tableOptions,
          authorization: getToken(),
          tableElements: res
        },
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }).then(res => {
        Debugging.log(res.data.data)
      })
    })
  }

  static call(dashboardTabList, isExportNode, type, api) {
    if (!Debugging._isOpen || !isExportNode) return

    Debugging._type = type

    const list = Debugging.getTableParams(dashboardTabList)

    list.forEach(parserParams => {
      if (parserParams) {
        parserParams.forEach(params => {
          Debugging.callApi(params, api)
        })
      }
    })
  }
}

export default {
  inject: {
    kanbanId: { type: String, default: '' },
    sdpLangcode: { type: String, default: 'zh' },
    commonData: { default: {} },
    sdpBus: { default: {} },
  },

  mixins: [emitter],
  data() {
    const getNowTime = () => {
      const date = new Date()
      function getZeroDate(date) {
        return date >= 10 ? date : `0${date}`
      }
      return `${date.getFullYear()}${getZeroDate(date.getMonth() + 1)}${getZeroDate(date.getDate())}`
    }
    return {
      languageChangeWithAll: languageChangeWithAll,
      // 控制是否显示参数详情
      oldShow: false,
      // 正在导出
      exporting: false,
      fileName: '',
      // 当然导出类型
      exportType: '',
      format: '',
      requestObj: {},
      elShot: '',
      requestList: [],
      getElShot: '',
      currentParamPanel: 0,
      isSubscribe: false,
      waitingForUse: undefined,
      isSinglePage: false,
      isContainer: false,
      isAdvanceContainer: false,
      isExportChartImg: true,
      isExportChartData: false,
      tabsSelects: [],
      printElement: [],
      nowTime: getNowTime(),
      nodeExportData: null, // 导出--外部数据用于强制node导出和不下载文件
      isExportAllDateData: false, // 是否导出日期维度切换
      chioceTab: null, // 单元素导出指标
      isOpenChioceTab: false, // 单个导出元素长度
      isOpenWatermark: false,

      isTempPdfSet: false, // pdf元素导出临时变量
      pdfLayoutTemp: '1',
      pdfSizeTemp: '4',
      pdfRatioTemp: 100,

      wordExportIds: [],
      wordExportCnt: 0,
      printElementLeng: 0,
    }
  },
  computed: {
    dashboardId() {
      return this.boardInfo?.id || 'new'
    },
    isElementExport() {
      return this.isSinglePage ||
      this.isContainer ||
      this.isAdvanceContainer
    },
    // 判断外部数据用于强制node导出和不下载文件
    resourceType() {
      return this.nodeExportData?.resourceType === '1'
    },
    isExportNode() {
      // 如果是组合导出 则关闭
      if (this.utils?.options?.groupId) return false
      if (this.resourceType) return true

      if (this.isElementExport) {
        return this.boardInfo.elementExport?.isFormatExportNode
      }
      return this.boardInfo.isExportNode
    },
    // 看板需要导出的元素个数
    elAllLength() {
      let chioceTabLen = 0
      let elList = this.elList.filter(item => CAN_ELEMENT_EXPORT.includes(item.type))

      if (this.openBoardTab && (this.exportType !== FILE_TYPE_KEY.pdf || this.boardInfo.pdfType !== '1')) {
        elList = elList.filter(v => {
          return this.boardInfo.tagModeStack.midware[this.boardData.paramsPanelList.find(x => x.active).id].content.some(i => i === v.id)
        })
      }

      if (this.isOpenChioceTab) {
        elList.forEach(element => {
            if (element.type === TYPE_ELEMENT.CHART) {
              const len = element.content?.chioceTab?.length
              // len - 1是减去当前的元素指标
              chioceTabLen += len ? (len - 1) : 0
            }
        })
      }

      return elList.length + chioceTabLen
    },
    languageAndCurrencyRes() {
      let res
      if (Object.keys(this.languageAndCurrency).length > 0) {
        const { shopCurrency = null, tenantCurrency = null } = this.languageAndCurrency
        res = this.isEnterPriseCurrency ? shopCurrency : tenantCurrency
      }
      return res
    },
    getParamElementLabel() {
      const labelObj = {}
      this.paramsPanelList.forEach(item => {
        item.content.forEach(eve => {
          const labelList = getParamElementLabels.call(this, eve, this.orderParamsList, this.boardSlectLang)
          if (labelList) {
            if (Array.isArray(labelList)) {
              labelObj[eve.elName] = labelList.join(',')
            } else {
              let key
              let value
              if (Object.keys(labelList).length) {
                Object.keys(labelList).forEach(item => {
                  if (item === 'Prior Period') {
                    labelObj[item] = labelList[item].join(',')
                  } else if (item === 'name') {
                    key = labelList[item]
                  } else {
                    value = labelList[item].join(',')
                  }
                })
                labelObj[key] = value
              }
            }
          }
        })
      })
      if (this.languageAndCurrencyRes) {
        labelObj.Currency = this.languageAndCurrencyRes
      }
      return labelObj
    },
    getParamElementLabelList() {
      const res = []
      let locationObj = {}
      let hasLocation = false
      const orderParamsList = this.getOrderParamsList()
      // let paramsActive = this.paramsPanelList.find(item => item.active)
      // 导出过滤日期
      const calenedar = orderParamsList.find(item => [TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR, TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR].includes(item.type))
      const quick = orderParamsList.find(item => [TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION, TYPE_PARAM_ELEMENT.CALENDAR_QUICK].includes(item.type))
      let arr = this.$_deepClone(orderParamsList.filter(item => { return !item.isHideElement }))
      if (this.paramsType === RUN_TYPE.paramsRun) {
        if (arr.find(v => v.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK)) {
          arr.splice(arr.findIndex(v => v.type === TYPE_PARAM_ELEMENT.LOCATION_NEW), 1)
        }
      } else {
        if (arr.find(v => v.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK)) {
          arr.splice(arr.findIndex(v => v.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK), 1)
        }
      }
      if (calenedar && quick) {
        arr = orderParamsList.filter(el => el.id !== (this.paramsType === RUN_TYPE.paramsRun ? calenedar.id : quick.id))
      }

      arr.forEach(eve => {
        if (eve.type === 'locationNew') {
          hasLocation = true
          locationObj = eve
        }
        const labelList = getParamElementLabels.call(this, eve, orderParamsList, this.boardSlectLang)
        if (labelList) {
          if (Array.isArray(labelList)) {
            if (eve.type === TYPE_PARAM_ELEMENT.SEARCH) {
              let searchV = labelList[0]
              let resText = ''
              let billText = ''
              let moneyTest = ''
              if (searchV) {
                if (searchV.chkField.values.length) {
                  billText = `${eve.content.language.check.name} ${searchV.chkField.values[0]}`
                }
                if (searchV.amtField.values.length) {
                  moneyTest = `${eve.content.language.amount.name} ${searchV.amtField.filterType} ${searchV.amtField.values[0]}`
                }
              }
              resText = billText || moneyTest
              if (billText && moneyTest) {
                resText = `${billText}，${moneyTest}`
              }
              if (resText) {
                res.push({
                  paramName: eve.elName,
                  paramValue: resText
                })
              }
            } else {

              if (eve.type === 'locationNew' && labelList.length === 1 && labelList[0] === 'ALL' && eve.content.pullDownData) {
                res.push({
                  paramName: eve.elName,
                  paramValue: `${this.$t('sdp.select.allCapital')}:` + eve.content.pullDownData.map(item => {
                    return item.name
                  })?.join(',')
                })
              } else if ([TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS, TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS].includes(eve.type)) {
                const text = labelList?.join(',')
                res.push({
                  paramName: eve.elName,
                  paramValue: text.replace('ALL,', `${this.$t('sdp.select.allCapital')}:`)
                })
              } else {
                res.push({
                  paramName: eve.elName,
                  paramValue: labelList?.join(',')
                })
              }
            }
          } else {
            if (Array.isArray(labelList.Location)) {
              res.push({
                paramName: eve.elName,
                paramValue: labelList.Location?.join(',')
              })
              delete labelList.Location
            }
            if (Object.keys(labelList).length) {
              let keys = Object.keys(labelList)
              if (keys.length > 1 && eve.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
                const index = keys.indexOf('Compare')
                if (index > -1) {
                  keys = [...keys.slice(0, index), ...keys.slice(index + 1), keys[index]]
                }
              }

              keys.forEach(item => {
                let paramValue = labelList[item]?.join(',')
                let paramName = item
                if (item === 'Prior Period') {
                  paramName = eve.content.campareAlise
                }
                if (item === 'Compare' && eve.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
                  if (eve.content.options.isCompare) {
                    paramName = eve.content.LocationCompareName
                  }
                  if (paramValue === 'ALL' && eve.content.pullDownData) {
                    paramValue = `${this.$t('sdp.select.allCapital')}:` + eve.content.pullDownData.map(item => {
                      return item.name
                    })?.join(',')
                  }
                }
                if (item !== 'Compare' && eve.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
                  if (paramValue === 'ALL' && eve.content.pullDownData) {
                    paramValue = `${this.$t('sdp.select.allCapital')}:` + eve.content.pullDownData.map(item => {
                      return item.name
                    })?.join(',')
                  }
                }
                if ([TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR, TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR].includes(eve.type)) {
                  const text = paramValue
                  paramValue = text.replace('ALL,', `${this.$t('sdp.select.allCapital')}:`)
                }
                res.push({
                  paramName,
                  paramValue
                })
              })
            }
          }
        }
      })
      if ((this.languageAndCurrency.shopCurrency || this.languageAndCurrency.tenantCurrency) && !hasLocation && this.boardInfo.currencyInfo) {
        res.push({
          paramName: 'Currency',
          paramValue: this.isEnterPriseCurrency ? this.languageAndCurrency.shopCurrency : this.languageAndCurrency.tenantCurrency
        })
      }
      // 过滤value为空的值
      var newres = []
      Object.keys(res).forEach(el => {
        if (res[el].paramValue) {
          res[el].paramValue = this.languageChangeWithAll(res[el].paramValue)
          newres.push(res[el])
        }
      })
      if (this.languageAndCurrencyRes && hasLocation && this.boardInfo.currencyInfo) {
        newres.push({
          paramName: locationObj.content.currency || 'Currency',
          paramValue: this.languageAndCurrencyRes
        })
      }
      return newres
    },
    openBoardTab() {
      return this.boardInfo.openBoardTab
    },
    orderByProperty() {
      if (this.exportType === FILE_TYPE_KEY.excel) return 'position'
      if (!this.isExportNode && FILE_TYPE_KEY.csv) return 'position' // csv如果不是大数据导出,需要按位置导出
      return 'elName'
    },
  },
  watch: {
    elShot(val) {
      let postData = {
        pdfReportType: this.boardInfo.pdfType,
        direction: this.boardInfo.pdfLayout,
        pageSize: this.boardInfo.pdfSize,
        percentage: this.boardInfo.pdfRatio,
        dashboardName: this.boardInfo.nameSub,
        dashboardCode: this.boardInfo.code,
        tenantId: this.utils.tenantId,
        dashboardId: this.dashboardId,
      }
      if (this.exportType === FILE_TYPE_KEY.pdf && this.isTempPdfSet && this.boardInfo.pdfType === '2') {
        Object.assign(postData, {
          direction: this.pdfLayoutTemp,
          pageSize: this.pdfSizeTemp,
          percentage: this.pdfRatioTemp,
        })
      } else if (this.exportType === FILE_TYPE_KEY.ofd && this.isTempPdfSet && this.boardInfo.pdfType === '2') {
        Object.assign(postData, {
          direction: this.pdfLayoutTemp,
          pageSize: this.pdfSizeTemp,
          percentage: this.pdfRatioTemp,
        })
      } else {
        this.clearPdfTempSet()
      }
      Object.assign(postData, val)
      console.log('pdf  --- 3')
      this._downloadPDF(postData)
    },
    exporting(val) {
      this.$emit('exportingChange', val)
    },
    requestList(newval) {
      const isElementExport = this.isSinglePage || this.isContainer || this.isAdvanceContainer
      const isOk = isElementExport ? newval.length === this.printElementLeng : newval.length === this.elAllLength
      if (isOk) { // 一次性全部导出或者单张导出时可以执行
        let val = this.orderRequestList(newval)
        if (isElementExport) {
          const isAdvanceContainer = this.isAdvanceContainer && this?.tabsSelects?.length > 0

          const sheetEls = val.filter(item => item.isSheet).map((el, index) => {
            let title = ''

            if (isAdvanceContainer) {
              this.tabsSelects.forEach(item => {
                const isHas = item.checkedEl.some(it => it.id === el.id)
                isHas && (title = item.title)
              })
            }

            return this._formatDashboardTab([el], el.sheetName, title ? `${title}_${el.sheetName}(${index + 1})` : `${el.sheetName}(${index + 1})`)
          })
          const els = val.filter(item => !item.isSheet)

          if (isAdvanceContainer) {
            this.requestObj.dashboardTabList = this.tabsSelects.map((item) => {
              let tabElList = []
              els.forEach(v => {
                if (item.checkedEl.some(it => it.id === v.id)) {
                  tabElList.push(v)
                }
              })
              if (!tabElList.length) {
                return false
              }

              let res = this._formatDashboardTab(tabElList, item.title)
              return res
            }).filter(e => e)

            this.requestObj.dashboardTabList.push(...sheetEls)
          } else {
            this.requestObj.dashboardTabList = els.length ? [this._formatDashboardTab(els), ...sheetEls] : sheetEls
          }
        } else if (this.dynamicTags.length && !this.isAdvanceContainer) {
          let dynamicTags = this.$_deepClone(this.dynamicTags)
          this.requestObj.dashboardTabList = dynamicTags.reverse().map((item) => {
            let tabElList = val.filter(v => v.dynamicTagsId === item.id)
            let res = this._formatDashboardTab(tabElList, this.openBoardTab ? `` : item.label)
            return res
          })
        }
        const darkFlag = Number(this.themeData.themeType !== THEME_TYPE.classicWhite).toString()
        this.requestObj.paramElementList = this.getParamElementLabelList
        this.requestObj.dashboardName = this.boardInfo.nameSub
        this.requestObj.tenantId = this.utils.tenantId
        this.requestObj.showWaterMark = this.isOpenWatermark
        this.requestObj.exportStyle = this.boardInfo.exportStyle
        this.requestObj.titleStyle = Object.assign({}, this.boardInfo.content.nameCss, this.titleStyle)
        if (this.requestObj.titleStyle) {
          delete this.requestObj.titleStyle.gradientColor
          delete this.requestObj.titleStyle.angle
          delete this.requestObj.titleStyle.type
        }
        this.requestObj.darkFlag = darkFlag
        this.openBoardTab ? this.requestObj.dashboardCode = `${this.boardInfo.code} (${this.boardData.paramsPanelList.find(v => v.active).label})` : this.requestObj.dashboardCode = this.boardInfo.code
        if (!this.requestObj.dashboardTabList.length) {
          this.exporting = false
          this.commonData.setExportLoading(false)
          return this.$message.error(this.$t('sdp.views.downErr'))
        }

        if (this.requestObj.dashboardTabList.some(item => !/^[^\[\]:：*?？\/\\]{0,31}$/.test(item.sheetName || item.tabName))) {
          this.exporting = false
          this.commonData.setExportLoading(false)
          return this.$message.error(this.$t('sdp.views.downFormat'))
        }
        if (this.isExportNode) {
          this.requestObj.fileType = FILE_TYPE[this.exportType]
          this.requestObj.dashboardId = this.dashboardId
        }
        this.setDashboardRemark(this.exportType)
        if (this.exportType === FILE_TYPE_KEY.pdf) {
          let obj = this.$_deepClone(this.requestObj)
          this.getElShot(obj)
        } else if (this.exportType === FILE_TYPE_KEY.ofd) {
          let obj = this.$_deepClone(this.requestObj)
          this.getElShot(obj)
        } else if (this.exportType === FILE_TYPE_KEY.excel) {
          const exportExcelSheetApi = this.isExportNode ? exportExcelSheet2 : exportExcelSheet
          Debugging.call(this.requestObj.dashboardTabList, this.isExportNode, FILE_TYPE_KEY.excel, this.api)
          if (this.utils?.options?.groupId) {
            // const paramStr = JSON.stringify(this.paramAddNodeExportData(this.requestObj))
            console.log('10532 down', this.requestObj)
            this.$emit('onGroupExportSuccess', this.$_JSONClone(this.requestObj))
            return
          }
          exportExcelSheetApi(this.api, this.paramAddNodeExportData(this.requestObj), this.utils.env?.projectName || '')
            .then(res => {
              const callback = () => {
                if (this.isSubscribe) this.nextExcelExport(false)
                this.$emit('onExportSuccess')
                this.exporting = false
                this.commonData.setOpenRunState(false)
                this.commonData.setExportLoading(false)
              }

              if (this.isExportNode) {
                this.nodeRollPolling(res, this.format, callback)
              } else {
                this.downloadFile(res, this.format)
                callback()
              }
            })
            .catch(() => {
              this.$emit('onExportFail')
              // this.$message.error(this.$t('sdp.views.downFailure'))
              this.exporting = false
              this.commonData.setOpenRunState(false)
              this.commonData.setExportLoading(false, false)
            })
            .finally(() => {
              this.isSinglePage = false
              this.isContainer = false
              this.isAdvanceContainer = false
              this.isExportAllDateData = false
              this.chioceTab = null
              this.isOpenChioceTab = false
              this.isOpenWatermark = false
              this.isExportChartImg = true
              this.isExportChartData = false
            })
        }
      }
    }
  },
  methods: {
    setDashboardRemark(exportType) {
      const pdfBoardRemark = this.boardInfo.exportStyle.pdfBoardRemark
      const excelBoardRemark = this.boardInfo.exportStyle.excelBoardRemark
      const showBoardDescription = this.boardInfo.showBoardDescription
      const isBool = (pdfBoardRemark === '1' && exportType === FILE_TYPE_KEY.pdf) || (excelBoardRemark === '1' && exportType === FILE_TYPE_KEY.excel)
      if (isBool && showBoardDescription) {
        this.requestObj.dashboardRemark = this.boardDescriptionContent
      } else {
        this.$delete(this.requestObj, 'dashboardRemark')
      }
    },
    hideParam(val) {
      this.show = val
    },
    clearPdfTempSet() {
      this.isTempPdfSet = false
      this.pdfLayoutTemp = '1'
      this.pdfSizeTemp = '4'
      this.pdfRatioTemp = 100
    },
    exportSinglePdf(eventData) {
      this.exportType = FILE_TYPE_KEY.pdf
      if (eventData instanceof EventData) {
        // @ts-ignore
        const { data } = eventData
        this.isTempPdfSet = data.isTmp
        this.pdfLayoutTemp = data.pdfLayout
        this.pdfSizeTemp = data.pdfSize
        this.pdfRatioTemp = data.pdfRatio
      } else {
        this.clearPdfTempSet()
      }
      // todo 调试用 导出需要恢复这行代码
      this.commonData.setExportLoading(true)
      this.oldShow = this.show
      this.hideParam(true)
      this.exporting = true
      // this.commonData.setShowTooltipFlag(true)
      let doExport = this.autoRetry(this._exportSinglePdf, 5)

      const isReportPdfScreenExport = this.isDataReport && this.boardInfo.pdfType === '1' && !this.isMobile
      if (isReportPdfScreenExport) {
        this.wordExportIds = []
        this.wordExportCnt = 0
      }

      // 延迟调用，避免参数组件不显示
      setTimeout(() => {
        doExport().then(() => {
          // this.$emit('onExportSuccess')
        }).catch(() => {
          // this.$emit('onExportFail')
          // this.$message.error(this.$t('sdp.views.downFailure'))
        })
      }, 1000)
    },
    exportSingleOfd(eventData) {
      this.exportType = FILE_TYPE_KEY.ofd
      if (eventData instanceof EventData) {
        // @ts-ignore
        const { data } = eventData
        this.isTempPdfSet = data.isTmp
        this.pdfLayoutTemp = data.pdfLayout
        this.pdfSizeTemp = data.pdfSize
        this.pdfRatioTemp = data.pdfRatio
      } else {
        this.clearPdfTempSet()
      }
      // todo 调试用 导出需要恢复这行代码
      this.commonData.setExportLoading(true)
      this.oldShow = this.show
      this.hideParam(true)
      this.exporting = true
      // this.commonData.setShowTooltipFlag(true)
      let doExport = this.autoRetry(this._exportSinglePdf, 5)

      const isReportPdfScreenExport = this.isDataReport && this.boardInfo.pdfType === '1' && !this.isMobile
      if (isReportPdfScreenExport) {
        this.wordExportIds = []
        this.wordExportCnt = 0
      }

      // 延迟调用，避免参数组件不显示
      setTimeout(() => {
        doExport().then(() => {
          // this.$emit('onExportSuccess')
        }).catch(() => {
          // this.$emit('onExportFail')
          // this.$message.error(this.$t('sdp.views.downFailure'))
        })
      }, 1000)
    },
    ignoreElements(el) {
      const needEl = !['STYLE', 'HEAD', 'BR', 'SCRIPT', 'META', 'LINK', 'TITLE'].includes(el.tagName) &&
              !el.classList.contains('space') &&
              !el.classList.contains('sdp-board-preview') &&
              !el.classList.contains('sdp-full-board-preview')
      const noNeed = el.classList.contains('sdp-large-screen-enlarge') || el.style.display === 'none'
      const noWH = (el.offsetHeight === 0 || el.offsetWidth === 0) && el.style.overflow === 'hidden'
      // if (needEl && (noNeed || noWH)) {
      //   console.log('ignoreElements-》', el)
      // }
      return needEl && (noNeed || noWH)
    },
    // 导出全部看板元素单页pdf
    async _exportSinglePdf() {
      const darkFlag = Number(this.themeData.themeType !== THEME_TYPE.classicWhite).toString()

      switch (this.boardInfo.pdfType) {
        // 截图导出
        case '1': {
          console.log('ptf导出')
          const element: HTMLElement = this.isMobile ? document.querySelector(`#mobileElementPanel`) : document.querySelector(`.${this.kanbanId}.supernatant-pc`)
          const elContainer = document.querySelectorAll('.container-element-wrapper')
          // 容器截屏bug修复
          const carouselItem = element.querySelector<HTMLElement>('.container-tabs')
          const carouselBar = element.querySelector<HTMLElement>('.print-right-change')
          const head = document.querySelector<HTMLElement>('#data-screen-table')
          // 移动端
          this.isMobile && this.mobileScrollTo?.()
          const fullScreen = document.querySelectorAll<HTMLElement>('.hquanpingzhanshifix')
          const dismobileEnlarge = document.querySelector<HTMLElement>('.dismobileEnlarge')
          element.classList.add('export-status')

          // let mobileElementHeight = ''
          let mobileEnlargeHeight = ''
          if (this.isMobile) {
            // mobileElementHeight = element.style.height
            mobileEnlargeHeight = dismobileEnlarge.style.height
            // element && (element.style.height = 'auto')
            dismobileEnlarge && (dismobileEnlarge.style.height = 'auto')
          }

          // const reportElement: HTMLElement = this.isDataReport ? document.querySelector('.data-report-wrapper-active') : null
          const reportElementScale: HTMLElement = this.isDataReport ? document.querySelector('.data-report-scale-bar-container') : null

          head && (head.style.overflow = 'hidden')
          carouselItem && (carouselItem.style.opacity = '0')
          carouselBar && (carouselBar.style.opacity = '0')
          reportElementScale && (reportElementScale.style.opacity = '0')

          elContainer.forEach(each => {
            const carouselBoxItem = each.querySelectorAll<HTMLElement>('.el-carousel__item')
            const activeCarouselBoxItem = each.querySelector<HTMLElement>(
              '.el-carousel__item.is-active'
            )
            const carouselBoxBar = each.querySelector<HTMLElement>('.el-tabs__active-bar')
            carouselBoxItem.length &&
              carouselBoxItem.forEach((item: HTMLElement) => {
                item.style.opacity = '0'
              })
            activeCarouselBoxItem && (activeCarouselBoxItem.style.opacity = '1')
            carouselBoxBar && (carouselBoxBar.style.opacity = '0')
          })

          fullScreen.forEach(each => {
            each.style.visibility = 'hidden'
          })
          // 设置水印
          if (this.watermarkUrl) {
            const watermarkDiv = document.querySelector('.watermark')
            const cloneNode = watermarkDiv.cloneNode()
            element.appendChild(cloneNode)
          }

          await new Promise<void>((resolve) => {
            let customerElements = this.elList.filter(el => el.type === TYPE_ELEMENT.CUSTOMER_ELEMENT).map(el => el.id)

            if (!customerElements.length) {
              resolve()
              return
            }
            this.sdpBus.$emit(EVENT_BUS.CUSTOMR_EXPORT, undefined, this.exportType, (id) => {
              customerElements = customerElements.filter(elId => elId !== id)

              if (!customerElements.length) {
                resolve()
              }
            })
          })

          const SCALE = window.SDP_EXPORT_NUM || 5
          const toCanvasCallback = async (element, height = undefined) => {
            return await html2canvas(element, {
              onclone: cloneHandler,
              scale: SCALE,
              windowHeight: height || element.scrollHeight,
              backgroundColor: this.themeData.themeType === THEME_TYPE.darkBlue ? '#1D2631' : '#F7F7F7',
              ignoreElements: this.ignoreElements,
              useCORS: true
            })
          }

          let secondBase64 = ''

          const reportItemList = []
          if (this.isDataReport && !this.isMobile) {
            const reportItemDomList = document.querySelectorAll<HTMLElement>('.data-report-wrapper-active .data-report-item') || []
            const paramsInfo = document.querySelector<HTMLElement>('.selected-params-info')

            if (this.boardInfo.exportStyle.pdfPreferences === '1') {
              const reportContainerList = document.querySelectorAll('.data-report-container')
              reportContainerList.forEach(each => each.scrollTop = 0)

              const titleElement = document.querySelector<HTMLElement>('#title')
              element && (element.style.width = (reportItemDomList[0].offsetWidth + 30) + 'px')
              titleElement && (titleElement.style.display = 'none')
              // paramsInfo && (paramsInfo.style.height = reportItemDomList[0].offsetHeight + 'px')

              // 固定占有的高度 和 参数padding等大概需要160 170的高度
              const scrollHeight = reportItemDomList[0].scrollHeight + paramsInfo.offsetHeight + (paramsInfo.offsetHeight ? 170 : 160)
              const reportItemTmp = await toCanvasCallback(element, scrollHeight)
              const reportItemBase64 = reportItemTmp.toDataURL('image/png')
              reportItemList.push(reportItemBase64)

              element && (element.style.width = 'auto')
              titleElement && (titleElement.style.display = 'block')
              // paramsInfo && (paramsInfo.style.height = 'auto')
            }

            const scaleNum = window.SDP_EXPORT_NUM || 5
            const scaleOldObj = {}
            this.elList.forEach(el => {
              if (el.type === TYPE_ELEMENT.TABLE) {
                scaleOldObj[el.id] = el.scale
                el.scale = scaleNum
                el.vm?.$refs?.tableRender?.sdpSheet?.setScale?.(el.scale)?.render?.()
              }
            })

            for (let [index, item] of reportItemDomList.entries()) {
              if (index === 0 && this.boardInfo.exportStyle.pdfPreferences === '1') continue
              let hasActive = false
              let cloneNode = null
              if (item.classList.contains('active')) {
                hasActive = true
                item.classList.remove('active')
              }
              // 设置水印
              if (this.watermarkUrl) {
                const watermarkDiv = document.querySelector('.watermark')
                cloneNode = watermarkDiv.cloneNode()
                item.appendChild(cloneNode)
              }
              const reportItemTmp = await toCanvasCallback(item)
              // reportItemTmp.width = item.offsetWidth
              // reportItemTmp.height = item.offsetHeight
              // const canvasTmp = document.createElement('canvas')
              // canvasTmp.width = item.offsetWidth * scaleNum
              // canvasTmp.height = item.offsetHeight * scaleNum
              // const ctxTmp = canvasTmp.getContext('2d')
              // ctxTmp.drawImage(reportItemTmp, 0, 0, item.offsetWidth * scaleNum, item.offsetHeight * scaleNum)

              // ctxTmp.mozImageSmoothingEnabled = false
              // ctxTmp.webkitImageSmoothingEnabled = false
              // ctxTmp.msImageSmoothingEnabled = false
              // ctxTmp.imageSmoothingEnabled = false

              // const reportItemBase64 = canvasTmp.toDataURL('image/png')
              const reportItemBase64 = reportItemTmp.toDataURL('image/png')
              reportItemList.push(reportItemBase64)
              if (hasActive) {
                item.classList.add('active')
              }
              if (this.watermarkUrl) {
                item.removeChild(cloneNode)
              }
            }

            this.elList.forEach(el => {
              if (el.type === TYPE_ELEMENT.TABLE) {
                el.scale = scaleOldObj[el.id]
                !el.scale && delete el.scale
                el.vm?.$refs?.tableRender?.sdpSheet?.setScale?.(el.scale || 1)?.render?.()
              }
            })
          } else {
            console.log('html2canvas>', html2canvas)
            console.log('window html2canvas>', window.html2canvas)
            const tempCanvas1 = await html2canvas(element, {
              onclone: cloneHandler,
              windowHeight: element.scrollHeight,
              backgroundColor: this.themeData.themeType === THEME_TYPE.darkBlue ? '#1D2631' : '#F7F7F7',
              ignoreElements: this.ignoreElements,
              useCORS: true
            })
            secondBase64 = tempCanvas1.toDataURL('image/png')
          }
          console.log('PDF EXPORT ==== >', reportItemList)

          setTimeout(() => {
            head && (head.style.overflow = 'auto')

            const watermarkDiv = element.getElementsByClassName('watermark')
            if (watermarkDiv && watermarkDiv.length) {
              element.removeChild(watermarkDiv[watermarkDiv.length - 1])
            }
            // remarkTooltip.forEach(item => {
            //   document.body.appendChild(item)
            // }) 3 line 备注导出时显示用，可删
            carouselItem && (carouselItem.style.opacity = '1')
            carouselBar && (carouselBar.style.opacity = '1')
            reportElementScale && (reportElementScale.style.opacity = '1')
          }, 0)
          elContainer.forEach(each => {
            const carouselBoxItem = each.querySelectorAll('.el-carousel__item')
            const carouselBoxBar = each.querySelector<HTMLElement>('.el-tabs__active-bar')
            carouselBoxItem.length &&
              carouselBoxItem.forEach((item: HTMLElement) => {
                item.style.opacity = '1'
              })
            carouselBoxBar && (carouselBoxBar.style.opacity = '1')
          })
          fullScreen.forEach(each => {
            each.style.visibility = ''
          })
          if (this.isMobile) {
            // element && (element.style.height = mobileElementHeight)
            element && (element.classList.remove('export-status'))
            dismobileEnlarge && (dismobileEnlarge.style.height = mobileEnlargeHeight)
          }
          this.hideParam(this.oldShow)
          let postData = {
            pdfReportType: this.boardInfo.pdfType,
            direction: this.boardInfo.pdfLayout,
            pageSize: this.boardInfo.pdfSize,
            percentage: this.boardInfo.pdfRatio,
            tenantId: this.utils.tenantId,
            dashboardId: this.dashboardId,
          }
          Object.assign(postData, {
            screenshotUrl: this.isDataReport && !this.isMobile ? reportItemList[0] : secondBase64,
            screenshotUrlList: this.isDataReport && !this.isMobile ? reportItemList.slice(1) : undefined,
            darkFlag
          }, this.isExportNode ? {
            dashboardTabList: [],
            fileType: this.isDataReport && !this.isMobile ? FILE_TYPE.PDF_DATA_REPORT : FILE_TYPE[this.exportType]
          } : {
            fileType: this.isDataReport && !this.isMobile ? FILE_TYPE.PDF_DATA_REPORT : undefined
          })
          this.generateFileName()
          this._downloadPDF(postData)
          break
        }
        // 元素导出
        case '2': {
          console.log('ptf元素导出')
          this.getElementShot(
            (val) => {
              this.elShot = { ...val, darkFlag }
            }
          )
          break
        }
        default:
          this.getElementShot(
            (val) => {
              this.elShot = { ...val, darkFlag }
            }
          )
      }
    },
    _formatDashboardTab(list, tabName = '', sheetName = '') {
      let chartArr = CAN_ELEMENT_EXPORT_CHART
      const tableList = list.filter(item => item.type === TYPE_ELEMENT.TABLE || (item.isTable && item.type === TYPE_ELEMENT.CUSTOMER_ELEMENT))
      const chartDataList = list.filter((item) => chartArr.includes(item.type) || (!item.isTable && item.type === TYPE_ELEMENT.CUSTOMER_ELEMENT))

            let obj = {
        chartDataList: chartDataList.map(v => v.data.chartData.dataURL),
        // 没有sheetName后台直接使用该值和dashboardCode拼接为sheet名称
        tabName,
        // 有sheetName后台直接使用该值为sheet名称
        sheetName,
        [this.isExportNode ? 'parserParams' : 'tableElementList']: (this.isExportNode
          ? tableList.map(v => v.params)
          : (
            this.exportType === FILE_TYPE_KEY.pdf
            ? tableList.filter(v => Object.keys(v.data).length > 0).map(v => v.data)
            : tableList.map(v => v.data)
          ))
      }

      if (this.isSinglePage || this.isContainer || this.isAdvanceContainer && this.isExportChartData) {
        obj['chartRespList'] = chartDataList.map(e => e.data.chartData._chartResponseTmp).filter(e => e)
      }

      if (obj.chartDataList && !obj.chartDataList.filter(e => e).length) {
        delete obj.chartDataList
      }

      if (obj.chartRespList && !obj.chartRespList.filter(e => e).length) {
        delete obj.chartRespList
      }

      if (this.isExportNode && obj.parserParams) {
        obj.parserParams.forEach(item => {
          item.tableParams && this.$set(item.tableParams, 'bigDataExport', true)
        })
      }
      return obj
    },
    // pdf全部截图导出
    _downloadPDF(postData) {
      console.log('pdf  --- 4')
      const isReportPdfScreenExport = this.isDataReport && this.boardInfo.pdfType === '1' && !this.isMobile
      let isEnd = true
      const currentPanelLabel = this.boardData.paramsPanelList.find(v => v.active).label
      if (isReportPdfScreenExport) {
        const pageTabId = this.dynamicTagsActiveId
        const tabId = this.paramsPanelList.find(e => e.active)?.id
        // 增加一个cnt限制次数，以防死循环
        this.wordExportIds.push(this.openBoardTab ? tabId : pageTabId)
        this.wordExportCnt++

        const tabList = this.openBoardTab ? this.paramsPanelList.map(e => e.id) : this.dynamicTags.map(e => e.id)
        isEnd = tabList.every(tab => this.wordExportIds.includes(tab)) || this.wordExportCnt >= 6

        if (!isEnd) {
          // 切换tab
          const nextTabId = tabList.find(e => !this.wordExportIds.includes(e))
          if (this.openBoardTab) {
            const tab = this.paramsPanelList.find(e => e.id === nextTabId)
            tab && this.broadcast('tab', 'onPanelChange', { data: tab })
          } else {
            this.setDynamicTagsActiveById(nextTabId)
          }

          // 运行导出
          this.run({
            data: { options: { exportType: 'PDF' } },
            source: 'export',
            options: {
              derivedFun: () => {
                this._exportSinglePdf()
              }
            }
          })
        } else {
          const nextTabId = this.wordExportIds[0]
          if (this.openBoardTab) {
            const tab = this.paramsPanelList.find(e => e.id === nextTabId)
            tab && this.broadcast('tab', 'onPanelChange', { data: tab })
          } else {
            this.setDynamicTagsActiveById(nextTabId)
          }

          const ids = this.dynamicTags.find(e => e.active)?.id || []
          this.refreshEl({ ids: ids })
        }
      }
      let isOfd = this.exportType === FILE_TYPE_KEY.ofd
      const exportPdfApi = this.isExportNode ? exportExcelSheet2 : isOfd ? exportOfd : exportPdf
      const callback = (flag) => {
        let isPreviewMsg = !postData?.pdfReportType || postData.pdfReportType !== '1'
        isEnd && this.commonData.setExportLoading(false, isPreviewMsg)
        isEnd && (this.exporting = false)
        this.$emit('onExportSuccess')
      }
      const format = isOfd ? 'ofd' : 'pdf'
      Debugging.call(postData.dashboardTabList, this.isExportNode, format, this.api)
      if (isOfd) {
        postData = {
          ...postData,
          fileType: '6'
        }
      }
      exportPdfApi(this.api, this.paramAddNodeExportData(postData), this.utils.env?.projectName || '')
        .then(res => {
          if (this.isExportNode) {
            this.nodeRollPolling(res, format, callback)
          } else {
            let fileName = ''
            if (this.commonData.isSubscribe) {
              let subscribeObj = this.commonData.subscribeObj()
              fileName = subscribeObj.boardName || this.$_generateCode()
            } else {
              let code = this.isTemplateBoard ? this.boardInfo.name : this.boardInfo.code
              fileName = code ? (this.openBoardTab ? `${code}(${currentPanelLabel || this.boardData.paramsPanelList.find(v => v.active).label})` : code) : this.$_generateCode()
            }
            this.downloadFile(res, format, fileName)
            callback('success')
          }
          this.$emit('onExportSuccess')
        })
        .catch(() => {
          callback('fail')
          this.$emit('onExportFail')
        })
    },
    // 重试
    autoRetry(func, retryMax) {
      let retryNum = 0
      const vm = this
      let funcR = function () {
        let params = arguments
        return new Promise((resolve, reject) => {
          func(...params).then(result => {
            resolve(result)
          }).catch((err) => {
            if (retryNum < retryMax) {
              retryNum++
              console.log('错误次数' + retryNum)
              setTimeout(() => {
                resolve(funcR(...params))
              }, 5000)
            } else {
              reject(err)
              vm.$emit('onErr', err)
            }
          })
        })
      }
      return funcR
    },
    getElementShot(func) {
      this.getElShot = func
      this.generateFileName()
      this.commonData.setOpenRunState(true)
      this.exporting = true
      this.requestList = []
      this.getAllElRequestData()
    },
    async getHtml2canvas(id, useCORS = true) {
      const dom = this.$el.querySelector(`.${this.kanbanId}.exportDOM${id}`)
      const tempDom: HTMLElement = dom || document.querySelector(`.${this.kanbanId}.exportDOM${id}`)
      if (!tempDom) return

      const element = this.elList.find(e => e.id === id)
      const isTableOrChart = element && [
        TYPE_ELEMENT.TABLE,
        TYPE_ELEMENT.CHART,
        TYPE_ELEMENT.TEXT,
        TYPE_ELEMENT.ELEMENT_TITLE,
        TYPE_ELEMENT.CONTAINER,
        TYPE_ELEMENT.ADVANCE_CONTAINER,
      ].includes(element.type)

      const icContainer = [
        TYPE_ELEMENT.CONTAINER,
        TYPE_ELEMENT.ADVANCE_CONTAINER,
      ].includes(element.type)

      const isExcel = this.exportType === FILE_TYPE_KEY.excel
      const SCALE = isTableOrChart && !isExcel ? 10 : 1
      let svgElem = tempDom.querySelectorAll('svg')
      if (!svgElem?.length) {
        return html2canvas(tempDom, {
          onclone: icContainer ? undefined : cloneHandler,
          useCORS,
          scale: SCALE,
          backgroundColor: themeBg[this.themeData.themeType],
          ignoreElements: this.ignoreElements,
        })
      }

      // 先复制该元素，避免影响原有的dom
      const tempDom_copy: DocumentFragment = document.createDocumentFragment()
      const tempDom_copy_inner = tempDom.cloneNode(true)
      tempDom_copy_inner.style.zIndex = -10
      tempDom_copy.appendChild(tempDom_copy_inner)
      tempDom.parentElement.append(tempDom_copy)
      const copyDom: HTMLElement = tempDom_copy_inner

      svgElem = copyDom.querySelectorAll('svg')
      const promiseList = []
      svgElem.forEach((node) => {
        var parentNode: HTMLElement = node.parentElement
        const { height, width } = parentNode.getBoundingClientRect()
        var svg = node.outerHTML.trim()
        var svgcanvas = document.createElement('canvas')
        const ctx = svgcanvas.getContext('2d')
        promiseList.push(Canvg.from(ctx, svg).then(cvs => {
          cvs.resize(width, height)
          cvs.start()
          parentNode.removeChild(node)
          parentNode.appendChild(svgcanvas)
        }))
      })

      await Promise.all(promiseList)

      const res = html2canvas(copyDom, {
        onclone: cloneHandler,
        scale: SCALE,
        useCORS,
        backgroundColor: themeBg[this.themeData.themeType],
        ignoreElements: this.ignoreElements
      })
      // 移除追加的元素
      tempDom.parentElement.removeChild(copyDom)

      return res
    },
        async getAllElRequestData() {
      console.log('pdf  --- 2')
      const copyElList = this.orderElList()
      let elList: any[] = []
      const isSingle = this.isContainer || this.isAdvanceContainer || this.isSinglePage

      if (isSingle) {
        elList = [...this.printElement]
      } else if (this.openBoardTab) {
        let currentTag = this.getCurrentTag()
        elList = copyElList.filter(v => {
          return currentTag.some(i => i === v.id)
        })
      } else {
        elList = copyElList
      }

      for (let i = 0; i < elList.length; i++) {
        const elListItem: any = elList[i]
        // 被设置交互的看板元素需要导出空数据
        let allowsEmptyExport = !!elListItem?.elAttr?.isRenderInteraction
        let exportTableElement
        const { content: elContent, type, elName, id = '', _dynamicTags } = elListItem
        const obj = {
          data: {},
          type,
          elName,
          layout: this.getElOrderByLayout(elListItem),
          dynamicTagsId: _dynamicTags?.id,
          id
        }
        const isWeAndTimeAndScrollText = type === TYPE_ELEMENT.SCROLL_TEXT || type === TYPE_ELEMENT.WEB || type === TYPE_ELEMENT.TIME
        if (isWeAndTimeAndScrollText) {
          delete obj.id
        }
        // 是否包含日期维度切换
        const isHasDateDimensionElement = this.isExportAllDateData && isHasDateDimension(elListItem)
        // 指标
        let isChioceTab = false
        let chioceTabIndex: number[] = []
        if (isSingle) {
          const chioceTab = this.chioceTab
          const chioceTabIds = chioceTab?.[elListItem.id] || []
          isChioceTab = !!chioceTabIds.length
          elListItem.content?.chioceTab?.forEach((el, index) => {
            if (chioceTab[elListItem.id].includes(el.id)) {
              chioceTabIndex.push(index)
            }
          })
          // 添加导出数量
         if (isChioceTab) {
            // 减去自身的
            this.printElementLeng += (chioceTabIds.length - 1)
          }
        } else if (this.isOpenChioceTab) {
          isChioceTab = !!elListItem.content?.chioceTab?.length

          elListItem.content?.chioceTab?.forEach((el, index) => {
              chioceTabIndex.push(index)
          })
        }
        const isVeGridNormal = (elListItem) => {
          return elListItem.content.alias === 've-grid-normal'
        }
        const getChioceTabName = (elListItem, index) => {
          const isChioceTab = !!elListItem.content?.chioceTab?.length
          let name = elListItem.elName
          if (isChioceTab) {
            elListItem.content?.chioceTab?.forEach((el, i) => {
              if (i === index) {
                name = el.name
              }
            })
          }

          return name
        }
        // 表格导出
        const tableDown = async (elListItem, index?) => {
                    obj.type = elListItem.type
                    const vm = isVeGridNormal(elListItem) ? elListItem.vm.$refs.grid : elListItem.vm

          const tableDownCall = (isSheet, sheetName = '', resolve = () => {}) => {
            if (allowsEmptyExport) {
              this.requestList.push(this.$_JSONClone({ ...obj, isSheet, sheetName }))
              resolve()
            } else {
              if (this.isExportNode) {
                const data = this.getPrintParamsFormatterData(elListItem)

                this.requestList.push(this.$_JSONClone({ ...data, isSheet, sheetName }))
                resolve()
              } else {
                let callBack = (val) => {
                  if (!val) {
                    this.exportHints()
                    resolve()
                    return
                  }
                  Object.assign(obj, { data: val.data, isSheet })

                  if (elContent.alias === 've-grid-normal') {
                    const tableName = this.$_getProp(elContent, `chartUserConfig.title.text`, '')

                    if (tableName) obj.data.tableName = tableName
                    const tableTitleStyle = elListItem.vm.titleStyle
                    if (tableTitleStyle) {
                      const color = tableTitleStyle.type === 'gradient' ? tableTitleStyle.gradientColor[0] : tableTitleStyle.color
                      obj.data.tableTitleStyle = {
                        'text-decoration': tableTitleStyle['textDecoration'],
                        'font-family': tableTitleStyle['fontFamily'],
                        'font-size': tableTitleStyle['fontSize'],
                        'font-style': tableTitleStyle['fontStyle'],
                        'font-weight': tableTitleStyle['fontWeight'],
                        color
                      }
                    }
                  }

                  // 删除表格组件内导出数据的引用
                  const tempData = obj.data.data
                  val.data.data = null
                  obj.data.data = tempData
                  obj.type = TYPE_ELEMENT.TABLE
                  this.requestList.push(this.$_JSONClone({ ...obj, isSheet, sheetName }))
                                    resolve()
                }
                vm.print(!this.isSinglePage, callBack)
              }
            }
          }

          // 是否有日期维度导出
          if (isHasDateDimensionElement) {
            // 简单表格
            if (isVeGridNormal(elListItem)) {
              const vm = elListItem.vm
              const orginSelectedDateVal = vm.selectedDateVal
              const dateDimensionValList = vm?.dateDimensionValList

              const changeDateDimensionVal = (vm, value = '') => {
                vm.selectedDateVal = value
                vm.changeDateDimensionVal()
              }

              let callLen = dateDimensionValList.length - 1

              const deepCall = async (item) => {
                changeDateDimensionVal(vm, item.value)
                await new Promise<void>((resolve) => {
                  this.exportRequestFinishCallback = () => {
                    setTimeout(() => {
                      tableDownCall(true, `${getChioceTabName(elListItem, index)}_${item.label}`, resolve)
                    }, 2000)
                      // 调用完重置
                    this.exportRequestFinishCallback = () => {}
                  }
                })

                callLen--
                if (callLen >= 0) {
                  await deepCall(dateDimensionValList[callLen])
                } else {
                  changeDateDimensionVal(vm, orginSelectedDateVal)
                }
              }

              if (callLen >= 0) {
                await deepCall(dateDimensionValList[callLen])
              }
            } else {
              // 普通表格
              const orginDateSwitchValue = vm.dateSwitchValue
              const dateSwitchList = vm?.dateSwitchList

              let callLen = dateSwitchList.length - 1

              const deepCall = async (item) => {
                vm.dateSwitchChange(item.value)

                await new Promise<void>((resolve) => {
                  this.exportRequestFinishCallback = () => {
                    setTimeout(() => {
                      tableDownCall(true, `${elListItem.elName}_${item.label}`, resolve)
                    }, 1000)

                    this.exportRequestFinishCallback = () => {}
                  }
                })

                callLen--
                if (callLen >= 0) {
                  await deepCall(dateSwitchList[callLen])
                } else {
                  vm.dateSwitchChange(orginDateSwitchValue)
                }
              }

              if (callLen >= 0) {
                await deepCall(dateSwitchList[callLen])
              }
            }
          } else {
            await new Promise<void>((resolve) => {
                tableDownCall(false, '', resolve)
            })
          }
        }

        if (type === TYPE_ELEMENT.TABLE) {
          await tableDown(elListItem)
        } else if (type === TYPE_ELEMENT.CHART) {
          const chartDown = async (elListItem, index?) => {
            obj.type = elListItem.type
            // 是否包含日期维度切换
            const isHasDateDimensionElement = this.isExportAllDateData && isHasDateDimension(elListItem)

              // 处理图形导出
            const chartDownCall = async (isSheet, label = '') => {
              const option = this.isSinglePage || this.isContainer || this.isAdvanceContainer ? {
                img: this.isExportChartImg,
                data: this.isExportChartData,
              } : {}
              const requestElement = await elListItem.vm.savePrintData(option)
              if ((!requestElement || !Object.keys(requestElement).length) && !allowsEmptyExport) {
                this.exportHints()
                return
              }
              if (allowsEmptyExport) {
                const imgBase64 = await this.getHtml2canvas(id)
                exportTableElement = {
                  chartData: { dataURL: imgBase64 ? imgBase64.toDataURL('image/png') : '' }
                }
              } else {
                exportTableElement = {
                  chartData: { ...(requestElement.chartData), dataURL: requestElement.dataURL, _chartResponseTmp: requestElement._chartResponseTmp }
                }
              }
              Object.assign(obj, {
                data: exportTableElement
              }, isSheet ? { sheetName: `${getChioceTabName(elListItem, index)}_${label}`, isSheet } : { isSheet: false, sheetName: '' })
              this.requestList.push(this.$_JSONClone(obj))
            }

            // 是否有日期维度导出
            if (isHasDateDimensionElement) {
              const vm = elListItem.vm
              const orginSelectedDateVal = vm.selectedDateVal

              const one = vm?.dateDimensionValList.find(item => {
                return item.value === orginSelectedDateVal
              })

              const dateDimensionValList = [one, ...(vm?.dateDimensionValList.filter((item: { value: any }) => item.value !== orginSelectedDateVal) || [])]

              const changeDateDimensionVal = (vm, value = '') => {
                vm.selectedDateVal = value
                vm.changeDateDimensionVal()
              }

              const deepCall = async (item) => {
                if (item.value === orginSelectedDateVal) {
                  await chartDownCall(true, item.label)
                } else {
                  changeDateDimensionVal(vm, item.value)

                  await new Promise<void>((resolve) => {
                    this.exportRequestFinishCallback = () => {
                        setTimeout(() => {
                          chartDownCall(true, item.label).then(resolve)
                        }, 5000)

                        // 调用完重置
                        this.exportRequestFinishCallback = () => {}
                    }
                  })
                }

                const data = dateDimensionValList.shift()
                if (data) {
                  await deepCall(data)
                } else {
                  changeDateDimensionVal(vm, orginSelectedDateVal)
                }
              }

              const data = dateDimensionValList.shift()
              if (data) {
                await deepCall(data)
              }
            } else {
              await chartDownCall(false)
            }
          }

          // 指标
          if (isChioceTab) {
            const vm = elListItem.vm
            const checkedDimension = vm.checkedDimension
            const chioceTabIndexs = chioceTabIndex.includes(checkedDimension) ? [...new Set([checkedDimension, ...chioceTabIndex])] : chioceTabIndex

            const deepCall = async (index) => {
              if (index === checkedDimension) {
                if (isVeGridNormal(elListItem)) {
                  await tableDown(elListItem, index)
                } else {
                  await chartDown(elListItem, index)
                }
              } else {
                await new Promise<void>((resolve) => {
                    let unwatch = this.$watch('isFinish', (val) => {
                        if (val) {
                            vm.handleChoiceTab(index)

                            typeof unwatch === 'function' && unwatch()
                        }
                      }, {
                        immediate: true
                    })

                    this.exportRequestFinishCallback = () => {
                        setTimeout(() => {
                          // 切换加入日期维度数量
                          if (this.isExportAllDateData && isHasDateDimension(elListItem)) {
                            this.printElementLeng += getDateDimensionNum(elListItem)
                          }

                          if (isVeGridNormal(elListItem)) {
                            tableDown(elListItem, index).then(resolve)
                          } else {
                            setTimeout(() => {
                              chartDown(elListItem, index).then(resolve)
                            }, 3000)
                          }
                        }, 1000)

                        // 调用完重置
                        this.exportRequestFinishCallback = () => {}
                    }
                })
              }

              const data = chioceTabIndexs.shift()
              if (data !== undefined) {
                await deepCall(data)
              } else {
                await new Promise<void>((resolve) => {
                  let unwatch = this.$watch('isFinish', (val) => {
                    if (val) {
                        vm.handleChoiceTab(checkedDimension)
                        resolve()

                        typeof unwatch === 'function' && unwatch()
                    }
                  }, {
                    immediate: true
                  })
                })
              }
            }

            const data = chioceTabIndexs.shift()
            if (data !== undefined) {
              await deepCall(data)
            }
          } else {
            if (isVeGridNormal(elListItem)) {
              await tableDown(elListItem)
            } else {
              await chartDown(elListItem)
            }
          }

        } else if (type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
          exportTableElement = await this.getCardRequest(elListItem)
          if (exportTableElement) {
            Object.assign(obj, { data: exportTableElement })
            this.requestList.push(obj)
          }
        } else if (type === TYPE_ELEMENT.TEXT || isWeAndTimeAndScrollText || type === TYPE_ELEMENT.ELEMENT_TITLE || type === TYPE_ELEMENT.COMBINE_CARD) {
          exportTableElement = await this.getTextRequest(elListItem)
          if (exportTableElement) {
            Object.assign(obj, { data: exportTableElement })
            this.requestList.push(obj)
          }
        } else if (type === TYPE_ELEMENT.IMAGE) {
          const imgURL = elContent.url
          Object.assign(obj, { data: { chartData: { dataURL: imgURL } } })
          this.requestList.push(obj)
        } else if (type === TYPE_ELEMENT.FOUR_QUADRANT) {
          const requestElement = elContent.response
          if (!requestElement || (!requestElement.length && !allowsEmptyExport)) {
            this.exportHints()
            return
          }
          // 多合一表格导出需要百分比格式
          const imgBase64 = await this.getHtml2canvas(id)
          exportTableElement = {
            dataURL: imgBase64 ? imgBase64.toDataURL('image/png') : '',
          }
          Object.assign(obj, { data: { chartData: exportTableElement } })
          this.requestList.push(obj)
        } else if (type === TYPE_ELEMENT.CUSTOMER_ELEMENT) {
          let typeTemp = 'chart'
          let data: any = null
          // 自定义元素导出
          await new Promise<void>((resolve) => {
            this.sdpBus.$emit(EVENT_BUS.CUSTOMR_EXPORT, id, this.exportType, (typeName, tableData) => {
              typeTemp = typeName
              data = tableData ? {
                data: tableData,
                type: TYPE_ELEMENT.TABLE,
                elName: elListItem.elName,
                layout: this.getElOrderByLayout(elListItem),
                dynamicTagsId: elListItem?._dynamicTags?.id,
                id: elListItem.id,
                isTable: true,
              } : null

              resolve()
            })
          })
          if (typeTemp === 'table') {
            data ? this.requestList.push(this.$_JSONClone({ ...obj, ...data })) : this.exportHints()
          } else {
            const imgBase64 = await this.getHtml2canvas(id)
            exportTableElement = {
              dataURL: imgBase64 ? imgBase64.toDataURL('image/png') : '',
            }
            Object.assign(obj, { data: { chartData: exportTableElement } })
            this.requestList.push(obj)
          }

        } else if (type === TYPE_ELEMENT.DUPONT_ANALYSIS) {
          const cardDataLen = elContent.cardData.length
          const imgBase64 = await this.getHtml2canvas(id)
          if (!imgBase64 || !cardDataLen) {
            this.exportHints()
            return
          }
          Object.assign(obj, { data: {
            chartData: {
              dataURL: imgBase64 ? imgBase64.toDataURL('image/png') : '',
            },
          } })
          this.requestList.push(obj)
        }
      }
    },
    nextExcelExport(isFirst = false) {
      if (this.currentParamPanel < this.paramsPanelList.length && this.openBoardTab) {
        this.requestList = []
        this.broadcast('tab', 'onPanelChange', { data: this.paramsPanelList[this.currentParamPanel] })
        if (isFirst) {
          this.getAllElRequestData()
          this.currentParamPanel++
        } else {
          this.waitingForUse = () => {
            this.getAllElRequestData()
            this.currentParamPanel++
          }
        }
      } else {
        this.requestList = []
        this.currentParamPanel = 0
        this.waitingForUse = undefined
      }
    },
    nextCsvExport(isFirst = false, args = {}) {
      if (this.currentParamPanel < this.paramsPanelList.length && this.openBoardTab) {
        this.broadcast('tab', 'onPanelChange', { data: this.paramsPanelList[this.currentParamPanel] })
        if (isFirst) {
          this._doExcelCsv(args)
          this.currentParamPanel++
        } else {
          this.waitingForUse = () => {
            this._doExcelCsv(args)
            this.currentParamPanel++
          }
        }
      } else {
        this.currentParamPanel = 0
        this.waitingForUse = undefined
      }
    },
    setSingleExportData(eventData) {
      const isSingleExport = [ELEMENT_EXPORT_DATA_TYPE.singlePage, ELEMENT_EXPORT_DATA_TYPE.advanceContainer, ELEMENT_EXPORT_DATA_TYPE.container].includes(eventData?.data?.type)

      if (eventData?.data?.type === ELEMENT_EXPORT_DATA_TYPE.singlePage) {
        this.isSinglePage = true
        this.isExportChartImg = eventData.data.isExportChartImg
        this.isExportChartData = eventData.data.isExportChartData
        this.printElement = [eventData.data.element]
      }
      if (eventData?.data?.type === ELEMENT_EXPORT_DATA_TYPE.container) {
        this.isContainer = true
        this.printElement = eventData.data.excitList
        this.isExportChartImg = eventData.data.isExportChartImg
        this.isExportChartData = eventData.data.isExportChartData
      }
      if (eventData?.data?.type === ELEMENT_EXPORT_DATA_TYPE.advanceContainer) {
        this.isAdvanceContainer = true
        this.printElement = eventData.data.excitList
        this.tabsSelects = eventData.data.tabsSelects
        this.isExportChartImg = eventData.data.isExportChartImg
        this.isExportChartData = eventData.data.isExportChartData
      }
      this.isExportAllDateData = eventData?.data?.isExportAllDateData
      this.chioceTab = eventData?.data?.chioceTab || null
      isSingleExport && (this.isOpenWatermark = eventData?.data?.isOpenWatermark || false)

      let num = this.printElement?.length

      if (this.isExportAllDateData) {
        this.printElement.forEach(element => {
          num += getDateDimensionNum(element)
        })
      }

      this.printElementLeng = num

      return isSingleExport
    },
    setExportType({
      data: {
        nodeExportData, isOpenChioceTab, isOpenWatermark
      }
    }) {
      this.nodeExportData = nodeExportData
      this.isOpenChioceTab = isOpenChioceTab
      this.isOpenWatermark = isOpenWatermark
    },
    paramAddNodeExportData(param) {
      if (!this.nodeExportData) return param

      return Object.assign({}, param, this.nodeExportData)
    },
    // -----------------------------------------
    // 导出excel的逻辑,包括后台特殊导出excel的逻辑
    doExportExcel(eventData) {
      const isSubscribe = this.commonData.isSubscribe
      const isSingleExportData = this.setSingleExportData(eventData)
      this.isSubscribe = isSubscribe
      if (isSubscribe) this.currentParamPanel = 0
      console.log('=======doExportExcel=========')
      this.requestList = []
      this.exportType = FILE_TYPE_KEY.excel
      this.exporting = true
      this.commonData.setOpenRunState(true)
      this.generateFileName()
      if (isSubscribe && this.openBoardTab && !isSingleExportData) { // 只有在开启标签页的时候才走依序导出
        this.nextExcelExport(true)
      } else {
        this.getAllElRequestData()
      }
      this.format = 'xlsx'
    },
    singleElementExportExcel(id) {
      const targetElement = this.elList.find(el => el.id === id)
      if (!targetElement) {
        // 无对应元素，需要报失败给后台 todo
        return
      }
      const isContainer = targetElement.type === TYPE_ELEMENT.CONTAINER
      const isAdvanceContainer = isContainer && targetElement.subType === TYPE_ELEMENT.ADVANCE_CONTAINER

      let type = ELEMENT_EXPORT_DATA_TYPE.singlePage
      isAdvanceContainer && (type = ELEMENT_EXPORT_DATA_TYPE.advanceContainer)
      isContainer && (type = ELEMENT_EXPORT_DATA_TYPE.container)

      const commonEventData = {
        format: 'excel',
        type: type,
        isExportAllDateData: false,
        isOpenWatermark: false,
        isExportChartImg: true,
        isExportChartData: false,
      }
      // 如果是普通元素 传element, chioceTab: { [this.element.id]: [] }
      // 如果是容器 传excitList  chioceTab: {}
      // 如果是高级容器 传excitList  tabsSelects   chioceTab:{}
      let eventData
      if (isContainer) {
        if (isAdvanceContainer) {
          const getChartChioceTab = (element) => {
            if (element.type === TYPE_ELEMENT.CHART) {
              return this.$_JSONClone(element.content?.chioceTab || [])
            }
            return []
          }

          const getIncludeEl = (includedElsIdList, el) => this.utils.isLargeScreen ? includedElsIdList.includes(el.id) && el.type === TYPE_ELEMENT.TABLE : includedElsIdList.includes(el.id)

          const getTabsSelectItem = (item) => {
            const { title, content } = item
            const isIndeterminate = false
            let checkAll = true
            let elLists = this.elList.filter(el => getIncludeEl(content.includedElsIdList, el))
            let checkedEl = []
            elLists.forEach((element) => {
              checkedEl.push(element)

              const chioceTab = getChartChioceTab(element)
              if (chioceTab.length) {
                const data = chioceTab.map(el => {
                  el.elName = el.name
                  el.parentId = element.id
                  return el
                })

                checkedEl.push(...data)
              }
            })
            elLists = checkedEl

            return {
              checkAll,
              isIndeterminate,
              title: this.element.content.settings.elAlias[item.name] || title,
              elLists,
              checkedEl: checkedEl.filter(e => e.id === id),
            }
          }

          const tabsSelects = tabList.filter((item) => {
            const { content } = item
            return this.elList.some(el => getIncludeEl(content.includedElsIdList, el))
          }).map((item) => getTabsSelectItem(item))

          eventData = {
            data: Object.assign({}, commonEventData,  {
              checkList: this.elList.filter(e => e.id === id),
              tabsSelects,
              chioceTab: {}
            })
          }
        } else {
          eventData = {
            data: Object.assign({}, commonEventData, {
              checkList: this.elList.filter(e => e.id === id),
              chioceTab: {}
            })
          }
        }
      } else {
        eventData = {
          data: Object.assign({}, commonEventData, {
            element: targetElement,
            chioceTab: {
              [targetElement.id]: []
            }
          })
        }
      }

      this.doExportExcel(eventData)
    },
    setGradientShowHidden(isSow = true) {
      if (isSow) {
        document.querySelectorAll('.gradient').forEach(dom => {
          dom.classList.remove('no-gradient')
        })
      } else {
        document.querySelectorAll('.gradient').forEach(dom => {
          !dom.classList.contains('no-gradient') && dom.classList.add('no-gradient')
        })
      }
    },
    // 导出Word的逻辑
    async doExportWord(eventData) {
      console.log('=======doExportWord=========', eventData)
      if (eventData.source === 'fileExport') {
        // init
        this.wordExportIds = []
        this.wordExportCnt = 0
        this.exporting = true
        this.commonData.setOpenRunState(true)
      }
      this.requestList = []
      this.exportType = FILE_TYPE_KEY.word
      this.generateFileName()
      this.setGradientShowHidden(false)

      this.format = 'docx'

      this.wordExportFun()
    },
    // 数据报告Word导出 进入不同的tab运行获取参数的并导出下载的主要逻辑部分
    async wordExportFun() {
      const pageTabId = this.dynamicTagsActiveId
      const tabId = this.paramsPanelList.find(e => e.active)?.id

      const wordExportParams = await this.getWordExportParams(pageTabId)

      // 订阅附件大数据导出目前不支持导出多个，文件下载
      if (this.isExportNode && this.nodeExportData) {
        exportExcelSheet2(this.api, {
          ...this.nodeExportData,
          dashboardId: this.dashboardId,
          fileType: FILE_TYPE[FILE_TYPE_KEY.word],
          wordExportDTO: wordExportParams
        }).then(() => {
          this.$emit('onExportSuccess')
        }).catch(() => {
          this.$emit('onExportFail')
        })
        return
      }

      // 增加一个cnt限制次数，以防死循环
      this.wordExportIds.push(this.openBoardTab ? tabId : pageTabId)
      this.wordExportCnt++

      const tabList = this.openBoardTab ? this.paramsPanelList.map(e => e.id) : this.dynamicTags.map(e => e.id)
      const isEnd = tabList.every(tab => this.wordExportIds.includes(tab)) || this.wordExportCnt >= 6
      if (!isEnd) {
        // 切换tab
        const nextTabId = tabList.find(e => !this.wordExportIds.includes(e))
        if (this.openBoardTab) {
          const tab = this.paramsPanelList.find(e => e.id === nextTabId)
          tab && this.broadcast('tab', 'onPanelChange', { data: tab })
        } else {
          this.setDynamicTagsActiveById(nextTabId)
        }

        // 运行导出
        this.run({
          data: { options: { exportType: 'Word' } },
          source: 'export',
          options: {
            derivedFun: () => {
              this.setGradientShowHidden(false)
              this.doExportWord({ source: 'export' })
            }
          }
        })
      } else {
        const nextTabId = this.wordExportIds[0]
        if (this.openBoardTab) {
          const tab = this.paramsPanelList.find(e => e.id === nextTabId)
          tab && this.broadcast('tab', 'onPanelChange', { data: tab })
        } else {
          this.setDynamicTagsActiveById(nextTabId)
        }

        const ids = this.dynamicTags.find(e => e.active)?.id || []
        this.refreshEl({ ids: ids })
      }

      exportWord(this.api, wordExportParams)
        .then((res) => {
          this.downloadFile(res, this.format)

          this.$emit('onExportSuccess')
        })
        .catch(() => {
          this.$emit('onExportFail')
        })
        .finally(() => {
          if (!isEnd) return
          this.exporting = false
          this.commonData.setOpenRunState(false)
          this.commonData.setExportLoading(false, false)
          this.setGradientShowHidden(true)

          this.isSinglePage = false
          this.isContainer = false
          this.isAdvanceContainer = false
          this.isExportAllDateData = false
          this.chioceTab = null
        })
    },
    // 数据报告Word导出 获取拼接参数
    async getWordExportParams(tabId = this.dynamicTagsActiveId) {
      const wordExportParams = {
        dashboardCode: '',
        dashboardName: this.boardInfo.name,
        tenantId: this.utils.tenantId,
        exportStyle: {},
        wordPageContentDTOList: [],
        wordPageSetDTO: {
          direction: '1',
          pageFootFormat: 'SignNumberSign',
          pageSize: 'A4',
          pageWidth: 21,
          pageHeight: 29.7,
        },
      }

      this.openBoardTab ? wordExportParams.dashboardCode = `${this.boardInfo.code} (${this.boardData.paramsPanelList.find(v => v.active).label})` : wordExportParams.dashboardCode = this.boardInfo.code

      // const tabId = this.dynamicTagsActiveId
      const reportSettings = this.dataReport?.getReportSettings(tabId, { deepClone: true })
      const padding = this.dataReport?.getReportPadding()
      const wordPageSetDTO = {
        direction: '1', // 1 纵向 2 横向
        pageFootFormat: this.boardInfo.wordExport.isPage && reportSettings.pageFormat !== PageFormat.None ? reportSettings.pageFormat : undefined,
        pageSize: reportSettings.pageSize,
        pageWidth: reportSettings.pageWidth || 21,
        pageHeight: reportSettings.pageHeight || 29.7,
        marginTop: padding.top,
        marginBottom: padding.bottom,
        marginLeft: padding.left,
        marginRight: padding.right,
      }

      // 这一段可有可无
      if (wordPageSetDTO.pageSize !== PageSize.Custom) {
        delete wordPageSetDTO.pageWidth
        delete wordPageSetDTO.pageHeight
      }

      Object.assign(wordExportParams.wordPageSetDTO, wordPageSetDTO)

      // 获取所有页面ID
      const pageList = this.dataReport.getPageOrder(tabId)
      const wordPageContentDTOList = []
      for (const pageId of pageList) {
        const page = this.dataReport.getPage(pageId)
        const { elIdList = [] } = page
        wordPageContentDTOList.push({
          wordElementDTOList: await this.getWordElementDTOListByElIdList(elIdList)
        })
      }

      wordExportParams.wordPageContentDTOList = wordPageContentDTOList

      return wordExportParams
    },
    // 数据报告Word导出 根据元素类型获取元素参数
    async getWordElementDTOListByElIdList(elIdList) {
      const wordElementDTOList = []

      await new Promise<void>((resolve) => {
        let customerElements = this.elList.filter(el => el.type === TYPE_ELEMENT.CUSTOMER_ELEMENT).map(el => el.id)

        if (!customerElements.length) {
          resolve()
          return
        }
        this.sdpBus.$emit(EVENT_BUS.CUSTOMR_EXPORT, undefined, this.exportType, (id) => {
          customerElements = customerElements.filter(elId => elId !== id)

          if (!customerElements.length) {
            resolve()
          }
        })
      })

      for (const elId of elIdList) {
        const element = this.elList.find(e => e.id === elId)
        if (!element || element._containerId) continue

        const { type, id, styleConfig, zIndex, themeMap } = element

        if (TO_IMG_ELEMENT_LIST.includes(type)) {
          let imgBase64
          //   imgBase64 = element?.vm?.echartInstance?.getDataURL?.({pixelRatio: 8})
            let old
            if (type === TYPE_ELEMENT.TABLE) {
              old = element.scale
              element.scale = 10
              element.vm?.$refs?.tableRender?.sdpSheet?.setScale?.(element.scale)?.render?.()
            }

            this.setElementTitleShow(element, false)

            imgBase64 = await this.getHtml2canvas(id, true)

            this.setElementTitleShow(element)

            if (type === TYPE_ELEMENT.TABLE) {
              old ? element.scale = old : delete element.scale
              element.vm?.$refs?.tableRender?.sdpSheet?.setScale?.(element.scale || 1)?.render?.()
            }

          if (imgBase64) {
            // const base64Url = type === TYPE_ELEMENT.CHART ? imgBase64 : imgBase64.toDataURL('image/png')
            const base64Url = imgBase64.toDataURL('image/png')
            const wordElementDTO = {
              elementType: '1',
              wordPicDTO: {
                base64: base64Url,
                width: styleConfig.size.width,
                height: styleConfig.size.height,
                x: styleConfig.position.left,
                y: styleConfig.position.top
              },
              zIndex
            }
            wordElementDTOList.push(wordElementDTO)

            this.getWordElementTitleDto(element, wordElementDTOList)
          }
        } else if (IMG_ELEMENT_LIST.includes(type)) {
          const imgURL = element.content.url
          const wordElementDTO = {
            elementType: '1',
            wordPicDTO: {
              base64: imgURL,
              width: styleConfig.size.width,
              height: styleConfig.size.height,
              x: styleConfig.position.left,
              y: styleConfig.position.top
            },
            zIndex
          }
          wordElementDTOList.push(wordElementDTO)
        } else if (TO_TEXT_ELEMENT_LIST.includes(type)) {
          const alignKey = type === TYPE_ELEMENT.ELEMENT_TITLE && element.content?.sonType !== ELEMENT_TITLE_SONTYPE.TITLE_LEVEL_TEXT ? 'titleStyle' : 'textStyle'
          const alignment = element.content[alignKey]['text-align'] || 'Left'
          const piddingLeft = element.content[alignKey]['padding-left'] || '0px'

          const wordElementDTO = {
            elementType: '0',
            wordTextBoxDTO: {
              alignment: alignment[0].toUpperCase() + alignment.substr(1),
              leftIndent: parseInt(piddingLeft),
              wordTextDTOList: this.getWordTextBoxById(id),
              background: themeMap[this.themeData.themeType]?.imgData ? '' : themeMap[this.themeData.themeType]?.pcBgc,
              width: styleConfig.size.width,
              height: styleConfig.size.height,
              x: styleConfig.position.left,
              y: styleConfig.position.top
            },
            zIndex
          }

          if (styleConfig.padding) {
            Object.assign(wordElementDTO.wordTextBoxDTO, {
              paddingLeft: styleConfig.padding.left,
              paddingRight: styleConfig.padding.right,
              paddingTop: styleConfig.padding.top,
              paddingBottom: styleConfig.padding.bottom,
            })
          }

          wordElementDTOList.push(wordElementDTO)
        }
      }

      return wordElementDTOList.sort((a, b) => b.zIndex - a.zIndex)
    },
    setElementTitleShow(element, show = true) {
      const { type, id } = element
      if (EXPORT_TEXT_TITLE_LIST.includes(type)) {
        const titleDom = document.getElementsByClassName(`export-title-${id}`)
        ;[...titleDom].forEach(title => {
          title.style.opacity = show ? '' : '0'
        })
      }
    },
    getWordElementTitleDto(element, wordElementDTOList) {
      const { type, id, styleConfig, zIndex, themeMap } = element

      if (EXPORT_TEXT_TITLE_LIST.includes(type)) {
        const list = []
        const titleDom = document.getElementsByClassName(`export-title-${id}`)
        if (titleDom.length) {
          this.getContentTextDto(list, titleDom)
          console.log('getWordElementTitleDto >', list)
          if (!list.length) { return }

          const wordElementDTO = {
            elementType: '0',
            wordTextBoxDTO: {
              alignment: 'Left',
              leftIndent: 4,
              wordTextDTOList: list,
              background: 'rgba(255, 255, 255, 0)',
              width: styleConfig.size.width,
              height: 36,
              x: styleConfig.position.left,
              y: styleConfig.position.top + 4
            },
            zIndex: zIndex + 1
          }

          wordElementDTOList.push(wordElementDTO)
        }
      }
    },
    // 数据报告Word导出 获取元素文本的样式
    getDomStyle(dom) {
      if (!dom.style) return {}

      const FontExculdeList = [
        'Roboto-Bold',
        'Roboto-Regular',
        'Robot-Light',
        'NotoSansHans-Bold',
        'NotoSansHans-Regular',
      ]

      const defaultFontFamily = 'Microsoft Yahei'

      const fontSize = isNaN(parseInt(dom.style['fontSize'])) ? undefined : dom.style['fontSize']

      const style = {
        'font-weight': dom.style['fontWeight'] || undefined,
        'color': dom.style['color'] || undefined,
        'text-decoration': dom.style['textDecoration'] || undefined,
        'font-family': dom.style['fontFamily'] || undefined,
        'font-size': fontSize || undefined,
        'font-style': dom.style['fontStyle'] || undefined,
      }

      if (dom.nodeName === 'FONT') {
        if (dom.color) {
          style.color = dom.color
        }
        if (dom.face) {
          style['font-family'] = dom.face
        }
      }

      const font = dom.querySelectorAll('font')?.[0]
      if (font && font.face) {
        style['font-family'] = font.face
      }

      if (FontExculdeList.includes(style['font-family'])) {
        style['font-family'] = defaultFontFamily
      }

      for (let key of Object.keys(style)) {
        if (!style[key]) delete style[key]
      }

      return style
    },
    // 数据报告Word导出 元素文本数组获取的递归
    getContentTextDto(wordTextDTOList, list, style = {}) {
      console.log('list: ', list)
      for (let item of list) {
        if (['DIV', 'P'].includes(item.nodeName)) {
          // 啥也不需要，没用的东西
          if (item.nodeName === 'P' && ![0, 2].includes(wordTextDTOList.length)) {
            wordTextDTOList.push(this.transformTextAndStyle({
              text: `\n`,
              style: Object.assign({}, style, this.getDomStyle(item))
            }))
          }
        } else if (['BR'].includes(item.nodeName)) {
          // wordTextDTOList.push(this.transformTextAndStyle({
          //   text: `\n`,
          //   style: Object.assign({}, style, this.getDomStyle(item))
          // }))
        } else {
          if (item.data) {
            wordTextDTOList.push(this.transformTextAndStyle({
              text: item.data,
              style: Object.assign({}, style, this.getDomStyle(item))
            }))
          }
        }

        if (item.childNodes && item.childNodes.length) {
          this.getContentTextDto(wordTextDTOList, item.childNodes, Object.assign({}, style, this.getDomStyle(item)))
        }
      }
    },
    // 数据报告Word导出 通过元素id获取元素文本 --仅针对文本框元素
    getWordTextBoxById(elId) {
      const wordTextDTOList = []
      const element = this.elList.find(e => e.id === elId)
      if (!element) return wordTextDTOList

      const isTitle = element.type === TYPE_ELEMENT.ELEMENT_TITLE && element.content?.sonType !== ELEMENT_TITLE_SONTYPE.TITLE_LEVEL_TEXT
      const { themeConfig, title, titleStyle, textStyle, orderStyle, richTextHtml, titleHtml, orderHtml } = element.content
      const colorConfig = themeConfig[this.themeData.themeType]

      if (!isTitle) {
        if (title) {
          wordTextDTOList.push(this.transformTextAndStyle({
            text: title,
            style: Object.assign({}, titleStyle, colorConfig?.titleStyle || {})
          }))

          if (!richTextHtml) return wordTextDTOList

          wordTextDTOList.push(this.transformTextAndStyle({
            text: '\n',
            style: Object.assign({}, titleStyle, colorConfig?.titleStyle || {})
          }))
        }
      } else {
        if (this.isOrderTitle(element) && orderHtml) {
          const orderDom = document.getElementById(`sdp-element-order-richText-${elId}`)
          if (orderDom) {
            this.getContentTextDto(wordTextDTOList, [orderDom], orderStyle)
          }

          wordTextDTOList.push(this.transformTextAndStyle({
            text: ' ',
            style: Object.assign({}, orderStyle, colorConfig?.orderStyle || {})
          }))
        }
      }

      // 通过childNodes获取 递归获取文本和样式
      const contentDom = document.getElementById(`sdp-element-text-richText-${elId}`)
      console.log('contentDom>', contentDom)
      console.log('wordTextDTOList>', wordTextDTOList)

      if (contentDom) {
        this.getContentTextDto(wordTextDTOList, [contentDom], isTitle ? titleStyle : textStyle)
      }

      if (isTitle && this.isOrderTitle(element) && orderHtml) {
        const delIndex = wordTextDTOList.findIndex(e => e.text === '\n')
        delIndex !== -1 && wordTextDTOList.splice(delIndex, 1)
      }

      return wordTextDTOList
    },
    // 数据报告Word导出 元素样式转化参数
    transformTextAndStyle(wordTextObj) {
      const { text, style } = wordTextObj
      const FontExculdeList = [
        'Roboto-Bold',
        'Roboto-Regular',
        'Robot-Light',
        'NotoSansHans-Bold',
        'NotoSansHans-Regular',
      ]

      const defaultFontFamily = 'Microsoft Yahei'

      const wordTextDTO = {
        'bold': style['font-weight'] === 'bold' || false,
        'color': style['color'],
        'deleteline': style['text-decoration'] === 'line-through' || false,
        'fontName': FontExculdeList.includes(style['font-family']) ? defaultFontFamily : style['font-family'],
        'fontSize': (parseInt(style['font-size']) * 0.75) || 9, // px -> word: 1px = 0.75pt
        'italic': style['font-style'] === 'italic' || false,
        'text': text,
        'underline': style['text-decoration'] === 'underline' || false,
        'background': style['background-color'] || false,
      }

      return wordTextDTO
    },
    isOrderTitle(element) {
      const type = element?.content?.sonType || ''
      return [
        ELEMENT_TITLE_SONTYPE.TITLE_ORDER_1,
        ELEMENT_TITLE_SONTYPE.TITLE_ORDER_2,
        ELEMENT_TITLE_SONTYPE.TITLE_ORDER_4,
        ELEMENT_TITLE_SONTYPE.TITLE_ORDER_5,
        ELEMENT_TITLE_SONTYPE.TITLE_ORDER_6,
      ].includes(type)
    },
    generateFileName() {
      if (this.commonData.isSubscribe) {
        let subscribeObj = this.commonData.subscribeObj()
        this.fileName = subscribeObj.boardName ? (this.openBoardTab ? `${subscribeObj.boardName}(${this.boardData.paramsPanelList.find(v => v.active).label})` : subscribeObj.boardName) : this.$_generateCode()
      } else {
        this.fileName = this.boardInfo.code ? (this.openBoardTab ? `${this.boardInfo.code}(${this.boardData.paramsPanelList.find(v => v.active).label})` : this.boardInfo.code) : this.$_generateCode()
      }
    },
    // ellist排序(容器排序)
    orderElList() {
      let containerList = this.elList.filter(v => v.type === 'chartContainer')
      let containerTableList = []
      let res = []
      containerList.forEach(item => {
        // item.content.includedElIds 排序
        let arr = this.elList.filter(v => v._containerId === item.id)
        let orderArr = []
        if (item.subType === 'advance-container') {
          item.content.tabList.map(v => {
            v.content.includedElsIdList.map(i => {
              if (arr.find(k => k.id === i)) {
                orderArr.push(arr.find(k => k.id === i))
              }
            })
          })
        } else {
          orderArr = item.content.includedElIds.map(v => {
            return arr.find(k => k.id === v)
          })
        }
        if (arr.length) containerTableList.push(orderArr)
      })
      res = this.elList.filter(v => !v._containerId)
      containerTableList.forEach(item => {
        res = res.concat(item)
      })
      return res
    },
    // csv的逻辑
    doExcelCsvPdf(args = {}) {
      const isSubscribe = this.commonData.isSubscribe
      this.isSubscribe = isSubscribe
      this.currentParamPanel = 0 // 每次主动触发需要重置状态
      this.waitingForUse = undefined
      this.exportType = FILE_TYPE_KEY.csv
      this.exporting = true
      this.commonData.setOpenRunState(true)
      this.format = 'csv'
      if (isSubscribe && this.openBoardTab) {
        this.nextCsvExport(true, args)
      } else {
        this._doExcelCsv(args)
      }
    },
    getPrintParamsFormatterData(elListItem) {
      const isSinglePage = this.isSinglePage || this.isContainer || this.isAdvanceContainer
      const vm = elListItem.content.alias === 've-grid-normal'
        ? elListItem.vm.$refs.grid
        : elListItem.vm
      const params = vm.printParamsFormatter({
        isSinglePage,
        printElement: this.printElement
      })
      let obj = {
        data: {},
        type: TYPE_ELEMENT.TABLE,
        elName: elListItem.elName,
        layout: this.getElOrderByLayout(elListItem),
        dynamicTagsId: elListItem?._dynamicTags?.id,
        id: elListItem.id,
        params
      }
      return obj
    },
    getElOrderByLayout(elListItem) {
      let layout = elListItem.layout || {}
      if (elListItem._containerId) {
        let containerEl = this.elList.find(v => v.id === elListItem._containerId)
        if (containerEl) {
          if (containerEl.subType === 'advance-container') {
            const currentTab = containerEl.content?.tabList?.findIndex(t => t.content?.includedElsIdList?.includes(elListItem.id)) || 0
            layout = {
              x: containerEl.layout.x,
              y: containerEl.layout.y,
              elX: elListItem.layout.x,
              elY: elListItem.layout.y,
              dynamicTagsIndex: currentTab,
            }
          } else {
            layout = {
              ...containerEl.layout || {},
              dynamicTagsIndex: containerEl.content.includedElIds.findIndex(c => c === elListItem.id),
            }
          }
        }
      }
      return layout
    },
    getCurrentTag() {
      let key = this.boardData.paramsPanelList.find(v => v.active).id
      return this.boardInfo.tagModeStack.midware[key].content
    },
    async _doExcelCsv(args = {}) {
      const { subscriptionElementId } = args
      this.generateFileName()
      let elList = []
      if (this.openBoardTab) {
        const currentTag = this.getCurrentTag()
        elList = this.elList.filter(v => {
          if (!isSupportCSV(v)) return false
          return currentTag.some(i => i === v.id)
        })
      } else {
        elList = this.elList.filter(v => isSupportCSV(v))
      }
      for (let i = 0; i < elList.length; i++) {
        const elListItem = elList[i]
        const { type, content: elContent, id } = elListItem
        if (subscriptionElementId && id !== subscriptionElementId) continue

        if (type === TYPE_ELEMENT.TABLE || elContent.alias === 've-grid-normal') {
          if (this.isExportNode) {
            const val = this.getPrintParamsFormatterData(elListItem)
            this.exportCsv(val.params, i, elList.length, args)
          } else {
            let callBack = (val) => {

              const tableName = this.$_getProp(elContent, `${elContent.alias === 've-grid-normal' ? 'chartUserConfig' : 'tableDefaultConfig'}.title.text`, '')

              if (tableName) val.data.tableName = tableName

              this.exportCsv(val.data, i, elList.length, args)
            }
            const vm = elContent.alias === 've-grid-normal' ? elListItem.vm.$refs.grid : elListItem.vm
            vm.print(!this.isSinglePage, callBack)
          }
        }
      }
    },
    exportHints(loading = false) {
      this.$message.error(this.$t('sdp.views.setChart'))
      this.$emit('onGroupExportSuccess', '', this.$t('sdp.views.setChart'))
      this.commonData.setExportLoading(loading)
      this.exporting = loading
    },
    nodeRollPolling(id, format, callback, count = 20, time = 5000, type = 'sync') {
      if (this.resourceType) {
        callback()
        return void '不需要下载文件'
      }

      if (!count) {
        callback()
        this.$message.error(this.$t('sdp.views.exportFail'))
        return void '调用次数用完'
      }

      const callDownUrl = () => {
        getDownUrl(this.api, id).then(href => {
          if (href) {
            download(this.api, href).then((res) => {
              this.downloadFile(res, format)
            }).finally(() => {
              callback()
            })

            // fetch(href).then(response => response.blob()).then(blob => {
            //   this.clickDownload(URL.createObjectURL(blob), format)
            //   callback()
            // }).catch(() => {
            //   callback()
            // })
          } else {
            count--
            this.nodeRollPolling(id, format, callback, count, time, 'async')
          }
        })
      }

      if (type === 'sync') {
        callDownUrl()
      } else {
        setTimeout(() => {
          callDownUrl()
        }, time)
      }

    },
    // 导出csv和pdf的处理(csv只会导出表格)
    exportCsv(exportTableElement, index, length, args = {}) {
      if (!this.api) {
        throw new Error('没有传入ajax请求实例')
      }
      const titleStyleObj = Object.assign({}, this.boardInfo.content.nameCss, this.titleStyle)
      if (titleStyleObj) {
        delete titleStyleObj.gradientColor
        delete titleStyleObj.angle
        delete titleStyleObj.type
      }
      let exportData = {
        paramElementList: this.getParamElementLabelList,
        dashboardCode: this.boardInfo.code,
        dashboardName: this.boardInfo.nameSub,
        tenantId: this.utils.tenantId,
        exportStyle: this.boardInfo.exportStyle,
        titleStyle: titleStyleObj,
        [this.isExportNode ? 'parserParamsDTO' : 'tableElementDTO']: exportTableElement
      }
      if (this.isExportNode) {
        exportData.fileType = FILE_TYPE[this.exportType]
        exportData.dashboardId = this.dashboardId
      }
      const callbackApi = this.isExportNode ? exportExcelSheet2 : exportExcel

      const catchCallback = () => {
        this.commonData.setExportLoading(false, false)
        this.exporting = false
        this.$emit('onExportFail')
        // this.$message.error(this.$t('sdp.views.downFailure'))
      }

      const thenCallback = (res) => {
        const callback = () => {
          this.$emit('onExportSuccess')
          this.exporting = false
          this.commonData.setExportLoading(false)
          this.commonData.setOpenRunState(false)
          if ((index === length - 1) && this.isSubscribe) {
            this.nextCsvExport(false, args)
          }
        }

        if (this.isExportNode) {
          this.nodeRollPolling(res, this.format, callback)
        } else {
          this.downloadFile(res, this.format)
          callback()
        }
      }
      Debugging.call([{ parserParams: [exportData.parserParamsDTO] }], this.isExportNode, this.exportType, this.api)
      callbackApi(this.api, this.paramAddNodeExportData(exportData), this.utils.env?.projectName || '').then(thenCallback).catch(catchCallback)
    },
    // 处理后台返回的文档流
    downloadFile(res, suffix, fileName = this.fileName) {
      const blob = new Blob([res], {
        // type: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const _URL = window.URL || window.webkitURL || window || {}

      // if (this.isMobileTablet) {
      //   if (!_URL.createObjectURL) {
      //     this.$message.warning('URL.createObjectURL 不存在')
      //   }
      // }
      this.clickDownload(_URL.createObjectURL(blob), suffix, fileName)
    },
    clickDownload(href, suffix, fileName = this.fileName) {
      // if (this.isMobileTablet) {
      //   const _URL = window.URL || window.webkitURL || window || {}
      //   if (_URL.createObjectURL) {
      //     this.$message({type: 'warning', message: `URL.createObjectURL ${href}`, duration: 20000})
      //   }
      // }
      const link = document.createElement('a')
      link.download = `${fileName}_${this.nowTime}.${suffix}`
      link.style.display = 'none'
      link.href = href
      document.body.appendChild(link)
      link.click()
      link.remove()
    },
    orderRequestList(list) {
      let res: any[] = []
      if (this.orderByProperty === 'position') {
        res = list.map(l => l).sort(this._compareProperty())
      } else {
        let orderArr = CAN_ELEMENT_EXPORT
        orderArr.forEach(v => {
          let tempArr: any[] = []
          list.forEach(k => {
            if (k.type === v) {
              tempArr.push(k)
            }
          })
          tempArr.sort(this._compareProperty())
          res.push(tempArr)
        })
      }

      return res.reduce((acc, val) => acc.concat(val), [])
    },
    // 卡片导出参数的处理
    async getCardRequest(el) {
      const tempArr = el.content.optionArray && el.content.optionArray.map(item => {
        return {
          cardName: item.cardName,
          indexValue: item.indexValue,
          rateCompletion: item.rateCompletion,
          rateGrowth: item.rateGrowth,
        }
      })
      const imgBase64 = await this.getHtml2canvas(el.id)
      if (!imgBase64 || !tempArr.length) {
        this.exportHints()
      }
      return {
        chartData: {
          dataURL: imgBase64 ? imgBase64.toDataURL('image/png') : '',
          cardData: tempArr,
        },
      }
    },
    // 文本导出参数的处理
    async getTextRequest(el) {
      const imgBase64 = await this.getHtml2canvas(el.id, true)
      if (!imgBase64) {
        this.exportHints()
      }
      return {
        chartData: {
          dataURL: imgBase64 ? imgBase64.toDataURL('image/png') : '',
          cardData: [{
            cardName: el.elName,
            indexValue: '',
            rateCompletion: '',
            rateGrowth: '',
          }],
        },
      }
    },
    // 多合一表格导出参数处理
    // getFourQuadrant(param) {
    //   const _param = this.$_deepClone(param)
    //   _param.forEach(item => {
    //     const { aliasColumnName, tableResponse } = item
    //     aliasColumnName.forEach(e => {
    //       if (!tableResponse[e].type || tableResponse[e].type === 'FQT_SUMMARY') return
    //       tableResponse[e].data = tableResponse[e].data.map(eve => eve + '%')
    //     })
    //   })
    //   return _param
    // },
    _compareProperty() {
      if (this.orderByProperty === 'elName') {
        return function (obj1, obj2) {
          let value1 = obj1.elName
          let value2 = obj2.elName
          return value1.localeCompare(value2)
        }
      }
      return function (obj1, obj2) {
        let { x: x1, y: y1, elX: elX1, elY: elY1, dynamicTagsIndex: dynamicTagsIndex1 } = obj1.layout
        let { x: x2, y: y2, elX: elX2, elY: elY2, dynamicTagsIndex: dynamicTagsIndex2 } = obj2.layout

        return (y1 - y2) || (x1 - x2) || (dynamicTagsIndex1 - dynamicTagsIndex2) || (elY1 - elY2) || (elX1 - elX2)
      }
    },
    getOrderParamsList() {
      if (!this.isMobile) {
        return this.orderParamsList
      }
      const activePanel = this.paramsPanelList.find(panel => panel.active)

      const paramsPanelCont = activePanel?.content?.filter?.(paramEl => {
        const label = this.getLabels(paramEl)
        return label !== undefined && label !== null
      }) || []

      const res = []

      let newLayout = sortLayout(JSON.parse(JSON.stringify(activePanel.layout)))
      newLayout.forEach(v => {
        paramsPanelCont.forEach(k => {
          if (k.id === v.i) {
            res.push(k)
          }
        })
      })
      let quickDate = paramsPanelCont.find(v => [TYPE_PARAM_ELEMENT.CALENDAR_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION, TYPE_PARAM_ELEMENT.LOCATION_QUICK].includes(v.type))
      let dataIndex = res.findIndex(v => v.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR || v.type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR)
      if (quickDate) {
        if (dataIndex >= 0) {
          res.splice(dataIndex, 0, quickDate)
        } else {
          res.push(quickDate)
        }
      }
      return res
    },
    getLabels(el, title) {
      let activeparams = this.paramsPanelList.find(a => a.active)
      const data = getParamElementLabels.call(this, el, activeparams.content, this.boardSlectLang)
      if (title) {
        if (data !== null && data !== undefined) {
          return data.join(';')
        }
      } else {
        if (data !== null && data !== undefined) {
          // 解决bug
          if ([TYPE_PARAM_ELEMENT.LOCATION_NEW].includes(el.type) && !Array.isArray(data)) {
            const arr = Object.keys(data)
            if (arr.findIndex(key => key === el.elName)) {
              return arr.reverse().reduce((a, b) => (a[b] = data[b]) ? a : {}, {})
            }
          }
          return data
        }
      }
    },
  }
}
