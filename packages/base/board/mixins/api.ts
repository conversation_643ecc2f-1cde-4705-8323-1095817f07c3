import Vue from 'vue'
// 调用大屏数据
import { J<PERSON><PERSON>lone, removeRepetition } from '../../../assets/utils/globalTools'
import { DatasetCommand, OnlyField } from '../displayPanel/datasetReplace'
import { AliasDict } from '../displayPanel/utils'

export function addAndExecuteJob(api, params) {
  return api.post('/job/jobOperate/addAndExecuteJob', params)
}

// 更新大屏数据
export function updateJobExecuteParams(api, params) {
  return api.post(`/job/jobOperate/updateJobExecuteParams`, params)
}

// 获取表头token
export function getHeaderToken(api) {
  return api.get('/auth/jwt/headerToken')
}

// 获取token时效
export function getTokenFreeTime(api) {
  return api.get('/auth/jwt/tokenFreeTime')
}

// 清除大屏数据
export function pauseAndRemoveJob(api, params) {
  return api.post('/job/jobOperate/pauseAndRemoveJob', params)
}

// 获得物业树
export function amount(api, params) {
  return api.post('/report/engine/dataBoard/shop/amount', params)
}

// 获得物业树
export function getReferRecentTableTimes(api, params) {
  return api.post('/bi/metaDataset/getReferRecentTableTimes', params)
}

// 获取看板权限
export function getBoardAuth(api, id, channel) {
  return api.get(`/bi/metaDashboard/validateBoardPermission/${id}?jumpFlag=1&channel=${channel}`)
}

// sbi获取看板权限
export function getSbiBoardAuth(api, id, channel) {
  return api.get(`/bi/sbiMetaDashboard/validateBoardPermission/${id}?jumpFlag=1&channel=${channel}`)
}

// 获取日期
export function getBatchLunarCalendar(api, params) {
  return api.post(`/rule/quickCalendarConfig/getBatchLunarCalendar`, params)
}

export function getColumnValues(api, params) {
  return api.post('/report/engine/post/getColumnValues', params)
}

// function getDataSetIds() {
  // let cache = []

  // return function(api, params) {
    // 是否清除缓存
    // if (bool) cache = []
    // const ids = JSONClone(params.ids || [])

    // const data = JSONClone(cache.filter(e => {
    //   if (ids.includes(e.id)) {
    //     ids.splice(ids.indexOf(e.id), 1)
    //     return true
    //   }
    // }))

    // if (ids.length) {
    //   return api.post('/bi/metaDataset/getDataSetTreeByIds', Object.assign(params, { ids })).then(res => {
    //     if (!Array.isArray(res) || !res.length) return
    //
    //     res.forEach(item => {
    //       if (item.children.length) {
    //         const currentDisabled = item.enableFlag === '0'
    //         item.children.forEach(e => {
    //           const { columnTpe } = e
    //           if (!columnTpe) e.columnTpe = 'string'
    //           if (currentDisabled) e.unavailable = currentDisabled
    //         })
    //       }
    //     })
        // cache = removeRepetition([...cache, ...res], 'id')
        // return [...data, ...res]
      //   return res
      // })
    // }

    // return new Promise((resolve) => {
    //   resolve(data)
    // })
//   }
// }
// const getDataSetTreeByIds = getDataSetIds()

// 唯一获取数据集方法
export function getDataSetTreeByIds(api, params, aliasDict: AliasDict) {
  // if (!(aliasDict instanceof AliasDict)) return console.error('缺少必要的 AliasDict 实例')
  const apiUrl = Vue.prototype?.$getSystemConfig?.('api.urls.dataSetTreeByIds')
  const onlyIndex = Vue.prototype?.$getSystemConfig?.('onlyIndex')
  const _api = apiUrl || '/bi/metaDataset/getDataSetTreeByIds'

  return api.post(_api, onlyIndex ? Object.assign(params.ids) : Object.assign(params)).then(response => {
    let res = onlyIndex ? response.data : response
    onlyIndex && res.map(item => {
      item.indexFlag = true
      item.id = String(item.dataSourceId)
      item.parentId = '-1'
      item.children.forEach(e => {
        e.parentId = item.id
        if (e.calendarColumn) {
          e.timeType = item.timeType
        }
      })
      return item
    })

    if (!Array.isArray(res) || !res.length) return []

    res.forEach(item => {
      if (item.children.length) {
        const currentDisabled = item.enableFlag === '0'
        item.children.forEach(e => {
          const { columnTpe } = e
          if (!columnTpe) e.columnTpe = 'string'
          if (currentDisabled) e.unavailable = currentDisabled
        })
      }

      // 修改指标的第一个度量字段的名称，实现拖拽的指标度量名称是指标名称
      if (item.indexFlag && item.children) {
        let flag = false
        item.children = item.children.map(e => {
          if (e.labeName.toLocaleUpperCase().startsWith('INDEX_NUM') && !flag) {
            e.alias = item.labeName
            e.aliasName = item.labeName
            e.aliasNameLan = `{"zh":"${item.labeName}","en":"${item.labeName}"}`
            flag = true
          }
          return e
        })
      }
    })
    if (aliasDict && (aliasDict.isOpen || aliasDict.temporaryOpen) && typeof aliasDict.setDict === 'function') aliasDict.setDict(res)

    OnlyField.setOnlyFieldId(res)
    return res
  })
}

// 新增看板横竖屏查询接口 1-横屏 2-竖屏
export function getBoardHorizontal(api, id) {
  return api.get(`/bi/metaDashboard/screenSetting/${id}`)
}

// 获取数据集详情
export function getDataPreview(api, params) {
  return api.post('/report/dataPreview/previewEnc', params)
}

// 获取字段信息
export function getDatasetInfo(api, id) {
  return api.post(`/bi/datasetInfo/getDatasetInfo/${id}`, {})
}

// 外部数据集获取数据集详情
export function getExternalDatasetInfo(api, id) {
  return api.post(`/bi/external/dataset/getDatasetInfo/${id}`, {})
}

// 复合指标详情
export function getComplexIndex(api,params) {
  return api.post(`/query/entIndex/complexIndex`, params)
}

// 派生指标详情
export function getDeriveIndex(api,params) {
  return api.post('/query/entIndex/driveIndex', params)
}

// 单维度字段预览
export function valueView(api,params) {
  return api.post(`/query/entIndex/singlePreview`, params)
}

// 派生 指标预览
export function indexPreview(api,params) {
  return api.post(`/query/entIndex/drivePreview`, params)
}

// 复合 指标预览
export function complexPreview(api,params) {
  return api.post(`/query/entIndex/complexPreview`, params)
}

// 判断调用指标类型
export function isSim(api) {
  return api.post(`/query/source/isSim`)
}
