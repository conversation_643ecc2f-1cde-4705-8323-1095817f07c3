<template>
  <div class="daily-concern-metrics">
    <div
      id="data-screen-table"
      ref="dataScreenTableRef"
      class="board-display-panel"
      :class="[...dataScreenClass, isDarkMode ? 'theme-dialog-background ' : '', hiddenScroll ? 'hiddenScroll' : '']"
      :style="isHiddenX"
    >
      <loading :isLoading="fullScreenLoading || !isBoardLoaded || initLoading" />
      <supernatant
        id="supernatant"
        v-show="!isNoDataConcern || isPersonalAttentionEdit"
        v-loading="boardLoading"
        element-loading-spinner="sdp-loading-gif"
        ref="supernatant"
        class="supernatant-pc"
        :class="{
        'transition': needTransition,
        'theme-background': isDarkMode,
        [kanbanId]: true,
      }"
        :needTransition="needTransition"
        v-bind="bindSupernatantData"
        @eventBus="eventBus"
      >
        <template v-slot:tabs="{ isCloseCarousel, isShowLargeTabs, screenModeDate, tagModeStack, pcTabsHeight, contentInfoStyle }">
          <supernatantTabs
            :key="boardRes && boardRes.id"
            :style="contentInfoStyle"
            @eventBus="eventBus"
            @triggerRun="triggerRun"
            v-if="!boardInfo.openBoardTab || isShowLargeTabs"
            v-bind="{
            runCollection,
            commonData,
            dynamicTags,
            elList,
            init,
            isFinish,
            dynamicTagsRun,
            isCloseCarousel,
            tagModeStack,
            screenModeDate,
            paramsPanelList,
            supernatant: $refs['supernatant'],
            pcTabsHeight,
            isOpenAutoRefresh,
            boardInfo,
            isChangePos,
            previewModeStyle,
            modalBool,
            parent
          }" />
        </template>
      </supernatant>
      <div v-if="isNoDataConcern && !isPersonalAttentionEdit" class="no-data-screen">
        <div class="image-content">
          <img style="width: 100%;height: 100%;" :src="STATIC_BASE_PATH.images + sdpNoDataImg">
        </div>
        <div class="no-data-title">{{ $t('sdp.views.noDailyConcern') }}</div>
        <el-button size="mini" type="primary" @click="changePersonalAttentionEdit(true)">{{ $t('sdp.button.addConcern') }}</el-button>
      </div>
    </div>
    <setting-panel
      v-if="isPersonalAttentionEdit"
      v-bind="bindSupernatantData"
      @setElement="setElement"
    />
    <addSbiElementDialog
      v-if="addDialogVisible && isSbiType"
      :addDialogVisible.sync="addDialogVisible"
      :elList="elList"
      @eventBtn="eventBtn"
    />
    <add-element-dialog
      v-if="addDialogVisible && !isSbiType"
      :addDialogVisible.sync="addDialogVisible"
      :elList="elList"
      @initCreated="initCreated"
      @eventBtn="eventBtn"
    />
  </div>
</template>

<script type="text/ecmascript-6">// @ts-nocheck

import displayPanel_mixin from '../displayPanel/index'
import websocket_mixin from '../mixins/websocket'
import { THEME_TYPE, STATIC_BASE_PATH, STANDARD_FOLDER_TYPE, CUSTOM_FOLDER_TYPE } from 'packages/assets/constant'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import { DISPLAY_PANEL_COMPONENT_NAME } from '../utils/constant'
import { DailyConcernMetrics } from './utils'
import settingPanel from './components/settingPanel'
// import addElementDialog from './components/addElementDialog'
import addElementDialog from './components/addElementDialogNew'
import addSbiElementDialog from './components/addSbiElementDialog'
import { EVENT_BUS } from '../displayPanel/constants'
import { ALL_PROJECT_NAME } from '../../../components/mixins/commonMixin'

export default {
  name: DISPLAY_PANEL_COMPONENT_NAME,
  componentName: DISPLAY_PANEL_COMPONENT_NAME,
  mixins: [displayPanel_mixin, websocket_mixin],
  data() {
    return {
      // 每日关注类
      dailyConcernMetricsClass: new DailyConcernMetrics(this),
      bigFullscreenState: false, // 大屏看板
      fullscreenState: false, // pc看板
      isSuperLink: false, // 判断是否是弹窗中的看板
      needTransition: false, // 首次进入看板时不执行过度效果
      themeFullScreen: false,
      parent: this,
      isChangePos: false,
      previewModeStyle: {},
      initLoading: false,
      addDialogVisible: false,
      isNoDataConcern: false,
      EVENT_BUS,
      STATIC_BASE_PATH,
    }
  },
  directives: { SdpElScrollbar },
  components: {
    settingPanel,
    addSbiElementDialog,
    addElementDialog
  },
  computed: {
    sdpNoDataImg() {
      if (this.themeData.themeType === THEME_TYPE.deepBlue) {
        return 'sdp-no-data-deepBlue.png'
      }
      return 'sdp-no-data.png'
    },
    dataScreenClass() {
      if (this.isFullScreen) {
        return [this.toggle % 2 === 0 ? 'part-screen' : 'panel-active']
      } else {
        let arr = ['modal-screen', this.commonData.isPreview && !this.isFullScreen ? 'index-screen' : 'modal-screen-color']
        return arr
      }
    },
    projectName() {
      return this.utils.env?.projectName || ''
    },
    isSbiType() {
      return this.projectName === ALL_PROJECT_NAME.SBI || false
    },
    isDarkMode() {
      return this.themeData.themeType === THEME_TYPE.darkBlue && this.themeData.enableTheme && this.commonData.isPreview
    },
    isHiddenX() {
      const overflow = this.commonData.isPreview ? 'hidden' : 'auto'
      return { 'overflow-x': overflow }
    },
    isPersonalAttentionEdit: {
      get() {
        return this.utils?.isPersonalAttentionEdit
      },
      set(val) {
        this.$set(this.utils, 'isPersonalAttentionEdit', val)
      }
    },
    bindSupernatantData() {
      return {
        layoutDeploy: this.layoutDeploy,
        // datasetList: this.datasetList,
        init: this.init,
        toggle: this.toggle,
        elList: this.elList,
        boardInfo: this.boardInfo,
        titleStyle: this.titleStyle,
        dynamicTags: this.dynamicTags,
        themeFullScreen: this.themeFullScreen,
        isFinish: this.isFinish,
        recordRunTimes: this.recordRunTimes,
        paramsPanelList: this.paramsPanelList,
        isScreenSkipToScreen: this.isScreenSkipToScreen,
        breadcrumbHeight: this.breadcrumbHeight,
        isHideTitle: this.isHideTitle,
        hideSupernatantTop: this.hideSupernatantTop,
        parent: this,
        watermarkUrl: this.watermarkUrl,
        superLinkArr: this.superLinkArr,
        dialogVisible: this.dialogVisible,
        modalBool: this.modalBool,
        isChangePos: this.isChangePos,
        isDisabledScreenMode: this.isDisabledScreenMode,
        settingPanelInfo: this.settingPanelInfo,
        highlightEl: this.highlightEl,
        languageAndCurrency: this.languageAndCurrency,
      }
    },
  },
  watch: {
  },
  mounted() {
    // DailyHasUpdate(this.api, {}).then(res => {
    //   res && this.$set(this.utils, 'dailyHasUpdate', res)
    // })
  },
  destroyed() {
    this.dailyConcernMetricsClass.destroyed()
  },
  created() {
    this.getLangList()
    this.getStandardChartSchemeList()
  },
  methods: {
    // 调用按钮 type --- (EVENT_BTN_TYPE)
    eventBtn(type, params) {
      this.sdpBus.$emit(EVENT_BUS.DAILY_CONCERN_BTN, type, params)
    },
    initCreated(val) {
      this.$emit('initCreated', val)
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../../../assets/styles/animation.css';
.daily-concern-metrics{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sdp-box {
  display: flex;
  flex-direction: row-reverse;
  padding-top:8px;
  position:absolute;
  z-index:8;
  top:0;
  right:0;
}
.fullscreenModeBox {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.theme-dialog-background {
  @include sdp-mixin-style($type:(
    // 背景颜色调整
    backgroundColor:('themeDialogBackground':true),
  ));
}
.hiddenScroll{
  overflow: hidden!important;
}
// 主题背景色
#board-display-panel-border-box {
  display: flex;
  flex-direction: column;
}
.board-display-panel-box {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.preview-bgc {
  @include sdp-mixin-style($type:(
    backgroundColor:(dialogbgc:true),
  ));
  &.watermark-area {
    .sdp-box {
      z-index: 10000;
    }
    .paramspanel-pc {
      z-index: 9999;
    }
  }
}
.theme-background {
  @include sdp-background_0();
}

.supernatant-pc.transition {
  transition-property: top;
  transition-timing-function: ease;
  transition-delay: 0s;
}
.slideIn {
  transition: top 1s;
}
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}
::-webkit-scrollbar-corner {
  background: #C8C7CA;
  border-radius: 3px;
}
::-webkit-scrollbar-thumb {
  background: #C8C7CA;
  border-radius: 3px;
}
.board-display-panel {
  position: relative;
}
.sdp-support {
  height: 40px;
}
.sdpiconfont {
  font-size: 24px;
}
.placeholder {
  width: 100%;
  height: 40px;
}
.fullscreenModeBox {
  .icon-sdp-Nguanbi {
    font-size: 24px;
    z-index: 7;
    cursor: pointer;
  }
  .icon-sdp-a-Nquanping {
    z-index: 7;
    font-size: 24px;
    cursor: pointer;
  }
}
#data-screen-table {
  display: flex;
  flex-direction: column;
  border-top: #EAEAEA;
  width: 100%;
  height: 100%;
  /deep/ .el-tooltip__popper[data-sdp-el-insert='el-tooltip'],  /deep/ .el-tooltip__popper[data-sdp-el-insert-firefox='el-tooltip']{
    position: absolute !important;
  }
  /deep/ .el-select-dropdown[data-sdp-el-insert-firefox='el-select'] {
    transition: opacity .3s;
  }
  .theme-dark-mode {
    @include operation_icon-0;
  }
  .borad-box {
    min-height: 100%;
    position: relative;
    flex: none;
    .border-box-fixed {
      position: sticky;
      z-index: 2001;
      top: 8px;
      height: 0;
      padding: 0;
    }
    .params-panel-fixed {
      width: 100%;
      z-index: 2000;
      position: sticky !important;
    }
    .icon-sdp-Nguanbi {
      font-size: 24px;
      color: #666;
      z-index: 7;
      cursor: pointer;
      height: 24px;
      line-height: 24px;
      width: 30px;
      text-align: center;
    }
    .hidden {
      position: absolute;
      z-index: 6;
      top: 8px;
      right:0;
      width: 100px;
      height: 25px;
      cursor: pointer;
      float: right;
      text-align: right;
    }
    .icon-sdp-Ngudingshaixuanqu1 {
      font-size: 24px;
      width: 30px;
      height: 24px;
      line-height: 24px;

      color: #666;
      z-index: 7;
      cursor: pointer;
      text-align: center;
      margin: 0 4px;
    }
    .icon-sdp-Ngudingshaixuanqu2 {
      font-size: 24px;
      width: 30px;
      height: 24px;
      line-height: 24px;
      color: #666;
      z-index: 7;
      cursor: pointer;
      text-align: center;
      margin: 0 4px;
    }
  }
}
.no-data-screen{
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .image-content{
    // width: 120px;
    // height: 86px;
    width: 200px;
    height: 136px;
    opacity: 0.15;
    [data-theme='sdp-dark-blue'] & {
      opacity: 0.5;
    }
    [data-theme='sdp-deep-blue'] & {
      opacity: 0.9;
    }
  }
  .no-data-title {
    margin-top: 20px;
    font-size: 14px;
    color: #333333;

    [data-theme='sdp-dark-blue'] & {
      color: #FFF;
    }
    [data-theme='sdp-deep-blue'] & {
      color: #afb2be;
    }
  }
  > button{
    margin-top: 40px;
  }
}
/deep/ .sdp-board-scrollbar {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .el-scrollbar__wrap {
    overflow-x: hidden;
    overflow-y: auto;
  }
  .el-scrollbar__bar {
    z-index: 30;
  }
}
.full-screen {
  position: fixed;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1002;
  background-color: var(--sdp-ysbjc);
  box-shadow: none !important;
}
.modal-screen {
  height: 100%;
  width: 100%;
  box-shadow: none !important;
}
.modal-screen-color {
  background-color: var(--sdp-ysbjc);
}
.index-screen {
  background-color: #f7f7f7;
}
.part-screen {
  background-color: var(--sdp-ysbjc);
}
.panel-active {
  background-color: #f8f8f8;
  @include sdp-background_0($key: 'panelActiveBgc');
}
.scroll-disable{
  overflow: hidden!important;
}
.export-box {
  display: inline-block;
  .container {
    display: inline-block;
    margin-right: 13px;
  }
  i {
    margin-right: 8px;
    font-size: 22px;
    color: #333;
    cursor: pointer;
  }
}
.export-box-right {
  right: 58px;
}

#data-screen-table .el-button:not(.is-disabled):hover:not(.el-button--text) {
  [data-theme='sdp-dark-blue'] & {
    background: #553CCE !important;
    background-color: #553CCE !important;
    color: #FFF !important;
    border-color: transparent !important;
  }
}
</style>
