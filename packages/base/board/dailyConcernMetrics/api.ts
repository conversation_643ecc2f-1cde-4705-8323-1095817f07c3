// 添加每日关注指标元素
export function addPersonalAttention(api, params) {
  return api.post('bi/sbi/personalAttention/addPersonalAttention', params)
}
export function batchAddPersonalAttention(api, params) {
  return api.post('bi/sbi/personalAttention/batchAddPersonalAttention', params)
}

// 获取个人关注列表
export function getPersonalAttentionList(api) {
  return api.post('bi/sbi/personalAttention/getPersonalAttentionList')
}

// 获取个人关注列表
export function GetPersonalAttentionListByBoardId(api, params) {
  return api.post('bi/sbi/personalAttention/getPersonalAttentionListByBoardId', params)
}

// 移除个人关注指标元素
export function removePersonalAttention(api, params) {
  return api.post('bi/sbi/personalAttention/deletePersonalAttention', params)
}

// 在列表中删除个人关注元素
export function deleteRealEntity(api, params) {
  return api.post('bi/sbi/personalAttention/deleteRealEntity', params)
}

// 保存个人关注
export function savePersonalAttention(api, params) {
  return api.post('bi/sbi/personalAttention/savePersonalAttention', params)
}

// 获取全部关注元素文件夹树形结构(标准/自定义）
export function getPersonalAttentionFolderTree(api, params) {
  return api.post('bi/sbiMetaFolder/getPersonalAttentionFolderTree', params)
}
export function getClassify(api, params = { channel: '1', code: 'B0043' }) {
  return api.post('bi/metaClassify/personalAttention/getClassify', params)
}

// 获取全部关注元素文件夹树形结构(已发布）
export function getPersonalReleaseToMeTree(api, params) {
  return api.post('bi/sbiMetaFolder/getReleaseToMeTree', params)
}

// 通过树节点获取元素列表
export function getPersonalAttentionListById(api, params) {
  return api.post('bi/sbi/personalAttention/getPersonalAttentionListById', params)
}

// 获取元素数据
export function getAttentionList4Update(api, params) {
  return api.post('bi/sbi/personalAttention/getAttentionList4Update', params)
}

// 清除已更新状态
export function clearHasUpdate(api, params) {
  return api.post('bi/sbi/personalAttention/clearHasUpdate', params)
}

// 获取目录
export function getClassifyTree(api, params) {
  return api.post('bi/metaClassify/getClassifyTree', params)
}

// 获取目录
export function personalAttentionTree(api, params) {
  return api.post('bi/metaClassify/personalAttention/tree', params)
}

// 元素属于每日关注的元素需要调用此接口
export function SaveDailyElementData(api, params) {
  return api.post('bi/sbi/personalAttention/record/update', params)
}
