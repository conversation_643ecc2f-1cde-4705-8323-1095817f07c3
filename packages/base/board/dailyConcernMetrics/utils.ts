import { RUN_ELEMENT_TYPE } from '../../../assets/constant'
import { formatDate, generateUUID } from '../../../assets/utils/globalTools'
import { fn, getPreviewContentList, switchParamsPanelList } from '../displayPanel/boardLanguage'
import { getParamElementLabels } from '../displayPanel/components/utils'
import { EVENT_BUS, TYPE_ELEMENT } from '../displayPanel/constants'
import { checkLangElement } from '../displayPanel/boardLanguage'
import { mostrecent, parseClausesFinal } from '../displayPanel/params/paramElement/bussinessCalendar/api'
import { PERIOD, THIS_PERIOD_KEY_TYPE } from '../displayPanel/params/paramElement/bussinessCalendar/bridge'
import { COMPARE_TYPE, DATE_CONST, USER_FDATE_TYPE } from '../displayPanel/params/paramElement/bussinessCalendar/components/constants'
import { getParseClausesFinalParam } from '../displayPanel/params/paramElement/bussinessCalendar/components/utils'
import { CONSTANTS } from '../displayPanel/params/paramElement/locationNew/bridge'
import { TYPE_PARAM_ELEMENT } from '../displayPanel/params/utils/constants'
import {getContainerIncludeElsIdList, isHasElBind, layoutCount, layoutCount2, getContainerIncludeElsIdList } from '../displayPanel/utils'
import mediator from '../displayPanel/utils/mediator'
import {batchAddPersonalAttention, getPersonalAttentionList, savePersonalAttention} from './api'
import { elementPaddingCompatible } from 'packages/base/board/displayPanel/compatibility'

export enum EVENT_BTN_TYPE {
    Refurbish = 'refurbish',
    UpdateStyle = 'updateStyle',
    Save = 'save',
    AddElement = 'addElement',
    RemoveElement = 'removeElement'
}

export const SBI_ATTR = 'sbiAttr'

const JOIN_STR = '&#&'

function removeDuplicatesById(arr) {
  const seen = new Set()
  return arr.filter(item => {
    const id = `${item.dashboardId}$&${item.elementId}`
    if (!seen.has(id)) {
      seen.add(id)
      return true
    }
    return false
  })
}

export class DailyConcernMetrics {
    _vm: any
    personalAttentionListRequestData: any
    addElementList: any
    constructor(vm) {
        // 存vue组件当前this
        this._vm = vm
        this.eventBtn = this.eventBtn.bind(this)
        this.dailyConcernRun = this.dailyConcernRun.bind(this)
        this._vm.sdpBus.$on(EVENT_BUS.DAILY_CONCERN_BTN, this.eventBtn)
        this._vm.sdpBus.$on(EVENT_BUS.DAILY_CONCERN_RUN, this.dailyConcernRun)
    }
    destroyed() {
        this._vm.sdpBus.$off(EVENT_BUS.DAILY_CONCERN_BTN, this.eventBtn)
        this._vm.sdpBus.$off(EVENT_BUS.DAILY_CONCERN_RUN, this.dailyConcernRun)
    }

    eventBtn(type, params) {
        if (typeof this[type] !== 'function') return void '实例上没有该方法'

        this[type](params)
    }
    // 替换元素id(copy看板元素id)
    static replaceUuid(elementContent) {
        if (typeof elementContent !== 'string') return elementContent

        let elementContentStr = elementContent

        elementContent = JSON.parse(elementContent)

        elementContent.elList.forEach(element => {
            const id = element.id
            const uuid = generateUUID()
            const reg = new RegExp(id, 'g')

            elementContentStr = elementContentStr.replace(reg, uuid)
        })

        return JSON.parse(elementContentStr)
    }
    // 每日关注run
    dailyConcernRun(el, type = RUN_ELEMENT_TYPE.paramEl, otherType = '', eventOptions = { requestFinishCallback: () => {} }) {
        if (!el[SBI_ATTR]) return

        this.metricSwitch(el)

        const { previewData: { paramsRequest, options, requestOptions }, linkageData = {}, isRun, paramsPanel } = el[SBI_ATTR]

        // 没有run过，第一次修改为 RUN_ELEMENT_TYPE.paramEl 触发
        if (!isRun) {
            type = RUN_ELEMENT_TYPE.paramEl
            el[SBI_ATTR].isRun = true
        }

        const runId = generateUUID()

        const ids = getContainerIncludeElsIdList(el)

        const elementRequest = this._vm.$refs['supernatant'].collectRequest({ ids, type, otherType, skipLoading: true })

        const { datasetListOption } = options

        const datasetList = this._vm.datasetList.filter((item) => datasetListOption.ids.includes(item.id))

        const paramsRequestCallback = this._vm.filterServerNeedParams.filterServerNeedParams(this._vm.elList)

        // 清空其他的联动数据
        this._vm.filterServerNeedParams._params = {}

        for (const key in linkageData) {
            paramsRequestCallback(key, linkageData[key])
        }

        const reqElementDatas = mediator.collectsAdapter(
            elementRequest,
            this._vm.$_JSONClone(paramsRequest),
            {
                isSupportFilterHandler: true,
                elList: this._vm.elList,
                datasetList
            }
        ) || []
        this._vm.requestOptions = requestOptions
        if (this._vm.isFinish) this._vm.isFinish = false

        Promise.all(reqElementDatas.map(req => this._vm.requestInterface({ req, runId, paramsPanel: [paramsPanel] }))).then((res) => {
            this._vm.commonFinishCall([el.id])

            // eslint-disable-next-line no-unused-expressions
            eventOptions?.requestFinishCallback?.()
        })
    }
    // 移除每日关注指标中的元素
    deleteElement({
        element: el,
        call
    }) {
        call && call()

        // if (!el[SBI_ATTR]) {
        //   call && call()
        //   return
        // }
        //
        // removePersonalAttention(this._vm.api, {
        //     id: el[SBI_ATTR].dataId
        // }).then(call)
    }
    // 添加数据集
    async addDataSet(elementList) {
        const saveData = {}
        elementList.forEach(element => {
            const { previewData } = element[SBI_ATTR]
            const { options: { datasetListOption } } = previewData

            if (datasetListOption) {
                const { ids, authorityBoardId } = datasetListOption
                saveData[authorityBoardId] = saveData[authorityBoardId] ? [...new Set([...saveData[authorityBoardId], ...ids])] : ids
            }
        })
        let arr: any[] = []

        for (let key in saveData) {
            // arr.push(this._vm.getDataSet(saveData[key], true, key))
            arr.push(this._vm.getDataSet(saveData[key], true)) // 87201 无需权限
        }

        await Promise.all(arr)
    }

    // 初始化看板
    initBoard() {
        // 初始run --- dynamicTags
        this._vm.dynamicTagsRun = {}
        this._vm.initLoading = true
        this._vm.collectRender.clear()
        getPersonalAttentionList(this._vm.api).then(async ({
            personalAttentionVos = [],
            content = ''
        }) => {
            personalAttentionVos = removeDuplicatesById(personalAttentionVos)

            this.personalAttentionListRequestData = {
              personalAttentionVos,
              content
            }

            this._vm.fullScreenLoading = false
            // this._vm.isNoDataConcern = false
            this._vm.boardLoaded('', false, {})
            this._vm.elList.splice(0, this._vm.elList.length)

            this._vm.$set(this._vm.boardInfo, 'showElementTip', true)
            this._vm.$set(this._vm.boardInfo, 'showElementDescription', true)

            let elementList: any[] = []

            if (personalAttentionVos.length) {
                elementList = await this.switchElList(personalAttentionVos)
                await this.addDataSet(elementList)
            } else if (!content) {
              const themeType = this._vm.utils.themeParameters.themeType
              const defaultSettingPanelInfo = {
                widgetSpace: 8,
                referenceLineOpts: this._vm.getReferenceLineDftOpts(themeType),
              }
              this._vm.$set(this._vm, 'settingPanelInfo', defaultSettingPanelInfo)
              this._vm.$set(this._vm, 'dynamicTags', [])
              this._vm.initDynamicTags([])
            }
            // 替换看板数据
            if (content) {
                const { dynamicTags, layouts, settingPanelInfo = {} } = JSON.parse(content)
                // 还原layout
                elementList.forEach(el => {
                    const { dashboardId, elementOriginalId } = el[SBI_ATTR]

                    const key = DailyConcernMetrics.joinStr(dashboardId, elementOriginalId)

                    const newLayout = layouts[key]

                    if (newLayout) {
                        newLayout.i = el.id
                        el.layout = newLayout
                    }
                })

                const containerIncludeEls = elementList.filter(el => el.type === TYPE_ELEMENT.CONTAINER).reduce((obj, el) => {
                  const { dashboardId, elementOriginalId } = el[SBI_ATTR]
                  const key = DailyConcernMetrics.joinStr(dashboardId, elementOriginalId)
                  obj[key] = getContainerIncludeElsIdList(el).map(id => elementList.find(el => el.id === id)).filter(e => e)
                  return obj
                }, {})

                const newDynamicTags = dynamicTags.map(item => {
                    const content = item.content
                    // 添加容器里吗格外添加的元素
                    Object.keys(containerIncludeEls).filter(key => content.includes(key)).forEach(key => {
                      const els = containerIncludeEls[key]
                      els.filter(el => {
                        const { dashboardId, elementOriginalId } = el[SBI_ATTR]
                        const key = DailyConcernMetrics.joinStr(dashboardId, elementOriginalId)
                        if (!content.includes(key)) {
                          content.push(key)
                        }
                      })
                    })

                    return {
                        ...item,
                        content: content.map((id) => {
                            const el = elementList.find(el => {
                                const { dashboardId, elementOriginalId } = el[SBI_ATTR]
                                const key = DailyConcernMetrics.joinStr(dashboardId, elementOriginalId)
                                return key === id
                            })

                            if (!el) return false

                            el._dynamicTags = {
                              id: item.id,
                              isHide: item.isHide
                            }

                            return el.id
                        }).filter(e => e)
                    }
                })

                if (this._vm.utils?.options?.dailyActiveElementId) {
                    const target = personalAttentionVos.find(e => e.elementId === this._vm.utils.options.dailyActiveElementId)
                    if (target) {
                        const targetEl = elementList.find(e => e.sbiAttr?.elementOriginalId === target.elementId)
                        if (targetEl) {
                          // 如果发现了，则直接改tab
                          const targetDynamicTag = newDynamicTags.find(e => e.content.includes(targetEl.id))
                          if (targetDynamicTag) {
                            newDynamicTags.forEach(tag => {
                              tag.active = tag.id === targetDynamicTag.id
                            })
                          } else {
                            // 如果没发现，则改存的tab
                            if (targetEl?.sbiAttr?.tabId) {
                              const targetTab = newDynamicTags.find(e => e.id === targetEl.sbiAttr.tabId)
                              if (targetTab) {
                                newDynamicTags.forEach(tag => {
                                  tag.active = tag.id === targetTab.id
                                })
                              }
                            }
                          }
                        }
                    }
                }

                // 设置界面的数据（参考线默认初始值）
                if (!settingPanelInfo?.referenceLineOpts) {
                  settingPanelInfo.referenceLineOpts = this._vm.getReferenceLineDftOpts(this._vm.themeType)
                }
                if (!settingPanelInfo?.widgetSpace) {
                  settingPanelInfo.widgetSpace = 8
                }
                this.initMarginLayout(settingPanelInfo.widgetSpace)

                this._vm.$set(this._vm, 'dynamicTags', newDynamicTags)
                this._vm.$set(this._vm, 'settingPanelInfo', settingPanelInfo)
            }
            // 排查垃圾数据
            const hasIds = this._vm.dynamicTags.reduce((pre, { content = [] }) => {
                return [...pre, ...content]
            }, [])

            this._vm.elList.push(...elementList.filter(el => hasIds.includes(el.id)))

            this.setIsNoDataConcern(!personalAttentionVos.length)

            this._vm.initLoading = false
            // 内边距兼容
            this._vm.elList.forEach(el => {
              elementPaddingCompatible(el, this._vm)
            })
            // 初始化多语言
            this._vm.checkPreviewContentLang(this._vm.langCode)

            this._vm.collectRender.execute(() => {
                // 自动run
                setTimeout(() => {
                    this._vm.initLazyData()
                    this._vm.init = false
                    this[EVENT_BTN_TYPE.Refurbish]()

                    this.initNotSaveElement(this._vm.dailyScrollToElement)
                })
            })
        })
    }

    async initNotSaveElement(callback = null) {
      // 获取tab页
      const dynamicTags = this._vm.dynamicTags
      // 获取元素
      const allElIdList = dynamicTags.reduce((pre, cur) => {
        return [...pre, ...cur.content]
      }, [])
      const allElList = this._vm.elList.filter(e => allElIdList.includes(e.id))
      const boardAndElIdList = allElList.map(e => `${e.sbiAttr.dashboardId}&${e.sbiAttr.elementOriginalId}`)
      const {
        personalAttentionVos = [],
        content = ''
      } = this.personalAttentionListRequestData
      // 获取全部未添加进来的元素
      const todoElList = personalAttentionVos.reverse().filter(e => !boardAndElIdList.includes(`${e.dashboardId}&${e.elementId}`))

      // 添加全部新元素到最后
      if (todoElList.length) {
        this._vm.isDailyAdding = true
        this._vm.isNoDataConcern = false

        await this[EVENT_BTN_TYPE.AddElement](todoElList, callback)
      }
    }

    // 刷新
    [EVENT_BTN_TYPE.Refurbish]() {
        const dynamicTag = this._vm.dynamicTags.find(item => item.active)

        if (!dynamicTag) return

        const { id, content = [] } = dynamicTag

        if (!content.length) return

        this._vm.dynamicTagsRun[id] = true

        const ids = this._vm.initPcScroll()

        const runIds = content.filter(id => ids.includes(id))

        this._vm.$nextTick(() => {
            this._vm.setloading({ loading: true })

            this._vm.elList.filter(element => runIds.includes(element.id)).forEach(element => {
                this.dailyConcernRun(element)
            })
         })
    }

    [EVENT_BTN_TYPE.UpdateStyle](callback) {
        if (this._vm.dailyConcernData) {
        this.initNotSaveElement(this._vm.dailyScrollToElement)
        this[EVENT_BTN_TYPE.Save](() => {
            callback && callback()
            this._vm.utils.dailyHasUpdate = false
            this.initBoard()
        }, 'updateStyle')
        }
    }

    metricSwitch(element) {
        if (![TYPE_ELEMENT.CHART, TYPE_ELEMENT.TABLE].includes(element.type)) return

        const vm = element.vm
        if (!vm) return

        const { paramsPanel } = element[SBI_ATTR]

        if (!paramsPanel) return

        const { content = [] } = paramsPanel

        if (!content.length) return

        const metric = content.find(el => el.type === TYPE_PARAM_ELEMENT.METRIC)

        if (!metric) return

        const bindElements = metric.content.bindElements

        if (!bindElements.includes(element.id)) return

        const dataList = (Array.isArray(metric.content.userOperationData) ? metric.content.userOperationData : [metric.content.userOperationData]).map((name) => {
          const item = metric.content.metricList.find(item => item.name === name)
          if (item.groupNameList && item.groupNameList.length) {
            const lan = item.groupNameList.find(group => group.languageCode === this._vm.langCode)
            name = lan.value
          } else if (item.aliasList && item.aliasList.length) {
            const lan = item.aliasList.find(group => group.languageCode === this._vm.langCode)
            name = lan.value
          } else if (item.res && item.res.length) {
            const lan = item.res.find(group => group.languageCode === this._vm.langCode)
            name = lan.value
          }

          return {
            name
          }
        })

        vm.metricSwitch && vm.metricSwitch(dataList, true)
    }

    // 拼接唯一id
    static joinStr(dashboardId, elementOriginalId) {
        return `${dashboardId}${JOIN_STR}${elementOriginalId}`
    }

    // 设置状态
    setIsNoDataConcern(bool) {
        this._vm.isNoDataConcern = typeof bool === 'boolean' ? bool : !this._vm.elList.length

        this._vm.$emit('getIsNoDataConcern', this._vm.isNoDataConcern)
    }

    // 保存
    async [EVENT_BTN_TYPE.Save](call, type = '') {
        const elList = this._vm.elList

        const params = {
            personalAttentionDtos: [],
            content: '',
            updateFlag: type === 'updateStyle'
        }

        // 排除容器里的元素进行保存
        const noContainerIdElList = elList.filter(el => !el._containerId)
        // 需要修改的layout
        const layouts = {}

        params.personalAttentionDtos = noContainerIdElList.map(el => {
            const { dashboardId, elementOriginalId, previewData } = el[SBI_ATTR]

            if (!previewData || !Object.keys(previewData).length) return

            const key = DailyConcernMetrics.joinStr(dashboardId, elementOriginalId)

            layouts[key] = el.layout

            return {
                dashboardId,
                elementId: elementOriginalId,
            }
        }).filter(e => e)

        const dynamicTags = this._vm.dynamicTags.map(item => {
            return {
                ...item,
                content: item.content.map((id) => {
                    const el = elList.find(el => el.id === id)

                    if (!el) return false

                    const { dashboardId, elementOriginalId } = el[SBI_ATTR]

                    return el && DailyConcernMetrics.joinStr(dashboardId, elementOriginalId)
                }).filter(e => e)
            }
        })

        // 设置界面的数据
        const settingPanelInfo = this._vm.settingPanelInfo

        params.content = JSON.stringify({
            dynamicTags,
            layouts,
            settingPanelInfo,
        })

        await this.batchAddNewPersonalAttentionElement()

      // // 数据错误使用
      // let initParams = {
      //   personalAttentionDtos: [],
      //   content: ''
      // }
        savePersonalAttention(this._vm.api, params).then(res => {
            if (type === 'updateStyle') {
                this._vm.$message.success(this._vm.$t('sdp.message.updateSuccessMsg'))
            } else {
                this._vm.$message.success(this._vm.$t('sdp.message.saveSuccessMsg'))
            }
            call && call()
        }, err => {
            console.log(err)
        })
    }
    // 获取当前语言下已选信息
    getSelectedParamsInfo(selectedParamsInfo, metaDashboardElementLanList, paramsPanel, requestParamsIds) {
        let info: any[] = []
        const _this = this._vm

        if (!selectedParamsInfo) {
          return []
        }

        const { allElIds, Currency } = selectedParamsInfo
        if (!paramsPanel) {
            return info
        }

        const { content: aliasContent = [], layout = [] } = paramsPanel || {}

        const content = [...layout].sort((a, b) => (a.x + a.y) - (b.x + b.y)).map(item => item.i).reduce((pre, id) => {
            pre.push(aliasContent.find(el => el.id === id))
            return pre
        }, [])

        const ids = layout.map(item => item.i)

        const noLayoutEl = aliasContent.filter(item => !ids.includes(item.id))

        const paramsPanels = [...content, ...noLayoutEl].filter(element => {
            const isBind = isHasElBind(element, allElIds)

            if (!isBind) return false

            let isHasType = false
            let requestIds: string[] = []

            for (let [types, ids] of requestParamsIds.entries()) {
                if (types.includes(element.type)) {
                    isHasType = true
                    requestIds = ids
                }
            }

            return isHasType ? requestIds.includes(element.id) : true
        })

        const arr = getPreviewContentList.call(_this, _this.commonData.boardSlectLang(), {
            metaDashboardLanguageList: [],
            metaDashboardElementLanList
        })

        switchParamsPanelList(paramsPanel.content || [], paramsPanel, _this, arr)

        // 参数组件
        info = paramsPanels.map(paramEl => {
          if (paramEl.type === TYPE_PARAM_ELEMENT.SEARCH) {
            const searchV = paramEl.content.options.dataSets[0]
            const res = []

            if (searchV) {
              if (searchV.chkField.values.length) {
                res.push(`${paramEl.content.language.check.name} ${searchV.chkField.values[0]}`)
              }
              if (searchV.amtField.values.length) {
                res.push(`${paramEl.content.language.amount.name} ${searchV.amtField.filterType} ${searchV.amtField.values[0]}`)
              }
            }

            return { [paramEl.elName]: res.join(',') }
          }

          return {
            [paramEl.elName]: getParamElementLabels.call(_this, paramEl, content)
          }
        })



        function currencyAlias() {
            let locationItem = content.find(k => k.type === TYPE_PARAM_ELEMENT.LOCATION_NEW)
            if (locationItem?.content?.currency) return locationItem?.content?.currency

            return metaDashboardElementLanList?.find(item => item.key.includes('_currency') && item.languageCode === _this.langCode)?.value || 'Currency'
        }

        info.push({
            [currencyAlias()]: Currency
        })

        return info
    }

    async updataCalender(content, paramsRequest, requestOptions) {
        const calendarComponents = paramsRequest['calendarComponents']
        if (!calendarComponents) return
        const _this = this._vm
        // 获取location请求数据
        const locationData = paramsRequest[CONSTANTS.KEY]
        const dateNormal = formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss')

        await Promise.all(calendarComponents.map(async item => {
            const calendarParseParam = item.calendarParseParam
            const el = content.find(el => el.id === item.id)

            const { currentList = [], priorList = [] } = el?.content?.typeSaveData?.[el?.content?.typeSaveData?.activeTabs] || {}
            const isScatteredSingle = currentList.length === 1 && !priorList.length

            calendarParseParam.dateNormal = isScatteredSingle ? formatDate(new Date(`${currentList[0]}T00:00:00`), 'yyyy-MM-dd hh:mm:ss') : dateNormal

            const tenantId = requestOptions.tenantId
            const quickClauses = calendarParseParam.quickClauses
            const calenderTypes = [TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR, TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR, TYPE_PARAM_ELEMENT.CALENDAR_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION]
            // DATE_CONST.MostRecent 是数据集最大时间
            if (!quickClauses || quickClauses === DATE_CONST.MostRecent || !calenderTypes.includes(el.type)) return

            const setClausesPeriodMapList = (res) => {
                calendarParseParam.clausesPeriodMapList = Array.isArray(res) ? res : [res]
                // el.content.thisPeriodKey = thisPeriodKey
                let thisPeriodKeyData: {
                    [index: string]: string[]
                } = {
                    [THIS_PERIOD_KEY_TYPE.thisPeriodKey]: [],
                    [THIS_PERIOD_KEY_TYPE.infrasys]: [],
                    [THIS_PERIOD_KEY_TYPE.monthOnMonth]: [],
                    [THIS_PERIOD_KEY_TYPE.yearOnYear]: [],
                    [THIS_PERIOD_KEY_TYPE.PriorPeriod]: [],
                }
                calendarParseParam.clausesPeriodMapList.forEach(({ formatValues }) => {
                    if (formatValues) {
                        Object.entries(formatValues).forEach(([key, value = {}]) => {
                            // @ts-ignore
                            let { endDate = '', startDate = '' } = value
                            let str = endDate === startDate ? startDate : `${startDate} ~ ${endDate}`

                            switch (key) {
                                case PERIOD.THIS:
                                    thisPeriodKeyData[THIS_PERIOD_KEY_TYPE.thisPeriodKey].push(str)
                                    break
                                case PERIOD.PRI:
                                    thisPeriodKeyData[THIS_PERIOD_KEY_TYPE.PriorPeriod].push(str)
                                    break
                                case PERIOD.YEAR:
                                    thisPeriodKeyData[THIS_PERIOD_KEY_TYPE.yearOnYear].push(str)
                                    break
                                case PERIOD.MONTH:
                                    thisPeriodKeyData[THIS_PERIOD_KEY_TYPE.monthOnMonth].push(str)
                                    break
                                case PERIOD.INFRASYS:
                                    thisPeriodKeyData[THIS_PERIOD_KEY_TYPE.infrasys].push(str)
                                    break
                            }
                        })
                    }
                })

                Object.assign(el.content.thisPeriodKey, thisPeriodKeyData)
            }

            await parseClausesFinal(_this.api, getParseClausesFinalParam({
                tenantId,
                quickClauses,
                dateNormal: isScatteredSingle ? currentList[0] : undefined,
                pId: _this.commonData.pid,
                calendarType: calendarParseParam.quickType,
                pastN: calendarParseParam.pastN,
                nextN: calendarParseParam.nextN,
                customizedToday: isScatteredSingle,
                beginDayOfWeek: requestOptions.beginDayOfWeek,
                location: { data: locationData },
            })).then(setClausesPeriodMapList).catch(err => {
                console.log(err)
            })
        }))
    }

    async updataParamPanel(content, paramsRequest, requestOptions) {
        if (!Array.isArray(content)) return

        // 更新日历数据
        await this.updataCalender(content, paramsRequest, requestOptions)
    }

    async switchElList(element = [], isStatic = false) {
        const _this = this._vm

        const elementList: any[] = []

        // const ids = element.map(e => e.elementContent.indexOf('previewData') === -1)

        await Promise.all(element.map(async (el: any) => {
            const newElementContent = DailyConcernMetrics.replaceUuid(el.elementContent)

            const { elList: oldElList, boardInfo, tabId } = JSON.parse(el.elementContent)

            const { elList, metaDashboardElementLanList, previewData, selectedParamsInfo, paramsPanelList, paramsPanelActiveId, linkageData = {} } = newElementContent

            if (!previewData) return

            const paramsPanel = Array.isArray(paramsPanelList) ? (paramsPanelList.length ? paramsPanelList.find(item => item.active) || {} : {}) : paramsPanelList

            const requestParamsIds = new Map()

            requestParamsIds.set([TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR, TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR, TYPE_PARAM_ELEMENT.CALENDAR_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION], (previewData.paramsRequest['calendarComponents'] || []).map(param => param.id))

            await this.updataParamPanel(paramsPanel.content, previewData.paramsRequest, previewData.requestOptions)

            // 添加多语言
            _this.newBoardContent.metaDashboardElementLanList.push(...metaDashboardElementLanList)

            elList.forEach((element, index) => {
                const elementOriginalId = oldElList[index].id

                element[SBI_ATTR] = {
                    // 是否run过
                    isRun: false,
                    // 是否失效
                    isInvalid: el.isInvalid,
                    // 失效原因
                    invalidReason: el.invalidReason,
                    // 是否更新
                    hasUpdate: el.hasUpdate || false,
                    // 用于删除数据
                    dataId: el.id,
                    // 看板id
                    dashboardId: el.dashboardId,
                    // 原看板id
                    elementOriginalId,
                    // 原看板参数组件tab id
                    paramsPanelActiveId: paramsPanelActiveId || '',
                    // 看板相关 文件夹id 看板名称/别名
                    boardInfo: boardInfo,
                    boardInfoLanguage: {
                      boardCode: el.boardCode,
                      boardName: el.boardName,
                    },
                    // 看板目录类型
                    personalAttentionType: el.personalAttentionType,
                    // 请求数据
                    previewData,
                    // 已选数据
                    selectedParamsInfo: this.getSelectedParamsInfo(selectedParamsInfo, metaDashboardElementLanList, paramsPanel, requestParamsIds),
                    // 参数组件
                    paramsPanel,
                    // 默认加入的tabId
                    tabId,
                    // 参数组件联动数据
                    linkageData
                }
                element.layout.static = isStatic

                elementPaddingCompatible(element, _this)
            })

            elementList.push(...elList)
        }))

        console.log('kyz --- 2 赋值完成', elementList)

        return elementList
    }

    saveAddElementData(element = []) {
        // if (!this._vm.isEdit) return
        const saveList = this.addElementList || []
        this.addElementList = [...saveList, ...element]
    }

    async batchAddNewPersonalAttentionElement() {
      if (!this.addElementList?.length) return

      const elIdList = this._vm.elList.map(e => {
        return `${e.sbiAttr.dashboardId}&${e.sbiAttr.elementOriginalId}`
      })
      const addElementList = this.addElementList.filter(item => {
        return elIdList.includes(`${item.dashboardId}&${item.elementId}`)
      })
      if (!addElementList.length) return

      await batchAddPersonalAttention(this._vm.api, addElementList)
      this.addElementList = []
    }

    // 添加元素
    async [EVENT_BTN_TYPE.AddElement](element = [], callbackFun = null) {
        console.log('%c [ element ]-911', 'font-size:13px; background:#ae6882; color:#f2acc6;', element)
        const elementList: any[] = await this.switchElList(element, false)
        console.log('%c [ elementList ]-913', 'font-size:13px; background:#2192ca; color:#65d6ff;', elementList)
        if (!elementList.length) return

        this.saveAddElementData(element)
        this._vm.isDailyAdding = true

        await this.addDataSet(elementList)

        const ids = elementList.map(el => el.id)
        // debugger
        // this._vm.unLoadList.push(...ids)
        this._vm.addElIdsPreventLazyLoad = ids

        // this._vm.dynamicTags.find(({ active, content }) => {
        //     if (active) {
        //         content.push(...ids)
        //     }
        //     return active
        // })

        const activeDynamicTag = this._vm.dynamicTags.find(e => e.active)
        elementList.forEach(el => {
            const tabId = el.sbiAttr.tabId
            if (tabId) {
                const targetDynamicTag = this._vm.dynamicTags.find(e => e.id === tabId) || activeDynamicTag
                targetDynamicTag.content.push(el.id)
            } else {
                activeDynamicTag.content.push(el.id)
            }
        })

        const sortList = element.map(e => {
            const target = elementList.find(el => el.sbiAttr.elementOriginalId === e.elementId && el.sbiAttr.dashboardId === e.dashboardId)
            return target
        }).filter(e => e)

        const sortElementList:any = []
        sortList.forEach(item => {
            if (item.type === TYPE_ELEMENT.CONTAINER) {
                const contentList = elementList.filter(e => e._containerId === item.id) || []
                sortElementList.push(...contentList)
            }
            sortElementList.push(item)
        })

        const callback = (el, saveEl = []) => {
            saveEl.push(el)

            new Promise<void>((resolve) => {
                setTimeout(() => {
                    if (!el._containerId) {
                        // 最后添加
                        el.layout.y = 9999
                        layoutCount2.call(this._vm, el)
                    }
                    this._vm.elList.push(el)

                    resolve()
                })
            }).then(() => {
                try {
                    const type = el.type
                    // 单个元素翻译
                    if (type) {
                        const lanList = getPreviewContentList(
                            this._vm.langCode,
                            this._vm.newBoardContent
                        )
                        const contentKey = 'key'
                        fn(el, lanList, contentKey, 'elName')
                        // eslint-disable-next-line no-unused-expressions
                        checkLangElement[type]?.call(this._vm, { el, arr: lanList, contentKey })
                    }
                } catch (err) {
                    console.error(EVENT_BTN_TYPE.AddElement, err)
                } finally {
                    const newEl = sortElementList.shift()

                    if (newEl) {
                        callback(newEl, saveEl)
                    } else {
                        saveEl.forEach(el => {
                            // 表格会自动run,过滤表格
                            if (el.type !== TYPE_ELEMENT.CONTAINER) {
                                // this.dailyConcernRun(el)
                                this._vm.collectRender.execute(() => {
                                    // 自动run
                                    setTimeout(() => {
                                      if (el.type !== TYPE_ELEMENT.TABLE) {
                                            this.dailyConcernRun(el)
                                        } else {
                                            this._vm.setloading({ id: el.id, loading: true })
                                            this.dailyConcernRun(el, RUN_ELEMENT_TYPE.refreshEl)
                                        }
                                      callbackFun && callbackFun()
                                    }, 300)
                                })
                            }
                        })
                        this._vm.isDailyAdding = false
                    }
                }
            })
        }

        callback(sortElementList.shift())
    }

    // 打开弹框
    openAddELDialog(val) {
      this._vm.addDialogVisible = val
    }

    // 修改元素边距
    changeLayoutMargin(marginList) {
      this._vm.layoutDeploy.margin.splice(0, 2, ...marginList)
    }

    // 初始化组件间距
    initMarginLayout(widgetSpace) {
      let margin = Number(widgetSpace)
      this.changeLayoutMargin([margin, margin])
      this._vm.sdpBus.$emit(EVENT_BUS.LAYOUT_CHANGE)
    }

    // 删除元素
    async [EVENT_BTN_TYPE.RemoveElement]({ element: element = {}, callback }) {
      if (this._vm.$refs['supernatant']) {
        this._vm.$refs['supernatant'].elToolsDeleteHandler(element, callback)
      }
    }
}
