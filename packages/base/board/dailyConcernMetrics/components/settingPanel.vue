<template>
  <div class="setting-panel"
       :style="isShowStyle">
    <div class="close setting-close-icon">
      <i
        :title="$t('sdp.views.attribute')"
        class="sdpiconfont"
        :class="['icon-sdp-zuojiantou',isShow ? '' : 'rotate']"
        @click="isShow = !isShow"
      ></i>
    </div>

    <div v-show="isShow" style="height: 100%">

    <div class="setting-title">
      {{$t('sdp.views.pageSetting')}}
    </div>

    <div class="setting-board">
      <div class="cell borderAttr">
        <div class="row">
          <span>{{ $t('sdp.views.referenceLine') }}</span>
        </div>

        <el-switch
          v-model="referenceLineShow"
          @change="changeReferenceLineShow"
          size="mini"
          style="width: 32px;height: 16px"/>
      </div>

      <div v-if="referenceLineShow" class="cell borderAttr flex-vertical-center">
        <div class="row">
          <span>{{ $t('sdp.views.referenceLine') }}</span>
          <i
            class="icon-sdp-zhongzhiyinying"
            style="font-size: 10px; padding: 4px 8px; cursor: pointer"
            @click="changeConfig(null)"
          />
        </div>

        <el-color-picker
          v-model="referenceLineStrokeStyle"
          @change="changeConfig"
          size="mini"
          style="width: 32px;height: 16px"
          :popper-class="'board-design-pop title-color-picker customer-el-color-picker ' + getCurrentThemeClass()"
          show-alpha
        />
      </div>

      <div class="cell borderAttr widgetSpacing">
        <div class="row el-space">{{$t('sdp.views.widgetSpacing')}}</div>
        <el-input-number
          v-model.number="widgetSpace"
          @change="changeLayoutMargin"
          controls-position="right"
          style="width: 208px;height: 34px"
          :min="1" :max="50"
        />
      </div>
    </div>

    <!--    组件设置  -->
    <div class="panel-list table-list-wrap">
      <div class="setting-title"> {{$t('sdp.views.widgetList')}} </div>
      <div class="table-list" v-if="treeData && treeData.length">
        <el-row
          v-for="el in treeData"
          :key="el.id"
        >
          <el-tooltip :content="getElementTitle(el) || el.elName || ''" placement="top">
          <div
            class="row-data"
            :class="{ 'row-active': activeHighLightElement(el) }"
            @click.capture="setHighLightElement(el)">
            <svg class="icon row-icon" aria-hidden="true">
              <template>
                <use :xlink:href="'#' + el.icon"></use>
              </template>
            </svg>
            <span class="el-title">
              {{getElementTitle(el) || el.elName || ''}}
            </span>
          </div>
          </el-tooltip>
        </el-row>
      </div>
    </div>

    </div>
  </div>
</template>

<script>
import { REFERENCE_LINE_DFT_OPTS, THEME_TYPE } from '../../../../assets/constant'
import { ELEMENT_TYPE_LIST } from '../constants'
import { EVENT_BUS, TYPE_ELEMENT } from '../../displayPanel/constants'
import { getElementTitle } from 'packages/base/board/displayPanel/supernatant/utils/utils'
import common from 'packages/base/board/settingPanel/components/mixins/common'
export default {
  mixins: [common],
  inject: {
    sdpBus: { default: () => () => {} },
    getCurrentThemeClass: { default: () => () => '' },
    utils: { default: {} },
    langCode: { default: 'zh' },
  },
  data() {
    return {
      isShow: true,
      isShowStyle: {},
    }
  },
  props: {
    elList: {
      type: Array,
      default: () => []
    },
    dynamicTags: {
      type: Array,
      default: () => []
    },
    paramsPanelList: {
      type: Array,
      default: () => []
    },
    boardInfo: {
      type: Object,
    },
    settingPanelInfo: {
      type: Object,
      default: () => {}
    },
    highlightEl: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    themeType() {
      let curThemeType = this.utils.themeParameters.themeType
      return curThemeType === THEME_TYPE.deepBlue ? THEME_TYPE.darkBlue : curThemeType
    },
    referenceLineOpts() {
      return this.settingPanelInfo?.referenceLineOpts || {}
    },
    activeStyle(el) {
      return function (el) {
        return {
          background: this.highlightEl?.id === el?.id ? '#ECEBFC' : ''
        }
      }
    },
    activeHighLightElement(el) {
      return function (el) {
        return this.highlightEl?.id === el?.id
      }
    },
    referenceLineShow: {
      get() {
        return this.referenceLineOpts?.visible
      },
      set(val) {
        this.$set(this.referenceLineOpts, 'visible', val)
      }
    },
    widgetSpace: {
      get() {
        return this.settingPanelInfo?.widgetSpace
      },
      set(val) {
        this.$set(this.settingPanelInfo, 'widgetSpace', val)
      }
    },
    treeData() {
      return this.conbineTree()
    }
  },
  watch: {
    isShow (val) {
      this.isShowStyle = val ? {} : { width: '0px', padding: '0' }
    },
    referenceLineOpts: {
      handler(val) {
        this.$set(this.boardInfo, 'referenceLineOpts', val)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getElementTitle(el, langCode = this.langCode) {
      if (el.type === TYPE_ELEMENT.TABLE) {
        if (el?.content?.dataScreenRes?.tableControlsLanguageList) {
          const title = el.content.dataScreenRes.tableControlsLanguageList.find(e => e.key === 'name' && e.languageCode === langCode)
          if (title) {
            return title.value || getElementTitle(el, langCode)
          }
        }
      }

      return getElementTitle(el, langCode)
    },
    conbineTree() {
      const activeTabs = this.dynamicTags.find(e => e.active)?.content
      activeTabs && activeTabs.forEach(elementId => {
        let element = this.elList.find(el => el.id === elementId) || {}
        let elIconObj = ELEMENT_TYPE_LIST.find(type => {
          return type?.alias === element?.type || type?.alias === element?.content?.alias
        })
        elIconObj?.icon && this.$set(element, 'icon', elIconObj.icon)
      })
      return this.elList.filter(el => activeTabs.includes(el.id)).filter(e => !e._containerId)
    },
    changeReferenceLineShow() {
      this.sdpBus.$emit(EVENT_BUS.FEATURE_HEIGHT_UPDATE)
    },
    changeLayoutMargin() {
      let margin = Number(this.widgetSpace)
      if (!margin) margin = 1
      if (margin < 1) margin = 1
      if (margin > 50) margin = 50
      this.widgetSpace = margin
      this.sdpBus.$emit(EVENT_BUS.DAILY_CONCERN_BTN, 'changeLayoutMargin', [margin, margin])
      this.sdpBus.$emit(EVENT_BUS.LAYOUT_CHANGE)
      // this.changeReferenceLineShow()
    },
    setHighLightElement (data) {
      this.$emit('setElement', { data: data.id })
    }
  }
}
</script>

<style lang="scss" scoped>
$font-family: PingFang-SC-Medium, NotoSansHans-Regular,Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
$font400: 400;
$font700: 700;

.icon-sdp-zhongzhiyinying {
  color: var(--sdp-zs);
}

.setting-panel{
  position: relative;
  height: 100%;
  width: 240px;
  flex-direction: column;
  font-family: $font-family;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  padding: 16px;
  box-shadow: inset 1px 0px 0px rgba(29, 35, 78, 0.08);
  background: var(--sdp-szk-bjs);

  .setting-title {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
    color: var(--sdp-cszj-tgl);
  }

  .setting-board{
    width: 208px;
    padding-bottom: 24px;
    border-bottom: 1px dashed var(--sdp-ycfgx);
  }
  .cell {
    flex: 1;
    font-family: $font-family;
    font-weight: $font400;
    font-size: 12px;
    color: #222222;
    letter-spacing: 0;
    line-height: 12px;
    /deep/ input {
      border-radius: 2px;
    }
  }
  .borderAttr {
    margin-top: 14px;
    display: flex;
    flex-direction: row;
    .row {
      flex: 1;
      &:nth-of-type(1) {
        text-align: left;
        line-height: 20px;
        font-family: $font-family;
        font-weight: $font400;
        font-size: 12px;
        letter-spacing: 0;
        color: var(--sdp-xxbt1);
      }
    }
  }
  .widgetSpacing{
    flex-direction: column;
    .el-space{
      height: 18px;
      line-height: 18px;
      color: var(--sdp-xxbt1);
      margin-bottom: 8px;
    }
    .el-input{
      height: 34px;
      line-height: 34px;
    }
    /deep/ .el-input-number .el-input__inner{
      height: 34px;
      line-height: 34px;
      text-align: left;
      color: var(--sdp-fhwzs);
      border: 1px solid var(--sdp-ycsz-srk-bcs)
    }
  }
  .table-list-wrap {
    overflow-y: auto;
    height: calc(100% - 200px);
  }
  .panel-list{
    padding-top: 24px;
    .table-list{
      .row-data{
        display: flex;
        align-items: center;
        justify-content: space-between;
        //width: 680px;
        width: 100%;
        height: 34px;
        padding: 8px 10px;
        cursor: pointer;
        background: transparent;
        transition: all .3s ease;

        .row-icon{
          width: 18px;
          height: 18px;
          line-height: 18px;
          text-align: center;
          font-size: 18px;
          margin-right: 8px;
        }
        .el-title {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          display: flex;
          flex: 1;
          color: var(--sdp-xxbt1);
        }
      }

      .row-active {
        background: var(--sdp-ycsz-hgls);

        .el-title {
          color: var(--sdp-zs);
        }
      }
    }
  }
}
// 关闭按钮
.close {
  .icon-sdp-zuojiantou {
    transform: rotate(-180deg);
    cursor: pointer;
    position: absolute;
    right: 0;
    left: -18px;
    top: 48%;
    width: 18px;
    font-size: 18px;
    line-height: 63px;
    box-shadow: 3px 0px 6px 0 rgba(0, 0, 0, 0.1);
    color: var(--sdp-zs) !important;
    background-color: var(--sdp-szk-bjs) !important;
  }

  .rotate {
    box-shadow: -2px 6px 6px 0 rgba(0, 0, 0, 0.1);
    background-color: #f3f3f3;
    transform: rotate(0deg);
  }
}

.flex-vertical-center {
  display: flex;
  align-items: center;
}

.setting-panel /deep/ {
  .el-color-picker--mini .el-color-picker__trigger{
    width: 32px;
    height: 16px;
    padding: 0;
    border-color: #EDF0F5;

    [data-theme='sdp-dark-blue'] & {
      border-radius: 0;
    }
    [data-theme='sdp-deep-blue'] & {
      border-radius: 0;
    }

    .el-color-picker__color {
      border-color: #D9D9D9;

      [data-theme='sdp-dark-blue'] & {
        border-radius: 0;
      }
      [data-theme='sdp-deep-blue'] & {
        border-radius: 0;
      }
    }

    .el-icon-arrow-down:before {
      content: none;
    }
  }
}

.el-input-number{ //inputnumber多主题
    /deep/ {
      .el-input-number__decrease, .el-input-number__increase{
        background-color: var(--sdp-ycsz-srk-bgs) !important;

        border-left-color: var(--sdp-ycsz-srk-bcs) !important;
        border-right-color: var(--sdp-ycsz-srk-bcs) !important;

        color: var(--sdp-ycsz-srtswz) !important;

        &.is-disabled{
          color: var(--sdp-jys) !important;
        }
      }
      .el-input-number__increase{
        border-bottom-color: var(--sdp-ycsz-srk-bcs) !important;
      }
      input {
        background-color: var(--sdp-ycsz-srk-bgs) !important;
        border: 1px solid var(--sdp-ycsz-srk-bcs);
      }
    }
}
</style>
