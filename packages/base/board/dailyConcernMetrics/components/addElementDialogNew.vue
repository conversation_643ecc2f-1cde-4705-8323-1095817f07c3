<template>
  <div>
    <el-dialog
      :title="`${$t('sdp.views.plsSelectToAdd')}`"
      :custom-class="`sdp-dialog sdp-add-element-new-dialog sdp-ems-dialog ${getCurrentThemeClass()}`"
      :visible.sync="dialogShow"
      :append-to-body="true"
      :close-on-click-modal="false"
      fullscreen
      class="param-font sdp-title-start"
    >
      <div class="main" v-loading="loading" element-loading-spinner="sdp-loading-gif">
        <div class="left-main" :style="{width: `${leftWidth}px`}">
          <div class="left-title">
            {{$t('sdp.views.BoardList')}}
          </div>
          <div class="search-box">
            <el-input
              type="text"
              v-model="searchInput"
              @change="handleSearchChange"
              clearable
              :suffix-icon="searchInput ? '' : 'icon-sdp-sousuo'"
              :placeholder="$t('sdp.views.sousuo')"
            />
          </div>

          <div class="tree-params">
            <el-scrollbar
              v-if="treeData.length"
              style="height: 100%"
              v-sdp-el-scrollbar>
              <!--<div-->
              <!--  class="el-tree multi-theme"-->
              <!--  role="tree"-->
              <!--  v-for="(tree, tIndex) in treeData"-->
              <!--  :key="tIndex"-->
              <!--&gt;-->
                <!--:default-expanded-keys="defaultExpandKeys"-->

                <el-tree
                  default-expand-all
                  highlight-current
                  icon-class=""
                  ref="tree"
                  class="tree-line-list"
                  node-key="id"
                  :indent="0"
                  :data="treeData"
                  :props="defaultProps"
                  :filter-node-method="filterNode"
                >
                  <div
                    class="custom-tree-item"
                    slot-scope="{ node, data }"
                  >
                    <div style="width: 100%" @click="handleNodeClick(data, node)" :title="data.name || data.label">
                      <svg class="icon row-icon" aria-hidden="true"
                           style="width: 12px;height: 12px"
                           v-if="data.children && data.children.length">
                        <template>
                          <use :xlink:href="node.expanded ? '#icon-sdp-zhankai2' : '#icon-sdp-shouqi3'"></use>
                        </template>
                      </svg>

                      <span>{{data.name || data.label}}</span>
                    </div>
                  </div>
                </el-tree>
              <!--</div>-->
            </el-scrollbar>

            <div v-if="!treeData.length" class="nodate-style">
              {{$t('sdp.views.noData')}}
            </div>
          </div>

          <drag-box
            style="width: 5px;height: 100%;position: absolute;right: -2px;top: 0;z-index: 2001"
            :value.sync="leftWidth"
          />
        </div>

        <div class="right-main" :style="{width: `calc(100% - ${leftWidth}px)`}">
          <div class="preview">
            <sdp-board-preview
              v-if="selectedBoardInfo.id"
              ref="boardPreview"
              class="board-preview no-Full"
              v-bind="bindData"
              @addElementSelected="handleAddElementSelected"
            />
            <div v-else class="no-data-box"></div>
          </div>

          <div class="footer">
            <div class="content">
              <div class="info-box">{{$t('sdp.views.Selected')}}: {{ selectedCnt }}</div>
              <div class="btn-box">
                <el-button type="primary" class="btn-width btn-sure-color" @click="onConfirm">{{$t('sdp.button.ensure')}}</el-button>
                <el-button class="btn-width btn-cancel-color" @click="cancel">{{$t('sdp.button.cancel')}}</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--<span slot="footer" class="dialog-footer">-->
      <!--  <el-button type="primary" class="btn-width btn-sure-color" @click="onConfirm">{{$t('sdp.button.ensure')}}</el-button>-->
      <!--  <el-button class="btn-width btn-cancel-color" @click="cancel">{{$t('sdp.button.cancel')}}</el-button>-->
      <!--</span>-->
    </el-dialog>
  </div>
</template>

<script>
import * as api from '../api'
import {
  EXTERNAL_STANDARD,
  CUSTOM_FOLDER_TYPE,
  STANDARD_FOLDER_TYPE,
  RELEASE_FOLDER_TYPE,
  RELEASE_TO_ME_TYPE, THEME_VALUE_MAPS
} from 'packages/assets/constant'
import { ELEMENT_TYPE_LIST } from '../constants'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import { EVENT_BTN_TYPE } from '../utils'
import {batchAddPersonalAttention, deleteRealEntity, personalAttentionTree} from '../api'
import { checkLangElement, MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD, TYPE_ELEMENT } from '../../displayPanel/constants'
import { getContainerOnElementId, getElementTitle } from '../../displayPanel/supernatant/utils/utils'
import { fn } from '../../displayPanel/boardLanguage'
import DragBox from 'packages/base/common/dragBox/index'

function processTreeData(treeData) {
  function traverse(node) {
    if (node.children && node.children.length === 0 && node.boardList) {
      // 如果children是空数组且存在boardList，则处理boardList
      node.children = node.boardList.map(item => ({ ...item, isBoard: true, label: item.name }))
      // 清空boardList字段，因为数据已经移动到children中
      node.boardList = []
    } else if (node.children) {
      // 如果children不为空，则递归遍历children
      node.children.forEach(traverse)
    }
  }

  // 从根节点开始遍历
  treeData.forEach(traverse)

  return treeData
}

export default {
  name: 'addElementDialog',
  inject: {
    utils: { default: {} },
    langCode: { default: 'zh' },
    getCurrentThemeClass: { default: () => () => '' }
  },
  directives: {
    SdpElScrollbar,
  },
  components: {
    DragBox
  },
  data() {
    return {
      loading: false,
      treeData: {}, // treeData数据
      searchInput: '', // 搜索条件
      defaultExpandKeys: [], // 默认展开
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      selectedBoardInfo: {
        id: '',
        folderId: '',
      },
      curNode: {},
      selectedCnt: 0,
      leftWidth: 256,
      originalData: null,
    }
  },
  props: {
    addDialogVisible: {
      type: Boolean,
      default: false
    },
    elList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    api() {
      return this.utils.api
    },
    // 打开弹框状态
    dialogShow: {
      get() {
        return this.addDialogVisible
      },
      set(val) {
        this.$emit('update:addDialogVisible', val)
      },
    },
    bindData() {
      const dailySelectedElement = this.selectedBoardInfo.id ? this.elList.filter(e => e.sbiAttr.dashboardId === this.selectedBoardInfo.id).map(e => e.sbiAttr.elementOriginalId)  : []
      let { isOpen, themeType } = this.utils.themeParameters || {}
      const commonData = {
        api: this.api,
        langCode: this.langCode,
        themeParameters: {
          isOpen,
          themeType: THEME_VALUE_MAPS[themeType],
        },
        options: {
          tenantId: this.utils.tenantId,
          editType: 'openSource',
          // parameterComponentListSelected: this.utils.options.parameterComponentListSelected
        },
        isMobile: false,
        isReference: false,
        env: {
          isOpenAliasDict: true
        },
        sbiOptions: {
          isSbiDashPreview: true, // 是否为sbi仪表盘预览界面
          isAddDailyConcernElement: true,
          dailySelectedElement,
        },
        updateSwitch: { disUpdateCheck: true },
        // skipLinks: { exportManagement: this.$_getSkipLinks('admin/exportManagement') },
        isSavaAndPublish: false
      }

      return Object.assign({}, commonData, {
        boardInfo: {
          folderId: this.selectedBoardInfo.folderId,
          id: this.selectedBoardInfo.id,
        }
      })
    }
  },
  watch: {
  },
  mounted() {
    this.selectedCnt = 0
    this.getBoardMenuData()
  },
  methods: {
    async getBoardMenuData() {
      this.loading = true
      const data = await api.personalAttentionTree(this.api, {
        channel: '1',
      }).catch(e => {
        console.error(e)
        this.loading = false
        return [{}]
      })
      this.treeData = processTreeData(data)
      this.loading = false
      this.originalData = this.$_JSONClone(this.treeData)

      setTimeout(() => {
        this.setCheckedFirstBoard()
      }, 100)
    },

    getFirstBoard(list) {
      // 递归函数
      const handle = (data) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].isBoard) {
            if (!data[i].children || data[i].children.length === 0) {
              return data[i]
            } else if (handle(data[i].children)) {
              // 如果子节点内还存在board
              return handle(data[i].children)
            }
          } else if (data[i].children && data[i].children.length > 0) {
            const tempNode = handle(data[i].children)
            if (tempNode) {
              return tempNode
            }
          }
        }
      }
      if (list && list[0] && list[0].children && list[0].children.length > 0) {
        const nodes = JSON.parse(JSON.stringify(list[0].children))
        return handle(nodes)
      }
      return null
    },

    setCheckedFirstBoard() {
      const node = this.getFirstBoard(this.treeData)
      if (node) {
        this.curNode = node
        this.selectedBoardInfo.id = node.id
        this.selectedBoardInfo.folderId = node.folderId
      } else {
        this.curNode = {}
      }

      this.$nextTick(() => {
        const menuTree = this.$refs.tree

        menuTree && menuTree.setCurrentKey(this.curNode.id || null)
      })
    },

    handleNodeClick(data, node) {
      // console.log('data, node', data, node)
      if (!!data?.children?.length || !data.isBoard) return
      if (this.selectedBoardInfo.id === data.id) return
      // todo 如果不是看板则退出
      const callback = () => {
        this.selectedBoardInfo.id = ''
        this.selectedBoardInfo.folderId = ''
        this.curNode = {}
        this.selectedCnt = 0

        setTimeout(() => {
          this.selectedBoardInfo.id = data.id
          this.selectedBoardInfo.folderId = data.folderId
          this.curNode = data
        }, 10)
      }

      if (this.selectedCnt > 0) {
        this.$sdp_eng_confirm(`${this.$t('sdp.views.notSaveLeave')}`, `${this.$t('sdp.views.confirm')}`, {
          confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
          cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
          closeOnHashChange: false,
          type: 'warning',
          closeOnClickModal: false,
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog',
        }).then(async () => {
          const success = await this.onConfirm({ close: false })
          if (success) {
            callback()
          } else {
            this.$nextTick(() => {
              const menuTree = this.$refs.tree

              menuTree && menuTree.setCurrentKey(this.curNode.id || null)
            })
          }
        }).catch(() => {
          callback()
        })
      } else {
        callback()
      }
    },

    handleSearchChange() {
      this.$set(this, 'treeData', this.filterTree(this.originalData, this.searchInput))
      this.$nextTick(() => {
        const menuTree = this.$refs.tree

        menuTree && menuTree.setCurrentKey(this.curNode.id || null)
      })
      // const menuTree = this.$refs.tree
      // if (menuTree) {
      //   if (Array.isArray(menuTree)) {
      //     menuTree.forEach(tree => tree.filter(this.searchInput))
      //   } else {
      //     menuTree.filter(this.searchInput)
      //   }
      // }
    },

    filterNode(value, data) {
      // if (!value) return true
      // return data?.code?.indexOf(value) !== -1 || data.label.indexOf(value) !== -1
    },
    filterTree(data, keyword) {
      if (!keyword) return JSON.parse(JSON.stringify(data)) // 返回原始数据的深拷贝

      const result = []

      data.forEach(node => {
        const code = node.code || ''
        const label = node.name || node.label || ''
        if (code.toUpperCase().indexOf(keyword.toUpperCase()) !== -1 || label.toUpperCase().indexOf(keyword.toUpperCase()) !== -1) {
          // 如果当前节点匹配，则直接添加到结果中
          result.push(JSON.parse(JSON.stringify(node)))
        } else if (node.children && node.children.length > 0) {
          // 递归检查子节点
          const matchedChildren = this.filterTree(node.children, keyword)
          if (matchedChildren.length > 0) {
            // 如果有匹配的子节点，则保留该父节点并更新其子节点
            const newNode = JSON.parse(JSON.stringify(node))
            newNode.children = matchedChildren
            result.push(newNode)
          }
        }
      })

      return result
    },
    // filterNode(value, data, node) {
    //   if (!value) return true
    //
    //   // 判断当前节点是否包含过滤关键字
    //   const containsKeyword = data?.code?.indexOf(value) !== -1 || data.label.indexOf(value) !== -1
    //
    //   // 如果当前节点是叶子节点，则根据containsKeyword决定显示与否
    //   // 如果当前节点不是叶子节点，则递归检查其子节点是否有符合条件的节点
    //   if (this.isLeaf(node)) {
    //     return containsKeyword
    //   } else {
    //     let anyChildMatched = false
    //     const checkChildren = (nodes) => {
    //       for (let child of nodes) {
    //         if (child.label.indexOf(value) !== -1 || checkChildren(child.children || [])) {
    //           anyChildMatched = true
    //           break
    //         }
    //       }
    //       return anyChildMatched
    //     }
    //
    //     checkChildren(data.children)
    //
    //     return anyChildMatched
    //   }
    // },
    // isLeaf(node) {
    //   return !(node.childNodes && node.childNodes.length)
    // },

    cancel() {
      this.dialogShow = false
    },

    // 添加元素
    async onConfirm({ close = true }) {
      if (!this.$refs?.boardPreview?.$refs?.displayPanel?.getDailyElementParam) {
        return false
      }
      const displayPanel = this.$refs.boardPreview.$refs.displayPanel
      const boardElList = displayPanel.elList
      const newElList = await displayPanel.getDailyElementParam()

      if (!newElList?.length) {
        return true
      }

      const elementNum = newElList.reduce((num, next) => {
        const element = boardElList.find(e => e.id === next.elementId)

        if (element.type === TYPE_ELEMENT.CONTAINER) {
          const ids = getContainerOnElementId(element)
          num += ids.length
        }
        return num + 1
      }, 0)

      const totalNum = this.elList.length + elementNum

      if (totalNum > MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD) {
          const str = this.$t('sdp.message.dailyConcernElementsInBoardMessage')
          this.$message(str.replace('$$', this.elList.length))
          return false
      }

      displayPanel.supernatantFunCall('resetAddDailyElementSelect')

      // if (!this.utils.isPersonalAttentionEdit) {
      //   await batchAddPersonalAttention(this.api, newElList)
      //   this.$emit('initCreated', true)
      //   this.dialogShow = false
      //   return
      // }
      this.$emit('eventBtn', EVENT_BTN_TYPE.AddElement, newElList)
      close && (this.dialogShow = false)
      return true
    },

    handleAddElementSelected(val) {
      this.selectedCnt = val
    },
  }
}
</script>

<style lang="scss" scoped>
@import "packages/base/board/displayPanelMobile/components/variable.scss";
$self: (
  sdp-classic-white: (
    treeNodeLabelColor: #333333,
    treeNodeLabelActiveColor: #4136A8,
    treeDashedColor: #DCDFE5,
    fontColor1: #333333,
    fontColor2: #989FA6,
  ),
  sdp-dark-blue: (
    treeNodeLabelColor: #A7ABB2,
    //treeNodeLabelActiveColor: #15B8E0,
    treeNodeLabelActiveColor: #FFFFFF,
    treeDashedColor: #A7ABB21A,
    fontColor1: #FFFFFF,
    fontColor2: #989FA6,
  )
);

.main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: calc(100vh - 69px - 0px);
  position: relative;

  .row-icon{
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-size: 18px;
    margin-right: 8px;
  }

  .left-main{
    width: 256px;
    padding: 12px 16px 12px 0;
    height: 100%;
    box-shadow: -1px 0px 0px 0px var(--sdp-ycfgx) inset;
    position: relative;

    .left-title {
      color: var(--sdp-cszj-tgl, #333);
      font-family: "PingFang HK";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      padding-bottom: 12px;
    }
    .search-box {
      width: 240px;
      margin-bottom: 12px;

      /deep/ .el-input__inner{
        height: 30px;
        line-height: 30px;
        padding-left: 8px;
      }

      /deep/ .el-input__suffix .el-input__icon {
        line-height: 30px;
        cursor: pointer;
        margin-right: 5px;
        font-size: 14px;
      }
    }
    .tree-params{
      height: calc(100% - 31px - 42px);
      width: calc(100% + 16px);
      margin-left: -16px;
    }
  }

  .right-main{
    height: 100%;
    width: calc(100% - 256px);
    margin-left: 0px;
    position: relative;

    .preview {
      height: calc(100% - 62px);
      padding: 1px 0;
      overflow-y: auto;

      .board-preview {
        width: 100%;
        height: 100%;
      }
      .no-data-box {
        width: 100%;
        height: 100%;
      }
    }

    .footer {
      box-shadow: 0px 1px 0px 0px var(--sdp-ycfgx) inset;
      width: 100%;
      height: 62px;
      padding: 0 24px;

      .content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .info-box {
          color: var(--sdp-cszj-tgl, #333333);
          font-family: "PingFang HK";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }
    }
  }
  .nodate-style {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: var(--sdp-cszj-tswzs) !important;
  }

  .el-loading-mask {

  }
}

.main /deep/ {
  padding-left: 24px;
  .tree-line-list{
    .el-tree-node {
      position: relative;
      padding-left: 16px; // 缩进量
    }
    .el-tree-node__children {
      padding-left: 16px; // 缩进量
    }
    .custom-tree-item{
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: absolute;
      top: 0;
      left: 25px;
      width: calc(100% - 25px);

      .iconfont{
        font-size: 12px;
        margin-right: 8px;
      }
      .el-icon-circle-plus-outline{
        background: #EDF0F5;
      }
    }

    // 竖线
    .el-tree-node::before {
      content: "";
      height: 100%;
      width: 1px;
      position: absolute;
      left: -3px;
      top: -26px;
      border-width: 1px;
      border-left: 1px dashed #DCDFE6;
      border-color: var(--sdp-xk-bks);
    }
    // 当前层最后一个节点的竖线高度固定
    .el-tree-node:last-child::before {
      height: 38px; // 可以自己调节到合适数值
    }

    // 横线
    .el-tree-node::after {
      content: "";
      width: 24px;
      height: 20px;
      position: absolute;
      left: -3px;
      top: 12px;
      border-width: 1px;
      border-top: 1px dashed #DCDFE6;
      color: red;
      border-color: var(--sdp-xk-bks);
    }

    // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
    & > .el-tree-node::after {
      border-top: none;
    }
    & > .el-tree-node::before {
      border-left: none;
    }

    // 展开关闭的icon
    .el-tree-node__expand-icon{
      display: none;
      font-size: 16px;
      // 叶子节点（无子节点）
      &.is-leaf{
        color: transparent;
      }
    }
  }

  .el-tree {
    background-color: transparent !important;
    .el-tree-node__label {
      font-size: 12px;
      color: var(--sdp-cszj-tgl, #333);
    }
    .el-tree-node__content {
      height: 26px;
      line-height: 26px;
      color: var(--sdp-zjbxx-wzs);
      &:hover {
        background-color: var(--sdp-ycsz-hgls);
      }
    }
    .el-tree-node.is-current>.el-tree-node__content {
      background-color: var(--sdp-ycsz-hgls);
      color: var(--sdp-zs);
      .custom-tree-item {
        color: var(--sdp-tagsc-hover);
      }

      .el-tree-node__label {
        font-weight: 600;
      }
    }
    .el-tree-node__content:hover {
      background-color: var(--sdp-sjj-hs);

      > div {
        span {
          color: var(--sdp-tagsc-hover);
        }
      }
      > .node-disable{
        span {
          color: var(--dataSetNodeDisabledFont, 'zx-roc-0.6606594778684218');
        }
      }
      .is-leaf {
        color: transparent;
      }
    }
  }
}
</style>

<style lang="scss">
.sdp-add-element-new-dialog {
  .el-dialog__header {
    box-shadow: 0px 1px 0px 0px var(--sdp-ycfgx);
  }
  .el-dialog__body {
    padding: 0px !important;
  }
  .el-dialog__footer {
    display: none;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 300px;
      top: 0;
      width: 1px;
      height: 62px;
      box-shadow: 1px 0px 0px 0px var(--sdp-ycfgx);
    }
  }
}
</style>
