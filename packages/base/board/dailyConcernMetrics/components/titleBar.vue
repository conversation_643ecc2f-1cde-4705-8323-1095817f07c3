<template>
<div>
  <div class="title-bar">
    <div class="board-button-wrap">
      <template v-for="item in buttonList">
        <div
          class="board-button-list"
          v-if="item.type === TYPE_BUTTON.BUTTON && item.show"
          @click="item.callback($event)"
        >
          <el-button
            class="board-button iconBox"
            :icon="'icon-font ' + item.icon"
            :loading="item.loading"
            :disabled="item.loading">
            {{item.label}}
          </el-button>
<!--          <i class="iconfont" :class="item.icon" />-->
<!--          <div>{{item.label}}</div>-->
        </div>
      </template>
    </div>
  </div>
</div>
</template>

<script>
import { EVENT_BUS } from '../../displayPanel/constants'
import { EVENT_BTN_TYPE } from '../utils'
import { operationalLog } from '../../displayPanel/components/api'
import { OPERATION_LOG } from 'packages/base/board/displayPanel/boardLanguage'

const TYPE_BUTTON = {
  BUTTON: 'button',
  LINE: 'line',
  DROPDOWN: 'dropDown'
}

export default {
  name: 'dailyTitleBar',
  inject: ['sdpBus', 'utils'],
  data() {
    return {
      TYPE_BUTTON,
      addDialogVisible: false,
      isSave: false,
      saveLoading: false,
      updateStyleLoading: false,
    }
  },
  computed: {
    isPersonalAttentionEdit: {
      get() {
        return this.utils?.isPersonalAttentionEdit
      },
      set(val) {
        this.$set(this.utils, 'isPersonalAttentionEdit', val)
      }
    },
    dailyHasUpdate() {
      return this.utils?.dailyHasUpdate || false
    },
    buttonList() {
      return [
        {
          icon: 'icon-sdp-shuaxin',
          label: this.$t('sdp.button.updateStyle'),
          type: TYPE_BUTTON.BUTTON,
          show: this.dailyHasUpdate && !this.isPersonalAttentionEdit,
          callback: this.updateStyle,
          loading: this.updateStyleLoading,
        },
        // 预览界面
        {
          icon: 'icon-sdp-shuaxin',
          label: this.$t('sdp.button.refreshData'),
          type: TYPE_BUTTON.BUTTON,
          show: true,
          callback: this.refresh
        },
        {
          icon: 'icon-sdp-bianji',
          label: this.$t('sdp.button.edit'),
          type: TYPE_BUTTON.BUTTON,
          show: !this.isPersonalAttentionEdit,
          callback: this.changeEdit.bind(this, true)
        },
        // 编辑界面
        // {
        //   icon: 'icon-sdp-shuaxin',
        //   label: this.$t('sdp.button.refresh'),
        //   type: TYPE_BUTTON.BUTTON,
        //   show: this.isPersonalAttentionEdit,
        //   callback: this.refresh
        // },
        {
          icon: 'icon-sdp-xinzeng1',
          label: this.$t('sdp.button.add'),
          type: TYPE_BUTTON.BUTTON,
          show: this.isPersonalAttentionEdit,
          callback: this.openAddELDialog
        },
        {
          icon: 'icon-sdp-baocun',
          label: this.$t('sdp.button.save'),
          type: TYPE_BUTTON.BUTTON,
          show: this.isPersonalAttentionEdit,
          callback: this.save,
          loading: this.saveLoading,
        },
        {
          icon: 'icon-sdp-fanhui2',
          label: this.$t('sdp.button.return'),
          type: TYPE_BUTTON.BUTTON,
          show: this.isPersonalAttentionEdit,
          callback: this.changeEdit.bind(this, false)
        },
      ]
    },
    api() {
      return this.utils.api || function() {}
    },
  },
  watch: {
    isPersonalAttentionEdit: {
      handler(bool) {
        setTimeout(() => {
          this.sdpBus.$emit(EVENT_BUS.DAILY_CONCERN_SET_PREVIEW_STATE, !bool)
        })
      },
      immediate: true
    }
  },
  components: {
  },
  mounted() {
    this.sdpBus.$on(EVENT_BUS.SBI_CHANGE_EDIT, this.changeEdit)
  },
  beforeDestroy() {
    this.sdpBus.$off(EVENT_BUS.SBI_CHANGE_EDIT, this.changeEdit)
  },
  methods: {
    refresh() {
      this.eventBtn(EVENT_BTN_TYPE.Refurbish)
    },
    updateStyle() {
      if (this.updateStyleLoading) return
      this.updateStyleLoading = true
      this.eventBtn(EVENT_BTN_TYPE.UpdateStyle, () => {
        this.updateStyleLoading = false
      })
    },
    save() {
      if (this.$parent?.$refs?.displayPanel) {
        const displayPanel = this.$parent.$refs.displayPanel
        if (displayPanel.isDailyAdding) {
          this.$message.warning(this.$t('sdp.views.isRunFinish'))
          return
        }
      }
      if (this.saveLoading) return
      this.saveLoading = true
      this.eventBtn(EVENT_BTN_TYPE.Save, () => {
        this.isSave = true
        this.saveLoading = false
        // this.changeEdit(false)
        // this.$emit('initCreated', true)
      })
    },
    // 调用按钮 type --- (EVENT_BTN_TYPE)
    eventBtn(type, params) {
      this.sdpBus.$emit(EVENT_BUS.DAILY_CONCERN_BTN, type, params)
    },
    changeEdit(val) {
      if (!val && !this.isSave) { // 没有保存退出编辑界面
        this.$sdp_eng_confirm(`${this.$t('sdp.views.boardTipsConfirmTitle')}`, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
          confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
          cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
          closeOnHashChange: false,
          type: 'warning',
          closeOnClickModal: false,
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog',
          beforeClose: (action, instance, done) => {
            instance.$el.style.zIndex = -1
            done()
          },
        }).then(() => {
          this.isPersonalAttentionEdit = false
          this.$emit('eventEmit', 'handlerEdit', val)
          this.$emit('initCreated', true)
        }).catch(() => {})
      } else {
        if (this.isSave && !val) {
          this.sdpBus.$emit(EVENT_BUS.DAILY_CONCERN_BTN, 'setIsNoDataConcern')
        }
        this.isPersonalAttentionEdit = val
        this.isSave = false
        this.$emit('eventEmit', 'handlerEdit', val)
        this.logEdit()
      }
      this.$forceUpdate()
    },
    logEdit() {
      const log = this.$_deepClone(OPERATION_LOG)
      log.menuI18Key = 'OSCDWDZY'
      log.modelI18Key = 'OSCDWDZY'
      log.operateType = '2'
      log.objectName = this.$t('sdp.views.dailyConcern')
      log.operateContent = JSON.stringify(`[${this.$t('sdp.views.dailyConcern')}] has been edited.`)
      !this.utils.isShare && operationalLog(this.api, log)
    },
    openAddELDialog() {
      this.eventBtn('openAddELDialog', true)
    },
  }
}
</script>

<style lang="scss" scoped>
.title-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 54px;
  line-height: 54px;
  background: var(--sdp-tb-bj);
  padding: 0 16px;
  position: relative;
  z-index: 10;

  .board-button-wrap {
    height: 30px;
    display: flex;
    align-items: center;

    .board-button-list{
      margin: 0 6px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }

    .board-button {
      /deep/ .icon-font {
        font-size: 14px;
        margin-right: 8px;
        color: var(--sdp-zs);
      }
    }

    .lineBox {
      display: flex;
      align-items: center;
      justify-content: center;

      .line {
        width: 1px;
        height: 34px;
        background: #333;
      }
    }
  }
}
.iconBox {
  cursor: pointer;
  width: auto;
  min-width: 84px;
  height: 30px;
  line-height: 26px;
  padding: 0 18px;
  box-sizing: border-box;
  text-align: center;
  display: inline-block;
  background: var(--sdp-nn-fzds) !important;
  border-width: 1px;
  vertical-align: middle;
  color: var(--sdp-cyfhwzs) !important;
  font-size: 12px;
  border-radius: 2px;
  i {
    font-weight: 400;
    font-size: 16px;
    margin-right: 8px;
    display: inline-block;
    vertical-align: middle;
  }
  div {
    display: inline-block;
    vertical-align: middle;
    font-weight: 600;
  }

  &.iconBoxDisabled{
    cursor: not-allowed;
    i {
      color: var(--color-button-disabled);
      /*color: #553cce;// todo delete*/
    }
  }
  /deep/ {
    .el-icon-loading {
      font-size: 12px !important;
    }
  }
}
.el-button:focus, .el-button:hover {
  // color: var(--color-primary) !important;
  // border: 1px solid var(--color-primary) !important;
  // background: var(--color-sub-2) !important;
}
</style>
