<template>
  <el-dialog
    :visible.sync="dialogShow"
    append-to-body
    :custom-class="`sdp-superLinkDialog sdp-dialog ${getCurrentThemeClass()}`"
    :title="$t('sdp.views.superLink')"
    :close-on-click-modal="false"
  >
    <el-container class="sdp-container">
      <el-aside class="sdp-aside sdp-theme-params-setting-border" width="216px">
        <el-scrollbar class="sdp-scrollbar" v-sdp-el-scrollbar="utils.themeParameters">
          <el-tree
          ref="tree"
          class="box-tree"
          default-expand-all
          :data="treeData"
          node-key="site"
          :props="defaultProps"
          @node-click="nodeSelect"
          :render-content="renderContent"
          :expand-on-click-node="false"
          :highlight-current="true">
          </el-tree>
        </el-scrollbar>
      </el-aside>

      <el-scrollbar class="sdp-scrollbar" v-sdp-el-scrollbar="utils.themeParameters">
        <el-main class="sdp-main">
            <div v-if="formShow" style="height: 100%;padding-bottom: 10px">
              <!-- 传参字段 -->
              <div class="sdp-link-type" v-if="hideChange && !isChartGrid">
                <el-row>
                  <div class="sdp-fieldTitle">
                    <span class="sdp-title fontW">
                      {{$t('sdp.views.theField')}}
                    </span>

                    <!-- 添加 -->
                    <span
                      class="sdp-fieldAdd"
                      @click="addTheField"
                      :style="{cursor: disFieldAdd ? 'not-allowed' : ''}"
                    >
                      <i class="icon-sdp-add"/>
                      <span>{{$t('sdp.button.add')}}</span>
                    </span>
                  </div>

                  <el-col :span="8" v-for="(item, index) in parameterField" :key="index" class="sdp-fieldBox">
                    <!-- 字段选择 -->
                    <el-select
                      :popper-class="'board-design-pop sdp-paramsetting-select '+getCurrentThemeClass()"
                      v-model="parameterField[index]"
                      :placeholder="$t('sdp.placeholder.plsInput')"
                      style="width: 200px"
                      @change="verifyChange(parameterField)"
                    >
                      <sdp-option
                        style="width: 330px"
                        v-for="item in parameterFieldArrs"
                        :key="item.label"
                        :label="item.label"
                        :value="item.label"
                        :item="item"
                        parentName="dataSetId"
                      />
                    </el-select>
                    <!-- 删除 -->
                    <i v-if="index" class="icon-sdp-rongqishanchu" @click="delTheField(index)"/>
                  </el-col>
                </el-row>

                <div style="height: 20px"/>

                <el-row v-if="isHasExclude">
                  <div class="sdp-fieldTitle">
                    <span class="sdp-title fontW">
                      {{$t('sdp.views.ExculdeFeature')}}
                    </span>
                  </div>

                  <el-col :span="8" class="sdp-fieldBox">
                    <el-select
                      multiple
                      :popper-class="'board-design-pop sdp-paramsetting-select '+getCurrentThemeClass()"
                      v-model="labelBoard.excludeColumnNames"
                      :placeholder="$t('sdp.placeholder.plsInput')"
                      style="width: 200px"
                      @change="verifyChange(parameterField)"
                    >
                      <sdp-option
                        style="width: 330px"
                        v-for="item in parameterFieldArrs.filter(item=> item.isExcludeDimension)"
                        :key="item.label"
                        :label="item.label"
                        :value="item.label"
                        :item="item"
                        parentName="dataSetId"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </div>

                <!-- 打开看板 / 默认跳转tab页 -->
                <div class="sdp-link-type">
                  <el-row>
                    <el-col :span="8">
                      <p class="fontW disbance8 param-setting-normal-dataSet-font">{{$t('sdp.views.b_openBoard')}}</p>
                      <div class="sdp-folder">
                        <el-input :title="title" :placeholder="$t('sdp.placeholder.pleaseInputContent')" style="width: 200px" v-model="input">
                          <i slot="suffix"  class="el-input__icon el-icon-folder-remove" @click="dataBoardDialogShow = true"></i>
                        </el-input>
                      </div>
                    </el-col>

                    <el-col :span="8" v-if="superLinkTabList.length > 1">
                      <p class="fontW disbance8 param-setting-normal-dataSet-font">
                        <span>{{$t('sdp.views.b_openDefaultTab')}}</span>
                      </p>

                      <el-select :popper-class="'board-design-pop sdp-paramsetting-select '+getCurrentThemeClass()" v-model="superLinkActiveId" :placeholder="$t('sdp.placeholder.plsInput')" style="width: 200px;">
                        <el-option v-for="item in superLinkTabList" :key="item.id" :label="item.label" :value="item.id">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>

                <!-- 打开方式 / 窗口名称 -->
                <div class="sdp-link-type">
                <el-row>
                  <el-col :span="8">
                    <p class="fontW disbance8 param-setting-normal-dataSet-font">
                      <span>{{$t('sdp.views.b_openStyle')}}</span>
                      <el-tooltip v-if="!utils.isScreen" class="tooltip" effect="dark" :content="$t('sdp.views.popupJumps')" placement="top">
                        <i class="el-tooltip icon-sdp-info"></i>
                      </el-tooltip>
                    </p>
                    <p v-if="isMobile" style="height: 32px;line-height: 32px;" class="param-setting-normal-dataSet-font">{{this.$t('sdp.views.currentPage')}}</p>
                    <el-select v-else :popper-class="'board-design-pop sdp-paramsetting-select '+getCurrentThemeClass()" v-model="value" :placeholder="$t('sdp.placeholder.plsInput')" style="width: 200px;">
                      <el-option v-for="item in options" :key="item.index" :label="item.label" :value="item.index">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="8" v-if="value === '2' && !utils.isScreen">
                    <p class="fontW disbance8 param-setting-normal-dataSet-font">{{$t('sdp.views.windowName')}}:</p>
                    <el-input :placeholder="$t('sdp.placeholder.pleaseInputName')" maxlength="20" v-model="styles.name" style="width: 200px">
                    </el-input>
                  </el-col>
                </el-row>
              </div>

                <!-- 窗口设置 -->
              <section v-if="value === '2'" class="sdp-link-type window-settings-container" style="padding-bottom: 10px">
                <div class="radio-container">
                  <p class="fontW disbance8 param-setting-normal-dataSet-font">{{ $t('sdp.views.windowSettings') }}</p>
                  <el-radio v-model="windowSettings.type" :label="WINDOW_SETTINGS.TYPE.SYSTEM">{{ $t('sdp.views.systemTemplate') }}</el-radio>
                  <el-radio v-model="windowSettings.type" :label="WINDOW_SETTINGS.TYPE.CUSTOM" v-if="utils.isScreen">{{ $t('sdp.views.customTemplate') }}</el-radio>
                  <el-radio v-model="windowSettings.type" :label="WINDOW_SETTINGS.TYPE.FIXED">{{ $t('sdp.views.fixedTemplate') }}</el-radio>
                </div>

                <!-- 系统模板 -->
                <el-row :gutter="16" v-if="windowSettings.type === WINDOW_SETTINGS.TYPE.SYSTEM" class="system-container ">
                  <el-col :span="-1" v-for=" ({ base64, type, text }) in sysTemplateTypeInfoList" :key="type">
                    <div class="image-box" @click="windowSettings.sysValue = type" :class="{ isActive: windowSettings.sysValue === type }">
                      <img :src="base64[themeType]" alt="" />
                      <i v-if="windowSettings.sysValue === type" class="el-icon-check"></i>
                    </div>
                    <p :class="{ isActive: windowSettings.sysValue === type, 'param-setting-normal-dataSet-font': windowSettings.sysValue !== type }">{{ text }}</p>
                  </el-col>
                </el-row>

                <!-- 自定义宽高: 宽 高 边框 颜色 -->
                <el-row :gutter="20" v-if="windowSettings.type === WINDOW_SETTINGS.TYPE.CUSTOM" style="margin-top: 24px;" class="param-setting-normal-dataSet-font">
                  <el-col :span="8">
                    {{$t('sdp.views.aspectRatio')}}:
                   <div style="display: flex;align-items: center">
                     <el-input style="width: 91px" class="ratioStyle-input"
                                      v-model="ratioStyle.width" controls-position="right"
                                      :placeholder="$t('sdp.views.width')"
                                      @change="verifyStyle(ratioStyle.width, 'width')">
                     </el-input>
                     <span style="margin: 0 5px">:</span>
                     <el-input style="width: 91px" class="ratioStyle-input"
                                      v-model="ratioStyle.height" controls-position="right"
                                      :placeholder="$t('sdp.views.height')"
                                      @change="verifyStyle(ratioStyle.height, 'height')">
                     </el-input>
                   </div>
                  </el-col>
                  <el-col :span="4">
                    {{$t('sdp.views.scaling')}}:
                    <el-input-number style="width: 91px" class="ratioStyle-input scaling-input"
                                     v-model="ratioStyle.scaling" controls-position="right" :precision="0"
                                     :min="10" :max="95">
                    </el-input-number>
                  </el-col>
                </el-row>

                <!-- 固定宽高: 宽 高 边框 颜色 -->
                <el-row :gutter="20" v-if="windowSettings.type === WINDOW_SETTINGS.TYPE.FIXED" style="margin-top: 24px;" class="param-setting-normal-dataSet-font">
                  <el-col :span="4">
                    {{$t('sdp.views.width')}}:
                    <el-input  style="width: 91px" :placeholder="$t('sdp.placeholder.pls')" v-model="styles.width">
                    </el-input>
                  </el-col>
                  <el-col :span="4">
                    {{$t('sdp.views.height')}}:
                    <el-input  style="width: 91px" :placeholder="$t('sdp.placeholder.pls')"  v-model="styles.height">
                    </el-input>
                  </el-col>
                  <el-col :span="4">
                    {{$t('sdp.placeholder.border')}}:
                    <el-input-number style="width: 91px" v-model="styles['border-width']" controls-position="right" :min="1" :max="10" :placeholder="$t('sdp.placeholder.plsInput')">
                    </el-input-number>
                  </el-col>
                  <el-col :span="4">
                    <div>{{$t('sdp.placeholder.color')}}:</div>
                    <el-color-picker :popper-class="getCurrentThemeClass()" v-model="styles['border-color']">
                    </el-color-picker>
                  </el-col>
                </el-row>
              </section>

              <!-- 数据集 -->
              <div class="sdp-link-type" style="margin-bottom: 32px;" v-if="hideChange">
                <el-row :gutter="20" v-if="dataArr.length">
                  <el-col :span="6">
                    <span class="fontW">{{$t('sdp.views.dataSet')}}:</span>
                  </el-col>
                  <el-col :span="6">
                    <span  class="fontW">{{$t('sdp.views.field')}}:</span>
                  </el-col>
                  <el-col :span="12" v-if="isHasExclude">
                    <span  class="fontW">{{$t('sdp.views.ExculdeFeature')}}:</span>
                  </el-col>
                </el-row>
                <el-row :gutter="20" align="middle" v-for="(val,index) in dataArr" :key="index" style="padding-top: 10px;">
                  <el-col :span="6">
                    <p :title="val.dataSetArr[0].name" class="test">{{val.dataSetArr[0].name}}</p>
                  </el-col>
                  <el-col :span="6" v-if="val.fieldsArr">
                    <el-select style="width: 100%" :popper-class="'board-design-pop sdp-paramsetting-select '+getCurrentThemeClass()" :class="i ? 'm-Top': ''" v-for="(item, i) in parameterField" :key="i"
                               @change="verifyChange(val.fields)" class="fields" :title="val.fields[i]" v-model="val.fields[i]" :placeholder="$t('sdp.placeholder.plsInput')" clearable>
                      <sdp-option
                        v-for="item in val.fieldsArr" :key="item.labeName" :label="item.labeName" :value="item.labeName" :item="item"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="12" v-if="isHasExclude && val.fieldsArr">
                    <el-select multiple :multiple-limit="10" style="width: 96%" :popper-class="'board-design-pop sdp-paramsetting-select '+getCurrentThemeClass()" :class="i ? 'm-Top': ''" v-for="(item, i) in parameterField" :key="i"
                               @change="verifyChange(val.fields)" class="fields" v-model="val.excludeColumnNames" :placeholder="$t('sdp.placeholder.plsInput')" clearable>
                      <sdp-option
                        v-for="item in val.fieldsArr.filter(item => item.columnTpe !== 'number')" :key="item.labeName" :label="item.labeName" :value="item.labeName" :item="item"
                      />
                    </el-select>
                  </el-col>
                </el-row>
                <filter-condition ref="filterCondition" v-if="filterBool" v-model="filterValue" v-bind="{ filters, type:'superLink', labelBoard, dataArr, filterMap}"/>
              </div>

              <!-- 关联目标看板筛选器 -->
            <associate-params-setting
              v-if="associateParamsShow"
              class="sdp-link-type"
              style="padding-bottom: 24px"
              :associateParamsSetting="associateParamsSetting"
              :board-data="boardData"
              @saveAssociateData="saveAssociateData"/>
            </div>
        </el-main>
      </el-scrollbar>
      <!-- 按钮部分 -->
      <div v-if="formShow" class="sdp-location">
        <div class="el-dialog__footer sdp-box-button">
          <el-button class="sdp-button" type="primary" @click="save">{{$t('sdp.button.ensure')}}</el-button>
          <el-button class="sdp-button" @click="cleanData">{{$t('sdp.views.clear')}}</el-button>
        </div>
      </div>
    </el-container>

    <div slot="footer" style="text-align: right;">
      <el-button type="primary" @click="addComponent">{{$t('sdp.button.ensure')}}</el-button>
      <el-button @click="dialogShow = false">{{$t('sdp.button.cancel')}}</el-button>
    </div>

    <!-- 选择看板 -->
    <data-board-dialog :visible.sync="dataBoardDialogShow" @add-component="getBoardData" :boardInfo="boardInfo" :boardData="boardData">
    </data-board-dialog>
  </el-dialog>

</template>
<script>// @ts-nocheck

import { TYPE_ELEMENT, WINDOW_SETTINGS } from 'packages/base/board/displayPanel/constants'
import { BASE_64 } from 'packages/assets/newTheme/constant'
import { SUPERLINK_CONST_TYPE, THEME_TYPE, CHART_TYPE } from 'packages/assets/constant'
import DataBoardDialog from './components/DataBoardDialog'
import filterCondition from './components/filterCondition'
import * as api from './api'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import { filterMapVerify } from '../../displayPanel/utils'
import superLink_mixins from './superLink_mixins'
import { SUPER_LINK_CONST_SITE, TAGNEWCARD } from '../../../../assets/constant'
import { CHART_ALIAS_TYPE, DIMENSION_SUPER_LINK_CHART } from '../../displayPanel/supernatant/boardElements/elementChart/constant'
import SdpOption from 'packages/base/common/sdpOption/index.vue'
import associateParamsSetting from './components/associateParamsSetting'
import { breadth } from 'packages/base/board/displayPanel/supernatant/boardElements/elementDupontAnalysis/bridge'
function getWindowSettingsDftOPts() {
  return {
    type: WINDOW_SETTINGS.TYPE.DEFAULT,
    sysValue: WINDOW_SETTINGS.SYS_VALUE.DEFAULT,
  }
}

function getStylesDftOpts(name = '') {
  return {
    name,
    'width': '',
    'height': '',
    'border-width': '',
    'border-color': '#fff',
    'border-style': 'solid',
  }
}

function getRatioStyle(name = '') {
  return {
    name,
    'width': '',
    'height': '',
    'scaling': undefined,
  }
}

function getAssociateParamsSetting() {
  return {
    isAssociateParams: false,
    associateParamsData: [],
    allAssociateParamsData: []
  }
}

export default {
  name: 'SuperLinkDialog',
  components: {
    DataBoardDialog,
    filterCondition,
    SdpOption,
    associateParamsSetting,
  },
  inject: ['utils', 'getUnknownName', 'getCurrentThemeClass'],
  mixins: [superLink_mixins],
  directives: {
    SdpElScrollbar,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    highlightEl: {
      type: Object,
      default: () => ({}),
    },
    elList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      id: '',
      dataBoardDialogShow: false,
      treeData: [],
      associateParamsSetting: getAssociateParamsSetting(),
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      boardData: null,
      formShow: false,
      boardArrData: [],
      labelBoard: null,
      value: '1',
      input: '',
      dataSet: '',
      fields: [],
      dataArr: [],
      fieldsArr: [],
      hideChange: true,
      verifyFlag: false,
      styles: getStylesDftOpts(),
      ratioStyle: getRatioStyle(),

      windowSettings: getWindowSettingsDftOPts(),

      sysTemplateTypeInfoList: [
        {
          base64: BASE_64[WINDOW_SETTINGS.SYS_VALUE.LARGE],
          type: WINDOW_SETTINGS.SYS_VALUE.LARGE,
          text: this.$t('sdp.views.large')
        },
        {
          base64: BASE_64[WINDOW_SETTINGS.SYS_VALUE.STANDARD],
          type: WINDOW_SETTINGS.SYS_VALUE.STANDARD,
          text: this.$t('sdp.views.standard')
        },
        {
          base64: BASE_64[WINDOW_SETTINGS.SYS_VALUE.VERTICAL],
          type: WINDOW_SETTINGS.SYS_VALUE.VERTICAL,
          text: this.$t('sdp.views.vertical')
        },
        {
          base64: BASE_64[WINDOW_SETTINGS.SYS_VALUE.HORIZONTAL],
          type: WINDOW_SETTINGS.SYS_VALUE.HORIZONTAL,
          text: this.$t('sdp.views.horizontal')
        },
      ],

      WINDOW_SETTINGS,

      parameterField: [''],
      parameterFieldArr: [],
      // 找到对应parameterFieldArr的数据集字段
      parameterFieldArrs: [],
      filterValue: false,
      filterMap: null,
      boardInfo: {},
      superLinkTabList: [],
      curActiveId: '', // 当前激活的面板id
      superLinkActiveId: '', // 超链接跳转设置的id
      selectedEl: null,
      saveData: {},
      isHasExclude: false,
    }
  },
  computed: {
    isAdvanceContainer() {
      return this.highlightEl.type === TYPE_ELEMENT.CONTAINER && this.highlightEl.subType === TYPE_ELEMENT.ADVANCE_CONTAINER
    },
    paramsPanelList() { // 原看板的tab页
      let { paramsPanelList: paneList, dynamicTags } = this.boardInfo.content
      if (!paneList?.length || !dynamicTags?.length) return []
      let newTags = this.isMobile ? dynamicTags : this.$_JSONClone(dynamicTags).reverse()
      return this.boardInfo.openBoardTab ? paneList : newTags
    },
    options() {
      return this.isMobile ? [
        { label: this.$t('sdp.views.currentPage'), index: '1' },
      ] : [
        { label: this.$t('sdp.views.currentPage'), index: '1' },
        { label: this.$t('sdp.views.popUpWindow'), index: '2' },
      ]
    },
    // 判断是否隐藏过滤
    filterBool() {
      let filterBool = false
      if (this.filters && this.filters.length) {
        const { dataSetId } = this.labelBoard
        if (dataSetId) {
          filterBool = this.filters.some(item => item.dataSetId === dataSetId)
        }
      }
      return filterBool
    },
    // 打开弹框状态
    dialogShow: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible')
      },
    },
    // 是否为图形的简单表格
    isChartGrid() {
      return this.$_getProp(this.selectedEl, 'content.alias', '') === CHART_TYPE.VE_GRID_NORMAL
    },
    // 过滤数据
    filters() {
      const selectedEl = this.selectedEl
      if (selectedEl.type === TYPE_ELEMENT.TABLE) {
        const filters = selectedEl.content.tableDefaultConfig.filters || []
        if (filters[0] && Object.keys(filters[0]).includes('expression')) {
          return []
        }
        return filters
      } else {
        return []
      }
    },
    api() {
      return this.utils.api || function() {}
    },
    isMobile() {
      return this.utils.isMobile
    },
    themeType() {
      return this.utils.themeParameters.themeType
    },
    // isHasExclude() {
    //   return this.highlightEl.type === TYPE_ELEMENT.TABLE && this.highlightEl.content.originalTable.some(row => row.some(cell => cell.content.exclude))
    // },
    disFieldAdd() {
      if ([TYPE_ELEMENT.CHART, TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(this.selectedEl.type)) {
        return this.parameterField.length >= this.parameterFieldArrs.length
      }
      return this.parameterField.length >= 3
    }
  },
  methods: {
    getHasExclude(highlightEl) {
      if (!highlightEl) return

      return highlightEl.type === TYPE_ELEMENT.TABLE && highlightEl.content.originalTable.some(row => row.some(cell => cell.content.exclude))
    },
    // 对选中的元素做筛选
    getSelectedEl(selectedEl) {
      if (!selectedEl) return selectedEl

      // let selectedEl = this.highlightEl
      if (selectedEl.type === TYPE_ELEMENT.CONTAINER && selectedEl.subType !== TYPE_ELEMENT.ADVANCE_CONTAINER && selectedEl.content && selectedEl.content.activeElId) {
        const arr = [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.TABLE, TYPE_ELEMENT.CHART, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.CUSTOMER_ELEMENT]
        const status = this.elList.some((item) => {
          const bool = (item.id === selectedEl.content.activeElId) && arr.includes(item.type)
          bool && (selectedEl = item)
          return bool
        })
        if (status) {
          return selectedEl
        }
      } else if ([TYPE_ELEMENT.ELEMENT_TITLE, TYPE_ELEMENT.CHART, TYPE_ELEMENT.TABLE, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.COMBINE_CARD, TYPE_ELEMENT.CUSTOMER_ELEMENT, TYPE_ELEMENT.DUPONT_ANALYSIS].includes(selectedEl.type)) {
        return selectedEl
      } else if (this.isAdvanceContainer) {
        return selectedEl
      }
      return null
    },
    // 验证
    verifyChange(a) {
      this.verifyFlag = false
      if (/(\\x0f[^\\x0f]+)\\x0f[\s\S]*\1/.test('\\x0f' + a.join('\\x0f\\x0f') + '\x0f')) {
        this.verifyFlag = true
        this.$message(`${this.$t('sdp.views.FieldRepeated')}`)
      }
    },
    verifyStyle(value, field) {
      if (!value) return
      let curVal = value.toString().replace(/[^\d]/g, '')
      curVal = Number(curVal)
      curVal = curVal < 1 ? 1 : curVal > 50 ? 50 : curVal
      this.$set(this.ratioStyle, field, curVal)
    },
    // 清空数据
    resetData() {
      this.filterValue = false
      this.input = ''
      this.title = ''
      this.dataArr = []
      this.value = '1'
      this.verifyFlag = false
      this.superLinkActiveId = ''
      this.associateParamsSetting = getAssociateParamsSetting()
      this.superLinkTabList = []
      this.styles = getStylesDftOpts()
      this.windowSettings = getWindowSettingsDftOPts()
      this.boardData = {}
      return () => { this.parameterField = [''] }
    },
    onRepeat(arr) {
      const obj = {}
      return arr.reduce((cur, next) => {
        if (!obj[next.label]) {
          obj[next.label] = true
          cur.push(next)
        }
        return cur
      }, [])
    },
    // 初始化
    initData(selectedEl, isCallNodeSelect) {
      const isHasExclude = this.getHasExclude(selectedEl)
      const saveId = this.$_generateUUID()
      let boardArrData = []
      let parameterFieldArr = []
      // 记录当前表格是否设置了看板跳转位置
      let labelBoard = []
      if (selectedEl.content?.superLinkOptions) {
        boardArrData = this.$_JSONClone(selectedEl.content.superLinkOptions)
        labelBoard = boardArrData.map(item => item.labelBoard.site).filter(e => e)
      }
      let treeData = []
      // 清除位置不对的数据
      let selectSiteList = []
      const constant = SUPERLINK_CONST_TYPE.constant
      const isChartGrid = selectedEl.content?.alias === CHART_ALIAS_TYPE.VE_GRID

      let callback = () => {}
      // 表格设置 简单表格也可以是设置
      // 初始化方法
      // if (selectedEl.type === TYPE_ELEMENT.TABLE || selectedEl.content?.alias === 've-grid-normal') {
      //   selectSiteList = this[`${TYPE_ELEMENT.TABLE}_init`](selectedEl, labelBoard)
      // } else if ([TYPE_ELEMENT.CHART, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.COMBINE_CARD].includes(selectedEl.type)) {
      //   selectSiteList = this[`${selectedEl.type}_init`](selectedEl, labelBoard)
      // }
      if (selectedEl.type === TYPE_ELEMENT.TABLE || isChartGrid) {
        const vm = selectedEl.vm
        const { text = [], variable = [], titleText = '' } = vm.getVariableAndText()
        const textArr = []
        const variableArr = []

        if (titleText) {
          const site = SUPER_LINK_CONST_SITE.TITLE
          const attr = labelBoard.includes(site)
          textArr.push({ label: titleText, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site, isHideSite: isChartGrid, saveId })
          attr && selectSiteList.push(site)
        }

        text.forEach(element => {
          const { val, isHide, site = '' } = element
          if (!isHide && /\S/.test(val)) {
            let attr = labelBoard.includes(site)
            textArr.push({ label: val, type: constant, attr, site, saveId })
            if (attr) {
              selectSiteList.push(site)
            }
          }
        })

        variable.forEach(element => {
          const { isHide, site = '', dataSetId, val, isExcludeDimension } = element
          let attr = labelBoard.includes(site)
          const data = { label: val, type: SUPERLINK_CONST_TYPE.variavle, dataSetId, attr, site, isExcludeDimension, isHideSite: isChartGrid, saveId, isHasExclude }
          if (attr) {
            selectSiteList.push(site)
          }
          // 隐藏不可点击跳转  但可以配置
          !isHide && variableArr.push(data)
          parameterFieldArr.push(data)
        })
        parameterFieldArr = this.onRepeat(parameterFieldArr)

        treeData = [{
          label: this.$t('sdp.views.b_tableVar'),
          children: variableArr,
        }, {
          label: this.$t('sdp.views.b_tableConst'),
          children: textArr,
        }]
        // 图形设置
      } else if (selectedEl.type === TYPE_ELEMENT.CHART) {
        const chioceTabList = selectedEl?.content?.chioceTab || []
        const vm = selectedEl.vm

        if (chioceTabList && chioceTabList.length) {
          // 设置了关联数据集的看板元素不支持维度超链接
          let { content = {}, type } = this.highlightEl
          // 判断是否有关联数据集
          const isAssociationDataset = this.$_getProp(content, 'chartUserConfig.datasetAssociation', false) && Object.keys(content.associatedData).length

          chioceTabList.forEach(chioceTab => {
            const node = {
              label: chioceTab.name,
              children: [],
            }
            const { id: tabId, name: tabName, saveObj } = chioceTab
            const { chartUserConfig } = saveObj
            const { text = [], variable = [], titleText = '' } = vm.getVariableAndText(chartUserConfig)
            const textArr = []
            const variableArr = []

            if (titleText) {
              const site = `${tabId}_${SUPER_LINK_CONST_SITE.TITLE}`
              const id = selectedEl.id
              const attr = labelBoard.includes(site)
              textArr.push({
                label: titleText,
                id: id,
                type: SUPERLINK_CONST_TYPE.title,
                attr,
                site,
                isHideSite: true,
                saveId
              })
              attr && selectSiteList.push(site)
            }

            // 地图方案
            let hasMapSchemeSetting = false
            if (type === TYPE_ELEMENT.CHART && chartUserConfig.chartAlias === 've-map-parent') {
              hasMapSchemeSetting = content.mapSchemeSetting?.schemeList?.length
            }

            if (variable.length && !isAssociationDataset && !hasMapSchemeSetting) {
              variable.forEach(element => {
                const { isHide, site = '', dataSetId, val, keyName, alias } = element
                const attr = labelBoard.includes(site)
                const data = {
                  label: val,
                  alias,
                  type: SUPERLINK_CONST_TYPE.variavle,
                  dataSetId,
                  attr,
                  site,
                  keyName,
                  isHideSite: true,
                  saveId
                }
                // 隐藏不可点击跳转  但可以配置
                !isHide && variableArr.push(data)
                parameterFieldArr.push(data)
              })
              variable && selectSiteList.push('')

              const data = {
                label: this.$t('sdp.views.chartDimension'),
                dataSetId: this.$_getProp(saveObj, 'drillSettings.dataSetId', ''),
                site: tabId,
                id: selectedEl.id,
                type: SUPERLINK_CONST_TYPE.variavle,
                chartDimension: variableArr,
                isHideSite: true,
                saveId
              }
              const attr = !!boardArrData.find(item => data.label === this.$t('sdp.views.chartDimension') && item.labelBoard.label === data.label && item.boardData && item.labelBoard.site === data.site)

              attr && selectSiteList.push(data.site)

              node.children.push(
                {
                  ...data,
                  attr
                }
              )

              titleText && node.children.push({
                label: this.$t('sdp.views.chartTitle'),
                children: textArr,
              })
            }

            treeData.push(node)
          })
        } else {
          const { text = [], variable = [], titleText = '' } = vm.getVariableAndText()
          const textArr = []
          const variableArr = []

          if (titleText) {
            const site = SUPER_LINK_CONST_SITE.TITLE
            const attr = labelBoard.includes(site)
            textArr.push({ label: titleText, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site, saveId })
            attr && selectSiteList.push(site)
          }

          // 设置了关联数据集的看板元素不支持维度超链接
          let { content = {}, type } = this.highlightEl
          // 判断是否有关联数据集
          const isAssociationDataset = this.$_getProp(content, 'chartUserConfig.datasetAssociation', false) && Object.keys(content.associatedData).length
          // 地图方案
          let hasMapSchemeSetting = false
          if (type === TYPE_ELEMENT.CHART && content.chartUserConfig.chartAlias === 've-map-parent') {
            hasMapSchemeSetting = content.mapSchemeSetting?.schemeList?.length
          }
          if (variable.length && !isAssociationDataset && !hasMapSchemeSetting) {
            variable.forEach(element => {
              const { isHide, site = '', dataSetId, val, keyName, alias } = element
              let attr = labelBoard.includes(site)
              const data = { label: val, alias, type: SUPERLINK_CONST_TYPE.variavle, dataSetId, attr, site, keyName, saveId }
              // 隐藏不可点击跳转  但可以配置
              !isHide && variableArr.push(data)
              parameterFieldArr.push(data)
            })
            variable && selectSiteList.push('')
            const data = {
              label: this.$t('sdp.views.chartDimension'),
              // style: 'nodeStyle',
              dataSetId: this.$_getProp(selectedEl, 'content.drillSettings.dataSetId', ''),
              site: '',
              id: selectedEl.id,
              type: SUPERLINK_CONST_TYPE.variavle,
              chartDimension: variableArr,
              saveId
            }
            let attr = !!boardArrData.find(item => data.label === this.$t('sdp.views.chartDimension') && item.labelBoard.label === data.label && item.boardData)

            treeData.push(
              {
                ...data,
                attr
              }
            )
          }
          titleText && treeData.push({
            label: this.$t('sdp.views.chartTitle'),
            // style: 'nodeStyle',
            children: textArr,
          })
          const superLinkOptions = boardArrData
          const superLinkItem = superLinkOptions?.find(item => {
            return item?.labelBoard?.type !== '' && ![SUPERLINK_CONST_TYPE.title, SUPERLINK_CONST_TYPE.variavle].includes(item.labelBoard.type)
          })
          let curTitle = selectedEl.content?.chartUserConfig?.title?.text
          // 兼容以前的看板
          if (curTitle && superLinkItem) {
            const attr = labelBoard.includes(SUPER_LINK_CONST_SITE.TITLE)
            superLinkItem.labelBoard = { label: curTitle, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site: SUPER_LINK_CONST_SITE.TITLE }
            selectSiteList.push(SUPER_LINK_CONST_SITE.TITLE)
          }
        }
      } else if (selectedEl.type === TYPE_ELEMENT.TEXT) {
        // 文本框设置
        const vm = selectedEl.vm
        const { variable = [], titleText = '' } = vm.getVariableAndText()
        const textArr = []

        if (titleText) {
          const site = SUPER_LINK_CONST_SITE.TITLE
          const attr = labelBoard.includes(site) || labelBoard.includes(selectedEl.id)
          textArr.push({ label: titleText, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site, saveId })
          attr && selectSiteList.push(site)
        }

        let variableGroup = variable.reduce((arr, item) => {
          arr[item.dataSetName] = arr[item.dataSetName] || []
          arr[item.dataSetName].push(item)
          return arr
        }, {})

        if (variable.length) {
          Object.keys(variableGroup).forEach(group => {
            let groupList = []
            variableGroup[group].map(element => {
              const { keyName, site = '', dataSetId, val } = element
              let attr = labelBoard.includes(site)
              attr && selectSiteList.push(site)
              const data = { label: val, type: SUPERLINK_CONST_TYPE.variavle, dataSetId, attr, site, isHideSite: true, saveId }
              groupList.push(data)
            })
            parameterFieldArr.push(...groupList)
            treeData.push({
              label: group,
              // style: 'nodeStyle',
              children: groupList,
            })
          })
        }
        titleText && treeData.push({
          label: this.$t('sdp.views.textTitle'),
          // style: 'nodeStyle',
          children: textArr,
        })
        const superLinkOptions = boardArrData
        const superLinkItem = superLinkOptions?.find(item => {
          return item?.labelBoard?.type !== '' && ![SUPERLINK_CONST_TYPE.title, SUPERLINK_CONST_TYPE.variavle].includes(item.labelBoard.type)
        })
        let curTitle = selectedEl.content?.title
        // 兼容以前的看板
        if (curTitle && superLinkItem) {
          const attr = labelBoard.includes(SUPER_LINK_CONST_SITE.TITLE) || labelBoard.includes(selectedEl.id)
          superLinkItem.labelBoard = { label: curTitle, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site: SUPER_LINK_CONST_SITE.TITLE }
          selectSiteList.push(SUPER_LINK_CONST_SITE.TITLE)
        }
      } else if (selectedEl.type === TYPE_ELEMENT.ELEMENT_TITLE) {
        // 文本框设置
        const vm = selectedEl.vm
        const { variable = [], titleText = '', superlinkType } = vm.getVariableAndText()
        const textArr = []

        if (titleText) {
          const site = selectedEl.id
          const attr = labelBoard.includes(site) || labelBoard.includes(selectedEl.id)
          textArr.push({ label: titleText, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site, saveId })
          attr && selectSiteList.push(site)
          treeData.push({
            label: titleText,
            id: selectedEl.id,
            type: superlinkType === 'title' ? SUPERLINK_CONST_TYPE.title : SUPERLINK_CONST_TYPE.constant,
            attr,
            site,
            saveId,
            isHideSite: true,
          })
        }

        let variableGroup = variable.reduce((arr, item) => {
          arr[item.dataSetName] = arr[item.dataSetName] || []
          arr[item.dataSetName].push(item)
          return arr
        }, {})

        if (variable.length) {
          Object.keys(variableGroup).forEach(group => {
            let groupList = []
            variableGroup[group].map(element => {
              const { keyName, site = '', dataSetId, val } = element
              let attr = labelBoard.includes(site)
              attr && selectSiteList.push(site)
              const data = { label: val, type: SUPERLINK_CONST_TYPE.variavle, dataSetId, attr, site, isHideSite: true, saveId }
              groupList.push(data)
            })
            parameterFieldArr.push(...groupList)
            treeData.push({
              label: group,
              // style: 'nodeStyle',
              children: groupList,
            })
          })
        }

        const superLinkOptions = boardArrData
        const superLinkItem = superLinkOptions?.find(item => {
          return item?.labelBoard?.type !== '' && ![SUPERLINK_CONST_TYPE.title, SUPERLINK_CONST_TYPE.variavle].includes(item.labelBoard.type)
        })
        let curTitle = selectedEl.content?.title
        // 兼容以前的看板
        if (curTitle && superLinkItem) {
          const attr = labelBoard.includes(SUPER_LINK_CONST_SITE.TITLE) || labelBoard.includes(selectedEl.id)
          superLinkItem.labelBoard = { label: curTitle, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site: SUPER_LINK_CONST_SITE.TITLE }
          selectSiteList.push(SUPER_LINK_CONST_SITE.TITLE)
        }
      } else if (selectedEl.type === TYPE_ELEMENT.CUSTOMER_ELEMENT) {
        const { treeData: treeDataTmp, parameterFieldArr: parameterFieldArrTmp } = this.customerInit(selectedEl, labelBoard, selectSiteList, saveId)
        treeData = treeDataTmp
        parameterFieldArr = parameterFieldArrTmp
      } else if ([TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD].includes(selectedEl.type)) {
          const isDoubleCard = TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD === selectedEl.type && selectedEl?.content?.tagNewCardContent === TAGNEWCARD.TWOINDICES
          const { id, elName, type } = selectedEl
          const superLinkOptions = boardArrData
          const superLinkItem = superLinkOptions.find(item => item.labelBoard.id === id)
          // 兼容以前的看板
          if (superLinkItem) {
            superLinkItem.labelBoard.site = id
          }
          const item = {
            label: elName,
            id: id,
            type: constant,
            attr: !!superLinkItem,
            site: id,
            saveId,
            elType: type }
          treeData = [item]
          if (superLinkItem) {
            selectSiteList.push(item.site)
          }
          // 双指标卡片特殊处理
          if (isDoubleCard) {
            const optionArray = selectedEl?.content?.optionArray || []
            treeData[0].children = optionArray.map(({ cardName }, index) => {
              const id = `${selectedEl.id}_${index}`
              const i = index + 1
              const attr = !!superLinkOptions.find(item => item.labelBoard.id === id)
              if (attr) {
                selectSiteList.push(id)
              }
              return {
                label: `${cardName}-${i}`,
                id,
                type: constant,
                attr,
                site: id,
                elType: selectedEl?.content?.tagNewCardContent || type,
                saveId
              }
            })
            // 兼容以前的看板
            if (superLinkItem) {
              superLinkItem.labelBoard.children = treeData[0].children
            }
          } else if (isCallNodeSelect) {
            callback = () => {
              this.nodeSelect(item)
              this.setHighlight(item)
            }
          }
      } else if ([TYPE_ELEMENT.COMBINE_CARD].includes(selectedEl.type)) {
        const { id, elName, type, content: { choiceTab } } = selectedEl
        treeData = [
          {
            // site: id,
            id,
            label: elName,
            type: constant,
            elType: type,
            children: choiceTab.map(({ id: i, name, saveObj: { cardList = [] } }) => ({
              // site: `${id}_${i}`,
              id: `${id}_${i}`,
              label: name,
              type: constant,
              elType: `${type}_choiceTab`,
              children: cardList.map(({ id, elName, type, content }) => {
                const optionArray = content?.optionArray || []
                if (content?.tagNewCardContent === TAGNEWCARD.TWOINDICES) {
                  const attr = labelBoard.includes(id)
                  attr && selectSiteList.push(id)
                  return {
                    site: id,
                    id,
                    attr,
                    label: 'Double Indexes Card',
                    type: constant,
                    elType: type,
                    children: optionArray.map(({ cardName }, index) => {
                      const site = `${id}_${index}`
                      const attr = labelBoard.includes(`${id}_${index}`)
                      attr && selectSiteList.push(site)
                      const i = index + 1
                      return {
                        label: `${cardName}-${i}`,
                        id: site,
                        type: constant,
                        site,
                        attr,
                        elType: content?.tagNewCardContent || type,
                        saveId
                      }
                    })
                  }
                }
                const attr = labelBoard.includes(id)
                attr && selectSiteList.push(id)
                return {
                  site: id,
                  id,
                  label: optionArray[0]?.cardName,
                  type: constant,
                  attr,
                  elType: type,
                  saveId
                }
              })
            }))
          }
        ]
      } else if ([TYPE_ELEMENT.DUPONT_ANALYSIS].includes(selectedEl.type)) {
        const { id, elName, type, content: { cardData = [], userConfig: { title, treeData: tData } } } = selectedEl
        const textArr = []

        const cardDatas = breadth(tData[0], cardData)

        treeData = [
          {
            id,
            label: elName,
            type: constant,
            elType: type,
            children: cardDatas.filter(({ content }) => {
              const optionArray = content?.optionArray || []
              return optionArray[0]?.cardName
            }).map(({ id, elName, type, content }) => {
              const optionArray = content?.optionArray || []
              const attr = labelBoard.includes(id)
              attr && selectSiteList.push(id)
              return {
                site: id,
                id,
                label: optionArray[0]?.cardName,
                type: constant,
                attr,
                elType: type,
                saveId
              }
            })
          }
        ]

        const titleText = title?.value

        if (titleText) {
          const site = SUPER_LINK_CONST_SITE.TITLE
          const attr = labelBoard.includes(site)
          textArr.push({ label: titleText, id: selectedEl.id, type: SUPERLINK_CONST_TYPE.title, attr, site, saveId })
          attr && selectSiteList.push(site)
          treeData.push({
            label: this.$t('sdp.views.propTitle', { prop: this.$t('sdp.views.dupontAnalysisChart') }),
            children: textArr,
          })
        }
      }

      this.$set(this.saveData, saveId, {
        selectedEl,
        parameterFieldArr,
      })

      return {
        treeData,
        boardArrData: boardArrData.filter(item => selectSiteList.includes(item.labelBoard.site)).map(item => ({
          ...item,
          labelBoard: {
            ...item.labelBoard,
            saveId,
          },
          elementId: selectedEl.id
        })),
        parameterFieldArr,
        callback
      }
    },
    /**
     * 自定义元素初始化
     * @param selectedEl 选择的元素 类型为元素
     * @param labelBoard 已选的数据
     */
    customerInit(selectedEl = {}, labelBoard = [], selectSiteList = [], saveId = '') {
      const { dataSetIds = [], userConfig = {}, config = {} } = selectedEl.content
      const { dimensions = [], metrics = [] } = userConfig

      const fieldList = dimensions.concat(metrics)

      const nodes = fieldList.map(e => {
        const site = JSON.stringify(e)
        const attr = labelBoard.includes(site)
        attr && selectSiteList.push(site)

        return {
          ...e,
          label: e.labeName,
          type: SUPERLINK_CONST_TYPE.variavle,
          dataSetId: e.parentId,
          site,
          attr,
          saveId,
          elType: selectedEl.type,
        }
      })

      this.parameterFieldArr.push(...nodes)

      const treeData = [{
        label: this.$t('sdp.views.customerElement'),
        children: nodes,
      }]
      const titleText = config.title?.text?.trim()
      if (titleText) {
        const site = SUPER_LINK_CONST_SITE.TITLE
        labelBoard.includes(site) && selectSiteList.push(site)
        treeData.push({
          label: this.$t('sdp.views.propTitle', { prop: this.$t('sdp.views.customerElement') }),
          children: [{
            label: titleText,
            id: selectedEl.id,
            type: SUPERLINK_CONST_TYPE.title,
            saveId,
            site: site,
            attr: labelBoard.includes(site),
          }],
        })
      }

      console.log('已选的数据 dataSetIds', dataSetIds)
      console.log('已选的数据 userConfig', userConfig)
      console.log('已选的数据 dimensions', dimensions)
      console.log('已选的数据 metrics', metrics)

      return { treeData, parameterFieldArr: nodes, }
    },
    // 设置高亮
    setHighlight(param) {
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(param)
      })
    },
    // 删除传参字段
    delTheField(i) {
      this.parameterField.splice(i, 1)
      if (this.dataArr && this.dataArr.length) {
        this.dataArr.forEach(item => {
          item.fields.splice(i, 1)
        })
      }
    },
    // 添加传参字段
    addTheField() {
      if (this.disFieldAdd) return
      if (this.selectedEl.type === TYPE_ELEMENT.CHART) {
        let curFields = this.parameterFieldArrs.filter(item => {
          return !this.parameterField.includes(item.label)
        })
        this.parameterField.push(curFields[0].label)
        this.setDataField()
        return
      }
      this.parameterField.push('')
    },
    // 点击保存数据到element上
    addComponent() {
      const data = Object.values(this.saveData).reduce((pre, next) => {
        next.selectedEl?.id && (pre[next.selectedEl.id] = [])
        return pre
      }, {})

      const boardObjData = this.boardArrData.reduce((pre, next) => {
        if (pre[next.elementId]) {
          pre[next.elementId].push(next)
        } else {
          pre[next.elementId] = [next]
        }
        return pre
      }, data)

      for (const key in boardObjData) {
        if (Object.hasOwnProperty.call(boardObjData, key)) {

          const selectedEl = this.elList.find(item => item.id === key)
          if (!selectedEl?.content) {
            // this.dialogShow = false
            // return false
            continue
          }
          const data = boardObjData[key]

          if (data.length) {
            if (selectedEl.type === TYPE_ELEMENT.TABLE) {
              const dataSetArr = []
              const vm = selectedEl.vm

              data.forEach((item) => {
                const { label: field, dataSetId, site, type } = item.labelBoard
                // 标题不需做单元格处理
                if (type === SUPERLINK_CONST_TYPE.title) {
                  return
                }
                const pos = vm.getCellPositionByField({ field, type: 'super', dataSetId, data: item, site })
                pos && dataSetArr.push(pos)
              })

              vm.setCellsProperty(dataSetArr, { 'link': !!dataSetArr.length })
            }
            // else if ([TYPE_ELEMENT.COMBINE_CARD, TYPE_ELEMENT.CHART, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD].includes(selectedEl.type)) {
            // }
          } else if (selectedEl.type === TYPE_ELEMENT.TABLE) { // this.boardArrData.length===0 可能存在用户将超链接配置删除了但未更新canvas
            const vm = selectedEl.vm
            vm.paintTable(this.$_JSONClone(vm.copyTable))
          }

          this.setSuperLinkOptions(data, selectedEl)
        }
      }

      this.dialogShow = false
    },
    setSuperLinkOptions(boardArrData = [], selectedEl) {
      boardArrData = boardArrData && this.$_JSONClone(boardArrData)

      boardArrData?.length ? this.$set(selectedEl.content, 'superLinkOptions', boardArrData) : this.$delete(selectedEl.content, 'superLinkOptions')

      const isChartGrid = selectedEl.content?.alias === CHART_ALIAS_TYPE.VE_GRID

      if (isChartGrid) {
        const vm = selectedEl.vm
        vm && vm.createTable()
      }
    },
    // 处理选中数据
    async getBoardData({ boardData: data, dataSets: dataArr = [], openFilter = false, filterMap = null, superLinkActiveId = '' }, type = '') {
      const value = this.value
      const styles = this.styles
      const windowSettings = this.windowSettings
      this.resetData()
      this.value = value
      this.styles = styles
      this.windowSettings = windowSettings

      this.boardData = await api.getBoardInfo.call(this, data.id)
      this.getSuperLinkTab(superLinkActiveId)
      const { dataSets, elListDataSetIds } = JSON.parse(this.boardData.content)
      const dataSetArrs = await api.getDatasetListByIds.call(this, [...new Set([...dataSets, ...elListDataSetIds])])
      let fields = []
      let excludeColumnNames = []
      for (let index = 0; index < dataSetArrs.length; index++) {
        let item = dataSetArrs[index]
        let exitExclude = (dataArr && dataArr.length) && dataArr.some((val) => {
          if (val.dataSetId === item.id) {
            fields = typeof val.columnName === 'string' ? [val.columnName] : val.columnName
            if (val.excludeColumnNames) {
              excludeColumnNames = [...val.excludeColumnNames]
            }
          }
          return val.dataSetId === item.id
        })
        this.dataArr.push({
          dataSet: item.id,
          dataSetArr: [item],
          fields: exitExclude ? fields : [],
          excludeColumnNames: exitExclude ? excludeColumnNames : [],
          fieldsArr: [],
        })
        await this.dataSetChange(item.id, index)
      }
      if ([TYPE_ELEMENT.CHART, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.TABLE, TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(this.selectedEl.type) && type !== 'init') {
        this.setDataField()
      }
      this.input = this.boardData.code
      this.title = this.boardData.code + ' ' + this.boardData.name
      if (filterMapVerify({ filterMap, filterData: this.filters })) {
        this.filterValue = false
        this.filterMap = null
      } else {
        this.filterValue = openFilter
        this.filterMap = filterMap
      }
    },
    getSuperLinkTab(id = '') {
      if (!this.boardData?.content) return
      let { paramsPanelList: paneList, dynamicTags, boardInfo: { openBoardTab } } = JSON.parse(this.boardData?.content) || {}
      let newTags = this.isMobile ? dynamicTags : this.$_JSONClone(dynamicTags).reverse()
      // let reverseTags = newTags.splice(0, newTags.length, ...newTags.reverse())
      this.superLinkTabList = openBoardTab ? paneList : newTags
      if (id) { // 对于设置过的默认tab做处理
        if (this.superLinkTabList.find(item => item.id === id)) {
          this.superLinkActiveId = id
        } else {
          const contraryTabList = openBoardTab ? newTags : paneList // 与当前要匹配的tab相反
          let curId = contraryTabList.findIndex(item => item.id === id)
          if (curId > -1) {
            this.superLinkActiveId = this.superLinkTabList[curId]?.id || ''
          } else if (this.superLinkTabList.length === 1) {
            this.superLinkActiveId = this.superLinkTabList[0]?.id || ''
          } else {
            // this.superLinkActiveId = ''
            this.$message.warning(this.$t('sdp.views.defaultTabNotExist'))
          }
        }
        return
      }
      let activeTab = this.paramsPanelList.findIndex(item => item.active)
      if (!id && activeTab > -1) {
        let targetTab = this.superLinkTabList.length > activeTab ? this.superLinkTabList[activeTab] : this.superLinkTabList[0]
        this.superLinkActiveId = targetTab?.id
      }
    },
    async dataSetChange(value, index) {
      const fieldsArr = await api.getDatasetFields.call(this, value)
      // todo 5946
      this.dataArr[index].fieldsArr = fieldsArr
      // this.dataArr[index].fieldsArr = fieldsArr.map((e, index) => {
      //   if (!e.aliasName) e.aliasName = `${e.labeName}_${index}`
      //   return e
      // })
    },
    // 是否指标卡片设置互斥
    isMutuallyDoubleCard(node) {
      if ([TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.COMBINE_CARD].includes(this.selectedEl.type)) {
        if (node.children && node.children.every(item => item.elType === TAGNEWCARD.TWOINDICES)) {
          const ids = node.children.map(item => item.id)
          return this.boardArrData.find(item => ids.includes(item.labelBoard.id))
        } else if (node.elType === TAGNEWCARD.TWOINDICES) {
          return this.boardArrData.find(item => {
            const children = item.labelBoard.children
            if (children) {
              const ids = children.map(item => item.id)
              return ids.includes(node.id)
            }
            return false
          })
        }
      }
      return false
    },
    // 给数据集字段设置原看板字段 dataSet
    setDataField () {
      this.dataArr.forEach(item => {
        if (!item?.fieldsArr?.length) return
        this.$set(item, 'fields', JSON.parse(JSON.stringify(this.parameterField)))
        item.fields.find((cur, index) => {
          if (!item.fieldsArr.find(field => { return field.labeName === cur })) {
            item.fields[index] = ''
          }
        })
      })
    },
    // 还原去除
    reductionDislodge(node) {
      const parameterFieldArrValue = this.parameterFieldArr.map(item => item.label)
      let excludeColumnNames = []
      if (node?.excludeColumnNames) {
        excludeColumnNames = node.excludeColumnNames.filter(value => parameterFieldArrValue.includes((value)))
      } else {
        const { dataSets = [] } = this.boardArrData.find(item => item.labelBoard.site === node.site) || {}
        excludeColumnNames = this.$_flatten(dataSets.map(item => item.excludeColumnNames || [])).filter(value => parameterFieldArrValue.includes((value)))
      }
      this.$set(node, 'excludeColumnNames', [...new Set(excludeColumnNames)])
    },
    // 点击node事件
    nodeSelect(node) {
      console.log('node', node)

      this.isHasExclude = !!node.isHasExclude

      this.hideChange = node.type === SUPERLINK_CONST_TYPE.variavle || node?.chartDimension?.length > 0
      // 优化点击处理
      if (this.labelBoard?.site === node?.site && this.labelBoard?.saveId === node?.saveId) {
        return
      }
      // 高级容器切换存储数据
      if (this.isAdvanceContainer) {
        const saveId = node?.saveId
        if (saveId) {
          const { selectedEl, parameterFieldArr } = this.saveData[saveId]
          this.selectedEl = selectedEl

          this.parameterFieldArr = parameterFieldArr

          let { type = '', content = {} } = selectedEl
          if (type === TYPE_ELEMENT.CHART) {
            const chioceTab = this.$_getProp(content, 'chioceTab', [])

              // 具备指标选择器的元素暂不支持超链接设置
              // if (chioceTab.length > 1) {
              //   this.$message(this.$t('sdp.views.indicatorSelectorsLink'))
              //   return
              // }
          }
        }
      }

      const isChartDimension = node.label === this.$t('sdp.views.chartDimension') && node.type === SUPERLINK_CONST_TYPE.variavle // 是否支持设置维度超链接
      const isMutuallyDoubleCard = this.isMutuallyDoubleCard(node)
      if (!node.site === undefined || isMutuallyDoubleCard) {
        this.formShow = false
        this.labelBoard = null
        return isMutuallyDoubleCard && this.$message.warning(this.$t('sdp.views.skipNotSupported'))
      } else {
        this.labelBoard = node
        const obj = {}
        this.parameterFieldArrs = this.parameterFieldArr.filter(item => item.dataSetId === node.dataSetId).reduce((cur, next) => {
          if (!obj[next.label]) {
            obj[next.label] = true
            cur.push(next)
          }
          return cur
        }, [])
        if (this.selectedEl.type === TYPE_ELEMENT.CHART && this.selectedEl.content?.chioceTab?.length) {
          const chioceTab = this.selectedEl.content.chioceTab
          const targetTab = chioceTab.find(e => e.id === node.site)
          if (targetTab) {
            const dimIds = targetTab.saveObj.chartUserConfig.dimensionList.map(e => e.keyName)
            this.parameterFieldArrs = this.parameterFieldArrs.filter(e => dimIds.includes(e.keyName))
          }
        }

        this.formShow = (node.label !== this.$t('sdp.views.chartDimension') && node.site) || isChartDimension
        if (isChartDimension && node.chartDimension) {
          this.parameterField = node.chartDimension.reduce((cur, next) => {
            next.label && cur.push(next.label)
            return cur
          }, [])
        } else {
          this.parameterField = [node.label]
        }
        this.reductionDislodge(node)
      }
      let bool = true
      this.boardArrData.forEach(item => {
        const { labelBoard: { site = '', saveId = '' }, boardData = {}, dataSets = [], openFilter = false, filterMap = null, style, ratioStyle, windowSettings, soleId, superLinkActiveId, associateParamsSetting } = this.$_JSONClone(item)
        if (site === node.site && saveId === node.saveId) {
          this.getBoardData({ boardData, dataSets, openFilter, filterMap, superLinkActiveId }, 'init')
          this.associateParamsSetting = associateParamsSetting || getAssociateParamsSetting()
          this.fields = typeof item.fields === 'string' ? [item.fields] : item.fields
          this.value = item.openMethod
          this.styles = style

          if (ratioStyle) this.ratioStyle = ratioStyle
          this.windowSettings = windowSettings || (() => {
            const isCurrentWindow = this.value.toString() === '1'
            return isCurrentWindow ? getWindowSettingsDftOPts() : {
              type: WINDOW_SETTINGS.TYPE.FIXED,
              sysValue: WINDOW_SETTINGS.SYS_VALUE.DEFAULT,
            }
          })()

          if (this.windowSettings && this.windowSettings.type === WINDOW_SETTINGS.TYPE.CUSTOM && !ratioStyle) {
            // this.$set(item.windowSettings, 'type', WINDOW_SETTINGS.TYPE.FIXED)
            this.windowSettings = {
              type: WINDOW_SETTINGS.TYPE.FIXED,
              sysValue: WINDOW_SETTINGS.SYS_VALUE.DEFAULT,
            }
          }
          const labels = this.parameterFieldArrs.map(item => item.label)
          const parameterField = typeof item.parameterField === 'string' ? [item.parameterField] : item.parameterField.filter(e => e && labels.includes(e))
          this.parameterField = parameterField.length ? parameterField : ['']
          this.soleId = soleId || this.$_generateUUID()
          bool = false
        }
      })
      bool && this.resetData()
    },
    save() {
      if (this.verifyFlag) {
        this.$message(`${this.$t('sdp.views.FieldRepeated')}`)
        return
      }
      if (this.$refs.filterCondition && this.filterValue) {
        this.filterMap = this.$refs.filterCondition.save()
      }
      const dataSets = []
      if (this.hideChange) {
        // const len = this.parameterField.length
        if (!this.parameterField.every(item => item)) return this.$message.warning(this.$t('sdp.views.plsFillData'))
        this.dataArr.forEach(item => {
          // 数据集中设置一个字段也要保存数据
          // const fields = this.parameterField.every((v, i) => item.fields[i])
          if (item.dataSet) {
            dataSets.push({
              dataSetId: item.dataSet,
              columnName: item.fields,
              excludeColumnNames: item.excludeColumnNames
            })
          }
        })
        const isFillData = this.dataArr.find(item => {
          return this.parameterField.length === item?.fields.filter(e => e)?.length
        })
        if (!dataSets.length || !isFillData) return this.$message.warning(this.$t('sdp.views.plsFillData'))
      }
      if (this.input === '') return this.$message.warning(this.$t('sdp.views.plsFillData'))

      const isFixed = this.windowSettings.type === WINDOW_SETTINGS.TYPE.FIXED
      const isCustom = this.windowSettings.type === WINDOW_SETTINGS.TYPE.CUSTOM
      const customFillData = this.ratioStyle.scaling && (this.ratioStyle.width && this.ratioStyle.height)
      const invalidSize = isFixed ? !this.styles.width || !this.styles.height : !customFillData
      if ((isFixed || isCustom) && this.value === '2' && invalidSize) return this.$message.warning(this.$t('sdp.views.plsFillData'))
      const onlyOneTypeSuperLink = [TYPE_ELEMENT.ELEMENT_TITLE]
      if (this.boardArrData.length) {
        let bool = ''
        let labelBoardType = {}
        this.boardArrData.forEach((item, i) => {
          const { site = '', saveId = '', type } = item.labelBoard
          const { site: nowSite = '', saveId: nowSaveId = '' } = this.labelBoard
          labelBoardType[type] = true
          if (site === nowSite && saveId === nowSaveId) {
            bool = i
          }
        })
        if (bool !== '') {
          this.boardArrData.splice(bool, 1)
        } else if (onlyOneTypeSuperLink.includes(this.selectedEl.type)) {
          if (!Object.keys(labelBoardType).includes(this.labelBoard.type)) {
            // 标题元素只支持给内容/字段中的一种设置超链接
            this.$message(`${this.$t('sdp.views.elementTitleTitleSuperlinkTips')}`)
            return
          }
        }
      }
      // 获得看板id和名字
      const { id, name, content } = this.boardData

      // if (!this.utils.isScreen) {
      //   if (this.value === '2' && this.$_getProp(JSON.parse(content), 'boardInfo.screenModeDate.screenMode', false)) {
      //     return this.$message.warning(this.$t('sdp.views.setHyperlinkCannot'))
      //   }
      // }

      this.windowSettingsDataClean()
      const data = Object.assign({
        dataSets,
        parameterField: this.parameterField.filter(e => e),
        boardData: { id, name },
        openMethod: this.value,
        labelBoard: this.labelBoard,
        style: this.styles,
        ratioStyle: this.ratioStyle,
        windowSettings: this.windowSettings,
        superLinkActiveId: this.superLinkActiveId,
        associateParamsSetting: this.associateParamsSetting,
        soleId: this.soleId || this.$_generateUUID(),
        elementId: this.selectedEl.id
      }, this.filterMap ? this.filterMap : {})

      if (this.verifyCheckInteraction()) {
        this.$message(this.$t('sdp.views.reminder'))
        return false
      }
      // 图形维度字段设置了钻取不可以设置超链接
      const isSetDrill = this.selectedEl?.content?.drillList?.length
      if (this.selectedEl?.type === TYPE_ELEMENT.CHART && isSetDrill && data.labelBoard.type === SUPERLINK_CONST_TYPE.variavle) {
        this.$message(this.$t('sdp.views.mutexDrillAndLink'))
        return false
      }
      this.boardArrData.push(this.$_deepClone(data))
      this.setDataAttr(true)
      this.$message.success(this.$t('sdp.message.saveSuccessMsg'))
    },
    setDataAttr(bool = false) {
      this.labelBoard.attr = bool
    },
    cleanData(toast = true) {
      this.boardArrData.some((item, index) => {
        const { site = '' } = item.labelBoard
        const { site: nowSite = '' } = this.labelBoard
        if (site === nowSite) {
          this.boardArrData.splice(index, 1)
        }
        return site === nowSite
      })
      // 优化 8888 【优化】超链接设置页面清除按钮，应是清除当年页面的内容，而不是只清除已保存过的信息。
      this.parameterField = []
      this.labelBoard.excludeColumnNames = []
      this.resetData()()
      this.setDataAttr(false)
      if (toast) this.$message.success(this.$t('sdp.views.vclearSuccess'))
    },

    windowSettingsDataClean() {
      switch (this.windowSettings.type) {
        case WINDOW_SETTINGS.TYPE.SYSTEM:
          this.styles = getStylesDftOpts(this.styles.name)
          this.ratioStyle = getRatioStyle()
          break
        case WINDOW_SETTINGS.TYPE.CUSTOM:
          this.windowSettings.sysValue = WINDOW_SETTINGS.SYS_VALUE.DEFAULT
          this.styles = getStylesDftOpts(this.styles.name)
          break
        case WINDOW_SETTINGS.TYPE.FIXED:
          this.windowSettings.sysValue = WINDOW_SETTINGS.SYS_VALUE.DEFAULT
          this.ratioStyle = getRatioStyle()
          break
        default:
          console.log('如果你看到了此日志信息, 请检查是否有新的窗口设置类型需要处理')
      }
    },

     // render函数渲染是否配置
     renderContent(h, { data }) {
      const isVar = data.type === SUPERLINK_CONST_TYPE.variavle
      const name = isVar ? this.getUnknownName(data.dataSetId, data.label) || data.label : data.label

      let label = data.site && !data.elType && !data.isHideSite ? `${name}(${data.site || ''})` : `${name}`
      let otherClass = ''
      // const isChartDimension = this.boardArrData.find(item => {
      //   if (data.label === this.$t('sdp.views.chartDimension') && isVar &&
      //     item.labelBoard.label === data.label && item.boardData) {
      //     return item
      //   }
      // })
      // 是否支持设置维度超链接
      // if (data.elType === TAGNEWCARD.TWOINDICES) {
      //   otherClass = otherClass + ' ml10'
      // }

      // 自定义元素不想展示site这玩意儿
      if (this.selectedEl?.type === TYPE_ELEMENT.CUSTOMER_ELEMENT && (data.site !== SUPER_LINK_CONST_SITE.TITLE)) {
        label = name
      }
      if (this.selectedEl?.type === TYPE_ELEMENT.CHART) {
        if (data.type === SUPERLINK_CONST_TYPE.title) {
          label = `${name}(${SUPER_LINK_CONST_SITE.TITLE})`
        } else {
          label = `${name}`
        }
      }

      return h('span', {
        class: data.style || `chlid-group ${otherClass}`,
        attrs: {
          title: label
        },
      }, [
        h('span', [
          h('i', {
            // || isChartDimension
            style: data.attr ? 'display: inline-block' : 'display:none',
            class: 'el-icon-check'
          }),
          label,
        ])
      ])
    },
  },
  watch: {
    dialogShow: {
      handler(val) {

        if (val) {
          this.formShow = false
          this.resetData()
          this.boardArrData = []
          this.saveData = {}

          const el = this.highlightEl
          // 高级容器
          if (this.isAdvanceContainer) {
            const tabList = el.content.tabList.map((item) => {
              const { content: { includedElsIdList = [] } } = item

              const children = includedElsIdList.map((id) => {
                const el = this.elList.find(el => el.id === id)

                const selectedEl = this.getSelectedEl(el)

                if (!selectedEl) return false

                const { treeData, boardArrData } = this.initData(selectedEl, false)
                this.boardArrData.push(...boardArrData)

                if ([TYPE_ELEMENT.COMBINE_CARD, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD].includes(selectedEl.type)) {
                  return treeData[0]
                } else {
                  return {
                    label: el.elName,
                    children: treeData
                  }
                }
              }).filter(e => e)

              return {
                label: item.title,
                children
              }
            }).filter(item => item.children.length)
            const title = el.content.settings.title
            // el.content.settings.showContainerTitle &&
            const isShowContainerTitle = title

            let attr = false
            // 高级容器的标题的saveId
            let saveId = this.$_generateUUID()

            if (el?.content?.superLinkOptions) {
              const superLinkOptions = this.$_JSONClone(el.content.superLinkOptions || [])[0]
              const oldSaveId = superLinkOptions?.labelBoard?.saveId
              this.boardArrData.push(superLinkOptions)
              attr = true
              oldSaveId && (saveId = oldSaveId)
            }

            const treeData = isShowContainerTitle ? [{
              label: title,
              id: el.id,
              site: el.id,
              isHideSite: true,
              saveId,
              attr,
              children: tabList
            }] : tabList

            this.$set(this, 'treeData', treeData)

            if (isShowContainerTitle) {
              const selectedEl = this.getSelectedEl(el)
              const parameterFieldArr = []

              this.selectedEl = selectedEl
              this.parameterFieldArr = parameterFieldArr

              this.$set(this.saveData, saveId, {
                selectedEl,
                parameterFieldArr,
              })
              this.nodeSelect(treeData[0])
              this.setHighlight(treeData[0])
            }

          } else {

            this.selectedEl = this.getSelectedEl(el)

            if (!this.selectedEl) return false

            const { treeData, boardArrData, parameterFieldArr, callback } = this.initData(this.selectedEl, true)
            this.boardArrData = boardArrData
            this.parameterFieldArr = parameterFieldArr
            this.$set(this, 'treeData', treeData)
            callback()
          }
        }

      },
      immediate: true,
    },
  },
  mounted() {
    this.boardInfo = this.$parent.boardInfo
    this.nodeSelect = this.$_debounce(this.nodeSelect, 500)
  },
}

</script>
<style lang="scss" scoped>
$color: var(--sdp-zs);
.tooltip {
  line-height: 0px;
}
.el-dialog__wrapper /deep/ {
  .el-color-picker__trigger {
    height: 32px !important;
  }
  .sdp-superLinkDialog{
    width: 980px;
    height: 500px;
    .el-dialog__footer{
      padding-top: 24px;
      padding-bottom: 0;
    }
    .el-dialog__header {
      padding: 24px 24px 20px;
      .el-dialog__headerbtn {
          top: 28px;
      }
    }
    .el-dialog__body {
      padding: 0 24px;
      overflow: hidden;
      .sdp-container {
        background-color:#fff;
        height: 332px;
        overflow: hidden;
        position: relative;
        .sdp-aside {
          background-color: #fff;
          border-right: 1px solid #ddd;
          /*height: 332px;*/
          overflow: hidden;
          .sdp-scrollbar {
            height: 100%;
            .el-scrollbar__wrap {
              .el-scrollbar__view {
                /*position: relative;*/
                display: inline-block;
                min-width: 100%;
              }
            }
            .box-tree {
              font-size: 12px;
              color: #333333;
              font-weight: 400;
              /*min-width: 215px;*/
              /*position: absolute;*/
              /*.nodeStyle {*/
              /*  color: #444444;*/
              /*  font-weight: 600;*/
              /*}*/
              .el-tree-node__content{
                height:30px;
                .ml10 {
                  margin-left: 15px;
                }
              }
              .is-current > .el-tree-node__content{
                .chlid-group {
                  font-weight: 600;
                }
              }
            }
          }
        }
        .sdp-location {
          position: absolute;
          bottom: 0;
          margin-left: 234px;
              z-index: 1;
          .sdp-box-button{
            padding:0;
            .sdp-button:first-child{
              width: 53px;
            }
            .sdp-button:last-child{
              width: 79px;
            }
          }
        }
        .sdp-scrollbar{
          flex: 1;
        }
        .sdp-main{
          // color: #34383D;
          background-color: #fff;
          padding: 0 0 0 20px;
          overflow-x: hidden;
              .sdp-fieldTitle{
                height: 20px;
                .sdp-title {
                  display: inline-block;
                  padding-right: 12px;
                  line-height: 12px;
                }
                .sdp-fieldAdd{
                  cursor: pointer;
                  padding-left: 12px;
                  color: $color;
                  i{
                    font-size: 14px;
                    margin-right: 8px;
                  }
                }
              }
              .sdp-fieldBox {
                margin-top: 10px;
                .icon-sdp-rongqishanchu {
                  color: var(--sdp-qcgls);
                  cursor: pointer;
                  margin-left: 6px;
                }
              }
              .sdp-link-type {
                margin-bottom: 25px;
                .sdp-folder{
                  /deep/ .el-input__inner{
                    /*padding-right: 30px;*/
                  }
                  position: relative;
                  .el-icon-folder-remove{
                    // position: absolute;
                    font-size: 14px;
                    // left: 174px;
                    // top: 9px;
                    color: $color;
                  }
                }
              }
              .fontW{
                font-weight: 600;
              }
        }
      }
    }
  }
}
.m-Top{
  margin-top: 10px;
}
.disbance8{
  margin-bottom: 8px;
}
.test{
  text-overflow:ellipsis;white-space:nowrap;overflow:hidden;width:150px;padding:10px 0 10px 0;
}
.test1{
  text-overflow:ellipsis;white-space:nowrap;overflow:hidden;width:150px;padding:5px 0 5px 0;
}
.fields{
   /deep/ .el-input__inner {
    text-overflow:ellipsis;white-space:nowrap;overflow:hidden;
  }
}

.window-settings-container {
  .radio-container /deep/ .el-radio__label {
    font-size: 12px;
    letter-spacing: 0;
    line-height: 12px;
  }

  .system-container {
    margin-top: 18px;

    .image-box {
      position: relative;
      border: 1px solid transparent;

      &.isActive {
        border: 1px solid $color-main;

        i {
          color: #fff;
          font-size: 10px;
          position: absolute;
          right: -0.5px;
          bottom: -1px;
          z-index: 1;
        }

        &:after {
          content: '';
          position: absolute;
          right: -1px;
          bottom: -1px;
          width: 20px;
          height: 20px;
          border-width: 10px;
          border-style: solid;
          border-right-color: $color-main;
          border-bottom-color: $color-main;
          border-left-color: transparent;
          border-top-color: transparent;
        }
      }

      > img {
        width: 80px;
      }
    }

    p {
      font-size: 12px;
      color: var(--sdp-sztc-srkbt);
      letter-spacing: 0;
      text-align: center;
      line-height: 14px;
      margin-top: 8px;

      &.isActive {
        color: $color-main;
      }
    }

  }

}

.component-container {
  box-sizing: border-box;
  box-shadow: 1px 1px 1px #ccc;
  height: 186px;
  width: 200px;
  margin: 10px;
}

.el-icon-folder-opened {
  color: #13b5b1;
  display: inline-block;
  margin-left: 16px;
}

.setting-arguments-icon {
  cursor: pointer;
}

.el-icon-sdp-Add {
  color: #13b5b1;
  margin-left: 10px;
  position: relative;
  top: 2px
}

.add-names {
  color: #13b5b1;
  text-decoration: underline;
}

.setting-arguments-icon {
  width: 292px;
  height: 45px;
  line-height: 45px;
}

.icon-sdp-info {
  font-size: 20px;
  font-weight: 400;
  color: var(--sdp-zs);;
  position: relative;
  top: 2px;
}

/deep/ .scaling-input.el-input-number {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }
  .el-input:after {
    content: '%';
    position: absolute;
    right: 6px;
    top: 0;
    font-size: 12px;
  }
}
/deep/ .scaling-input.el-input-number:hover {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: block;
  }
  .el-input:after {
    content: ''
  }
}
/*.icon-sdp-info:hover {*/
/*  color: #553CCE;*/
/*}*/
</style>

<!-- 主题 -->
<style lang="scss" scoped>
  .el-dialog__wrapper /deep/ {
    .sdp-superLinkDialog{
      background-color: var(--sdp-szk-bjs);
      .el-dialog__body {
        background-color: var(--sdp-szk-bjs);
        .sdp-container {
          @import 'packages/assets/theme/theme-setting.scss';
          background-color: var(--sdp-szk-bjs);
          .sdp-aside {
            background-color: var(--sdp-szk-bjs);
            border-color: var(--sdp-cszj-bkfgx) !important;
            @import 'packages/assets/theme/theme-tree.scss';
          }
          .sdp-main{
            background-color: var(--sdp-szk-bjs);
          }
          .fontW, .test {
            color: var(--sdp-sztc-srkbt) !important;
          }
        }
      }
    }
  }
</style>
