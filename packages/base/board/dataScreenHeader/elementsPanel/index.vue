<template>
  <div>
    <!-- 移动端 :open-delay="500"-->
    <div v-if="isMobile" class="databoard-wrap">
      <div class="databoard databoard_left">
        <el-tooltip
          effect="dark"
          v-for="(item, index) in mobileList"
          :content="item.name"
          :key="index"
          popper-class="databoard-btn-tooltip"
          placement="bottom"
          :popper-options="{}"
        >
          <div
            class="databoard-pic-btn"
          >
            <div
              v-if="item.type !== TYPE_ELEMENT.TABLE"
              @click="addElement(item.type)"
            >
              <GuidePopover v-bind="getGuidePopoverData(item.type)">
                <span class="icon sdpiconfont" :class="item.icon"></span>
              </GuidePopover>
            </div>
            <!-- 表格 -->
            <div v-if="item.type === TYPE_ELEMENT.TABLE">
              <span v-if="isEnterprise" class="icon sdpiconfont" :class="item.icon" @click="addElement(ELEMENTS_TAG.NEWTABLE)"></span>
              <el-dropdown v-else trigger="click" placement="bottom">
                <span class="icon sdpiconfont" :class="item.icon"></span>
                <el-dropdown-menu class="sdp-dialog sdp-dropdown" slot="dropdown">
                  <el-dropdown-item v-for="({type, name}) of item.typeList" :key="type" @click.native="addElement(type)">
                    {{name}}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </el-tooltip>

        <component
          :is="componentId"
          :boardInfo="boardInfo"
          :dialogVisible.sync="dialogVisible"
          :highlightEl="highlightEl"
          :elList="elList"
          :templateType="templateType"
          @closeDia="closeDia"
          @add-component="addComponent"
          @updateDaSetIds="setTemplateIdsEventBus"
          :defaultAuthorityBoardId="defaultAuthorityBoardId"
        />
        <copyElementDialog
          :visible.sync="copyElementDialogVisible"
          :boardInfo="boardInfo"
          :themeData="themeData"
          :isAdvanceContainerEdit="isAdvanceContainerEdit"
          @updateDaSetIds="setTemplateIdsEventBus"
        ></copyElementDialog>
      </div>

      <div class="databoard databoard_right">
        <!-- 右侧区域 -->
        <el-tooltip
          effect="dark"
          :open-delay="500"
          v-for="(item, index) in rightList.filter(e => e.show !== false)"
          :content="item.name"
          :key="index"
          popper-class="databoard-btn-tooltip"
          placement="bottom"
          :popper-options="{}"
        >
          <div class="databoard-pic-btn">
            <div @click="listRightClick(item)">
              <span class="icon sdpiconfont" :class="[ `${item.icon}`, getRightDisabled(item) ? 'disabled' : '' ]"></span>
            </div>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- PC端 -->
    <div v-else class="databoard-wrap">
      <div class="databoard databoard_left">
        <template v-for="(item, index) in list">
          <el-tooltip
            effect="dark"
            :open-delay="500"
            :content="item.name"
            :key="index"
            popper-class="databoard-btn-tooltip"
            placement="bottom"
            v-if="item.type !== TYPE_ELEMENT.ELEMENT_TEMPLATE || !isEnterprise"
            :popper-options="{}"
          >
            <div class="databoard-pic-btn">
              <!-- 工具 -->
              <el-dropdown v-if="item.typeList && item.typeList.length" trigger="click" placement="bottom">
                <span class="icon sdpiconfont" :class="item.icon"></span>
                <el-dropdown-menu :class="['sdp-dialog sdp-dropdown', getCurrentThemeClass()]" slot="dropdown">
                  <el-dropdown-item
                    v-for="(second, nth) in item.typeList"
                    :key="nth"
                    @click.native="addElement(second.type)"
                    data-cy-board-feature-bar-create-grid-btn
                  >
                    <div class="element-second">
                      <span :class="['icon', 'sdpiconfont', 'element-second-icon', second.icon]"></span>
                      <span class="element-second-name">{{ second.name }}</span>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- 图片 -->
              <template v-else-if="item.type === TYPE_ELEMENT.IMAGE">
                <elUpload
                  v-if="!listTotal"
                  @uploadStatus="handUploadImgStatus"
                  @change="uploadImgChange"
                >
                  <span class="icon sdpiconfont" :class="item.icon"></span>
                </elUpload>
                <span
                  v-else
                  :class="{
                    'icon': true,
                    'sdpiconfont': true,
                    [item.icon]: true,
                  }"
                  @click="addElement(item.type)"
                />
              </template>
              <!-- 素材库、标题 -->
              <el-popover
                v-else-if="[TYPE_ELEMENT.MATERIAL_LIBRARY, TYPE_ELEMENT.ELEMENT_TITLE].includes(item.type)"
                :popper-class="'sdp-board-design sdp-popover sdp-dialog sdp-screen-tool-material ' + 'popover_' + item.type + ' ' + getCurrentThemeClass()"
                placement="top-start"
                :width="item.type === TYPE_ELEMENT.MATERIAL_LIBRARY ? 420 : 750"
                ref="materialPopover"
                trigger="click"
                :popper-options="{}"
              >
                <div class="tool-material-content">
                  <div class="tool-material-content-menu">
                    <div
                      class="tool-material-content-menu-item"
                      :class="{'active': currentMaterial.TYPE === menu.TYPE}"
                      v-for="menu in popoverLeftOptions"
                      :key="menu.TYPE"
                      @click="materialMenuChose(menu)"
                    >
                      {{getMaterialNameByType(menu.TYPE)}}
                    </div>
                  </div>
                  <div class="tool-material-content-element">
                    <ElementTitleChildren
                      v-if="item.type === TYPE_ELEMENT.ELEMENT_TITLE"
                      :titleType="currentMaterial.TYPE"
                      @select="e => materialElementChose(e, item.type)"
                    ></ElementTitleChildren>
                    <div
                      v-else
                      class="tool-material-content-element-item"
                      v-for="ele in currentMaterial.CHILDREN"
                      :key="ele.TYPE"
                      @click="materialElementChose(ele, item.type)"
                    >
                      <img style="width: 100%;height: 100%;" :src="STATIC_BASE_PATH.images + 'board/materialLibrary/' + ele.IMG">
                    </div>
                  </div>
                </div>
                <div slot="reference">
                  <span class="icon sdpiconfont" :class="item.icon" @click="elementTypeIconHandler(item.type)"></span>
                </div>
              </el-popover>

              <GuidePopover v-else-if="![TYPE_ELEMENT.TABLE].includes(item.type)" v-bind="getGuidePopoverData(item.type)">
                <span
                  :class="{
                    'icon': true,
                    'sdpiconfont': true,
                    [item.icon]: true,
                    'disabled': isAdvanceContainerEdit && item.type === TYPE_ELEMENT.ADVANCE_CONTAINER,
                  }"
                  @click="addElement(item.type)"
                />
              </GuidePopover>
            </div>
          </el-tooltip>
        </template>

        <component
          :is="componentId"
          :dialogVisible.sync="dialogVisible"
          :highlightEl="highlightEl"
          :elList="elList"
          :templateType="templateType"
          :boardInfo="boardInfo"
          :isAdvanceContainerEdit="isAdvanceContainerEdit"
          @closeDia="closeDia"
          @add-component="addComponent"
          @updateDaSetIds="setTemplateIdsEventBus"
          :defaultAuthorityBoardId="defaultAuthorityBoardId"
        />
        <ElementImportDialog
          :visible.sync="elementImportDialogVisible"
          :boardInfo="boardInfo"
          :themeData="themeData"
          :isAdvanceContainerEdit="isAdvanceContainerEdit"
          @updateDaSetIds="setTemplateIdsEventBus"
        ></ElementImportDialog>
        <copyElementDialog
          :visible.sync="copyElementDialogVisible"
          :boardInfo="boardInfo"
          :themeData="themeData"
          :isAdvanceContainerEdit="isAdvanceContainerEdit"
          @updateDaSetIds="setTemplateIdsEventBus"
          formType="PC"
        ></copyElementDialog>
        <ImageAddgDialog :visible.sync="imageDialogVisible" @confirm="uploadImgChange"></ImageAddgDialog>
      </div>

      <div class="databoard databoard_right">
        <!-- 右侧区域 -->
        <el-tooltip
          effect="dark"
          :open-delay="500"
          v-for="(item, index) in rightList.filter(e => e.show !== false)"
          :content="item.name"
          :key="index"
          popper-class="databoard-btn-tooltip"
          placement="bottom"
          :popper-options="{}"
        >
          <div class="databoard-pic-btn">
            <div @click="listRightClick(item)">
              <span class="icon sdpiconfont" :class="[ `${item.icon}`, getRightDisabled(item) ? 'disabled' : '' ]"></span>
            </div>
          </div>
        </el-tooltip>
      </div>
    </div>


  </div>
</template>

<script>
import {
  ELEMENTS_TAG,
  TYPE_ELEMENT,
  MAX_COUNT_ELEMENTS_IN_BOARD,
  TOOL_TAG,
  MATERIAL_ELEMENT,
  ELEMENT_TITLE_TYPES,
  MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD,
  MAX_COUNT_ADVANCE_CONTAINER_BOARD
} from 'packages/base/board/displayPanel/constants'
import ElementsPanelSetting from './elementsPanelSetting'
import EventData from 'packages/assets/EventData'
import { DISPLAY_MODE, ADVANCE_CONTAINER_DEFAULT_TAB_NAME } from '../../displayPanel/supernatant/boardElements/elementContainer/constants'
import elUpload from 'packages/base/board/largeScreen/components/elUpload/index.vue'
import ElementTitleChildren from './components/elementTitleChildren.vue'
import { EVENT_BUS } from '../../displayPanel/constants'
// import { MATERIAL_NEED_SUBTITLE } from '../../displayPanel/supernatant/boardElements/elementMaterialLibrary/mixins/constant'
import { STATIC_BASE_PATH, SUPERLINK_CONST_TYPE } from 'packages/assets/constant'

import { MATERIAL_NEED_SUBTITLE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementMaterialLibrary/mixins/constant'
import { inBoardMessage } from '../../displayPanel/utils'
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin'
import { TYPE_PARAM_ELEMENT } from '../../displayPanel/params/utils/constants'
import { RECORD_OPERATE_TYPE } from 'packages/base/board/mixins/boardRecord'
import { CUSTOMER_ELMENT_MAX_NUM } from '../../displayPanel/supernatant/boardElements/elementCustomer/utils/constant'
import { isOrdinaryContainer_fn } from 'packages/assets/utils/helper'
import { isRealScreenFn } from 'packages/helpers'
import { IS_CAN_SET_SUPER_LINK_ELEMENT } from '../../displayPanel/supernatant/boardElements/constant'
import { getElementDataSet } from 'packages/base/board/dataScreenHeader/elementsPanel/InteractionSettingModal.vue'
import { getImageMaterialList } from 'packages/base/board/displayPanel/supernatant/api'
import {
  OPERATION_LOG,
} from 'packages/base/board/displayPanel/boardLanguage'
import {
  operationalLog,
} from 'packages/base/board/displayPanel/api'
import { kanbanGuideStepEntryAddElementsMixin } from 'packages/base/KanbanGuide'

export default {
  name: 'elementsPanel',
  inject: ['utils', 'sdpBus', 'isTemplateBoard', 'boardRecord', 'getCurrentThemeClass'],
  mixins: [kanbanGuideStepEntryAddElementsMixin],
  props: {
    isElementsPanelMode: {
      required: true,
      type: Boolean,
    },
    highlightEl: {
      type: Object,
      default: () => ({})
    },
    elList: {
      type: Array,
      default: () => []
    },
    boardInfo: {
      type: Object,
      default: () => ({})
    },
    themeData: {
      type: Object,
      default: () => ({})
    },
    defaultAuthorityBoardId: {
      type: String,
      default: ''
    },
    isAdvanceContainerEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listTotal: 0,
      STATIC_BASE_PATH,
      TYPE_ELEMENT,
      ELEMENTS_TAG,
      TOOL_TAG,
      MATERIAL_NEED_SUBTITLE,
      componentId: '',
      templateType: ELEMENTS_TAG.REFERENCETABLE,
      dialogVisible: false,
      popoverShow: false,
      elementType: '',
      activeTabId: '',
      elementTypeLanguage: {
        [TYPE_ELEMENT.CHART]: this.$t('sdp.views.chart'),
        [TYPE_ELEMENT.TEXT]: this.$t('sdp.views.textArea'),
      },
      undoEmpty: true,
      redoEmpty: true,
      arrName: ['BoardDataSet', 'InteractionSettingModal', 'SuperLinkDialog', 'ElementImportDialog'],
      imageDialogVisible: false,
      elementImportDialogVisible: false,
      copyElementDialogVisible: false,
      mobileList: [
        {
          name: this.$t('sdp.views.specChart'),
          type: TYPE_ELEMENT.CHART,
          icon: 'icon-sdp-tubiao'
        },
        {
          name: this.$t('sdp.views.dataTable'),
          type: ELEMENTS_TAG.NEWTABLE,
          icon: 'icon-sdp-shujubiaoge',
        },
        {
          name: this.$t('sdp.views.newCard'),
          type: TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
          icon: 'icon-sdp-kapian'
        },
        {
          type: TYPE_ELEMENT.COMBINE_CARD,
          name: this.$t('sdp.views.combineCard'),
          icon: 'icon-sdp-kapianzu'
        },
        {
          name: this.$t('sdp.views.container'),
          type: TYPE_ELEMENT.CONTAINER,
          icon: 'icon-sdp-rongqi'
        },
        {
          name: this.$t('sdp.views.advanceContainer'),
          type: TYPE_ELEMENT.ADVANCE_CONTAINER,
          icon: 'icon-sdp-gaojirongqi'
        },
        {
          name: this.$t('sdp.views.FourQuadrant'),
          type: TYPE_ELEMENT.FOUR_QUADRANT,
          icon: 'icon-sdp-duoheyibiaoge'
        },
        {
          name: this.$t('sdp.views.textArea'),
          type: TYPE_ELEMENT.TEXT,
          icon: 'icon-sdp-kanban-wenbenkuang'
        },
        {
          name: this.$t('sdp.views.elementTemplate'),
          type: TYPE_ELEMENT.ELEMENT_TEMPLATE,
          icon: 'icon-sdp-yinyongmoban'
        },
        {
          name: this.$t('sdp.views.copyElement'),
          type: TYPE_ELEMENT.COPY_ELEMENT,
          icon: 'icon-sdp-fuzhiyuansu'
        },
        {
          name: this.$t('sdp.views.customerElement'),
          type: TYPE_ELEMENT.CUSTOMER_ELEMENT,
          icon: 'icon-sdp-zidingyiyuansu'
        },
        {
          name: this.$t('sdp.views.interactionSet'),
          type: TYPE_ELEMENT.INTERACTION_OPTIONS,
          icon: 'icon-sdp-jiaohushezhi',
        },
        {
          name: this.$t('sdp.views.superLink'),
          type: TYPE_ELEMENT.LINKSETTING,
          icon: 'icon-sdp-chaojilianjie',
        },
      ],
      list: [
        {
          name: this.$t('sdp.views.specChart'),
          type: TYPE_ELEMENT.CHART,
          icon: 'icon-sdp-tubiao'
        },
        {
          name: this.$t('sdp.views.dataTable'),
          type: ELEMENTS_TAG.NEWTABLE,
          // type: TYPE_ELEMENT.TABLE,
          icon: 'icon-sdp-shujubiaoge',
          // typeList: [
          //   {
          //     type: ELEMENTS_TAG.REFERENCETABLE,
          //     name: this.$t('sdp.views.referenceTemplate')
          //   },
          //   {
          //     type: ELEMENTS_TAG.NEWTABLE,
          //     name: this.$t('sdp.views.newTable')
          //   }
          // ],
        },
        {
          name: this.$t('sdp.views.newCard'),
          type: TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
          icon: 'icon-sdp-kapian'
        },
        {
          name: this.$t('sdp.views.container'),
          type: TYPE_ELEMENT.CONTAINER,
          icon: 'icon-sdp-rongqi',
        },
        {
          name: this.$t('sdp.views.advanceContainer'),
          type: TYPE_ELEMENT.ADVANCE_CONTAINER,
          icon: 'icon-sdp-gaojirongqi'
        },
        {
          name: this.$t('sdp.views.FourQuadrant'),
          type: TYPE_ELEMENT.FOUR_QUADRANT,
          icon: 'icon-sdp-duoheyibiaoge'
        },
        {
          name: this.$t('sdp.views.textArea'),
          type: TYPE_ELEMENT.TEXT,
          icon: 'icon-sdp-kanban-wenbenkuang'
        },
        {
          name: this.$t('sdp.views.title'),
          type: TYPE_ELEMENT.ELEMENT_TITLE,
          icon: 'icon-sdp-biaoti'
        },
        {
          name: this.$t('sdp.views.elementTemplate'),
          type: TYPE_ELEMENT.ELEMENT_TEMPLATE,
          icon: 'icon-sdp-yinyongmoban'
        },
        // pc不需要复制元素 (40需求9124 大屏新增复制元素)
        {
          name: this.$t('sdp.views.copyElement'),
          type: TYPE_ELEMENT.COPY_ELEMENT,
          icon: 'icon-sdp-fuzhiyuansu'
        },
        {
          name: this.$t('sdp.views.customerElement'),
          type: TYPE_ELEMENT.CUSTOMER_ELEMENT,
          icon: 'icon-sdp-zidingyiyuansu'
        },
        {
          name: this.$t('sdp.views.referenceElement'),
          type: TYPE_ELEMENT.ELEMENT_REFERENCE,
          icon: 'icon-sdp-kanbanyinyongyuansu'
        },
        {
          name: this.$t('sdp.views.dupontAnalysisChart'),
          type: TYPE_ELEMENT.DUPONT_ANALYSIS,
          icon: 'icon-sdp-dubangfenxitu'
        },
        {
          name: this.$t('sdp.views.img'),
          type: TYPE_ELEMENT.IMAGE,
          icon: 'icon-sdp-daping_tupian'
        },
        {
          name: this.$t('sdp.views.tool'),
          type: TOOL_TAG.TOOL,
          icon: 'icon-sdp-daping_gongju',
          typeList: [
            {
              type: TYPE_ELEMENT.WEATHER,
              name: this.$t('sdp.views.weather'),
              icon: 'icon-sdp-daping_tianqi',
            },
            {
              type: TYPE_ELEMENT.TIME,
              name: this.$t('sdp.views.time_ware'),
              icon: 'icon-sdp-daping_shijianqi',
            },
            {
              type: TYPE_ELEMENT.SCROLL_TEXT,
              name: this.$t('sdp.views.scroll_text'),
              icon: 'icon-sdp-daping_gundongwenzi',
            }
          ]
        },
        {
          name: this.$t('sdp.views.url'),
          type: TYPE_ELEMENT.WEB,
          icon: 'icon-sdp-daping_wangye',
        },
        {
          name: this.$t('sdp.views.materialLibrary'),
          type: TYPE_ELEMENT.MATERIAL_LIBRARY,
          icon: 'icon-sdp-daping_sucaiku1',
        },
        {
          name: this.$t('sdp.views.interactionSet'),
          type: TYPE_ELEMENT.INTERACTION_OPTIONS,
          icon: 'icon-sdp-jiaohushezhi',
        },
        {
          name: this.$t('sdp.views.superLink'),
          type: TYPE_ELEMENT.LINKSETTING,
          icon: 'icon-sdp-chaojilianjie',
        }
      ],
      rightList: [
        {
          name: this.$t('sdp.views.undo'),
          type: 'undo',
          icon: 'icon-sdp-chexiao',
          show: true,
          disabled: this.boardRecord.undoEmpty,
          callback: () => { this.boardRecord.undoRelease() }
        },
        {
          name: this.$t('sdp.views.redo'),
          type: 'redo',
          icon: 'icon-sdp-zhongzhi',
          show: true,
          disabled: this.boardRecord.redoEmpty,
          callback: () => { this.boardRecord.redoRelease() }
        }
      ],
      // 素材库
      currentMaterial: { TYPE: '', CHILDREN: [] },
      popoverLeftOptions: [],
      currentMaterialElement: { TYPE: '', CHILDREN: [] },
      // 引用元素的对象列表，为了对自定义元素进行数量限制
      referenceRecords: []
    }
  },
  components: {
    ...ElementsPanelSetting,
    elUpload,
    ElementTitleChildren,
  },
  created() {
    // 大屏看板去除普通容器类型
    const exclude = this.isRealScreen ? [TYPE_ELEMENT.CONTAINER] : this.isDataReport ? [TOOL_TAG.TOOL, TYPE_ELEMENT.MATERIAL_LIBRARY, TYPE_ELEMENT.WEB] : [TOOL_TAG.TOOL, TYPE_ELEMENT.MATERIAL_LIBRARY, TYPE_ELEMENT.WEB, TYPE_ELEMENT.IMAGE]
    // 只有oms有模板引用功能
    if (![ALL_PROJECT_NAME.OMS, ALL_PROJECT_NAME.EMS, ALL_PROJECT_NAME.SBI].includes(this.utils.env?.projectName)) {
      exclude.push(TYPE_ELEMENT.ELEMENT_TEMPLATE)
      this.mobileList = this.mobileList.filter(item => item.type !== TYPE_ELEMENT.ELEMENT_TEMPLATE)
    }
    if (this.isTemplateBoard) {
      // 模板看板处理location 引用模板隐藏
      exclude.push(...[TYPE_ELEMENT.LINKSETTING, TYPE_ELEMENT.ELEMENT_TEMPLATE])
      this.mobileList = this.mobileList.filter(item => item.type !== TYPE_ELEMENT.ELEMENT_TEMPLATE)
    }
    // 排除自定义元素
    if ([ALL_PROJECT_NAME.INFRASYS_ER, ALL_PROJECT_NAME.EMS, ALL_PROJECT_NAME.SBI].includes(this.utils.env?.projectName)) {
      exclude.push(TYPE_ELEMENT.CUSTOMER_ELEMENT)
      this.mobileList = this.mobileList.filter(item => item.type !== TYPE_ELEMENT.CUSTOMER_ELEMENT)
    }
    // // 在EMS、OMS、INFRASYS_ER移动端中才有 SBI也有
    // if ([ALL_PROJECT_NAME.SBI].includes(this.utils.env?.projectName)) {
    //   this.mobileList = this.mobileList.filter(item => item.type !== TYPE_ELEMENT.COPY_ELEMENT)
    // }
    // // OMS移动看板
    // if ([ALL_PROJECT_NAME.OMS].includes(this.utils.env?.projectName)) {
    //   exclude.push(TYPE_ELEMENT.COPY_ELEMENT)
    // }

    if (this.isDataReport) {
      exclude.push(TYPE_ELEMENT.CONTAINER)
    } else {
      exclude.push(TYPE_ELEMENT.ELEMENT_TITLE)
      exclude.push(TYPE_ELEMENT.ELEMENT_REFERENCE)

    }
    if(!this.isRealScreen){
      exclude.push(TYPE_ELEMENT.COPY_ELEMENT)
    }
    this.list = this.list.filter(item => !exclude.includes(item.type))
    this.getMaterialNumber()
  },
  destroyed() {
    inBoardMessage('clear')
  },
  computed: {
    isRealScreen() {
      return isRealScreenFn(this.utils)
    },
    isDataReport() {
      return this.utils.isDataReport
    },
    isMobile() {
      return this.utils.isMobile
    },
    isEnterprise() {
      return this.utils.isEnterprise
    },
    containerActiveTab() {
      let containerEl = this.elList.find(el => el?.content?.isAdvanceContainerEdit)
      if (!this.isAdvanceContainerEdit || !containerEl) return {}
      let { tabList, activeTabId } = containerEl.content
      let activeTab = tabList.find(tab => tab.name === activeTabId) || {}
      return activeTab
    },
    activeTabElsIdList: {
      get() {
        return this.containerActiveTab?.content?.includedElsIdList || []
      },
      set(l) {
        this.containerActiveTab.content.includedElsIdList = l
      }
    },
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        const eventData = new EventData({
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'getCurBoardInfo',
        })
        this.$emit('eventBus', eventData)
      }
      !val && (this.componentId = '')
    },
    'boardRecord.undoEmpty'(val) {
      this.undoEmpty = val
    },
    'boardRecord.redoEmpty'(val) {
      this.redoEmpty = val
    }
  },
  methods: {
    getGuidePopoverData(type) {
      return {
        content: this.$t('sdp.guide.clickToAddChartElements'),
        value: this.isShowStepTipsByAddElements && type === TYPE_ELEMENT.CHART,
        step: this.stepEntryAddElements,
        markPaddingLeft: 5,
        markPaddingRight: 5,
        markPaddingTop: 4,
        markPaddingBottom: 4,
        arrowOffsetX: -10,
        tipsOffsetY: -18,
      }
    },

    getMaterialNumber() {
      if (!this.utils.isScreen || !this.list.find(l => l.type === TYPE_ELEMENT.IMAGE)) return
      const imgTypeList = ['1', '2', '3']
      const paginationData = {
        limit: 1,
        page: 1,
        status: '1',
      }
      Promise.all(imgTypeList.map(imgType => getImageMaterialList(this.utils.api, { imgType, ...paginationData }))).then(res => {
        let listTotal = 0
        res.forEach(r => {
          if (r?.total) listTotal = listTotal + r.total
        })
        this.listTotal = listTotal
      })
    },
    listRightClick(item) {
      !this.getRightDisabled(item) && item.callback && item.callback()
    },
    getRightDisabled(item) {
      if (['undo', 'redo'].includes(item.type)) {
        return item.type === 'undo' ? this.undoEmpty : this.redoEmpty
      }
      return item.disabled
    },
    getHighlightEl() {
      let highlightEl = this.highlightEl
      if (isOrdinaryContainer_fn(highlightEl) && highlightEl?.content?.activeElId) {
        highlightEl = this.elList.find(item => item.id === highlightEl.content.activeElId)
      }
      return highlightEl
    },
    getDefault(messageType) {
      const highlightEl = this.getHighlightEl()
      let { type = '', content = {} } = highlightEl
      if (type === TYPE_ELEMENT.CHART) {
        const chioceTab = this.$_getProp(content, 'chioceTab', [])
        if (chioceTab.length > 1 && [TYPE_ELEMENT.INTERACTION_OPTIONS, TYPE_ELEMENT.LINKSETTING].includes(messageType)) {
          // 具备指标选择器的元素暂不支持交互设置
          // return this.$t('sdp.views.indicatorSelectors')
        }
      }
      // if (messageType === TYPE_ELEMENT.INTERACTION_OPTIONS) {
      //   let isDatasetAssociation = false
      //   if (type === TYPE_ELEMENT.CHART) {
      //     // const isSuperLinkClick = content?.superLinkOptions && content.superLinkOptions.find(item => item.labelBoard.type === SUPERLINK_CONST_TYPE.variavle)
      //     isDatasetAssociation = this.$_getProp(content, 'chartUserConfig.datasetAssociation', false) && Object.keys(content.associatedData).length
      //     // if (isSuperLinkClick) {
      //       // 设置了超链接不能设置交互
      //       // message = this.$t('sdp.views.reminder')
      //     // }
      //   } else if (type === TYPE_ELEMENT.TABLE) {
      //     const isShowExclude = this.$_getProp(highlightEl.vm, 'isShowExclude', false)
      //     const datasetAssociation = this.$_getProp(content, 'tableDefaultConfig.dataSetJoinsConfig', false)
      //     isDatasetAssociation = !isShowExclude && datasetAssociation && datasetAssociation.hasOwnProperty('mainDatasetId')
      //   }
      //   if (isDatasetAssociation) {
      //     // 设置了关联数据集的看板元素不支持交互
      //     return this.$t('sdp.views.associationInteraction')
      //   }
      // }
    },
    addElement(type) {
      if (this.isAdvanceContainerEdit && type === TYPE_ELEMENT.ADVANCE_CONTAINER) return
      const exactElement = [TYPE_ELEMENT.INTERACTION_OPTIONS, TYPE_ELEMENT.LINKSETTING, TYPE_ELEMENT.ELEMENT_TEMPLATE]

      if (!exactElement.includes(type) && this.elList.length > MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD) {
        inBoardMessage(() => {
            this.$message(`${this.$t('sdp.message.elementsInBoardMessage')}`)
        })
      }

      if (!exactElement.includes(type) && this.elList.length >= MAX_COUNT_ELEMENTS_IN_BOARD) {
        this.$message(`${this.$t('sdp.message.mostElementsInBoard')}`)
        return
      }

      const customerElements = this.elList.filter(el => el.type === TYPE_ELEMENT.CUSTOMER_ELEMENT)
      if (customerElements.length >= CUSTOMER_ELMENT_MAX_NUM && type === TYPE_ELEMENT.CUSTOMER_ELEMENT) {
        this.$message(this.$t('sdp.message.mostCustomerElement'))
        return
      }

      const isSelectMoreEl = () => {
        if (this.utils.isScreen) {
          let getIds = []
          this.sdpBus.$emit(EVENT_BUS.GET_ACTIVE_COMPONENT_ID, getIds)
          let containerEl = this.elList.find(item => item?.content?.isAdvanceContainerEdit)
          if (this.isAdvanceContainerEdit && containerEl?.id) {
            getIds = getIds.filter(id => containerEl.id !== id)
          }
          getIds = [...new Set(getIds)]
          if (getIds.length > 1) {
            this.$message(`${this.$t('sdp.views.multiple_is_not_support_interaction')}`)
            return true
          }
        }
        return false
      }

      const openDialogVisible = (name) => {
          this.componentId = name
          this.$set(this, 'dialogVisible', true)
      }
      switch (type) {
        case ELEMENTS_TAG.REFERENCETABLE:
        case TYPE_ELEMENT.ELEMENT_TEMPLATE: {
          this.setRefElement(type)
          break
        }
        // 从看板引入元素
        case TYPE_ELEMENT.ELEMENT_REFERENCE: {
          this.setImportElement(type)
          break
        }
        case ELEMENTS_TAG.NEWTABLE: {
          this.setNewTable()
          break
        }
        case TYPE_ELEMENT.IMAGE: {
          this.addImageElement()
          break
        }
        case TYPE_ELEMENT.CUSTOMER_ELEMENT:
        case TYPE_ELEMENT.DUPONT_ANALYSIS:
        case TYPE_ELEMENT.TEXT:
        case TYPE_ELEMENT.CHART:
        case TYPE_ELEMENT.CONTAINER:
        case TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD:
        case TYPE_ELEMENT.COMBINE_CARD:
        case TYPE_ELEMENT.WEB:
        case TYPE_ELEMENT.FOUR_QUADRANT:
        case TYPE_ELEMENT.WEATHER:
        case TYPE_ELEMENT.SCROLL_TEXT:
        case TYPE_ELEMENT.TIME: {
          this.sendEventBus({ type })
          break
        }
        case TYPE_ELEMENT.ADVANCE_CONTAINER: {
          this.sendEventBus({ type: TYPE_ELEMENT.CONTAINER }, this.initAdvanceContainer)
          break
        }

        case TYPE_ELEMENT.INTERACTION_OPTIONS: {
          if (isSelectMoreEl()) break
          if (!Object.keys(this.highlightEl).length) {
            this.$message(`${this.$t('sdp.views.selectElObject')}`)
            break
          }
          const msg = this.getDefault(TYPE_ELEMENT.INTERACTION_OPTIONS)
          if (msg) {
            this.$message(msg)
            break
          }
          if ([TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE].includes(this.highlightEl.type) && !this.highlightEl.content?.referenceField?.length) {
            this.$message(`${this.$t('sdp.views.this_is_not_support_interaction')}`)
            break
          }
          if (TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD === this.highlightEl.type && this.highlightEl?.content?.superLinkOptions?.length) {
            this.$message(`${this.$t('sdp.views.elementNotInteractionAndLink')}`)
            break
          }
          if (this.highlightEl.content.alias === 've-map-parent' && this.highlightEl.content?.mapSchemeSetting?.schemeList?.length) {
            this.$message(`${this.$t('sdp.views.interactionWithoutMapScheme')}`)
            break
          }
          if (this.highlightEl.type === TYPE_ELEMENT.ELEMENT_GROUP) {
            this.$message(`${this.$t('sdp.views.multiple_is_not_support_interaction')}`)
            break
          }
          if ([TYPE_ELEMENT.TIME, TYPE_ELEMENT.WEATHER, TYPE_ELEMENT.WEB, TYPE_ELEMENT.SCROLL_TEXT, TYPE_ELEMENT.IMAGE, TYPE_ELEMENT.MATERIAL_LIBRARY].includes(this.highlightEl.type) ||
              ([TYPE_ELEMENT.TEXT].includes(this.highlightEl.type) && !this.highlightEl.content?.referenceField?.length)) {
            this.$message(`${this.$t('sdp.views.this_is_not_support_interaction_hyperlink')}`)
            break
          }
          // 高级容器不可设置交互设置
          if ((TYPE_ELEMENT.CONTAINER === this.highlightEl.type && TYPE_ELEMENT.ADVANCE_CONTAINER === this.highlightEl.subType) || this.highlightEl.content.alias === 've-themeRiver') return this.$message(`${this.$t('sdp.views.setInteraBoard')}`)
          const arr = [TYPE_ELEMENT.FOUR_QUADRANT, TYPE_ELEMENT.COMBINE_CARD, TYPE_ELEMENT.DUPONT_ANALYSIS]
          if (arr.includes(this.highlightEl.type)) {
            this.$message(`${this.$t('sdp.views.setInteraBoard')}`)
            break
          }

          const { dataSetList, allDataSetIds, isShowExclude } = getElementDataSet.call(this, this.highlightEl)

          if (!isShowExclude) {
            if (!allDataSetIds.length || !dataSetList.some(item => item.fieldList.length)) {
              this.$message(`${this.$t('sdp.message.noFieldNoInteractive')}`)
              break
            }
          }

          openDialogVisible(this.arrName[1])
          break
        }

        case TYPE_ELEMENT.LINKSETTING: {
          if (isSelectMoreEl()) break
          if (!Object.keys(this.highlightEl).length) {
            this.$message(`${this.$t('sdp.views.selectElObject')}`)
            break
          }
          const msg = this.getDefault(TYPE_ELEMENT.LINKSETTING)
          if (msg) {
            this.$message(msg)
            break
          }
          if (this.highlightEl.type === TYPE_ELEMENT.ELEMENT_GROUP) {
            debugger
            this.$message(`${this.$t('sdp.views.multiple_is_not_support_interaction')}`)
            break
          }
          if ([TYPE_ELEMENT.TIME, TYPE_ELEMENT.WEATHER, TYPE_ELEMENT.WEB, TYPE_ELEMENT.SCROLL_TEXT, TYPE_ELEMENT.IMAGE, TYPE_ELEMENT.MATERIAL_LIBRARY].includes(this.highlightEl.type)) {
            this.$message(`${this.$t('sdp.views.this_is_not_support_interaction_hyperlink')}`)
            break
          }
          if (TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD === this.highlightEl.type && this.highlightEl?.content?.interactionOptions?.length) {
            this.$message(`${this.$t('sdp.views.elementNotInteractionAndLink')}`)
            break
          }
          // 容器中的卡片
          if (this.highlightEl.type === TYPE_ELEMENT.CONTAINER && this.highlightEl.content && this.highlightEl.content.activeElId) {
            const item = this.elList.find(item => item.id === this.highlightEl.content.activeElId)
            if (item && item.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD && item?.content?.interactionOptions?.length) {
              this.$message(`${this.$t('sdp.views.elementNotInteractionAndLink')}`)
              break
            }
          }
          // 未设置标题的图形不能设置超链接
          const noHyperlink = notSupportHyperlinks.call(this, this.highlightEl)
          if (noHyperlink) {
            break
          }
          // 那些元素可以配置
          const arr = IS_CAN_SET_SUPER_LINK_ELEMENT
          // 高级容器不可设置超链接
          if (TYPE_ELEMENT.CONTAINER === this.highlightEl.type && TYPE_ELEMENT.ADVANCE_CONTAINER === this.highlightEl.subType) {
            // return this.$message(`${this.$t('sdp.views.setInteraBoard')}`)
            openDialogVisible(this.arrName[2])
            return
          }
          // 容器纵向不可跳转
          if (
            TYPE_ELEMENT.CONTAINER === this.highlightEl.type
          ) {
            if (this.$_getProp(
              this.highlightEl,
              'content.settings.displayMode',
              ''
            ) === DISPLAY_MODE.VERTICAL) {
              this.$message(`${this.$t('sdp.views.containersVertically')}`)
              break
            } else {
              const activeElId = this.$_getProp(this.highlightEl, 'content.activeElId', '')
              const el = this.elList.find((item) => item.id === activeElId)
              // 未设置标题的图形不能设置超链接
              const noHyperlink = notSupportHyperlinks.call(this, el)
              if (noHyperlink) {
                break
              }
              if (el && !arr.includes(el.type)) {
                this.$message(`${this.$t('sdp.views.TableAndCompositeCardVertically')}`)
                break
              }
            }
          }
          // 移动端支持元素
          if (!arr.includes(this.highlightEl.type)) {
            this.$message(`${this.$t('sdp.views.TableAndCompositeCardVertically')}`)
            break
          }
          openDialogVisible(this.arrName[2])
          break
        }

        case TYPE_ELEMENT.ELEMENT_TITLE:
        case TYPE_ELEMENT.MATERIAL_LIBRARY: {
          if (this.currentMaterialElement.TYPE && this.currentMaterialElement.TYPE.length) {
            this.sendEventBus({ type: type, content: { sonType: this.currentMaterialElement.TYPE || '' } })
            if (MATERIAL_NEED_SUBTITLE.includes(this.currentMaterialElement.TYPE)) {
              this.sendEventBus({ type: type, content: { sonType: `${this.currentMaterialElement.TYPE}_2` || '' } })
            }
          }
          break
        }
        case TYPE_ELEMENT.COPY_ELEMENT:{
          this.copyElement(type)
          break
        }
      }
      this.elementType = type
      function notSupportHyperlinks(_el) {
        let flag = false

        if ([TYPE_ELEMENT.CHART, TYPE_ELEMENT.TEXT].includes(_el?.type)) {
          const content = _el.content
          // 没有设置标题的图形点击超链接设置时，提示“没有标题的图形不支持设置超链接”；
          if (_el.type === TYPE_ELEMENT.TEXT && (!content?.title && !content?.referenceField?.length)) {
            flag = true
          }
          // 图形不支持维度超链接，且不存在标题时，需要提示
          if (_el.type === TYPE_ELEMENT.CHART && !content?.chartUserConfig?.title?.text) {
            const { chartUserConfig = {}, mapSchemeSetting = {}, associatedData = {} } = content
            // 是否使用关联数据集
            const isAssociationDataset = this.$_getProp(content, 'chartUserConfig.datasetAssociation', false) && Object.keys(associatedData).length
            // 是否存在维度
            const isDimensionList = chartUserConfig.dimensionList?.length
            // 地图方案
            const hasMapSchemeSetting = mapSchemeSetting.schemeList?.length && chartUserConfig.chartAlias === 've-map-parent'
            const disableDimensionSuperLink = isAssociationDataset || !isDimensionList || hasMapSchemeSetting
            if (disableDimensionSuperLink) {
              flag = true
            }
          }

        }
        if (flag) {
          this.$message(`${this.$t('sdp.views.notSupportHyperlinksProp', { prop: this.elementTypeLanguage[_el?.type] })}`)
        }
        // 是否设置了钻取
        const isSetDrill = _el.content?.drillList?.length
        // 是否有标题
        const isTitle = !!_el.content?.chartUserConfig?.title?.text
        if (_el?.type === TYPE_ELEMENT.CHART && isSetDrill && !isTitle) {
          this.$message(this.$t('sdp.views.mutexDrillAndLink'))
          flag = true
        }
        // 判断是否设置了交互
        // const interactionOptions = _el.content?.interactionOptions || []
        // if (interactionOptions.length) {
        //   this.$message(this.$t('sdp.views.reminder'))
        //   flag = true
        // }
        return flag
      }
    },
    addImageElement() {
      this.imageDialogVisible = true
    },
    initAdvanceContainer(container) {
      const id = this.$_generateUUID()
      const defaultTab = {
        title: ADVANCE_CONTAINER_DEFAULT_TAB_NAME,
        name: id,
        content: {
          includedElsIdList: []
        }
      }
      container.subType = TYPE_ELEMENT.ADVANCE_CONTAINER
      this.$set(container.content, 'activeTabId', id)
      this.$set(container.content, 'tabList', [defaultTab])
      this.isMobile && (container.layout.h = 8 * 6)
    },

    // 关闭弹窗
    closeDia() {
      this.componentId = ''
      this.$set(this, 'dialogVisible', false)
    },
    // 新建表格
    setNewTable() {
      this.sendEventBus({ type: TYPE_ELEMENT.TABLE })
    },
    setRefElement(type) {
      this.templateType = type
      this.$set(this, 'dialogVisible', true)
      this.componentId = this.arrName[0]
    },
    // 添加特殊空间
    addComponent(highlightItem) {
      const { type } = highlightItem
      this.sendEventBus({ type })
    },
    recordAddElement(newEl) {
      this.activeTabId = this.containerActiveTab?.name
      let containerEl = this.elList.find(el => el?.content?.isAdvanceContainerEdit)
      this.boardRecord.undoSave({
        type: RECORD_OPERATE_TYPE.ELEMENT_ADD,
        idList: [newEl.id],
        value: newEl,
        containerEl: containerEl?.id ? this.$_deepClone(containerEl) : null,
        source: 'element_panel_add',
        callback: () => {
        }
      })
    },
    // 发送eventBus
    sendEventBus(data, callback) {
      const elToAddNum = data && data.ids ? data.ids.length : 1
      if ((this.elList.length + elToAddNum) > MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD) {
        inBoardMessage(() => {
          this.$message(`${this.$t('sdp.message.elementsInBoardMessage')}`)
        })
      }

      if (this.isAdvanceContainerEdit) {
        if ((this.activeTabElsIdList.length + elToAddNum) > MAX_COUNT_ADVANCE_CONTAINER_BOARD) {
          this.$message.info(this.$t('sdp.message.advanceContainerElMsg', { prop: this.activeTabElsIdList.length }))
          return false
        }
      }

      if ((this.elList.length + elToAddNum) > MAX_COUNT_ELEMENTS_IN_BOARD) {
        this.$message.info(this.$t('sdp.message.mostElementsInBoard'))
        return false
      }

      const customerElements = this.elList.filter(el => el.type === TYPE_ELEMENT.CUSTOMER_ELEMENT)
      if ((customerElements.length >= CUSTOMER_ELMENT_MAX_NUM && data.type === TYPE_ELEMENT.CUSTOMER_ELEMENT) || (Number(data.folder?.elementType) === 4 && ((customerElements.length + elToAddNum) > CUSTOMER_ELMENT_MAX_NUM))) {
        this.$message(this.$t('sdp.message.mostCustomerElement'))
        return
      }
      // 引入元素的情况，找出自定义元素数量，与现有自定义元素数量相加，不能超过最大
      if (data.type === TYPE_ELEMENT.ELEMENT_REFERENCE) {
        const referCustoms = this.referenceRecords.filter(record => {
          return record.type === TYPE_ELEMENT.CUSTOMER_ELEMENT && data.ids.includes(record.elementId)
        })
        if (customerElements.length + referCustoms.length > CUSTOMER_ELMENT_MAX_NUM) {
          return this.$message(this.$t('sdp.message.mostCustomerElement'))
        }
      }

      const eventData = new EventData({
        ...this.defaultEventData,
        type: 'addElement',
        data,
        source: 'elementsPanel',
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'addBoardElement',
      })

      eventData.options.callback = (newEl) => {
        callback && callback(newEl)
        // 记录操作
        this.recordAddElement(newEl)
        this.scrollToNewEl(newEl)
      }

      this.$emit('eventBus', eventData)
    },
    scrollToNewEl(newEl) {
      const eventData = new EventData({
        source: 'elementPanel',
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'scrollTargetElement',
        data: newEl.id,
        type: 'scrollTargetElement',
      })
      // this.$nextTick(() => {
      //   this.$emit('eventBus', eventData)
      // })

      setTimeout(() => {
        this.$emit('eventBus', eventData)
      }, 250)
    },
    // 引用表格方法
    // boardType 看板类型：从看板引用元素用到 otherParams:复制元素弹窗中传出的额外参数
    setTemplateIdsEventBus(ids, selectedFolder, referenceRecords = [], boardType, otherParams = {}) {
      const callback = newEl => {
        const eventData = new EventData({
          source: 'setTemplateIdsEventBus',
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'refreshEl',
          data: { ids: [newEl.id], },
          type: 'addElement',
        })
        setTimeout(() => {
          this.$emit('eventBus', eventData)
        }, 500)
      }

      let referenceCallback = null
      // hbw
      if (this.templateType === TYPE_ELEMENT.ELEMENT_REFERENCE) {
        referenceCallback = (newEl, oldEl, idsMap) => {
          const eventData = new EventData({
            type: 'addElement',
            target: ['dataScreenHeader'],
            targetFn: 'handleReferenceElement',
            data: {
              newEl,
              oldEl,
              idsMap,
              boardType
            }
          })
          this.$emit('eventBus', eventData)
        }
      }
      this.referenceRecords = referenceRecords

      // hbw 复制元素后排版回调,大屏元素不需要
      // if (this.templateType === TYPE_ELEMENT.COPY_ELEMENT && !this.isMobile) {
      //   referenceCallback = (newEl, oldEl, idsMap) => {
      //     const eventData = new EventData({
      //       type: 'addElement',
      //       target: ['dataScreenHeader'],
      //       targetFn: 'handleReferenceElement',
      //       data: {
      //         newEl,
      //         oldEl,
      //         idsMap,
      //         boardType
      //       }
      //     })
      //     this.$emit('eventBus', eventData)
      //   }
      // }
      // 如果是复制的筛选器
      if (this.templateType === TYPE_ELEMENT.COPY_ELEMENT && !!referenceRecords.filter(item => item.copyElementIsParamsList).length) {
        const eventData = new EventData({
          ...this.defaultEventData,
          data: { copyElement: referenceRecords.filter(item => item.copyElementIsParamsList) },
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'addParamsFormCopy'
        })
        this.$emit('eventBus', eventData)
      }
      // 在这里区分一下是元素 还是 筛选器
      this.sendEventBus({
        type: this.templateType,
        ids,
        refreshCallback: callback,
        folder: selectedFolder,
        referenceCallback,
        copyElement: referenceRecords.filter(item => item.copyElementIsElList),
        metricGroupMap: otherParams.metricGroupMap || {},
        customComputedMap: otherParams.customComputedMap || {},
        copyForm: otherParams.copyForm,
        referenceFrom: boardType
      })
    },
    handUploadImgStatus(type, file) {
       const eventData = new EventData({
          target: ['displayPanel'],
          targetFn: 'setScreenLoading',
          data: { val: true },
          type: 'setBoardLoading',
        })
      if (type === 'start') {

      }
      if (['error', 'success'].includes(type)) {
        eventData.data.val = false
      }
      this.$emit('eventBus', eventData)
    },
    uploadImgChange(data) {
      // 记录图片日志
      const params = Object.assign({}, OPERATION_LOG, {
        // 渠道（PC、SUB、APP）。注：SUB（订阅提醒，包括PC、APP）
        channel: this.utils.env.channel || '',
        // 日志类型（1：登录日志，2：操作日志）
        logType: '2',
        // 本节点ID
        objectId: this.boardInfo.id,
        // 本节点名称
        objectName: this.boardInfo.name || '',
        // 操作内容（前后对比json串）
        operateContent: `Add the picture, url: ${data.url || ''}`,
        // 操作页面ID
        operatePageId: this.boardInfo.id,
        // 操作类型（1：新增，2：修改，3：..., 59: 浏览）
        operateType: '2',
        // 切换租户得到的企业ID
        tenantId: this.utils.tenantId,
        // 本地浏览器时间 2018-11-23 00:00:00
        updateDateLocal: this.boardInfo.nowStr,
        // 页面
        menuI18Key: this.utils.env.menuI18Key || '',
        // 模块
        modelI18Key: this.utils.env.modelI18Key || ''
      })
      // 是上传的图片，不是选择模板库时才记录日志
      if (data?.uploadImageData?.id) {
        operationalLog(this.utils.api, params)
      }
      this.sendEventBus({ type: TYPE_ELEMENT.IMAGE, result: data })
    },
    materialMenuChose(item) {
      this.currentMaterial = item
    },
    elementTypeIconHandler(type) {
      // this.elementType = type
      if (type === TYPE_ELEMENT.ELEMENT_TITLE) {
        this.popoverLeftOptions = ELEMENT_TITLE_TYPES
      } else {
        this.popoverLeftOptions = MATERIAL_ELEMENT
      }
      this.currentMaterial = this.popoverLeftOptions[0]
    },
    materialElementChose(ele, type) {
      this.currentMaterialElement = ele
      this.addElement(type)
      this.closeMaterialPopover()
    },
    closeMaterialPopover() {
      const popover = this.$refs.materialPopover
      let popoverRef
      if (Array.isArray(popover)) {
        if (popover.length > 0) {
          popoverRef = popover[0]
        }
      } else {
        popoverRef = popover
      }
      if (popoverRef.doClose !== undefined && typeof popoverRef.doClose === 'function') {
        popoverRef.doClose()
      }
    },
    getMaterialNameByType(type) {
      const typeMap = {
        LINE: this.$t('sdp.views.line'),
        BORDER: this.$t('sdp.views.border'),
        TITLE: this.$t('sdp.views.title'),
        TITLE_COMMON: this.$t('sdp.views.frequentStyle'),
        TITLE_ALIGN_CENTER: this.$t('sdp.views.middleTitle'),
        TITLE_ORDER: this.$t('sdp.views.orderTitle'),
      }
      return typeMap[type] || ''
    },
    // 从看板引入元素
    setImportElement(type) {
      this.templateType = type
      this.elementImportDialogVisible = true
    },
     // 移动端复制元素
     copyElement(type) {
      this.templateType = type
      this.copyElementDialogVisible = true
    },
  }
}
</script>

<style lang="scss" scoped>
@import 'packages/base/board/displayPanel/params/utils/variable.scss';
.databoard-wrap {
  display: flex;
  justify-content: space-between;
}

.databoard-pic-btn {
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 24px;
  height: 24px;
  line-height: 24px;
  // background-color: #ffffff;
  margin-right: 6px;
  // box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  color: #333333;
  outline: none;
  font-size: 16px;
  .sdpiconfont {
    font-size: 24px;
  }
}
.databoard-pic-btn:hover .sdpiconfont {
  color: $color-main !important;
  background: #ffffff;
  background-color: var(--sdp-cszj-yxibj) !important;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10);
}
.disabled {
  cursor: not-allowed !important;
  color: var(--sdp-jys) !important;
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}
.databoard {
  height: 34px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
  flex-wrap: wrap;
  .btn-wide {
    width: 100px;
  }
}
.icon-size {
  width: 28px;
  height: 28px;
}
.sdpiconfont {
  font-size: 24px;
  color: var(--sdp-cszjq-is);
}
.el-dropdown-menu {
  margin-right: 0;
  .el-dropdown-menu__item{
    padding: 0 12px;
    line-height: 28px;
  }
  .element-second{
    .element-second-icon{
      font-size: 16px;
      margin-right: 8px;
    }
  }
}
</style>
