<template>
  <el-dialog
    key="interaction-setting-dialog"
    :visible.sync="dialogShow"
    :close-on-click-modal="false"
    :custom-class="`sdp-interactionDialog sdp-dialog ${getCurrentThemeClass()}`"
    :title="$t('sdp.views.interactionSet')"
    @close="dialogClose"
    append-to-body
  >
    <div class="sdp-main sdp-theme-params-setting-border" :class="{'no-border': isTabChart}">
      <div class="sdp-main-name param-setting-normal-font">{{$t('sdp.views.mainEle')}}：<span>{{getElName}}</span></div>
      <el-checkbox v-if="utils.isScreen && !utils.isDataReport && (isTable || ANIMATION_WITH_INTERACTION_CHART.includes(selectedEl.content.alias))" :title="$t('sdp.views.animationWithInteraction')" v-model="animationWithInteraction">{{$t('sdp.views.animationWithInteraction')}}</el-checkbox>
      <div v-if="isTabChart">
        {{$t('sdp.views.mainEleDataset')}}: {{ mainEls[0] ? mainEls[0].dataSetName : ''}}
      </div>
    </div>

    <div class="sdp-main-tab sdp-theme-params-setting-border" v-if="isTabChart">
      <el-tabs
        v-model="activeTab"
        type="card"
        class="tab-style"
        @tab-click="handleTabChange"
      >
        <el-tab-pane
          :key="index"
          v-for="(item, index) in tabList"
          :label="item.title"
          :name="item.name"
        >

        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="sdp-interaction-setting-container">
      <el-scrollbar class="sdp-scrollbar sdp-theme-params-setting-border" v-sdp-el-scrollbar="utils.themeParameters">
        <div style="font-weight:bold" :style="{ height: isTabChart ? '24px' : '', lineHeight: isTabChart ? '24px' : ''}" class="param-setting-normal-font">
          {{$t('sdp.views.b_elements')}}
        </div>
        <div class="sdp-tab-all-chioce" v-if="isTabChart">
<!--          <el-checkbox-->
<!--            style="display: none"-->
<!--            :title="$t('sdp.views.usedWithAllChioce')"-->
<!--            :disabled="disabledByAllUsed"-->
<!--            v-model="usedAllChioce"-->
<!--            @change="allChioceChange"-->
<!--          >-->
<!--            {{$t('sdp.views.usedWithAllChioce')}}-->
<!--          </el-checkbox>-->

          <el-button
            class="sdp-btn sync-btn"
            @click="allChioceChange(true)"
          >
            {{$t('sdp.views.Sync')}}
          </el-button>
          <el-tooltip
            effect="dark"
            :content="$t('sdp.tooltips.SynchronizeAssociated')"
            placement="top"
          >
            <i class="el-tooltip icon-sdp-info"/>
          </el-tooltip>
        </div>
        <el-group-tree
          ref="tree"
          :multiSelect="true"
          class="paramcommponents-tree"
          :elList="hasDataSetElList"
          :treeProps="treeProps"
          default-expand-all
          :include="includeList"
          :excludeIds="excludeIds"
          :withDialogVisible="dialogShow"
          :checkedKeys="checkedKeys"
          @checked-change="onCheckedChange">
        </el-group-tree>
      </el-scrollbar>
      <el-scrollbar class="sdp-interaction-setting-right"  v-sdp-el-scrollbar="utils.themeParameters" v-loading="loading" element-loading-spinner="sdp-loading-gif" ref="scrollInteraction">
          <el-collapse
            :class="{
              'sdp-type-chart': isChart || isCustomer,
              'sdp-tab-chart': isTabChart,
            }"
            v-model="activeIds"
            @change="handleChange"
          >
              <!--  去除  -->
              <interaction-table-exclude v-if="isShowExclude" :excludeData="excludeData" :associElements="associElements" v-bind="{substring15}"/>
            <template
              v-for="(mainEl, index) in mainEls"
            >
            <el-collapse-item
              v-if="!isTabChart || (mainEl.chioceTabId === activeTab)"
              :title="`${$t('sdp.views.mainEleDataset')}: ${mainEl.dataSetName}`"
              :name="mainEl.dataSetId"
              :key="`${mainEl.dataSetId}${index}`"
            >
                <div>
                  <div class="sdp-field-box">
                    <span class="sdp-field-name sdp-name-color">{{$t('sdp.views.interactiveField')}}</span><span class="sdp-field-name sdp-name-color">|</span>
                    <el-popover
                      :popper-class="'sdp-board-design sdp-popover sdp-dialog sdp-params-theme-background sdp-field-list '+ getCurrentThemeClass()"
                      placement="bottom"
                      width="400"
                      @show="popoverShow(mainEl)"
                      v-model="mainEl.isPopover"
                      trigger="click">
                      <el-scrollbar v-sdp-el-scrollbar="utils.themeParameters">
                        <el-checkbox-group class="sdp-checkbox-group" v-model="mainEl.selectedFieldIds">
                          <el-checkbox
                            :disabled="field.hide"
                            :title="getTagSiteName(mainEl, field.label, field.site)"
                            style="width: 100%;white-space: nowrap;"
                            v-for="field in mainEl.fieldList" :label="field.site" :key="field.site">
                            {{ getTagSiteName(mainEl, field.label, field.site) }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </el-scrollbar>
                      <p class="sdp-btn-opera">
                        <el-button type="sdp-ensure" size="mini" class="sdp-preview-btn el-button--sdp-ensure"  @click="selectedFieldIdsFun(mainEl)">{{$t('sdp.button.ensure')}}</el-button>
                      </p>
                      <span v-if="!(isChart || isCustomer)" class="sdp-field-name sdp-field-cursor" slot="reference"><i class="iconStyle icon-sdp-add">
                        </i>{{$t('sdp.views.add')}}
                      </span>
                    </el-popover>
                    <span v-if="(isChart || isCustomer)" class="sdp-field-name sdp-field-cursor" :class="mainEl.selectedFieldId ? 'sdp-not-allowed' : ''" @click="addChartField(mainEl)">
                      <i class="iconStyle icon-sdp-add"></i>{{$t('sdp.views.add')}}
                    </span>
                  </div>
                  <div style="display: flex;flex-wrap: wrap">
                    <el-tag
                      class="sdp-interaction-tag"
                      :class="site === mainEl.selectedFieldId ? 'sdp-interaction-tag-active' : ''"
                      :key="site"
                      v-for="(tag, site) in mainEl.selectedField"
                      :closable="!(isChart || isCustomer)"
                      :disable-transitions="false"
                      :title="getTagSiteName(mainEl, tag.label, site)"
                      @click="setSlectedFieldId(mainEl, site)"
                      @close="closeSelectedFieldId(mainEl, site)">
                      <div>
                        {{getTagSiteName(mainEl, tag.label, site)}}
                      </div>
                    </el-tag>
                    <i v-if="(isChart || isCustomer) && mainEl.selectedFieldId" class="iconRongqishanchu icon-sdp-rongqishanchu" @click="delChartField(mainEl)"></i>
                  </div>
                  <div class="sdp-target" v-if="!isCard">{{$t('sdp.views.targetElement')}}</div>
                  <!-- 子元素模板 -->
                  <template v-if="!!Object.keys(mainEl.selectedField).length">
                    <div v-for="(mainElItem, key) in mainEl.selectedField" :key="key" v-show="key === mainEl.selectedFieldId">
                      <div v-if="isCard">
                        <card-interaction-el-setting
                          ref="cardInteractionElSetting"
                          class="cardInteractionElSetting"
                          v-bind="{ setElId, associElements, mainElItem, mainEl, allParameterList, elList }"
                        />
                      </div>
                      <div v-else>
                        <interaction-element-item v-bind="{ setElId, associElements, mainElItem, mainEl, substring15, sameDataSet }"/>
                        <!-- 过滤数据 -->
                        <filterCondition
                          v-if="!!filtersObject[mainEl.dataSetId]"
                          v-model="mainEl.openFilter"
                          :ref="mainEl.dataSetId"
                          :type="'interaction'"
                          :filters="filtersObject[mainEl.dataSetId]"
                          :mainEl="mainEl"
                          :filterMap="mainEl.filterMap"
                          :associElements="associElements"
                        />
                      </div>
                    </div>
                  </template>
                </div>
              </el-collapse-item>
            </template>
          </el-collapse>
        </el-scrollbar>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary"
        @click="onSubmit">
        {{$t('sdp.button.ensure')}}
      </el-button>
      <el-button @click="dialogShow = false">
        {{$t('sdp.button.cancel')}}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import elGroupTree from './components/ElGroupTree'
import interactionTableExclude from './InteractionTableExclude'
import interactionElementItem from './InteractionElementItem'
import cardInteractionElSetting from './cardInteractionElSetting'
import filterCondition from './components/filterCondition'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import { getDatasetListByIds } from './api'
import { getElListDataSetIds } from 'packages/base/board/displayPanel/utils'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import { CHART_TYPE, VIRTUAL_DATASET_KEY } from 'packages/assets/constant'
import { INTERACTION_OPTIONS } from 'packages/base/board/displayPanel/supernatant/boardElements/elementTagNewCard/constant'
import { filterMapVerify, substring15 } from '../../displayPanel/utils'
import { getDataSetTreeByIds } from '../../mixins/api'
import { ANIMATION_WITH_INTERACTION_CHART } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { getGlobalParameterList } from '../../displayPanel/utils/helpers/api'
import { NUMBER_FORMAT, PARAMETER_TYPE } from '../../displayPanel/params/utils/constants'

function selectedElDisposal(selectedEl) {
  if (selectedEl.type === TYPE_ELEMENT.CONTAINER && selectedEl.content && selectedEl.content.activeElId) {
    const item = this.elList.find(item => item.id === selectedEl.content.activeElId)
    if (item && [TYPE_ELEMENT.CHART, TYPE_ELEMENT.TABLE, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD].includes(item.type)) {
      return item
    }
    selectedEl = item
  }
  return selectedEl
}

export function getElementDatasetAssociation(element) {
  const type = element.type
  let associatedData = null
  if (type === TYPE_ELEMENT.TABLE) {
    associatedData = element.content?.tableDefaultConfig?.dataSetJoinsConfig || null
  } else if (type === TYPE_ELEMENT.CHART) {
    associatedData = element.content?.associatedData || null
  }

  return associatedData
}

export function getElementDataSet(selectedEl) {
    selectedEl = selectedElDisposal.call(this, selectedEl)
    const { type } = selectedEl

    let dataSetList = []
    let allDataSetIds = []
    let isShowExclude = false

      // 简单表格
    if (type === TYPE_ELEMENT.TABLE) {
      const { isShowExclude: isExclude = false } = selectedEl.vm || {}
      const datasetAssociation = this.$_getProp(selectedEl.content, 'tableDefaultConfig.dataSetJoinsConfig', false)
      const isDatasetAssociation = !isExclude && datasetAssociation && datasetAssociation.hasOwnProperty('mainDatasetId')

      let dataSetIds = selectedEl.content.dataSetIds || []
      // const isVirtualDataset = dataSetIds.includes(VIRTUAL_DATASET_KEY)
      // 一个元素的所有数据集
      if (this.$_getProp(selectedEl, 'content.dataSetIds.length', false)) {
          allDataSetIds = dataSetIds.map((id) => {
            if (id === VIRTUAL_DATASET_KEY) return datasetAssociation.mainDatasetId
            return id
          })
      }
      // 初始化选择元素
      dataSetList = selectedEl.vm.getInteractionData().map((item) => {
        if (item.id === VIRTUAL_DATASET_KEY) {
          item.id = datasetAssociation.mainDatasetId
          item.isDatasetAssociation = isDatasetAssociation
          item.name = datasetAssociation.associateDatasetName
        }
        return item
      })

      isShowExclude = isExclude

    } else if (type === TYPE_ELEMENT.CHART) {
      const associatedData = selectedEl.content.associatedData
      const isDatasetAssociation = this.$_getProp(selectedEl.content, 'chartUserConfig.datasetAssociation', false) && !!Object.keys(associatedData).length

      console.log(associatedData, 'associatedData')

      const isChioceTabChart = TYPE_ELEMENT.CHART === selectedEl.type && !!selectedEl.content?.chioceTab?.length
      if (isChioceTabChart) {
        const chioceTab = selectedEl.content.chioceTab

        chioceTab.forEach(tab => {
          const { id, saveObj } = tab
          const { drillSettings } = saveObj
          const { dataSetId, layers = [] } = drillSettings

          const fieldList = layers.reduce((arr, layer) => {
            // 过滤地图目的地
            arr.push(...layer.dimension.filter(item => !item._mapDestination))
            return arr
          }, []).map(item => ({
            label: item.columnName,
            value: item.columnName,
            keyName: item.keyName || '',
            webFieldType: item.webFieldType
          }))

          if (dataSetId) {
            allDataSetIds.push(dataSetId)
            dataSetList.push({
              chioceTabId: id,
              id: dataSetId,
              fieldList,
              name: associatedData?.associateDatasetName || '',
              isDatasetAssociation
            })
          }
        })
      } else {
        // 数据集 图表只有一个数据集
        const dataSetId = this.$_getProp(selectedEl, 'content.drillSettings.dataSetId', '')
        const layers = this.$_getProp(selectedEl, 'content.drillSettings.layers', [])
        if (dataSetId) {
          allDataSetIds = [dataSetId]
        }
        let fieldList = []
        if (layers.length) {
          fieldList = layers.reduce((arr, layer) => {
            // 过滤地图目的地
            arr.push(...layer.dimension.filter(item => !item._mapDestination))
            return arr
          }, [])
          fieldList = fieldList.map(item => ({ label: item.columnName, value: item.columnName, keyName: item.keyName || '', webFieldType: item.webFieldType }))
        }
        if (dataSetId) {
          dataSetList.push({
            id: dataSetId,
            fieldList,
            name: associatedData?.associateDatasetName || '',
            isDatasetAssociation
          })
        }
      }

    } else if ([TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE].includes(type)) {
      let { dataSetIds = [] } = selectedEl.content
      // 一个元素的所有数据集
      if (dataSetIds.length) {
        allDataSetIds = dataSetIds
      }
      // 初始化选择元素
      dataSetList = selectedEl.vm.getInteractionData()
    } else if (type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
      // const dataSetId = this.$_getProp(selectedEl, 'content.drillSettings.dataSetId', '')
      // 一个元素的所有数据集
      // if (dataSetId) {
        allDataSetIds = getElListDataSetIds([selectedEl])
      // }
      // 初始化选择元素
      dataSetList = selectedEl.vm.getInteractionData()
    } else if (type === TYPE_ELEMENT.CUSTOMER_ELEMENT) {
      const dataSetIds = selectedEl.content.config.dataSetIds
      const dimensions = selectedEl.content.userConfig.dimensions

      if (dataSetIds.length) {
        allDataSetIds = dataSetIds
      }

      let fieldList = []
      if (dimensions.length) {
        fieldList = dimensions.map(item => ({ label: item.columnName, value: item.columnName, keyName: item.keyName || '', webFieldType: item.webFieldType }))
      }

      if (dataSetIds.length) {
        dataSetList.push({
          id: dataSetIds[0],
          fieldList,
        })
      }
    }

    return {
      dataSetList,
      allDataSetIds,
      isShowExclude
    }
}

export default {
  inject: ['utils', 'getUnknownName', 'aliasDict', 'getCurrentThemeClass'],
  components: {
    elGroupTree,
    filterCondition,
    interactionTableExclude,
    interactionElementItem,
    cardInteractionElSetting
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    highlightEl: {
      type: Object,
      default: () => ({}),
    },
    elList: {
      type: Array,
      default: () => [],
    }
  },
  directives: {
    SdpElScrollbar,
  },
  data() {
    return {
      includeList: [TYPE_ELEMENT.CHART, TYPE_ELEMENT.TABLE, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE, TYPE_ELEMENT.FOUR_QUADRANT, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.COMBINE_CARD, TYPE_ELEMENT.DUPONT_ANALYSIS, TYPE_ELEMENT.CUSTOMER_ELEMENT],
      ANIMATION_WITH_INTERACTION_CHART,
      tabList: [],
      activeTab: '',
      treeData: [],
      checkedKeys: [],
      // TODO
      mainEls: [],
      associElements: {},
      associElementsObj: {},
      checkedKeysObj: {},
      activeIds: [],
      allParameterList: [],
      uuid: this.$_generateUUID(),
      loading: false,
      animationWithInteraction: false,
      // 表格是否有去除功能
      isShowExclude: false,
      excludeData: null
    }
  },

  computed: {
    hasDataSetElList() {
      return this.elList.filter((el) => {
        // const { allDataSetIds, dataSetList } = getElementDataSet.call(this, el)
        // return !(!allDataSetIds.length || !dataSetList.some(item => item.fieldList.length))
        const ids = getElListDataSetIds([el])
        return ids.length
      })
    },
    dialogShow: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible')
      },
    },
    getElName() {
      const selectedEl = this.selectedEl
      if (selectedEl) {
        return this.$_getProp(selectedEl, 'content.dataScreenRes.name', selectedEl.elName)
      } else {
        return ''
      }
    },
    getTagSiteName(mainEl, label, site) {
      return function (mainEl, label, site) {
        if (this.isCard) {
          let curTag = mainEl.fieldList.find(el => el.site === site)
          let cardName = curTag?.cardName ? `(${curTag.cardName})` : ''
          let isGrowth = site.includes(INTERACTION_OPTIONS.growthValue)
          return isGrowth ? `${label}${cardName}` : `${this.getUnknownName(mainEl.dataSetId, label)}${cardName}`
        } else if (this.isChart || this.isText || this.isCustomer) {
          return `${this.getUnknownName(mainEl.dataSetId, label)}`
        } else {
          return `${this.getUnknownName(mainEl.dataSetId, label)}(${site})`
        }
      }
    },
    api() {
      return this.utils.api || function() {}
    },
    excludeIds() {
      if (!Object.keys(this.selectedEl).length) return []
      const includedElIds = this.selectedEl.content.includedElIds
      if (includedElIds && includedElIds.length) {
        return includedElIds
      }
      return [this.selectedEl.id]
    },
    isTable() {
      return this.selectedEl.type === TYPE_ELEMENT.TABLE
    },
    filters() {
      const selectedEl = this.selectedEl
      if (selectedEl.type === TYPE_ELEMENT.TABLE) {
        return selectedEl.content.tableDefaultConfig.filters || []
      } else if (selectedEl.type === TYPE_ELEMENT.CHART) {
        return selectedEl.content.drillSettings.filters || []
      } else {
        return []
      }
    },
    filtersObject() {
      return this.filters.reduce((pre, next) => {
        const { dataSetId } = next
        if (Array.isArray(pre[dataSetId])) {
          pre[dataSetId].push(next)
        } else {
          pre[dataSetId] = [next]
        }
        return pre
      }, {})
    },
    treeProps() {
      return {
        label: 'elName',
        children: 'children',
        disabled: () => this.disabledByAllUsed
      }
    },
    // usedAllChioce: {
    //   get() {
    //     const target = this.mainEls.find(e => e.chioceTabId === this.activeTab)
    //     if (target) return !!target.usedAllChioce
    //     return false
    //   },
    //   set(val) {
    //     const target = this.mainEls.find(e => e.chioceTabId === this.activeTab)
    //     if (target) {
    //       target.usedAllChioce = val
    //     }
    //   }
    // },
    // disabledByAllUsed() {
    //   const target = this.mainEls.some(e => e.usedAllChioce && e.chioceTabId !== this.activeTab)
    //   return target
    // },
    // 对选中的元素做筛选
    selectedEl() {
      let selectedEl = this.highlightEl
      return selectedElDisposal.call(this, selectedEl)
    },
    // 是否为图形的简单表格
    isChartGrid() {
      return this.$_getProp(this.selectedEl, 'content.alias', '') === CHART_TYPE.VE_GRID_NORMAL
    },
    // 是否为图形
    isChart() {
      return TYPE_ELEMENT.CHART === this.selectedEl.type
    },
    isTabChart() {
      return this.isChart && !!this.selectedEl.content?.chioceTab?.length
    },
    isCustomer() {
      return TYPE_ELEMENT.CUSTOMER_ELEMENT === this.selectedEl.type
    },
    // 是否为文本
    isText() {
      return TYPE_ELEMENT.TEXT === this.selectedEl.type
    },
    // 是否为卡片
    isCard() {
      return TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD === this.selectedEl.type
    },
    // 交互数据
    interactionOptions() {
      // 拉拷贝的数据做处理
      return this.$_JSONClone(this.$_getProp(this.selectedEl, 'content.interactionOptions', []))
    },
    // 是否有交互
    isInteractionOptions() {
      return !!this.interactionOptions.length
    }
  },
  watch: {
    dialogShow: {
      handler(val) {
        if (val) {
          this.dialogOpen()
        }
      },
      immediate: true,
    }
  },
  mounted() {
    // this.onCheckedChange = this.$_debounce(this.onCheckedChange, 1000)
    this.onScroll()
  },
  methods: {
    substring15,
    handleTabChange(item) {
      const target = this.mainEls.find(e => e.chioceTabId === item.name)
      if (target) {
        this.$set(this, 'checkedKeys', this.checkedKeysObj[target.chioceTabId] ? [...this.checkedKeysObj[target.chioceTabId]] : [])
        this.$set(this, 'associElements', this.$_deepClone(this.associElementsObj[target.chioceTabId] || {}))
        this.onCheckedChange({ checkedEls: this.elList.filter(el => this.checkedKeys.includes(el.id)), isSelect: false })
        this.updateCheckedKeys()
      }
    },
    syncSelectField() {
      this.mainEls.forEach(mainEl => {
        if (mainEl.chioceTabId === this.activeTab) return
        const associElementsTemp = this.associElementsObj[mainEl.chioceTabId] || {}
        Object.keys(associElementsTemp).forEach(key => {
          associElementsTemp[key].forEach(associ => {
            if (mainEl.dataSetId === associ.seletedDataSetId) {
              Object.keys(mainEl.selectedField).forEach(key2 => {
                if (mainEl.selectedField?.[key2]) {
                  mainEl.selectedField[key2][associ.id] = {
                    [associ.seletedDataSetId]: mainEl.selectedField[key2].label
                  }
                }
              })
            }
          })
        })
      })
    },
    updateCheckedKeys(checkedKeys = this.checkedKeys) {
      this.$refs.tree && this.$refs.tree.updateCheckedKeys(checkedKeys)
    },
    allChioceChange(v) {
      if (!v) return

      const target = this.mainEls.find(e => e.chioceTabId === this.activeTab)
      if (target) {
        this.mainEls.forEach(item => {
          if (item.chioceTabId === target.chioceTabId) return
          this.$set(this.checkedKeysObj, item.chioceTabId, [...this.checkedKeys] || [])
          this.$set(this.associElementsObj, item.chioceTabId, this.$_deepClone(this.associElements) || {})
        })
        this.syncSelectField()
      }
    },
    updateAllChioce() {
      // const target = this.mainEls.find(e => e.usedAllChioce)
      // if (target && this.activeTab === target?.chioceTabId) {
      //   this.mainEls.forEach(item => {
      //     if (item.chioceTabId === target.chioceTabId) return
      //     this.$set(item, 'checkedKeys', [...target.checkedKeys] || [])
      //     this.$set(item, 'associElementsObj', this.$_deepClone(target.associElementsObj) || {})
      //   })
      // }
    },
    // 禁止图标点击收缩
    handleChange(val) {
      if (this.isChart || this.isCustomer) {
        this.activeIds = [this.mainEls?.[0]?.dataSetId || '']
      }
    },
    onScroll() {
      this.$nextTick(() => {
        // 监听当前组件的滚动事件
        let scrollbarEl = this.$refs.scrollInteraction.wrap
        scrollbarEl.addEventListener('scroll', () => {
          const { scrollTop, clientHeight, scrollHeight } = scrollbarEl
          if (scrollTop > clientHeight) {
            // select组件层级太高，滚动隐藏
            this.$refs.cardInteractionElSetting?.forEach(option => option.handleClose())
          }
        }, true)
      })
    },
    async getGlobalParameter() {
      this.loading = true
      await getGlobalParameterList(this.api, { tenantId: this.utils.tenantId })// todo 此处暂时是'1'日期类型, 之后需求会增加字符串树治等其他类型, 需要修改
        .then(res => {
          this.loading = false
          this.allParameterList = res
        })
        .catch(() => {
          this.loading = false
          this.allParameterList = []
        })
    },
    // 添加图形字段
    addChartField(mainEl) {
      if (mainEl.selectedFieldId) return
      const { fieldList } = mainEl
      fieldList.forEach((f, i) => {
        i = `${i}_${this.uuid}`
        this.$set(mainEl.selectedField, `${f.keyName || i}`, { label: f.label })
      })
      const siteArr = Object.keys(mainEl.selectedField)
      this.$set(mainEl, 'selectedFieldIds', siteArr)
      this.$set(mainEl, 'selectedFieldId', siteArr[0] || '')
    },
    // 清空图形交互
    delChartField(mainEl) {
      this.$set(mainEl, 'selectedFieldIds', [])
      this.$set(mainEl, 'selectedFieldId', '')
      this.$set(mainEl, 'selectedField', {})
    },
    // 设置相同数据的字段
    sameDataSet(mainEl, id, seletedDataSetId, bool) {
      mainEl[id][seletedDataSetId] = mainEl.label
      return bool
    },
    // 点击初始化选中字段数据
    setElId(mainEl, id) {
      if (!mainEl[id]) {
        this.$set(mainEl, id, {})
      }
      return id
    },
    // 初始化Field选中数据
    popoverShow(mainEl) {
      const { selectedField } = mainEl
      mainEl.selectedFieldIds = Object.keys(selectedField)
    },
    // 点击确定设置选择项
    selectedFieldIdsFun(mainEl) {
      const { fieldList, selectedField, selectedFieldIds, selectedFieldId } = mainEl
      const sField = Object.keys(selectedField)
      sField.forEach(site => {
        if (!selectedFieldIds.includes(site)) {
          this.$delete(mainEl.selectedField, site)
        }
      })
      fieldList.filter(e => selectedFieldIds.includes(e.site)).reduce((pre, next) => {
        const { site, label } = next
        if (!sField.includes(site)) {
          this.$set(pre, site, {
            label
          })
        }
        return pre
      }, mainEl.selectedField)
      if (selectedFieldId === '' || !selectedFieldIds.includes(selectedFieldId)) {
        mainEl.selectedFieldId = selectedFieldIds[0] || ''
      }
      mainEl.isPopover = false
    },
    // 设置选中FieldId
    setSlectedFieldId(mainEl, site) {
      mainEl.selectedFieldId = site
    },
    // 删除FieldId
    /* @export
      * @param {Array} selectedFieldIds选中复选框id
      * @param {Object} selectedField选中复选框的对象
      * @param {string} selectedFieldId选中id
    */
    closeSelectedFieldId(mainEl, site) {
      const { selectedField, selectedFieldIds, selectedFieldId, filterMap } = mainEl
      this.$delete(selectedField, site)
      selectedFieldIds.some((item, i) => {
        if (item === site) {
          mainEl.selectedFieldIds.splice(i, 1)
        }
        return item === site
      })
      // 清除所有过滤
      if (Object.keys(filterMap).length && !Object.keys(selectedField).length) {
        mainEl.filterMap = {}
        mainEl.openFilter = false
      }
      if (site === selectedFieldId) {
        this.setSlectedFieldId(mainEl, selectedFieldIds[0] || '')
      }
    },
    // 打开弹框初始数据
    async dialogOpen() {
      this.initChartTab(this.selectedEl)

      if (this.isInteractionOptions) {
        await this.reduction()
        this.chartTabReduction()
      }
      this.animationWithInteraction = this.selectedEl?.elAttr?.animationWithInteraction || false
      const selectedEl = this.selectedEl
      if (Object.keys(selectedEl).length) {
        this.tableAndChartInit(selectedEl)
      }
    },
    initChartTab(selectedEl) {
      const isChioceTabChart = TYPE_ELEMENT.CHART === selectedEl.type && !!selectedEl.content?.chioceTab?.length
      if (!isChioceTabChart) {
        this.activeTab = ''
        this.tabList = []
        return
      }
      const chioceTab = selectedEl.content.chioceTab
      this.tabList = chioceTab.map(e => {
        const { id, name, saveObj } = e
        return {
          title: name,
          name: id
        }
      })
      this.activeTab = this.tabList?.[0]?.name || ''
    },
    // 数据还原
    async reduction() {
      const elIds = new Set()
      const interactionOptions = this.isTabChart ? this.interactionOptions.filter(e => e.chioceTabId === this.activeTab) : this.interactionOptions
      interactionOptions.forEach(item => {
        const { associElements = [] } = item
        associElements.forEach(associ => {
          elIds.add(associ.id)
        })
      })
      await this.onCheckedChange({ checkedEls: this.elList.filter(el => elIds.has(el.id)), isSelect: true })
    },
    async chartTabReduction() {
      const interactionOptions = this.interactionOptions
      for (const item of interactionOptions) {
        if (!item.chioceTabId) continue
        const keys = item.associElements.map(e => e.id)
        const associElementsTemp = await this.getAssociElementsData(this.elList.filter(el => keys.includes(el.id)))
        this.$set(this.checkedKeysObj, item.chioceTabId, keys)
        this.$set(this.associElementsObj, item.chioceTabId, associElementsTemp)
      }
    },
    // 关闭弹框清空tree选项
    dialogClose() {
      this.$nextTick(() => {
        this.$refs.tree && this.$refs.tree.initData()
      })
    },
    setTableExclude(vm) {
      const { isShowExclude = false, excludeRowData = [] } = vm || {}
      const data = this.interactionOptions.find(e => e.dataSetId === excludeRowData?.[0]?.content?.dataSetId && e.excludeColumnNames)
      this.excludeData = isShowExclude ? Object.assign({
        excludeRowData,
        selectData: [],
        selectedChildData: {},
        dataSetId: excludeRowData?.[0]?.content?.dataSetId || '',
        openFilter: false,
        filterMap: {},
      }, data ? {
        selectData: data.excludeColumnNames,
        selectedChildData: data.associElements.reduce((pre, item) => {
          if (!pre[item.id]) {
            pre[item.id] = {}
          }
          pre[item.id][item.dataSetId] = item.excludeColumnNames
          return pre
        }, {})
      } : {}) : null
      this.isShowExclude = isShowExclude
    },
    // table数据初始化
    async tableAndChartInit(selectedEl) {
      const { type } = selectedEl
      if (type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
        await this.getGlobalParameter()
      } else if (type === TYPE_ELEMENT.TABLE) {
        // 设置去除
        this.setTableExclude(selectedEl.vm)
      }

      const {
        dataSetList,
        allDataSetIds,
      } = getElementDataSet.call(this, selectedEl)

      if (dataSetList.length) {
        const allDatasetList = await getDatasetListByIds.call(this, allDataSetIds)
        // 添加数据集名称
        dataSetList.forEach(dataset => {
          allDatasetList.some(item => {
            if (item.id === dataset.id && !dataset.isDatasetAssociation) {
              dataset.name = item.name
            }
            return item.id === dataset.id
          })
        })
        this.mainEls = dataSetList.map((item, index) => {
          const { fieldList: _fieldList, id, name, chioceTabId, isDatasetAssociation = false } = item
          const fieldList = _fieldList.filter(f => !f.webFieldType)
          let selectedField = {}
          const interactionOptions = this.interactionOptions.filter(e => {
            if (this.isTabChart) return e.chioceTabId === chioceTabId
            let curSite = {}
            if (this.isCard) {
              // curSite = fieldList.find(field => field?.value === e?.columnName || field?.site === e?.site)
              curSite = fieldList.find(field => {
                // 不相等说明设置了别名
                if(field?.label !== field?.value){
                  return field?.value === e?.columnName || field?.site === e?.site
                }else if(field?.label === field?.value){
                  return field?.value === e?.columnName && field?.site === e?.site
                }
              })
              curSite?.site && this.$set(e, 'site', curSite.site)
            }
            return e.dataSetId === item.id && !e.excludeColumnNames && (!this.isCard || curSite?.site)
          })
          // 是否开启数据过滤
          let openFilter = false
          let filterMap = {}
          if (interactionOptions.length) {
            selectedField = this.initInteractions(interactionOptions, selectedField)
            interactionOptions.forEach((inier, n) => {
              // 初始设置过滤
              const { openFilter: open = false, filterMap: fMap = null } = inier
              if (filterMapVerify({ filterMap: fMap, filterData: this.filters })) {
                openFilter = false
                filterMap = {}
              } else {
                openFilter = open
                filterMap = fMap
              }
            })
          } else if (type === TYPE_ELEMENT.CHART) {
            fieldList.forEach((f, i) => {
              i = `${i}_${this.uuid}`
              selectedField[`${f.keyName || i}`] = { label: f.label }
            })
          }
          const siteArr = Object.keys(selectedField)
          return {
            // checkedKeys: !this.isTabChart ? undefined : interactionOptions?.[0]?.checkedKeys.filter(e => this.elList.find(el => el.id === e)) || [],
            // associElementsObj: !this.isTabChart ? undefined : interactionOptions?.[0]?.associElementsObj || {},
            // usedAllChioce: !this.isTabChart ? undefined : interactionOptions?.[0]?.usedAllChioce || false,
            chioceTabId,
            fieldList,
            dataSetId: id,
            dataSetName: name || '',
            selectedFieldIds: siteArr,
            selectedField,
            isPopover: false,
            selectedFieldId: siteArr[0] || '',
            openFilter,
            filterMap,
            isDatasetAssociation
          }
        })
      }
      this.activeIds = [this.excludeData ? '1' : this.mainEls?.[0]?.dataSetId || '']
    },
    initInteractions(interactionOptions, selectedField) {
      interactionOptions.forEach((inier, n) => {
        const { site = `${n}_${this.uuid}`, columnName, associElements, openFilter: open = false, filterMap: fMap = null, selectGranularity, values, actionType = '', indicators = [] } = inier
        let associEl = {}
        if (this.isCard && associElements?.length) {
          const { dataSetId, columnName } = inier
          let id = associElements[0].id
          let options = selectGranularity.reduce((pre, next, index) => {
            let selGranularity = this.allParameterList.find(list => list.id === next.id)
            if (!selGranularity) return pre
            let selGranularityDef = selGranularity?.originDefaultValue ? JSON.parse(selGranularity.originDefaultValue) : {}
            let valueList = selGranularity?.type === PARAMETER_TYPE.NUMBER ? this.granularityList(selGranularityDef) : selGranularityDef.list
            let curVal = valueList.find(val => val.value === values[index])
            pre.push({
              id: next.id,
              value: curVal?.key
            })
            return pre
          }, [])
          if (associEl[id]) {
            Object.assign(associEl[id], { [dataSetId]: columnName, options, selectGranularity, values, actionType, indicators })
          } else {
            associEl[id] = { [dataSetId]: columnName, options, selectGranularity, values, actionType, indicators }
          }
        } else {
          associEl = associElements.reduce((pre, next) => {
            const { dataSetId, columnName, id } = next
            if (pre[id]) {
              Object.assign(pre[id], { [dataSetId]: columnName })
            } else {
              pre[id] = { [dataSetId]: columnName }
            }
            return pre
          }, {})
        }
        let growthLabel = ''
        if (this.selectedEl?.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
          let { growthCompareSetting, growthCompare } = this.selectedEl?.content?.chartUserConfig
          if (this.isCard && growthCompareSetting && columnName?.includes(INTERACTION_OPTIONS.growthValue)) {
            growthLabel = growthCompare === 'growthRate' ? this.$t('sdp.views.growthRate') : this.$t('sdp.views.growthValue')
          }
        }
        // 过滤不匹配的字段
        Object.keys(associEl).forEach((key) => {
            const val = associEl[key] || {}
            Object.keys(val).forEach((k) => {
              const { fieldList = [] } = this.associElements?.[key]?.find(item => item.seletedDataSetId === k) || {}

              if (fieldList.length && !fieldList.some(f => f.value === val[k])) {
                delete val[k]
              }
            })
        })

        if (selectedField[site]) {
          Object.assign(selectedField[site], { label: growthLabel || columnName, ...associEl })
        } else {
          selectedField[site] = { label: growthLabel || columnName, ...associEl }
        }
      })
      return selectedField
    },
    granularityList({ list, format }) { // 获取当前全局参数选中的列表数据
      let { type: fmtType, decimal } = format
      list.forEach(item => {
        let curVal = fmtType === NUMBER_FORMAT.NUMBER ? Number(item.value) : Number(item.value) / 100
        let itemType = fmtType === NUMBER_FORMAT.NUMBER ? 'number' : 'percentage'
        item.value = this.$_getFormatterFunc(itemType, 10 ** Number(decimal), true)(curVal)
      })
      return list
    },
    async onCheckedChange({ checkedEls, isSelect }) {
      // 清除没有选中的
      if (!isSelect) {
        const elIds = checkedEls.map(e => e.id)
        Object.keys(this.associElements).forEach(key => {
          if (!elIds.includes(key)) {
            this.mainEls.forEach(item => {
              if (this.isTabChart && item.chioceTabId !== this.activeTab) return
              // 清除选择子交互设置
              const { selectedField, filterMap } = item
              Object.keys(selectedField).forEach(k => {
                if (Object.keys(selectedField[k]).includes(key)) {
                  this.$delete(selectedField[k], key)
                }
              })
              // 清除过滤
              if (Object.keys(filterMap).length) {
                const dataSetIds = this.associElements[key].map(associEl => associEl.seletedDataSetId)
                const { mapDataSets } = filterMap
                filterMap.mapDataSets = mapDataSets.filter(e => !dataSetIds.includes(e.dataSetId))
              }
            })
            this.$delete(this.associElements, key)
          }
        })
        this.checkedKeys = checkedEls.filter(e => e).map(e => e.id)
        if (this.isTabChart) {
          this.$set(this.checkedKeysObj, this.activeTab, [...this.checkedKeys] || [])
          this.$set(this.associElementsObj, this.activeTab, this.$_deepClone(this.associElements) || {})
        }
        this.updateAllChioce()
        return void '优化取消不应该调接口'
      }
      // 所有数据集
      let allDatasetList = []
      const isCheckedEls = checkedEls.length
      const ids = isCheckedEls ? getElListDataSetIds(checkedEls) : []
      // 取数据
      if (ids.length) {
        allDatasetList = await getDataSetTreeByIds(this.api, { ids, tenantId: this.utils.tenantId }, this.aliasDict)
      }
      // 拼装数据
      if (isCheckedEls) {
        checkedEls.forEach(checkedItem => {
          const { type, elName, id } = checkedItem
          // 获取结果数据集
          const associatedData = getElementDatasetAssociation(checkedItem)

          const { referenceDatasetId = [] } = associatedData || {}

          // 获取数据集
          const dataSetIds = getElListDataSetIds([checkedItem], type).filter(e => !referenceDatasetId.includes(e))

          // 判断是否有数据集
          if (dataSetIds.length || associatedData) {
            if (isSelect && !this.associElements[id]) {
              const data = dataSetIds.map(seletedDataSetId => {
                const { children = [], labeName } = allDatasetList.find(item => item.id === seletedDataSetId) || {}

                return {
                  id,
                  elName,
                  fieldList: children.map(item => ({ label: item.labeName.toUpperCase(), value: item.labeName.toUpperCase(), type: item.columnTpe, comment: item.comment ? item.comment.toUpperCase() : '' })),
                  seletedDataSetId,
                  seletedDataSetName: labeName,
                  type,
                }
              }).filter(e => e.seletedDataSetName)

              if (associatedData && Object.keys(associatedData).length) {
                const { associateDatasetName, mainDatasetId, dataSetJoinsColumns } = associatedData
                data.push({
                  id,
                  elName,
                  fieldList: dataSetJoinsColumns.map(item => ({
                    label: item.labeName.toUpperCase(),
                    alias: item.alias,
                    value: item.columnName.toUpperCase(),
                    type: item.columnTpe,
                    comment: item.comment ? item.comment.toUpperCase() : '',
                    parentId: item.parentId
                  })),
                  seletedDataSetId: mainDatasetId,
                  seletedDataSetName: associateDatasetName,
                  type,
                  isDatasetAssociation: true
                })
              }

              // 组合元素数据
              this.$set(this.associElements, [id], data)
            }
          }
        })
        this.checkedKeys = checkedEls.filter(e => e).map(e => e.id)
        if (this.isTabChart) {
          this.$set(this.checkedKeysObj, this.activeTab, [...this.checkedKeys] || [])
          this.$set(this.associElementsObj, this.activeTab, this.$_deepClone(this.associElements) || {})
        }
        this.updateAllChioce()
      }
    },
    async getAssociElementsData(checkedEls) {
      console.log('---------------------------')
      console.log('getAssociElementsData keys', checkedEls)
      // 所有数据集
      let allDatasetList = []
      const res = {}
      const isCheckedEls = checkedEls.length
      const ids = isCheckedEls ? getElListDataSetIds(checkedEls) : []

      // 取数据
      if (ids.length) {
        allDatasetList = await getDataSetTreeByIds(this.api, { ids, tenantId: this.utils.tenantId }, this.aliasDict)
      }

      // 拼装数据
      if (isCheckedEls) {
        checkedEls.forEach(checkedItem => {
          const { type, elName, id } = checkedItem
          // 获取结果数据集
          const associatedData = getElementDatasetAssociation(checkedItem)

          const { referenceDatasetId = [] } = associatedData || {}

          // 获取数据集
          const dataSetIds = getElListDataSetIds([checkedItem], type).filter(e => !referenceDatasetId.includes(e))

          // 判断是否有数据集
          if (dataSetIds.length || associatedData) {
            const data = dataSetIds.map(seletedDataSetId => {
              const { children = [], labeName } = allDatasetList.find(item => item.id === seletedDataSetId) || {}

              return {
                id,
                elName,
                fieldList: children.map(item => ({ label: item.labeName.toUpperCase(), value: item.labeName.toUpperCase(), type: item.columnTpe, comment: item.comment ? item.comment.toUpperCase() : '' })),
                seletedDataSetId,
                seletedDataSetName: labeName,
                type,
              }
            }).filter(e => e.seletedDataSetName)

            if (associatedData && Object.keys(associatedData).length) {
              const { associateDatasetName, mainDatasetId, dataSetJoinsColumns } = associatedData
              data.push({
                id,
                elName,
                fieldList: dataSetJoinsColumns.map(item => ({
                  label: item.labeName.toUpperCase(),
                  alias: item.alias,
                  value: item.columnName.toUpperCase(),
                  type: item.columnTpe,
                  comment: item.comment ? item.comment.toUpperCase() : '',
                  parentId: item.parentId
                })),
                seletedDataSetId: mainDatasetId,
                seletedDataSetName: associateDatasetName,
                type,
                isDatasetAssociation: true
              })
            }

            // 组合元素数据
            this.$set(res, [id], data)
          }
        })
      }
      return res
    },
    getExcludeOptions() {
      const excludeData = this.excludeData
      const selectedEl = this.selectedEl
      // 没有去除
      if (!excludeData) return null

      const { dataSetId, selectData, selectedChildData, excludeRowData } = excludeData
      // 主元素没有选择维度
      if (!selectData.length) return null

      const associElements = Object.keys(selectedChildData).reduce((pre, next) => {
        // 单个元素中包含的数据集
        const dataSetIds = this.associElements[next] ? this.associElements[next].map(e => e.seletedDataSetId) : []
        Object.keys(selectedChildData[next]).forEach(e => {
          const excludeColumnNames = selectedChildData[next][e]
          if (dataSetIds.includes(e) && excludeColumnNames.length) {
            pre.push({
              id: next,
              dataSetId: e,
              excludeColumnNames
            })
          }
        })
        return pre
      }, [])

      return {
        id: selectedEl.id,
        dataSetId,
        excludeColumnNames: selectData,
        associElements,
        keyNames: excludeRowData.filter(item => selectData.includes(item.content.text)).map(item => item.content.keyName).join(','),
      }
    },

    getInteractionOptions() {
      const selectedEl = this.selectedEl
      let interactionOptions = []
      const mainEls = this.mainEls
      // 收集数据集数据
      if (mainEls.length) {
        mainEls.forEach(item => {
          const { dataSetId, selectedField, openFilter, chioceTabId, isDatasetAssociation } = item
          // 整合数据
          if (this.isCard) {
            Object.keys(selectedField).map(key => {
              const { label } = selectedField[key]
              let filterMap = {}
              // 数据过滤
              const ref = Array.isArray(this.$refs[dataSetId]) ? this.$refs[dataSetId][0] : this.$refs[dataSetId]
              if (ref && openFilter) {
                filterMap = ref.save()
              }
              let mergeObj = {}
              if (!key.includes(this.uuid)) {
                mergeObj['site'] = key
              }
              Object.keys(selectedField[key]).filter(k => k !== 'label').map(next => {
                // 单个元素中包含的数据集
                let associElements = []
                let selectGranularity = []
                let values = []
                const dataSetIds = this.associElements[next] ? this.associElements[next].map(e => e.seletedDataSetId) : []
                let actionType = ''
                let indicators = []
                dataSetIds.forEach(dataSetId => {
                  associElements.push({
                    id: next,
                    dataSetId,
                    columnName: ''
                  })
                })
                Object.keys(selectedField[key][next]).forEach(e => {
                  let curItem = selectedField[key][next][e]
                  if (e === 'selectGranularity') {
                    selectGranularity.push(...curItem)
                  }
                  if (e === 'values') {
                    values.push(...curItem)
                  }
                  // if (dataSetIds.includes(e)) {
                  //   associElements.push({
                  //     id: next,
                  //     dataSetId: e,
                  //     columnName: curItem
                  //   })
                  // }
                  if (e === 'actionType') {
                    actionType = curItem
                  }
                  if (e === 'indicators') {
                    indicators.push(...curItem)
                  }
                })
                let curOptions = Object.assign({
                  id: selectedEl.id,
                  dataSetId,
                  columnName: key.includes(INTERACTION_OPTIONS.growthValue) ? key : label,
                  associElements,
                  selectGranularity,
                  values,
                  actionType,
                  indicators,
                }, filterMap || {}, mergeObj)
                // 清除无用的数据
                if (curOptions.actionType === 'params') {
                  curOptions.indicators = []
                } else if (curOptions.actionType === 'indicator') {
                  curOptions.values = ['']
                  curOptions.selectGranularity = []
                }
                interactionOptions.push(curOptions)
              })
            })
          } else {
            interactionOptions.push(...Object.keys(selectedField).map(key => {
              const { label } = selectedField[key]
              let filterMap = {}
              const associElementsVal = this.isTabChart ? this.associElementsObj[chioceTabId] : this.associElements
              const associElements = Object.keys(selectedField[key]).filter(k => k !== 'label').reduce((pre, next) => {
                // 单个元素中包含的数据集
                const dataSetIds = associElementsVal[next] ? associElementsVal[next].map(e => e.seletedDataSetId) : []
                // 饱含结果数据的子数据
                const associElementsValAssociation = associElementsVal[next] ? associElementsVal[next].filter(e => e.isDatasetAssociation) : []
                // 饱含结果数据的数据id
                const dataSetIdsAssociation = associElementsValAssociation.map(e => e.seletedDataSetId)

                Object.keys(selectedField[key][next]).forEach(e => {
                  let curItem = selectedField[key][next][e]
                  if (dataSetIds.includes(e)) {
                    // if (dataSetIdsAssociation.includes(e)) {
                    //   const associElementsValAssociationItem = associElementsValAssociation.find(data => data.seletedDataSetId === e)

                    //   const field = associElementsValAssociationItem.fieldList.find(item => item.label === curItem)

                    //   e = field.parentId
                    // }

                    pre.push({
                      id: next,
                      dataSetId: e,
                      columnName: curItem,
                      isDatasetAssociation: dataSetIdsAssociation.includes(e)
                    })
                  }
                })
                return pre
              }, [])
              // 数据过滤
              const ref = Array.isArray(this.$refs[dataSetId]) ? this.$refs[dataSetId][0] : this.$refs[dataSetId]
              if (ref && openFilter) {
                filterMap = ref.save()
              }
              let mergeObj = {}
              if (!key.includes(this.uuid)) {
                mergeObj['site'] = key
              }

              const tabChartObj = this.isTabChart ? {
                chioceTabId,
              } : {}

              return Object.assign({
                  id: selectedEl.id,
                  dataSetId,
                  columnName: label,
                  associElements,
                  isDatasetAssociation,
                }, filterMap || {}, mergeObj, tabChartObj)
            }))
          }
        })
      }
      // 验证去除数据
      const excludeOptions = this.getExcludeOptions()
      excludeOptions && interactionOptions.push(excludeOptions)

      return interactionOptions.filter(item => !!item.associElements?.length)
    },

    onSubmit() {
      const selectedEl = this.selectedEl
      const interactionOptions = this.getInteractionOptions()
      if (interactionOptions.length) {
        // 验证交互设置
        if (!this.verifyInteractionOptions(interactionOptions)) return false

        // 判断是否与超链接有冲突
        const superLinkOptions = selectedEl.content.superLinkOptions
        if (this.isTabChart) {
          if (superLinkOptions && interactionOptions && superLinkOptions.some(item => {
            return interactionOptions.some(e => e.chioceTabId === `${item.labelBoard.site}`?.split('_')?.[0])
          })) {
            this.$message(this.$t('sdp.views.reminder'))
            return false
          }
        } else if (superLinkOptions && interactionOptions && superLinkOptions.some(item => {
          const { dataSetId, site } = item.labelBoard
          if (item.labelBoard?.chartDimension?.length > 0) {
            return interactionOptions.some(e => e.dataSetId === dataSetId && item.parameterField.includes(e.columnName))
          } else {
            return interactionOptions.some(e => e.dataSetId === dataSetId && e.site === site)
          }
        })) {
          this.$message(this.$t('sdp.views.reminder'))
          return false
        }

        this.setInteractionOptions(selectedEl, interactionOptions)
        // 设置jumper
        this.setTableInteractive()
      } else {
        // let columnFlag = this.mainEls.every(item => item?.selectedFieldIds?.length)
        // if (!(!Object.keys(this.associElements)?.length && !columnFlag)) {
        //   if (!Object.keys(this.associElements)?.length) {
        //     this.$message.warning(this.$t('sdp.message.selBoardElement'))
        //     return
        //   }
        //   if (!columnFlag) {
        //     this.$message.warning(this.$t('sdp.message.fillInTheData'))
        //     return
        //   }
        // }
        this.setInteractionOptions(selectedEl)
        this.setTableInteractive(false)
      }

      this.dialogShow = false
    },
    setInteractionOptions(selectedEl, interactionOptions = null) {
      this.$set(selectedEl.content, 'interactionOptions', interactionOptions)
      if (!this.isText && selectedEl.elAttr) {
        this.$set(selectedEl.elAttr, 'animationWithInteraction', this.animationWithInteraction)
      }
      if (this.isChartGrid) {
        const vm = selectedEl.vm
        vm && vm.createTable()
      }
    },
    verifyInteractionOptions(options) {
      if (this.isTabChart) {
        let message = ''
        const bool = !options.every(item => {
          const { associElements = [], excludeColumnNames, chioceTabId } = item
          let bool = false
          if (excludeColumnNames) {
            bool = !associElements.length || associElements.some(e => !e.excludeColumnNames.length)
            if (bool) {
              message = `${this.$t('sdp.views.excludeInteractionSetting')} ${this.$t('sdp.views.plsWord')}`
            }
          } else {
            bool = !associElements.length || associElements.some(e => !e.columnName)
            const associElementsVal = this.associElementsObj[chioceTabId]
            if (!Object.keys(associElementsVal)?.length) {
              message = this.$t('sdp.message.selBoardElement')
            } else if (associElements.some(e => !e.columnName)) {
              message = this.$t('sdp.message.plsSelTargetField')
            }
          }
          return bool
        })
        !bool && this.$message({
          message,
          type: 'warning',
        })
        return bool
      }

      // 卡片走单独的校验逻辑
      // 迭代所有site，每个site里面，如所有交互项都没有配置，则提示校验失败
      // 也就是说只要有一个填了就可以了
      if (this.isCard) {
        const keysMap = {}
        options.forEach(option => {
          const key = option.site
          if (!keysMap[key]) {
            keysMap[key] = [option]
          } else {
            keysMap[key].push(option)
          }
        })
        const bool = Object.keys(keysMap).some(key => {
          const arr = keysMap[key]
          // 是否所有的都没填
          return arr.every(item => {
            const { selectGranularity = [], values = [], actionType = 'params', indicators = [] } = item
            return actionType === 'indicator' ? !indicators.length : (!selectGranularity.length || selectGranularity.some(e => !e.id) || !values.length || values.some(e => !e))
          })
        })
        if (bool) {
          this.$message({
            message: `${this.$t('sdp.message.fillInTheData')}`,
            type: 'warning',
          })
        }
        return !bool
      }

      let message = ''
      const bool = !options.some(item => {
        const { columnName, site = '', associElements = [], chioceTabId, excludeColumnNames, selectGranularity = [], values = [], actionType = 'params', indicators = [] } = item
        let bool = false
        // 去除
        if (excludeColumnNames) {
          bool = !associElements.length || associElements.some(e => !e.excludeColumnNames.length)
          if (bool) {
            message = `${this.$t('sdp.views.excludeInteractionSetting')} ${this.$t('sdp.views.plsWord')}`
          }
        } else {
          if (this.isCard) {
            bool = actionType === 'indicator' ? !indicators.length : (!selectGranularity.length || selectGranularity.some(e => !e.id) || !values.length || values.some(e => !e))
            if (bool) {
              message = `${this.$t('sdp.message.fillInTheData')}`
            }
          } else {
            bool = !associElements.length || associElements.some(e => !e.columnName)
            const associElementsVal = this.isTabChart ? this.associElementsObj[chioceTabId] : this.associElements
            if (!Object.keys(associElementsVal)?.length) {
              message = this.$t('sdp.message.selBoardElement')
            } else if (associElements.some(e => !e.columnName)) {
              message = this.$t('sdp.message.plsSelTargetField')
              // message = site && !this.isChart && !this.isText ? `${columnName}(${site}) ${this.$t('sdp.views.plsWord')}` : `${columnName} ${this.$t('sdp.views.plsWord')}`
            }
          }
        }
        return bool
      })
      if (message) {
        this.$message({
          message,
          type: 'warning',
        })
      }
      return bool
    },
    setTableInteractive(jumper = true) {
      const selectedEl = this.selectedEl
      const interactionOptions = selectedEl.content.interactionOptions
      if (selectedEl.type === TYPE_ELEMENT.TABLE && interactionOptions) {
        const vm = selectedEl.vm
        const dataSetArr = []
        const exclude = interactionOptions.find(item => item.excludeColumnNames)

        interactionOptions.filter(
          item => {
            const { columnName: field, dataSetId, site } = item
            const pos = site && vm.getCellPositionByField({ field, type: 'interaction', dataSetId, data: item, site })
            if (pos) {
              dataSetArr.push(pos)
            }
            return pos
          }
        )
        if (dataSetArr.length) {
          vm.setCellsProperty(dataSetArr, { jumper })
        }
        if (!exclude && !dataSetArr.length) {
          this.$set(selectedEl.content, 'interactionOptions', null)
        }
      }
    }
  },
}
</script>
<style lang="scss">
.sdp-dialog.sdp-field-list{
  @import 'packages/assets/theme/theme-setting.scss';
  color: var(--sdp-bg-lhtwz) !important;
  // overflow: auto;
  .sdp-checkbox-group{
    width: 150px;
    height: 200px;
  }
  .sdp-btn-opera{
    float: right;
  }
  .el-checkbox {
    height: auto;
    line-height: inherit;
  }
}
</style>

<style lang="scss" scoped>
/*::-webkit-scrollbar {*/
/*  width: 7px;*/
/*  height: 7px;*/
/*}*/
/*::-webkit-scrollbar-corner {*/
/*    background: #C8C7CA;*/
/*    border-radius: 3px;*/
/*}*/
/*::-webkit-scrollbar-thumb {*/
/*    background: #C8C7CA;*/
/*    border-radius: 3px;*/
/*}*/
.isDisplayNone {
  /deep/ .el-tabs__header {
    display: none;
  }
}
  .el-dialog__wrapper /deep/ {
    .sdp-interactionDialog{
       width: 980px;
       max-height: 550px;
       .el-dialog__header {
          padding: 24px 24px 20px;
          .el-dialog__headerbtn {
              top: 28px;
          }
       }
       .dialog-footer {
         margin-top: 45px;
          text-align: right;
        }
      .el-dialog__body {
        padding: 0 24px;
        overflow: hidden;
        // font-size: 12px;
        .paramcommponents-tree .el-tree-node>.el-tree-node__children {
                overflow: inherit;
          }
        .sdp-main {
          border-bottom: 1px solid var(--sdp-cszj-bkfgx);
          display: flex;
          padding-bottom: 10px;
          font-size: 12px;
          flex: 0;
          .sdp-main-name{
            padding-bottom: 10px;
            font-size: 12px;
            letter-spacing: 0;
            white-space: nowrap;
            //text-overflow: ellipsis;
            font-weight: 400;
            width: 50%;
            span{
              font-weight: 500;
              padding-left: 24px;
            }
          }
          .sdp-main-select{
            margin-right: 8px;
          }
        }
        .no-border {
          border: none !important;
        }
        .sdp-main-tab {
          .el-tabs__header {
            margin: 0;
          }
        }
        .sdp-interaction-setting-container {
          padding-top: 16px;
          display: flex;
          height: 261px;

          .sdp-scrollbar {
            flex: none;
            width: 236px;
            border-right: 1px solid var(--sdp-cszj-bkfgx);
            box-sizing: border-box;
            height: 245px;
            .el-scrollbar__view {
              display: inline-block;
              min-width: 100%;
            }
          }

          .sdp-tab-all-chioce {
            position: absolute;
            top: 0px;
            right: 4px;
            z-index: 2;
            line-height: 24px;
            display: flex;
            align-items: center;

            .el-button {
              height: 24px !important;
              line-height: 24px !important;
              padding: 0 15px;
            }

            /*/deep/ {*/
            /*  .el-button {*/
            /*    height: 24px !important;*/
            /*    line-height: 24px !important;*/
            /*    padding: 0 15px;*/
            /*  }*/
            /*}*/
            i {
              color: var(--sdp-zs);
            }
          }

          .sdp-interaction-setting-right {
              padding-left:16px;
              width:100%;
              box-sizing: border-box;
              .el-scrollbar__wrap {
                overflow-x: hidden;
              }
              // 兼容火狐
              .el-collapse {
                border: none;
                .el-collapse-item__content{
                  padding-top: 10px;
                }
                .el-collapse-item__header {
                  font-size: 12px;
                  color: #333333;
                  letter-spacing: 0;
                  line-height: 12px;
                  font-weight: 400;
                }
              }
              .sdp-type-chart {
                // 兼容火狐
                .el-collapse-item__header{
                  cursor: auto;
                  i{
                    display: none;
                  }
                }
                // 兼容火狐
                .el-collapse-item__wrap{
                  border: none;
                }
              }
              .sdp-tab-chart {
                .el-collapse-item__header {
                  display: none;
                }
              }

              .sdp-field-box{
                height: 28px;
                line-height: 28px;
                //display: flex;
                //flex-wrap: wrap;
                //align-items: center;
                .sdp-not-allowed{
                  cursor: not-allowed!important;
                }
                .sdp-name-color{
                  color: #333333;
                  font-size: 12px;
                }
                .sdp-field-name{
                  // display: inline-block;
                  vertical-align: top;
                  padding-right: 16px;
                }
                .sdp-field-cursor {
                  padding-right:40px;
                  cursor: pointer;
                }
                .iconStyle{
                  font-size: 12px;
                  padding-right:8px;
                }
              }
              .sdp-target{
                padding-top: 16px;
                padding-bottom: 12px;
                font-size: 14px;
                font-weight: 500;
                color: #222222;
                letter-spacing: 0;
                line-height: 12px;
              }
              .sdp-interaction-tag{
                position: relative;
                width: 82px;
                height: 28px;
                background-color: var(--sdp-fs1);
                border-radius: 2px;
                border: 1px solid var(--sdp-zs);
                box-sizing: border-box;
                cursor: pointer;
                margin-right: 12px;
                margin-bottom: 8px;
                div {
                  overflow: hidden;
                  font-weight: 400;
                  font-size: 12px;
                  letter-spacing: 0;
                  text-overflow:ellipsis;
                  white-space: nowrap;
                  height: 28px;
                  line-height: 28px;
                }
                .el-icon-close{
                  display: none;
                  position: absolute;
                  top: -7px;
                  right: -8px;
                  color: var(--sdp-zs);
                  background-color: #E7E5F1;
                }
                &:hover .el-icon-close{
                  display: block;
                }
                // 兼容火狐
                .el-icon-close{
                  display: none;
                  position: absolute;
                  top: -7px;
                  right: -8px;
                  color: var(--sdp-zs);
                  background-color: #E7E5F1;
                }
                &:hover .el-icon-close{
                  display: block;
                }
              }
              .iconRongqishanchu {
                vertical-align: top;
                color: var(--sdp-qcgls);
                cursor: pointer;
                height: 28px;
                line-height: 28px;
              }
              .sdp-interaction-tag-active{
                background: var(--sdp-zs);
                div {
                  color:#fff;
                }
              }
            }
        }
      }
    }
  }

  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .chart-item {
    margin: 10px 0;
  }

  .pad-left-10 {
    padding-left: 10px;
  }

  .pad-left-20 {
    padding-left: 20px;
  }

  .pad-left-30 {
    padding-left: 30px;
  }

  .short-select {
    width: 100px;
  }

  .sync-btn {
    &:hover {
      @include common-header-preview-hover-button;
      opacity: 1 !important;
    }
  }
</style>

<!-- 主题 -->
<style lang="scss" scoped>
  .el-dialog__wrapper /deep/ {
    .sdp-interactionDialog{
      background-color: var(--sdp-szk-bjs);
      .el-dialog__body {
        @import 'packages/assets/theme/theme-setting.scss';
        @import 'packages/assets/theme/theme-tree.scss';
        background-color: var(--sdp-szk-bjs);
        .sdp-main, .sdp-scrollbar {
          @include sdp-mixin-style($type:(
            borderColor:(resetBorder:true),
          ),$self: (
            sdp-classic-white: (
              resetBorder: #ddd,
            ),
            sdp-dark-blue: (
              resetBorder: #273341,
            )
          ));
        }
        .el-collapse-item__header, .el-collapse-item__content {
          background-color: var(--sdp-szk-bjs);
          color: var(--sdp-xxbt1) !important;
        }
        .el-collapse-item__wrap, .el-collapse-item__header {
          border-color: var(--sdp-cszj-bkfgx) !important;
        }
        .sdp-name-color, .sdp-target, .el-tag, .sdp-seletedDataSetName {
          color: var(--sdp-xxbt1) !important;
        }
      }
    }
  }
</style>
