import { SIM_TYPE } from "../../../../common/referenceDataSet/constants"

export function getFolderList(params) {
  return this.api.get('/bi/metaFolder/getDateSetTree', { params })
}

// 查询列表
export function getTableList(params) {
  return this.api.get('/bi/metaDashboard/pageList', { params })
}

// 开源框架接口
export function getTenantFolderTree(num) {
  return this.api.get(`/bi/metaFolder/getTenantFolderTree/4/${num}`)
}

export function getTenantFolderList(params) {
  return this.api.post('/bi/metaFolder/tenantFolderTree', params)
}

export function getSbiMetaFolderTree(params) {
  return this.api.post('/bi/sbiMetaFolder/getFolderTree', params)
}

export function getInfo(api) {
  return api.get('/admin/tenant/info')
}

export function getClassFyTree(api, params = {}) {
  if (SIM_TYPE.isSim) {
    return api.post('/query/sim/index/classify/list', params)
  }
  return api.post('/query/entIndex/getClassFyTree', params)
}

export function getPage(api, params) {
  if (SIM_TYPE.isSim) {
    return api.post('/query/sim/index/list', params)
  }
  return api.post('/query/entIndex/list', params)
}
