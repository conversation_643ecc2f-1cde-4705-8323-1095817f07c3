<template>
  <div class="root"
       :class="[isShowMore ? 'shadow' : '', (isShowMore && commonData.isPreview && themeData.enableTheme && themeData.themeType === 'sdp-dark-blue') ? 'theme-dark-mode' : '' ]">
    <cube-scroll
      ref="scroll"
      :data="btnsCopy"
      :scrollEvents="['scroll']"
      :options="{bounce: false}"
      direction="horizontal"
      class="horizontal-scroll-list-wrap"
      v-if="!utils.isDailyConcerned"
    >
      <div class="list-wrapper">
        <template v-if="isShowMore">
          <div class="list-item" v-for="(btn,index) in moreBtnsCopyRemoveRefreshElement" :key="'moreBtns_'+index">
            <template v-if="isIconShow(btn)">
              <i
                :title="btn.title"
                :class="btn.icon"
                @click="onClick(btn)"
                class="icon"
              />
            </template>

            <template v-if="btn.icon === 'icon-sdp-sdp-gengxinshijian' && !isMobile && commonData.isPreview && canShowGengXinShiJian && boardInfo.showElementUpdateTime">
              <el-popover
                placement="bottom-start"
                :title="btn.title"
                trigger="hover"
                @show="btn.fetchUpdateTime(element, elementId, btn)"
                :popper-class="'time_popover '+ getCurrentThemeClass()"
                width="290"
                :content="btn.content"
              >
                <i :title="btn.title" :class="btn.icon" slot="reference" class="icon"/>
              </el-popover>
            </template>

            <FilterPopover
              v-if="btn.buttonKey === 'dataFilter' && !isMobile && commonData.isPreview && !isFilterSortTile"
              :btn="btn"
              :element="element"
              :boardInfo="boardInfo"
              @eventBus="eventBus"
            />

            <SortPopover
              v-if="btn.buttonKey === 'dataSort' && !isMobile && commonData.isPreview && !isFilterSortTile"
              :btn="btn"
              :element="element"
              @eventBus="eventBus"
            />

            <params-info-box
              v-if="btn.icon === 'icon-sdp-sdp-canshuxinxi' && !isMobile && paramsInfoIconShow(element)"
              :btn="btn"
              :element="element"
              @eventBus="eventBus"
            />
          </div>

          <div class="list-item" v-if="!commonData.isPreview && !utils.isPcMobile">
            <i
              :title="containerBtn.title"
              :class="containerBtn.icon"
              @click="containerBtn.onClick(element)"
              class="icon"
              v-if="previewIcon && !isDailyConcerned"
            />
          </div>

          <div class="list-item" v-if="!commonData.isPreview" v-for="(btn,index) in btnsCopy" :key="'btns_'+index">
            <i
              v-if="btn.buttonKey !== 'dataFilter' && btn.buttonKey !== 'dataSort'"
              :title="btn.title"
              :class="btn.icon"
              @click="btn.onClick(element)"
              class="icon"
            />

            <FilterPopover
              v-if="btn.buttonKey === 'dataFilter' && element.filterData && (!isFilterSortTile || utils.isPcMobile)"
              :btn="btn"
              :boardInfo="boardInfo"
              :element="element"
              @eventBus="eventBus"
            />

            <SortPopover
              v-if="btn.buttonKey === 'dataSort' && (!isFilterSortTile || utils.isPcMobile)"
              :btn="btn"
              :element="element"
              @eventBus="eventBus"
            />
          </div>
        </template>

        <div class="list-item" v-if="(commonData.isPreview ? isShowMore : true) && iconRefreshElement && isIconShow(iconRefreshElement)">
          <i
            :title="iconRefreshElement.title"
            :class="[iconRefreshElement.icon, !isShowMore && 'icon-not-show-more']"
            @click="onClick(iconRefreshElement)"
            class="icon"
          />
        </div>

        <div class="list-item" v-if="!commonData.isPreview && iconElementEdit && !isShowMore">
          <GuidePopover
            :content="$t('sdp.guide.clickToEnterTheElementEditingPage')"
            :value="isShowStepTipsByElConfigs"
            :step="stepEntryElConfigs"
            :boardScale="boardScale"
            :markPaddingLeft="11"
            :markPaddingRight="11"
            :markPaddingTop="11"
            :markPaddingBottom="11"
            :arrowOffsetX="16"
            :tipsOffsetY="-18"
          >
            <i
              :title="iconElementEdit.title"
              :class="[iconElementEdit.icon, !isShowMore && 'icon-not-show-more']"
              @click="iconElementEdit.onClick(element)"
              class="icon"
            />
          </GuidePopover>
        </div>
      </div>
    </cube-scroll>

    <!-- 隐藏此段老代码，上面如有bug可直接恢复 -->
    <template v-if="isShowMore && utils.isDailyConcerned">
      <div v-for="(btn,index) in moreBtnsCopy" :key="'moreBtns_'+index">
        <template v-if="isIconShow(btn)">
          <i :title="btn.title" :class="btn.icon" @click="onClick(btn)" class="icon"></i>
        </template>
        <template v-if="btn.icon === 'icon-sdp-sdp-gengxinshijian' && !isMobile && commonData.isPreview">
          <el-popover
            placement="bottom-start"
            :title="btn.title"
            trigger="hover"
            @show="btn.fetchUpdateTime(element, elementId, btn)"
            :popper-class="'time_popover ' + getCurrentThemeClass()"
            width="290"
            :content="btn.content">
            <i :title="btn.title" :class="btn.icon" slot="reference" class="icon"></i>
          </el-popover>
        </template>
        <FilterPopover
          v-if="btn.buttonKey === 'dataFilter' && !isMobile && commonData.isPreview && !isFilterSortTile"
          :btn="btn"
          :element="element"
          :boardInfo="boardInfo"
          @eventBus="eventBus"
        />

        <SortPopover
          v-if="btn.buttonKey === 'dataSort' && !isMobile && commonData.isPreview && !isFilterSortTile"
          :btn="btn"
          :element="element"
          @eventBus="eventBus"
        />
        <params-info-box
          v-if="btn.icon === 'icon-sdp-sdp-canshuxinxi' && !isMobile && paramsInfoIconShow(element)"
          :btn="btn"
          :element="element"
          @eventBus="eventBus"
        />
      </div>
      <template v-if="!commonData.isPreview">
        <i :title="containerBtn.title" :class="containerBtn.icon" @click="containerBtn.onClick(element)" class="icon"
           v-if="previewIcon && !isDailyConcerned"></i>
        <div v-for="(btn,index) in btnsCopy" :key="'btns_'+index">
          <i v-if="btn.buttonKey !== 'dataFilter' && btn.buttonKey !== 'dataSort'" :title="btn.title" :class="btn.icon"
             @click="btn.onClick(element)" class="icon"></i>

          <FilterPopover
            v-if="btn.buttonKey === 'dataFilter' && element.filterData && (!isFilterSortTile || utils.isPcMobile)"
            :btn="btn"
            :boardInfo="boardInfo"
            :element="element"
            @eventBus="eventBus"
           />

          <SortPopover
            v-if="btn.buttonKey === 'dataSort' && (!isFilterSortTile || utils.isPcMobile)"
            :btn="btn"
            :element="element"
            @eventBus="eventBus"
          />
        </div>
      </template>
    </template>

    <i :title="moreBtn.title" :class="['popupClickOutsideClassName', moreBtn.icon, changeColor ? 'color' : '', utils.isDailyConcerned ? 'daily-concerned-more' : '']"
       @click="onMoreBtnClick" class="icon clear-margin icon-not-show-more icon-more" v-if="moreBtn && !themeData.themeFullScreen"
       data-cy-elToolsMoreBtn></i>
  </div>
</template>
<script>
import eventBus from 'packages/assets/eventBus'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import FilterPopover from 'packages/base/board/displayPanel/supernatant/filterDialog/FilterPopover'
import SortPopover from 'packages/base/board/displayPanel/supernatant/filterDialog/SortPopover'
import { FILTER_DISPLAY_MODE, isShowSorterWidget } from './filterDialog/utils'
import { DISPLAY_MODE } from './boardElements/elementContainer/constants'
import { isAdvanceContainer_fn } from 'packages/assets/utils/helper'
import { ICON } from './utils/icon'
import { isTableType } from './utils/utils'
import { ELEMENT_EXPORT_TYPE } from '../../settingPanel/components/exportComponents/constants'
import {CUSTOM_FOLDER_TYPE, PREVIEW_STATUS, STANDARD_FOLDER_TYPE} from 'packages/assets/constant'
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin'
import { CAN_COPY_TYPES, IS_CAN_ELEMENT_EXPORT, NOT_CAN_EDIT_TYPES } from './boardElements/constant'
import ParamsInfoBox from './paramsInfoBox/index'
import { SBI_ATTR } from '../../dailyConcernMetrics/utils'
import { kanbanGuideStepEntryElConfigsMixin } from 'packages/base/KanbanGuide'
import {AllowSubscriptionType} from "packages/base/board/settingPanel/constants";
import {getContainerIncludeElsIdList} from "packages/base/board/displayPanel/utils";

export default {
  components: {
    ParamsInfoBox,
    FilterPopover,
    SortPopover,
  },
  name: 'elTools',
  inject: ['commonData', 'tenantData', 'themeData', 'utils', 'isEdit', 'getCurrentThemeClass', 'levelData', 'isEdit', 'platformMetaDashboardConfig', 'isTemplatePreview'],
  mixins: [kanbanGuideStepEntryElConfigsMixin],
  props: {
    isHold: Boolean,
    element: {
      type: Object
    },
    elementId: {
      type: String
    },
    boardScale: {
      type: Number,
      default: 100
    },
    boardInfo: {
      type: Object
    },
    btns: {
      type: Array,
      default: () => []
    },
    moreBtn: {
      type: Object,
    },
    containerBtn: {
      type: Object,
    },
    moreBtns: {
      type: Array,
      default: () => []
    },
    dailyConcernData: {
      type: Object,
      default: () => ({})
    },
    elList: {
      type: Array,
      required: true
    },
    // boardExportFlag: {
    //   type: Number
    // },
    elementExport: {
      type: Object,
    }
  },
  data() {
    return {
      isShowMore: !this.moreBtn,
      TYPE_ELEMENT,
      DISPLAY_MODE,
      btnsCopy: [],
      moreBtnsCopy: [],
      changeColor: false,
      event: null,
    }
  },
  computed: {
    moreBtnsCopyRemoveRefreshElement() {
      return this.moreBtnsCopy.filter(item => item.buttonKey !== 'refreshElement')
    },
    btnsCopyRemoveElementEdit() {
      return this.btnsCopy.filter(item => item.buttonKey !== 'elementEdit')
    },
    iconRefreshElement() {
      return this.moreBtnsCopy.find(item => item.buttonKey === 'refreshElement')
    },
    iconElementEdit() {
      return this.btnsCopy.find(item => item.buttonKey === 'elementEdit')
    },
    canShowGengXinShiJian() {
      return ![TYPE_ELEMENT.IMAGE].includes(this.element?.type)
    },
    isDataReport() {
      return Boolean(this.utils.isDataReport)
    },
    isMobile() {
      return this.utils.isMobile
    },
    isFilterSortTile() {
      return this.element.filterSortDisplayMode && this.element.filterSortDisplayMode === FILTER_DISPLAY_MODE.Tile
    },
    // 预览icon
    previewIcon() {
      return this.element.type === TYPE_ELEMENT.CONTAINER
    },
    isDailyConcerned() {
      return this.utils.isDailyConcerned || false
    },
    projectName() {
      return this.utils.env?.projectName
    },
    metaType() {
      return this.boardInfo.metaType === '1'
    },
    previewType() {
      const previewType = this.commonData?.previewType
      return previewType ? previewType() : ''
    },
    settingConfig() {
      return this.tenantData.settingConfig
    },
    isSbiType() {
      return this.projectName === ALL_PROJECT_NAME.SBI
    },
    // 容器里面没有看板返回false
    containerExport() {
      let is = false
      if (this.previewIcon) {
        if (this.element.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
          this.element.content.tabList.forEach(item => {
             if (item.content.includedElsIdList && item.content.includedElsIdList.length > 0) {
               is = true
             }
          })
        } else {
         is = this.element.content.includedElIds && this.element.content.includedElIds.length > 0
        }
      } else {
        is = true
      }
      return is
    },
  },
  watch: {
    isHold() {
      if (!this.isHold && this.isShowMore) {
        this.changeColor = !this.changeColor
        this.isShowMore = !this.isShowMore
      }
    },
    isShowMore(b) {
      const isNotShowMore = !b
      if (isNotShowMore && this.event.style) this.event.style.width = ''
    },
    element: {
      handler() {
        this.filterSorterIconHandler()
      },
      deep: true
    },
    'commonData.isPreview': {
      immediate: true,
      handler(b) {
        !b && this.filterSorterIconHandler()
      }
    }
  },
  mounted() {
  },
  methods: {
    eventBus,
    scrollRefresh() {
      this.$nextTick(() => {
        const scroll = this.$refs.scroll
        if (scroll) {
          scroll.refresh()

          setTimeout(() => {
            const scrollWidth = scroll.$el.scrollWidth || 0
            const offsetWidth = scroll.$el.offsetWidth || 0
            scroll.scrollTo((-scrollWidth + offsetWidth), 0)
          })
        }
      })
    },
    // mouseenterHandler() {
    //   const fn = this.element?.vm?.animationInstance?.mouseenter
    //   fn && fn.call(this.element.vm.animationInstance)
    // },
    // mouseleaveHandler() {
    //   const fn = this.element?.vm?.animationInstance?.mouseleave
    //   fn && fn.call(this.element.vm.animationInstance)
    // },
    filterSorterIconHandler() {
      // debugger
      this.btnsCopy = this.btns
      this.moreBtnsCopy = this.moreBtns
      let canCopyTypes = CAN_COPY_TYPES
      if (!canCopyTypes.includes(this.element.type)) {
        this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'elementCopy')
      }
      if (TYPE_ELEMENT.CUSTOMER_ELEMENT === this.element.type) {
        const { dataSetIds = [] } = this.element.content.config
        if (!dataSetIds.length) {
          this.moreBtnsCopy = this.moreBtnsCopy.filter(item => item.buttonKey !== 'refreshTime')
        }
      }
      if (NOT_CAN_EDIT_TYPES.includes(this.element.type)) {
        this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'elementEdit')
      }
      if ([TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE].includes(this.element.type)) {
        this.moreBtnsCopy = this.moreBtnsCopy.filter(item => {
          return item.buttonKey !== 'refreshTime' || (!this.element.style.zoomBtn && item.buttonKey !== 'enlargeInPreview')
        })
      }
      if ([TYPE_ELEMENT.WEB, TYPE_ELEMENT.SCROLL_TEXT].includes(this.element.type)) {
        this.moreBtnsCopy = this.moreBtnsCopy.filter(item => {
          return !['refreshElement', 'refreshTime'].includes(item.buttonKey)
        })
      }
      // 只有oms系统有添加至模板的功能
      if (this.element.vm?.getCustomFieldsUsedInElement) {
        const metricGroupFields = this.element.vm.getCustomFieldsUsedInElement(['ALL'])
        if (metricGroupFields?.length) {
          this.btnsCopy = this.btnsCopy.filter(item => !item.icon.match(/icon-sdp-zidingyibianjimingming/g))
        }
      }
      if (![ALL_PROJECT_NAME.OMS, ALL_PROJECT_NAME.EMS, ALL_PROJECT_NAME.SBI].includes(this.utils.env?.projectName) || ![TYPE_ELEMENT.CHART, TYPE_ELEMENT.TABLE, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(this.element.type)) {
        this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'elementToTemplate')
      }
      // if (![ALL_PROJECT_NAME.OMS].includes(this.utils.env?.projectName) && [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(this.element.type)) {
      //   this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'elementToTemplate')
      // }
      // 原先的判断条件
      let conditionOMS = ![ALL_PROJECT_NAME.OMS].includes(this.utils.env?.projectName) && [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(this.element.type)
      // 在ems中卡片组件新增设置模板功能
      let conditionEMS = !(this.utils.env?.projectName === ALL_PROJECT_NAME.EMS && this.element.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD)
      let conditions ={
        conditionEMS,
        conditionOMS,
      }
      if(Object.values(conditions).every(condition => condition)){
        this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'elementToTemplate')
      }

      // 【SBI】预览时，数据更新时间的功能sbi后端未支持，在SBI系统中前端暂时先隐藏下
      if ([ALL_PROJECT_NAME.EMS, ALL_PROJECT_NAME.SBI].includes(this.utils.env?.projectName)) {
        this.moreBtnsCopy = this.moreBtnsCopy.filter(item => item.buttonKey !== 'refreshTime')
        if (this.utils.isDailyConcerned) {
          let ICON_LIST = [
            'icon-sdp-hoverkanbanshuaxin', // 刷新
            'icon-sdp-tuxingshaixuan', // 筛选
            'icon-sdp-tuxingpaixu', // 排序
            'icon-sdp-sdp-dakaiyuankanban', // 看板新标签跳转
            'icon-sdp-sdp-canshuxinxi', // 参数信息
            'icon-sdp-xianshishuzhi', // 数值显示
            'icon-sdp-buxianshishuzhi', // 数值不显示
          ]
          if (this.utils.isPersonalAttentionEdit) { // 編輯界面
            ICON_LIST.push(
              'icon-sdp-kanbanshanchu', // 删除
            )
          }
          this.moreBtnsCopy = this.moreBtnsCopy.filter(item => ICON_LIST.find(icon => new RegExp(icon, 'ig').test(item.icon)))
          this.btnsCopy = this.btnsCopy.filter(item => ICON_LIST.find(icon => new RegExp(icon, 'ig').test(item.icon)))
        } else {
          this.moreBtnsCopy = this.moreBtnsCopy.filter(item => !item.icon.match(/icon-sdp-sdp-dakaiyuankanban/g) && !item.icon.match(/icon-sdp-sdp-canshuxinxi/g))
        }
      } else {
        this.moreBtnsCopy = this.moreBtnsCopy.filter(item => !item.icon.match(/icon-sdp-sdp-dakaiyuankanban/g) && !item.icon.match(/icon-sdp-sdp-canshuxinxi/g))
      }

      // 大屏看板过滤复制、删除icon、模板
      if (this.utils.isScreen && !this.isDataReport) {
        this.btnsCopy = this.btnsCopy.filter(item => !['elementCopy', 'elementDelete'].includes(item.buttonKey))
      }
      // 大屏看板容器内刷新去除
      if (this.utils.isScreen && this.element._containerId) {
        this.moreBtnsCopy = this.moreBtnsCopy.filter(item => item.buttonKey !== 'refreshElement')
      }
      if ([
        TYPE_ELEMENT.CHART,
        TYPE_ELEMENT.TABLE,
        TYPE_ELEMENT.CONTAINER
      ].includes(this.element['type'])
      ) {
        const { filterData, content } = this.element
        if (!(filterData?.isEnable && filterData?.form?.length)) {
          this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'dataFilter')
          this.moreBtnsCopy = this.moreBtnsCopy.filter(item => item.buttonKey !== 'dataFilter')
        }
        if (!isShowSorterWidget(this.element, this.elList)) {
          this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'dataSort')
          this.moreBtnsCopy = this.moreBtnsCopy.filter(item => item.buttonKey !== 'dataSort')
        }
        // roc todo mark by app_advance_container: 移动端的高级容器暂不开放"筛选器/排序器"配置入口
        const isAppAdvanceContainer = this.isMobile && isAdvanceContainer_fn(this.element)
        if (content.settings?.displayMode === DISPLAY_MODE.VERTICAL || isAppAdvanceContainer) {
          this.btnsCopy = this.btnsCopy.filter(item => {
            return item.buttonKey !== 'elementSetting'
          })
        }
      } else {
        this.btnsCopy = this.btnsCopy.filter(item => {
          return item.buttonKey !== 'elementSetting' && item.buttonKey !== 'dataFilter' && item.buttonKey !== 'dataSort'
        })
        this.moreBtnsCopy = this.moreBtnsCopy.filter(item => {
          return !['dataFilter', 'dataSort'].includes(item.buttonKey)
        })
      }
      if (this.commonData.isAdvanceContainerEdit) {
        this.btnsCopy = this.btnsCopy.filter(item => {
          return item.buttonKey !== 'dataFilter' && item.buttonKey !== 'dataSort' && item.buttonKey !== 'elementSetting'
        })
      }
      if (this.isDataReport) {
        if (!this.utils.isMobile && !this.utils.isPcMobile) {
        this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'elementCopy' && item.buttonKey !== 'elementDelete')
        } else {
          this.btnsCopy = this.btnsCopy.filter(item => item.buttonKey !== 'elementCopy')
        }
        this.moreBtnsCopy = this.moreBtnsCopy.filter(item => item.buttonKey !== 'enlargeInPreview')
        if (this.element.type === TYPE_ELEMENT.ELEMENT_TITLE) {
          this.moreBtnsCopy = this.moreBtnsCopy.filter(item => item.buttonKey !== 'refreshTime')
        }
      }
      // 报告移动布局的场景
      if (this.utils.isMobile && this.utils.isPcMobile) {
        this.btnsCopy = this.btnsCopy.filter(item => ['dataFilter', 'dataSort', 'elementDelete'].includes(item.buttonKey))
      }
    },
    onMoreBtnClick(e) {
      this.event = e.target.offsetParent
      this.event.style.width = this.isShowMore ? '' : '100%'
      this.changeColor = !this.changeColor
      this.isShowMore = !this.isShowMore
      this.scrollRefresh()
      // 移动看板组合卡片去除编辑icon
      // if (this.isMobile && this.element.type === TYPE_ELEMENT.COMBINE_CARD) {
      //   this.btnsCopy = this.btns.filter(item => !item.icon.match(/icon-sdp-kanbanbianji1/g))
      // }
      this.filterSorterIconHandler()
    },
    onClick(btn) {
      btn.onClick && btn.onClick(this.element)
      this.isShowMore = false
      this.event && (this.event.style.width = '')
      this.changeColor = false
    },
    isIconShow(btn) {
      // 显示值按钮(眼睛)
      if (btn.icon === 'icon-sdp-xianshishuzhi' || btn.icon === 'icon-sdp-buxianshishuzhi') {
        if (!this.commonData.isPreview || ![TYPE_ELEMENT.CHART, TYPE_ELEMENT.CONTAINER].includes(this.element.type)) return false

        let elementIdList = []
        if (this.element.type === TYPE_ELEMENT.CHART) {
          elementIdList = [this.element.id]
        } else if (this.element.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
          const currentTab = this.element.content.tabList.find(t => t.name === this.element.content.activeTabId)
          elementIdList = currentTab.content.includedElsIdList || []
        } else if (this.element.type === TYPE_ELEMENT.CONTAINER) {
          if (this.element.content.settings.displayMode === 'horizontal') {
            // 横向容器
            elementIdList = [this.element.content.activeElId]
          } else if (this.element.content.settings.displayMode === 'vertical') {
            // 纵向容器
            elementIdList = this.element.content.includedElIds || []
          }
        }
        // 不存在标签的图形类型
        const noLabelChart = ['ve-grid-normal']
        // const hasLabelChart = ['ve-sunburst', 've-treemap', 've-tree', 've-calendar']
        const elementVMList = elementIdList.map(eid => eid && this.elList.find(el => el.id === eid && el.type === TYPE_ELEMENT.CHART && !noLabelChart.includes(el.content.chartUserConfig.chartAlias))?.vm).filter(v => v)

        return elementVMList.some(v => {
          const { labelLineShow, dataShow, percentageShow, chartAlias } = v.element.content.chartUserConfig
          if (!labelLineShow && !dataShow && !percentageShow) return false
          // 依赖labelLineShow的图形判断(不确定全不全)
          const arr = [
            've-pie-normal',
            've-pie-rose',
            've-ring-normal',
            've-liquidfill',
            've-ring-multiple',
            've-roundCascades'
          ]
          if (!arr.includes(chartAlias) && !labelLineShow) return false
          if (chartAlias === 've-tree' && (!labelLineShow && !percentageShow)) return false
          const checkedDimension = v.element.content.saveIndex || 0
          if (v.labelLineButtonList[checkedDimension]) {
            const currentLabelLineButton = v.labelLineButtonList[checkedDimension].labelLineButton
            if (currentLabelLineButton !== undefined) {
              return btn.icon === 'icon-sdp-xianshishuzhi' ? currentLabelLineButton : !currentLabelLineButton
            }
            return btn.icon === 'icon-sdp-xianshishuzhi'
          }
        })
      }
      // this.boardInfo.boardExportFlag === '1' &&
      if (['refreshTime', 'dataFilter', 'dataSort'].includes(btn.buttonKey)) return false
      // return btn.icon === 'el-icon-upload2'
      //   ? btn.isShow && (!this.isMobile && this.commonData.isPreview) && this.containerExport && [TYPE_ELEMENT.CHART, TYPE_ELEMENT.TABLE, TYPE_ELEMENT.FOUR_QUADRANT, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.CONTAINER].includes(this.element.type)
      //   : btn.isShow
      //   || (!this.isMobile && this.commonData.isPreview)
      if (btn.title === this.$t('sdp.views.comment')) {
        return btn.isShow && (!this.isMobile && this.commonData.isPreview && this.commonData.isTenantUser)
      }

      // 每日关注指标
      if (btn.icon === 'icon-sdp-add') {
        if (this.isDataReport) return false
        if ([ALL_PROJECT_NAME.EMS].includes(this.projectName)) {
          const { personalAttentionVos = [] } = this.dailyConcernData
          const personalAttentionElementList = personalAttentionVos.filter(e => e.dashboardId === this.boardInfo.id)
          const targetElement = personalAttentionElementList.find(e => e.elementId === this.element.id)
          const hasAddDaily = !!targetElement
          // todo 正式版用上面这行
          return (!this.isMobile && !this.isEdit && this.commonData?.isPreview && !this.metaType) && this.utils?.sbiOptions?.addToDaily && !hasAddDaily
          // return (!this.isMobile && !this.isEdit && this.commonData?.isPreview && !this.metaType) && !hasAddDaily
        }
        return (!this.isMobile && !this.isEdit && this.commonData?.isPreview && !this.metaType) &&
          ([ALL_PROJECT_NAME.SBI, ALL_PROJECT_NAME.EMS].includes(this.projectName))
          // ([ALL_PROJECT_NAME.SBI, ALL_PROJECT_NAME.EMS].includes(this.projectName) && [CUSTOM_FOLDER_TYPE, STANDARD_FOLDER_TYPE].includes(this.boardInfo?.folderType))
      }
      // 每日关注指标 更新
      if (btn.icon === 'icon-sdp-gengxin') {
        if (this.isDataReport) return false
        if ([ALL_PROJECT_NAME.EMS].includes(this.projectName)) {
          const { personalAttentionVos = [] } = this.dailyConcernData
          const personalAttentionElementList = personalAttentionVos.filter(e => e.dashboardId === this.boardInfo.id)
          const targetElement = personalAttentionElementList.find(e => e.elementId === this.element.id)
          const hasAddDaily = !!targetElement
          // todo 正式版用上面这行
          return (!this.isMobile && !this.isEdit && this.commonData?.isPreview && !this.metaType) && this.utils?.sbiOptions?.addToDaily && hasAddDaily
          // return (!this.isMobile && !this.isEdit && this.commonData?.isPreview && !this.metaType) && hasAddDaily
        }
        return false
        // return (!this.isMobile && !this.isEdit && this.commonData?.isPreview && !this.metaType) &&
        //   ([ALL_PROJECT_NAME.SBI, ALL_PROJECT_NAME.EMS].includes(this.projectName))
          // ([ALL_PROJECT_NAME.SBI, ALL_PROJECT_NAME.EMS].includes(this.projectName) && [CUSTOM_FOLDER_TYPE, STANDARD_FOLDER_TYPE].includes(this.boardInfo?.folderType))
      }

      if (btn.icon === 'icon-sdp-sdp-canshuxinxi') {
        return false
      }

      if (btn.icon === 'icon-sdp-zidingyiyangshi') {
        return btn.isShow && !this.isMobile && this.commonData.isPreview
      }

      if (btn.icon === 'icon-sdp-yulanshifangda' && !this.isMobile) {
        // 智能搜索不展示放大
        if (this.utils?.intelligentData?.isIntelligentComponent) {
          return false
        }

        return this.element.style?.zoomBtn
      }

      if (btn.icon === 'icon-sdp-con-sdp-dingyue-16') {
        const isEnterpriseEditMode = this.utils.isEnterprise || this.commonData.isEnterpriseEdit
        const isTenant = this.previewType === PREVIEW_STATUS.TENANT
        const isTaskPlan = this.levelData.userLevel === '2' && this.boardInfo.allowSubscription !== AllowSubscriptionType.No && !isTenant && !isEnterpriseEditMode && !this.isEdit
        const addScheduledTaskFlag = this.isDataReport
          ? this.platformMetaDashboardConfig?.reportAddScheduledTaskFlag === '1'
          : this.settingConfig.addScheduledTaskFlag === '1'
        const isReleaseBoard = !this.isSbiType || this.boardInfo?.isRelease
        const isReleaseHide = this.isSbiType && this.boardInfo?.isReleaseHide && this.boardInfo?.isReleaseToMe
        const isIntelligentSearch = !!this.utils?.intelligentData?.isIntelligentSearch

        const subscribeBtn = isTaskPlan &&
          addScheduledTaskFlag &&
          !this.commonData.isSubscribe &&
          isReleaseBoard &&
          !isReleaseHide &&
          !this.isTemplatePreview &&
          !this.utils.isShare &&
          !isIntelligentSearch

        const isTenant2 = [ALL_PROJECT_NAME.EMS, ALL_PROJECT_NAME.INFRASYS_ER].includes(this.projectName)
        return btn.isShow && !this.isMobile && this.commonData.isPreview && isTenant2 && subscribeBtn
      }

      return btn.icon === 'icon-sdp-rongqidaochu'
        ? this.getIsShowElementExport() && btn.isShow && (!this.isMobile && this.commonData.isPreview) && this.containerExport && IS_CAN_ELEMENT_EXPORT.includes(this.element.type)
        : btn.isShow ||
        (!this.isMobile && this.commonData.isPreview)
    },
    paramsInfoIconShow(element) {
      let selectedParamsInfoList = element?.[SBI_ATTR]?.selectedParamsInfo
      return selectedParamsInfoList?.length || this.projectName === ALL_PROJECT_NAME.EMS
    },
    getIsShowElementExport() {
      const exportType = this.elementExport?.exportType || []
      let isElementExport = !!exportType?.length
      if (!isElementExport) return isElementExport

      const isContainer = this.element.type === TYPE_ELEMENT.CONTAINER
      if (isContainer) {
        const containerElList = getContainerIncludeElsIdList(this.element)
        const elementList = this.elList.filter(e => containerElList.includes(e.id))
        const hasTable = elementList.some(el => isTableType(el))
        return hasTable && exportType?.length
      }

      const isTable = isTableType(this.element)
      if (!isTable) {
        isElementExport = !(exportType.includes(ELEMENT_EXPORT_TYPE.unFormatExport) && exportType?.length === 1)
      }
      return isElementExport
    },
  }
}
</script>
<style lang="scss" scoped>
  //noinspection CssUnknownTarget
  /deep/ .horizontal-scroll-list-wrap {
    width: 100%;
    text-align: right;
    .cube-scroll-content {
      display: inline-block;
    }
  }
  .list-wrapper {
    white-space: nowrap;

    .list-item {
      display: inline-block;
      border-radius: 4px;
    }
  }

  .shadow {
    transition: background 0.5s;
    opacity: 0.65;
    background: #000;
    @include sdp-tools();
    // color:#fff !important;
    &:after {
      content: '';
      position: absolute;
      bottom: -5px;
      height: 5px;
      left: 0;
      width: 100%;
      background-color: transparent;
    }
  }

  .theme-dark-mode {
    background-color: #07111D;
  }

  .root {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }

  .icon {
    font-size: 12px;
    /*margin-right: 16px;*/
    margin: 0 8px;
    cursor: pointer;
    color: #fff;
    // &:hover{
    //   color: #333;
    // }
  }

  .icon-more {
    margin: 0;
    padding: 1px 8px 0 0;
  }
  .daily-concerned-more {
    padding: 0 8px !important;
  }

  .clear-margin {
    margin-right: 0;
  }

  .icon-not-show-more {
    color: #87A4B6 !important;
  }

  .color {
    color: #fff !important;
  }

  .font14 {
    font-size: 14px;
  }

  .font24 {
    font-size: 20px;
  }

  .font-size-large {
    font-size: 20px;
  }

</style>

<style lang="scss"> // 加在全局的css上才能给popover加上样式
.time_popover {
  &.el-popper {
    background: #444;
    border-radius: 1px;
    font-size: 12px;
    color: #F7F7F7;
    line-height: 14px;
    padding: 5px 8px;
    text-align: center;
  }

  .popper__arrow {
    border-bottom-color: #444 !important;

    &::after {
      border-bottom-color: #444 !important;
    }
  }
}
</style>
