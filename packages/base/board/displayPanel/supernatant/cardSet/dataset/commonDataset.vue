<template>
  <div class="dataset-drag-wrap">
    <div class="dataset-drag-placeholder" :style="{ width: DATASET_MIN_WIDTH + 'px' }" v-if="style.position === 'absolute'"></div>
    <div class="dataset dataset-drag" :style="{
      position: style.position,
      zIndex: style.zIndex,
      width: style.width + 'px',
    }" v-show="isDisplayDataSet">
      <div class="dataset-drag-bar" @mousedown="drag"></div>
      <div class="dataset-header">
        <span class="dataset-header-left">{{$t('sdp.views.dataSet')}}</span>
        <div class="dataset-header-right">
          <i
            v-if="configs && configs.type === 'template'"
            class="icon-sdp-yinyongshujuji"
            @click="openLinkDatasetDialog"
            ></i>
        </div>
      </div>
      <!-- 拖拽字段 -->
      <!-- style="height: calc(100% - 20px); padding-top: 8px;"> -->
      <div class="dataset-item-container" style="height: 100%; padding-top: 8px;">
        <!-- style="max-height: 150px; overflow-y: auto; flex-shrink: 0;"  -->
        <div style="overflow-y: auto; flex-shrink: 0;" :style="{ height: dragParam.topHeight + 'px' }">
          <div
            class="dataset-item"
            :class="{
              'node-disable': datasetItem.enableFlag === '0',
              'active': selectedDataset.id === datasetItem.id,
              'dataset-high-light': datasetItem.highLight,
            }"
            v-for="(datasetItem, dsIndex) in datasetList" :key="dsIndex"
            @click="datasetClick(datasetItem)"
          >
            <span class="dataset-name" :title="getDatasetLabel(datasetItem)">
              <!-- icon-sdp-paishengzhibiao icon-sdp-fuhezhibiao-->
              <i  :class="isSim ? {
                1: 'icon-sdp-paishengzhibiao',
                2: 'icon-sdp-fuhezhibiao',
                0: 'icon-sdp-yuanzizhibiao'
              }[datasetItem.indexType]
               : (datasetItem.indexType=== 2 ? 'icon-sdp-paishengzhibiao':'icon-sdp-fuhezhibiao')"  style="font-size:16px;color:var(--sdp-zs);vertical-align: bottom;margin-right: 5px;" v-if="datasetItem.indexFlag"></i>
                {{ getDatasetLabel(datasetItem) }}
            </span>
            <div class="dataset-oprate">
              <i class="icon-sdp-yulan" :class="{ 'node-disable': datasetItem.enableFlag === '0' }" @click="e => openDataDetailPreview(e, datasetItem)"></i>
              <i v-if="configs && configs.type === 'template'" class="el-icon-delete-solid" @click.stop="handleDeleteDataset(datasetItem)"></i>
            </div>
          </div>
        </div>
        <DatasetContent v-if="selectedDataset && selectedDataset.id" ref="DatasetContent" :highLightItem="selectedDataset" :needDrag="true" elementType="3" :dragParam="dragParam"></DatasetContent>
      </div>
    </div>
    <!-- 隐藏数据集按钮 -->
    <div :class="isDisplayStyle" @click="changeDataSetDisplay">
      <i :class="displayIcon"></i>
    </div>
    <component
      :is="componentId"
      @closeDia="closeDia"
      :dialogVisible.sync="datasetDialogVisible"
      :visible.sync="datasetDialogVisible"
      @updateDaSetIds="e => getSelectionsList(e, true)"
      :dataSets="dataSets"
    ></component>
    <DataSetPreview
      :dataSets="dataSets"
      :visible.sync="dialogVisible"
    />
    <DataSetIndicatorPreview
      :dataSets="dataSets"
      :visible.sync="dataSetIndicatorPreviewShow"
    />
  </div>
</template>

<script>
import { substring15 } from 'packages/base/board/displayPanel/utils'
import { getDataSetTreeByIds } from 'packages/base/board/mixins/api'
import ReferenceDataSet from 'packages/base/common/referenceDataSet'
import { TAGNEWCARD } from 'packages/assets/constant'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import DataSetPreview from 'packages/base/common/dataSet/dialogComponents/dataSetPreview'
import DataSetIndicatorPreview from 'packages/base/common/dataSet/dialogComponents/dataSetIndicatorPreview'
import DatasetContent from 'packages/base/common/dataSet/datasetContent.vue'
import { kanbanGuideStepEntryAddDatasetsMixin } from "packages/base/KanbanGuide";
import { SIM_TYPE } from 'packages/base/common/referenceDataSet/constants'
import { EVENT_BUS, EXTERNAL_CALL_TYPE } from 'packages/base/board/displayPanel/constants'

const DATASET_MIN_WIDTH = 232
export default {
  inject: {
    configs: { default: () => false },
    getDictAlias: { default: () => () => {} },
    utils: { default: () => () => {} },
    sdpBus: { default: () => () => {} },
  },
  mixins: [kanbanGuideStepEntryAddDatasetsMixin, datasetMixin],
  components: { ReferenceDataSet, DataSetPreview, DataSetIndicatorPreview, DatasetContent },
  props: {
    datasetArray: {
      type: Array,
      default: () => [],
    },
    element: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    displayIcon() {
      return this.isDisplayDataSet ? 'el-icon-arrow-left' : 'el-icon-arrow-right'
    },
    isDisplayStyle() {
      return ['displayIcon', this.isDisplayDataSet ? 'displayDataSet' : 'hiddenDataSet']
    },
    datasetList() {
      if (this.configs?.type === 'template') {
        return this.datasetRes || []
      }
      return this.datasetArray || []
    },
    dataSets() {
      return {
        type: 'card' + ((this.configs && this.configs.type && ('-' + this.configs.type)) || ''),
        datasetList: this.datasetList,
        selection: 'multiple',
        activeObj: null,
      }
    },
  },
  data() {
    return {
      isSim: SIM_TYPE.isSim,
      isDisplayDataSet: true,
      datasetDialogVisible: false,
      componentId: '',
      datasetRes: [],
      dialogVisible: false,
      dataSetIndicatorPreviewShow:false,
      selectedDataset: {},
      style: {
        width: DATASET_MIN_WIDTH,
        zIndex: 2023, // 高于加载层 2000 低于选择器层 2048 今年 2023 那就用 2023 吧
        position: 'relative'
      },
      DATASET_MIN_WIDTH,

      dragParam: {
        topHeight: 100,
        middleHeight: 200,
        bottomHeight: 200,
        startY: 0,
        startTopHeight: 0,
        startMiddleHeight: 0,
        startBottomHeight: 0,
        resizing: null,
        topMinHeight: 60,
        middleMinHeight: 100
      }
    }
  },
  mounted() {
    this.selectedDataset = this.datasetList[0] || {}
  },
  methods: {
    substring15,
    resetWidth() {
      this.style.width = DATASET_MIN_WIDTH
    },
    drag (e) {
      this.style.position = 'relative' // 改为改变宽度影响右边，如果需要浮起改变宽度不影响右边，这里改成 'absolute' 即可
      const left = e.target.parentNode
      const leftW = left.offsetWidth
      const startX = e.clientX
      const _this = this
      document.onmousemove = (e) => {
        this.closeDurativeWithAddDatasets()
        e.preventDefault()
        const distX = e.clientX - startX
        _this.style.width = leftW + distX
        if (_this.style.width <= DATASET_MIN_WIDTH) {
          _this.style.width = DATASET_MIN_WIDTH
        }
        if (_this.style.width >= 600) {
          _this.style.width = 600
        }
      }
      document.onmouseup = () => {
        if (_this.style.width <= DATASET_MIN_WIDTH) {
          this.style.position = 'relative'
        }
        document.onmousemove = null
      }
    },
    handleDeleteDataset(data) {
      let datasetList = []
      const cardType = this.element.content.tagNewCardContent
      const keys = cardType === TAGNEWCARD.RATECARD ? ['dimension', 'ratioValue', 'compareValue'] : cardType === TAGNEWCARD.RANKCARD ? ['dimension', 'rankValue'] : ['dimension', 'metrics']
      this.element.content.optionArray.forEach(opt => {
        keys.forEach(k => {
          datasetList.push(...(opt[k] || []).map(d => d.parentId))
        })
      })
      datasetList = Array.from(new Set(datasetList))
      const deleteDisabled = datasetList.includes(data.id)

      const beforeTxt = this.$t('sdp.message.whetherTo')
      const index = beforeTxt.length - 1
      const deleteMsg = `${beforeTxt.slice(0, index)} ${data.labeName}${beforeTxt.slice(index)}`
      this.$sdp_eng_confirm(`${ deleteDisabled ? this.$t('sdp.message.D_removeDataset') : deleteMsg }`, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
        // customClass: 'sdp-grid-design',
        confirmButtonText: this.$t('sdp.message.confirmButton'),
        cancelButtonText: this.$t('sdp.message.cancelButton'),
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        showConfirmButton: !deleteDisabled,
        customClass: 'sdp-dialog',
        type: 'warning',
        closeOnClickModal: false,
        beforeClose: (action, instance, done) => { // bug 13203 表格编辑已经关闭，但是此弹窗还是会存在 延迟半秒才消失，和看板退出做同样处理
          instance.$el.style.zIndex = -1
          done()
        },
      }).then(() => {
        this.datasetRes = this.datasetRes.filter(d => d.id !== data.id)
        this.element.content.drillSettings.dataSetId = this.datasetRes.map(d => d.id)
        this.$emit('updateDataset', this.datasetList)
        this.datasetClick({})
      }).catch(() => {})
    },
    getDatasetLabel(propItem, labelTrim = 1, commentTrim = 0) {
      // 0表示不用显示，1表示要显示全名，2表示显示钱15个字符
      let label = labelTrim > 0 ? this.getUnknownName(propItem.parentId, propItem.labeName) : ''
      if (labelTrim > 1) label = substring15(label)
      let comment = commentTrim > 1 ? substring15(propItem.comment) : propItem.comment
      if (commentTrim > 0 && comment) label = `${ label } (${ comment })`
      return label || ''
    },
    getPropIcon(propItem) {
      if (propItem.columnTpe === 'number') return 'icon-sdp-Numerical'
      if (propItem.columnTpe === 'date') return 'icon-sdp-Calendar'
      return 'icon-sdp-Fonttype'
    },
    datasetClick(datasetItem) {
      if (datasetItem.enableFlag === '0' || this.selectedDataset.id === datasetItem.id) return
      this.selectedDataset = datasetItem
      // hbw
      this.resetHeights()
      this.$refs.DatasetContent && this.$refs.DatasetContent.reset(datasetItem)
      this.$nextTick(() => {
        this.$refs.DatasetContent && this.$refs.DatasetContent.updateHeights()
      })
    },
    resetHeights() {
      this.dragParam= {
        topHeight: 100,
        middleHeight: 200,
        bottomHeight: 200,
        startY: 0,
        startTopHeight: 0,
        startMiddleHeight: 0,
        startBottomHeight: 0,
        resizing: null,
        topMinHeight: 60,
        middleMinHeight: 100
      }
    },
    // 展开 / 折叠 数据集数据，一次只能展开一个数据集
    async openChidrenDataList(datasetId, datasetItem) {
      this.datasetList.forEach(d => {
        this.$set(d, 'open', d.id === datasetId ? !d.open : false)
      })
      this.datasetClick(datasetItem)
    },
    // 数据集具体数据查询
    openDataDetailPreview (event, data) {
      if (data.enableFlag === '0') return void ''

      Object.assign(this.dataSets, { 'activeObj': data })
      if (data.indexFlag) {
        if (SIM_TYPE.isSim) {
          this.sdpBus.$emit(EVENT_BUS.EXTERNAL_CALL, {
            type: EXTERNAL_CALL_TYPE.INDICATOR_RREVIEW,
            data: {
              id: data.indexId
            }
          })
        } else {
          this.dataSetIndicatorPreviewShow = true
        }
      } else {
        this.dialogVisible = true
      }
    },
    changeDataSetDisplay() {
      this.isDisplayDataSet = !this.isDisplayDataSet
    },
    // 打开引用数据集弹窗
    openLinkDatasetDialog () {
      if (this.datasetLoading) return
      this.$set(this, 'datasetDialogVisible', true)
      this.componentId = ReferenceDataSet
    },
    // 关闭弹窗
    closeDia () {
      this.$set(this, 'datasetDialogVisible', false)
      this.componentId = ''
    },
    // MARK:数据集id变动
    async getSelectionsList(datasetId = [], needset = false) {
      const params = {
        ids: datasetId,
        tenantId: this.utils.tenantId,
      }
      this.datasetLoading = true
      if (this.dataSets.type === 'card-template') {
        params.ids = datasetId.concat(this.dataSets.datasetList.map(d => d.id))
      }
      if (needset) {
        this.element.content.drillSettings.dataSetId = params.ids
      }

      const res = await getDataSetTreeByIds(this.utils.api, params, this.aliasDict)
      res[0] && (res[0].open = true)
      if (res[0]) {
        res[0].open = true
        this.selectedDataset = res[0] || {}
      }
      this.datasetRes = res || []
      this.$emit('updateDataset', this.datasetList)

      if (this.datasetLoading) {
        this.datasetLoading = false
      }

      return res || []
    },
  },
}
</script>

<style lang="scss" scoped>
.dataset-drag-wrap {
  position: relative;
}
.dataset-drag-placeholder {
  height: 0;
}
.dataset-drag {
  .dataset-drag-bar {
    content: '';
    width: 10px;
    height: 100%;
    position: absolute;
    top: 0;
    right: -10px;
    bottom: 0;
    cursor: col-resize;
  }
}
.dataset-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 700;
  line-height: 20px;
  height: 20px;
  .dataset-header-right{
    i {
      cursor: pointer;
      font-weight: normal;
      color: var(--sdp-is);
      &:hover {
        color: var(--sdp-zs);
      }
    }
  }
}
.dataset-item-container{
  display: flex;
  flex-direction: column;
  .node-disable{
    cursor: not-allowed;
    color: var(--dataSetNodeDisabledFont, 'zx-roc-0.86071427991816');
  }
}
.dataset-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px 0 12px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  font-size: 12px;
  color: var(--sdp-cszjsz-wzs1);
  &:hover, &.active{
    background-color: var(--sdp-sjj-hs);
    color: var(--sdp-zs);
  }
  .dataset-name{
    @include ellipsis;
    font-weight: bold;
  }
  .dataset-oprate{
    flex-shrink: 0;
    height: 100%;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i{
      color: $color-main;
      font-size: 12px;
    }
  }
}
.dataset {
  height: 100%;
  width: 232px;
  background: #F7F7F7;
  padding: 16px;
  box-shadow: 2px 2px 2px 0 rgba(0,0,0,0.10);
  color: var(--sdp-cszjsz-wzs1);
  background-color: var(--sdp-zcsjj-bj);
  /deep/ .draggableBox {
    font-size: 12px;
    display: inline-block;
    max-height: 100%;
    min-height: 30px;
    padding-left: 10px;
    overflow: auto;
    overflow-x: hidden;
    .node-disable{
      color: var(--dataSetNodeDisabledFont, 'zx-roc-0.86071427991816');
    }
  }
  .select-data {
    margin-top: 6px;
    margin-bottom: 16px;
  }
}
.fieldListStyle {
  background-color: var(--sdp-ycsz-srk-bgs);
  color: var(--chartsetDatasetColor, 'zx-roc-0.022737684305925043');
  border-color: var(--chartsetInputInnerBorderColor, 'zx-roc-0.6162363179402666');
  opacity: 0.9;
  box-shadow: 1px 1px 4px 0 rgba(231, 214, 214, 0.1);
  border-radius: 2px;
  margin-top: 10px;
  width: 200px;
  height: 100%;
  font-family: PingFang-SC-Regular;
  font-size: 12px;
  overflow-y: auto;
  .data-list-area {
    margin-top: 10px;
    .data-list-name {
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 10px;
      &.node-disable{
        color: var(--dataSetNodeDisabledFont, 'zx-roc-0.86071427991816');
      }
    }
  }
  .icon-sdp-yulan{
    color: $color-main;
    font-size: 12px;
    cursor: pointer;
  }
  .showBox {
    cursor: pointer;
    border: 0;
    min-width: 16px;
    width: 100%;
    //margin:1px;防止拖拽出现其他元素的边框
    display: flex;
    align-items: center;
    margin-top: 10px;
    & > span {
      // width: calc(100% - 18px);
      display: inline-block;
    }
  }
  .ml-5 {
    margin-left: 5px;
  }
  .treeIcon {
    font-size: 12px;
    margin-right: 3px;
  }
}
.displayIcon{
  height: 64px;
  box-shadow: -2px 6px 6px 0 rgba(0, 0, 0, 0.1);
  background-color: var(--sdp-cy-bjs);
  position: absolute;
  top: 50%;
  cursor: pointer;
  i {
    line-height: 64px;
  }
}
.el-icon-delete-solid{
  margin-left: 12px;
  cursor: pointer;
}
.el-icon-delete-solid::before {
  color: $color-main;
  color: var(--dataSetTreeParentDeleteIcon, 'zx-roc-0.86071427991816');
}
.displayDataSet {
  width: 18px;
  left: 216px;
}
.hiddenDataSet {
  width: 16px;
  z-index: 100;
}
.dataset-high-light {
  color: $color-main !important;
}
</style>
