<template>
  <div class="dragBox">
    <draggable
      :class="{'pd-lt':isHorizontal}"
      class="draggableBox"
      :value="dragList"
      @input="changeDragList($event)"
      :options="mergedDragOption"
      >
      <div
        v-for="(item,i) in dragList"
        :key="item.alias || i"
        :class="{ inlineBox: isHorizontal,showBox: !isHorizontal,'node-disable':!isHorizontal&&item.unavailable  }"
        @mouseover="highLightDataset(item.parentId)"
        @mouseout="removeHighLight()"
        >
        <!--:key="item[dragKey]"-->
        <i :class="elTreeIconfont.iconArr[i]" :title="item.columnTpe" class="treeIcon" v-if="!isHorizontal"></i>
        <slot :item="item">
          <span style=" white-space: nowrap;" v-if="!isHorizontal" :title="item.comment?`${item[dragLabel]} (${item.comment})` :item[dragLabel]">{{item.comment?`${item[dragLabel]}   (${substring15(item.comment)})` :item[dragLabel]}}</span>
          <span :class="elTreeIconfont.content[i] !== ''?'ml-5':''" :title="item[dragLabel]" v-if="!isHorizontal" style="white-space: nowrap;">{{elTreeIconfont.content[i] !== '' ?`(${elTreeIconfont.content[i]})` : ''}}</span>
          <drag-cascader
            :trimMaxWidth='trimMaxWidth'
            ref="dragCascader"
            :item="item"
            :index="i"
            isCard
            :type="type"
            :cardIndex="index"
            :dragList="dragList"
            :isRateCard="isRateCard"
            :cardType="cardType"
            :isIndex="isIndex"
            :isTarget="isTarget"
            :isRatioValue="isRatioValue"
            :isCompareValue="isCompareValue"
            :isRankValue="isRankValue"
            :current-edit-data="currentEditData"
            :metric-type="metricType"
            v-if="isHorizontal"
            @clear-status="data => $emit('clear-status', data)"
            @selectMess="dragCascaderSelect"
            @customsort = "customSort"
            @setGraphicalParam = "setGraphicalParam"
            @alias-change="aliasChange"
            @deleteLists="deleteLists(item[dragKey], i)"
          />
        </slot>
      </div>
    </draggable>
    <DialogSelectIndicator ref="refDialogSelectIndicator" @select="changeDragList"/>

  </div>
</template>

<script>
import { DragCascader } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { NO_METRIC_WARNING_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementTagNewCard/constant'
import { paramsBindDatasets } from 'packages/base/board/displayPanel/constants'
import { TAGNEWCARD } from 'packages/assets/constant'
import { substring15 } from '../../utils'
import { EVENT_BUS } from 'packages/base/board/displayPanel/constants'

import indicatorMixin from '../mixins/indicatorMixin'
export default {
  components: {
    DragCascader,
  },
  mixins:[indicatorMixin],
  props: {
    isHorizontal: {
      type: Boolean,
      default: false,
    },
    // 指标索引
    index: {
      type: Number,
    },
    dragOption: {
      type: Object,
      default: () => ({}),
    },
    dragKey: {
      type: String,
      default: 'id',
    },
    dragLabel: {
      type: String,
      default: 'name',
    },
    // 卡片类型
    cardType: {
      type: String,
      default: ''
    },
    // 卡片目标值
    isTarget: {
      type: Boolean,
      default: false,
    },
    // 卡片指标值
    isIndex: {
      type: Boolean,
      default: false,
    },
    // 比率值
    isRatioValue: {
      type: Boolean,
      default: false,
    },
    // 对比值
    isCompareValue: {
      type: Boolean,
      default: false,
    },
    isRankValue: {
      type: Boolean,
      default: false,
    },
    prevent: {
      type: Boolean,
      default: false,
    },
    drillSettings: {
      type: Object,
      default: () => ({}),
    },
    chartUserConfig: {
      default: () => ({}),
    },
    currentEditData: {
      type: Object,
      default: () => ({}),
    },
    // 最大宽度，超过这个会被截取
    trimMaxWidth: {
      type: Number,
      default: 0
    },
    metricType: {
      default: 'default',
    },
    datasetList: {
      type: Array,
    },
    // dimension: {
    //   type: Array,
    //   default: () => []
    // },
    // metric: {
    //   type: Array,
    //   default: () => []
    // },
    cardContent: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: '',
    },
  },
  inject: ['boardData', 'utils','sdpBus', 'chart'],

  data() {
    return {
      dragList: [],
      preListLength: 0,
      firstLeave: ''
    }
  },

  created() {
    this.onDragListChange = this.$_throttle(this.onDragListChange)
  },
  beforeDestroy() {
    this.sdpBus.$off(EVENT_BUS.DBLCLICK_INDICATOR)
  },
  computed: {
    mergedDragOption() {
      return this.$_deepMergeObjects({
        group: {
          name: 'people',
          // pull: 'clone',
          pull: true,
          put: true,
        },
        sort: true,
      }, this.dragOption)
    },
    elTreeIconfont() {
      const tempObj = { iconArr: [], content: [] }
      this.dragList.forEach(item => {
        let sdpiconfont = ''
        switch (item.columnTpe) {
          case 'string':
            sdpiconfont = 'icon-sdp-Fonttype'
            break
          case 'number':
            sdpiconfont = 'icon-sdp-Numerical'
            break
          case 'date':
            sdpiconfont = 'icon-sdp-Calendar'
            break
          default:
            sdpiconfont = 'icon-sdp-Fonttype'
            break
        }
        if (item.tagName) {
          tempObj.content.push(item.tagName)
        } else {
          tempObj.content.push('')
        }
        tempObj.iconArr.push(sdpiconfont)
      })
      return tempObj
    },
    defaultCascader: {
      get() {
        return this.currentEditData.content.chartUserConfig.defaultCascader || []
      },
      set(val) {
        this.$set(this.currentEditData.content.chartUserConfig, 'defaultCascader', val)
      }
    },
    isCompareCard() {
      return this.cardType === TAGNEWCARD.COMPARECARD
    },
    isRateCard() {
      return this.cardType === TAGNEWCARD.RATECARD
    },
    isRankCard() {
      return this.cardType === TAGNEWCARD.RANKCARD
    },
    isMobile() {
      return this.utils.isMobile
    },
    fieldList() {
      return this.chart.fieldList
    },
  },

  watch: {
    'dragList.length'(val, oldVal) {
      // 非数据集区域
      if (this.isHorizontal) {
        let message = ''
        if (this.isRateCard) {
          // 比率卡片支持多个指标值、单比率值、单对比值
          this.isIndex && this.dragList.length > 3 && (message = this.$t('sdp.views.rateCardDimensionLimit'))
          this.isRatioValue && this.dragList.length > 1 && (message = this.$t('sdp.views.ratioValueLimit'))
          this.isCompareValue && this.dragList.length > 1 && (message = this.$t('sdp.views.compareValueLimit'))
        } else if (this.isCompareCard && this.isCompareValue) {
          // 比较卡片支持多个对比值、单指标值
          // 8589: 限制最多能添加20个对比值
          // if (this.isMobile && this.dragList.length > 2) {
          //   message = this.$t('sdp.views.onlyTwoCompareValue')
          // } else if (this.dragList.length > 3) {
          //   message = this.$t('sdp.views.onlyThreeCompareValue')
          // }
          if (this.dragList.length > 20) {
            message = this.$t('sdp.views.onlyTwentyCompareValue')
          }
        } else if (this.isRankCard && this.isRankValue) {
          // 排名卡片支持多个维度值、单指标值
          if (this.dragList.length > 2) {
            message = this.$t('sdp.views.TheCardSupportsAtMostTwoDimensions')
          }
        } else if (this.dragList.length > 1) {
          // 单/双指标卡片支持单指标值、单目标值
          message = this.isIndex ? this.$t('sdp.views.cardOnlyOneIndex') : this.$t('sdp.views.cardOnlyOneTarget')
        }
        if (message) {
          this.$message({
            type: 'error',
            message: message,
          })
          this.dragList.pop()
        }
        // 指标值、比率值、对比值有变更，删除对应的预警
        let { warnLineSettingList = [], referenceValueList = [] } = this.chartUserConfig
        const optionArray = this.$_getProp(this.currentEditData, 'content.optionArray')
        if (this.isIndex || this.isRatioValue || this.isCompareValue) {
          const metricsList = this.getFieldAlias(['ratioValue', 'compareValue', 'dimension'])
          // 过滤掉匹配不到此别名的预警线数据
          warnLineSettingList.forEach((warnLineItem, i) => {
            const filterItem = this.$_JSONClone(warnLineItem.filter(line => metricsList.includes(line.metric) || NO_METRIC_WARNING_TYPE.includes(line.cardWarnLineType)))
            this.$set(this.chartUserConfig.warnLineSettingList, i, filterItem)
          })
        }

        // 指标值有变更，删除对应的预警
        if (this.isIndex) {
          let newLineList = []
          const dimensionList = this.getFieldAlias(['dimension']).map(alias => `ReferenceValue_${alias}`)
          this.$set(this.chartUserConfig, 'referenceValueList', this.$_JSONClone(referenceValueList.filter(item => dimensionList.includes(item.referenceValueKey))))
        }

        // 清空匹配不到数据集的过滤设置
        const drillSettings = this.$_getProp(this.currentEditData, 'content.drillSettings', {})
        if (Array.isArray(drillSettings.filters) && drillSettings.filters.length) {
          // 获取使用的数据集id
          let useIds = optionArray.map(item => (item.dataSetJoinsDataSetId || item.dataSetId)).filter(item => item)
          // 过滤匹配不到数据集的设置
          const newFilters = drillSettings.filters.filter(filterItem => useIds.includes(filterItem.dataSetId))
          this.$set(this.currentEditData.content.drillSettings, 'filters', this.$_JSONClone(newFilters))
        }
        // 检查卡片各字段是否来自同一数据集
        const keys = this.isRateCard ? ['dimension', 'ratioValue', 'compareValue'] : this.isCompareCard ? ['dimension', 'compareValue'] : this.isRankCard ? ['dimension', 'rankValue'] : ['dimension', 'metrics']
        const filedData = []
        keys.forEach(item => {
          this.cardContent[item] && filedData.push(...this.cardContent[item])
        })
        filedData.push(...this.dragList)
        const sameDataset = filedData.every(item => item.parentId === filedData[0].parentId)
        // 如果选中的字段都是结果数据集，则通过
        const associationDataset = filedData.every(item => item.webFieldFrom === "associationDataset")
        if (!sameDataset && !associationDataset) {
          this.$message.warning(this.$t('sdp.views.T_differentDataset'))
          this.dragList.pop()
        }
        // 流式模型与批处理模型不可混用
        let modelType = ''
        const dataSetIds = optionArray.map(item => item.dataSetId).concat(this.dragList.map(item => item.parentId))
        const differentModelType = dataSetIds.some(dataSetId => {
          let curDataset = this.datasetList.find(item => item.id === dataSetId)
          if (curDataset && !modelType) {
            modelType = curDataset.modelType
          }
          return curDataset && modelType && modelType !== curDataset.modelType
        })
        if (differentModelType) {
          this.$message.warning(this.$t('sdp.views.cardDifferentModelType'))
          this.dragList.pop()
        }
      }
      // 不可以放入相同的维度字段
      // this.dragList = this.$_removeRepetition(this.dragList, this.dragKey)
      if (this.prevent) { this.$emit('update:prevent', false) }
      this.onDragListChange()
    },
    "cardType":{
      handler(){
        this.sdpBus.$off(EVENT_BUS.DBLCLICK_INDICATOR)
        setTimeout(() => {
          this.sdpBus.$on(EVENT_BUS.DBLCLICK_INDICATOR, (current) => {
          console.log('卡片类型'+this.cardType);
          console.log('dragBox类型'+this.type);
            if(this.cardType==='rate' && this.type==='cardRatioValue'){
              // 是比率卡片的情况 自动填充到比率值
              this.dragList.push(current);
            } else if(this.type==='cardIndicator' && this.index===0 && this.cardType!='rate'){
              // 如果是指标dragBox 且是第一个指标 自动填充到指标值
              this.dragList.push(current);
            }
         })
        }, 400);
      },
      immediate:true
    }
  },

  methods: {
    substring15,
    deleteLists(id, index, item) {
      function delArr() {
        const content = this.currentEditData.content
        const index = this.dragList.findIndex(e => e[this.dragKey] === id)
        index > -1 && this.dragList.splice(index, 1)
        this.$nextTick(() => {
          let dataSetId = []
          const keys = this.isRateCard ? ['dimension', 'ratioValue', 'compareValue'] : this.isRankCard ? ['dimension', 'rankValue'] : ['dimension', 'metrics']
          Array.isArray(content.optionArray) && content.optionArray.forEach(opt => {
            keys.forEach(k => {
              dataSetId.push(...(opt[k] || []).map(d => d.parentId))
            })
          })
          dataSetId = Array.from(new Set(dataSetId))
          this.$set(content.drillSettings, 'dataSetId', dataSetId)
        })
      }
      if (this.isHorizontal) {
        if (!this.firstLeave) this.firstLeave = this.dragList[0].parentId
        const { metrics = [], dimension = [], compareValue = [], ratioValue = [], rankValue = [] } = this.cardContent
        // 当前卡片所有字段长度
        let filedLength = metrics.length + dimension.length
        switch (this.cardType) {
          case TAGNEWCARD.COMPARECARD:
            filedLength = dimension.length + compareValue.length
            break
          case TAGNEWCARD.RANKCARD:
            filedLength = dimension.length + rankValue.length
            break
          case TAGNEWCARD.RATECARD:
            filedLength = dimension.length + compareValue.length + ratioValue.length
            break
        }
        if (filedLength === 1) {
          let dataSets = []
          this.boardData.paramsPanelList.map(v => {
            if (v.content && v.content.length > 0) {
              v.content.map(i => {
                dataSets.push(...paramsBindDatasets(i, this.currentEditData.id).map(d => d.datasetId))
              })
            }
          })
          dataSets = Array.from(new Set(dataSets))
          if (dataSets.includes(this.dragList[0].parentId) && this.firstLeave === this.dragList[0].parentId) {
            this.$sdp_eng_confirm(`${this.$t('sdp.views.confirmChangeDataSet')}`, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
              confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
              cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
              cancelButtonClass: 'el-button--sdp-cancel',
              confirmButtonClass: 'el-button--sdp-ensure',
              customClass: 'sdp-dialog',
              type: 'warning',
            }).then(() => {
              delArr.call(this)
            }).catch(() => {
            })
          } else {
            delArr.call(this)
          }
        } else {
          delArr.call(this)
        }
      }
    },
    clearList() {
      this.dragList = []
    },
    setList(list) {
      // 不是卡片对比值则需要移除 sdpHideAlias 属性
      if (!(this.type === 'cardCompareValue' && this.isCompareValue)) {
        list.forEach(item => {
          this.$delete(item, 'sdpHideAlias')
        })
      }
      this.dragList = list
    },
    getList() {
      return this.dragList
    },
    // 选中级联菜单的某一项的事件
    dragCascaderSelect({ selectValue, selectItem, selectList }) {
      const { dateDimension, SORT } = selectValue
      this.$emit('on-change', selectList)
    },
    setGraphicalParam(Param) {
      console.log('Param', Param)
      this.$emit('on-change-graphical', Param)
    },
    aliasChange() {
      if (this.prevent) { this.$emit('update:prevent', false) }
      this.$emit('on-change', this.dragList)
    },
    clearStatus(data) {
      const dragCascaderComp = this.$refs.dragCascader
      dragCascaderComp.forEach(comp => {
        comp.clearStatus(data)
      })
    },
    initCascader(data) {
      const dragCascaderComp = this.$refs.dragCascader
      dragCascaderComp.forEach(comp => {
        comp.initCascader(data)
      })
    },
    changeCascader(method, data) {
      const dragCascaderComp = this.$refs.dragCascader
      dragCascaderComp && dragCascaderComp.forEach(comp => {
        comp[method](data)
      })
    },
    // 自定义排序
    customSort(orderList) {
      this.$emit('on-customsort', orderList)
    },
    // 当列表长度发生变化的时候触发
    onDragListChange() {
      if (this.preListLength !== this.dragList.length) {
        // 拖入对比值字段时在drag-cascader组件中会设置默认值，触发dragCascaderSelect方法，故此处不需要请求接口，避免重复请求
        const notRefresh = this.preListLength < this.dragList.length && ['cardCompareValue'].includes(this.type)
        if (!(this.type === 'cardCompareValue' && this.isCompareValue)) {
          this.dragList.forEach(item => {
            this.$delete(item, 'sdpHideAlias')
          })
        }
        this.$emit('on-change', this.dragList, !notRefresh)
        this.preListLength = this.dragList.length
      }
    },
    // 鼠标停在维度、度量字段上，对应数据集高亮
    highLightDataset(datasetId) {
      if (!this.isHorizontal) return
      this.datasetList.map(item => {
        item.id === datasetId && this.$set(item, 'highLight', true)
      })
    },
    removeHighLight() {
      if (!this.isHorizontal) return
      this.datasetList.map(item => {
        this.$set(item, 'highLight', false)
      })
    },
    changeDragList(dragList) {
      console.log('%c [ dragList ]-466', 'font-size:13px; background:#42f21e; color:#86ff62;', dragList)
      // hbw
      if (!this.isHorizontal) return

      // 如果拖拽的是指标数据
      // let hasIndicator = dragList.find(item => item.isIndicator);
      // if (hasIndicator) {
      //   let index = dragList.findIndex(item => item.isIndicator);
      //   // 如果拖拽的是维度行，则需要弹出弹窗让用户选择维度
      //   if (this.isRankCard && this.isRankValue) {
      //     let options = hasIndicator.children.filter(item => {
      //       return item.columnTpe === 'string'
      //     });

      //     // 将指标拖过来后dragList.length === 1 ,但是当前未选择其他维度，所以需要-1
      //     if(options.length > 1){
      //       this.$refs.refDialogSelectIndicator.show(options, dragList.length ? dragList.length - 1 : 0, 2, dragList);
      //       return
      //     }else{
      //       // 待验证
      //       // 选第一个指标
      //       if(index>=dragList.length){
      //         dragList.push(hasIndicator.children[0]);
      //       }else{
      //         dragList.splice(index, 0,hasIndicator.children[0]);
      //       }
      //     }

      //   } else {
      //     // let index = dragList.findIndex(item => item.isIndicator);
      //      // 不管拖拽到哪一个，只填充度量到拖拽的那一行,在此处找出度量的数据
      //     let current = hasIndicator.children.find(item => item.columnTpe === 'number');
      //     // 删除指标整个的数据，并塞入度量数据
      //     dragList.splice(index, 1, current);
      //   }
      // }
      let list = dragList
      let isDimension = this.isRankCard && this.isRankValue
      let otherParam = {
        drillSettings: this.drillSettings || {}
      }
      list =  this.indicatorDragAfter(dragList,isDimension,1, this.fieldList, otherParam)
      list!=='clogExecute' &&  this.dragList.splice(0, this.dragList.length, ...this.$_deepClone(list))
    },
    // 获取卡片使用字段别名
    getFieldAlias(keys, dataSetId) {
      const optionArray = this.$_getProp(this.currentEditData, 'content.optionArray')
      if (!keys) {
        keys = this.isRateCard ? ['dimension', 'ratioValue', 'compareValue'] : this.isRankCard ? ['dimension', 'rankValue'] : ['dimension', 'metrics']
      }
      // 获取全部字段别名
      let metricsList = []
      Array.isArray(optionArray) && optionArray.forEach(cardContent => {
        keys.forEach(key => {
          if ((!dataSetId || cardContent.dataSetId === dataSetId) && cardContent[key]) {
            metricsList.push(...cardContent[key].map(e => e.alias || e.labeName))
          }
        })
      })
      return metricsList
    }
  },
}
</script>

<style   lang="scss">
.pd-lt {
      .svg-icon-tree-before{
          opacity: 0!important;
      }
}
</style>

<style lang="scss" scoped>
  .dragBox{
    min-width: 104px;
    max-width: 100%;
    .draggableBox{
      display: inline-block;
      max-height: 100%;
      min-height: 30px;
      padding-left: 10px;
      overflow: auto;
      overflow-x: hidden;
      // /deep/ .dragCascader{
      //   margin-top: 6px
      // }
    }
    .pd-lt {
      // padding-right: 76px;
    }
  }
  .showBox{
    cursor: pointer;
    border: 0;
    min-width: 16px;
    //margin:1px;防止拖拽出现其他元素的边框
    margin-top: 10px;
  }
  .inlineBox{
    cursor: pointer;
    border: 0;
    min-width: 16px;
    display: inline-block;
    // height: 100%;
    margin-right: 7px;
    margin-top: 7px;
    //margin: 1px;防止拖拽出现其他元素的边框
  }
  .treeIcon {
    font-size: 12px;
    margin-right: 3px;
  }
  .ml-5 {
    margin-left: 5px;
  }
</style>
