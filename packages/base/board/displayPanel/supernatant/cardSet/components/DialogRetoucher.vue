<template>
  <DialogRoot
    :title=" !!title ? title : $t('sdp.views.decorativeIndicatorIcon')"
    :visible.sync="dialogVisible"
    width="660px"
    @open="initData"
    :confirmDisabled="uploadStartId !== uploadEndId"
    @confirm="confirm"
    @close="closeDialog"
  >
    <div class="dialog-retoucher-container">
      <el-tabs v-model="retoucherOptionClone.type" type="card">
        <el-tab-pane name="system">
          <ItemLabel
            slot="label"
            :label="$t('sdp.views.systemIcon')"
            :class="{ selected: retoucherOptionClone.type === 'system' }"
            :level="2"
          ></ItemLabel>
          <div class="datarefresh-icon-container">
            <ImageBox
              v-for="(item, iconIndex) in systemIconMaterialList"
              :key="iconIndex"
              :selectedData="{ id: retoucherOptionClone.pictureId }"
              :showSelectedCheck="true"
              :imageData="item"
              imageType="icon"
              displayType="display"
              @item-click="e => pictureClickHandler(e.id, e.url)"
            ></ImageBox>
          </div>
        </el-tab-pane>
        <el-tab-pane name="custom" v-if="!$getFeatureConfig || !$getFeatureConfig('warningCustomIcon.hidden')">
          <ItemLabel
            slot="label"
            :label="$t('sdp.views.customIcon')"
            :class="{ selected: retoucherOptionClone.type === 'custom' }"
            :level="2"
          ></ItemLabel>

          <div
            class="datarefresh-icon-container"
            @scroll="containerScrollHandler"
            ref="iconContainer"
          >
            <div
              :class="['upload-box', saveAsMaterialShow ? 'checkboxShow' : '']"
            >
              <div
                :class="[
                  'datarefresh-icon-box',
                  retoucherOptionClone.pictureId &&
                  retoucherOptionClone.pictureId ===
                    retoucherOptionClone.uploadIconId
                    ? 'selected'
                    : ''
                ]"
              >
                <ImageBox
                  :selectedData="{ id: retoucherOptionClone.pictureId }"
                  :imageData="{
                    id: null,
                    url: null
                  }"
                  imageType="icon"
                  displayType="uploader"
                  :showSelectedCheck="true"
                  @uploadStatus="handGetUploadStatus"
                  @item-click="e => pictureClickHandler(e.id, e.url)"
                  @uploaded="pictureUploadedHandler"
                >
                  <i
                    class="icon-sdp-xinzengjiahao"
                  ></i>
                </ImageBox>
              </div>
              <!-- <el-checkbox v-model="isSaveAsMaterial">{{
                $t('sdp.views.saveAsMaterial')
              }}</el-checkbox> -->
            </div>
            <!-- 用户上传的 -->
            <div
              v-for="(item, uploadIndex) in uploadList"
              :key="item.uploadIconId"
              class="upload-list-content custom-upload-box"
              :class="[
                'datarefresh-icon-box',
                retoucherOptionClone.pictureId &&
                retoucherOptionClone.pictureId === item.uploadIconId
                  ? 'selected'
                  : ''
              ]"
            >
              <ImageBox
                :imageData="{
                  id: item.uploadIconId,
                  url: item.uploadIconUrl
                }"
                :selectedData="{ id: retoucherOptionClone.pictureId }"
                :showSelectedCheck="true"
                imageType="icon"
                displayType="uploader"
                @item-click="e => customPictureClickHandler(e.id, e.url, item)"
                @uploaded="e => pictureUploadedHandler(e, item)"
              >
                <div class="upload-mask">
                  <i
                    slot="trigger"
                    class="icon-sdp-zhibiaoxuanzeqibianji"
                    @click="handleEdit(item, uploadIndex)"
                  ></i>
                  <i
                    class="el-icon-delete"
                    @click.stop="handleRemove(item)"
                  ></i>
                </div>
              </ImageBox>
              <el-checkbox v-model="item.isSaveAsMaterial">{{
                $t('sdp.views.saveAsMaterial')
              }}</el-checkbox>
            </div>
            <ImageBox
              v-for="(item, iconIndex) in iconMaterialList"
              :key="iconIndex"
              :selectedData="{ id: retoucherOptionClone.pictureId }"
              :showSelectedCheck="true"
              :imageData="item"
              imageType="icon"
              displayType="display"
              @item-click="e => pictureClickHandler(e.id, e.url)"
            ></ImageBox>
            <!-- :style="saveAsMaterialShow && (iconIndex < 5) ? 'margin-bottom: 58px;' : ''" -->
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </DialogRoot>
</template>

<script>
import { ItemLabel } from 'packages/base/board/displayPanel/supernatant/chartSet/common';

import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog';
import ImageBox from 'packages/base/board/displayPanel/supernatant/chartSet/components/image-box';
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin';
import {
  getImageMaterialList,
  saveAsImageMaterial,
  saveAsImageMaterialList,
} from 'packages/base/board/displayPanel/supernatant/api';
import { STATIC_BASE_PATH } from 'packages/assets/constant';
import { OPERATION_LOG } from 'packages/base/board/displayPanel/boardLanguage';
import { operationalLog } from 'packages/base/board/displayPanel/api';
export default {
  inject: {
    utils: { default: {} },
    chart: { default: {} },
    getUnknownName: { default: () => (a, labeName) => labeName }
  },
  mixins: [mixin_dialog],
  props: {
    retoucherOption: {
      type: Object,
      default: {}
    },
    userUploadList: {
      type: Array,
      default: () => ([])
    },
    boardInfo: {
      type: Object,
      default: () => ({})
    },
    item: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: null
    }
  },
  components: { ItemLabel, ImageBox },
  mounted() {},
  computed: {
    api() {
      return this.utils.api || function() {}
    },
    saveAsMaterialShow() {
      if (!this.dialogVisible) return false
      const projectName = this.utils.env?.projectName || ALL_PROJECT_NAME.OMS
      return (
        projectName === ALL_PROJECT_NAME.OMS &&
        !!this.retoucherOptionClone.uploadIconId
      )
    },
    folderName() {
      // 获取主题色的文件夹名称
      let words = this.themeType.split('-')
      words.shift() // 去除最后两个单词、
      let camelCase = words
        .map((word, index) => {
          if (index === 0) {
            return word
          } else {
            return word.charAt(0).toUpperCase() + word.slice(1)
          }
        })
        .join('')
      return camelCase
    },
    systemIconMaterialList() {
      let res = this.systemImg.map((i, index) => {
        return {
          id: `${this.folderName}${i}${index}`,
          url: `${STATIC_BASE_PATH.images}board/retoucher/${this.folderName}/${i}`
        }
      })
      return res
    }
  },

  data() {
    return {
      systemImg: ['money.png', 'money1.png', 'home.png', 'rank.png'],
      iconMaterialList: [], // 自定义图片
      uploadList: [],
      paginationData: {
        limit: 40,
        page: 1,
        status: '1'
      },
      listTotal: 0,
      uploadStartId: 'success',
      uploadEndId: 'success',
      // isSaveAsMaterial: false,
      listLoading: true,
      confirming: false,
      retoucherOptionClone: {},
      editItem: null,
      editIndex: null
    }
  },

  methods: {
    initData() {
      // this.isSaveAsMaterial = false;
      this.iconMaterialList = []
      this.uploadList = []
      this.getIconMaterialList()
      this.retoucherOptionClone = this.$_deepClone(this.retoucherOption)
      // if (this.retoucherOption && this.retoucherOption.uploadIconName) {
      //   this.uploadList.push(this.retoucherOption)
      // }
      if (this.userUploadList?.length > 0) {
        let isSaveAsMaterial = this.userUploadList.filter(i => !i.isSaveAsMaterial)
        isSaveAsMaterial.length > 0 && (this.uploadList = isSaveAsMaterial)
      }
      // if (this?.userUploadList.length > 0) {
      //   let list = this.userUploadList.map(i => {
      //     return {
      //       ...i,
      //       isSaveAsMaterial: false
      //     }
      //   }
      //   )
      //   this.uploadList = list
      // }
    },
    pictureClickHandler(id, url) {
      if (id === this.retoucherOptionClone.pictureId) {
        this.retoucherOptionClone.pictureId = ''
        this.retoucherOptionClone.pictureUrl = ''
      } else if (id && url) {
        this.retoucherOptionClone.pictureUrl = url
        this.retoucherOptionClone.pictureId = id
      }
    },
    customPictureClickHandler(id, url, item) {
      if (id === this.retoucherOptionClone.pictureId) {
        this.retoucherOptionClone.pictureId = ''
        this.retoucherOptionClone.pictureUrl = ''
      } else if (id && url) {
        this.retoucherOptionClone.pictureUrl = url
        this.retoucherOptionClone.pictureId = id
      }
    },
    async confirm() {
      if (this.confirming) return // 正在保存为素材
      // if (this.retoucherOptionClone.uploadIconId && this.isSaveAsMaterial) {
      //   this.confirming = true;
      //   const apiParams = {
      //     id: this.retoucherOptionClone.uploadIconId,
      //     type: '3',
      //     width: this.retoucherOptionClone.uploadIconWidth,
      //     height: this.retoucherOptionClone.uploadIconHeight,
      //     logoName: this.retoucherOptionClone.uploadIconName
      //   };
      //   saveAsImageMaterial(this.utils.api, apiParams)
      //     .then(res => {
      //       callbackFn.call(this);
      //     })
      //     .finally(() => {
      //       this.confirming = false;
      //     });
      //   return;
      // }

      if (this.uploadList.length > 0) {
        let list = this.uploadList.filter(item => item.isSaveAsMaterial)
        if (list.length > 0) {
          let batchBgp = list.map(item => {
            const apiParams = {
                id: item.uploadIconId,
                type: '3',
                width: item.uploadIconWidth,
                height: item.uploadIconHeight,
                logoName: item.uploadIconName
            }
            return apiParams
          })
          saveAsImageMaterialList(this.utils.api, { batchBgp: batchBgp })
          .then(res => {
            let uploadList = this.uploadList.filter(i => i.isSaveAsMaterial)
            if (uploadList.length > 0) {
              let currentIndex = uploadList.findIndex(i => i.uploadIconId === this.retoucherOptionClone.pictureId)
              if (currentIndex >= 0) {
              this.retoucherOptionClone.pictureId = res[currentIndex]
              }
            }
            callbackFn.call(this)
          })
          .finally(() => {
            this.confirming = false
          })

          return
        }
      }

      callbackFn.call(this)

      function callbackFn() {
        // 上传日志
        this.uploadImgChange()
        // this.retoucherOptionClone.isSaveAsMaterial = this.isSaveAsMaterial;
        this.dialogVisible = false
        this.$emit('confirm', this.retoucherOptionClone, this.item, this.uploadList)
      }
    },
    async getIconMaterialList(val) {
      this.listLoading = true
      const res = await getImageMaterialList(this.utils.api, {
        imgType: '3',
        ...this.paginationData
      })
      this.listLoading = false
      if (!res) return
      this.listTotal = res.total || 0
      const startIndex =
        (this.paginationData.page - 1) * this.paginationData.limit
      this.iconMaterialList.splice(startIndex, res.rows.length, ...res.rows)
    },
    handGetUploadStatus(status, file) {
      if (status === 'start') {
        this.uploadStartId = file.uid
      } else {
        this.uploadEndId = file.uid
      }
    },
    // pictureUploadedHandler(data = null) {
    //   this.retoucherOptionClone.pictureUrl = data?.url || ''
    //   this.retoucherOptionClone.pictureId = data?.id || ''
    //   this.retoucherOptionClone.uploadIconUrl = data?.url || ''
    //   this.retoucherOptionClone.uploadIconId = data?.id || ''
    //   this.retoucherOptionClone.uploadIconWidth = data?.width || ''
    //   this.retoucherOptionClone.uploadIconHeight = data?.height || ''
    //   this.retoucherOptionClone.uploadIconName = data?.name || ''
    // },
    pictureUploadedHandler(data = null, item) {
      // 说明是编辑
      if (item) {
        this.$set(this.uploadList, this.editIndex, {
          pictureUrl: data?.url || '',
          pictureId: data?.id || '',
          uploadIconUrl: data?.url || '',
          uploadIconId: data?.id || '',
          uploadIconWidth: data?.width || '',
          uploadIconHeight: data?.height || '',
          uploadIconName: data?.name || '',
          isSaveAsMaterial: this.editItem.isSaveAsMaterial || false
        })

        this.retoucherOptionClone.pictureUrl = data?.url || ''
        this.retoucherOptionClone.pictureId = data?.id || ''
        this.retoucherOptionClone.uploadIconUrl = data?.url || ''
        this.retoucherOptionClone.uploadIconId = data?.id || ''
        this.retoucherOptionClone.uploadIconWidth = data?.width || ''
        this.retoucherOptionClone.uploadIconHeight = data?.height || ''
        this.retoucherOptionClone.uploadIconName = data?.name || ''
      } else {
        let res = {
          pictureUrl: data?.url || '',
          pictureId: data?.id || '',
          uploadIconUrl: data?.url || '',
          uploadIconId: data?.id || '',
          uploadIconWidth: data?.width || '',
          uploadIconHeight: data?.height || '',
          uploadIconName: data?.name || '',
          isSaveAsMaterial: false
        }
        this.retoucherOptionClone.pictureUrl = data?.url || ''
        this.retoucherOptionClone.pictureId = data?.id || ''
        this.retoucherOptionClone.uploadIconUrl = data?.url || ''
        this.retoucherOptionClone.uploadIconId = data?.id || ''
        this.retoucherOptionClone.uploadIconWidth = data?.width || ''
        this.retoucherOptionClone.uploadIconHeight = data?.height || ''
        this.retoucherOptionClone.uploadIconName = data?.name || ''
        // this.uploadList.push(res);
        // 放在数组的第一位
        this.uploadList.unshift(res)
      }
    },
    uploadImgChange() {
      let list = this.uploadList.filter(item => item.isSaveAsMaterial).map(item => {
        return item.uploadIconUrl
      }
      )
      // 记录图片日志
      if (!this.retoucherOptionClone.uploadIconUrl) return
      const params = Object.assign({}, OPERATION_LOG, {
        // 渠道（PC、SUB、APP）。注：SUB（订阅提醒，包括PC、APP）
        channel: this.utils.env.channel || '',
        // 日志类型（1：登录日志，2：操作日志）
        logType: '2',
        // 本节点ID
        objectId: this.boardInfo.id,
        // 本节点名称
        objectName: this.boardInfo.name || '',
        // 操作内容（前后对比json串）
        operateContent: `Add the Decorative Indicator Icon, url: ${list || ''}`,
        // 操作页面ID
        operatePageId: this.boardInfo.id,
        // 操作类型（1：新增，2：修改，3：..., 59: 浏览）
        operateType: '2',
        // 切换租户得到的企业ID
        tenantId: this.utils.tenantId,
        // 本地浏览器时间 2018-11-23 00:00:00
        updateDateLocal: this.boardInfo.nowStr,
        // 页面
        menuI18Key: this.utils.env.menuI18Key || '',
        // 模块
        modelI18Key: this.utils.env.modelI18Key || ''
      })
      operationalLog(this.utils.api, params)
    },
    containerScrollHandler() {
      if (this.listLoading || this.iconMaterialList.length >= this.listTotal) return
      const {
        clientHeight,
        scrollTop,
        scrollHeight
      } = this.$refs.iconContainer
      if (scrollHeight - clientHeight - scrollTop <= 50) {
        if (
          this.iconMaterialList.length < this.listTotal &&
          this.iconMaterialList.length >=
            this.paginationData.page * this.paginationData.limit
        ) {
          this.paginationData.page = this.paginationData.page + 1
        }
        this.getIconMaterialList()
      }
    },
    handleEdit(item, index) {
      this.editItem = item
      this.editIndex = index
    },
    handleRemove(item) {
      if (this.retoucherOptionClone.pictureId === item.uploadIconId) {
        this.retoucherOptionClone.pictureUrl = ''
        this.retoucherOptionClone.pictureId = ''
      }
      this.uploadList = this.uploadList.filter(
        i => i.uploadIconId !== item.uploadIconId
      )
    }
    // handleRemove() {
    //   if (
    //     this.retoucherOptionClone.pictureId ===
    //     this.retoucherOptionClone.uploadIconId
    //   ) {
    //     this.retoucherOptionClone.pictureUrl = '';
    //     this.retoucherOptionClone.pictureId = '';
    //   }
    //   this.retoucherOptionClone.uploadIconUrl = '';
    //   this.retoucherOptionClone.uploadIconId = '';
    // }
  }
}
</script>
<style media="screen" lang="scss" scoped>
.el-dialog__wrapper /deep/ {
  text-align: center;

  .el-dialog {
    .el-dialog__body {
      padding: 20px 20px;

      .main {
        margin: 10px 0;

        .title {
          display: inline-flex;
          width: 80px;
          margin: 0 20px 0 10px;
          line-height: 32px;
          text-align: right;
        }

        .el-input {
          width: 200px;
        }
      }
    }

    .el-dialog__footer {
      padding-top: 0;

      .dialog-footer {
        display: block;
      }
    }
  }

  .selected {
    &.item-label-container {
      color: $color-main;
    }
  }
}
.dialog-retoucher-container{
  /deep/ .image-box-container.image-icon-container {
    width: 83px;
    height: 83px;
  }
}
.datarefresh-icon-container /deep/ {
  height: 288px;
  overflow-y: auto;
  padding: 0 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;

  .sdp-upload {
    height: 100%;
    width: 100%;
  }

  .custom-upload-box {
    .upload-mask {
      height: 32px;
      background: rgba(0, 0, 0, 0.6);
      position: absolute;
      bottom: 0;
      width: 100%;
      line-height: 32px;
      text-align: center;
      pointer-events: none;
      border-radius: 0 0 8px 8px;
      display: none;

      .icon-sdp-zhibiaoxuanzeqibianji {
        margin-right: 16px;
      }

      i {
        font-size: 16px;
        pointer-events: auto;
        color: #fff;

        &:hover,
        &:focus {
          color: $color-main;
        }
      }
    }
    .image-box-container {
      &:hover {
        .icon-sdp-xinzengjiahao {
          color: $color-main;
        }

        .upload-mask {
          display: block;
        }
      }
    }
  }

  .upload-box {
    // margin-bottom: 16px;
    height: 80px;
    float: left;
    display: flex;
    flex-direction: column;

    .el-checkbox {
      visibility: hidden;
      display: none;
    }

    .icon-sdp-xinzengjiahao {
      line-height: 80px;
    }

    .image-box-container {
      &:hover {
        .icon-sdp-xinzengjiahao {
          color: $color-main;
        }

        .upload-mask {
          display: block;
        }
      }
    }

    &.checkboxShow {
      // height: 122px;
      display: flex;
      align-items: flex-start;

      .el-checkbox {
        visibility: visible;
        display: inline-flex;

        .el-checkbox__label {
          padding-left: 8px;
        }
      }
    }

    .upload-mask {
      height: 32px;
      background: rgba(0, 0, 0, 0.6);
      position: absolute;
      bottom: 0;
      width: 100%;
      line-height: 32px;
      text-align: center;
      pointer-events: none;
      border-radius: 0 0 8px 8px;
      display: none;

      .icon-sdp-zhibiaoxuanzeqibianji {
        margin-right: 16px;
      }

      i {
        font-size: 16px;
        pointer-events: auto;
        color: #fff;

        &:hover,
        &:focus {
          color: $color-main;
        }
      }
    }
  }

  .el-upload--picture-card:hover,
  .el-upload:focus {
    color: unset;
  }
}

.image-box-container {
  // margin-right: 16px;
  margin-bottom: 16px;

  &:nth-child(6n) {
    margin-right: 0;
  }
}

.upload-list-content {
  display: flex;
  flex-direction: column;
  gap: 4px;

  /deep/ .el-checkbox__label {
    font-size: 9px !important;
    padding-left: 4px !important;
    vertical-align: middle;
  }
  /deep/ .el-checkbox__inner {
    height: 12px !important;
    width: 12px !important;
  }
  /deep/ .el-checkbox__inner::after {
    top: 0px !important;
    left: 3px !important;
  }

  /deep/ .image-box-container {
    margin-bottom: 0px !important;
    // width: 90px;
    // height: 90px;
    // .icon-item{
    //   width: 48px;
    //   height: 48px;
    //   top: 20px;
    //   left: 20px;
    // }
  }
}
</style>
