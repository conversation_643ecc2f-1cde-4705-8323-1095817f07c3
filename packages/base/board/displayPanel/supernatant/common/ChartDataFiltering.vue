<template>
  <SdpCollapse :title="$t('sdp.views.dataFilter')" :dot="showDot">
    <!--数据集过滤-->
    <table-switch style="margin-top: 12px;">
      <template slot="label">
        <ItemLabel :label="onlyIndex ? $t('sdp.views.DataParam') : $t('sdp.views.DatasetFilter')" :level="4" display="block" style="margin-top: 0px;"/>
      </template>
      <div slot="component">
      <div>
        <i class="icon-sdp-shujuguolv sdp-filter-btn" @click="dataFiltering"></i>
      </div>
      </div>
    </table-switch>

      <div>
        <div class="filter-container" :title="dataFilterArr.join(',')">
          <ItemLabel v-for="(val,index) of dataFilterArr" :key="index" :label="val" :level="4" :labelTitle="dataFilterArr.join(',')" display="block"></ItemLabel>
        </div>
        <add-data-filtering-dialog
          :key="elementType"
          :visible.sync="dataFilteringShow"
          @dataFilteringCallback="confirmDataFilter"
          :type="elementType"
          :datasetList="datasetList"
          :drill-settings="drillSettings"/>
      </div>

    <!--聚合过滤-->
    <table-switch style="margin-top: 12px;" v-if="!$getFeatureConfig || !$getFeatureConfig('polymericFilter.hidden')">
      <template slot="label">
        <ItemLabel :label="$t('sdp.views.PolymericFilter')" :level="4" display="block" style="margin-top: 0px;"/>
      </template>
      <div slot="component">
        <div>
          <i class="icon-sdp-shujuguolv sdp-filter-btn" @click="polymericFiltering"></i>
        </div>
      </div>
    </table-switch>

    <div>
      <div class="filter-container" :title="polymericFilterArr.join(',')">
        <ItemLabel v-for="(val,index) of polymericFilterArr" :key="index" :label="val" :level="4" :labelTitle="polymericFilterArr.join(',')" display="block"></ItemLabel>
      </div>
      <add-data-filtering-dialog
        :key="'polymeric'"
        :visible.sync="polymericFilteringShow"
        @dataFilteringCallback="confirmDataFilter"
        :type="'polymeric'"
        :getDatasetLabel="getDatasetLabel"
        :drill-settings="drillSettings"
      />
    </div>
  </SdpCollapse>
</template>

<script>
import AddDataFilteringDialog from 'packages/base/grid/components/common/CommonDialogDataFilter'
import {ItemLabel, TableSwitch} from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import SdpCollapse from 'packages/base/board/displayPanel/supernatant/chartSet/components/components/SdpCollapse'

export default {
  mixins: [datasetMixin],
  components: {TableSwitch, AddDataFilteringDialog, ItemLabel, SdpCollapse },
  inject: {
    configs: { default: () => false },
  },
  props: {
    datasetList: {
      type: Array,
      default: () => [],
    },
    drillSettings: {
      type: Object,
      default: () => ({}),
    },
    elementType: {
      type: String,
      default: 'chart',
    },
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs.options?.onlyIndex
    },
    datasetIdList() {
      const { dataSetId } = this.drillSettings
      if (!dataSetId) return []
      if (Array.isArray(dataSetId)) return dataSetId
      return [dataSetId]
    },
    // 是否显示小圆点
    showDot() {
      const filters = this.drillSettings.filters
      if (filters && filters.length) {
        return true
      }

      if (!this.drillSettings?.layers?.[0]?.metrics?.length) return false

      const polymericFilters = this.drillSettings.polymericFilters
      if (polymericFilters && polymericFilters.length) {
        return true
      }

      return false
    },
    equalToArr() {
      return {
        'isnull': this.$t('sdp.views.empty'),
        'is_not_null': this.$t('sdp.views.notEmpty'),
        '=': this.$t('sdp.views.equal'),
        '≠': this.$t('sdp.views.notEqual'),
        '>': this.$t('sdp.views.Morebig'),
        '>=': this.$t('sdp.views.MorebEq'),
        '<': this.$t('sdp.views.moreLess'),
        '<=': this.$t('sdp.views.LessEq'),
        'like_start': this.$t('sdp.views.beginIs'),
        'like_end': this.$t('sdp.views.EndIs'),
        'like': this.$t('sdp.views.containIs'),
        'not_like': this.$t('sdp.views.notContainIs'),
      }
    },
    dataFilterArr() {
      const filters = this.drillSettings.filters

      if (filters && filters.length) {
        const arr = filters.map((item, index) => {
          // const fieldList = this.datasetList.find(d => d.id === item.dataSetId)?.children || []
          if (item.expression) {
            // let theFieldDict = {}
            // fieldList.forEach(item => {
            //   theFieldDict[item.labeName] = item.aliasName
            // })
            // return this.translateExpression(item.expression, theFieldDict)
            return this.aliasDict?.updateExpressionFieldAlias(item.expression, item.dataSetId)
          } else {
            let val = item.values[0]
            if (item.filterValueType === 'field') {
              val = this.getUnknownName(item.dataSetId, item.values[0]) || item.values[0]
            } else if (item.filterType === 'isnull' || item.filterType === 'is_not_null') {
              val = ''
            }
            const columnAliasName = this.getUnknownName(item.dataSetId, item.columnName) || item.columnName
            const str = `${index ? item.groupFilterType : ''}${index ? ' ' : ''}${columnAliasName} ${this.equalToArr[item.filterType]}`
            const string = (val || val === 0) ? `${ str } '${val}'` : `${ str }`
            return string
          }
        })
        return arr
      }
      return []
    },
    // 聚合过滤显示值
    polymericFilterArr() {
      if (!this.drillSettings?.layers?.[0]?.metrics?.length) return []

      const filters = this.drillSettings.polymericFilters || []
      const metrics = this.drillSettings.layers[0].metrics

      if (filters && filters.length) {
        const arr = filters.map((item, index) => {
          let val = item.values
          if (item.filterType === 'isnull' || item.filterType === 'is_not_null') {
            val = ''
          }
          const target = metrics.find(e => e.keyName === item.metric)
          if (!target) return ''
          const columnAliasName = this.getDatasetLabel(target)
          const str = `${index ? item.groupFilterType : ''}${index ? ' ' : ''}${columnAliasName} ${this.equalToArr[item.filterType]}`
          const string = (val || val === 0) ? `${ str } '${val}'` : `${ str }`
          return string
        })
        return arr.filter(e => e)
      }
      return []
    },
  },
  data() {
    return {
      filteringDatasetList: [],
      dataFilteringShow: false,
      polymericFilteringShow: false,
    }
  },
  methods: {
    confirmDataFilter() {
      this.$emit('dataFilteringCallback')
      this.dataFilteringShow = false
    },
    dataFiltering() {
      if (this.datasetIdList.length) {
        this.dataFilteringShow = true
        this.filteringDatasetList = this.datasetList.filter(item => this.datasetIdList.includes(item.id))
      } else {
        this.$message(this.$t('sdp.views.plsDataSet'))
      }
    },
    // 聚合过滤打开方式
    polymericFiltering() {
      if (this.drillSettings?.layers?.[0]?.metrics?.length) {
        this.polymericFilteringShow = true
      } else {
        this.$message(this.$t('sdp.views.selectMeasures'))
      }
    },
  }
}
</script>

<style lang="scss" scoped>
/deep/ .table-switch-container {
  .item-label-container.item-label-block {
    height: auto;
    line-height: unset;
  }
}
.filter-container {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  //line-height: 16px;
  color: var(--sdp-xxbt2);
  /deep/ .item-label-container.item-label-block {
    //height: auto;
    //line-height: unset;
    color: var(--sdp-sztc-srkbt);
    margin-top: 0;
    &:nth-child(1) {
      margin-top: 4px !important;
    }
  }
}
.sdp-filter-btn {
  cursor: pointer;
  font-size: 18px;
  color: var(--sdp-xlk-jts),
}
</style>
