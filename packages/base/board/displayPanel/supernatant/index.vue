<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <!-- isSafari ? 'noFlex' : '' -->
  <div :class="['supernatantWrap', isEnlarge? 'enlarge-mobile-height' : '', isMobile ? 'is-app' : 'is-pc']" ref="supernatant" :style="supernatantStyle">
    <!-- <div v-if="!isMobile" :class="['patchBar', !commonData.isPreview && !(toggle % 2) ? 'panel-disable':'panel-active']"></div> -->
    <!-- 看板元素设置弹窗 -->
    <!-- 表格组件编辑在这 -->
    <component
      :is="elSettingDialog"
      :key="elSettingDialog"
      :class="`${getCurrentThemeClass()}`"
      v-model="elSettingVisible"
      v-bind="elSettingProps"
      :ref="settingDialogRef"
      :dataset-list="datasetList"
      :el-list="elList"
      :board-info="boardInfo"
      :dynamicTags="dynamicTags"
      @on-back="backHandler"
      @on-save="elContentSaveHelper"
      @eventBus="eventBus"
    />
    <chartContainerDialog
      v-if="!isScreen"
      :value.sync="containerPreviewVisible"
      ref="containerPreviewRef"
      key="containerPreviewRef"
      :type="TYPE_CHART_CONTAINER_DIALOG.preview"
      :dataset-list="datasetList"
      :el-list="elList"
      :board-info="boardInfo"
      :dynamicTags="dynamicTags"
      @eventBus="eventBus"
    />
    <!--  弹窗放大  -->
    <component
      :is="fullscreenComponent"
      @eventBus="eventBus"
      ref="fullscreenDialog"
      v-model="enlargeVisible"
      :enlargeEl.sync="enlargeEl"
      v-bind="{
        enlargementColorFormatter,
        fullscreenStyle,
        elementBox,
        dynamicTagsClone,
        elBgColorFormatter,
        isMobile,
        elList,
        containerIdElList,
        parent: this,
        isWeAppAndWeb,
        isHorizontal,
        watermarkUrl,
        isApp,
      }"
    />
    <!-- 预览时信息  -->
    <div v-show="!themeFullScreen && !isTvScreen && (!isDisabledScreenMode || !isScreen)">
      <slot v-if="!isMobile" name="breadcrumb" :isShowBreadcrumb="isShowBreadcrumb"></slot>
      <!-- 面板名称 -->
      <div class="board-veiw-container" v-if="isHideTitle" style="position: relative">
        <div class="title" :class="{tiny: isPreview && isDivideTabs}" :style="{ ...reTitleStyle, minHeight: '60px' }" id="title">
          {{title}}
        </div>
      </div>
      <slot name="language"></slot>
      <slot class="supernatant-board-remark" name="boardRemark"></slot>
      <div class="supernatant-params-info" ref="paramsInfo">
        <slot name="paramsInfo"></slot>
      </div>
      <slot name="tabs" :pcTabsHeight="pcTabsHeight"></slot>
    </div>
    <!-- 屏幕留白 自定义色块部分 noFlex: isSafari, end -->
    <div ref="supernatantContent" class="content" id="supernatantContent" :style="getSupernatantContentStyles()" :class="{ pcPadding: !isMobile, screenBoard: !isMobile && !utils.isPcMobile && isScreen && !isDataReportDing, isPreview, 'watermark-area': isShowWatermark, 'sdp-report-ding': isDataReportDing, 'show-scale': !isScreen && !isMobile && !isPreview }">
      <!-- 水印 -->
      <div v-if="isShowWatermark" class="watermark" :style="{'background-image': watermarkUrl}"></div>
      <!--  大屏顶部留出位置  -->
      <div v-if="isShowScreenTopBox" :style="contentInfoStyle"></div>
      <div v-if="isLargeScreen" class="sdp-screen-box">
        <!--  大屏跳转面包屑  -->
        <slot name="breadcrumb" :isShowBreadcrumb="isShowBreadcrumb" :contentInfoStyle="contentInfoStyle"></slot>
        <!-- 占位 -->
        <div v-if="!isShowBreadcrumb"></div>
        <!-- 大屏看板全屏 -->
        <slot name="fullscreenMode"></slot>
      </div>
      <!--  大屏看板tabs切换  -->
      <slot v-if="isShowLargeTabs" name="tabs" :contentInfoStyle="contentInfoStyle" :isShowLargeTabs="isShowLargeTabs" :isCloseCarousel="isCloseCarousel" :screenModeDate="boardInfo.screenModeDate" :tagModeStack="boardInfo.tagModeStack" :pcTabsHeight="pcTabsHeight"></slot>
      <el-carousel ref="carousel" :initial-index="dynamicIndex"
                   :class="{ carouselIsAnimating: !themeFullScreen }"
                   class="carouselStyle" trigger="click"
                   :autoplay="carouselAttr.autoplay"
                   :interval="carouselAttr.interval"
                   @change="handleCarouselChange"
                   indicator-position="none" arrow="never">
        <el-carousel-item v-for="tag in dynamicTagsClone"
                          :class="{
                            'enlarge-mobile-height': isEnlarge,
                            [animateClass(tag)]: tag.active && isPreview && !isCloseCarousel,
                            'board-scale-overflow': boardScale !== 100,
                          }"
                          :key="tag.id"
                          class="supernatant-layout-item">
            <component
              :is="fullscreenType"
              class="supernatant-layout"
              :class="{ 'layout-ready': isReady, 'layout-scroll': fullscreenType === 'largeScreen' && themeData.themeFullScreen, 'no-scroll': isDataReportDing }"
              :style="{
                transformOrigin: boardScale !== 100 ? `left top` : '',
                transform: boardScale !== 100 ? `scale(${boardScale/100})` : ''
              }"
              ref="dynamicTags"
              v-bind="{
                init,
                isFinish,
                highlightItem,
                content: tag.content,
                layoutDeploy,
                elList,
                active: tag.active,
                visibleCopyMenuChildren,
                flattenElementComp,
                elTools,
                tag,
                dynamicTags,
                hasSpecialElementCopy: true,
                hasSpecialElementDel: true,
                tagId:tag.id,
                enlargeId,
                referenceLineOpts: boardInfo.referenceLineOpts,
                fullscreenRestHeight: fullscreenTitleHeight[tag.id].height + paddingFix + breadcrumbHeights + pcTabsHeights + hideSupernatantTop + contentInfoStyleNum,
                isScreenSkipToScreen,
                boardInfo,
                paddingData,
                boardScale,
                needTransition,
                isPreviewEdit: !commonData.isAdvanceContainerEdit,
                largeScreenBg: largeScreenBgFormatter(tag, themeData),
                storeSet,
                dailyConcernData,
              }"
              @eventBus="eventBus"
              @gridItemClickHandler="gridItemClickHandler"
              @layout-ready="layoutReady"
              @copy-element="elToolsCopyHandler"
              @remove-element="removeEl"
            >
              <template v-slot:default="slotProps">
                  <div
                    v-if="isMobile"
                    class="full-class-mobile"
                    :style="{...setElBoxStyle(slotProps.el.type),...elBgColorFormatter(slotProps.el)}"
                    :ref="`${slotProps.i}-fullscreen`"
                    :class="[
                      kanbanId,
                      `exportDOM${slotProps.el.id}`,
                      slotProps.el.type === 'chartContainer' || slotProps.el.type === 'element-tag-new-card' ? 'full-class-mobile-container' : '',
                      slotProps.el.type === 'combine-card' ? 'full-class-mobile-combine-card' : '',
                      isEnlarge ? 'enlarge-mobile-height' : ''
                    ]">
                    <!-- :style="{background: !init && slotProps.el.style['background'] ? slotProps.el.style['background'] : '#fff'}" -->
                    <LoadingMiddleWare
                      class="loading-content"
                      :key="slotProps.i"
                      :ref="slotProps.i"
                      :element="slotProps.el"
                      :componentName="componentNameList[slotProps.el.type]"
                      :elList="elList"
                      :screenType="screenType"
                      :fullscreen="fullscreen"
                      :fullscreenId="fullscreenId"
                      :enlargeVisible="enlargeVisible"
                      :isShowWatermark="isShowWatermark"
                      :init="init"
                      :boardScale="boardScale"
                      :loadingInfo="{style: elBgColorFormatter(slotProps.el, true, {  }),title:slotProps.el.elName,isMobile:isMobile}"
                      :style="{...elPaddingFormatter(slotProps.el)}"
                      :board-info="boardInfo"
                      :paddingStyle="[TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD === slotProps.el.type ? {} : setElPaddingStyle(slotProps.el, isMobile)]"
                      @eventBus="eventBus"
                      @cube-popup="(data) => $emit('cube-popup', data)"
                      @cube-tip="(data) => $emit('cube-tip', data)"
                    >
                      <!-- 这里可以放置插槽 提了优化单，将这个图标放到了MobilePopover里面 -->
                      <!-- <div :class="(isPreview && isFilterSoter(slotProps.el) )? 'genxinshijian-position':''">
                          <div v-for="(btn,index) in elTools.moreBtns" :key="'moreBtns_'+index">
                                <template v-if="btn.icon === 'icon-sdp-sdp-gengxinshijian'  && commonData.isPreview && !enlargeVisible">
                                      <i :title="btn.title" :class="btn.icon" slot="reference" class="icon" @click="btn.mobileFetchUpdateTime(slotProps.el, slotProps.i, btn)"></i>
                                  </template>
                                  全局还需要什么小图标可以在这进行添加
                          </div>
                       </div> -->
                    </LoadingMiddleWare>
                    <!-- v-if="isPreview && isFilterSoter(slotProps.el) 去除isFilterSoter判断，因为跟新时间需要放在这里面。更新时间每个容器在预览时都要展示" -->
                    <div  class="filtersorttip sdp-tools-dots" v-if="isPreview">
                      <i
                        v-if="slotProps.el.type === TYPE_ELEMENT.CHART &&
                              slotProps.el.content.alias !== 've-grid-normal' &&
                              !(isNormalLiquidfill(slotProps.el.content)) &&
                              slotProps.el.content.chartUserConfig.dataViewer"
                        class="icon icon-sdp-chakanshuju icon_color"
                        style="font-size: 15px;padding: 10px;"
                        @click="handleShowChartPopup(slotProps.el)"
                      />
                      <i class="icon icon-sdp-yulanshisandian icon_color" style="font-size: 12px" @click="showTip(slotProps.i)"></i>
                    </div>

                    <!-- roc todo mark by app_advance_container: 移动端的高级容器暂不开放"横屏"图标按钮 -->
<!--                    v-if="!isAdvanceContainer_fn(slotProps.el)"-->
                    <template v-if="!isHomePageFlag">
                      <div :ref="`${slotProps.i}-btn`" class="hquanpingzhanshifix" v-if="isApp && slotProps.el.style.zoomBtn !== false">
                        <i class="icon icon-sdp-hquanpingzhanshi" @click="delayChartFullScreen(slotProps.i, slotProps)"></i>
                      </div>
                      <div :ref="`${slotProps.i}-btn`" class="hquanpingzhanshifix" v-if="isPcHorizontalView && isPreview && (slotProps.el.style.zoomBtn !== false)">
                        <i class="icon icon-sdp-hquanpingzhanshi" @click.stop="handHorizontalView(slotProps.i, slotProps)"></i>
                      </div>
                    </template>

                    <cube-tip
                      :ref="`${slotProps.i}-tip`"
                      direction="right"
                      style="right: 23px; top: 30px;z-index: 10000;width:auto"
                      class="mobile-popover"
                    >
                      <MobilePopover
                        :moreBtns="elTools.moreBtns"
                        :el="slotProps.el"
                        :el-list="elList"
                        :board-info="boardInfo"
                        :is-show="isShowTip"
                        @cube-tip="$emit('cube-tip',$event,slotProps.el )"
                        @eventBus="eventBus"
                      ></MobilePopover>
                    </cube-tip>
                  </div>
                  <fullscreen v-else :ref="`${slotProps.i}-fullscreen`" class="full-class chart-background" :style="elBgColorFormatter(slotProps.el)" @change="fullscreenChange"
                              :class="[kanbanId, `exportDOM${slotProps.el.id}`]"
                  >
                      <LoadingMiddleWare
                        :active="tag.active"
                        :key="slotProps.i"
                        :ref="slotProps.i"
                        :layoutDeploy="layoutDeploy"
                        :screenType="screenType"
                        :element="slotProps.el"
                        :componentName="componentNameList[slotProps.el.type]"
                        :isShowWatermark="isShowWatermark"
                        :isFinish="isFinish"
                        :elList="elList"
                        :fullscreen="fullscreen"
                        :fullscreenId="fullscreenId"
                        :enlargeVisible="enlargeVisible"
                        :init="init"
                        :boardScale="boardScale"
                        :board-info="boardInfo"
                        :isBrowserAdaptationMode="slotProps.isBrowserAdaptationMode"
                        :browserAdaptationEnabled="slotProps.browserAdaptationEnabled"
                        :addDailyElementSelectedList="addDailyElementSelectedList"
                        :loadingInfo="{style: elBgColorFormatter(slotProps.el, true),title:slotProps.el.elName,isMobile:isMobile}"
                        :style="{...elPaddingFormatter(slotProps.el)}"
                        @elementCheck="handleAddDailyElementCheck"
                        @eventBus="eventBus"
                        @cube-popup="(data) => $emit('cube-popup', data)"
                        :elStyle="[TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD === slotProps.el.type ? {} : setElPaddingStyle(slotProps.el),...setElBoxStyle(slotProps.el.type)]"
                      />
                  </fullscreen>
              </template>
            </component>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 屏幕留白 自定义色块部分 start -->
    <div v-if="screenSpaceMode && themeFullScreen" class="screenSpaceTop" :style="screenSpaceTop()"></div>
    <div v-if="screenSpaceMode && themeFullScreen" :style="screenSpaceBottom()"></div>
    <!-- 大屏看板 缩放滑块 -->
    <div v-if="isShowSlider" class="large-screen-slider">
      <scalePopover v-model="boardScale" />
      <div class="slider-icon slider-sub" @click="changeBoardScale('sub')"><i class="icon-sdp-slider-sub-button"></i></div>
      <el-slider v-model="boardScale" :min="10" :max="400" :format-tooltip="sliderFormatTooltip"></el-slider>
      <div class="slider-icon slider-add" @click="changeBoardScale('add')"><i class="icon-sdp-slider-add-button"></i></div>
    </div>
    <!-- PC看板 缩放滑块 -->
    <div
      v-if="!isScreen && !isMobile && !isPreview"
      class="large-screen-slider sdp-board-scale-slider"
      style=""
    >
      <scalePopover v-model="boardScale" @input="handleBoardScaleChange" popperClass="sdp-board-scale-slider-popper" />
      <div class="slider-icon slider-sub" @click="changeBoardScale('sub')"><i class="icon-sdp-slider-sub-button"></i></div>
      <el-slider v-model="boardScale" :min="10" :max="400" :format-tooltip="sliderFormatTooltip" @change="handleBoardScaleChange"></el-slider>
      <div class="slider-icon slider-add" @click="changeBoardScale('add')"><i class="icon-sdp-slider-add-button"></i></div>
    </div>

    <div v-else-if="isDataReport && !isPreview && !utils.isPcMobile" class="data-report-scale-bar-container">
      <div class="data-report-scale-bar">
        <div class="slider-icon slider-sub" @click="changeBoardScale('sub')"><i class="icon-sdp-slider-sub-button"></i></div>
        <!-- disabled: 禁用 Popover, 若开放则需对样式进行多主题处理 -->
        <scalePopover v-model="boardScale" disabled />
        <div class="slider-icon slider-add" @click="changeBoardScale('add')"><i class="icon-sdp-slider-add-button"></i></div>
      </div>
    </div>
    <FilterSorterDialog
      ref="filterSorterDialog"
      :visible.sync="filterSorterVisible"
      :elList="elList"
      :dataset-list="datasetList"
      @eventBus="eventBus"
    />
    <ContainerFilterSortDialog
      ref="containerFilterSortDialog"
      :visible.sync="containerFilterSorterVisible"
      :elList="elList"
      :dataset-list="datasetList"
      @eventBus="eventBus"
    />
    <RemarkDialog
      v-if="commonData.isTenantUser"
      ref="remarkDialog"
      :visible.sync="remarkVisible"
      :elList="elList"
      :board-info="boardInfo"
      :dataset-list="datasetList"
      @close="remarkVisible = false"
      @eventBus="eventBus"
    />
    <CopyElementsToTabsDialog
      ref="copyElementsToTabsDialog"
      :isMobile="isMobile"
      :visible.sync="copyElementsToTabsVisible"
      :dynamicTags="dynamicTags"
      @confirm="copyElementsToTabs"
    />

    <TextSettingDialog
      :elList="elList"
      :dataset-list="datasetList"
      ref="textSettingDialog"
      @eventBus="eventBus"
      @on-save="elContentSaveHelper"/>

    <export-dialog
      :el-list="elList"
      :init="init"
      :watermarkUrl="watermarkUrl"
      :elementExport="boardInfo.elementExport"
      :board-info="boardInfo"
      @eventBus="eventBus"
      @setProgressId="(id) => progressId = id"
      ref="exportDialog"
    />

    <ExportTableData v-model="isExportTableVisible" :boardInfo="boardInfo" :el-list="elList" :element="exportTableElement" :watermarkUrl="watermarkUrl"  @eventBus="eventBus" @setProgressId="(id) => progressId = id"></ExportTableData>

    <ExportProgressDialog :id="progressId" :api="utils.api"></ExportProgressDialog>

    <AddToTemplate :templateElement="templateElement" :newElementContent="newElementContent" :treeData="treeData" :visible.sync="isShowTemplate" />

    <boardRemark
      :visible.sync="boardRemarkVisible"
      :boardInfo="boardInfo"
      @close="closeBoardRemarkDialog"
    />

    <add-daily-concern-dialog
      ref="addDailyConcernDialog"
      :visible.sync="addDailyConcernDialogVisible"
      :dailyConcernData="dailyConcernData"
      @close="addDailyConcernDialogVisible = false"
      @confirm="handleAddDailyDialogConfirm"
      @eventBus="eventBus"
    />

    <!-- todo（后面改成调用外界弹窗） sbi加入每日指标样式  -->
    <el-dialog
      width="400px"
      top="30vh"
      title=""
      center
      :custom-class="`sdp-dialog ${getCurrentThemeClass()}`"
      :append-to-body="true"
      :close-on-click-modal="false"
      :show-close="false"
      class="message-success-dialog"
      :visible.sync="isMessageDialog">
      <div class="el-message-box__content">
        <div class="iconBoxCls">
          <SvgSuccess/>
        </div>
        <div class="tips">{{ $t('sdp.message.addSuccess') }}</div>
        <!--<p v-if="isShowDddSuccessTip" class="fontStype">{{ $t('sdp.message.addSuccessTip') }}</p>-->
        <p v-if="isShowAddSuccessTip" class="fontStype" @click="handleOpenDailyConcern">{{ $t('sdp.message.addSuccessTip2') }}</p>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" class="el-button--sdp-ensure" @click="isMessageDialog = false">{{$t('sdp.button.ensure')}}</el-button>
      </div>
    </el-dialog>

    <div v-if="kanbanGuideEntryPanelVisible && !isPreview" class="kanban-guide-panel" ref="kanbanGuidePanelRef">
      <!-- <div>isElementEditMode: {{ kanbanGuide.isElementEditMode }}</div> -->
      <!-- <div>isFilterEditMode: {{ kanbanGuide.isFilterEditMode }}</div> -->
      <!-- getStepState -->
      <!-- getStepStyles -->
      <!-- <hr /> -->

      <div class="guide-header">
        <div class="guide-title">{{$t('sdp.guide.operatingGuide')}}</div>
        <i
          class="el-icon-close guide-header-close"
          @click="closeGuidePanel"
        ></i>
      </div>

      <div
        class="guide-entry"
        :class="[getStepClassName(kanbanGuide.stepEntryMap.addDatasets)]"
        @click="showTheStepTips(kanbanGuide.stepEntryMap.addDatasets)"
       >
        <span class="guide-entry-name">
          <span class="guide-entry-index">{{kanbanGuide.stepEntryMap.addDatasets.stepIndex + 1}}.</span>
          <span>{{ $t('sdp.guide.addDataset') }}</span>
        </span>
        <span class="done-icon" v-show="kanbanGuide.stepEntryMap.addDatasets.isDone">
          <i class="el-icon-check"></i>
        </span>
      </div>
      <!-- 配置元素内容 -->
      <div
        class="guide-entry"
        :class="[getStepClassName(kanbanGuide.stepEntryMap.addElements)]"
        @click="showTheStepTips(kanbanGuide.stepEntryMap.addElements)"
       >
        <span class="guide-entry-name">
          <span class="guide-entry-index">{{kanbanGuide.stepEntryMap.addElements.stepIndex + 1}}.</span>
          <span>{{ $t('sdp.guide.addChart') }}</span>
        </span>
        <span class="done-icon" v-show="kanbanGuide.stepEntryMap.addElements.isDone">
          <i class="el-icon-check"></i>
        </span>
      </div>

      <div
        class="guide-entry"
        :class="[getStepClassName(kanbanGuide.stepEntryMap.elConfigs), !kanbanGuide.stepEntryMap.addElements.isDone && 'disabled']"
        @click="kanbanGuide.stepEntryMap.addElements.isDone && showTheStepTips(kanbanGuide.stepEntryMap.elConfigs)"
       >
        <span class="guide-entry-name">
          <span class="guide-entry-index">{{kanbanGuide.stepEntryMap.elConfigs.stepIndex + 1}}.</span>
          <span>{{ $t('sdp.guide.configurationElementContent') }}</span>
        </span>
        <span class="done-icon" v-show="kanbanGuide.stepEntryMap.elConfigs.isDone">
          <i class="el-icon-check"></i>
        </span>
      </div>

      <div
        class="guide-entry"
        :class="[getStepClassName(kanbanGuide.stepEntryMap.addFilters)]"
        @click="showTheStepTips(kanbanGuide.stepEntryMap.addFilters)"
       >
        <span class="guide-entry-name">
          <span class="guide-entry-index">{{kanbanGuide.stepEntryMap.addFilters.stepIndex + 1}}.</span>
          <span>{{ $t('sdp.guide.addFilter') }}</span>
        </span>
        <span class="done-icon" v-show="kanbanGuide.stepEntryMap.addFilters.isDone">
          <i class="el-icon-check"></i>
        </span>
      </div>

      <div
        class="guide-entry"
        :class="[getStepClassName(kanbanGuide.stepEntryMap.filterConfigs), !kanbanGuide.stepEntryMap.addFilters.isDone && 'disabled']"
        @click="kanbanGuide.stepEntryMap.addFilters.isDone && showTheStepTips(kanbanGuide.stepEntryMap.filterConfigs)"
       >
        <span class="guide-entry-name">
          <span class="guide-entry-index">{{kanbanGuide.stepEntryMap.filterConfigs.stepIndex + 1}}.</span>
          <span>{{ $t('sdp.guide.configureFilters') }}</span>
        </span>
        <span class="done-icon" v-show="kanbanGuide.stepEntryMap.filterConfigs.isDone">
          <i class="el-icon-check"></i>
        </span>
      </div>

      <div
        class="guide-entry"
        :class="[getStepClassName(kanbanGuide.stepEntryMap.kanbanSaved)]"
        @click="showTheStepTips(kanbanGuide.stepEntryMap.kanbanSaved)"
       >
        <span class="guide-entry-name">
          <span class="guide-entry-index">{{kanbanGuide.stepEntryMap.kanbanSaved.stepIndex + 1}}.</span>
          <span>{{ $t('sdp.guide.saveKanban') }}</span>
        </span>
        <span class="done-icon" v-show="kanbanGuide.stepEntryMap.kanbanSaved.isDone">
          <i class="el-icon-check"></i>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
// import html2canvas from 'html2canvas'
import html2canvas from 'packages/base/board/mixins/html2canvas.esm.js'
import SupernatantEl from './supernatantEl'
/*  导入看板元素  */
import elementTable from './boardElements/elementTable'
import elementChart from './boardElements/elementChart'
import elementImage from './boardElements/elementImage'
import elementText from './boardElements/elementText'
import elementTime from './boardElements/elementTime'
import elementWeb from './boardElements/elementWeb'
import elementScrollText from './boardElements/elementScrollText'
import elementFourQuadrant from './boardElements/elementFourQuadrant'
import elementContainer from './boardElements/elementContainer'
import elementTagNewCard from './boardElements/elementTagNewCard'
import elementCombineCard from './boardElements/elementCombineCard'
import elementMaterialLibrary from './boardElements/elementMaterialLibrary'
import elementTitle from './boardElements/elementTitle'
/* 导入属性和方法 */
import { componentNameList } from './boardElements/bridge'

import EventData from 'packages/assets/EventData'
import eventBus from 'packages/assets/eventBus'
import { BOARD_ELEMENT_THEME, BOARD_ELEMENT_THEME_TRANSPARENT, BoardElement } from './boardElements/BoardElement'
// import { convertData } from '../../../../base/common/sdpGrid/utils'
import { generateElNames } from './utils/supernatantHelper'
import { elContentSaveHelper, elContentSetHelper, getContainerOnElement, IS_ADD_TYPE, saveElementData } from './utils/utils'
import {
  adapter, EVENT_BUS,
  MAX_COUNT_ELEMENTS_IN_BOARD,
  MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD,
  paramsBindElements,
  REFRESH_COMPONENT,
  TYPE_CHART_CONTAINER_DIALOG,
  TYPE_ELEMENT
} from 'packages/base/board/displayPanel/constants'
// import { Message } from 'element-ui'
import layout from './layout'
import mobileFull from './mobileFull'
import { SdpGridItemBody, SdpGridItemHeader } from '../../../../base/common/sdpGrid/index'
// 导入特殊控件弹窗
import chartDialog from './chartSet'
import elementFourQuadrantDialog from './fourQuadrantDialog'
import elementTagNewCardDialog from './cardSet'
import tableDialog from './boardElements/elementTable/setting'
import customerElementDialog from './boardElements/elementCustomer/editorSetting'
import dupontAnalysisDialog from './boardElements/elementDupontAnalysis/editorSetting'
import chartContainerDialog from './elementContainerDialog'
import combineCardDialog from './combineCardDialog'
import RemarkDialog from './remarkDialog'
import { actionGetDataSetIds } from 'packages/base/board/displayPanel/params/utils/utils'
import * as api from './api'
import { getContainerIncludeElsIdList, getELementPadding, inBoardMessage } from '../utils'
import pcFullscreen from './fullscreenDialog/pcFullscreen'
import appFullscreen from './fullscreenDialog/appFullscreen'
import {
  BG_MAP_STRING,
  CREATE_TOAST_TIME,
  GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY,
  TARO_ENV_TYPE,
  THEME_TYPE
} from 'packages/assets/constant'
import helper from 'packages/base/gridPreview/render/canvasRenderer/core/helper'
// import fullScreenInfo from './fullScreenInfo'
// var elementResizeDetectorMaker = require('element-resize-detector')
import LoadingMiddleWare from '../../../../base/common/loadingMask/index.vue'
import FilterSorterDialog from 'packages/base/board/displayPanel/supernatant/filterDialog/FilterSorterDialog'
import ContainerFilterSortDialog
  from 'packages/base/board/displayPanel/supernatant/filterDialog/ContainerFilterSortDialog'
import horizontalView from './mixins/horizontalView'
import CopyElementsToTabsDialog from './copyElementsToTabsDialog'
import MobilePopover from 'packages/base/board/displayPanelMobile/components/MobilePopover.vue'
import TextSettingDialog from './boardElements/elementText/TextSettingDialog'
import largeScreen from './largeScreen/index.vue'
import DataReportLayout from './dataReport/DataReportLayout.vue'
import ExportDialog from '../components/ExportDialog'
import ExportProgressDialog from '../components/ExportProgressDialog'
import ExportTableData from '../components/ExportTableData'
import AddToTemplate from './addToTemplate'
import SvgSuccess from '../../../common/SdpSvg/src/SvgSuccess'
import { isAdvanceContainer_fn } from 'packages/assets/utils/helper'

import { elStyleFormatter, setElPaddingStyle } from 'packages/base/board/displayPanel/utils'
import { RUN_ELEMENT_TYPE } from '../../../../assets/constant'
import {
  MATERIAL_COLOR_TYPE,
  MATERIAL_NO_BACKGROUND,
  MATERIAL_TRANSPARENT_BACKGROUND
} from './boardElements/elementMaterialLibrary/mixins/constant'
import { getBrowserInfo } from 'packages/assets/utils/system.ts'
import { getTreeData } from './api'
import { largeScreenFormatter, largeScreenFormatterBg } from '../components/utils'
import { ALL_PROJECT_NAME } from '../../../../components/mixins/commonMixin'
import { ANIMATION_WITH_INTERACTION_CHART } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { RECORD_OPERATE_TYPE } from '../../mixins/boardRecord'
import { calculateGroupPositionAndSize } from './largeScreen/globalTools'
import { JSONClone } from '../../../../assets/utils/globalTools'
import { Communication, SOURCE_TYPE } from './boardElements/elementCustomer/utils'
import { CUSTOMER_ELMENT_MAX_NUM } from './boardElements/elementCustomer/utils/constant'
import { getRecordCoords, recordNextCoords } from './dataReport/helper/coords'
import { isRealScreenFn } from 'packages/helpers'
import scalePopover from './scalePopover'
import {
  CAN_EDIT_TYPES,
  CAN_SET_PADDING_TYPES,
  ELEMENT_PADDING_DEFAULT,
  ELEMENT_PADDING_DEFAULT_MOBILE
} from './boardElements/constant'
import { SBI_ATTR } from '../../dailyConcernMetrics/utils'
import CryptoJS from 'crypto-js'
import { getDefaultReportPadding } from './dataReport/ReportSettings'
import { COPY_PAGE, COPY_TO_CURRENT_PAGE, COPY_TO_OTHER_PAGE } from './largeScreen/constant'
import BoardRemark from '../components/boardRemark'
import paramElementDisableMixins from 'packages/base/board/displayPanel/mixins/paramElementDisable'
import refreshMixin from 'packages/base/board/displayPanel/datasetReplace/refreshMixin'
import { getPX } from './dataReport/helper/cm2px'
import { PC_MOBILE_DISABLED_ELEMENT } from '../../pcMobile/constans'
import { getElementShoots } from '../../displayPanelMobile/components/PcMobileElementBox/utils'
import { kanbanGuideStepEntryPanelMixin } from 'packages/base/KanbanGuide'
import { styleObject } from 'packages/assets/theme/theme-style/constant'
import { indexForTop } from './largeScreen/shape.helper'
import { filterElementLans } from 'packages/base/board/displayPanel/utils'
import {isNormalLiquidfill} from "./boardElements/elementChart/constant";
import AddDailyConcernDialog from "packages/base/board/displayPanel/supernatant/addDailyConcernDialog/index.vue";
import { DISPLAY_MODE } from "packages/base/board/displayPanel/supernatant/boardElements/elementContainer/constants.ts"

const PUBLIC_KEY = 1234567890123456
export const componentName = 'supernatant'
export default {
  name: componentName,
  provide() {
    const fullscreenData = {}
    Object.defineProperty(fullscreenData, 'enlargeVisible', {
      enumerable: true,
      configurable: true,
      get: () => {
        return this.enlargeVisible || this.fullscreen
      },
    })
    Object.defineProperty(fullscreenData, 'enlargeEl', {
      enumerable: true,
      configurable: true,
      get: () => {
        return this.enlargeEl
      },
    })
    Object.defineProperty(fullscreenData, 'isPcHorizontalView', {
      enumerable: true,
      configurable: true,
      get: () => {
        return this.isPcHorizontalView
      },
    })
    const getActiveElement = () => {
      return this.highlightItem
    }
    return {
      fullscreenData,
      getActiveElement,
      elBgColorFormatter: this.elBgColorFormatter,
      getNewElementData: this.addEl,
      elementRef: () => this.element,
      communication: this.communication,
      getContainerRunTab: () => this.containerRunTab
    }
  },
  inject: {
    kanbanId: { type: String, default: '' },
    commonData: { default: {} },
    sdpBus: { default: {} },
    utils: { default: {} },
    requestAppEvent: { default: () => () => {} },
    themeData: { default: () => () => {} },
    boardRecord: { default: () => () => {} },
    langCode: { default: 'zh' },
    datasetList: { default: () => () => [] },
    getBoardUnderIdElement: { default: () => (name) => { return document.getElementById(name.slice(1)) } },
    getDataReport: { type: Function },
    getCurrentThemeClass: { type: Function },
    tenantData: { default: {} },
    dailyConcernMetricsClass: { default: {} },
    filterServerNeedParams: { default: () => () => {} },
  },
  props: {
    needTransition: Boolean,
    isDisabledScreenMode: Boolean,
    breadcrumbHeight: {
      type: Number
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    modalBool: {
      type: Boolean,
      default: false,
    },
    superLinkArr: {
      type: Array,
      default: () => []
    },
    elList: {
      type: Array,
      default: () => []
    },
    paramsPanelList: {
      type: Array,
      default: () => []
    },
    boardInfo: {
      type: Object,
    },
    titleStyle: {
      type: Object,
    },
    layoutDeploy: {
      type: Object,
    },
    init: {
      type: Boolean,
      default: true,
    },
    themeFullScreen: {
      type: Boolean,
      default: false,
    },
    toggle: {
      type: Number,
    },
    dynamicTags: {
      type: Array,
      required: true
    },
    appData: {
      type: Object,
      default: () => {}
    },
    languageAndCurrency: {
      type: Object,
      default: () => {}
    },
    isFinish: {
      type: Boolean
    },
    recordRunTimes: {
      type: Number
    },
    isScreenSkipToScreen: {
      type: Boolean
    },
    isHideTitle: {
      type: Boolean,
      default: false
    },
    hideSupernatantTop: {
      type: Number,
      default: 0
    },
    parent: {
      type: Object
    },
    watermarkUrl: {
      type: String,
      default: ''
    },
    isChangePos: {
      type: Boolean,
      default: false
    },
    storeSet: {
      type: Object,
      default: () => ({}),
    },
    dailyConcernData: {
      type: Object,
      default: () => ({}),
    },
    isHomePageFlag: {
      type: Boolean,
      default: false
    },
  },
  mixins: [horizontalView, paramElementDisableMixins, kanbanGuideStepEntryPanelMixin, refreshMixin],
  components: {
    ExportTableData,
    AddDailyConcernDialog,
    BoardRemark,
    scalePopover,
    customerElementDialog,
    dupontAnalysisDialog,
    TextSettingDialog,
    mobileFull,
    ExportProgressDialog,
    // elTools,
    SupernatantEl,
    elementTable,
    elementChart,
    elementImage,
    elementFourQuadrant,
    elementContainer,
    elementTagNewCard,
    elementCombineCard,
    elementText,
    elementTime,
    elementWeb,
    elementScrollText,
    elementMaterialLibrary,
    elementTitle,
    chartDialog,
    elementFourQuadrantDialog,
    chartContainerDialog,
    elementTagNewCardDialog,
    combineCardDialog,
    tableDialog,
    layout,
    SdpGridItemHeader,
    SdpGridItemBody,
    // fullScreenInfo,
    LoadingMiddleWare,
    pcFullscreen,
    appFullscreen,
    FilterSorterDialog,
    ContainerFilterSortDialog,
    CopyElementsToTabsDialog,
    MobilePopover,
    largeScreen,
    DataReportLayout,
    ExportDialog,
    RemarkDialog,
    AddToTemplate,
    SvgSuccess
  },
  data() {
    return {
      containerRunTab: {},
      isExportTableVisible: false,
      exportTableElement: null,
      progressId: '',
      updateTimeInfoVisible: false,
      CAN_SET_PADDING_TYPES,
      TYPE_ELEMENT,
      communication: new Communication(this.receiveIframePassData),
      isShowTemplate: false,
      boardRemarkVisible: false,
      addDailyElementSelectedList: [],
      treeData: [],
      templateElement: {},
      newElementContent: {
        tableControlsLanguageList: [],
        tableControlsElementLanList: []
      },
      isReady: false,
      enlargeVisible: false,
      containerPreviewVisible: false,
      TYPE_CHART_CONTAINER_DIALOG,
      MATERIAL_NO_BACKGROUND,
      componentNameList,
      load: true,
      enlargeId: '',
      settingDialogRef: '',
      savedTimeInfo: {},
      positionList: ['left', 'right'],
      elTools: {
        moreBtn: {
          icon: 'icon-sdp-yulanshisandian',
          title: this.$t('sdp.button.more')
        },
        containerBtn: {
          icon: 'icon-sdp-rongqishichuang',
          title: this.$t('sdp.button.preview'),
          onClick: (element) => {
            const { type } = element

            // 容器处理逻辑
            if (type === TYPE_ELEMENT.CONTAINER) {
              this.containerPreviewVisible = true
              if (!this.isScreen) {
                this.$set(element.content, 'isContainerEdit', true) // 判断是否为正在编辑的容器
                this.$refs['containerPreviewRef'].setData(element)
              } else {
                this.commonData.isAdvanceContainerEdit = true
                this.$set(element.content, 'isAdvanceContainerEdit', true) // 判断是否为正在编辑的容器
                this.boardRecord && this.boardRecord.clear() // 进入大屏容器编辑界面后把记录清除
              }
              return
            }

            this.elSettingDialog = 'combine-card-dialog'
            this.settingDialogRef = this.$_toCamel(this.elSettingDialog)

            this.elSettingProps.type = TYPE_CHART_CONTAINER_DIALOG.preview

            setTimeout(() => {
              this.elSettingVisible = true
              this.nowElementId = element.id
              this.$nextTick(() => {
                const fn = elContentSetHelper[type]
                fn && fn.call(this, element)
              })
            }, 100)
          }
        },
        btns: [
          {
            icon: 'icon-sdp-kanbanbianji1 cy-elToolsItemEdit',
            buttonKey: 'elementEdit',
            title: this.$t('sdp.button.edit'),
            onClick: this.elToolsEditHandler,
          },
          {
            icon: 'icon-sdp-sdp-kanbanfuzhi cy-elToolsItemEdit',
            buttonKey: 'elementCopy',
            title: this.$t('sdp.button.copy'),
            onClick: this.elToolsCopyHandler,
          },
          {
            icon: 'icon-sdp-tuxingshaixuan',
            buttonKey: 'dataFilter',
            title: this.$t('sdp.button.filter'),
            // onClick: this.containerSetHandler,
          },
          {
            icon: 'icon-sdp-tuxingpaixu',
            buttonKey: 'dataSort',
            title: this.$t('sdp.button.sort'),
            // onClick: this.containerSetHandler,
          },
          {
            icon: 'icon-sdp-guizeyinqing',
            buttonKey: 'elementSetting',
            title: this.$t('sdp.table.set'),
            onClick: this.filterSortHandler,
          },
          {
            icon: 'icon-sdp-zidingyibianjimingming',
            buttonKey: 'elementToTemplate',
            title: this.$t('sdp.views.template'),
            onClick: this.addToTemplate,
          },
          {
            icon: 'icon-sdp-kanbanshanchu',
            buttonKey: 'elementDelete',
            title: this.$t('sdp.button.delete'),
            onClick: this.elToolsDeleteHandlerWithRecord,
          }
        ],
        moreBtns: [
          { // sbi添加到每日指标
            icon: 'icon-sdp-add',
            title: this.$t('sdp.button.addToDaily'),
            isShow: false,
            onClick: (el) => {
              this.addToDailyConcernMetrics(el)
            }
          },
          { // sbi更新到每日指标
            icon: 'icon-sdp-gengxin',
            title: this.$t('sdp.button.updateToDaily'),
            isShow: false,
            onClick: (el) => {
              this.addToDailyConcernMetrics(el, { isUpdate: true })
            }
          },
          {
            icon: 'icon-sdp-hoverkanbanshuaxin font14',
            buttonKey: 'refreshElement',
            title: this.$t('sdp.button.refresh'),
            isShow: true,
            onClick: (el) => {
              let idList = el.type === TYPE_ELEMENT.CONTAINER ? getContainerIncludeElsIdList(el) : [el.id]

              const data = { ids: idList }
              // 解决bug 7683 判断该看板是否未被影响交互 刷新带上被影响交互数据
              // const eventData = new EventData({
              //   type: REFRESH_COMPONENT,
              //   target: ['displayPanel', 'displayPanelMobile'],
              //   targetFn: 'refreshEl',
              //   data,
              // })
              // this.$emit('eventBus', eventData)

              this.refreshEl(data)
            }
          },
          {
            icon: 'icon-sdp-sdp-dakaiyuankanban',
            title: this.$t('sdp.button.JumpOriginalBoard'),
            isShow: true,
            onClick: (el) => {
              if (el?.[SBI_ATTR].isInvalid === '1' && el?.[SBI_ATTR].invalidReason !== '0') {
                this.$message.warning(this.$t('sdp.message.OriginalNoardNotFound'))
                return
              }
              if (el?.[SBI_ATTR].isInvalid === '1' && ['2', '3'].includes(el?.[SBI_ATTR].invalidReason)) {
                this.$message.warning(this.$t('sdp.message.NoOriginalBoardPermission'))
                return
              }
              if (el?.[SBI_ATTR].boardInfo) {
                const { folderId = '', name = '', code = '' } = el[SBI_ATTR].boardInfo
                const dashboardId = el[SBI_ATTR].dashboardId
                const elementOriginalId = el[SBI_ATTR].elementOriginalId
                const personalAttentionType = el[SBI_ATTR].personalAttentionType
                const paramsPanelActiveId = el[SBI_ATTR].paramsPanelActiveId
                window.open(`#/home?id=${dashboardId}&fid=${folderId}&name=${encodeURIComponent(name)}&code=${encodeURIComponent(code)}&targetElementId=${elementOriginalId}&personalAttentionType=${personalAttentionType}&paramsPanelActiveId=${paramsPanelActiveId}`, '_blank')
                // window.open(`#/home?id=${this.encrypt(dashboardId)}&fid=${this.encrypt(folderId)}&name=${name}&code=${code}&targetElementId=${el.id}`, '_blank')
                // 选中目标元素 targetElementId 到时候会存在boardInfo
                // 调试代码  放到对应位置处
                // packages/base/board/displayPanel/index.ts:2184 initBoard
                // if (boardInfo) {
                //   boardInfo.id = '645651525850353664714'
                //   boardInfo.targetElementId = '8980659f-8ddb-4a69-d1df-9e7d386af457'
                //   boardInfo.paramsPanelActiveId = '61bff2a6-d684-4731-8664-5ba01c7c38f4'
                // }
              } else {
                return console.warn('数据缺失，跳转失败！')
              }
            }
          },
          {
            icon: 'icon-sdp-sdp-canshuxinxi',
            title: '',
            isShow: false,
            // onClick: '',
          },
          // {
          //   icon: 'icon-sdp-tubiaoquanping',
          //   title: this.$t('sdp.button.fullScreen'),
          //   isShow: false,
          //   onClick: (element) => {
          //     this.toggleFullScreen(element)
          //   }
          // },
          {
            icon: 'icon-sdp-yulanshifangda',
            buttonKey: 'enlargeInPreview',
            title: this.$t('sdp.button.enlargement'),
            isShow: false,
            onClick: (el) => {
              this.handleEnlargeEl(el.id)
            }
          },
          {
            icon: 'icon-sdp-tuxingshaixuan',
            buttonKey: 'dataFilter',
            title: this.$t('sdp.button.filter'),
            isShow: true,
            // onClick: '',
          },
          {
            icon: 'icon-sdp-tuxingpaixu',
            buttonKey: 'dataSort',
            title: this.$t('sdp.button.sort'),
            isShow: true,
            // onClick: '',
          },
          // hbw 跟新时间
          {
            icon: 'icon-sdp-sdp-gengxinshijian',
            buttonKey: 'refreshTime',
            title: '',
            content: `        `,
            loading: false,
            that: this,
            fetchUpdateTime: async (element, elementId, btn) => {
              if (btn.loading) return
              let that = btn.that
              if (that.init) { // 未完成run或者未完成请求时不发送请求
                btn.content = `${that.$t('sdp.button.updateTime')}: - -`
              } else {

                let ids = []
                if (element.type === TYPE_ELEMENT.CONTAINER) {
                  let els = that.elList.filter(v => getContainerIncludeElsIdList(element).some(i => i === v.id))

                  els.map(v => {
                    ids.push(...actionGetDataSetIds(v.type, v.content))
                  })
                  ids = Array.from(new Set(ids))
                } else {
                  ids = actionGetDataSetIds(element.type, element.content)
                }
                if (that.savedTimeInfo[element.id] !== undefined) {
                  if (ids.every(v => that.savedTimeInfo[element.id].dataSetIds.some(i => i === v)) && that.savedTimeInfo[element.id].recordRunTimes === that.recordRunTimes) {
                    btn.content = `${that.savedTimeInfo[element.id].res.dataUpdateTipsSwitch}` === '0' ? `${that.$t('sdp.button.updateTime')}:  ${that.savedTimeInfo[element.id].res.recentTableTime}` : `${that.$t('sdp.button.updateTime')}:  ${that.savedTimeInfo[element.id].res.recentTableTime}; ${that.$t('sdp.views.recentTableTimeFirst')} ${that.savedTimeInfo[element.id].res.dataUpdateTips} ${that.$t('sdp.views.recentTableTimeLast')}`
                    return
                  }
                }
                if (this.utils?.env?.projectName === ALL_PROJECT_NAME.SUBSYSTEM) return

                try {
                  btn.loading = true
                  let res = await api.fetchDatasetUpdateTime(that.utils.api, {
                    dataSetIds: ids,
                    tenantId: that.utils.tenantId
                  })
                  let obj = {}
                  obj.dataSetIds = ids
                  obj.res = res
                  obj.recordRunTimes = that.recordRunTimes
                  that.$set(that.savedTimeInfo, element.id, obj)
                  btn.content = res.dataUpdateTipsSwitch === '0' ? `${that.$t('sdp.button.updateTime')}:  ${res.recentTableTime};` : `${that.$t('sdp.button.updateTime')}:  ${res.recentTableTime}; ${that.$t('sdp.views.recentTableTimeFirst')} ${res.dataUpdateTips} ${that.$t('sdp.views.recentTableTimeLast')}`
                } catch (e) {
                  console.log(e, 'test')
                  btn.content = `${that.$t('sdp.button.updateTime')}: - -`
                } finally {
                  btn.loading = false
                }
              }
            },
            mobileFetchUpdateTime: async (element, elementId, btn) => {
              console.log('mobileFetchUpdateTime')
              // 有无可优化的方案?
              this.$parent.$parent.updateTimeInfoPopupText = btn.content
            //  let index =  this.elList.findIndex((item)=>{
            //     return elementId === item.id
            //   })
              if (btn.loading) return
              let that = btn.that
              if (that.init) { // 未完成run或者未完成请求时不发送请求
                 this.$parent.$parent.updateTimeInfoPopupText = btn.content = `${that.$t('sdp.button.updateTime')}: - -`
                 this.$parent.$parent.updateTimeInfoVisible = true
                 this.$parent.$parent.$refs.updateTimeInfoPopup.show()
              } else {

                let ids = []
                if (element.type === TYPE_ELEMENT.CONTAINER) {
                  let els = that.elList.filter(v => getContainerIncludeElsIdList(element).some(i => i === v.id))

                  els.map(v => {
                    ids.push(...actionGetDataSetIds(v.type, v.content))
                  })
                  ids = Array.from(new Set(ids))
                } else {
                  ids = actionGetDataSetIds(element.type, element.content)
                }
                if (that.savedTimeInfo[element.id] !== undefined) {
                  if (ids.every(v => that.savedTimeInfo[element.id].dataSetIds.some(i => i === v)) && that.savedTimeInfo[element.id].recordRunTimes === that.recordRunTimes) {
                     this.$parent.$parent.updateTimeInfoPopupText = btn.content = `${that.savedTimeInfo[element.id].res.dataUpdateTipsSwitch}` === '0' ? `<p>${that.$t('sdp.button.updateTime')}:</p>  <p>${that.savedTimeInfo[element.id].res.recentTableTime}</p>` : `<p>${that.$t('sdp.button.updateTime')}:</p>  <p>${that.savedTimeInfo[element.id].res.recentTableTime};</p> <p>${that.$t('sdp.views.recentTableTimeFirst')} ${that.savedTimeInfo[element.id].res.dataUpdateTips} ${that.$t('sdp.views.recentTableTimeLast')}</p>`
                     this.$parent.$parent.updateTimeInfoVisible = true
                     this.$parent.$parent.$refs.updateTimeInfoPopup.show()
                    return
                  }
                }
                if (this.utils?.env?.projectName === ALL_PROJECT_NAME.SUBSYSTEM) return

                try {
                  btn.loading = true
                  let res = await api.fetchDatasetUpdateTime(that.utils.api, {
                    dataSetIds: ids,
                    tenantId: that.utils.tenantId
                  })
                  let obj = {}
                  obj.dataSetIds = ids
                  obj.res = res
                  obj.recordRunTimes = that.recordRunTimes
                  that.$set(that.savedTimeInfo, element.id, obj)
                   this.$parent.$parent.updateTimeInfoPopupText = btn.content = res.dataUpdateTipsSwitch === '0' ? `<p>${that.$t('sdp.button.updateTime')}:</p>  <p>${res.recentTableTime};</p>` : `<p>${that.$t('sdp.button.updateTime')}:</p>  <p>${res.recentTableTime};</p>  <p>${that.$t('sdp.views.recentTableTimeFirst')} ${res.dataUpdateTips} ${that.$t('sdp.views.recentTableTimeLast')}<p>`
                    this.$parent.$parent.updateTimeInfoVisible = true
                    this.$parent.$parent.$refs.updateTimeInfoPopup.show()
                } catch (e) {
                  console.log(e, 'test')
                   this.$parent.$parent.updateTimeInfoPopupText = btn.content = `${that.$t('sdp.button.updateTime')}: - -`
                   this.$parent.$parent.updateTimeInfoVisible = true
                   this.$parent.$parent.$refs.updateTimeInfoPopup.show()
                } finally {
                  btn.loading = false
                }
              }
            }
          },
          {
            icon: 'icon-sdp-kanbanbianji1',
            buttonKey: 'elementEdit',
            title: this.$t('sdp.views.comment'),
            isShow: !this.utils?.intelligentData?.isIntelligentComponent,
            onClick: this.elToolsRemarkHandler,
          },
          {
            icon: 'icon-sdp-con-sdp-dingyue-16',
            buttonKey: 'elementSubscription',
            isShow: true,
            title: this.$t('sdp.button.elementSubscription'),
            onClick: (element) => {
              this.handleElementSubscription(element)
            }
          },
          {
            icon: 'icon-sdp-rongqidaochu',
            buttonKey: 'containerExport',
            isShow: true,
            title: this.$t('sdp.button.export'),
            onClick: (element) => {
              const paramsPanel = this.parent.paramsPanel
              if (!paramsPanel?.finishResult) {
                return this.$message({
                  message: this.$t('sdp.views.componentUndone'),
                  duration: 1000,
                })
              }
              this.callExportDialog({ data: element })
            }
          },
          {
            icon: 'icon-sdp-xianshishuzhi',
            buttonKey: 'dataValueDisplay',
            title: this.$t('sdp.button.ValueDisplay'),
            isShow: true,
            onClick: (e) => { // e 工具栏对应的元素
              const btnChange = () => {
                const show = this.elTools.moreBtns.find(item => item.title === this.$t('sdp.button.ValueDisplay'))
                show && this.$set(show, 'isShow', false)
                const hide = this.elTools.moreBtns.find(item => item.title === this.$t('sdp.button.ValueHidden'))
                hide && this.$set(hide, 'isShow', true)
              }
              // 图形直接隐藏
              if (e.type === TYPE_ELEMENT.CHART) {
                const showValue = this.$_getProp(e, 'vm.labelLineButtonListChange', false)
                if (showValue !== false) {
                  showValue(false)
                  btnChange()
                }
              } else if (e.type === TYPE_ELEMENT.CONTAINER) {
                // 容器 分高级 普通容器  普通容器分横向 纵向
                if (e.subType !== undefined && e.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
                  const curTab = e.content.activeTabId
                  e.content.tabList.forEach(item => {
                    if (item.name === curTab) {
                      const includedElsIdList = item.content.includedElsIdList
                      this.elList.forEach(el => {
                        if (includedElsIdList.length && includedElsIdList.includes(el.id)) {
                          if (el.type === TYPE_ELEMENT.CHART) {
                            const chart = el
                            const showValue = this.$_getProp(chart, 'vm.labelLineButtonListChangeAll', false)
                            if (showValue !== false) {
                              showValue(false)
                              btnChange()
                            }
                          }
                        }
                      })
                    }
                  })
                } else {
                  if (e.content.settings.displayMode === 'horizontal') {
                    let chart = null
                    for (let i = 0; i < this.elList.length; i++) {
                      if (this.elList[i].id === e.content.activeElId) {
                        chart = this.elList[i]
                        break
                      }
                    }
                    const showValue = this.$_getProp(chart, 'vm.labelLineButtonListChange', false)
                    if (showValue !== false) {
                      showValue(false)
                      btnChange()
                    }
                  } else if (e.content.settings.displayMode === 'vertical') {
                    for (let i = 0; i < this.elList.length; i++) {
                      if (e.content.includedElIds.includes(this.elList[i].id)) {
                        if (this.elList[i].type === TYPE_ELEMENT.CHART) {
                          const chart = this.elList[i]
                          const showValue = this.$_getProp(chart, 'vm.labelLineButtonListChangeAll', false)
                          if (showValue !== false) {
                            showValue(false)
                            btnChange()
                          }
                        }
                      }
                    }
                  }
                }
              }
            },
          },
          {
            icon: 'icon-sdp-buxianshishuzhi',
            buttonKey: 'dataValueHidden',
            title: this.$t('sdp.button.ValueHidden'),
            isShow: false,
            onClick: (e) => {
              const btnChange = () => {
                const show = this.elTools.moreBtns.find(item => item.title === this.$t('sdp.button.ValueDisplay'))
                show && this.$set(show, 'isShow', true)
                const hide = this.elTools.moreBtns.find(item => item.title === this.$t('sdp.button.ValueHidden'))
                hide && this.$set(hide, 'isShow', false)
              }
              // 图形直接隐藏
              if (e.type === TYPE_ELEMENT.CHART) {
                const hideValue = this.$_getProp(e, 'vm.labelLineButtonListChange', false)
                if (hideValue !== false) {
                  hideValue(true)
                  btnChange()
                }
              } else if (e.type === TYPE_ELEMENT.CONTAINER) {
                // 容器 分高级 普通容器  普通容器分横向 纵向
                if (e.subType !== undefined && e.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
                  const curTab = e.content.activeTabId
                  e.content.tabList.forEach(item => {
                    if (item.name === curTab) {
                      const includedElsIdList = item.content.includedElsIdList
                      this.elList.forEach(el => {
                        if (includedElsIdList.length && includedElsIdList.includes(el.id)) {
                          if (el.type === TYPE_ELEMENT.CHART) {
                            const chart = el
                            const hideValue = this.$_getProp(chart, 'vm.labelLineButtonListChangeAll', false)
                            if (hideValue !== false) {
                              hideValue(true)
                              btnChange()
                            }
                          }
                        }
                      })
                    }
                  })
                } else {
                  if (e.content.settings.displayMode === 'horizontal') {
                    let chart = null
                    for (let i = 0; i < this.elList.length; i++) {
                      if (this.elList[i].id === e.content.activeElId) {
                        chart = this.elList[i]
                        break
                      }
                    }
                    const hideValue = this.$_getProp(chart, 'vm.labelLineButtonListChange', false)
                    if (hideValue !== false) {
                      hideValue(true)
                      btnChange()
                    }
                  } else if (e.content.settings.displayMode === 'vertical') {
                    for (let i = 0; i < this.elList.length; i++) {
                      if (e.content.includedElIds.includes(this.elList[i].id)) {
                        if (this.elList[i].type === TYPE_ELEMENT.CHART) {
                          const chart = this.elList[i]
                          const hideValue = this.$_getProp(chart, 'vm.labelLineButtonListChangeAll', false)
                          if (hideValue !== false) {
                            hideValue(true)
                            btnChange()
                          }
                        }
                      }
                    }
                  }
                }
              }
            },
          }
        ]
      },
      eventData: {
        type: 'click',
        data: 'hah',
        source: 'displayPanel',
        options: {
          callback(data) {
            console.log('callback ... ', data)
          }
        }
      },
      highlightItem: {},
      // 当前请求id
      requestIds: {},
      elSettingDialog: '',
      elSettingVisible: false, // 配置图表属性
      elSettingProps: {
        dynamicTags: this.dynamicTags,
        elId: ''
      }, // 传入给看板元素设置组件的props属性
      chartData: {},
      elementComp: [],
      fullscreen: false,
      fullscreenId: '',
      // supernatantWidth: 0,
      isEnlarge: false,
      supernatantStyle: { opacity: 0 },
      containerIdElList: [],
      IsVertical: true,
      screenType: 'vertical',
      fullScreenStatus: false,
      nowElementId: '',
      carouselAttr: {
        autoplay: false,
        interval: 0
      },
      enlargeEl: null,
      element: [],
      elementBox: [],
      enlargeLoading: false,
      reTitleStyle: {},
      isSidebarClick: false,
      filterSorterVisible: false,
      containerFilterSorterVisible: false,
      remarkVisible: false, // 看板元素备注
      addDailyConcernDialogVisible: false, // 每日关注tab选择弹窗
      addDailyConcernDialogElement: null, // 每日关注tab选择元素
      copyElementsToTabsVisible: false,
      copyElement: null,
      isShowTip: false,
      isMessageDialog: false,
      // isShowDddSuccessTip: false,
      isShowAddSuccessTip: false,
      boardScale: 100,
      oldBoardScale: 100,
      boardScaleChangeFn: () => {},
    }
  },
  computed: {
    activeTabId() {
      return this.dynamicTags.find(tag => tag.active)?.id
    },
    isDataReport() {
      return this.utils.isDataReport
    },
    isLargeScreen() {
      return this.utils.isLargeScreen
    },
    dataReport() {
      if (this.isDataReport) {
        return this.getDataReport() || null
      } else {
        return null
      }
    },
    isDataReportDing() {
      return this.commonData.isPreview &&
              this.isDataReport &&
              this.commonData.staticParamsPanel &&
              !this.commonData.freezeParamsPanel
    },
    visibleCopyMenuChildren() {
      // 看板标签页模式关闭时才支持复制元素到指定tab页
      if (this.boardInfo.openBoardTab && !this.isDataReport) {
        return false
      } else if (!this.dynamicTags || this.dynamicTags.length === 0) {
        return false
      } else if (this.dynamicTags.length > 1) {
        return true
      } else if (this.dynamicTags.length === 1) {
        if (!this.isDataReport) return false
        return this.dataReport?.getPageOrder(this.dynamicTags[0].id).length > 1
      } else {
        return false
      }
    },
    isTvScreen() {
      return this.utils.isTvScreen
    },
    animateClass(tag) {
      return function(tag) {
        return tag?.canvasBg?.animate || ''
      }
    },
    isSafari() {
      const browserInfo = getBrowserInfo()
      return browserInfo.browser === 'Safari' && !this.isMobile
    },
    isShowBreadcrumb() {
      const len = this.superLinkArr.length
      if (this.modalBool) {
        if (this.dialogVisible && len === 1) {
          return false
        } else {
          return !!len
        }
      }
      // 弹窗会获得上一个看板数据
      return !!(len - 1)
    },
    isShowScreenTopBox() {
      if (this.isScreen) {
        return this.isShowLargeTabs || this.isShowBreadcrumb
      }
      return !this.isMobile && this.themeFullScreen
    },
    fullscreenStyle() {
      if (this.enlargeEl) {
        // 直接从黑色/白色主题下复制的看板  在深邃蓝的背景下没有设置themeData的数据，放大背景色会有问题 应该设置默认
        const themeType = this.themeData?.appThemeType
        const defaultThemeBgc = BOARD_ELEMENT_THEME[themeType]
        const themeMap = this.enlargeEl?.element?.themeMap?.[themeType] || defaultThemeBgc
        let bgc = themeMap.mobileBgc
        // 判断是否使用渐变色
        const styleConfig = this.enlargeEl?.element?.styleConfig || {}
        if (styleConfig.fillColor) {
          if (styleConfig.fillColor.type === MATERIAL_COLOR_TYPE.GRADIENT) {
            themeMap.fillColorType = styleConfig.fillColor.type
            themeMap.fillGradientAngle = styleConfig.fillColor.linear.angle
          }
        }
        if (themeMap?.fillColorType === MATERIAL_COLOR_TYPE.GRADIENT) {
          themeMap.type = themeMap.fillColorType
          themeMap.angle = themeMap.fillGradientAngle
          bgc = this.getElEnlargementBgColor(themeMap)
        }
        return {
          background: bgc
        }
      } else {
        return {}
      }
    },
    fullscreenComponent() {
      return (this.isMobile ? 'app' : 'pc') + 'Fullscreen'
    },
    contentInfoStyleNum() {
      return this.isChangePos ? 0 : 8
    },
    contentInfoStyle() {
      return {
        marginBottom: `${this.contentInfoStyleNum}px`
      }
    },
    breadcrumbHeights() {
      return this.breadcrumbHeight && (this.breadcrumbHeight + this.contentInfoStyleNum)
    },
    pcTabsHeight() {
      if (this.commonData.isPreview && this.isScreen && this.boardInfo.tabSuspension) return 0
      if (this.utils.isDailyConcerned) return 40
      if (this.themeFullScreen) {
        return this.isShowLargeTabs && this.dynamicTagsGreaterOne ? 26 : 0
      }
      return this.commonData.isPreview ? 26 : 40
    },
    pcTabsHeights() {
      return this.pcTabsHeight && (this.pcTabsHeight + this.contentInfoStyleNum)
    },
    isCloseCarousel() {
      return this.$_getProp(this.boardInfo, 'screenModeDate.isCloseCarousel', false)
    },
    isOpenElementsTabClick() {
      return this.$_getProp(this.boardInfo, 'screenModeDate.isOpenElementsTabClick', false)
    },
    dynamicTagsGreaterOne() {
      return this.dynamicTags.length > 1
    },
    isShowLargeTabs() {
      if (this.utils.isTvScreen) {
        return false
      }
      return (this.isCloseCarousel || this.isOpenElementsTabClick) && this.themeFullScreen && this.dynamicTagsGreaterOne
    },
    // 屏幕留白的设置
    screenSpaceMode() {
      return this.boardInfo.screenModeDate.screenSpaceMode
    },
    // 大屏模式 拉伸、自适应 屏幕留白的修正值
    paddingFix() {
      let num = 0
      if (this.screenSpaceMode) {
        num = this.paddingData.top + this.paddingData.bottom
      }
      return num
    },
    // 根据屏幕留白设置对高度的转换
    paddingData() {
      let height = window.screen.height
      const { TopPadding = 0, BottomPadding = 0 } = this.boardInfo.screenModeDate || {}
      const getPadding = (p) => this.screenSpaceMode ? (parseInt(String(height * p / 100)) || 0) : 0
      return {
        top: getPadding(TopPadding),
        bottom: getPadding(BottomPadding)
      }
    },
    dynamicIndex() {
      return this.dynamicTags.findIndex(v => v.active)
    },
    dynamicTagsClone() {
      let newArr = []
      this.dynamicTags.map(v => {
        newArr.unshift(v)
      })
      return newArr
    },
    visibleElList() {
      return this.elList.filter(el => {
        return this.commonData?.isAdvanceContainerEdit ? el : !el._containerId
      })
    },
    chartSet() {
      return this.$refs[this.settingDialogRef]
    },
    isPreview() {
      // 初始化layout格式
      // this.handleResize()
      return this.commonData.isPreview
    },
    isDivideTabs() { // 预览中有分页和没分页的样式要做区分
      return this.dynamicTags.length > 1
    },
    title() {
      return this.boardInfo.nameSub || ''
    },
    flattenElementComp() {
      // return this.$_flatten(this.elementComp).filter(e => e)
      return this.getNewestFlattenElementComp()
    },
    isMobile() {
      return this.utils.isMobile
    },
    isApp() {
      return this.commonData.isMobileApp
    },
    infoHeight() {
      if (this.isScreen) {
        return 0
      }
      return 14
    },
    infoHeightPx() {
      return this.infoHeight + 'px'
    },
    fullscreenPaddingTop() {
      return 8
    },
    fullscreenPaddingTopPX() {
      return this.fullscreenPaddingTop + 'px'
    },
    titleStyleLineHeight() {
      return 1.2
    },
    titleStyleDefaultFontSize() {
      return 12
    },
    titleStyleDefaultFontSizePX() {
      return this.titleStyleDefaultFontSize + 'px'
    },
    fullscreenTitleStyle() {
      return {
        ...this.reTitleStyle,
        lineHeight: this.titleStyleLineHeight,
      }
    },
    fullscreenTitleHeight() {
      const themeFullScreen = this.themeFullScreen
      const settingTitle = this.$_getProp(this.boardInfo, `tagModeStack.settingTitle`, false)
      const refreshData = this.$_getProp(this.boardInfo, `screenModeDate.refreshData`, {})
      const midware = this.$_getProp(this.boardInfo, `tagModeStack.midware`, false)
      const visibleHeight = this.fullscreenPaddingTop
      const getData = (id) => {
        const { groupList = [] } = refreshData[id] || {}
        return groupList.reduce((pre, next) => {
          if (next) pre[next.name] = next
          return pre
        }, {})
      }

      if (settingTitle) {
        return Object.entries(settingTitle).reduce((pre, [key, value]) => {
          const { id } = midware[key]
          const { dataDate = {}, dataRefreshTime = {}, propertyNumber = {}, dataUpdateTime = {}, weatherData = {} } = getData(key)
          const obj = {
            isShow: false,
            height: visibleHeight
          }
          if (themeFullScreen) {
            const { currentLang, title } = value
            if (currentLang || title) {
              const fontSize = parseFloat(value.Tstyle['font-size']) || this.titleStyleDefaultFontSize
              obj['height'] += fontSize * this.titleStyleLineHeight + this.contentInfoStyleNum
              obj.isShow = true
            }
            if (!!dataRefreshTime.show || !!dataUpdateTime.show || !!propertyNumber.show || !!dataDate.show || !!weatherData.show) {
              obj['height'] += this.infoHeight + this.contentInfoStyleNum
              obj.isShow = true
            }
          }
          pre[id] = obj
          return pre
        }, {})
      } else {
        const { id = '' } = this.paramsPanelList.find(item => item.active) || {}
        const { dataDate = {}, dataRefreshTime = {}, propertyNumber = {}, dataUpdateTime = {}, weatherData = {} } = getData(id)
        const obj = {
          isShow: false,
          height: visibleHeight,
        }
        if (themeFullScreen) {
          if (this.title.trim().length) {
            const fontSize = parseFloat(this.fullscreenTitleStyle['font-size']) || this.titleStyleDefaultFontSize
            obj['height'] += fontSize * this.titleStyleLineHeight + this.contentInfoStyleNum
            obj.isShow = true
          }
          if (!!dataRefreshTime.show || !!dataUpdateTime.show || !!propertyNumber.show || !!dataDate.show || !!weatherData.show) {
            obj['height'] += this.infoHeight + this.contentInfoStyleNum
            obj.isShow = true
          }
        }
        return this.dynamicTags.reduce((pre, { id }) => {
          pre[id] = obj
          return pre
        }, {})
      }
    },
    marginHrPX() {
      return this.layoutDeploy.margin[0] + 'px'
    },
    isWeAppAndWeb() {
      return this.$_getProp(this, 'appData.appType', '') === TARO_ENV_TYPE.WEAPP || this.$_getProp(this, 'appData.appType', '') === TARO_ENV_TYPE.WEB
    },
    isHorizontal() {
      return this.commonData.isHorizontal
    },
    isScreen() {
      return this.utils.isScreen
    },
    isRealScreen() {
      return isRealScreenFn(this.utils)
    },
    isShowSlider() {
      return this.isRealScreen && !this.isPreview
    },
    fullscreenType() {
      if ((this.utils.isMobile && this.utils.isPcMobile) || this.commonData.isMobileDataReport()) return 'layout'
      if (this.dataReport) return 'DataReportLayout'
      return this.isScreen ? 'largeScreen' : 'layout'
    },
    isShowWatermark() {
      return this.commonData.isPreview && this.watermarkUrl
    },
    enlargementColorFormatter() { // 元素放大背景色设置
      const themeType = this.utils.themeParameters.themeType
      if (!this.isMobile && this.enlargeVisible) {
        const imgUrl = this.boardInfo?.elementAmplificationBg?.imgData?.url
        if (imgUrl) {
          return { background: `url(${this.$_getAssetsUrl(imgUrl)}) 0% 0%/100% 100% no-repeat` }
        } else {
          const themeMap = this.boardInfo?.elementAmplificationBg?.[themeType]
          const bg = themeMap ? this.getElEnlargementBgColor(themeMap, true) : undefined
          return { background: bg }
        }
      }
      // !已经弃用 使用elementAmplificationBg
      // if (!this.isMobile && this.enlargeVisible) {
      // const enlargementPcStyle = this.boardInfo?.enlargementOpts?.themeStyleMap?.[themeType] || ''
      // return { backgroundColor: enlargementPcStyle }
      // }
      return {}

    },
    isEnlargeVisible() {
      return this.enlargeVisible || this.fullscreen
    }
  },
  watch: {
    refershComponentKey() {
      this.gridItemClickHandler()
    },
    refershComponentIds(ids) {
      const hasIds = this.elList.filter(el => ids.includes(el.id) && ![TYPE_ELEMENT.CONTAINER, TYPE_ELEMENT.CHART].includes(el.type)).map(el => el.id)

      setTimeout(() => {
        this.refreshEl({ ids: hasIds }, 'refreshElByForce')
      })
    },
    dataReport: {
      immediate: true,
      handler() {
        if (this.dataReport) {
          let validIdList = this.dataReport.getAllElIdList()
          ;[...this.elList].forEach(el => {
            if (!validIdList.includes(el.id)) {
              this.removeEl(el)
            }
          })
        }
      }
    },
    'isEnlargeVisible'(val) {
      if (!this.isScreen && !this.isPreview) return
      if (val) {
        this.parent.carouselClear && this.parent.carouselClear()
      } else {
        this.parent.openCarouselClick && this.parent.openCarouselClick()
      }
    },
    dynamicIndex: {
      handler(val) {
        this.$nextTick(() => {
          if (!this.$refs.dynamicTags) return
          const index = this.$refs.dynamicTags.findIndex(el => el.active)
          const carousel = this.$refs.carousel
          if (carousel) {
            carousel.setActiveItem(index)
          }
        })
      },
      deep: true
    },
    visibleElList(val) {
      this.$nextTick(() => {
        this.initElementComp(val)
      })
    },
    isPreview(val) {
      if (val) {
        this.flattenElementComp.forEach(el => {
          this.filterIsRenderInteraction(
            {
              el,
              callback: () => {
                const interactionState = this.$_getProp(el, 'element.content.interactionState', false)
                if (interactionState) {
                  const ids = Object.keys(interactionState)
                  this.elList.find(el => {
                    if (ids.includes(el.id) && el.type === TYPE_ELEMENT.CHART) {
                      const gridRef = el?.vm?.getGridRef()
                      if (!gridRef) {
                        el.content.interactionOptions.forEach(item => {
                          this.$delete(item, 'values')
                          item.children && (item.children = [])
                        })
                      }
                    }
                  })
                }
              }
          })
        })
        this.gridItemClickHandler()
        this.reSetTitleStyle()

        this.oldBoardScale = this.boardScale
        this.boardScale = 100
        this.handleBoardScaleChange()
      } else {
        this.oldBoardScale && (this.boardScale = this.oldBoardScale)
        this.handleBoardScaleChange()
      }
    },
    boardScale() {
      this.closeDurativeWithElConfigs()
    },
    titleStyle: {
      handler() {
        this.reSetTitleStyle()
      },
      deep: true,
      immediate: true
    },
    'appData'(val) {
      if (this.isApp) {
        if (this.appData && this.appData.type === 'vertical') {
          // app 分析页面 物理返回退出横屏时才触发
          this.chartFullScreen(this.appData.IsVerticalId)
          this.appData.type = false
        }
      }
    },
    // 'appData.IsVertical'(val) {
    //   if (this.isApp) {
    //     if (this.appData && !this.appData.IsVertical) {
    //       // app 横屏时 重新渲染时才触发
    //       this.indexFullScreen(this.appData.IsVerticalId)
    //     }
    //   }
    // },
    // 'boardInfo.elementExport.exportType.length'(val) {
    //   const elementExport = this.elTools.moreBtns.find(item => item.title === this.$t('sdp.button.export'))
    //   elementExport && this.$set(elementExport, 'isShow', !!val)
    // },
    elSettingVisible(val) {
      val || this.updateParamsPanelBind([this.nowElementId], 'setDatasStatus')
    },
    // 监听是否点击钉住 刷新看板
    // 'commonData.appDingFlag'(val) {
    //   if (this.tenantData?.settingConfig?.appDingFlag === '1') {
    //     this.refreshGridLayout()
    //   }
    // },
    themeFullScreen(val) {
      if (!this.isScreen || this.isMobile) return
      // 大屏取消全屏时 关闭元素放大
      if (!val) {
        const fullscreenDialog = this.$refs.fullscreenDialog
        if (fullscreenDialog) {
          fullscreenDialog.enlargeBeforeFun()
          this.enlargeVisible = false
        }
      }
    },
    elList: {
      handler(val){
        if(val?.length && val.some(v => v.content?.drillSettings?.datasethighLightItemFlag)) {
          this.$set(this,'highlightItem', {})
        }
      },
      deep: true
    }
  },
  mounted() {
    this.$set(this.supernatantStyle, 'opacity', 1)
    this.reSetTitleStyle()
    this.boardScaleChangeFn = this.$_debounce(this.boardScaleChange, 500)
    document.addEventListener('click', this.documentClick)
    this.sdpBus.$on(EVENT_BUS.UPDATE_PARAMS_PANEL_BIND, this.updateParamsPanelBind)
    this.sdpBus.$on(EVENT_BUS.EXPORT_TABLE_DATA, this.handleExportTableData)

    setTimeout(() => {
      if (this.isApp) {
        this.$refs.supernatantContent.style.width = `${window.innerWidth}px`
      }
    }, 500)
  },
  methods: {
    isNormalLiquidfill,
    refreshEl(data, targetFn = 'refreshEl') {
      const eventData = new EventData({
        type: REFRESH_COMPONENT,
        target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
        targetFn,
        data,
        source: componentName
      })
      this.$emit('eventBus', eventData)
    },
    handleAddDailyElementCheck(id, val) {
      if (val) {
        if (!this.addDailyElementSelectedList.includes(id)) {
          this.addDailyElementSelectedList.push(id)
        }
      } else {
        this.$set(this, 'addDailyElementSelectedList', this.addDailyElementSelectedList.filter(e => e !== id))
      }
      this.handleEmitSelectCnt()
    },
    handleEmitSelectCnt() {
      this.eventDataFun({
        target: ['displayPanel'],
        targetFn: 'addElementSelected',
        data: this.addDailyElementSelectedList.length,
      })
    },
    getStepClassName(step) {
      if (step.isDone) {
        return 'is-done'
      } else if (step.isDoing) {
        return 'is-doing'
      } else {
        return 'is-todo'
      }
    },
    getStepState(step) {
      if (step.isDone) {
        return 'Done'
      } else if (step.isDoing) {
        return 'Doing'
      } else {
        return 'Todo'
      }
    },
    getStepStyles(step) {
      if (step.isDone) {
        return { color: 'skyblue' }
      } else if (step.isDoing) {
        return { color: 'red', fontWeight: 'bold' }
      } else {
        return { color: 'orange' }
      }
    },
    callExportDialog({ data }) {
      this.$nextTick(() => {
        this.$refs.exportDialog.setData(data)
      })
    },
    handleBoardScaleChange() {
      console.log('handleBoardScaleChange')
      this.boardScaleChangeFn()
    },
    boardScaleChange() {
      if (this.isScreen) return
      const scale = this.boardScale < 100 ? 1 : this.boardScale / 100
      this.elList.forEach(el => {
        if (el.type === TYPE_ELEMENT.TABLE) {
          if (scale === 1) {
            this.$delete(el, scale)
          } else {
            el.scale = scale
          }
          el.vm?.$refs?.tableRender?.sdpSheet?.setScale?.(scale || 1)?.render?.()
        }
        if (el.type === TYPE_ELEMENT.CHART) {
          // if (el.content.alias === 've-liquidfill') {
          //   if (scale === 1) {
          //     this.$delete(el, scale)
          //   } else {
          //     el.scale = 1 / scale
          //   }
          // }
        }
      })
    },
    // 接收数据
    receiveIframePassData(params) {
      this.sdpBus.$emit(EVENT_BUS.CUSTOMR_DISPATACH_DATA, params)
    },
    handleCarouselChange(index) {
      this.handleAutoChangeTabPanel(index)
    },
    // 自动切换参数组件页
    handleAutoChangeTabPanel(index) {
      const paramsPanel = this.parent.paramsPanel
      if (!paramsPanel?.finishResult) {
        return
      }

      if (this.isPreview && this.utils.isScreen && this.boardInfo.openBoardTab) {
        const tag = this.dynamicTagsClone[index]
        const { midware = {} } = this.$_getProp(this.boardInfo, `tagModeStack`, {})

        for (let [key, val] of Object.entries(midware)) {
          if (val.id === tag.id) {
            const panel = this.paramsPanelList.find(item => item.id === key)
            this.parent.tabPanelChange({ data: panel })
          }
        }
      }
    },
    getElEnlargementBgColor(item, onlyPure = false) {
      let color = '#FFF'
      if (onlyPure) {
        color = item.fillColor
      } else if (item.type === MATERIAL_COLOR_TYPE.PURE) {
        color = item.pureColor
      } else if (item.type === MATERIAL_COLOR_TYPE.GRADIENT) {
        color = `linear-gradient(${item.angle}deg,${item.gradientColor.join(',')})`
      }
      return color
    },
    /**
     * 背景色取值  增加报告取值
     * @param tag
     * @param themeData
     * @returns {{} | {background: string}}
     */
    largeScreenBgFormatter(tag, themeData) {
      if (this.utils.isDataReport && this.dataReport) {
        const data = this.dataReport.getReportSettings(tag.id)
        const stype = largeScreenFormatter(data, 'bgSet', themeData)

        // this.sdpBus.$emit(EVENT_BUS.TOGGLE_BG, largeScreenFormatterBg(data, 'bgSet', themeData), tag.active)
        return stype
      }

      const attr = this.isScreen ? 'canvasBg' : 'elementBoardBg'
      const stype = largeScreenFormatter(tag, attr, themeData)

      // this.sdpBus.$emit(EVENT_BUS.TOGGLE_BG, largeScreenFormatterBg(tag, attr, themeData), tag.active)
      return stype
    },
    largeScreenFormatter(data, formatter, themeData) {
      if (this.isMobile) return {}
      return largeScreenFormatter(data, formatter, themeData)
    },
    encrypt(data) {
      const key = CryptoJS.enc.Utf8.parse(PUBLIC_KEY)
      const iv = CryptoJS.enc.Utf8.parse(PUBLIC_KEY)
      return CryptoJS.AES.encrypt(data, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.ZeroPadding,
      }).toString()
    },
    documentClick() {
      if (this.isShowTip) {
        this.hideCubiTip()
      }
    },
    backHandler() {
      const fn = this.chartData?.vm?.animationInstance?.continue
      fn && fn.call(this.chartData.vm.animationInstance, { status: 'start' })
      this.elSettingVisible = false
    },
    reSetTitleStyle() {
      const themeType = this.themeData.themeType
      const themeDataTitle = this.$_getProp(this.boardInfo, 'content.themeDataTitle', {})
      // const titleStyle = themeDataTitle[themeType] ? themeDataTitle[themeType].title : this.titleStyle
      let cloneTitleStyle = this.$_deepClone(this.titleStyle)
      if (!this.boardInfo.openBoardTab) {
        if (themeDataTitle[themeType]) {
          cloneTitleStyle.color = themeDataTitle[themeType].title.color
        }
      }
      this.reTitleStyle = cloneTitleStyle
    },
    refreshGridLayout() {
      const dynamicTagsRef = this.$refs.dynamicTags

      ;(Array.isArray(dynamicTagsRef) ? dynamicTagsRef : [dynamicTagsRef])
        .forEach(sdpGrid => {
          setTimeout(() => {
            const vueGrid = sdpGrid?.$refs?.layoutRef
            vueGrid?.width || vueGrid?.onWindowResize?.()
          })
        })

      // 重新渲染图形
      setTimeout(() => {
        const eventData = new EventData({
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'reloadAllChart',
        })
        this.$emit('eventBus', eventData)
      }, 500)
    },
    // app筛选器和排序器 tip
    showTip(id) {
      const dom = this.$refs[`${id}-tip`] && this.$refs[`${id}-tip`][0]
      if (dom && dom?.isVisible) {
        dom.hide()
      } else {
        setTimeout(() => {
          this.isShowTip = true
          this.$refs[`${id}-tip`][0].show()
        })

      }
      // }
    },
    handleShowChartPopup(el) {
      if (el?.vm?.showDimensionPupup) {
        el.vm.showDimensionPupup()
      }
    },
    // 点击关闭所有app筛选器和排序器 tip
    hideCubiTip() {
      this.isShowTip = false
      if (this.elList && this.elList.length > 0) {
        this.elList.forEach(item => {
          const dom = this.$refs[`${item.id}-tip`] && this.$refs[`${item.id}-tip`][0]
          if (dom && dom?.isVisible) {
            dom.hide()
          }
        })
      }
    },
    isFilterSoter(el) {
      let isFilter = el.filterData?.isEnable && el.filterData?.form?.length
      let isSort = el.sorterData?.isEnable && el.sorterData?.form?.length
      return isFilter || isSort || this.hasTableHeader(el) || this.isTreeTable(el)
    },
    hasTableHeader(el) {
      return el.type === 'table' ? el.vm?.mobileShowHeaderCtrl : false
    },
    isTreeTable(el) {
      return el.type === 'table' ? el.vm?.isTreeTable : false
    },
    isAdvanceContainer_fn(elem) {
      return isAdvanceContainer_fn(elem)
    },
    handleScreenEnlargeEl(data) {
      const { id, callback } = data.data
      id && this.handleEnlargeEl(id, callback)
    },
    // 元素订阅
    handleElementSubscription(element) {
      console.log('元素订阅', element)
      const { id } = element

      this.eventDataFun({
        target: ['fileExport'],
        targetFn: 'openElementTask',
        data: id
      })
    },
    // 添加到每日关注指标
    addToDailyConcernMetrics(el, options = {}) {
      const { isUpdate = false } = options
      if (this.init) {
        this.$message.warning(this.$t('sdp.views.plsRunBoard'))
        // 调用run
        return
      }

      const confirmTip = isUpdate ? this.$t('sdp.views.updateToDailyConcernMetrics') : this.$t('sdp.views.addToDailyConcernMetrics')
      const confirmTitle = isUpdate ? this.$t('sdp.views.confirm') : undefined
      const isEMS = [ALL_PROJECT_NAME.EMS].includes(this.utils?.env?.projectName)
      if (isEMS && !isUpdate) {
        const contentJson = this.dailyConcernData?.content || '{}'
        const contentParse = JSON.parse(contentJson)
        const { dynamicTags = [] } = contentParse
        // if (dynamicTags.length > 1) {
          this.$set(this, 'addDailyConcernDialogElement', el)
          this.addDailyConcernDialogVisible = true
          return
        // }
      }

      this.$sdp_eng_confirm(confirmTip, confirmTitle, {
        confirmButtonText: this.$t('sdp.button.ensure'),
        cancelButtonText: this.$t('sdp.button.cancel'),
        cancelButtonClass: 'confirm-cancel el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog',
        type: 'warning',
        closeOnClickModal: false,
      }).then(() => {
        const sonEl = getContainerOnElement(el, this.elList).map(el => el.id)
        const ids = sonEl.length ? sonEl : [el.id]

        if (!ids.length) {
          // 容器没有元素
          return
        }

        // const previewData = {}

        this.eventDataFun({
          data: { ids },
          options: {
            getParamsRequestAndElementRequest: (previewData) => {
                let { folderType, folderId, id, metaDashboardElementLanList } = this.boardInfo
              let personalAttentionType = this.utils?.sbiOptions?.folderType || this.boardInfo.personalAttentionType || '' // 设置类型

                saveElementData.call(this, {
                  api: this.utils.api,
                  folderType: folderType,
                  personalAttentionType,
                  classifyId: folderId,
                  dashboardId: id,
                  isEMS,
                  boardInfo: this.boardInfo,
                  elList: this.elList,
                  elementId: el.id,
                  paramsPanelList: this.paramsPanelList,
                  languageAndCurrency: this.languageAndCurrency,
                  tenantData: this.tenantData,
                  previewData,
                  metaDashboardElementLanList,
                  filterServerNeedParams: this.filterServerNeedParams,
                  isEnterPriseCurrency: this.parent.isEnterPriseCurrency,
                  errorCall: (res) => {
                    this.$message.error(res.msg)
                  },
                  successCall: (res) => {
                    // this.isShowDddSuccessTip = IS_ADD_TYPE.ADD === res.isAdd
                    this.isMessageDialog = true
                  }
                })
            },
          },
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'refreshElByForce',
          source: componentName
        })

      }).catch(() => {})
    },
    handleAddDailyDialogConfirm(tabId) {
      this.isShowAddSuccessTip = false
      const el = this.addDailyConcernDialogElement
      const sonEl = getContainerOnElement(el, this.elList).map(el => el.id)
      const ids = sonEl.length ? sonEl : [el.id]

      if (!ids.length) {
        // 容器没有元素
        return
      }

      // const previewData = {}

      this.eventDataFun({
        data: { ids },
        options: {
          getParamsRequestAndElementRequest: (previewData) => {
            let { folderType, folderId, id, metaDashboardElementLanList } = this.boardInfo
            let personalAttentionType = this.utils?.sbiOptions?.folderType || this.boardInfo.personalAttentionType || '' // 设置类型

            saveElementData.call(this, {
              isEMS: true,
              api: this.utils.api,
              folderType: folderType,
              personalAttentionType,
              classifyId: folderId,
              dashboardId: id,
              boardInfo: this.boardInfo,
              elList: this.elList,
              elementId: el.id,
              tabId: tabId,
              paramsPanelList: this.paramsPanelList,
              languageAndCurrency: this.languageAndCurrency,
              tenantData: this.tenantData,
              previewData,
              metaDashboardElementLanList,
              filterServerNeedParams: this.filterServerNeedParams,
              isEnterPriseCurrency: this.parent.isEnterPriseCurrency,
              errorCall: (res) => {
                this.$message.error(res.msg)
              },
              successCall: (res) => {
                if (tabId) {
                  this.isShowAddSuccessTip = true
                }
                // this.isShowDddSuccessTip = IS_ADD_TYPE.ADD === res.isAdd
                this.isMessageDialog = true
              }
            })
          },
        },
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'refreshElByForce',
      })

      this.addDailyConcernDialogVisible = false
    },
    handleOpenDailyConcern(str = '') {
      const id = `${this.addDailyConcernDialogElement?.id || str || ''}`
      if (!id) return

      this.$emit('eventEmit', 'routerTo', `/home?dailyActiveElementId=${id}`)
    },
    getElementListSortId(list) {
      return list.sort((a, b) => {
        if (a.layout.y !== b.layout.y) {
          return a.layout.y - b.layout.y // y坐标越小越前
        }
        return a.layout.x - b.layout.x // 如果y坐标相同，则x坐标越小越前
      }).map(item => item.id)
    },
    async getDailyElementParam() {
      if (this.init) {
        this.$message.warning(this.$t('sdp.views.plsRunBoard'))
        return []
      }
      if (!this.addDailyElementSelectedList.length) {
        this.$message(this.$t('sdp.placeholder.pleaseSelect'))
        return []
      }

      const getData = (el) => {
        return new Promise((resolve, reject) => {
          const sonEl = getContainerOnElement(el, this.elList).map(el => el.id)
          const ids = sonEl.length ? sonEl : [el.id]

          if (!ids.length) {
            // 容器没有元素
            resolve(undefined)
            return
          }
          this.eventDataFun({
            data: { ids },
            options: {
              getParamsRequestAndElementRequest: (previewData) => {
                let { folderType, folderId, id, metaDashboardElementLanList } = this.boardInfo
                let personalAttentionType = this.utils?.sbiOptions?.folderType || this.boardInfo.personalAttentionType || '' // 设置类型

                const data = saveElementData.call(this, {
                  isGetParam: true,
                  api: this.utils.api,
                  folderType: folderType,
                  personalAttentionType,
                  classifyId: folderId,
                  dashboardId: id,
                  boardInfo: this.boardInfo,
                  elList: this.elList,
                  elementId: el.id,
                  paramsPanelList: this.paramsPanelList,
                  languageAndCurrency: this.languageAndCurrency,
                  tenantData: this.tenantData,
                  previewData,
                  metaDashboardElementLanList,
                  filterServerNeedParams: this.filterServerNeedParams,
                  isEnterPriseCurrency: this.parent.isEnterPriseCurrency,
                })
                resolve(data)
              },
            },
            target: ['displayPanel', 'displayPanelMobile'],
            targetFn: 'refreshElByForce',
            source: componentName
          })
        })
      }

      const idSortList = []
      const dynamicTags = this.dynamicTags.reverse()
      dynamicTags.forEach(dynamic => {
        const { content = [] } = dynamic
        const elListFilter = this.elList.filter(e => content.includes(e.id) && this.addDailyElementSelectedList.includes(e.id))
        const ids = this.getElementListSortId(elListFilter) || []
        idSortList.push(...ids)
      })

      const dataList = []
      for (let item of idSortList) {
        const el = this.elList.find(e => e.id === item)

        dataList.push(await getData(el))
      }

      return dataList
    },

    async getDailyElementParamToBoardSave(element) { // 来自看板保存
      // if (this.init) {
      //   this.$message.warning(this.$t('sdp.views.plsRunBoard'))
      //   return []
      // }
      let that = this
      const getData = (el) => {
        return new Promise((resolve, reject) => {
          const sonEl = getContainerOnElement(el, this.elList).map(el => el.id)
          const ids = sonEl.length ? sonEl : [el.id]

          if (!ids.length) {
            // 容器没有元素
            resolve(undefined)
            return
          }

          let { folderType, folderId, id } = this.boardInfo
          let metaDashboardElementLanList = []
          // 只有PC保存才会触发这里，所以newBoardContent有值。（更新每日关注）
          if (that.$parent?.newBoardContent) {
            metaDashboardElementLanList = that.$parent?.newBoardContent?.metaDashboardElementLanList || []
          } else {
            metaDashboardElementLanList = this.boardInfo.metaDashboardElementLanList || []
          }
          let personalAttentionType = this.utils?.sbiOptions?.folderType || this.boardInfo.personalAttentionType || '' // 设置类型

          const data = saveElementData.call(this, {
            isEditToSave: true,
            isGetParam: true,
            api: this.utils.api,
            folderType: folderType,
            personalAttentionType,
            classifyId: folderId,
            dashboardId: id,
            boardInfo: this.boardInfo,
            elList: this.elList,
            elementId: el.id,
            paramsPanelList: this.paramsPanelList,
            languageAndCurrency: this.languageAndCurrency,
            tenantData: this.tenantData,
            previewData: {},
            metaDashboardElementLanList,
            filterServerNeedParams: this.filterServerNeedParams,
            isEnterPriseCurrency: this.parent.isEnterPriseCurrency,
          })
          resolve(data)
        })
      }
      const dataList = []
      dataList.push(await getData(element))
      return dataList
    },
    resetAddDailyElementSelect() {
      this.$set(this, 'addDailyElementSelectedList', [])
      this.handleEmitSelectCnt()
    },
    // pc放大
    handleEnlargeEl(id, callback) {
      this.enlargeEl = this.elementBox.find(({ element = {} }) => element.id === id) || null
      callback && callback(this.enlargeEl)
      this.enlargeVisible = true
      this.$refs.fullscreenDialog.noOpacity = true
      this.$nextTick(() => {
        this.$refs.fullscreenDialog.enlargeAfterFun(this.enlargeEl)
      })
    },
    closeChartFullScreen(eventData) {
      const { data } = eventData
      const callback = data.callback
      const e = data.e
      if (this.enlargeVisible && this.enlargeEl) {
        const { id } = this.enlargeEl.element
        this.handHorizontalView(id)
        if (callback) {
          setTimeout(() => {
            callback(e)
          }, this.IsVertical ? 2000 : 1000)
        }
      }
    },
    delayChartFullScreen(id) {
      if (this.init) {
        this.chartFullScreen(id)
      } else {
        setTimeout(() => {
          this.chartFullScreen(id)
        }, 500)
      }
    },
    // app放大
    chartFullScreen(id) {
      if (this.init) {
        let confrimToast = this.$createToast({
          type: 'warn',
          time: CREATE_TOAST_TIME,
          txt: this.$t('sdp.views.isRunFinish')
        })
        this.$_insertElement(this.getBoardUnderIdElement('#mobileElementPanel'), confrimToast.$el)
        confrimToast.show()
        return false
      }
      this.eventDataFun({
        target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
        targetFn: 'closePopup',
      })
      // 竖屏：vertical 横屏：landscape
      let postData = { type: '', id: id, data: {} }
      this.IsVertical = !this.IsVertical
      // 横竖屏放大缩小数据设置 landscape 横屏 vertical 竖屏
      // IsVertical 为true是缩小  false是放大
      // 小程序和app的区别 app放大都是横屏 小程序竖屏放大是竖屏
      if (this.IsVertical) { // 缩小
        postData.type = this.isHorizontal ? 'landscape' : 'vertical'
      } else { // 放大
        postData.type = this.isWeAppAndWeb ? this.isHorizontal ? 'landscape' : 'vertical' : 'landscape'
      }
      this.screenType = this.IsVertical ? 'vertical' : 'landscape'
      const dom = document.body
      const htmlDom = document.documentElement
      // 全屏时通知外部样式调整
      this.eventDataFun({
        target: ['displayPanelMobile', 'displayPanelPcToMobile'],
        targetFn: 'enlargeChange',
        data: {
          show: !this.IsVertical,
          screenType: this.screenType,
          isPcHorizontalView: this.isPcHorizontalView && this.isPreview
        },
      })
      if (this.IsVertical) {
        dom.removeAttribute('style')
        htmlDom.removeAttribute('style')
        this.supernatantStyle.opacity = 0
        this.$delete(this.supernatantStyle, 'transition')
        setTimeout(() => {
          this.supernatantStyle.opacity = 1
          this.supernatantStyle.transition = 'opacity .5s !important'
        }, 1500)
        setTimeout(() => {
          this.$refs.fullscreenDialog.enlargeBeforeFun()
          this.enlargeVisible = !this.IsVertical
        })
      } else {
        this.handleEnlargeEl(id, (el) => {
          if (!el) return
          // 直接从黑色/白色主题下复制的看板  在深邃蓝的背景下没有设置themeData的数据，放大背景色会有问题 应该设置默认
          const themeType = this.themeData?.appThemeType
          const defaultThemeBgc = BOARD_ELEMENT_THEME[themeType]
          const themeMap = el?.element?.themeMap?.[themeType] || defaultThemeBgc
          let bgc = themeMap.mobileBgc
          // 判断是否使用渐变色
          if (themeMap?.fillColorType === MATERIAL_COLOR_TYPE.GRADIENT) {
            bgc = this.getElEnlargementBgColor(themeMap)
          }
          // let bgc = el.element.themeMap[this.themeData.themeType].mobileBgc
          if (bgc) {
            postData.toolBgc = this.IsVertical ? '' : (bgc || '')
            if (postData.toolBgc) {
              dom.setAttribute('style', 'background-color:' + postData.toolBgc)
              htmlDom.setAttribute('style', 'background-color:' + styleObject[themeType]['--sdp-color-KBBJS'])
            }
          }
        })
      }
      this.eventDataFun({
        target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
        targetFn: this.IsVertical ? 'restScroll' : 'setFullScreenTop',
      })
      setTimeout(() => {
        // 放大不需要大小
        // const { width, height } = this.$_getRect(this.$refs.carousel.$el)
        // postData.size = { width, height }
        this.requestAppEvent(postData)
      })
      setTimeout(() => {
        // if ((/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))) {
          this.sdpBus.$emit(EVENT_BUS.LAYOUT_CHANGE)
        // }
      }, this.IsVertical ? 1600 : 500)
    },
    eventDataFun(param) {
      const eventData = new EventData(param)
      this.$emit('eventBus', eventData)
    },
    setElBoxStyle(type) {
      const arr = []
      if ([TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.IMAGE, TYPE_ELEMENT.TEXT].includes(type)) {
        // arr.push({ padding: 0 })
      } else if (type === TYPE_ELEMENT.ELEMENT_TITLE) {
        // arr.push({ padding: '6px 16px' })
      }
      return arr
    },
    setElPaddingStyle,
    // getElBackground(el) {
    //   return 'chart-background'
    // },
    showType(data) {
      // const EnlargechartList = ['ve-histogram-normal', 've-histogram-stack', 've-line-normal', 've-line-area', 've-bar-normal', 've-pie-normal', 've-pie-rose', 've-ring-normal', 've-scatter-normal', 've-composite', 've-gauge-normal', 've-map-parent', 've-radar', 've-themeRiver', 've-bar-percent', 've-ring-multiple']
      // 微信小程序不显示放大
      // return EnlargechartList.includes(data.content.alias) && this.appData.appType !== TARO_ENV_TYPE.WEAPP
      return this.appData.appType !== TARO_ENV_TYPE.WEAPP
    },
    // styleFun(bool) {
    // return bool ? {} : { transform: 'translateY(-100000px)', zIndex: '-1', position: 'absolute', left: '0' }
    // },
    eventBus,
    getSupernatantContentStyles() {
      let styles = {
        ...this.screenSpaceStyle()
      }
      if (this.isDataReport) {
        styles.backgroundColor = 'unset'
      }
      return styles
    },
    // 屏幕留白上下间距调整
    screenSpaceStyle() {
      const style = {}
      if (this.screenSpaceMode && this.themeData.themeFullScreen) {
        Object.assign(style, { paddingBottom: this.paddingData.bottom + 'px', paddingTop: this.paddingData.top + 'px' })
      }
      return style
    },
    // 屏幕留白 自定义色块1 样式
    screenSpaceTop() {
      let color = this.boardInfo.screenModeDate.TopColor
      return {
        height: this.paddingData.top + 'px',
        width: '100%',
        backgroundColor: color,
        position: 'absolute',
        top: 0,
        left: 0
      }
    },
    // 屏幕留白 自定义色块2 样式
    screenSpaceBottom() {
      let color = this.boardInfo.screenModeDate.BottomColor
      return {
        height: this.paddingData.bottom + 'px',
        width: '100%',
        backgroundColor: color,
        position: 'absolute',
        bottom: 0,
        left: 0
      }
    },
    // 返回时重新渲染
    // 触发全屏
    toggleFullScreen(data) {
      this.enlargeEl = this.elementBox.find(({ element = {} }) => element.id === data.id) || null
      this.fullscreenId = data.id
      this.fullScreenStatus = !this.fullScreenStatus
      const fullscreen = this.$refs[`${data.id}-fullscreen`]
      const ref = Array.isArray(fullscreen) ? fullscreen[0] : fullscreen
      // ref.toggle()
      if (this.fullScreenStatus) {
        ref.enter([ref])
      } else {
        ref.toggle([ref])
      }
    },
    fullscreenChange(fullscreen) {
      this.fullscreen = fullscreen
      this.fullScreenStatus = !this.fullScreenStatus
      setTimeout(() => {
        // 全屏时重新调整饼图大小和位置
        const _height = fullscreen ? window.screen.height : ''
        const eventData = new EventData({
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'settingChart',
          data: {
            height: _height,
            fullscreenId: this.fullscreenId,
          },
        })
        this.$emit('eventBus', eventData)
        fullscreen || (this.fullscreenId = '')
      })
    },
    initElementComp(val = this.visibleElList) {
      // 文本框关闭显示放大图标开关，放大预览时不展示
      const largeElList = val.filter(item => item.type !== TYPE_ELEMENT.TEXT || !this.enlargeVisible || item.style.zoomBtn)
      this.elementBox = largeElList.map(item => Array.isArray(this.$refs[item.id]) ? this.$refs[item.id][0] : this.$refs[item.id]).filter(e => e)
      this.element = this.elementBox.map(item => item.getElement && item.getElement())
      this.containerIdElList = this.element.filter(e => e?.element && (e.element.type === TYPE_ELEMENT.CONTAINER))
      this.elementComp = this.containerIdElList.reduce((pre, el) => {
        if (el.requestAdapter) {
          const requestData = el.requestAdapter()
          if (requestData) {
            const { requestParams = [] } = requestData
            pre.push(requestParams)
          }
        }
        return pre
      }, this.element.filter(e => e?.element && (e.element.type !== TYPE_ELEMENT.CONTAINER)))
      return this.elementComp
    },
    getNewestFlattenElementComp() {
      return this.$_flatten(this.initElementComp()).filter(e => e)
    },
    filterIsRenderInteraction({ el, type, callback }) {
      if (el) {
        const isRenderInteraction = this.$_getProp(el, 'element.elAttr.isRenderInteraction', false)

        let isBoardModeRefreshEl = false
        if (el?.element?.type === TYPE_ELEMENT.TABLE) {
          isBoardModeRefreshEl = el?.$refs?.grid?.isBoardModeRefreshEl
        }
        if ((getIsAnimationWithInteractioned.call(this, el.element) || isRenderInteraction) && (type !== RUN_ELEMENT_TYPE.refreshEl || isBoardModeRefreshEl) && el.deleteElementData) {
          el.deleteElementData()
          callback && callback()
          return false
        }
      }
      return el
      function getIsAnimationWithInteractioned(thisEl) {
        // tv屏蔽动画效果
        if (this.utils.isTvScreen && !this.utils.getPlayAnimation()) return false
        // 设置了播放动画时进行图标联动的元素是否交互了本元素
        return this.elList.some(item => {
          const interactionOptions = item.content.interactionOptions || []
          const flag = interactionOptions.find(intera => intera.associElements.some(eve => eve.id === thisEl.id))
          if (flag) {
            const animationWithInteraction = ANIMATION_WITH_INTERACTION_CHART.includes(item.content.alias) && this.$_getProp(item, 'elAttr.animationWithInteraction', false)
            if (animationWithInteraction) return true
          }
          return false
        })
      }
    },
    collectRequest({ ids, type, otherType, skipLoading, isExportType }, eventData) {
      const data = {}
      let elementComp = this.getNewestFlattenElementComp()
      if (Array.isArray(ids) && ids[0] !== 'ALL') {
        elementComp = elementComp.filter(el => ids.includes(el.id || (el.element && el.element.id)))
      }
      // 当前看板请求的ids
      this.requestIds = {}
      elementComp.filter(el => {
        return this.filterIsRenderInteraction({ el, type })
      }).filter(el => {
        // 排除容器不是当前tab到请求
        let bool = true
        const _containerId = el?.element?._containerId || ''
        const id = el?.element?.id || ''
        if (_containerId && id) {
          const element = this.elList.find(el => el.id === _containerId)
          const content = element.content
          if (element.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
            const activeTabId = content.activeTabId

            const tabList = content.tabList

            tabList.find(item => {
              if (!this.containerRunTab[_containerId]?.includes(item.name) && item.name !== activeTabId) {
                const includedElsIdList = item.content.includedElsIdList
                if (includedElsIdList.includes(id)) {
                  bool = false
                  return true
                }
              }
            })

            if (isExportType) {
              this.containerRunTab[_containerId] = tabList.map(item => item.name)
            } else {
              this.containerRunTab[_containerId] = [...new Set([...(this.containerRunTab[_containerId] || []), content.activeTabId])]
            }
          } else if (element.type === TYPE_ELEMENT.CONTAINER && content.settings.displayMode === DISPLAY_MODE.HORIZONTAL) {
            const activeElId = content.activeElId

            bool = this.containerRunTab[_containerId]?.includes(id) || (activeElId === id)

            if (isExportType) {
              this.containerRunTab[_containerId] = content.includedElIds
            } else {
              this.containerRunTab[_containerId] = [...new Set([...(this.containerRunTab[_containerId] || []), activeElId])]
            }
          }
        }

        return isExportType || bool
      }).forEach(el => {
        if (el.requestAdapter) {
          const requestData = el.requestAdapter({ type, elList: this.elList, eventData, skipLoading })
          const id = el.id || (el.element && el.element.id)
          if (requestData) {
            const { requestKey, requestParams } = requestData
            // 部分数据存在容器或高级容器上，后面需要获取
            requestParams._containerId = el?.element?._containerId || ''
            data[requestKey] = data[requestKey] || {
              ids: new Set(),
              datas: []
            }
            data[requestKey].ids.add(id)
            if ([TYPE_ELEMENT.COMBINE_CARD, TYPE_ELEMENT.FOUR_QUADRANT, TYPE_ELEMENT.DUPONT_ANALYSIS].includes(el.element.type) && Array.isArray(requestParams)) {
              data[requestKey].datas.push(...requestParams)
            } else {
              data[requestKey].datas.push(requestParams)
            }
            this.requestIds[id] = Array.isArray(requestParams) ? requestParams.map(e => e.id) : [requestParams.id]
          } else {
            if (el._chartResponseTmp) { el._chartResponseTmp = null }
            setTimeout(() => {
              this.setloading({ id: [id], loading: false })
            }, 1000)
          }
        }
      })
      console.log('-------------------------', data)
      return data
    },
    setBoardQuestParamsToTable(id, boardRequest) {
      const tableComp = this.flattenElementComp.filter(el => el.element && (el.element.id === id))
      tableComp.forEach(comp => {
        let vm = null
        let getGridRef = this.$_getProp(comp, 'element.vm.getGridRef', false)
        if (getGridRef) vm = getGridRef()
        vm || (vm = this.$_getProp(comp, 'element.vm', false))
        const setBoardQuestParams = this.$_getProp(vm, 'setBoardQuestParams', false)
        setBoardQuestParams && setBoardQuestParams(boardRequest)
      })
    },
    // 取消处理所有看板元素loading处理
    setloading({ id = [], loading = false }) {
      if (!this.utils.isMobile && this.utils.isPcMobileEdit) return
      // 解决bug 15103
      let flattenElementComp = this.getNewestFlattenElementComp()
      if (loading) {
        flattenElementComp = flattenElementComp.filter(el => !this.$_getProp(el, 'element.elAttr.isRenderInteraction', false))
      }
      if (id[0] === 'ALL') {
        flattenElementComp.filter(e => e).forEach(el => {
          el.setElementLoadState && el.setElementLoadState(loading)
        })
      } else {
        const requestIds = id.length ? id : Object.keys(this.requestIds)
        const elementComp = flattenElementComp.filter(el => requestIds.includes(el.id || (el.element && el.element.id)))
        elementComp.filter(e => e).forEach(el => {
          el.setElementLoadState && el.setElementLoadState(loading)
        })
      }
    },
    // 设置看板元素，数据错误的提示
    setError({ id = [], error = false }) {
      let flattenElementComp = this.getNewestFlattenElementComp()
      if (id[0] === 'ALL') {
        flattenElementComp.filter(e => e).forEach(el => {
          el.setElementErrorState && el.setElementErrorState(error)
        })
      } else {
        const requestIds = id.length ? id : Object.keys(this.requestIds)
        const elementComp = flattenElementComp.filter(el => requestIds.includes(el.id || (el.element && el.element.id)))
        elementComp.filter(e => e).forEach(el => {
          el.setElementErrorState && el.setElementErrorState(error)
        })
      }
    },
    setContainerLoading(loading) {
      this.init && this.containerIdElList.filter(e => e).forEach(el => {
        el.setElementLoadState && el.setElementLoadState(loading)
      })
    },
    // 分发数据
    distributeResponse({ response, id }, params = {}, res = {}) {
      // 判断为子集id取key作为id
      const requestIds = Object.keys(this.requestIds)
      requestIds.includes(id) || requestIds.some(key => {
        const bool = this.requestIds[key].includes(id)
        if (bool) id = key
        return bool
      })
      const element = this.flattenElementComp.find(
        el => el.id === id || el['element'].id === id
      )
      element && element.responseAdapter(response.length === 1 ? response[0] : response, params, res)
    },
    // 设置看板tabs
    setDynamicActiveTag(el, tabId) {
      const dynamicTags = this.dynamicTags
      const len = dynamicTags.length
      if (len) {
        const tag = dynamicTags.find(item => tabId ? item.id === tabId : item.active) || dynamicTags[0]
        this.$set(el, '_dynamicTags', { id: tag['id'], isHide: true, })
        this.dynamicTags.some(item => {
          (tag['id'] === item.id) && item.content.push(el.id)
          return tag['id'] === item.id
        })
      }
    },
    // 撤销恢复操作使用的方法
    updateDynamicTagsByElement(elementList) {
      const len = this.dynamicTags.length
      if (len) {
        const elIdList = this.elList.map(e => e.id)
        elementList.forEach(el => {
          if (el._dynamicTags && elIdList.includes(el.id)) {
            this.dynamicTags.some(item => {
              (el._dynamicTags.id === item.id && !item.content.includes(el.id)) && item.content.push(el.id)
              return el._dynamicTags.id === item.id
            })
          }
        })
        this.dynamicTags.forEach(item => {
          item.content = item.content.filter(e => elIdList.includes(e))
        })
      }
    },
    // 添加浮层元素
    addEl(elType, options = {}) {
      // 新增的元素为最高的层级；多tab页新增时，元素层级为该tab页最高层级
      let tabId = options.tabId
      let middleStyle = options?.style || null
      if (this.dynamicTags.length && !tabId) {
        const curDynamicTags = this.dynamicTags.find(item => item.active) || this.dynamicTags[0]
        tabId = curDynamicTags.id
      }
      const defaultOptions = {
        isMobile: this.isMobile || false,
        isScreen: this.isScreen || false,
        boardType: this.isDataReport ? 'dataReport' : '', // 看板类型，用布尔类型的话以后会有很多个值，所以用字符类型
      }
      if (this.utils.isDataReport && this.dataReport) {
        const data = this.dataReport.getReportPageSize(tabId, true)
        Object.assign(defaultOptions, {
          pageSize: {
            ...data,
          }
        })
      }
      let name = generateElNames.call(this, elType, options)
      const idList = Array.isArray(this.elList) ? this.elList.map(item => item.elName) : []
      if ((elType === TYPE_ELEMENT.TABLE) && options.content && options.content.dataScreen) {
        if (!idList.includes(options.content.dataScreen.name)) {
          options.content.dataScreen.name && (name = options.content.dataScreen.name)
        } else {
          options.content.dataScreen.name = name
          options.content.dataScreenRes.name = name
        }
      } else if ([TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.CHART, TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(elType) && options.content && options.elName) {
        if (!idList.includes(options.elName) && options.elName) {
          name = options.elName
        } else {
          options.elName = name
        }
      }
      if (this.isMobile) {
        options.style = { boxShadow: '0 0 0 0 rgba(0,0,0,0.10)' }
      }
      // 移动端和pc端默认不需要border
      options.style = { border: 'none' }
      Object.assign(options, defaultOptions)
      let containerEl = this.elList.find(item => item?.content?.isAdvanceContainerEdit)
      let isContainerAdd = this.commonData.isAdvanceContainerEdit && containerEl && elType !== TYPE_ELEMENT.CONTAINER
      if (isContainerAdd) {
        options._containerId = containerEl.id
      }
      const element = new BoardElement({
        type: elType,
        ...options,
        elName: name,
      })

      if (!this.isScreen && this.highlightItem?.layout?.y !== undefined) {
        element.layout.x = this.highlightItem.layout.x
        element.layout.y = this.highlightItem.layout.y + this.highlightItem.layout.h
      }

      if (!this.isScreen && !this.isMobile) {
        const {
          colNum = GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY,
        } = this.boardInfo.boardLayout

        element.layout.w = Math.round(element.layout.w * colNum / GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY)
        element.layout.x = Math.round(element.layout.x * colNum / GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY)
      }

      // 大屏看板需要设置元素位置及层级
      if (this.isScreen) {
        const { canvasHeight = 1080, canvasWidth = 1920 } = this.dynamicTags.find(item => item.id === tabId)?.canvasSize || {}
        let coords = null

        if (this.isDataReport && !isContainerAdd) {
          const padding = this.dataReport?.getReportPadding() || getDefaultReportPadding()
          coords = getRecordCoords(getPX(padding.left), getPX(padding.top))
        } else if (elType === TYPE_ELEMENT.MATERIAL_LIBRARY) {
          if (element.styleConfig.autoWidth === true) {
            element.styleConfig.size.width = canvasWidth * element.styleConfig.autoWidthPercent || canvasWidth
            element.styleConfig.position.left = (canvasWidth - element.styleConfig.size.width) / 2
          }
          if (element.styleConfig.needX !== undefined) {
            element.styleConfig.position.left = element.styleConfig.needX
          }
          if (element.styleConfig.needY !== undefined) {
            element.styleConfig.position.top = element.styleConfig.needY
          }
          if (element.styleConfig.offsetX !== undefined) {
            element.styleConfig.position.left += element.styleConfig.offsetX
          }
          if (element.styleConfig.offsetY !== undefined) {
            element.styleConfig.position.top += element.styleConfig.offsetY
          }
          if (element.styleConfig.alignRight === true) {
            element.styleConfig.position.left = canvasWidth - element.styleConfig.size.width
          }
        } else if (isContainerAdd) {
          coords = getRecordCoords()
          this.$set(element.content, 'isContainerElEdit', true)
          let { activeTabId, tabList } = containerEl.content
          let activeTabList = tabList.find(tab => tab.name === activeTabId) || {}
          activeTabList?.content?.includedElsIdList && activeTabList.content.includedElsIdList.push(element.id)
        } else {
          const { width, height } = element.styleConfig.size
          const initialX = (canvasWidth - width) / 2
          const initialY = (canvasHeight - height) / 2
          coords = getRecordCoords(initialX, initialY)
        }

        if (coords) {
          element.styleConfig.position.left = coords.x
          element.styleConfig.position.top = coords.y
          this.dataReport?.correctElPosition(element, tabId)
        }

        const highestIndex = this.getHighestIndex(tabId)
        element.zIndex = highestIndex + 1
        recordNextCoords(element.styleConfig.position.left, element.styleConfig.position.top)
      }

      this.setDynamicActiveTag(element, options.tabId)

      if (options._copyOperate) {
        if (this.isMobile) {
          let midThemeMap = options.themeMap || {}
          const themeType = this.utils.themeParameters.themeType
          if (options.copyForm === 'pcBoard') {
            midThemeMap[themeType].mobileBgc = midThemeMap?.[themeType]?.pcBgc || '#ffffff'
          }
          Object.assign(element, { style: middleStyle, themeMap: midThemeMap, elAttr: options.elAttr })
        } if (this.isDataReport) {
         // 报告引入元素场景
          Object.assign(element, { themeMap: options.themeMap, elAttr: options.elAttr })
        } else {
          Object.assign(element, { themeMap: options.themeMap, styleConfig: options.styleConfig, elAttr: options.elAttr })
        }
      }
      return element
    },
    // 获取最高图层
    getHighestIndex(tabId) {
      let highestIndex = 0
      let elList = this.elList
      if (tabId) {
        elList = this.elList.filter(el => el._dynamicTags.id === tabId)
      }
      elList.forEach((el) => {
        let zIndex = el.zIndex > indexForTop ? el.zIndex - indexForTop : el.zIndex
        if (el._elementGroupId) {
          let elementGroup = elList.find(e => e.id === el._elementGroupId)
          let groupZindex = elementGroup.zIndex > indexForTop ? elementGroup.zIndex - indexForTop : elementGroup.zIndex
          zIndex += groupZindex
        }
        if (zIndex > highestIndex) {
          highestIndex = zIndex
        }
      })
      return highestIndex
    },
    // 移除浮层元素
    removeEl(el) {
      let callback
      if (el instanceof EventData) {
        callback = el.callback
        el = el.data
      }
      const delId = []
      if (el.id === this.highlightItem.id) {
        this.gridItemClickHandler()
      }
      // 删除容器里关联元素
      if (el.type === TYPE_ELEMENT.CONTAINER) {
        let removeElsIdList = getContainerIncludeElsIdList(el)
        removeElsIdList = this.$_removeRepetition(removeElsIdList)
        removeElsIdList.forEach(this.deleteElById)
        delId.push(...removeElsIdList)
      }
      this.removeDynamicTags(el)
      this.deleteElById(el.id)
      delId.push(el.id)
      this.updateParamsPanelBind(delId)
      this.updateInteraction(delId)
      callback && callback()
    },
    // 更新交互设置
    updateInteraction(delId) {
      this.elList.forEach(el => {
        const interactionOptions = this.$_getProp(el, 'content.interactionOptions', null)
        if (interactionOptions) {
          const newInteractionOptions = interactionOptions.filter(item => {
            const { associElements = [] } = item
            return associElements.filter(({ id }) => !delId.includes(id)).length
          })
          this.$set(el.content, 'interactionOptions', newInteractionOptions.length ? newInteractionOptions : null)
        }
      })
    },
    updateParamsPanelBind(ids = [], type = 'setElBind') {
      if (Array.isArray(ids)) {
        // 设置看板元素的元素绑定更新
        this.$nextTick(() => {
          const paramsPanel = this.parent.paramsPanel
          if (paramsPanel) {
            paramsPanel.elementComp.forEach(el => {
              let check = false
              paramsBindElements(el.paramElement).forEach(a => {
                if (ids.includes(a)) {
                  check = true
                }
              })
              check && el[type] && el[type](ids)
            })
          }
          let noActiveTab = this.paramsPanelList.filter(item => !item.active)
          noActiveTab.forEach(tab => {
            tab.content.forEach(param => {
              let check = false
              paramsBindElements(param).forEach(a => {
                if (ids.includes(a)) {
                  check = true
                }
              })
              check && adapter(param.content, param.type, this.elList, this)
            })
          })
        })
      }
    },
    // 删除指定el的id
    deleteElById(id) {
      // const removeElement = this.elList.find(el => el.id === id)
      // if (!removeElement) return
      //
      // if (removeElement._containerId) {
      //   const advanceContainer = this.elList.find(el => el.id === removeElement._containerId && isAdvanceContainer_fn(el))
      //   if (!advanceContainer) return
      //
      //   const { activeTabId, tabList, settings: { elAlias } } = advanceContainer.content
      //   const activeTab = tabList.find(tab => tab.name === activeTabId)
      //   const activeTabElsIdList = activeTab.content.includedElsIdList
      //   const removeIndex = activeTabElsIdList.indexOf(id)
      //   activeTabElsIdList.splice(removeIndex, 1)
      //   this.$delete(elAlias, id)
      //
      //   // todo: moveElForAdvanceByFilterSorter !!!
      // }

      const elIndex = this.elList.findIndex(el => el.id === id)
      if (elIndex >= 0) {
        this.elList.splice(elIndex, 1)
      }
    },
    // 删除关联面板tabs的id
    removeDynamicTags(el) {
      if (!el._dynamicTags) return false
      this.dynamicTags.some(item => {
        if (item.id === el._dynamicTags.id) {
          const arr = [el.id]

          el.type === TYPE_ELEMENT.CONTAINER && arr.push(...getContainerIncludeElsIdList(el))

          arr.forEach(tagId => {
            const index = item.content.findIndex(id => tagId === id)
            if (index > -1) {
              item.content.splice(index, 1)
            }
          })
        }
        return item.id === el._dynamicTags.id
      })
    },
    getElement(id) {
      return this.visibleElList.find(el => el.id === id)
    },
    getElement2(id) {
      return this.elList.find(el => el.id === id)
    },
    gridItemFocus() {
      const activeTabs = this.dynamicTags.find(item => item.active)?.content || []
      if (!this.isMobile || !activeTabs.length || activeTabs.length > 1) return
      let gridItemId = activeTabs[0] || ''
      this.gridItemClickHandler(gridItemId)
    },
    gridItemClickHandlerById(id = '') {
      const el = this.getElement(id)

      if (el) {
        this.gridItemClickHandler(id)
      }
    },
    // 点击获得当前高亮元素
    gridItemClickHandler(i = '') {
      if (typeof i === 'object' && this.$_getProp(i, 'data') !== undefined) {
        i = i.data !== '' ? i.data : ''
      }
      const highlightItem = this.highlightItem
      if (highlightItem.id !== i) {
        let el = this.getElement(i)
        // 容器编辑界面 容器外的元素默认选中容器
        if (this.commonData.isAdvanceContainerEdit && (!el || !el?.content?.isContainerElEdit)) {
          el = this.elList.find(item => item?.content?.isAdvanceContainerEdit)
        }
        if (this.commonData.isContainerEdit && !el) {
          el = this.elList.find(item => item.id === i)
        }
        this.$set(this, 'highlightItem', el || {})
        const target = this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['settingPanel', 'displayPanel', 'dataScreenHeader', 'displayPanelMobile']
        const eventData = new EventData({
          type: 'getElement',
          target,
          targetFn: 'getElement',
          data: this.highlightItem,
        })
        this.$emit('eventBus', eventData)
      }
    },
    gridItemClickHandler2({ id, containerId }) {
      let i = id
      if (typeof i === 'object' && this.$_getProp(i, 'data') !== undefined) {
        i = i.data !== '' ? i.data : ''
      }
      const highlightItem = this.highlightItem
      if (highlightItem.id !== i || highlightItem.id !== containerId) {
        let el = this.getElement2(i)
        // 容器编辑界面 容器外的元素默认选中容器
        if (this.commonData.isAdvanceContainerEdit && (!el || !el?.content?.isContainerElEdit)) {
          el = this.elList.find(item => item?.content?.isAdvanceContainerEdit)
        }
        if (this.commonData.isContainerEdit && !el) {
          el = this.elList.find(item => item.id === i)
        }
        const target = this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['settingPanel', 'displayPanel', 'dataScreenHeader', 'displayPanelMobile']
        const eventData = new EventData({
          type: 'getElement',
          target,
          targetFn: 'getElement',
          data: el,
        })
        this.$emit('eventBus', eventData)
        if(containerId){
          el = this.elList.find(item => item.id === containerId)
        }
        this.$set(this, 'highlightItem', el || {})
      }
    },
    // 9307 设置红色边框
    errorFlickerFun(data, status = true){
      console.log('ErrorFlickerFun=>',data,status)
      const fullscreen = this.$refs[data]
      const ref = Array.isArray(fullscreen) ? fullscreen[0] : fullscreen
      ref && ref.setErrorStatus(status, data)
      ref && ref.setDuplicateNameStatus(status)
    },
    elContentSaveHelper(element) {
      const { type } = element
      if (type === 'chart') {
        const animationFn = this.chartData.vm.animationInstance?.stop
        animationFn && animationFn.call(this.chartData.vm.animationInstance)
        this.chartData.vm.animationInstance = {}
      }

      const fn = elContentSaveHelper[type]
      fn && fn.call(this, element, this.elList)
    },
    async getCanvasTop() {
      const canvasBox = document.querySelector('#selectedParamsInfo')
      const canvas = canvasBox && await html2canvas(canvasBox)
      return canvas
    },
    // 移动端交互
    interAction(type, value, id) {
      const _obj = {
        // interaction: 'interActionClick',
        interaction: 'handleChart',
        drill: 'handleChart',
        drillBack: 'onUpDrill',
        dimensionSuperLink: 'handleChart',
      }
      const key = _obj[type]
      const isMobileDrillClick = key === 'handleChart'
      const el = this.elList.find(item => item.id === id)
      if (el._containerId) {
        el.vm[key](value, isMobileDrillClick)
      } else {
        this.$refs[id][0].getElement()[key](value, isMobileDrillClick)
      }
    },
    elToolsDeleteHandlerByEventBus({ data: { element, callback } }) {
      this.elToolsDeleteHandler(element, callback)
    },
    elToolsDeleteHandlerWithRecord(element) {
      const callback = () => {
        this.recordElementDelete([element])
        // this.boardRecord.undoSave({
        //   type: RECORD_OPERATE_TYPE.ELEMENT_DELETE,
        //   idList: [element.id],
        //   value: [this.$_deepClone(element)],
        //   containerEl: element.type === TYPE_ELEMENT.CONTAINER ? element : undefined,
        //   callback: () => {
        //   },
        //   source: 'element_supernatant_delete'
        // })
      }
      this.elToolsDeleteHandler(element, callback)
    },
    // 不提示直接删除
    elToolsDeleteHandlerNoConfirm(element, callback) {
      const elements = Array.isArray(element) ? element : [element]
      const advList = elements.filter(e => e.type === TYPE_ELEMENT.CONTAINER).map(e => e.id) || []
      elements.forEach(el => {
        // 下方注释代码关联bug44173
        // if (!advList.includes(el._containerId)) {
          this.removeEl(el)
        // }
        if (el._containerId) {
          const container = this.elList.find(e => e.id === el._containerId)
          if (container) {
            if (container.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
              container.content.tabList.forEach(tab => {
                tab.content.includedElsIdList = tab.content.includedElsIdList.filter(id => id !== el.id)
              })
            } else {
              container.content.includedElIds = container.content.includedElIds.filter(id => id !== el.id)
            }
          }
        }
        if (el._elementGroupId) {
          const group = this.elList.find(e => e.id === el._elementGroupId)
          if (group) {
            calculateGroupPositionAndSize({ elementGroup: group, elList: this.elList })
          }
        }
        if (el.type === TYPE_ELEMENT.ELEMENT_GROUP) {
          this.elList.forEach(e => {
            if (e._elementGroupId === el.id) {
              this.$delete(e, '_elementGroupId')
            }
          })
        }
      })
      callback && callback()
    },
    elToolsDeleteHandler(element, callback) {
      // 报告移动端布局删除逻辑
      if (this.isMobile && this.utils.isPcMobile) {
        // 更新截图
        this.sdpBus.$emit(EVENT_BUS.SET_PCMOBILE_ELEMENT_SCREENSHOT, getElementShoots(element, this))
        const eventData = new EventData({
          type: 'changeSetStore',
          target: ['displayPanelPcToMobile'],
          targetFn: 'updateStoreSet',
          data: {
            actionType: 'remove',
            setType: 'mobileSet',
            listType: 'elementList',
            dynamicTagId: element._dynamicTags.id,
            el: element
          }
        })
        this.$emit('eventBus', eventData)
        return
      }
      this.$sdp_eng_confirm(this.$t('sdp.message.confirmDelete'), this.$t('sdp.button.menuDelete'), {
        confirmButtonText: this.$t('sdp.button.ensure'),
        cancelButtonText: this.$t('sdp.button.cancel'),
        cancelButtonClass: 'confirm-cancel el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog',
        beforeClose: (action, instance, done) => {
          instance.$el.style.zIndex = -1
          done()
        },
        closeOnClickModal: false,
        type: 'warning',
      }).then(() => {
        const elements = Array.isArray(element) ? element : [element]
        // 每日关注不用撤销
        const call = () => {
          callback && callback()
          elements.forEach(el => {
            this.removeEl(el)
          })
          if (this.utils.isDailyConcerned) {
            this.$message({
              type: 'success',
              message: this.$t('sdp.message.DeletedSuccessfully')
            })
          }
        }

        this.utils.isDailyConcerned ? this.dailyConcernMetricsClass('deleteElement', {
          element,
          call
        }) : call()
      }).catch(() => {
      })
    },
    // 撤销保存
    recordElementDelete(elements) {
      this.boardRecord.recordElementListZIndex()
      this.boardRecord.recordElementListLayout()
      const deleteEls = [...elements]
      const containerIdList = deleteEls.filter(e => e.type === TYPE_ELEMENT.CONTAINER).map(e => e.id)
      const containerList = this.$_deepClone(this.elList.filter(e => containerIdList.includes(e._containerId)))
      const deleteElIdList = [...deleteEls, ...containerList].map(e => e.id)
      const deleteElementList = this.$_deepClone(this.elList.filter(e => deleteElIdList.includes(e.id)))

      let containerEl = null
      if (deleteEls.some(e => e._containerId)) {
        const _containerId = deleteEls.find(e => e._containerId)._containerId
        const container = this.elList.find(e => e.id === _containerId)
        if (container) {
          containerEl = this.$_deepClone(container)
        }
      }
      this.boardRecord.undoSave({
        type: RECORD_OPERATE_TYPE.ELEMENT_DELETE,
        idList: deleteElIdList,
        value: deleteElementList,
        containerEl: containerEl,
        callback: () => {
          const tags = this.$refs.dynamicTags
          if (tags) {
            tags.forEach(tag => {
              tag.updateLayoutByElList && tag.updateLayoutByElList()
            })
          }
        },
        source: 'element_delete'
      })
    },
    // 复制元素到指定tab页
    copyElementsToTabs({ tabId, pageId }) {
      const { element, callback } = this.copyElement
      this.copyElementContent(element, callback, tabId, pageId)
      this.$delete(this, 'copyElement')
    },
    elToolsCopyHandler(element, options = {}, callback) {
      const { copyType = '' } = options
      if (copyType) {
        if (copyType === COPY_TO_OTHER_PAGE && this.visibleCopyMenuChildren) {
          this.copyElementsToTabsVisible = true
          this.copyElement = { element, callback }
        } else {
          this.copyElementContent(element, callback, undefined, undefined, { copyType })
        }
        return
      }
      // 存在多个tab页，且看板标签页模式关闭时，支持复制元素到指定tab页
      if (this.dynamicTags.length > 1 && !this.boardInfo.openBoardTab) {
        this.copyElementsToTabsVisible = true
        this.copyElement = { element, callback }
      } else {
        this.copyElementContent(element, callback, undefined, undefined, { copyType })
      }
    },
    // 验证最大元素数量
    verificationMaxElementNum() {
      if (this.elList.length >= MAX_COUNT_ELEMENTS_IN_BOARD) {
        this.$message.info(this.$t('sdp.message.mostElementsInBoard'))
        return true
      }
      return false
    },
    copyElementContent(element, callback, tabId, pageId, option = {}, source = '') {
      if (this.elList.length > MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD) {
        inBoardMessage(() => {
            this.$message(`${this.$t('sdp.message.elementsInBoardMessage')}`)
        })
      }

      if (this.verificationMaxElementNum()) return false

      if (!tabId) {
        const activeTab = this.dynamicTags.find(item => item.active) || { id: this.dynamicTags?.[0].id }
        tabId = activeTab.id || ''
      }
      if (!pageId && this.isDataReport) {
        pageId = this.dataReport.activePageId
      }

      const elements = this.$_JSONClone(Array.isArray(element) ? element : [element])
      let newElIds = []
      const copyEl = (el) => {
        const eventData = new EventData({
          type: 'copyEl',
          target: ['dataScreenHeader'],
          targetFn: 'handleCopyElement',
          data: {
            element: el,
            elRef: this.$refs[el.id],
            tabId,
            pageId,
            ignoreOffset: option.copyType === COPY_PAGE,
            options: {
              source: option.source,
              callback: (newEl) => {
                newElIds.push(newEl.id)
              }
            },
          }
        })
        this.$emit('eventBus', eventData)
      }
      const customerElements = this.elList.filter(el => el.type === TYPE_ELEMENT.CUSTOMER_ELEMENT)

      const isBeyondCustomer = elements.some((element) => {
        if (customerElements.length >= CUSTOMER_ELMENT_MAX_NUM) {
          let isHasCustomer = element.type === TYPE_ELEMENT.CUSTOMER_ELEMENT

          let customerIds = []

          if (element.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
            element.content.tabList.forEach(({ content: { includedElsIdList = [] } }) => {
              customerIds.push(...includedElsIdList)
            })
            isHasCustomer = customerElements.some(el => customerIds.includes(el.id))

          } else if (element.type === TYPE_ELEMENT.CONTAINER) {
            customerIds = element.content.includedElIds
            isHasCustomer = customerElements.some(el => customerIds.includes(el.id))
          }

          if (isHasCustomer) {
            this.$message(this.$t('sdp.message.mostCustomerElement'))
            return true
          }
        }
        return false
      })

      if (isBeyondCustomer) return

      const oldDataReport = this.$_deepClone(this.dataReport)
      const oldElList = this.$_deepClone(this.elList)

      elements.forEach(el => { copyEl(el) })
      // if (![COPY_PAGE, COPY_TO_CURRENT_PAGE, COPY_TO_OTHER_PAGE].includes(option.copyType)) {
      if (option?.source === 'tab') {
        callback && callback()
        return
      }
      if (!this.isDataReport) {
      this.boardRecord.undoSave({
        type: RECORD_OPERATE_TYPE.ELEMENT_ADD,
        idList: newElIds,
        value: this.$_deepClone(this.elList.filter(el => newElIds.includes(el.id))),
        source: 'element_panel_copy_add'
      })
      } else {
        if (![COPY_PAGE].includes(option.copyType)) {
          this.boardRecord.undoSave({
            type: RECORD_OPERATE_TYPE.DATA_REPORT_ELEMENT_COPY,
            oldElList: oldElList,
            oldDataReport: oldDataReport,
            elList: this.elList,
            dataReport: this.dataReport,
            source: 'dataReportElementCopy'
          })
        }
      }

      callback && callback()
    },
    filterSortHandler(element) {
      if (element.type === TYPE_ELEMENT.CONTAINER) {
        this.containerFilterSorterVisible = true
        this.$refs.containerFilterSortDialog.setData(element)
      } else {
        this.filterSorterVisible = true
        this.$refs.filterSorterDialog.setData(element)
      }
    },
    showBoardRemarkDialog() {
      this.boardRemarkVisible = true
    },
    closeBoardRemarkDialog() {
      this.boardRemarkVisible = false
    },
    setElementLans(element) {
      this.newElementContent = {
        tableControlsElementLanList: [],
        tableControlsLanguageList: []
      }

      if (![TYPE_ELEMENT.CHART, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD].includes(element.type)) return

      const metaDashboardElementLanList = this.parent?.newBoardContent?.metaDashboardElementLanList
      if (!metaDashboardElementLanList) return

      const tableControlsElementLanList = metaDashboardElementLanList.filter(data => {
        return typeof data.key === 'string' && data.key.includes(element.id)
      }).map(data => ({
        key: data.key,
        languageCode: data.languageCode,
        value: data.value
      }))
      const tableControlsLanguageList = filterElementLans(metaDashboardElementLanList, element.type, element.id)

      this.newElementContent = {
        tableControlsElementLanList,
        tableControlsLanguageList
      }
    },
    // 新增模板子节点
    addToTemplate(element) {
      this.setElementLans(element)
      this.templateElement = element
      this.getTreeData(element)
      this.isShowTemplate = true
    },
    async getTreeData(element) {
      // manage
      let params = { channel: this.isMobile ? '2' : '1' }
      if ([ALL_PROJECT_NAME.EMS, ALL_PROJECT_NAME.SBI].includes(this.utils?.env?.projectName)) {
        params.manage = true
      }
      let treeData = await getTreeData(this.utils.api, params) || []
      const folderTypeMap = {
        [TYPE_ELEMENT.CHART]: '7',
        [TYPE_ELEMENT.TABLE]: '5',
        [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD]: '8',
        [TYPE_ELEMENT.CUSTOMER_ELEMENT]: '12',
      }
      const elTypeTree = treeData.find(t => t.folderType === folderTypeMap[element.type])
      if (elTypeTree) this.treeData = elTypeTree.children
    },
    elToolsEditHandler(element) {
      if (element instanceof EventData) element = element.data.element
      const { type } = element
      // 可以编辑的元素类型
      const editableElType = CAN_EDIT_TYPES
      // 需要元素自己控制显示的元素类型
      // 没有在elContentSetHelper处理函数中设置this.elSettingVisible的话
      // 就不需要往这个数组里面添加类型
      const customVisibleElType = [TYPE_ELEMENT.TABLE]
      if (editableElType.includes(type)) {
        // 容器类型需要特殊处理
        if (type === 'chartContainer') {
          this.elSettingDialog = `chart-container-dialog`
          this.elSettingProps.type = TYPE_CHART_CONTAINER_DIALOG.setting
        } else if ([TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE].includes(type)) {
          this.elSettingDialog = 'text-setting-dialog'
        } else {
          this.elSettingDialog = `${type}-dialog`
        }
        this.elSettingProps.elId = element.id
        this.settingDialogRef = this.$_toCamel(this.elSettingDialog)
        setTimeout(() => {
          this.elSettingVisible = !customVisibleElType.includes(type)
          this.nowElementId = element.id
          this.$nextTick(() => {
            const fn = elContentSetHelper[type]
            fn && fn.call(this, element)
          })
        }, 100)
      }
    },
    elToolsRemarkHandler(element) {
      this.remarkVisible = true
      this.$refs.remarkDialog.setData(element)
    },
    layoutReady(gridLayout) {
      setTimeout(() => {
        this.isReady = true
      }, 1000)
      this.$emit('layout-ready', gridLayout)
    },
    elPaddingFormatter({ type, style, content }) {
      let padding = {}
      if (type === 'combine-card' && style.hasOwnProperty('showPadding') && !style.showPadding) {
        // 组合卡片多tab页时，还是需要下边距
        // const moreTab = content?.choiceTab?.length > 1
        // padding = moreTab ? { padding: '0 0 12px' } : { padding: '0' }
      } else if (type === TYPE_ELEMENT.TEXT) {
        padding = { padding: 0 }
      }
      return padding
    },
    elBgColorFormatter({ themeMap, styleConfig, type, content }, notSetImg, options = {
      getPositionColor: '', // 取值 left | right 根据传入位置返回对应位置应该展示的背景色，渐变色时左边取渐变左边颜色，右边取渐变右边颜色
    }) { // 背景色设置
      if (type === TYPE_ELEMENT.MATERIAL_LIBRARY) {
        if (MATERIAL_NO_BACKGROUND.includes(content?.sonType)) {
          return { background: 'transparent !important' }
        }
      }
      if (type === TYPE_ELEMENT.IMAGE) {
        return { background: 'transparent !important' }
      }
      if (this.isEnlargeVisible && (this.isMobile || this.utils.isPcMobile)) {
        return { background: 'transparent !important' }
      }
      const themeType = this.utils.themeParameters.themeType
      const imgUrl = themeMap?.[themeType]?.imgData?.url
      // 横屏、放大时元素div需要展示背景图，其余场景给父级div已经加上背景图，无需重复添加
      const notSetBgImage = notSetImg && !this.enlargeVisible
      if (this.isScreen && !this.utils.isPcMobileEdit) {
        const { background } = elStyleFormatter({ themeMap, styleConfig }, themeType, this.isMobile)
        let style = { background }
        if (type === TYPE_ELEMENT.TEXT && imgUrl) {
          style = {
            background: notSetBgImage ? '' : `url(${imgUrl}) 0% 0%/100% 100% no-repeat`,
          }
        }
        return style
      }
      const bgStr = (this.isMobile || this.utils.isPcMobileEdit) ? BG_MAP_STRING['mobile'] : BG_MAP_STRING['pc']
      let oldBgColor = themeMap?.[themeType]?.[bgStr] || BOARD_ELEMENT_THEME?.[themeType]?.[bgStr]
      // 判断是否使用渐变色
      if (styleConfig.fillColor) {
        if (styleConfig.fillColor.type === MATERIAL_COLOR_TYPE.GRADIENT) {
          if (!themeMap[themeType]) {
            themeMap[themeType] = {}
          }
          themeMap[themeType].fillColorType = styleConfig.fillColor.type
          themeMap[themeType].fillGradientAngle = styleConfig.fillColor.linear.angle
        }
      }
      if (themeMap?.[themeType]?.fillColorType === MATERIAL_COLOR_TYPE.GRADIENT) {
        const style = themeMap?.[themeType]
        oldBgColor = `linear-gradient(${style.fillGradientAngle}deg, ${(style.gradientColor || []).join(',')})`
      }
      const bgObj = type === TYPE_ELEMENT.TEXT && imgUrl ? { background: notSetBgImage ? '' : `url(${imgUrl}) 0% 0%/100% 100% no-repeat` } : { background: themeMap ? oldBgColor : '' }
      // let isOldHide = helper.hexify(oldBgColor) === BOARD_ELEMENT_THEME[themeType][bgStr]
      if (!this.isMobile && this.enlargeVisible) {
        // !已经弃用 使用elementAmplificationBg
        // const enlargementPcStyle = this.boardInfo?.enlargementOpts?.themeStyleMap?.[themeType] || ''
        bgObj.background = oldBgColor
      }
      // 处理移动端元素选中，左右两边 div 背景色
      if (themeMap?.[themeType]?.fillColorType === MATERIAL_COLOR_TYPE.GRADIENT && options.getPositionColor !== '') {
        const style = themeMap?.[themeType]
        if (+style.fillGradientAngle === 90) {
          // 左到右
          if (options.getPositionColor === 'left') {
            bgObj.background = style.gradientColor[0]
          } else {
            bgObj.background = style.gradientColor[1]
          }
        } else if (+style.fillGradientAngle === 270) {
          // 右到左
          if (options.getPositionColor === 'left') {
            bgObj.background = style.gradientColor[1]
          } else {
            bgObj.background = style.gradientColor[0]
          }
        }
      }
      return bgObj
    },
    sliderFormatTooltip(val) {
      return val + '%'
    },
    changeBoardScale(type) {
      let scale = type === 'sub' ? this.boardScale - 1 : this.boardScale + 1
      if (scale > 400 || scale < 10) return
      this.boardScale = scale

      this.handleBoardScaleChange()
    },
    // closeContainerSetEdit(elist) {
    //   this.elList = elist
    // },
    handleShowOrHideChartValue(e, show = true) {
      const btnChange = () => {
        const show = this.elTools.moreBtns.find(item => item.title === this.$t('sdp.button.ValueDisplay'))
        show && this.$set(show, 'isShow', show)
        const hide = this.elTools.moreBtns.find(item => item.title === this.$t('sdp.button.ValueHidden'))
        hide && this.$set(hide, 'isShow', !show)
      }
      // 图形直接隐藏
      const elementType = e.type // 元素类型
      if (elementType === TYPE_ELEMENT.CHART) {
        const hideValue = this.$_getProp(e, 'vm.labelLineButtonListChange', false)
        if (hideValue !== false) {
          hideValue(show)
          btnChange()
        }
      } else if (elementType === TYPE_ELEMENT.CONTAINER) {
        // 容器 分高级 普通容器  普通容器分横向 纵向
        if (e.subType !== undefined && e.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
          const curTab = e.content.activeTabId
          e.content.tabList.forEach(item => {
            if (item.name === curTab) {
              const includedElsIdList = item.content.includedElsIdList
              this.elList.forEach(el => {
                if (includedElsIdList.length && includedElsIdList.includes(el.id)) {
                  if (el.type === TYPE_ELEMENT.CHART) {
                    const chart = el
                    const hideValue = this.$_getProp(chart, 'vm.labelLineButtonListChange', false)
                    if (hideValue !== false) {
                      hideValue(show)
                      btnChange()
                    }
                  }
                }
              })
            }
          })
        } else {
          const displayMode = e.content.settings.displayMode // 容器横向 纵向
          if (displayMode === 'horizontal') {
            let chart = null
            const activeElId = e.content.activeElId
            for (let i = 0; i < this.elList.length; i++) {
              if (this.elList[i].id === activeElId) {
                chart = this.elList[i]
                break
              }
            }
            const hideValue = this.$_getProp(chart, 'vm.labelLineButtonListChange', false)
            if (hideValue !== false) {
              hideValue(show)
              btnChange()
            }
          } else if (displayMode === 'vertical') {
            const includedElIds = e.content.includedElIds
            for (let i = 0; i < this.elList.length; i++) {
              if (includedElIds.includes(this.elList[i].id)) {
                if (this.elList[i].type === TYPE_ELEMENT.CHART) {
                  const chart = this.elList[i]
                  const hideValue = this.$_getProp(chart, 'vm.labelLineButtonListChangeAll', false)
                  if (hideValue !== false) {
                    hideValue(show)
                    btnChange()
                  }
                }
              }
            }
          }
        }
      }
    },
    handleExportTableData(id) {
      this.exportTableElement = this.elList.find(el => el.id === id)
      this.isExportTableVisible = true

    },
  },
  beforeDestroy() {
    document.removeEventListener('click', this.documentClick)
    this.communication.cancal()
    this.sdpBus.$off(EVENT_BUS.UPDATE_PARAMS_PANEL_BIND, this.updateParamsPanelBind)
    this.sdpBus.$off(EVENT_BUS.EXPORT_TABLE_DATA, this.handleExportTableData)
  },
}
</script>
<style lang="scss" scoped>
@import "../../../grid/theme/index";
@import './variable.scss';
@import 'packages/base/board/displayPanelMobile/components/variable.scss';
@import 'animation';

.icon {
    color: var(--sdp-cszjq-is);
    font-size:16px;
    /*margin-right: 16px;*/
    cursor: pointer;
    // color: #fff;
    // &:hover{
    //   color: #333;
    // }
  }
.fontStype {
  margin-top: 20px;
  color: var(--sdp-zs) !important;
  cursor: pointer;
  word-break: break-word;
}
.tips {
  text-align: center;
  [data-theme = 'sdp-dark-blue'] & {
    color: #FFF;
  }
}
.sdp-screen-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  &.watermark-area {
    position: relative;
    .watermark {
      /*width: 100%;*/
      width: calc(100% - 16px);
      height: 100%;
      position: absolute;
      z-index: 999;
      pointer-events: none;
      left: 0;
      top: 0;
    }
  }
  .carouselStyle {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
    /deep/ &>.el-carousel__container {
      height: 100%;
      display: flex;
      flex-direction: column;
      flex: 1;
      &>.is-active {
        position: relative;
      }
      &>.el-carousel__item {
        flex: 1;
      }
    }
  }
}
.carouselIsAnimating {
  /deep/ &>.el-carousel__container{
    &>.is-animating {
      transition: none
    }
  }
}

/deep/ .vue-grid-item {
  transition: none;
}

.is-app .supernatant-layout /deep/ > .vue-grid-layout > .vue-grid-item {
  opacity: 0;
  transition: transform 0s, opacity 1s !important;
}

.is-app .supernatant-layout.layout-ready /deep/ > .vue-grid-layout > .vue-grid-item {
  &:not(.vue-grid-placeholder){
    opacity: 1;
  }

  & > .sdp-grid-item-wrapper > .sdp-grid-item-body {
    left: 0;
    transition: width 300ms, left 300ms;

    > .full-class-mobile {
      padding-left: 0;
      padding-right: 0;
      transition: padding 300ms;
    }
  }
}

.no-scroll {
  height: auto;
  bottom: auto;
  top: auto;
  overflow: unset;
  position: relative;
}
.layout-scroll {
  overflow-y: auto;
  overflow-x: hidden;
}

.hearder{
  top: 1px;
  right: 1px;
  z-index: 20000;
  width: calc(100% - 2px);
}
.body {
  box-shadow: 0 2px 5px 0 rgba(0,24,120,0.40) !important;
  border: 1px solid var(--sdp-zs) !important;
}

// .panel-disable{
//   background: #fefefe!important;
// }
// .panel-active{
//   background: #f8f8f8!important;
// }
.pcPadding{
  padding: 0 16px;
}
.noFlex {
  display: block !important;
  flex:none !important;
}
.screenBoard {
  flex: 1;
  background-color: #141921;
  &.isPreview {
    padding: 0;
  }
  .carouselStyle /deep/ {
    &>.el-carousel__container {
      // position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      .el-carousel__item {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
.sdp-report-ding {
  overflow: unset;

  &>.carouselStyle {
    height: auto;
    overflow: unset;

    &>.el-carousel__container {
      height: auto;
      overflow: unset;

      &>.el-carousel {
        height: auto;
        overflow: unset;

        &>.el-carousel__container {
          height: auto;
          overflow: unset;

          &>.supernatant-layout-item {
            height: auto;
            overflow: unset;
          }
        }
      }
    }
  }
}
.show-scale {
  padding-bottom: 34px;
}
.large-screen-slider {
  display: flex;
  height: 30px;
  align-items: center;
  width: 200px;
  margin-left: calc(100% - 220px);
  /deep/ .scale {
    font-family: PingFangSC-Semibold;
    font-size: 12px;
    color: #F6F6F6;
    width: 45px;
  }
  .slider-icon {
    background: #293543;
    cursor: pointer;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;

    i {
      color: #8499B2;
      font-size: 12px;
    }
  }
  .slider-sub {
    margin: 0 10px 0 0;
  }
  .slider-add {
    margin: 0 0 0 10px;
  }
  /deep/ .el-slider__runway {
    width: 80px;
    height: 2px;
    background: #404C5A;
    margin: 0;
    .el-slider__bar {
      height: 2px;
      background: #F6F6F6;
    }
    .el-slider__button-wrapper {
      display: flex;
      top: -4px;
      z-index: 100;
      height: 10px;
      width: 10px;
      .el-slider__button {
        width: 10px;
        height: 10px;
        border: none;
      }
    }
  }
}
.sdp-board-scale-slider {
  position: fixed;
  right: 15px;
  bottom: 15px;
  width: auto;
  height: auto;
  margin: 0;
  z-index: 2;

  /deep/ {
    .scale {
      color: var(--sdp-cszjq-is);
    }
    .el-slider__bar, .el-slider__button {
      background-color: var(--sdp-zs) !important;
    }
    .el-slider__runway {
      background-color: var(--sdp-bg-bcbk) !important;
    }
  }
  .slider-icon {
    background: transparent;
    border-radius: 2px;
    display: flex;
    align-items: center;

    i {
      color: var(--sdp-cszjq-is);
    }

    &:hover {
      background: var(--sdp-zs);
      i {
        color: var(--sdp-nngl);
      }
    }
  }
  .slider-sub {}
}
/deep/ .sdp-board-scale-slider-popper {
  border-color: var(--sdp-ycfgx);

  .scale-options {
    .scale-options-item {
      color: var(--sdp-sztc-srkbt);
      background: var(--sdp-szk-bjs);

      &:hover, &.active {
        color: var(--sdp-zs);
        background: var(--sdp-sjj-hs);
      }
    }
  }
}
.board-scale-overflow {
  overflow: auto !important;
}
.data-report-scale-bar-container {
  position: absolute;
  z-index: 999;
  bottom: 5px;
  right: 225px;

  .data-report-scale-bar {
    display: flex;
    width: 68px;
    height: 30px;
    justify-content: space-between;
    align-items: center;
    color: #999;

    /deep/ .scale {
      flex: 1;
      text-align: center;
    }

    i {
      cursor: pointer;
    }
  }
}
.supernatantWrap {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  // min-height: 100%;
  .patchBar {
    position: absolute;
    width: calc(100% - 32px);
    height: 2px;
    z-index: 1;
    background: #f7f7f7;
  }

  #title {
    margin-top: 0 !important;
  }
  .title{
    padding-top: 16px;
    position: relative;
    left: 0;
    min-width: 30%;
    top: 0;
    bottom: 0;
    overflow: hidden;
    margin: 0 auto;
    white-space:nowrap;
    text-align: center;
    line-height: 1;
    width: 100%;
    font-size: 14px;
    &.tiny {
      padding-bottom: 0;
      min-height: 46px;
    }
  }
  .full-class{
    height:100%;
    overflow: hidden;
    // margin-bottom: 10px;
    // background-color: #fff !important;
  }
  .full-class-mobile{
    width: 100%;
    height:100%;
    overflow: hidden;
    .mobile-popover{
        /deep/ .cube-tip-angle{
          opacity: 0;
        }
    }
    // padding: 11px;
    .loading-content{
      /*padding: 11px;*/
    }
    &.full-class-mobile-container {
      .loading-content{
        padding: 0;
      }
    }
    &.full-class-mobile-combine-card {
      .loading-content{
        /*padding: 7px 7px 12px 7px;*/
      }
    }
  }
  .supernatant-board-remark {
    padding: 0 16px;
    font-family: NotoSansHans-Regular;
    font-size: 14px;
    line-height: 20px;
    /*white-space: nowrap;*/
    font-weight: 700;
    color: var(--sdp-zjbxx-wzs);
    i {
      color: var(--sdp-zs);
      cursor: pointer;
      margin-left: 8px;
    }

    &+.supernatant-params-info {
      .selected-params-info {
        padding-top: 8px;
      }
    }
  }
}
.elTools{
  /*padding: 8px 12px 8px 0;*/
  height: 30px;
  line-height: 30px;
  // border-radius: 8px;
}
.element{
  border: 1px solid #ccc;
  width: 500px;
  &:hover{
    border-color: #409EFF;
  }
}
.hquanpingzhanshifix {
  position: absolute;
  right: 4px;
  bottom: 4px;
  cursor: pointer;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background-color: #E5E5E5;
  text-align: center;
  line-height: 28px;
  font-size: 28px;
  z-index: 9999;
  opacity: var(--sdp-fullscreen-icon-opacity, 0.3) !important;
  @include sdp-mixin-style($type:(
    backgroundColor:(iconBgc:true),
  ), $self: $supernatant);

  // 解决苹果内核transform层级渲染问题
  transform: translateZ(2px);
}
.icon_color {
  padding: 12px;
  color: var(--sdp-cszjq-is);
}
.filtersorttip {
  position: absolute;
  right: 0px;
  top: 12px;
  cursor: pointer;
  font-size: 12px;
  z-index: 9999;
  @include sdp-mixin-style($type:(
    color:(customInputMobileDateColor:true),
  ), $self: $self);
}
.sdp-tools-dots {
  display: none;
}
.icon-sdp-hquanpingzhanshi {
  font-size: 28px;
  color: #000000;
  opacity: 1;
  color: var(--sdp-icon-color);
}
.enlarge-mobile-height {
  height: 100%;
  width: 100%;
  position: absolute;
}
.supernatant-layout-item {
  // height: calc(100% - 60px);
  counter-reset: element-title__title_order_1 element-title__title_order_2 element-title__title_order_3 element-title__title_order_4 element-title__title_order_5 element-title__title_order_6;
}
/deep/.cube-tip {
  // 以前是100
  // max-height: 100px;
  max-height: 300px;
  background: #474749;
  padding: 0px;
  height: auto;
  width: 130px;
  border-radius: 3px;

  .cube-tip-close {
    width: 0px;
    height: 0px;
    right: 0px;
    top: 0px;
    transform: none;
  }
  .cubeic-close:before{
    content: none;
  }
}

.message-success-dialog {
  .el-message-box__content {
    padding: 0 0 32px 0 !important;
    .iconBoxCls {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
    }
    p {
      font-size: 14px;
      color: #333;
      text-align: center;
    }
    .dialog-footer{
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .dialog-footer{
    margin-top: 0px;
    text-align: center;
  }
}
.genxinshijian-position{
 position: absolute;
 right: 25px;
 top:-1px
}

.kanban-guide-panel {
  position: fixed;
  top: 316px;
  right: 32px;
  z-index: 99;
  padding: 12px;
  background: var(--sdp-guide-panel-bg);
  border-radius: 4px;
  box-shadow: var(--sdp-guide-panel-box-shadow);

  .guide-header {
    display: flex;
    justify-content: space-between;
    height: 14px;
    align-items: center;
    margin: 2px 2px 12px;

    .guide-title {
      font-size: 14px;
      font-weight: 600;
      line-height: 14px;
      color: var(--sdp-sztc-bts);
    }

    .guide-header-close {
      color: var(--sdp-srk-bxwzs);
      padding: 2px;
      cursor: pointer;
      &:hover {
        color: var(--sdp-zs);
      }
    }
  }

  .guide-entry {
    width: 184px;
    height: 26px;
    line-height: 26px;
    border-width: 1px;
    border-style: solid;
    border-color: var(--sdp-guide-entry-bg);
    background: var(--sdp-guide-entry-bg);
    border-radius: 14px;
    margin-top: 4px;
    padding: 0 8px 0 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
    }

    &.is-doing {
      font-weight: bold;
      border-color: var(--sdp-guide-entry-border-color-by-done);
    }

    &.is-done {
      border-color: var(--sdp-guide-entry-border-color-by-done);
    }

    &:hover {
      border-color: var(--sdp-zs);
    }

    .guide-entry-index {
      font-weight: bold;
      margin-right: 8px;
    }

    .guide-entry-name {
      font-family: PingFang SC;
      font-size: 12px;
      line-height: 12px;
      color: var(--sdp-guide-entry-color);
    }

    .done-icon {
      width: 16px;
      height: 16px;
      line-height: 16px;
      border-radius: 50%;
      text-align: center;
      background-color: var(--sdp-zs);
      > i {
        color: var(--sdp-nngl);
        font-size: 16px;
        transform: scale(0.6);
        transform-origin: center;
        font-weight: bold;
      }
    }
  }
}
</style>
