<template>
  <DialogRoot
    :visible="dialogVisible"
    width="980px"
    @open="initData"
    @confirm="confirm"
    @close="cancel"
  >
    <div slot="title">
      <span class="el-dialog__title">{{ $t('sdp.views.customDimension') }}</span>
      <el-tooltip
        class="sdp-chartinfo-tooltip"
        effect="dark"
        placement="top"
      >
        <div slot="content" v-html="$t('sdp.tooltips.customDimensionTips')"></div>
        <i class="item-label__tooltip-icon el-tooltip icon-sdp-info"></i>
      </el-tooltip>
    </div>
    <div class="customFn-text">
      <ItemLabel
        :label="$t('sdp.views.customFn')"
        :level="4"
      ></ItemLabel>
      <div class="effectiveness" @click="checkSQL(false)" v-if="!isAssociationDataset">
        <i class="icon icon-sdp-SQLxiaoyan"></i>
        {{$t('sdp.views.SQLCheck')}}
      </div>
    </div>
    <el-input
      v-if="itemData.customDimension"
      type="textarea"
      resize="none"
      :rows="6"
      class="textarea-style"
      :placeholder="$t('sdp.placeholder.pls')"
      ref="expressionRef"
      v-model="itemData.customDimension.expression"
      @change="checkExpression"
    ></el-input>
    <div class="calcField-container">
      <div>
        <ItemLabel
          :label="$t('sdp.views.field')"
          style="margin-bottom: 4px;"
          :level="4"
        ></ItemLabel>
        <CalcOptions
          :list="calcFieldList"
          :isDataSet="true"
          @onClick="updateExpression"
        />
      </div>
      <div>
        <ItemLabel
          :label="$t('sdp.views.globalParam')"
          style="margin-bottom: 4px;"
          :level="4"
        ></ItemLabel>
        <CalcOptions
          :list="globalParams"
          @onClick="updateExpression"
          @detailClick="globalParamDetailClick"
        />
      </div>
    </div>
    <GlobalParamDetailDialog
      :visible.sync="globalParamDetailVisible"
      :id="globalParamDetailId"
    />
  </DialogRoot>
</template>

<script>
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog'
import { LineGroup, TableSwitch, ItemLabel } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import CalcOptions from 'packages/base/grid/components/common/CalcOptions.vue'
import GlobalParamDetailDialog from 'packages/base/grid/components/attributes-panel/globalParam/globalParamDetailDialog.vue' 
import { SOURCE_DATA_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'

export default {
  mixins: [mixin_dialog],
  inject: {
    aliasDict: { default: () => ({}) },
    utils: { default: () => ({}) },
    tenantData: { default: () => ({}) },
    chart: { default: {} },
  },
  name: 'CustomDimensionDialog',
  components: { ItemLabel, CalcOptions, GlobalParamDetailDialog },
  props: {
    formData: {
      type: Object,
      default: () => ({}),
    },
    isAssociationDataset: {
      type: Boolean,
      default: false,
    },
    datasetList: {
      type: Array,
      default: () => [],
    },
    drillSettings: {
      type: Object,
    }
  },
  data() {
    return {
      itemData: {},
      SQLisTrue: true,
      globalParams: [],
      globalParamDetailVisible: false,
      globalParamDetailId: '',
    }
  },
  computed: {
    calcFieldList() {
      const datasetId = this.formData.parentId
      let fieldList = []
      if(this.drillSettings?.sourceDataType === SOURCE_DATA_TYPE.indexFlag) {
        fieldList = this.chart?.fieldList || []
      } else {
        const curDataset = this.datasetList.find(dataset => dataset.id === datasetId || (this.isAssociationDataset && dataset.dateType === 'associateDataset'))
        if (!curDataset) return []
        fieldList = curDataset.children
      }
      fieldList = fieldList.length ? fieldList.map(item => {
          return {
            ...item,
            label: item.labeName,
            value: item.labeName,
            comment: item.comment,
          }
        }) : []
      return fieldList
    },
    textareaNode() {
      return this.$refs.expressionRef.$el.children[0]
    },
  },
  methods: {
    initData() {
      if (!this.globalParams.length) {
        this.initGlobalParam()
      }
      const itemData = this.$_deepClone(this.formData)
      if (!itemData.customDimension) {
        itemData.customDimension = { expression: '', datasetId: itemData.parentId }
      }
      this.initCustomExpression(itemData)
      this.itemData = itemData
    },
    initCustomExpression(itemData) {
      const { expression: _expression, } = itemData.customDimension

      const displayExpression = this.compatibleGlobalParam(_expression)

      itemData.customDimension.expression = this.updateExpressionFieldAlias(displayExpression || _expression)
    },
    // 全局参数 修改显示值
    compatibleGlobalParam(expression) {
      let exp = expression
      this.globalParams.forEach(item => {
        // eslint-disable-next-line no-useless-escape
        const reg = new RegExp(`'param\\\{${item.code}\\\}'`, 'ig')
        exp = exp.replace(reg, `'param{${item.level === '1' ? '' : 't_'}${item.label}}'`)
      })
      return exp
    },
    // 全局参数详情
    globalParamDetailClick(value, item) {
      this.globalParamDetailVisible = true
      this.globalParamDetailId = item.code
    },
    initGlobalParam() {
      const globalParameterList = this.tenantData?.globalParameterList || []
      this.globalParams = globalParameterList.map(e => {
        return {
          value: `'param{${e.level === '1' ? '' : 't_'}${e.name}}'`,
          code: e.id,
          label: e.name,
          level: e.level,
          detail: this.$t('sdp.views.pDetail'),
          detailHover: true,
        }
      })
    },
    updateExpression(value, isCalcFn) {
      const expression = this.itemData.customDimension.expression || ''

      const { selectionStart, selectionEnd } = this.textareaNode

      /// /////////////////////////////////////////////////////
      // 适配 Edge 浏览器 //////////////////////////////////////
      const start = Math.min(selectionStart, selectionEnd) // /
      const end = Math.max(selectionStart, selectionEnd) /// //
      /// ///////////////////////////////////////////////////

      const valueLength = value.length
      const lText = expression.slice(0, start)
      const rText = expression.slice(end)

      let currentIndex = start + valueLength
      let insertText = value

      const expressionLength = expression.length
      const selectionLength = end - start
      const cursorIndex = this.getCursorPosition(this.textareaNode)

      // 计算函数逻辑
      if (isCalcFn && (cursorIndex === expressionLength || selectionLength === expressionLength)) {
        currentIndex += 1
        insertText += '()'
      }

      this.itemData.customDimension.expression = lText + insertText + rText
      this.$nextTick(() => this.setCaretPosition(this.textareaNode, currentIndex))
    },
    // 设置光标位置
    setCaretPosition(node, index) {
      if (node.setSelectionRange) {
        node.focus()
        node.setSelectionRange(index, index)
      } else if (node.createTextRange) {
        var range = node.createTextRange()
        range.collapse(true)
        range.moveEnd('character', index)
        range.moveStart('character', index)
        range.select()
      }
    },
    // 获取光标位置
    getCursorPosition(node) {
      let CaretPos = 0

      if (document.selection) {
        // IE Support
        node.focus()
        const Sel = document.selection.createRange()
        Sel.moveStart('character', -node.value.length)
        CaretPos = Sel.text.length
      } else if (node.selectionStart || node.selectionStart === 0) {
        // Firefox` support
        CaretPos = node.selectionStart
      }
      return (CaretPos)
    },
    checkSQL(needReturn = false) {
      let sql = this.itemData.customDimension.expression || ''
      sql = this.getGlobalParamChangeValue(sql)
      sql = this.updateExpressionFieldAlias(sql, true)

      let params = {
        'datasetId': this.itemData.parentId,
        'sql': sql
      }
      const resPromise = this.utils.api.post(`bi/check/customExpression`, params)
      if (needReturn) return resPromise
      resPromise.then((res) => {
        this.SQLisTrue = res
        if (res) {
          this.$message.success(this.$t('sdp.views.SQLStatement'))
        }
      }).catch((err) => {
        console.log(err)
      })
    },
    // 表达式非空校验
    checkExpression(val) {
      this.SQLisTrue = false
      if (!/\S/g.test(val)) {
        this.itemData.customDimension.expression = ''
      }
    },
    getGlobalParamChangeValue(displayValue) {
      let expression = displayValue
      this.globalParams.forEach(item => {
        const reg = new RegExp(`('param{${item.level === '1' ? '' : 't_'}${item.label}}')`, 'ig')
        expression = expression.replace(reg, `'param{${item.code}}'`)
      })
      return expression
    },
    // 更新表达式，替换别名或本名
    updateExpressionFieldAlias(expression = this.itemData.customDimension.expression, isAlias = false) {

      return this.aliasDict?.updateExpressionFieldAlias(expression, this.formData.parentId, isAlias) || expression
    },
    async confirm() {
      if (!this.isAssociationDataset && !this.SQLisTrue) {
        let check = await this.checkSQL(true)
        if (!check) return
      }
      this.dialogVisible = false
      let sqlAlias = this.itemData.customDimension.expression
      let sql = this.getGlobalParamChangeValue(sqlAlias)
      sql = this.updateExpressionFieldAlias(sql, true)
      this.itemData.customDimension.expression = sql
      this.$emit('confirm', this.$_deepClone(this.itemData))
    },
    cancel() {
      this.dialogVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
  .customFn-text{
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 24px;
    margin-bottom: 8px;
  }
  .effectiveness {
    width: 94px;
    height: 24px;
    border: 1px solid var(--sdp-zs);
    line-height: 24px;
    text-align: center;
    color: var(--sdp-zs);
    font-size: 12px;
    cursor: pointer;
  }
  .icon-sdp-SQLxiaoyan {
    font-size: 14px;
  }
  .calcField-container{
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    margin-top: 24px;
    &>*{
      width: calc(50% - 18px);
      flex-grow: 0;
    }
  }
  .textarea-style {
    height: 180px;
    /deep/ .el-textarea__inner {
      height: 180px;
    }
  }
  .el-textarea /deep/ textarea {
    font-family: sans-serif;
  }
</style>
