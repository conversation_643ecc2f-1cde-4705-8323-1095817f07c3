<template>
  <DialogRoot
    :title="$t('sdp.views.bWordSet')"
    :visible.sync="dialogVisible"
    width="720px"
    @open="initFileSettingData"
    @confirm="confirm"
    @close="$emit('close')"
  >
      <div class="main">
        <span class="title">{{$t('sdp.views.fieldName')}}</span>
        <el-input :placeholder="getDatasetLabel(originalDatasetField, true)" disabled :title="getDatasetLabel(originalDatasetField, true)"></el-input>
      </div>
      <div class="main">
        <span class="title flex">
          <span>{{$t('sdp.views.alias')}}</span>
          <el-checkbox v-if="isShowHideAlias" v-model="hideAlias">{{$t('sdp.views.hideAlias')}}</el-checkbox>
        </span>
        <el-input ref="aliasInput" v-model="paramName.name" :value="paramName.name" :placeholder="$t('sdp.views.alias')" @change="checkAlias"></el-input>
      </div>
      <div class="parent-2" v-if="showDateCustomFormatComponent">
        <div class="main">
          <span class="title">{{$t('sdp.views.date_format')}}</span>
          <ChartSelect v-model="dateCustomFormat" :placeholder="$t('sdp.placeholder.plsInput')" clearable>
            <el-option
              v-for="listItem in dateCustomFormatList"
              :key="listItem.value"
              :label="listItem.label"
              :value="listItem.value">
            </el-option>
          </ChartSelect>
        </div>
        <div class="main">
          <span class="title">{{$t('sdp.views.dateFormat')}}</span>
          <ChartSelect v-model="timeCustomFormat" :placeholder="$t('sdp.placeholder.plsInput')" clearable>
            <el-option
              v-for="listItem in timeCustomFormatList"
              :key="listItem.value"
              :label="listItem.label"
              :value="listItem.value">
            </el-option>
          </ChartSelect>
        </div>
      </div>
      <div class="main preset-dimension" v-if="showPresetDimension">
        <el-checkbox v-if="isShowPresetDimensionSwitch" v-model="presetDimensionLabel" @change="presetDimensionLabelChangeHandler">{{$t('sdp.views.PresetDimensionLable')}}</el-checkbox>
        <el-checkbox v-model="timeDimensionSwitch" @change="timeDimensionSwitchChangeHandler">{{$t('sdp.views.timeDimensionSwitch')}}</el-checkbox>
        <el-checkbox v-model="trendsDimensionSwitch" @change="trendsDimensionSwitchHandler">{{$t('sdp.views.trendsDimensionSwitch')}}</el-checkbox>
        <el-checkbox v-model="eventDimensionSwitch" @change="eventDimensionSwitchHandler">{{$t('sdp.views.eventDimensionSwitch')}}</el-checkbox>
      </div>
      <div class="display-format" v-if="showViewFormatSetting && type !== 'rankValue'">
        <div>{{$t('sdp.views.displayFormat')}}</div>
        <ChartSelect v-model="format" :placeholder="$t('sdp.placeholder.plsInput')" :disabled="viewformatDisabled" @change="changeViewFormat">
          <el-option
            v-for="listItem in viewFormatComputedList"
            :key="listItem.value"
            :label="listItem.label"
            :disabled="listItem.disabled"
            :value="listItem.value">
          </el-option>
        </ChartSelect>
        <ChartSelect v-model="decimals" :placeholder="$t('sdp.placeholder.plsInput')" v-if="showDecimals.includes(format)" :disabled="(disableDecimals.includes(format) && numberDisabled) || viewformatDisabled">
          <el-option
            v-for="listItem in decimalsList"
            :key="listItem.value"
            :label="listItem.label"
            :value="listItem.value">
          </el-option>
        </ChartSelect>
      </div>
      <!-- 0值显示 -->
      <div v-if="showZeroFormatComponent" class="setting-item zero-display">
        <item-label icon :level="5" :label="$t('sdp.views.zeroValueDisplay')" :tips="$t('sdp.views.zeroToStr')"></item-label>
        <ChartSelect v-model="zeroFormat" :placeholder="$t('sdp.placeholder.plsInput')">
          <el-option
            v-for="item in zeroFormatList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </ChartSelect>
      </div>
      <!-- 最小单位以下值简写 -->
      <div v-if="showSmallShortSetting" class="small-short-setting filed-setting-switch">
        <TableSwitch class="width-40" :value.sync="smallShort">
          <div class="item-label" slot="label">
            <span>{{ $t('sdp.views.minimumUnitValue') }}</span>
          </div>
        </TableSwitch>
      </div>
      <div class="dateDimensions" v-if="showCustomDateDimensionSetting">
        <span>{{$t('sdp.views.dateDimensionDisplay')}}</span>
        <ChartSelect v-model="dateDimensionValue">
        <el-option v-for="item in dateDimensionOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </ChartSelect>
      </div>
      <!-- 标题文本与文本的日期文本域 -->
      <template v-if="showTextDomainSetting">
        <div class="textDomains">
          <span>{{$t('sdp.views.textDomain')}}</span>
          <ChartSelect v-model="textDomain">
            <el-option
              v-for="item in textDomainOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </ChartSelect>
        </div>
        <div class="textDomains" v-if="textDomain === 'timePre'">
          <span>{{$t('sdp.views.compareType')}}</span>
          <ChartSelect v-model="compareType">
            <el-option
              v-for="item in compareTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </ChartSelect>
        </div>
      </template>
  </DialogRoot>
</template>

<script>
import { TableSwitch, ItemLabel } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { CUSTOM_DATE_DIMENSION_CHART, CHART_ALIAS_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { generateViewFormat, restoreViewformat } from 'packages/base/board/displayPanel/supernatant/chartSet/utils/generator'
import { WARNLINE_TYPE_CONNECT_STR } from 'packages/assets/constant'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog'
export default {
  inject: {
    chart: { default: () => ({}) },
    utils: {},
    getCurrentThemeClass: { default: () => () => '' }
  },
  mixins: [datasetMixin, mixin_dialog],
  components: { TableSwitch, ItemLabel },
  props: {
    isAddFileSetting: {
      type: Boolean,
      default: false,
    },
    dragList: {
      type: Array,
      default: () => ([]),
    },
    currentIndex: {
      type: Number,
      default: 0,
    },
    item: {
      type: Object,
    },
    isBandwidth: {
      type: Boolean,
      default: false,
    },
    isMetric: {
      type: Boolean,
      default: false,
    },
    isBoundToIntersectionLocation: {
      type: Boolean,
      default: false,
    },
    isIndex: {
      type: Boolean,
      default: false,
    },
    isCard: {
      type: Boolean,
      default: false,
    },
    isTip: {
      type: Boolean,
      default: false
    },
    cardIndex: {
      type: Number,
      default: 0,
    },
    // 拖拽框类型
    dragBoxType: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    cascaderOptions: {
      type: Array,
      default: () => []
    },
    // 是否展示维度字段的日期文本域设置
    showTextDomainSetting: {
      type: Boolean,
      default: false
    },
    // 开启地图飞线
    isShowMapDestination: {
      type: Boolean,
      default: false
    },
  },

  computed: {
    dialogVisible: {
      get() {
        return this.isAddFileSetting
      },
      set(val) {
        this.$emit('close', val)
      },
    },
    chartUserConfig() {
      return this.currentEditData.content.chartUserConfig || {}
    },
    viewformatDisabled() {
      const { chartAlias, butterflySetting = {} } = this.chartUserConfig
      if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY && butterflySetting.dataPercent) return true
      return false
    },
    dimensionList() {
      return this.chartUserConfig.dimensionList || []
    },
    numberDisabled() {
      return this.originalDatasetField.labeName?.includes('CURRENCY') || this.originalDatasetField.currencyFlag === '1'
    },
    showZeroFormatComponent() {
      const zeroFormatHidden = this.$getFeatureConfig?.('metricZeroValue.hidden')
      return ['metric', 'target', 'contrastValue', 'hoverMetric', 'cardRatioValue', 'cardCompareValue', 'cardIndicator', 'cardTarget', 'bandwidth'].includes(this.type) && !zeroFormatHidden
    },
    showDateCustomFormatComponent() {
      return this.isDateDimension && this.isDateDimensionNone && !['ve-grid-normal', 've-themeRiver'].includes(this.chartUserConfig.chartAlias)
    },
    currentEditData() {
      // 兼容非图形元素结构
      return this.chart.currentEditData ? this.chart.currentEditData() : {
        content: { chartUserConfig: {} },
      }
    },
    isShowPresetDimensionSwitch() {
      return this.chartUserConfig.chartAlias !== 've-grid-normal' && this.chartUserConfig.dimensionList?.length !== 2
    },
    showPresetDimension() {
      const dimensionPresetHidden = this.$getFeatureConfig?.('dimensionPreset.hidden')
      const bool = this.dragBoxType === 'dimension' && !this.isShowMapDestination
      return bool && !dimensionPresetHidden
    },
    showViewFormatSetting() {
      return this.originalDatasetField.labeName && (this.isMetric || this.isBandwidth || this.isIndex || ['chartContrastValue', 'metric-hover'].includes(this.dragBoxType))
    },
    // 是否显示最小单位以下值简写开关
    showSmallShortSetting() {
      return ['metric', 'target', 'contrastValue', 'cardRatioValue', 'cardCompareValue', 'cardIndicator', 'bandwidth'].includes(this.type) && this.format === 'currencyUnit'
    },
    isDateDimension() {
      if (this.dragBoxType !== 'dimension') return false
      const { dimensionList = [] } = this.chartUserConfig
      const dateDimensionList = dimensionList.filter(d => d.columnTpe === 'date')
      if (dateDimensionList.length !== 1) return false
      return this.originalDatasetField.columnTpe === 'date'
    },
    isDateDimensionNone() {
      return this.item.timeDimensionSplittingRule === '_vs_none'
    },
    showCustomDateDimensionSetting() {
      const hasDateDimension = this.cascaderOptions.find(item => item.value === 'dateDimension')
      return hasDateDimension && CUSTOM_DATE_DIMENSION_CHART.includes(this.chartUserConfig.chartAlias) && this.dimensionList.length !== 2
    },
    // 比较方式的默认值
    compareTypeDefaultVal() {
      return this.compareTypeList.find(item => item.default)?.value
    },
    // 是否显示 隐藏别名 选择框
    isShowHideAlias() {
      return this.isCard && ['cardCompareValue'].includes(this.type)
    },
    // 显示格式化选项列表
    viewFormatComputedList() {
      const cfg = this.$getFeatureConfig?.('metricViewFormat')
      if (!cfg) return this.formatList
      const { hidden = [] } = cfg
      return this.formatList.filter(item => !hidden.includes(item.value))
    }
  },
  data() {
    return {
      showDecimals: ['number', 'percent', 'currencyUnit'],
      disableDecimals: ['percent'],
      paramName: {
        name: '',
      },
      originalDatasetField: {},
      zeroFormat: 'none',
      dateCustomFormat: '',
      timeCustomFormat: '',
      smallShort: false,
      presetDimensionLabel: false,
      timeDimensionSwitch: false,
      trendsDimensionSwitch: false,
      eventDimensionSwitch: false,
      formatList: [
        {
          value: 'normal',
          label: this.$t('sdp.views.regular'),
        },
        {
          value: 'number',
          label: this.$t('sdp.views.number'),
        },
        {
          value: 'percent',
          label: this.$t('sdp.views.percentage'),
        },
        {
          value: 'currencyUnit',
          label: this.$t('sdp.views.currencyUnit'),
        },
      ],
      zeroFormatList: [
        {
          value: 'none',
          label: this.$t('sdp.views.none'),
        },
        {
          value: 'open',
          label: this.$t('sdp.views.on'),
        },
        {
          value: 'close',
          label: this.$t('sdp.views.off'),
        },
      ],
      decimalsList: [
        {
          value: 0,
          label: '0',
        },
        {
          value: 1,
          label: '0.0',
        },
        {
          value: 2,
          label: '0.00',
        },
        {
          value: 3,
          label: '0.000',
        },
        {
          value: 4,
          label: '0.0000',
        },
        {
          value: 5,
          label: '0.00000',
        },
        {
          value: 6,
          label: '0.000000',
        },
      ],
      format: '',
      decimals: 0,
      dateCustomFormatList: [
        { label: 'M/dd/yyyy', value: 'M/dd/yyyy' },
        { label: 'yyyy/M/d', value: 'yyyy/M/d' },
        { label: 'yyyy-M-d', value: 'yyyy-M-d' },
        { label: 'yyyy.M.d', value: 'yyyy.M.d' },
        { label: 'yyyy/MM/dd', value: 'yyyy/MM/dd' },
        { label: 'yyyy-MM-dd', value: 'yyyy-MM-dd' },
        { label: 'yyyy.MM.dd', value: 'yyyy.MM.dd' },
        { label: 'yy/M/d', value: 'yy/M/d' },
        { label: 'yy-M-d', value: 'yy-M-d' },
        { label: 'yy.M.d', value: 'yy.M.d' },
        { label: 'yy-MM-dd', value: 'yy-MM-dd' },
        { label: 'dd.MM.yyyy', value: 'dd.MM.yyyy' },
        { label: 'yy/MM/dd', value: 'yy/MM/dd' },
        { label: 'MM-dd', value: 'MM-dd' },
        { label: 'MM.dd', value: 'MM.dd' },
        { label: 'MM/dd', value: 'MM/dd' },
        { label: 'M-d', value: 'M-d' },
        { label: 'M.d', value: 'M.d' },
        { label: 'M/d', value: 'M/d' },
        { label: 'dd', value: 'dd' },
        { label: 'd', value: 'd' },
      ],
      timeCustomFormatList: [
        { label: 'H:mm', value: 'H:mm' },
        { label: 'HH:mm', value: 'HH:mm' },
        { label: 'tt h:mm', value: 'tt h:mm' },
        { label: 'tt hh:mm', value: 'tt hh:mm' },
      ],
      dateDimensionOption: [
        {
          value: 'NO_OFFSET',
          label: this.$t('sdp.views.defaultVal')
        }, {
          value: 'ZH_OFFSET',
          label: this.$t('sdp.views.lNDWMQY')
        }, {
          value: 'EN_OFFSET',
          label: ' -x / x'
          }
      ],
      dateDimensionValue: '',
      // 文本域
      textDomain: 'none',
      textDomainOptions: [
        {
          value: 'none',
          label: this.$t('sdp.views.none'),
        },
        {
          // 日期本期,
          value: 'timeCurr',
          label: this.$t('sdp.views.currentPeriod'),
        },
        {
          // 日期上期
          value: 'timePre',
          label: this.$t('sdp.views.priorPeriods'),
        },
        {
          // Location 本期,
          label: `Location ${this.$t('sdp.views.current')}`,
          value: 'locationCurr'
        },
        {
          // Location 上期,
          label: `Location ${this.$t('sdp.views.prior')}`,
          value: 'locationPre'
        },
      ],
      compareType: '',
      compareTypeList: [],
      // 隐藏别名
      hideAlias: false
    }
  },
  watch: {
    textDomain(val, oldVal) {
      if (val && val !== oldVal) {
        if (val === 'timePre') {
          !this.compareType && (this.compareType = this.compareTypeDefaultVal)
        } else {
          this.compareType = ''
        }
      }
    }
  },
  methods: {
    // 提交参数
    confirm() {
      const alias = this.getDatasetLabel({
        ...this.item,
        alias: this.paramName.name.trim(),
      })
      // if (!this.validate(alias)) {
      //   return false
      // }
      // 当开启了预警订阅，则对比字段必须设置别名
      if (this.item.allowSubscribe && !this.paramName.name.trim()) {
        this.$message({ type: 'warning', message: this.$t('sdp.views.plsSetAlias') })
        return
      }
      const index = this.dragBoxType === 'warningLine' ? 0 : this.currentIndex
      const aliasArray = this.dragList.filter((e, i) => i !== index).map(e => this.getDatasetLabel(e)).filter(e => e === alias)
      if (aliasArray.length) {
        this.$message({
          type: 'warning',
          message: this.$t('sdp.views.modifyFiled'),
          duration: 3000,
        })
        return
      }
      const oldAlias = this.item.alias || this.originalDatasetField.labeName
      this.$set(this.item, 'alias', alias)
      this.changeDateDimensionVal()

      const tempObj = {
        alias,
        presetDimensionLabel: this.presetDimensionLabel,
        timeDimensionSwitch: this.timeDimensionSwitch,
        trendsDimensionSwitch: this.trendsDimensionSwitch,
        eventDimensionSwitch: this.eventDimensionSwitch,
      }
      let viewFormat = {}
      if (this.showViewFormatSetting) {
        const params = {
          format: this.format,
          decimals: this.decimals,
          numberDisabled: this.numberDisabled,
          smallShort: this.smallShort,
          showSmallShortSetting: this.showSmallShortSetting,
        }
        viewFormat = generateViewFormat(params)
      }
      if (this.showZeroFormatComponent) {
        viewFormat.zeroFormat = this.zeroFormat
      }
      if (this.showDateCustomFormatComponent) {
        viewFormat.dateCustomFormat = this.dateCustomFormat
        viewFormat.timeCustomFormat = this.timeCustomFormat
      }
      if (this.showTextDomainSetting) {
        tempObj.textDomain = this.textDomain
        if (this.textDomain === 'timePre') {
          tempObj.compareType = this.compareType
        }
      }

      Object.keys(viewFormat).length && (tempObj.viewFormat = viewFormat)

      this.updateChartData(oldAlias, alias)

      if (this.isShowHideAlias) {
        // 隐藏别名
        tempObj.sdpHideAlias = this.hideAlias
        this.$set(this.item, 'sdpHideAlias', this.hideAlias)
      }

      this.$emit('confirm', tempObj)
      return void '别名修改成功'
    },
    updateChartData(oldAlias, alias) {
      if (this.isTarget || !this.chart.currentEditData) return
      const chartUserConfig = this.chart.currentEditData().content.chartUserConfig
      let { warnLineSettingList = [], tooltipFormatterList = [] } = chartUserConfig
      // 处理卡片的预警
      if (this.isCard && warnLineSettingList[this.cardIndex]) {
        const warnLineItem = warnLineSettingList[this.cardIndex]
        // TODO 可能存在的问题：拖入相同字段，再修改后拖入字段的别名，会影响到第一个字段的预警字段别名
        warnLineSettingList = this.$_flatten(warnLineItem)
      }
      // 更新对应预警字段别名
      if (warnLineSettingList.length) {
        warnLineSettingList.forEach(item => {
          if (item.metric === oldAlias) {
            this.$set(item, 'metric', alias)
            // 卡片需要更新cardWarnLineType
            if (this.isCard && item.cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)[1]) {
              item.cardWarnLineType = item.cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)[0] + WARNLINE_TYPE_CONNECT_STR + alias
            }
          }
          if (item.metricKeyName === `ReferenceValue_${oldAlias}`) {
            this.$set(item, 'metricKeyName', `ReferenceValue_${alias}`)
          }
        })
      }
      // 处理图形辅助线
      const auxiliaryLineKey = ['xAuxiliaryLineData', 'yAuxiliaryLineData']
      auxiliaryLineKey.forEach(key => {
        if (chartUserConfig[key]?.length) {
          chartUserConfig[key].forEach(item => {
            if (item.auxiliaryLineType !== 'custom' && item.metricValue !== 'otherField') {
              item.metricValue === oldAlias && this.$set(item, 'metricValue', alias)
            }
          })
        }
      })
      // 处理tooltip字段名
      if (tooltipFormatterList.length) {
        tooltipFormatterList.some(item => {
          const flag = item.alias === oldAlias
          if (flag) {
            this.$set(item, 'alias', alias)
          }
          return flag
        })
      }

      if (this.isCard) {
        const { referenceValueList } = chartUserConfig
        // 处理卡片参考值
        referenceValueList.forEach(item => {
          item.referenceValueKey === `ReferenceValue_${oldAlias}` && this.$set(item, 'referenceValueKey', `ReferenceValue_${alias}`)
        })
      }
    },
    validate(alias) {
      if (alias === '') {
        alias = this.originalDatasetField.labeName
        this.$set(this.item, 'alias', alias)
        this.paramName.name = alias
      }
      return true
      // if (!/^\d+$/.test(alias.trim())) {
      //   alias.replace(/\s+/g, ' ')
      //   return true
      // }
      // this.$message({
      //   message: this.$t('sdp.views.aliasNotAllNumber'),
      //   type: 'warning',
      //   duration: 5000,
      // })
      // // this.paramName.name = ''
      // this.$refs['aliasInput'].focus()
      // return false
    },
    // 弹窗打开初始化数据
    initFileSettingData() {
      this.originalDatasetField = this.getOriginDatasetField(this.item) || {}
      const alias = this.getDatasetLabel(this.item)
      if (alias === undefined) {
        this.$set(this.item, 'alias', '')
      }
      this.paramName.name = alias || ''
      this.$nextTick(() => {
        this.checkAlias(this.paramName.name)
      })
      this.presetDimensionLabel = Boolean(this.item.presetDimensionLabel)
      this.timeDimensionSwitch = Boolean(this.item.timeDimensionSwitch)
      this.trendsDimensionSwitch = Boolean(this.item.trendsDimensionSwitch)
      this.eventDimensionSwitch = Boolean(this.item.eventDimensionSwitch)
      const viewFormat = this.item.viewFormat
      // 数据兼容，原先开启0值显示时，viewFormat.zeroFormat为true
      this.zeroFormat = !viewFormat?.zeroFormat ? 'none' : typeof viewFormat.zeroFormat === 'boolean' ? 'open' : viewFormat.zeroFormat
      this.dateCustomFormat = viewFormat ? viewFormat.dateCustomFormat : ''
      this.timeCustomFormat = viewFormat ? viewFormat.timeCustomFormat : ''

      this.getDateDimensionVal()

      if (this.showViewFormatSetting) {
        const params = {
          numberDisabled: this.numberDisabled,
          decimalsList: this.decimalsList,
        }
        const displayFormate = restoreViewformat(viewFormat, params)
        if (this.type === 'cardRatioValue') {
          if (!displayFormate.format) {
            displayFormate.format = 'percent'
          }
          if (!this.originalDatasetField.labeName.includes('CURRENCY') && displayFormate.decimals === '') {
            // 非货币字段
            displayFormate.decimals = 2
          }
        } else if (!displayFormate.format) {
          if (!['growth_rate', 'contrast_ratio'].includes(this.item.selectedConttast)) {
            // 没有设置【对比类型】为【增长率】、【对比率】
            displayFormate.format = 'normal'
            // 查看配置是否设置了默认格式
            const cfg = this.$getFeatureConfig?.('metricViewFormat')
            if (cfg?.default) {
              const { format, decimals } = cfg.default
              displayFormate.format = format || 'normal'
              displayFormate.decimals = decimals || 0
            }
          } else {
            displayFormate.format = 'percent'
            if (!this.originalDatasetField.labeName.includes('CURRENCY')) {
              // 非货币字段
              displayFormate.decimals = 2
            }
          }
        }
        this.smallShort = displayFormate.smallShort || false
        this.format = displayFormate.format || ''
        this.decimals = displayFormate.decimals
      } else {
        this.format = ''
        this.decimals = this.numberDisabled ? '' : 0
      }
      // 文本域
      if (this.showTextDomainSetting) {
        this.textDomain = this.item.textDomain || 'none'
        this.compareType = this.item.compareType || ''
        this.initCompareTypeList()
      } else {
        this.compareTypeList = []
      }
      // 隐藏别名
      if (this.isShowHideAlias) {
        this.hideAlias = this.item.sdpHideAlias || false
      } else {
        this.$delete(this.item, 'sdpHideAlias')
      }
      console.log('alias', alias)
    },
    // 别名不允许超过30个字符
    checkAlias(value) {
      if (value.length > 30) {
        this.paramName.name = this.paramName.name.substring(0, 29)
        this.$message(this.$t('sdp.message.tabNameLength'))
      }
    },

    presetDimensionLabelChangeHandler(isChecked) {
      if (isChecked) {
        this.trendsDimensionSwitch = false
        this.timeDimensionSwitch = false
        this.eventDimensionSwitch = false
      }
      if (isChecked && this.isBoundToIntersectionLocation) {
        // 组件的标签快选与图形的预制维度设置不能同时存在
        this.$message.warning(this.$t('sdp.message.locationIntersectionModeConfluentWithChartPresetDimension'))
        this.presetDimensionLabel = false
      }
    },
    timeDimensionSwitchChangeHandler(isChecked) {
      if (isChecked) {
        this.trendsDimensionSwitch = false
        this.presetDimensionLabel = false
        this.eventDimensionSwitch = false
      }
      // if (isChecked && this.isBoundToIntersectionLocation) {
      //   // 组件的标签快选与图形的预制维度设置不能同时存在
      //   this.$message.warning(this.$t('sdp.message.locationIntersectionModeConfluentWithChartPresetDimension'))
      //   this.timeDimensionSwitch = false
      // }
    },
    trendsDimensionSwitchHandler(isChecked) {
      if (isChecked) {
        this.timeDimensionSwitch = false
        this.presetDimensionLabel = false
        this.eventDimensionSwitch = false
      }
    },
   eventDimensionSwitchHandler(isChecked) {
     if (isChecked) {
       this.timeDimensionSwitch = false
       this.presetDimensionLabel = false
       this.trendsDimensionSwitch = false
     }
   },
    changeViewFormat(val) {
      // 显示格式从货币单位简写切换至其他格式时，应清空最小单位以下值简写开关配置
      if (val === 'currencyUnit' && this.smallShort) {
        this.smallShort = false
      }

      if (!this.numberDisabled) {
        this.decimals = this.decimals || 0
      } else if (['number', 'currencyUnit'].includes(this.format)) {
        // 货币字段选择数字、货币单位简写时，需要默认小数位
        this.decimals = this.format === 'number' ? 0 : 2
      } else {
        this.decimals = ''
      }
    },

    changeDateDimensionVal() {
      if (this.dragBoxType !== 'dimension') return
      if (this.originalDatasetField.columnTpe === 'date') {
        this.$set(this.item, 'timeDimensionSplittingOffset', this.dateDimensionValue)
      }
    },
    getDateDimensionVal() {
      if (this.dragBoxType !== 'dimension') return
      this.dimensionList.forEach((val, index, arr) => {
        if (val['columnTpe'] === 'date' && val['timeDimensionSplittingOffset']) {
          this.dateDimensionValue = val['timeDimensionSplittingOffset']
        } else {
          this.dateDimensionValue = 'NO_OFFSET'
        }
      })
    },
    initCompareTypeList() {
      this.utils.api.get('/rule/quickCalendarConfig/getAuthConfig/Compare_Type/' + this.utils.tenantId)
        .then(res => {
          this.compareTypeList = res.map(item => {
            const { configName: label, configValue: value, isDefault } = item
            return { label, value, default: String(isDefault) === '1' }
          })
          if (this.textDomain === 'timePre' && !this.compareType) {
            this.compareType = this.compareTypeDefaultVal
          }
        })
        .catch(e => {
          console.log('获取比较方式列表接口发生错误:', e)
        })
    },
  },
}
</script>
<style media="screen" lang="scss" scoped>
  .dateDimensions{
    margin-top: 25px;
  }
  .dateDimensions > span{
         margin-right: 15px;
  }
  .el-dialog__wrapper /deep/ {
    // text-align: center;
    .el-dialog {
      .el-dialog__body {
        padding: 20px 24px;
        .main {
          margin: 10px 0;
          .title {
            display: inline-flex;
            width: 80px;
            margin: 0 20px 0 0;
            line-height: 32px;
            text-align: right;
          }
          .flex{
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            .el-checkbox {
              .el-checkbox__input{
                position: relative;
                top: 1px;
              }
              .el-checkbox__label{
                color: var(--sdp-sztc-srkbt);
              }
            }
          }
          .el-input {
            width: 100%;
          }
        }
        .parent-2{
          display: flex;
          margin-top: -10px;
          .main{
            width: 50%;
          }
          .title{
            display: block;
            text-align: left;
          }
        }
        .preset-dimension {
          margin-top: 20px;
          text-align: left;
          // margin-left: 122px;
        }
      }
      .el-dialog__footer {
        padding-top: 0;
        .dialog-footer {
          display: block;
          text-align: right;
        }
      }
    }
  }
  .display-format {
    margin-top: 20px;
    > div {
      width: 180px;
    }
    > div:first-child {
      margin-bottom: 8px;
    }
    > div:nth-child(3) {
      margin-left: 16px;
    }
  }
  .zero-display /deep/ {
    .el-select {
      width: 180px;
      margin-top: 8px;
    }
    .item-label-container {
      display: block;
      font-size: 14px;
    }
  }
  .filed-setting-switch /deep/ {
    .item-label{
      width: 335px;
    }
    .table-switch-container.width-40 {
      margin-top: 0;
      justify-content: normal;
      .el-switch__core{
        width: 40px !important;
      }
    }
  }
  .small-short-setting {
    margin-top: 18px;
  }
  .setting-item{
    margin-top: 18px;
    overflow: hidden;
    .item-label{
      line-height: 18px;
    }
  }

  .textDomains{
    margin: 10px 0;
    span{
      display: block;
    }
    .el-select {
      width: 180px;
      margin-top: 8px;
    }
  }
</style>
