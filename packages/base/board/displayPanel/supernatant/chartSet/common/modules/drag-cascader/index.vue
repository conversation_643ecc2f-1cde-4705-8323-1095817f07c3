<template>
  <!-- 这个class不能去掉dragCascader-container -->
  <div class="dragCascader-container" :key="aliasDict.dictKey + item.labeName">
    <div class="dragCascader" :title="getDatasetLabel(item) + functionNameInTag" ref="dragCascader" :class="[dragBoxType === 'dimension' ? 'dimension-tag' : '']">
      <el-tag
        ref='tag'
        :class="{trimMaxWidth}"
        :closable="!['metric-short', 'warningLine'].includes(dragBoxType) && !item.isGridCustom"
        :disable-transitions="false"
        :style="trimWidthStyle"
        @close="handleClose">
        <div :class="['tag-content', { 'no-close-icon': item.isGridCustom }]">
          <i class="el-icon-arrow-down"></i>
          <span class="tag-alias">{{ getDatasetLabel(item) }}</span>
          <!-- 排名卡片维度不显示函数 -->
          <span class="tag-func" v-if="type!=='rankValue'">{{ functionNameInTag }}</span>
        </div>
      </el-tag>
      <el-cascader
        expand-trigger="click"
        :options="cascaderOptions"
        v-model="selected"
        ref="cascader"
        :popper-class="'cascader-boxxx ' + getCurrentThemeClass()"
        size="mini"
        :disabled="disabled"
        :show-all-levels="false"
        @visible-change="selected = []"
        @change="handleChange($event, 'click')">
      </el-cascader>
    </div>

    <!-- 字段设置 -->
    <addFieldSettingDialog
      v-if="!['metric-short'].includes(dragBoxType)"
      :dragList="dragList"
      ref="AddFileSetting"
      :isAddFileSetting="isAddFileSetting"
      :current-index="index"
      :card-index="cardIndex"
      :item="item"
      :isBoundToIntersectionLocation="isBoundToIntersectionLocation"
      :isShowMapDestination="isShowMapDestination"
      :isMetric="isMetric"
      :isBandwidth="isBandwidth || isAutoBandwidth"
      :isIndex="isIndex"
      :isTarget="isTarget"
      :isCard="isCard"
      :type="type"
      :elementType="isCard ? 3 : 1"
      :dragBoxType="dragBoxType"
      :cascaderOptions="cascaderOptions"
      @confirm="confirmFileAlias"
      @close="close"
    />

    <!-- 自定义排序 -->
    <addCustomSortDialog
      v-if="!['metric-short', 'warningLine'].includes(dragBoxType)"
      :isCustomsort="isCustomsort"
      :dragList="dragList"
      :current-index="index"
      :card-index="cardIndex"
      :elementType="isCard ? 3 : 1"
      :drillSettings="drillSettings"
      @confirm="confirmCustom"
      ref="addCustomSortDialog"
      @close="close"
    />
    <!-- 自定义计算 -->
    <add-custom-dialog
      :visible.sync="isCustom"
      :datasetId="addCustomDataSetId"
      :isAssociationDataset="chart.isAssociationDataset && chart.isAssociationDataset()"
      :datasetList="chart.datasetList()"
      isChartCustom
      :cascader="item"
      @submit="confirmAddCustom"
      />

    <!-- 钻取 -->
    <add-drill-dialog
      v-if="dragBoxType === 'dimension'"
      ref="addDrillDialog"
      isChartDrill
      :index="index"
      :isAddDrillSetting.sync="isAddDrillSetting"
      :dimensionList="dragList"
      :dataList="chart.fieldList"
      :content="currentEditData.content"
      @confirm="confirmDrill"
      @close="close"
      :themeType="themeType"
      />
    <CustomDimensionDialog
      :ref="dialogName"
      :formData="item"
      :visible.sync="dialogVisible"
      :isAssociationDataset="chart.isAssociationDataset && chart.isAssociationDataset()"
      :datasetList="chart.datasetList()"
      :drillSettings="drillSettings"
      @confirm="a => dialogConfirm(a, dialogName)"
    ></CustomDimensionDialog>
    <!-- 同环比 -->
    <index-calendar-setting-dialog
      :visible="isAddIndexCalendarSetting"
      :item="item"
      :datasetId="addCustomDataSetId"
      :dragList="dragList"
      :current-index="index"
      :card-index="cardIndex"
      :elementType="isCard ? 3 : 1"
      :drillSettings="drillSettings"
      :content="currentEditData.content"
      @confirm="confirmIndexCalendar"
      ref="indexCalendarSettingDialog"
      @close="close"
    />
  </div>

</template>

<script>
import addFieldSettingDialog from './addFieldSettingDialog'
import addCustomSortDialog from './addCustomSortDialog'
import addDrillDialog from './addDrillSettingDialog'
import CustomDimensionDialog from './customDimensionDialog'
import indexCalendarSettingDialog from './indexCalendarSettingDialog'
import addCustomDialog from 'packages/base/grid/components/attributes-panel/AttributePanelDialogCustom'
import { generateViewFormat } from 'packages/base/board/displayPanel/supernatant/chartSet/utils/generator'
import { commonCalcOptions, sortOptions, businessOptions, dateDimensionOptions, alignOptions, stackAccOptions } from './options'
import {
  NEED_TIMEDIMENSION_CHART,
  DOUBLE_DIMENSION_CHART,
  NOT_SUPPORT_DIMENSION_CHART,
  STACK_ACC_TYPES,
  STACK_ACC_CHART,
  CHART_ALIAS_TYPE,
  DATE_DIMENSION_TYPE,
  SOURCE_DATA_TYPE
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { TAGNEWCARD } from 'packages/assets/constant'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import { DAY_DATA_VALUE } from 'packages/base/grid/components/attributes-panel/dateDimensionSwitch/constant'
import { THEME_TYPE } from 'packages/assets/constant'

export default {
  mixins: [datasetMixin],
  inject: {
    chart: { default: {} },
    utils: { default: {} },
    isTemplateBoard: { default: false },
    getCurrentThemeClass: { default: () => () => '' },
  },
  name: 'DragCascader',
  props: {
    item: {
      type: Object,
    },
    // 拖拽栏数据索引
    index: {
      type: Number,
    },
    // 卡片索引
    cardIndex: {
      type: Number,
    },
    isBoundToIntersectionLocation: {
      type: Boolean,
    },
    isMetric: {
      type: Boolean,
      default: false,
    },
    // 是否辅助线设置
    isAuxiliaryLineSetting: {
      type: Boolean,
      default: false,
    },
    // cascaderValue为对象时，需要传入一个当前cascader的唯一key
    cascaderUniqueKey: {
      type: String,
      default: ''
    },
    // 是否比率卡片
    isRateCard: {
      type: Boolean,
      default: false,
    },
    // 卡片指标值
    isIndex: {
      type: Boolean,
      default: false,
    },
    // 卡片目标值
    isTarget: {
      type: Boolean,
      default: false,
    },
    // 比率值
    isRatioValue: {
      type: Boolean,
      default: false,
    },
    // 对比值
    isCompareValue: {
      type: Boolean,
      default: false,
    },
    // 对比值
    isRankValue: {
      type: Boolean,
      default: false,
    },
    dragList: {
      type: Array,
    },
    // 拖拽框类型
    dragBoxType: {
      type: String,
      default: '',
    },
    // 卡片没有传入drillSettings
    drillSettings: {
      type: Object,
      default: () => ({}),
    },
    currentEditData: {
      type: Object,
      default: () => ({}),
    },
    customCascader: {
      type: [Array, Boolean],
      default: false,
    },
    trimMaxWidth: {
      type: Number,
      default: () => 0
    },
    metricType: {
      default: '',
    },
    isTargetLable: {
      type: Boolean,
      default: false,
    },
    isCard: {
      type: Boolean,
      default: false,
    },
    // 带宽
    isMinBandwidth: {
      type: Boolean,
      default: false
    },
    isMaxBandwidth: {
      type: Boolean,
      default: false
    },
    // 自动带宽
    isAutoBandwidth: {
      type: Boolean,
      default: false
    },
    // 经度
    isLongitude: {
      type: Boolean,
      default: false
    },
    // 纬度
    isLatitude: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 开启地图飞线
    isShowMapDestination: {
      type: Boolean,
      default: false
    },
  },

  components: {
    // addConttastDialog,
    addFieldSettingDialog,
    addCustomSortDialog,
    addCustomDialog,
    addDrillDialog,
    CustomDimensionDialog,
    indexCalendarSettingDialog,
  },

  data() {
    return {
      selected: [],
      isAddFileSetting: false,
      isCustomsort: false,
      isAddDrillSetting: false,
      isCustom: false,
      isAddIndexCalendarSetting: false,
      dialogSort: {
        FILELDSETTING: 'isAddFileSetting',
        DRILLSETTING: 'isAddDrillSetting',
        CUSTOM: 'isCustom',
        INDEX_CALENDAR: 'isAddIndexCalendarSetting',
      },
      dialogName: '',
      dialogItem: {},
      dialogVisible: false,
    }
  },
  mounted() {
  },
  created() {
    this.initCreated()
    this.initCompareData = this.$_debounce(this.initCompareData)

    if (this.item.webFieldFrom === 'associationDataset' && this.item.aggType === 'DIM' && this.dragBoxType === '') {
      this.$set(this.item, 'aggType', 'SUM')
    }
  },
  destroyed() {
  },
  watch: {
    'item.timeDimensionSplittingRule'(val) {
      this.settingActiveCascader('dateDimension', this.activeCascader, val)
    },
    'drillSettings.timeDimensionSplitting': {
      handler(val) {
        this.$nextTick(() => {
          // 时间维度拆分选中后, 置灰自定义排序
          this.disabledCustomSort()
          // 时间拆分被选中后，日期维度默认选择按天, 其余选项置灰
          this.dateDimenisonDefault(val)
        })
      },
      immediate: true,
    },
    'chartUserConfig.chartAlias': {
      handler(val, oldVal) {
        this.fixDrillOption(val)
        this.fixAlign(val, { from: 'alias-change', value: 'left' })
        if (val === 've-calendar' && this.item.columnTpe === 'date') {
          this.handleChange(['dateDimension', DATE_DIMENSION_TYPE.Day_vs_day])
        } else if (val === 've-themeRiver' && this.item.columnTpe === 'date') {
          const _dateDimensionVal = this.getDateDimensionVal()
          this.handleChange(['dateDimension', _dateDimensionVal])
        } else if (val === 've-grid-normal') {
          const { metricsContainer, dimensionList } = this.chartUserConfig
          const hasDimensionSort = dimensionList.some(dime => dime.order)
          const hasMetricSort = metricsContainer.default.some(dime => dime.order)
          if (hasDimensionSort && hasMetricSort && this.dragBoxType === 'dimension' && this.index === 0) {
            this.$emit('clear-status', {
              type: 'SORT',
              itemType: 'dimension',
              item: this.item,
            })
          }
        }
        if (oldVal === 've-grid-normal') {
          this.doubleDimensionSetting()
        }
      },
    },
    'chartUserConfig.chartType'(val) {
      if (val === 've-map') {
        this.initCascader()
      }
    },
    // 切换卡片类型时指标值需要初始化
    'isRateCard'(val) {
      if (val || this.index) {
        this.initCardData()
      }
    },
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex')
    },
    computedCommonCalcOptions() {
      if (this.onlyIndex) {
        return commonCalcOptions.filter(c => c.value !== 'MEDIAN')
      }
      return commonCalcOptions
    },
    addCustomDataSetId() {
      if (this.onlyIndex) {
        if (typeof this.item.indexId === 'number') {
          return this.item.parentId
        }
        return this.item.indexId || this.item.parentId
      }
      return this.drillSettings.dataSetId || this.item.dataSetJoinsDataSetId || this.item.parentId
    },
    themeType() {
      return this.utils?.themeParameters?.themeType || THEME_TYPE.default
    },
    trimWidthStyle() {
      if (!this.trimMaxWidth) return {}
      return {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        width: this.trimMaxWidth - 10 + 'px',
        paddingRight: '12px',
      }
    },
    dragboxType() {
      if (this.dragBoxType) return this.dragBoxType
      if (this.isMetric) return 'metric'
      if (this.isLongitude) return 'longitude'
      if (this.isLatitude) return 'latitude'
      if (this.isAutoBandwidth) return 'bandwidth-auto'
      return ''
    },
    cascaderOptions() {
      const chartAlias = this.chartUserConfig?.chartAlias
      const childChartAlias = this.currentEditData?.content?.chartUserConfig?.childChartAlias
      let optionArr = []
      let result = []
      if (this.item.isGridCustom) {
        optionArr = ['SORT']
      } else if (this.dragBoxType === 'dimension') {
        optionArr = ['sort']
        if (NOT_SUPPORT_DIMENSION_CHART.includes(chartAlias) || NOT_SUPPORT_DIMENSION_CHART.includes(childChartAlias)) {
          optionArr = ['sort']

          if (['ve-decomposition'].includes(chartAlias)) {
            optionArr = []
          }
        } else if (['ve-roundCascades'].includes(chartAlias)) {
          optionArr = []
        }
        if (this.item.columnTpe === 'date') {
          const dateDimensionList = this.dragList.filter(d => d.columnTpe === 'date')
          let dateCascader = []
          if (dateDimensionList.length === 1 && !['ve-calendar', 've-themeRiver', CHART_ALIAS_TYPE.VE_BANDWIDTH].includes(chartAlias)) {
            dateCascader = ['dateDimension']
          }
          optionArr = dateCascader.concat(optionArr)
        }
        if (chartAlias === 've-themeRiver' && this.index === 0) {
          optionArr = optionArr.filter(o => o !== 'sort')
        }
      } else if (this.dragBoxType === 'dimension-extend') {
        optionArr = ['sort']
      } else if (this.dragBoxType === 'warningLine') {
        optionArr = ['BUSINESS', 'CONTRAST']
      } else if (this.isAuxiliaryLineSetting || ['metric-short', 'calendarFactorSetting', 'referenceValueSetting'].includes(this.dragBoxType)) {
        optionArr = ['BUSINESS', 'CONTRAST']
      } else if (!this.isRankCard && !this.isRateCard && !this.isCompareCard && (this.isIndex || this.isTarget)) {
        optionArr = ['BUSINESS']
      } else if (this.isRateCard || this.isCompareCard) {
        optionArr = ['BUSINESS', 'CONTRAST']
      } else if (this.isRankCard) {
        if (this.type === 'rankValue') {
          optionArr = []
        } else {
          optionArr = ['BUSINESS', 'SORT']
        }
      } else {
        optionArr = ['BUSINESS', 'CONTRAST', 'SORT']
        if (['ve-roundCascades', 've-decomposition'].includes(chartAlias)) {
          optionArr = ['BUSINESS', 'CONTRAST']
        }
      }
      if (chartAlias === 've-grid-normal' && this.dragBoxType !== 'warningLine') {
        optionArr.push('ALIGN')
      } else if (chartAlias === 've-grid-normal' && this.dragBoxType === 'warningLine') {
        optionArr = []
      }
      if (this.dragBoxType === 'dimension-hover' || this.dragBoxType === 'dimension-destination' || this.isLongitude || this.isLatitude || this.isAutoBandwidth || this.isShowMapDestination) {
        optionArr = []
      }
      if (this.dragBoxType === 'metric-hover') {
        optionArr = ['BUSINESS', 'CONTRAST']
      }
      if (this.isMetric && STACK_ACC_CHART.includes(chartAlias)) {
        optionArr.push('STACKACC')
      }
      // 只有指标的场景，度量增加“同环比”
      if (this.onlyIndex) {
        optionArr.push('INDEX_CALENDAR')
      }
      optionArr = ['COMMON', 'CUSTOM', ...optionArr, 'DRILLSETTING', 'FILELDSETTING', 'CUSTOM_DIMENSION']
      optionArr.forEach(optionKey => {
        const option = this.computedOptions.find(opt => opt.value === optionKey)
        if (!option) return
        if (option.showFn && typeof option.showFn === 'function') {
          option.showFn() && result.push(option)
        } else {
          result.push(option)
        }
      })
      return result
    },
    isChinaMap() {
      return this.chartUserConfig?.childChartAlias === 've-map-china'
    },
    computedOptions() {
      const ITEM = this.item
      const checkedDot = '● '
      const { chartAlias = void 0, compareModeData = {}, mapRangeVal = void 0, dateDimensionSetting = {}, switchDateDimension = void 0, extendDragBoxShow = void 0, childChartAlias = void 0 } = this.chartUserConfig || {}
      const isDrillDisabled = this.isChinaMap && mapRangeVal === 'provice'
      const isTimeSplit = this.drillSettings.timeDimensionSplitting === '1'
      const isYearOrMonth = ['yyyy-MM', 'yyyy'].includes(ITEM.dateFormat)

      const dateDimensionItems = dateDimensionSetting.dateDimensionItems
      const needFilter = switchDateDimension && dateDimensionItems && !isTimeSplit
      const noYearOrMonthAndIsTimeSplit = !isYearOrMonth && isTimeSplit

      const compareList = this.$_JSONClone(compareModeData?.compareList || [])
      const metricSortOptions = sortOptions.filter(c => c.value !== 'customSort').map(c => {
        return {
          value: c.value,
          label: c.label,
          selectFn: c.selectFn,
        }
      })
      const selectedFn = (c) => {
        const func = c.selectFn || (() => false)
        const res = func.call(this, this.item)
        return res ? checkedDot : ''
      }
      const disabledFn = (c) => {
        const func = c.disabledFn || (() => false)
        const res = func.call(this, this.item)
        return !!res
      }
      const childrenFn = (arr) => {
        return arr.map(c => {
          return {
            value: c.value,
            label: selectedFn(c) + this.$t(c.label),
            disabled: disabledFn(c),
          }
        })
      }
      const allOptions = [
        {
          value: 'COMMON',
          label: (this.computedCommonCalcOptions.find(c => selectedFn(c)) ? checkedDot : '') + this.$t('sdp.views.commonCalc'),
          showFn: () => {
            if (this.item.webFieldType === 'customComputed' ||
                this.item.isGridCustom ||
                (this.isRankCard && this.type === 'rankValue') ||
                this.isShowMapDestination
            ) return false
            const noCommonArray = ['dimension', 'longitude', 'dimension-hover', 'latitude', 'dimension-extend', 'bandwidth-auto', 'dimension-destination']
            if (noCommonArray.includes(this.dragboxType)) return false
            return true
          },
          children: childrenFn(this.computedCommonCalcOptions),
        },
        {
          value: 'sort',
          label: (sortOptions.find(c => selectedFn(c)) ? checkedDot : '') + this.$t('sdp.views.sort'),
          children: childrenFn(sortOptions),
        },
        {
          value: 'CUSTOM',
          label: (['expression', 'cross_expression'].find(e => e === ITEM.aggType || e === ITEM.type) ? checkedDot : '') + this.$t('sdp.views.customCalc'),
          showFn: () => {
            // 使用指标数据集时，不展示自定义计算
            if (this.currentEditData?.content?.drillSettings?.sourceDataType === SOURCE_DATA_TYPE.indexFlag && !this.onlyIndex) return false
            
            if (['customComputed', 'metricGroup'].includes(this.item.webFieldType)) return false
            const noCommonArray = ['dimension', 'dimension-hover', 'dimension-extend', 'dimension-destination']
            if (this.item.isGridCustom ||
                noCommonArray.includes(this.dragboxType) ||
                (this.isRankCard && this.type === 'rankValue') ||
                (chartAlias === 've-grid-normal' && this.dragBoxType === 'warningLine') ||
                this.isLongitude ||
                this.isLatitude ||
                this.isAutoBandwidth
            ) return false
            return true
          },
        },
        {
          value: 'CONTRAST',
          label: (ITEM.aggType === 'CONTRAST' ? checkedDot : '') + this.$t('sdp.views.boardCompare'),
          children: [
              {
                value: 'none',
                label: this.$t('sdp.views.none'),
              }, {
                value: 'growth_rate',
                label: (ITEM.selectedConttast === 'growth_rate' ? checkedDot : '') + this.$t('sdp.views.growthRate'),
                children: compareList.map(item => {
                  return {
                    ...item,
                    label: (ITEM.selectedConttast === 'growth_rate' && ITEM.selectedConttastMode === item.value ? checkedDot : '') + item.label,
                  }
                }),
              }, {
                value: 'growth_in_value',
                label: (ITEM.selectedConttast === 'growth_in_value' ? checkedDot : '') + this.$t('sdp.views.growthValue'),
                children: compareList.map(item => {
                  return {
                    ...item,
                    label: (ITEM.selectedConttast === 'growth_in_value' && ITEM.selectedConttastMode === item.value ? checkedDot : '') + item.label,
                  }
                }),
              }, {
                value: 'reduced_value',
                label: (ITEM.selectedConttast === 'reduced_value' ? checkedDot : '') + this.$t('sdp.views.BoardCompareValue'),
                children: compareList.map(item => {
                  return {
                    ...item,
                    label: (ITEM.selectedConttast === 'reduced_value' && ITEM.selectedConttastMode === item.value ? checkedDot : '') + item.label,
                  }
                }),
              }, {
                value: 'contrast_ratio',
                label: (ITEM.selectedConttast === 'contrast_ratio' ? checkedDot : '') + this.$t('sdp.views.BoardCompareRatio'),
                children: compareList.map(item => {
                  return {
                    ...item,
                    label: (ITEM.selectedConttast === 'contrast_ratio' && ITEM.selectedConttastMode === item.value ? checkedDot : '') + item.label,
                  }
                }),
              }
          ],
          showFn: () => (true && !this.$getFeatureConfig?.('metricContrast.hidden'))
        },
        {
          value: 'SORT',
          label: (metricSortOptions.find(c => selectedFn(c)) ? checkedDot : '') + this.$t('sdp.views.sort'),
          children: childrenFn(metricSortOptions)
        },
        {
          value: 'FILELDSETTING',
          label: checkedDot + this.$t('sdp.views.bWordSet'),
          showFn: () => {
            if (
              ['dimension-extend', 'metric-short', 'calendarFactorSetting', 'referenceValueSetting'].includes(this.dragBoxType) ||
              this.isAuxiliaryLineSetting
            ) return false
            return true
          },
        },
        {
          value: 'DRILLSETTING',
          label: ((this.currentEditData.content?.drillList || []).find(d => {
            return (d.drillDimensionKey && d.drillDimensionKey === ITEM.keyName) ||
              (!d.drillDimensionKey && (ITEM.labeName) === (d.keyName))
          }) ? checkedDot : '') + this.$t('sdp.views.Drill'),
          disabled: isDrillDisabled,
          showFn: () => {
            // 热力图只有X轴维度支持钻取
            if (
              this.dragBoxType !== 'dimension' ||
              NOT_SUPPORT_DIMENSION_CHART.includes(chartAlias) || NOT_SUPPORT_DIMENSION_CHART.includes(childChartAlias) ||
              (this.index === 0 && this.dragList.length > 1 && !['ve-grid-normal'].includes(chartAlias)) ||
              this.isShowMapDestination ||
              (['ve-bar-Heatmap'].includes(chartAlias) && this.dragList.length === 1 && this.dragList[0].DIMENSIONTYPE === 'y')
            ) return false
            const { dimensionList = [] } = this.chartUserConfig
            if (dimensionList.find(i => ['metricGroup', 'customComputed'].includes(i.webFieldType))) return false
            return true
          },
        },
        {
          value: 'dateDimension',
          label: (dateDimensionOptions.find(c => selectedFn(c)) ? checkedDot : '') + this.$t('sdp.views.dateDimension'),
          children: [
            {
              value: '_vs_none',
              label: (ITEM.timeDimensionSplittingRule === '_vs_none' ? checkedDot : '') + this.$t('sdp.views.none'),
            },
            {
              value: DATE_DIMENSION_TYPE.Day_vs_day,
              label: (ITEM.timeDimensionSplittingRule?.includes(DATE_DIMENSION_TYPE.Day_vs_day) ? checkedDot : '') + this.$t('sdp.views.forDate'),
              disabled: isYearOrMonth || (needFilter && !dateDimensionItems.find(d => d === DATE_DIMENSION_TYPE.Day_vs_day)),
              children: [
                {
                  value: DAY_DATA_VALUE.Default,
                  label: (ITEM.timeDimensionSplittingRule === DATE_DIMENSION_TYPE.Day_vs_day ? checkedDot : '') + this.$t('sdp.views.default')
                },
                {
                  value: DAY_DATA_VALUE.Day,
                  label: (ITEM.timeDimensionSplittingRule === DATE_DIMENSION_TYPE.Day_vs_day_d ? checkedDot : '') + 'd'
                }
              ],
            },
            {
              value: 'week_vs_week',
              label: (ITEM.timeDimensionSplittingRule === 'week_vs_week' ? checkedDot : '') + this.$t('sdp.views.forWeek'),
              disabled: isTimeSplit !== isYearOrMonth || (needFilter && !dateDimensionItems.find(d => d === 'week_vs_week'))
            },
            {
              value: 'month_vs_month',
              label: (ITEM.timeDimensionSplittingRule === 'month_vs_month' ? checkedDot : '') + this.$t('sdp.views.forMonth'),
              disabled: noYearOrMonthAndIsTimeSplit || (!isTimeSplit && ['yyyy'].includes(ITEM.dateFormat)) || (needFilter && !dateDimensionItems.find(d => d === 'month_vs_month'))
            },
            {
              value: 'quarter_vs_quarter',
              label: (ITEM.timeDimensionSplittingRule === 'quarter_vs_quarter' ? checkedDot : '') + this.$t('sdp.views.forQuarter'),
              disabled: noYearOrMonthAndIsTimeSplit || (!isTimeSplit && ['yyyy'].includes(ITEM.dateFormat)) || (needFilter && !dateDimensionItems.find(d => d === 'quarter_vs_quarter'))
            },
            {
              value: 'half_year_vs_half_year',
              label: (ITEM.timeDimensionSplittingRule === 'half_year_vs_half_year' ? checkedDot : '') + this.$t('sdp.views.forHalfYear'),
              disabled: noYearOrMonthAndIsTimeSplit || (needFilter && !dateDimensionItems.find(d => d === 'half_year_vs_half_year'))
            },
            {
              value: 'year_vs_year',
              label: (ITEM.timeDimensionSplittingRule === 'year_vs_year' ? checkedDot : '') + this.$t('sdp.views.forYear'),
              disabled: noYearOrMonthAndIsTimeSplit || (needFilter && !dateDimensionItems.find(d => d === 'year_vs_year'))
            },
          ]
        },
        {
          value: 'CUSTOM_DIMENSION',
          label: (ITEM.customDimension?.expression ? checkedDot : '') + this.$t('sdp.views.customDimension'),
          showFn: () => this.dragBoxType === 'dimension' && !this.isAssociationDataset && !['metricGroup', 'customComputed'].includes(this.item.webFieldType) && !this.isShowMapDestination && !this.$getFeatureConfig?.('customDimension.hidden'),
        },
        {
          value: 'ALIGN',
          label: (alignOptions.find(c => selectedFn(c)) ? checkedDot : '') + this.$t('sdp.views.alignType'),
          children: childrenFn(alignOptions),
        },
        {
          value: 'STACKACC',
          label: (stackAccOptions.find(c => selectedFn(c)) ? checkedDot : '') + this.$t('sdp.views.stackingAccumulation'),
          disabled: extendDragBoxShow,
          children: childrenFn(stackAccOptions),
          showFn: () => (true && !this.$getFeatureConfig?.('metricStackCc.hidden'))
        },
        {
          value: 'INDEX_CALENDAR',
          label: (ITEM.calendar && ITEM.calendar.compareColumn ? checkedDot : '') + this.$t('sdp.views.yoymom'),
          showFn: () => {
            const hiddenDragTypes = ['dimension', 'dimension-hover', 'dimension-extend', 'dimension-destination']
            return this.onlyIndex && !hiddenDragTypes.includes(this.dragBoxType)
          }
        },
      ]
      return allOptions
    },
    functionNameInTag() {
      const webFieldType = this.item.webFieldType
      if (webFieldType === 'customComputed') {
        return strConcat('CUSTOM')
      }
      if (['dimension', 'dimension-extend', 'dimension-hover', 'dimension-destination'].includes(this.dragBoxType) || this.isLongitude || this.isLatitude || this.isAutoBandwidth) {
        if (this.dragBoxType === 'dimension' && this.item.customDimension?.expression) return strConcat('CUSTOM')
        if (
          this.item.columnTpe !== 'date' ||
          this.dragBoxType !== 'dimension' ||
          ['ve-themeRiver', CHART_ALIAS_TYPE.VE_BANDWIDTH].includes(this.chartUserConfig.chartAlias) ||
          this.item.customDimension?.expression
        ) return ''

        const dateDimensionList = this.dragList?.filter(d => d.columnTpe === 'date')
        if (dateDimensionList.length !== 1) return ''

        const dateDimensionObj = {
          [DATE_DIMENSION_TYPE.Day_vs_day]: 'DAY',
          [DATE_DIMENSION_TYPE.Day_vs_day_d]: 'DAY',
          week_vs_week: 'WEEK',
          month_vs_month: 'MONTH',
          quarter_vs_quarter: 'QUARTER',
          year_vs_year: 'YEAR',
        }

        const timeDimensionSplittingRule = this.item.timeDimensionSplittingRule
        const { dateDimension } = this.activeCascader
        // 双维度不显示日期维度配置
        const ret = dateDimensionObj[timeDimensionSplittingRule || dateDimension]
        return strConcat(ret)
      }
      if (this.item.isGridCustom) return ''

      const aggType = this.item.aggType === 'CONTRAST' ? this.item.type : this.item.aggType

      const commonOptions = this.cascaderOptions.find(opt => opt.value === 'COMMON')
      if (commonOptions) {
        const COMMON = commonOptions.children.find(opt => opt.value === aggType)
        if (COMMON) return strConcat(COMMON.value)
      }
      if (this.cascaderOptions.find(opt => opt.value === 'CUSTOM') && ['expression', 'cross_expression'].includes(aggType)) {
        return strConcat('CUSTOM')
      }
      return strConcat('SUM')
      function strConcat(str) {
        if (!str) return ''
        return `(${ str })`
      }
    },
    chartUserConfig() {
      return this.currentEditData?.content?.chartUserConfig || this.currentEditData?.content
    },
    asMetric() {
      return ['chartContrastValue'].includes(this.dragBoxType) || this.dragBoxType === 'metric-hover'
    },
    isBandwidth() {
      return this.isMinBandwidth || this.isMaxBandwidth
    },
    activeCascader: {
      get() {
        if (['metric-short', 'warningLine'].includes(this.dragBoxType) && this.customCascader) return this.customCascader[this.index] || {}
        if (this.chartUserConfig && !this.chartUserConfig[this.cascaderValueKey]) return {}
        if (this.isLongitude || this.isLatitude || this.asMetric || this.isBandwidth || this.isAutoBandwidth || ['calendarFactorSetting', 'dimension-hover', 'referenceValueSetting', 'dimension', 'warningLine', 'dimension-extend'].includes(this.dragBoxType)) {
          return this.chartUserConfig?.[this.cascaderValueKey]?.[this.index] || {}
        } else if (this.isAuxiliaryLineSetting) {
          return this.chartUserConfig[this.cascaderValueKey][this.cascaderUniqueKey]?.[this.index] || {}
        } else if (this.isIndex || this.isTarget || this.isRatioValue || this.isCompareValue || this.isRankValue) {
          const index = this.isRateCard || this.isCompareCard || this.isRankValue ? this.index : this.cardIndex
          return this.chartUserConfig[this.cascaderValueKey][index] || {}
        } else {
          const arr = ['default', 'x', 'histogram']
          const key = arr.includes(this.metricType) ? 'default' : (this.metricType === 'size' ? 'size' : 'y')
          return this.chartUserConfig.metricCascaderValue ? this.chartUserConfig.metricCascaderValue[key][this.index] || {} : {}
        }
      },
      set(val) {
        if (['metric-short', 'warningLine'].includes(this.dragBoxType) && this.customCascader) {
          this.$set(this.customCascader, this.index, val)
          return
        }
        const cascaderValue = this.chartUserConfig?.[this.cascaderValueKey]
        if (!cascaderValue) return
        if (this.isLongitude || this.isLatitude || this.asMetric || this.isBandwidth || this.isAutoBandwidth || ['calendarFactorSetting', 'dimension-hover', 'referenceValueSetting', 'dimension', 'warningLine', 'dimension-extend'].includes(this.dragBoxType)) {
          this.$set(cascaderValue, this.index, val)
        } else if (this.isAuxiliaryLineSetting) {
          this.$set(cascaderValue[this.cascaderUniqueKey], this.index, val)
        } else if (this.isIndex || this.isTarget || this.isRatioValue || this.isCompareValue || this.isRankValue) {
          const index = this.isRateCard || this.isCompareCard || this.isRankValue ? this.index : this.cardIndex
          this.$set(cascaderValue, index, val)
        } else {
          const arr = ['default', 'x', 'histogram']
          const key = arr.includes(this.metricType) ? 'default' : (this.metricType === 'size' ? 'size' : 'y')
          this.$set(cascaderValue[key], this.index, val)
        }
      }
    },
    // 存储函数内容的key
    cascaderValueKey() {
      const reflectObj = {
        'dimension': 'dimensionCascaderValue',
        isMetric: 'metricCascaderValue',
        'dimension-extend': 'extendDimensionCascaderValue',
        isIndex: 'indexCascaderValue',
        isTarget: 'targetCascaderValue',
        isRatioValue: 'ratioCascaderValue',
        isCompareValue: 'compareCascaderValue',
        'warningLine': 'warnCascaderValue',
        auxiliaryLine: 'auxiliaryCascaderValue',
        customAuxiliaryLine: 'customAuxiliaryCascaderValue',
        chartContrastValue: 'contrastCascaderValue',
        'dimension-hover': 'hoverDimensionCascaderValue',
        'metric-hover': 'hoverMetricCascaderValue',
        isLongitude: 'longitudeCascaderValue',
        isLatitude: 'latitudeCascaderValue',
        isMinBandwidth: 'minBandwidthCascaderValue',
        isMaxBandwidth: 'maxBandwidthCascaderValue',
        isAutoBandwidth: 'autoBandwidthCascaderValue',
        calendarFactorSetting: 'factorCascaderValue',
        referenceValueSetting: 'referenceCascaderValue',
      }
      let key = 'dimensionCascaderValue'
      Object.keys(reflectObj).map(item => {
        if (this.dragBoxType) {
          key = reflectObj[this.dragBoxType]
        } else {
          this[item] && (key = reflectObj[item])
        }
      })
      return key
    },
    isGrid() {
      return this.chartUserConfig?.chartAlias === 've-grid-normal'
    },
    isCompareCard() {
      return this.currentEditData?.content?.tagNewCardContent === TAGNEWCARD.COMPARECARD
    },
    isRankCard() {
      return this.currentEditData?.content?.tagNewCardContent === TAGNEWCARD.RANKCARD
    },
    itemIndex() {
      const itemIndex = this.dragList.findIndex(d => d.keyName === this.dialogItem.keyName)
      return itemIndex > -1 ? itemIndex : 0
    }
  },
  methods: {
    setKeyName() {
      if (!this.item.keyName) {
        this.setItem({ keyName: this.$_generateKeyName(this.getDatasetLabel(this.item), this.index) })
      }
    },
    dialogConfirm(dialogItem) {
      this.$set(this.dragList, this.itemIndex, dialogItem)
      this.$emit('selectMess', {
        selectValue: this.activeCascader,
        selectItem: dialogItem,
        selectList: this.dragList,
      })
    },
    initCreated() {
      if (this.dragBoxType === 'warningLine') {
        this.disableNotNormalMeasureOptions({ isOpen: 'init' })
      }
      this.setDateDimensionOptions()

      // 卡片对比值设置对比类型为无时，需要在item中保存数据用于区分
      if (this.isCompareValue && this.activeCascader.CONTRAST && this.activeCascader.CONTRAST.hasOwnProperty('none')) {
        this.setItem({ selectedConttast: 'none' })
      }
      this.initCardData()
      this.initCascader('init')
      // 字符串第一次走COUNT
      const isChangeColumnType = this.isMetric || this.isIndex || this.isTarget || this.isRatioValue || this.isCompareValue || (['metric-short', 'metric-hover', 'warningLine'].includes(this.dragBoxType) && this.customCascader)
      if ((this.item.columnTpe !== 'number') && !this.item.aggType && isChangeColumnType) {
        this.activeCascader.COMMON = 'COUNT'
        this.setItem({ aggType: 'COUNT' })
      }
    },
    // 设置图形度量函数默认值
    setCascaderDefaultData() {
      const { chartAlias = void 0, dimensionCascaderValue = [], metricCascaderValue = {} } = this.chartUserConfig || {}
      // 漏斗图第一个度量设置默认降序
      if (chartAlias === 've-funnel' && this.isMetric && this.index === 0) {
        const cascaderArr = [...dimensionCascaderValue, ...metricCascaderValue.default]
        if (cascaderArr.every(item => !item.SORT)) {
          this.handleChange(['SORT', 'desc'])
        }
      }
    },
    // 时间拆分被选中后，日期维度默认选择按天, 其余选项置灰
    dateDimenisonDefault(val) {
      const arr = ['yyyy-MM', 'yyyy']
      if (this.dragBoxType !== 'dimension' || this.item.columnTpe !== 'date' || arr.includes(this.item.dateFormat)) return
      const disable = !NEED_TIMEDIMENSION_CHART.includes(this.chartUserConfig.chartAlias) ? false : val === '1'

      const dateValue = disable ? DATE_DIMENSION_TYPE.Day_vs_day : this.activeCascader.dateDimension || DATE_DIMENSION_TYPE.Day_vs_day
      // 时间拆分被选中后, 日期维度默认值选择按天
      this.handleChange(['dateDimension', dateValue])
    },
    // 时间维度拆分选中后, 置灰自定义排序
    disabledCustomSort() {
      // 维度
      if (this.dragBoxType !== 'dimension') return
      const { orderList, dimension } = this.item
      // TODO 兼容双维度
      const dateFormatRule = dimension && dimension[0] && dimension[0].timeDimensionSplittingRule // 日期格式规范
      const timeDimensionSplitting = this.$_getProp(this, 'drillSettings.timeDimensionSplitting') // 时间维度拆分
      // 时间维度选中 或 日期格式规范为按周、月、季度、年时，禁用自定义排序 清空自定义排序数据orderList
      const flag = Number(timeDimensionSplitting) || (dateFormatRule && ![DATE_DIMENSION_TYPE.Day_vs_day, DATE_DIMENSION_TYPE.Day_vs_day_d].includes(dateFormatRule))
      // 清空自定义排序数据
      if (flag && orderList) {
        this.settingActiveCascader('SORT', this.activeCascader, '')
        this.$emit('selectMess', {
          selectValue: this.activeCascader,
          selectItem: this.item,
          selectList: this.dragList,
        })
      }

    },
    close() {
      this.isAddFileSetting = this.isCustomsort = this.isAddDrillSetting = this.isCustom = this.isAddIndexCalendarSetting = false
    },
    confirmIndexCalendar(data) {
      const calendar = { ...data }
      this.$set(this.dragList[this.index], 'calendar', calendar)
      this.setItem({ calendar })
      const activeCascader = this.activeCascader
      debugger
      const emitData = {
        selectValue: activeCascader,
        selectItem: this.item,
        selectList: this.dragList,
      }
      this.$emit('selectMess', emitData)
      this.close()
    },
    confirmFileAlias(data) {
      const { viewFormat } = data

      // 设置当前字段选中的函数
      this.settingActiveCascader('FILELDSETTING', this.activeCascader, data)

      Object.assign(this.dragList[this.index], data)
      if (!viewFormat) {
        Reflect.deleteProperty(this.item, 'viewFormat')
      }
      // 比率卡片需要默认显示格式，增加clearViewFormat字段用于区分是否自动清空显示格式
      this.isRateCard && this.setItem({ clearViewFormat: !viewFormat })
      this.$emit('alias-change', this.dragList)
      this.close()
    },
    // 自定义排序
    confirmCustom(data) {
      // 维度自定义排序互斥,清除其他排序,双维度不需要清除其他排序
      const doubleDimensionDisabled = this.dragBoxType === 'dimension' && this.dragList.length === 2 && this.index === 0
      if (this.dragBoxType !== 'dimension-extend' && !doubleDimensionDisabled) {
        this.$emit('clear-status', {
          type: 'SORT',
          item: this.item,
        })
      }
      const activeCascader = this.activeCascader
      // 设置当前字段选中的函数
      this.settingActiveCascader('sort', activeCascader, 'customSort')
      // 自定义排序字段也需要keyName属性
      const { dimensionList = [], extendDimensionList = [] } = this.chartUserConfig
      let old_orderList = null;
      [...dimensionList, ...extendDimensionList].forEach(d => {
        if (old_orderList) return
        if (d.order === 'customSort' && d.orderList?.length) {
          if (data.alias === d.orderList[0].alias && data.columnName === d.orderList[0].columnName) {
            old_orderList = d.orderList[0]
          }
        }
      })
      if (!old_orderList || !old_orderList.keyName) {
        data.keyName = this.$_generateKeyName(data.columnName, 0, Date.now())
        old_orderList && (old_orderList.keyName = data.keyName)
      } else {
        data.keyName = old_orderList.keyName
      }
      const orderList = [data]
      this.$set(this.dragList[this.index], 'orderList', orderList)
      this.$set(this.dragList[this.index], 'order', 'customSort')
      this.$emit('selectMess', {
        selectValue: activeCascader,
        selectItem: this.item,
        selectList: this.dragList,
      })
      this.close()
    },
    // 自定义计算
    confirmAddCustom(data, isCrossExpression) {
      // 设置选中的值
      const { expression, displayExpression } = data
      const activeCascader = this.activeCascader
      const aggType = isCrossExpression ? 'cross_expression' : 'expression'
      // 设置当前字段选中的函数
      this.settingActiveCascader('CUSTOM', activeCascader, {
        value: aggType,
        exp: expression,
      })
      // 设置item
      const needType = this.item.aggType === 'CONTRAST'
      const _key = needType ? 'type' : 'aggType'
      const itemObj = {
        [_key]: aggType,
        exp: expression,
        displayExpression,
      }
      this.setItem(itemObj)
      this.itemDeleteForCustom(needType)
      this.$emit('selectMess', {
        selectValue: activeCascader,
        selectItem: this.item,
        selectList: this.dragList,
      })
      this.close()
    },
    // 自定义计算需要删除的item一些属性
    itemDeleteForCustom(needType) {
      !needType && Reflect.deleteProperty(this.item, 'type')
      !needType && this.item.selectedConttast !== 'none' && Reflect.deleteProperty(this.item, 'selectedConttast')
      !needType && Reflect.deleteProperty(this.item, 'selectedConttastMode')
      Reflect.deleteProperty(this.item, 'lgeType')
      // 删除老看板的自定义计算属性
      this.item.exp && Reflect.deleteProperty(this.item, 'expression')
    },
    // 常规需要删除的item一些属性
    itemDeleteForCOMMON(needType) {
      !needType && Reflect.deleteProperty(this.item, 'type')
      !needType && this.item.selectedConttast !== 'none' && Reflect.deleteProperty(this.item, 'selectedConttast')
      !needType && Reflect.deleteProperty(this.item, 'selectedConttastMode')
      Reflect.deleteProperty(this.item, 'lgeType')
      this.clearCustomCascader()
    },
    // 清空自定义计算相关数据
    clearCustomCascader() {
      Reflect.deleteProperty(this.item, 'expression')
      Reflect.deleteProperty(this.item, 'exp')
      Reflect.deleteProperty(this.item, 'displayExpression')
    },
    // 钻取
    confirmDrill(drillList, drillStyle = {}, drillStyleOn = false) {
      const superLinkOptions = this.currentEditData.content?.superLinkOptions
      let parameterField = []
      const { parentId: dataSetId } = this.item
      let drillDimensions = []
      drillList.forEach((item) => {
        let drillDimension = this.dragList.find(d => d.keyName === item.drillDimensionKey)
        if (superLinkOptions && superLinkOptions.length) {
          parameterField = superLinkOptions.filter(option => {
            return option.labelBoard?.chartDimension?.length && option.parameterField.includes(drillDimension.labeName)
          })
        }
        let drillItem = {
          dataSetId,
          alias: item.alias,
          drillId: item.drillId,
          keyName: drillDimension.labeName,
          columnName: item.value.labeName,
          columnType: item.value.columnTpe,
          modelType: item.value.modelType,
          parentId: item.value.parentId,
          children: item.value.children,
          drillDimensionKey: drillDimension.keyName
        }
        if (item.orderList) {
          drillItem.orderList = [{
            columnName: item.orderList.labeName,
            // 字段类型(string,number,date)
            columnType: item.orderList.columnTpe,
            alias: item.orderList.labeName,
            // 升序:asc，降序:desc,默认：""
            modelType: item.orderList.modelType,
            parentId: item.orderList.parentId,
            children: item.orderList.children,
            order: item.order,
          }]
        } else if (item.order) {
          drillItem.order = item.order
        }
        drillDimensions.push(drillItem)
      })
      if (parameterField.length) {
        this.$message(this.$t('sdp.views.mutexDrillAndLink'))
        return
      }
      // 设置当前字段选中的函数
      const _value = drillDimensions.length ? drillDimensions.filter(d => d.drillDimensionKey === this.item.keyName) : ''
      this.settingActiveCascader('DRILLSETTING', this.activeCascader, _value)

      this.currentEditData.content.drillList = drillDimensions
      this.$set(this.currentEditData.content.drillSettings, 'drillDimensions', [])
      this.$set(this.currentEditData.content, 'drillStyle', drillStyle)
      this.$set(this.currentEditData.content, 'drillStyleOn', drillStyleOn)
    },
    setItem(obj) {
      Object.keys(obj).forEach(k => {
        this.$set(this.item, k, obj[k])
      })
    },
    handleChange(value, needOpen = true) {
      if (!value.length) return
      const { chartAlias = void 0 } = this.chartUserConfig || {}
      // TODO 重新整理该段业务逻辑， 逻辑太混乱了
      const [first, second, third = ''] = value
      let changeVal = second + third

      const activeCascader = this.activeCascader
      if (['SORT'].includes(first.toUpperCase())) {
        if (changeVal !== 'customSort' && this.item.orderList) {
          Reflect.deleteProperty(this.dragList[this.index], 'orderList')
        }
      }

      // 日期维度的特殊处理，点击相同的默认成点击了按天
      if (needOpen === 'click' && first === 'dateDimension' && changeVal !== 'none' && activeCascader.dateDimension === changeVal) {
        changeVal = '_vs_none'
      }

      // 兼容2.10.1版element-ui,隐藏二级选择器
      // 自定义排序 业务函数设置 取消高亮
      if (needOpen) {
        const delHighLight = value.length === 2 && (changeVal === 'customSort')
        if (value.length === 1 || delHighLight) {
          this.foldCascader()
        }
      }

      // 目前只支持一个排序字段
      // 清除其它字段的排序设置,双维度点击第一个维度不需要清除其他
      if (first.toUpperCase() === 'SORT' && changeVal !== 'customSort' && this.dragBoxType !== 'dimension-extend') {
        if (!(this.dragBoxType === 'dimension' && this.dragList.length === 2 && ((this.index === 0 && chartAlias !== 've-bar-Heatmap') || chartAlias === 've-bar-Heatmap') && !this.isGrid)) {
          this.$emit('clear-status', {
            type: 'SORT',
            itemType: this.dragBoxType === 'dimension' ? 'dimension' : 'metric',
            item: this.item,
          })
        }
      }
      // 函数设置
      if (Object.keys(this.dialogSort).includes(first)) {
        // 需要打开弹窗的一些设置
        this[this.dialogSort[first]] = true
      } else if (changeVal === 'customSort') {
        // 维度自定义排序
        this.isCustomsort = true
      } else if (first === 'CUSTOM_DIMENSION') {
        this.dialogItem = this.$_deepClone(this.item)
        this.dialogName = 'CustomDimensionDialog'
        this.dialogVisible = true
      } else {
        // 对比特殊处理
        const { defaultValue } = this.chartUserConfig?.compareModeData || {}
        if (first === 'CONTRAST') {
          changeVal = {
            [second]: third || defaultValue,
          }
        }
        // 直接生效的设置
        // 设置当前字段选中的函数
        const settingCascaderValue = first.toUpperCase() === 'SORT' ? changeVal : changeVal || first
        this.settingActiveCascader(first, activeCascader, settingCascaderValue)
        // 设置item
        this.setItem({ order: activeCascader.SORT })
        const _item = {}
        if (first === 'COMMON') {
          // 常规函数
          const needType = this.item.aggType === 'CONTRAST'
          const _key = needType ? 'type' : 'aggType'
          Object.assign(_item, {
            [_key]: activeCascader.COMMON,
          })
          this.setItem(_item)

          // 删除item的其他属性
          this.itemDeleteForCOMMON(needType)
        } else if (first === 'CONTRAST') {
          // 对比
          const CONTRASTValue = activeCascader.COMMON || this.$_getProp(activeCascader, 'CUSTOM.value') || 'SUM'
          Object.assign(_item, {
            aggType: 'CONTRAST',
            type: CONTRASTValue,
            selectedConttast: second,
            selectedConttastMode: third || defaultValue,
          })
          // 对比类型没有选中时,修改aggType
          const cancelContrast = second === 'none' || !third

          if (cancelContrast) {
            // 设置对比方式默认值
            // 清空对比方式数据
            Object.assign(_item, {
              aggType: CONTRASTValue,
              selectedConttastMode: '',
            })
            Object.assign(activeCascader.CONTRAST, {
              [second]: '',
            })
          }
          this.setItem(_item)

          // 删除item的其他属性
          const needType = !cancelContrast
          if (CONTRASTValue === activeCascader.COMMON || CONTRASTValue === 'SUM') {
            this.itemDeleteForCOMMON(needType)
          } else {
            activeCascader.CUSTOM.value && this.itemDeleteForCustom(needType)
          }
        } else if (first === 'ALIGN') {
          this.setItem({ align: activeCascader.ALIGN })
        } else if (first === 'STACKACC') {
          // 堆叠累加
          this.setItem({ addup: changeVal })
        }
        // 维度字段的日期维度特殊处理
        if (this.dragBoxType === 'dimension' && first === 'dateDimension') {

          console.log('kyz', value, JSON.stringify(this.activeCascader), activeCascader.dateDimension)

          this.setItem({ timeDimensionSplittingRule: activeCascader.dateDimension })
          // 日期格式规范为按周、月、季度、年时，禁用自定义排序 清空自定义排序数据
          if (!['_vs_none', DATE_DIMENSION_TYPE.Day_vs_day, DATE_DIMENSION_TYPE.Day_vs_day_d].includes(this.item.timeDimensionSplittingRule) && this.item.orderList) {
            this.$delete(this.dragList[this.index], 'orderList')
          }

          this.disabledCustomSort()
          if (changeVal === '_vs_none') {
            this.$set(this.chartUserConfig, 'switchDateDimension', false)
          }
        }
        // 其他设置抛出去
        const emitData = {
          selectValue: activeCascader,
          selectItem: this.item,
          selectList: this.dragList,
        }
        this.$emit('selectMess', emitData)
      }

    },
    handleClose() {
      if (this.dragBoxType === 'dimension') {
        const initCascaderObj = {
          dateDimension: '',
          SORT: '',
          // MAPTYPE: '',
          FILELDSETTING: '',
          DRILLSETTING: '',
        }
        this.activeCascader = initCascaderObj
      }
      this.$emit('deleteLists')
      if (this.isCard) return
      // 维度/度量发生变化时需要重置维度颜色
      const { chartAlias, dimensionList = [], treeSetting = {} } = this.chartUserConfig
      // if (hasDimensionColor(chartAlias, dimensionList.length)) {
      //   Reflect.deleteProperty(this.chartUserConfig, 'dimensionColors')
      // }
      // 维度，度量变更，清除用户保存的图例选择
      this.$set(this.chartUserConfig, 'saveLegendSelected', undefined)
      // 清空树图标题
      if (this.dragBoxType === 'dimension' && chartAlias === 've-tree' && treeSetting.baseName) {
        this.$set(treeSetting, 'baseName', '')
      }
    },
    setDateDimensionVal() {
      this.$nextTick(() => {
        const dateDimensionList = this.dragList?.filter(d => d.columnTpe === 'date')

        if (this.item.columnTpe === 'date' && this.dragBoxType === 'dimension' && dateDimensionList === 1) {
          const _dateDimensionVal = this.getDateDimensionVal()
          this.handleChange(['dateDimension', _dateDimensionVal])
        }
      })
    },
    _clearOrder(changedItem) {
      const { chartAlias } = this.chartUserConfig
      const metricsContainer = this.chartUserConfig.metricsContainer
      const metricKeyArr = Object.keys(metricsContainer)
      this.dragList.forEach((e, i) => {
        if (this.dragBoxType === 'dimension' && ((this.dragList.length === 2 && !i && !this.isGrid) || (this.isGrid && changedItem.itemType === 'dimension'))) return
        this.$delete(e, 'order')
        if (this.isTargetLable) {
          Reflect.deleteProperty(this.currentEditData.content.drillSettings.layers[0].gaugeTarget, 'order')
        } else if (this.isMetric) {
          this.currentEditData.content.drillSettings.layers[0].metrics.forEach(dimen => {
            Reflect.deleteProperty(dimen, 'order')
          })
          if (metricKeyArr.length === 1) {
            this.$delete(metricsContainer.default[i], 'order')
          } else {
            this.$delete(metricsContainer[this.metricType][i], 'order')
          }
        } else if (this.dragBoxType === 'dimension') {
          const dimensionList = this.chartUserConfig.dimensionList || []
          this.currentEditData.content.drillSettings.layers[0].dimension.forEach((dimen, index) => {
            if ((this.dragList.length === 1 && index === 0) || (this.dragList.length > 1 && ((this.index !== 0 && chartAlias !== 've-bar-Heatmap') || chartAlias === 've-bar-Heatmap')&& !this.isGrid)) {
              Reflect.deleteProperty(dimen, 'order')
              Reflect.deleteProperty(dimen, 'orderList')
            }
          })
          if (dimensionList[i]) {
            this.$delete(dimensionList[i], 'order')
          }
        } else if (this.dragBoxType === 'dimension-extend') {
          this.currentEditData.content.drillSettings.layers[0].extendDimension.forEach((dimen, index) => {
            Reflect.deleteProperty(dimen, 'order')
            Reflect.deleteProperty(dimen, 'orderList')
          })
        }
      })
      // 删除维度自定义排序字段
      Reflect.deleteProperty(this.currentEditData.content.drillSettings.layers[0], 'orderList')
      // 双维度第一个维度必须存在排序
      if (this.dragList.length === 1 || (this.dragList.length > 1 && ((this.index !== 0 && chartAlias !== 've-bar-Heatmap') || chartAlias === 've-bar-Heatmap' )&& !this.isGrid)) {
        Reflect.deleteProperty(this.dragList[this.index], 'orderList')
      }
      const cascaderArr = this.chartUserConfig[this.cascaderValueKey]
      if (Array.isArray(cascaderArr)) {
        cascaderArr.forEach((e, i) => {
          if (this.dragBoxType === 'dimension' && (this.dragList.length === 2 && !i && !this.isGrid)) return
          this.$set(e, 'SORT', '')
        })
      } else {
         Object.values(cascaderArr || {}).forEach(e => {
          if (Array.isArray(e)) {
            e.forEach(item => {
              if (item) {
                this.$set(item, 'SORT', '')
              }
            })
          }
        }) 
      }
    },
    clearStatus(data) {
      if (data.item === this.item) {
        return void '跳过当前修改'
      }

      this._clearOrder(data)
    },
    // 初始化cascader的选择
    initCascader(type) {
      // 只在初始化调用一次，修复钻取选项
      if (type === 'init') {
        this.setKeyName()
        const chartAlias = this.chartUserConfig?.chartAlias
        this.fixDrillOption(chartAlias, true)
        chartAlias === 've-grid-normal' && !this.item.align && this.fixAlign(chartAlias, { from: 'initCascader', value: 'left' })
        // if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY && this.chartUserConfig.butterflySetting?.dataPercent) {
        //   const format = generateViewFormat({ format: 'percent', decimals: 2, numberDisabled: this.item.labeName.includes('CURRENCY') || this.item.currencyFlag === '1' })
        //   this.setItem({ viewFormat: format })
        // }
      }
      // 初始化维度度量函数的选择
      this.initDimensionMetric()
      this.$nextTick(() => {
        // 部分图形存在度量函数默认值
        this.setCascaderDefaultData()
        this.initCompareData()
      })
    },
    // 卡片初始化
    initCardData() {
      // 普通卡片去除排序、对比，比率卡片保留对比
    },
    // 初始化度量函数对比方式
    initCompareData() {
      const compareModeData = this.chartUserConfig?.compareModeData
      const supportCompareType = ['chartContrastValue', 'auxiliaryLine', 'customAuxiliaryLine', 'metric-hover', 'warningLine']
      if ((!this.isCompareCard && !this.isRateCard && !this.isRankValue && !this.isMetric && !supportCompareType.includes(this.dragBoxType)) || !compareModeData) return
      const { compareList, defaultValue } = compareModeData
      const contrast = this.cascaderOptions.find(item => item.value === 'CONTRAST')
      if (!contrast) return
      const { selectedConttast, selectedConttastMode } = this.item
      let disabled = !selectedConttast || selectedConttast === 'none'

      // 对比值默认对比类型为增长率
      const compareValue = this.chartUserConfig[this.cascaderValueKey]
      if (this.isCompareValue && selectedConttast !== 'none' && (!compareValue || !compareValue[this.index] || !compareValue[this.index]['CONTRAST'])) {
        this.handleChange(['CONTRAST', 'growth_rate', selectedConttastMode || defaultValue])
        disabled = false
      } else if (!disabled && !selectedConttastMode) {
        // 存在对比方式时设置默认值
        this.handleChange(['CONTRAST', selectedConttast, defaultValue])
      }
    },
    // 折叠cacscader菜单
    foldCascader() {
      if (!Object.keys(this.$refs).length) return console.log('还没渲染好组件，不用折叠图形的cascader')
      const { activePath, menus } = this.$refs.cascader.panel.$data
      activePath.splice(0, 1)
      menus.splice(1, 1)
    },
    // 查找选中的项
    findActiveCascader(initCascaderObj) {
      const { labeName, alias, aggType, type, order, MAPTYPE, timeDimensionSplittingRule, selectedConttast, lgeType, expression, viewFormat, exp, selectedConttastMode, align } = this.item
      const _alias = alias || labeName
      const getChoseStatus = (value, isCrossExpression = false) => {
        this.cascaderOptions.some(item => {
          let _value = value === 'expression' || value === 'cross_expression' ? 'CUSTOM' : value
          const flag = item.value === _value || (item.children && item.children.some(e => e.value === _value))
          if (flag) {
            let cascaderValue = value
            switch (item.value) {
              case 'CONTRAST':
                // 设置对比方式默认值
                const { defaultValue } = this.chartUserConfig.compareModeData || {}
                cascaderValue = {}
                selectedConttast && Object.assign(cascaderValue, {
                  [selectedConttast || 'none']: selectedConttastMode || defaultValue,
                })
                break
              case 'CUSTOM':
                cascaderValue = {
                  value: isCrossExpression ? 'cross_expression' : 'expression',
                  exp: expression || exp,
                }
                break
              case 'DRILLSETTING':
                cascaderValue = this.hasDrill()
                break
              case 'FILELDSETTING':
                cascaderValue = _alias
                break
              case 'ALIGN':
                cascaderValue = value
                break
              // case 'MAPTYPE':
              //   cascaderValue = MAPTYPE
              //   break
            }
            this.settingActiveCascader(item.value, initCascaderObj, cascaderValue)
          }
          return flag
        })
      }
      _alias && getChoseStatus('FILELDSETTING')
      aggType && getChoseStatus(aggType, aggType === 'cross_expression')
      type && getChoseStatus(type, type === 'cross_expression')
      order && getChoseStatus(order)
      align && getChoseStatus(align)
      // MAPTYPE && getChoseStatus(MAPTYPE)
      timeDimensionSplittingRule && getChoseStatus(timeDimensionSplittingRule)
      // 初始化恢复钻取选中状态
      const hasDrill = this.hasDrill()
      hasDrill && getChoseStatus('DRILLSETTING')
    },
    hasDrill() {
      let drillList = this.currentEditData.content.drillList || []
      let currentDrillList = drillList.filter(d => {
        return (d.drillDimensionKey && d.drillDimensionKey === this.item.keyName) ||
          (!d.drillDimensionKey && this.getDatasetLabel(this.item) === (d.keyName))
      })
      return currentDrillList.length > 0 ? currentDrillList : false
    },
    // 初始化维度度量函数的选择
    initDimensionMetric() {
      const normalDragBoxType = ['chartContrastValue', 'auxiliaryLine', 'customAuxiliaryLine', 'calendarFactorSetting', 'referenceCascaderValue', 'dimension', 'metric-hover', 'warningLine', 'metric-short', 'dimension-extend']
      if (!(this.isMetric || this.isIndex || this.isTarget || this.isRatioValue || this.isCompareValue || this.isBandwidth || normalDragBoxType.includes(this.dragBoxType))) return
      let initCascaderObj = null
      if (this.dragBoxType === 'dimension' || this.dragBoxType === 'dimension-hover' || this.isLongitude || this.isLatitude || this.isAutoBandwidth) {
        initCascaderObj = {
          dateDimension: '',
          SORT: '',
          // MAPTYPE: '',
          FILELDSETTING: '',
          DRILLSETTING: '',
        }
      } else if (this.dragBoxType === 'dimension-extend') {
        initCascaderObj = {
          SORT: '',
        }
      } else if (this.item.isGridCustom) {
        initCascaderObj = {
          SORT: '',
          ALIGN: 'left',
          FILELDSETTING: '',
        }
      } else {
        initCascaderObj = {
          COMMON: this.isBandwidth ? 'AVG' : 'SUM',
          CUSTOM: '',
          CONTRAST: '',
          SORT: '',
          FILELDSETTING: '',
        }
      }
      if (this.chartUserConfig?.chartAlias === 've-grid-normal') {
        if (this.dragBoxType === 'dimension' || this.isMetric) {
          initCascaderObj.ALIGN = this.item.align || 'left'
        }
      }
      const key = this.cascaderValueKey
      const arr = ['default', 'x', 'histogram']

      // 维度的日期字段特殊处理
      const _dateDimensionVal = this.getDateDimensionVal()
      if (_dateDimensionVal) {
        initCascaderObj.dateDimension = _dateDimensionVal
        this.setItem({ timeDimensionSplittingRule: _dateDimensionVal })
      }

      // 初始化的时候，因为单双维度的变化需要恢复的一些数据
      this.initCascaderSetting()
      if (!(['metric-short', 'warningLine'].includes(this.dragBoxType) && this.customCascader) && !this.$_getProp(this.chartUserConfig, key)) {
        // this.chartUserConfig[key] = {}
        if (this.isMetric) {
          this.chartUserConfig.metricCascaderValue = {
            default: [],
            y: [],
            size: [],
          }
        } else if (this.isAuxiliaryLineSetting) {
          this.chartUserConfig[key] = {
            [this.cascaderUniqueKey]: []
          }
        } else {
          if (this.chartUserConfig) {
            this.chartUserConfig[key] = []
          }
        }
      }
      // 初始化特殊结构数据
      if (this.cascaderUniqueKey && Object.prototype.toString.call(this.chartUserConfig[key]) === '[object Object]') {
        if (!this.chartUserConfig[key].hasOwnProperty(this.cascaderUniqueKey)) {
          this.$set(this.chartUserConfig[key], this.cascaderUniqueKey, [])
        }
      }
      this.findActiveCascader(initCascaderObj)
      if (['metric-short', 'warningLine'].includes(this.dragBoxType) && this.customCascader) {
        this.$set(this.customCascader, this.index, initCascaderObj)
      } else if (this.isMetric) {
        if (arr.includes(this.metricType)) {
          this.$set(this.chartUserConfig.metricCascaderValue.default, this.index, initCascaderObj)
        } else {
          const _key = this.metricType === 'size' ? 'size' : 'y'
          this.$set(this.chartUserConfig.metricCascaderValue[_key], this.index, initCascaderObj)
        }
        // if (!this.item.hasOwnProperty('aggType') && this.item.columnTpe !== 'string') {
        //   this.setItem({ aggType: initCascaderObj.COMMON })
        // }
      } else if (['dimension', 'dimension-extend'].includes(this.dragBoxType) || this.asMetric) {
        this.$set(this.chartUserConfig[key], this.index, initCascaderObj)
      } else if (this.isAuxiliaryLineSetting) {
        this.$set(this.chartUserConfig[key][this.cascaderUniqueKey], this.index, initCascaderObj)
      } else {
        // 比率卡片存在多个指标值
        const index = this.isRateCard || this.isCompareCard || this.isRankValue ? this.index : this.cardIndex
        if (this.chartUserConfig) {
          this.$set(this.chartUserConfig[key], index, initCascaderObj)
        }
      }
      if ((this.isMetric || this.asMetric || this.isBandwidth || (['metric-short', 'warningLine'].includes(this.dragBoxType) && this.customCascader)) &&
          (!this.item.hasOwnProperty('aggType'))) {
        if (this.item.columnTpe === 'number') {
          this.setItem({ aggType: initCascaderObj.COMMON })
        } else if (this.item.columnTpe === 'string') {
          this.setItem({ aggType: 'COUNT' })
        }
      }
    },
    getDateDimensionVal() {
      let dateDimension
      const key = this.cascaderValueKey
      const chartAlias = this.chartUserConfig?.chartAlias
      const dateDimensionList = this.chartUserConfig?.dimensionList?.filter(d => d.columnTpe === 'date')

      if (this.dragBoxType === 'dimension' && this.item.columnTpe === 'date' && dateDimensionList.length === 1) {
        let dateValueIndex = 1
        switch (this.item.dateFormat) {
          case 'yyyy-MM':
            dateValueIndex = 3
            break
          case 'yyyy':
            dateValueIndex = 5
            break
        }
        let dateDimensionOption = this.cascaderOptions.find(opt => opt.value === 'dateDimension')
        if (chartAlias === 've-calendar') {
          dateDimension = DATE_DIMENSION_TYPE.Day_vs_day
        } else if (chartAlias === 've-themeRiver') {
          const dateDimensionOption = this.computedOptions.find(c => c.value === 'dateDimension')
          dateDimension = dateDimensionOption.children[dateValueIndex].value
        } else if (dateDimensionOption) {
          const dateValue = dateDimensionOption.children[dateValueIndex].value
          dateDimension = this.item.timeDimensionSplittingRule || (Array.isArray(this.chartUserConfig[key]) && (this.chartUserConfig[key][this.index]?.dateDimension)) || dateValue
        }
        // this.item.timeDimensionSplittingRule = cascaderObj.dateDimension
      }
      return dateDimension
    },
    // 设置当前字段选中的函数
    settingActiveCascader(key, cascader, value) {
      switch (key) {
        case 'COMMON':
          cascader[key] = value
          cascader.CUSTOM = ''
          break
        case 'CUSTOM':
          cascader[key] = {
            value: value.value,
            exp: value.exp,
          }
          cascader.COMMON = ''
          break
        case 'sort':
          cascader.SORT = value
          break
        // case 'ALIGN':
        //   cascader.ALIGN = value
        //   break
        case 'CONTRAST':
          if (Object.prototype.toString.call(cascader[key]) !== '[object Object]') {
            cascader['CONTRAST'] = {}
          }
          Object.assign(cascader[key], value)
          break
        default:
          cascader[key] = value
          break
      }
      this.activeCascader = cascader
    },
    doubleDimensionSetting(data = {}) {
      if (this.dragBoxType === 'dimension') {
        const dateDimensionList = this.dragList?.filter(d => d.columnTpe === 'date')

        if (dateDimensionList.length !== 1) {
          this.settingActiveCascader('dateDimension', this.activeCascader, '')
        } else if (this.item.columnTpe === 'date') {
          this.setDateDimensionOptions()
        }

        if (this.index === 0) {
          // 第一个维度强制排序，且为升序
          if (!data.init && this.dragList.length === 2 && DOUBLE_DIMENSION_CHART.includes(this.chartUserConfig.chartAlias)) {
            if (!this.isGrid) {
              this.setItem({ order: 'asc' })
              this.settingActiveCascader('sort', this.activeCascader, 'asc')
            }
          }
          this.item.order !== 'customSort' && delete this.item.orderList
        }
      }
    },
    // 双维度隐藏日期维度
    hiddenDateFunc() {
      if (this.dragBoxType === 'dimension' && this.item.columnTpe === 'date') {
        const emitData = {
          selectValue: this.activeCascader,
          selectItem: this.item,
          selectList: this.dragList,
        }
        this.$emit('selectMess', emitData)
      }
    },
    initCascaderSetting() {
      if (this.dragList.length === 2 && this.dragBoxType === 'dimension') {
        this.doubleDimensionSetting({ init: true })
      }
    },
    fixAlign(val, params = {}) {
      if (val === 've-grid-normal' && (this.isMetric || this.dragBoxType === 'dimension')) {
        if (this.activeCascader) {
          if (params.value) {
            this.setItem({ align: params.value })
          }
          this.findActiveCascader(this.activeCascader)
        }
      }
    },
    fixDrillOption(val, initFlag) {
      if (this.currentEditData.content?.chartUserConfig?.childChartAlias !== 've-map-china' && this.dragBoxType === 'dimension') {
        // let index = this.dimensionOptions.findIndex(item => { return item.value === 'MAPTYPE' })
        // index !== -1 && this.dimensionOptions.splice(index, 1)
        if (!initFlag && this.activeCascader) {
          this.findActiveCascader(this.activeCascader)
        }
      }
      if ((['ve-tree', 've-calendar'].includes(val) || this.currentEditData.content?.chartUserConfig?.childChartAlias === 've-map-world') && this.dragBoxType === 'dimension') { // 世界地图暂不支持下钻
        this.currentEditData.content.drillList = []
        this.settingActiveCascader('DRILLSETTING', this.activeCascader, '')
      }
    },

    // 如果字段是特殊格式的日期字段，变更dateDimensionOption
    setDateDimensionOptions() {
      const flag = this.item.dateFormat
      const chartAlias = this.chartUserConfig?.chartAlias
      if (!this.dragBoxType === 'dimension' || !flag || ['ve-calendar'].includes(chartAlias)) return
      // 没有时间维度，不管
      const hasDateDimension = this.cascaderOptions.find(opt => opt.value === 'dateDimension')
      if (!hasDateDimension) return
      const dateDimensionList = this.dragList?.filter(d => d.columnTpe === 'date')
      if (dateDimensionList.length === 1 || (this.dragList.length > 1 && this.index !== 0)) {
        switch (this.item.dateFormat) {
          case 'yyyy-MM':
            // todo kyz 7816
            const Marr = [DATE_DIMENSION_TYPE.Day_vs_day, DATE_DIMENSION_TYPE.Day_vs_day_d, 'week_vs_week']

            this.$nextTick(() => {
              this.$set(this.chartUserConfig[this.cascaderValueKey][this.index], 'dateDimension', Marr.includes(this.item.timeDimensionSplittingRule) ? 'month_vs_month' : this.item.timeDimensionSplittingRule)
            })
            break
          case 'yyyy':
            this.$nextTick(() => {
              this.$set(this.chartUserConfig[this.cascaderValueKey][this.index], 'dateDimension', 'year_vs_year')
            })
            break
        }
      }
    },
    // 预警时，部分设置只需要度量的常规函数
    disableNotNormalMeasureOptions({ isOpen }) {
      if (isOpen) {
        this.$nextTick(() => {
          this.initCascader()
        })
      }
    }
  },
}
</script>

<style lang="scss">
  .trimMaxWidth .el-icon-close {
    position:absolute;
    left:114px;
    top:4px;
  }
  .trimMaxWidth.el-tag {
    text-align: left;
  }
  .cascader-boxxx {
    margin: 0 !important;
    border-top: 0;
    .popper__arrow {
      display: none !important;
    }
    ul:nth-of-type(2) {
      border-left: solid 1px #e4e7ed;
      margin-left: -1px;
      li:last-of-type {
        border-bottom: solid 1px #e4e7ed;
      }
      .el-cascader-menu__item {
        border-top: solid 1px #e4e7ed;
        text-align: center;
        line-height: 34px;
        padding: 0;
      }
    }
    .el-cascader-menu {
      min-width: 136px;
      width: 160px;
      padding: 0 0 10px;
      height: auto;
      // li:last-of-type{
      //   // border-bottom: solid 1px #e4e7ed;
      // }
      // li:nth-of-type(7) {
      //   border-bottom: solid 1px #e4e7ed;
      // }
      .el-cascader-menu__item {
        // border-top: solid 1px #e4e7ed;
        text-align: center;
        line-height: 34px;
        padding: 0;
      }
      .el-cascader-menu__item--extensible {
        border-bottom: solid 1px #e4e7ed;
      }
      .el-cascader-node {
        height: 28px;
        line-height: 28px;
        padding-left: 16px;
        padding-right: 10px !important;
        .el-cascader-node__postfix {
          position: unset;
          font-weight: 700;
        }
      }
    }
    .el-cascader-node__prefix {
      display: none;
    }
    .el-cascader-menu__wrap {
      height: auto;
      overflow: auto;
      margin-right: 0px!important;
    }
    .el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path {
      color: var(--sdp-zs);
      font-weight: 400;
    }
    .el-cascader-node__label {
      padding: 0;
    }
  }
</style>

<style lang="scss" scoped>
  $dimensionCascader: var(--sdp-tsgls);
  $metricCascader: var(--sdp-zs);
  .dragCascader {
    display: inline-block;
    position: relative;
    height: 28px;
    overflow: hidden;
    vertical-align: middle;
    // margin-top: 7px;
    &:hover /deep/ {
      .el-icon-close {
        background-color: #fff;
      }
    }
    /deep/ .el-tag {
      background-color: $metricCascader;
      height: 100%;
      line-height: 25px;
      color: #fff;
      border-radius: 0;
      // min-width: 95px;
      position: relative;
      border: 0;
      background: linear-gradient(-45deg, transparent 4px, var(--sdp-zs) 0);
      padding: 0 16px 0 8px;
      max-width: 240px;
      display: flex;
      align-items: center;
      overflow: hidden;
      .el-icon-close {
        color: $metricCascader;
        right: -7px;
        top: 0;
        &:before {
          font-weight: 700;
        }
      }
    }
    /deep/ .el-cascader {
      position: absolute;
      left: 0;
      right: 28px;
      z-index: 1;
      top: 0;
      height: 28px;
      opacity: 0;
      .el-input{
        width: calc(100% - 28px);
        overflow: hidden;
      }
    }
    /deep/ .trimMaxWidth .el-icon-close {
      position: absolute;
      left: calc(100% - 21px);
      top: 6px;
    }
    /deep/ .tag-content{
      display: inline-flex;
      align-items: center;
      width: calc(100% - 16px);
      .tag-alias{
        @include ellipsis;
        margin-left: 3px;
      }
      .tag-func{
        flex-shrink: 0;
      }
      &.no-close-icon{
        width: 100%;
        overflow: hidden;
        .tag-alias{
          width: 100%;
        }
        .tag-func{
          display: none;
        }
      }
    }
    // /deep/ .el-cascader:nth-of-type(6) {
    //   position: absolute;
    //   left: 0;
    //   right: 8px;
    //   z-index: 1;
    //   top: 0;
    //   height: 28px;
    //   opacity: 0;
    // }
  }
  .dimension-tag /deep/ {
    .el-tag {
      background-color: $dimensionCascader;
      background: linear-gradient(-45deg, transparent 4px, $dimensionCascader 0);
      .el-icon-close {
        color: $dimensionCascader;
      }
    }
  }
</style>
