<template>
  <div class="chart-design-pannel parent sdp-grid-design">
    <!-- 图表标题 -->
    <ChartTitle ref="ChartTitle" v-bind="shareProps" @eventEmit="handler_ChartTitle"></ChartTitle>

    <!-- <div class="chart-design-splite-line"></div> -->

    <ItemLabel class="chart-type-title" slot="title" :label="$t('sdp.views.tableExcelType')" :level="2"></ItemLabel>
    <!-- 图表类型 -->
    <ChartAlias class="chart-type" ref="ChartAlias" v-bind="shareProps" @eventEmit="handler_ChartAlias"></ChartAlias>

    <!-- <div class="chart-design-splite-line"></div> -->

    <!-- :specificLeft="-10" -->
    <!-- <ChartGuideTopPopover
        content="配置图表功能和样式"
        :value.sync="isShowChartSetTips"
        :markPaddingLeft="8"
        :markPaddingRight="8"
        :markPaddingTop="0"
        :markPaddingBottom="0"
        :arrowOffsetX="0"
        :tipsOffsetY="0"
        :specificLeft="1"
      > -->

    <el-tabs class="setting-tabs" v-model="activeName" @tab-click="() => {}">
      <!-- 显示设置 -->
      <el-tab-pane :label="$t('sdp.views.displaySet')" name="1">
        <div class="sdp-collapse-content">
          <!--分解树类型-->
          <decompositionTypeSetting
            v-if="[CHART_ALIAS_TYPE.VE_DECOMPOSITION].includes(chartAlias)"
            v-bind="shareProps"
          />
          <!-- 颜色显隐已完成 -->
          <SdpCollapse :key="1" :title="$t('sdp.placeholder.color')" v-if="!['ve-grid-normal'].includes(chartAlias)" v-show="chartUserConfig && !(isLiquidFill && liquidFillMode === VE_LIQUIDFILL_MODE.NORMAL)">
            <slot name="colorSetting"></slot>
            <!-- 保存色系 -->
            <SaveColorSchema ref="SaveColorSchema" v-if="!$getFeatureConfig || !$getFeatureConfig('customColorScheme.hidden')"  v-bind="shareProps"></SaveColorSchema>
            <!-- 显示渐变 -->
            <TableSwitch v-if="chartUserConfig.chartAlias === 've-map-parent' && chartUserConfig.mapType !== 'ICON'" :label="$t('sdp.views.DisplayGradient')" :formData="chartUserConfig" prop="isMapGradients" @change="mapGradientsShowHandler"></TableSwitch>
            <!-- 维度颜色多语言保持 -->
            <DimensionLangColor ref="DimensionLangColor" v-if="!$getFeatureConfig || !$getFeatureConfig('dimensionLangColor.hidden')" v-bind="shareProps" @eventEmit="handler_DimensionLangColor"></DimensionLangColor>
          </SdpCollapse>
          <!-- 地图类型已完成 -->
          <MapParentSetting ref="MapParentSetting" v-bind="shareProps" @eventEmit="handler_MapParentSetting">
          </MapParentSetting>
          <!-- 日历图配置已完成-->
          <CalendarSetting ref="CalendarSetting" v-bind="shareProps" @eventEmit="handler_CalendarSetting" :type="1"></CalendarSetting>
          <!-- 树图/配置已完成 -->
          <TreeSetting ref="TreeSetting" v-bind="shareProps" @eventEmit="handler_TreeSetting"></TreeSetting>
          <!-- 简单表格完成-->
          <SimpleGridStyle ref="SimpleGridStyle" v-bind="shareProps" @eventEmit="handler_SimpleGirdSetting">
          </SimpleGridStyle>
          <!-- histogram -->
          <!-- 尺寸已完成 -->
          <SdpCollapse :key="2" :title="$t('sdp.views.elSize')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_RING,
              CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_TREEMAP,
              // CHART_ALIAS_TYPE.VE_WORDCLOUD, // 词云在词云组件中
              CHART_ALIAS_TYPE.VE_GAUGE,
              CHART_ALIAS_TYPE.VE_LIQUIDFILL,
              CHART_ALIAS_TYPE.VE_BAR_HEATMAP,
            ].includes(chartAlias)"
             v-show="chartUserConfig"
            >
            <slot v-if="[
              CHART_ALIAS_TYPE.VE_SCATTER,
            ].includes(chartAlias)" name="sizeSetting"></slot>
            <!-- 度量柱设置 -->
            <ChartBarSetting ref="ChartBarSetting" v-bind="shareProps" @eventEmit="handler_ChartBarSetting"></ChartBarSetting>
            <!-- 最小柱子高度 -->
            <BarMinHeight ref="BarMinHeight" v-bind="shareProps" @eventEmit="handler_BarMinHeight"></BarMinHeight>
            <!-- 图形比例/固定半径 -->
            <PieRadius ref="PieRadius" v-bind="shareProps" @eventEmit="handler_PieRadius"></PieRadius>
            <!-- 圆环比例 -->
            <RingWithRatio ref="RingWithRatio" v-bind="shareProps" @eventEmit="handler_RingWithRatio"></RingWithRatio>
            <!-- 间隔设置 -->
            <PlateSpacing class="mt-16" ref="PlateSpacing" v-bind="shareProps" @eventEmit="handler_emptyMethod"></PlateSpacing>
          </SdpCollapse>
          <!-- 按度量拆分显示 -->
          <SdpCollapse :key="3" class="table-info-wide-item" :title="$t('sdp.views.metricSplitDisplay')" v-if="isSurportMetricSplitDisplay(chartUserConfig)">
            <MetricSplitDisplay ref="MetricSplitDisplay" v-bind="shareProps" @eventEmit="handler_MetricSplitDisplay"></MetricSplitDisplay>
            <!-- 度量别名 -->
            <MetricSplitAliasDisplaySetting  ref="MetricSplitAliasDisplaySetting" v-bind="shareProps" @eventEmit="handler_MetricSplitAliasDisplay"></MetricSplitAliasDisplaySetting>
          </SdpCollapse>
          <!-- 表盘设置已完成 -->
          <SdpCollapse :key="3" class="table-info-wide-item" :title="$t('sdp.views.gaugeDialSetting')" v-if="[
              CHART_ALIAS_TYPE.VE_GAUGE,
            ].includes(chartAlias)">
            <!-- 仪表盘-表盘设置-轴线宽度、底色 -->
            <GaugeAxisLineSetting ref="GaugeAxisLineSetting" v-bind="shareProps" @eventEmit="handler_emptyMethod">
            </GaugeAxisLineSetting>
            <!-- 仪表盘/标题 -->
            <GaugeDialTitle ref="GaugeDialTitle" v-bind="shareProps" @eventEmit="handler_GaugeDialTitle"></GaugeDialTitle>
            <!-- 表盘刻度 -->
            <GaugeTickStyle ref="GaugeTickStyle" v-bind="shareProps" @eventEmit="handler_GaugeTickStyle"></GaugeTickStyle>
          </SdpCollapse>
          <!-- 数值显示已完成 -->
          <SdpCollapse :key="4" class="table-info-wide-item" :title="$t('sdp.views.NumericalDisplay')" v-if="[
              CHART_ALIAS_TYPE.VE_GAUGE,
            ].includes(chartAlias)">
            <!-- 数值显示/之后要调整到单位简写的前面 -->
            <LabelDisplay ref="LabelDisplay" v-bind="shareProps" @eventEmit="handler_LabelDisplay"></LabelDisplay>
          </SdpCollapse>
          <!-- 词云图设置已完成 -->
          <WordCloudSetting ref="WordCloudSetting" v-bind="shareProps" @eventEmit="handler_WordCloudSetting">
          </WordCloudSetting>
          <!-- 展示方式已完成 -->
          <SdpCollapse :key="5" class="histogram" :title="$t('sdp.views.LegendType')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
            ].includes(chartAlias)">
            <!-- 柱状图展示方式（嵌套柱状图） -->
            <HistogramDisplayMode onlyOption ref="HistogramDisplayMode" v-bind="shareProps" @eventEmit="handler_HistogramDisplayMode">
            </HistogramDisplayMode>
            <!-- 顶层图形已完成 -->
            <TopLevelGraph ref="TopLevelGraph" v-bind="shareProps" @eventEmit="handler_emptyMethod"></TopLevelGraph>
          </SdpCollapse>
          <!-- 已完成 -->
          <SdpCollapse :key="6" :title="$t('sdp.views.LegendType')" v-if="[
              CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
            ].includes(chartAlias)">
            <!-- 图形比例/固定半径 -->
            <PieRadius ref="PieRadius" v-bind="shareProps" @eventEmit="handler_PieRadius"></PieRadius>
          </SdpCollapse>
          <!-- 最大堆叠数已完成 -->
          <SdpCollapse :key="7" :title="$t('sdp.views.maximumStack')" v-show="showhide.maximumStack.length">
            <!-- 最大堆叠数 -->
            <MaxStack :showhide="showhide.maximumStack" ref="MaxStack" v-bind="shareProps" @eventEmit="handler_MaxStack"></MaxStack>
            <!-- 按度量拆分显示 -->
            <!-- <MetricSplitDisplay :showhide="showhide.maximumStack" ref="MetricSplitDisplay" v-bind="shareProps" @eventEmit="handler_MetricSplitDisplay">
            </MetricSplitDisplay> -->
          </SdpCollapse>
          <!-- 图例已修改 -->
          <SdpCollapse :key="8" class="table-info-wide-item" :title="$t('sdp.views.picPosi')" v-show="showhide.picPosi.length" v-if="![CHART_ALIAS_TYPE.VE_DECOMPOSITION].includes(chartAlias)">
            <!-- 图例 -->
            <ChartLegend :showhide="showhide.picPosi" ref="ChartLegend" v-bind="shareProps" @eventEmit="handler_ChartLegend"></ChartLegend>
            <!-- 图例选择全选反选 -->
            <LegendSelection :showhide="showhide.picPosi" ref="LegendSelection" v-bind="shareProps" @eventEmit="handler_LegendSelection"></LegendSelection>
            <LegendDialog :showhide="showhide.picPosi" v-bind="shareProps" :element="element" :themeType="themeType" @confirm="legendHandler"/>
            <!-- 图例内容自适应 -->
            <LegendAdaptive :showhide="showhide.picPosi" ref="LegendAdaptive" v-bind="shareProps" @eventEmit="handler_LegendAdaptive"></LegendAdaptive>
          </SdpCollapse>
          <!-- 水滴图设置 -->
          <LiquidFillSetting ref="LiquidFillSetting" v-bind="shareProps" @eventEmit="handler_LiquidFillSetting">
          </LiquidFillSetting>
          <!-- 数值显示/之后要调整到单位简写的前面 -->
          <!-- 河流图标签已完成 -->
          <SdpCollapse :key="9" :title="$t('sdp.views.label')" v-if="[
              CHART_ALIAS_TYPE.VE_THEMERIVER,
            ].includes(chartAlias)">
            <LabelDisplay ref="LabelDisplay" v-bind="shareProps" @eventEmit="handler_LabelDisplay"></LabelDisplay>
          </SdpCollapse>
          <!-- 纬度轴位置 已完成 -->
          <SdpCollapse :key="10" :title="$t('sdp.views.dimensionAxisPosition')" v-if="[
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
            ].includes(chartAlias)">
            <!-- 蝴蝶图-维度轴位置 -->
            <DimensionAxisPostionSetting ref="DimensionAxisPostionSetting" v-bind="shareProps"
              @eventEmit="handler_DimensionAxisPostionSetting"></DimensionAxisPostionSetting>
          </SdpCollapse>
          <!-- 动画效果 -->
          <AnimationSetting ref="AnimationSetting" v-bind="shareProps" @eventEmit="handler_AnimationSetting">
          </AnimationSetting>
          <!-- 地图-数据刷新 -->
          <MapDataRefresh ref="MapDataRefresh" v-bind="shareProps" @eventEmit="handler_MapDataRefresh"></MapDataRefresh>
          <!-- 区域堆叠 -->
          <LineAreaStack ref="LineAreaStack" v-bind="shareProps" @eventEmit="handler_LineAreaStack"></LineAreaStack>
          <!-- 背景线 (隐藏) 已完成 -->
          <SdpCollapse :key="11" :title="$t('sdp.views.Background')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
            ].includes(chartAlias)">
            <!-- 背景线 -->
            <BackgroundLine ref="BackgroundLine" v-bind="shareProps" @eventEmit="handler_BackgroundLine"></BackgroundLine>
            <!-- 背景趋势线 -->
            <BackgroundTrendLine ref="BackgroundTrendLine" v-bind="shareProps" @eventEmit="handler_BackgroundTrendLine"></BackgroundTrendLine>
          </SdpCollapse>
          <SdpCollapse :key="38"  :title="$t('sdp.views.Alignment')" v-if="[
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
            ].includes(chartAlias)">
             <RadioGroup
                v-model="chartUserConfig.alignmentMethod"
                type="ChartAlignmentSettings"
                @on-change="handleChartAlignChange"
            ></RadioGroup>
          </SdpCollapse>
          <!-- 漏斗标签已完成 -->
          <SdpCollapse :key="12" class="table-info-wide-item" :title="$t('sdp.views.funnelLabels')" v-if="[
              CHART_ALIAS_TYPE.VE_FUNNEL,
            ].includes(chartAlias)">
            <!-- 漏斗图配置 -->
            <FunnelSetting ref="FunnelSetting" v-bind="shareProps" @eventEmit="handler_FunnelSetting"></FunnelSetting>
          </SdpCollapse>
          <!-- 度量值已完成 -->
          <SdpCollapse :key="13" :title="onlyIndex ? $t('sdp.simSystem.IndicatorValue') : $t('sdp.views.Metric')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_RING,
              CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_RADAR,
              CHART_ALIAS_TYPE.VE_SUNBURST,
              CHART_ALIAS_TYPE.VE_TREEMAP,
              CHART_ALIAS_TYPE.VE_FUNNEL,
              CHART_ALIAS_TYPE.VE_TREE,
              CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
              CHART_ALIAS_TYPE.VE_CALENDAR,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
              CHART_ALIAS_TYPE.VE_MAP,
              CHART_ALIAS_TYPE.VE_LIQUIDFILL,
              CHART_ALIAS_TYPE.VE_BAR_HEATMAP,
              CHART_ALIAS_TYPE.VE_DECOMPOSITION
            ].includes(chartAlias)">
            <!-- 数值显示/之后要调整到单位简写的前面 -->
            <LabelDisplay ref="LabelDisplay" v-bind="shareProps" @eventEmit="handler_LabelDisplay"></LabelDisplay>
            <!-- 移动端-标签换行显示-不影响编辑界面，所以不需要处理 -->
            <LabelWarp ref="LabelWarp" v-bind="shareProps"></LabelWarp>
          </SdpCollapse>

          <!--分解树列分页-->
          <decompositionPageSetting
            v-if="[CHART_ALIAS_TYPE.VE_DECOMPOSITION].includes(chartAlias)"
            v-bind="shareProps"
          />
          <!--分解树尺寸分页-->
          <decompositionSizeSetting
            v-if="[CHART_ALIAS_TYPE.VE_DECOMPOSITION].includes(chartAlias)"
            v-bind="shareProps"
          />
          <!--分解树边框-->
          <decompositionBorderSetting
            v-if="[CHART_ALIAS_TYPE.VE_DECOMPOSITION].includes(chartAlias)"
            v-bind="shareProps"
          />

          <!-- 地图-名称显示已修改 -->
          <RegionNameDisplay class="table-info-wide-item" ref="RegionNameDisplay" v-bind="shareProps" @eventEmit="handler_RegionNameDisplay">
          </RegionNameDisplay>
          <!-- 地图-显示设置已修改 -->
          <MapDisplaySetting class="table-info-wide-item" ref="MapDisplaySetting" v-bind="shareProps" @eventEmit="handler_MapDisplaySetting">
          </MapDisplaySetting>
          <!-- 地图-提示设置已修改 -->
          <SdpCollapse :key="14" :title="$t('sdp.views.PromptSettings')" v-if="isMap && !isMobile">
            <MapTooltipSetting
              ref="MapTooltipSetting"
              v-bind="shareProps"
              @eventEmit="handler_MapTooltipSetting"
            ></MapTooltipSetting>
          </SdpCollapse>
          <!-- 地图-保存地图比例和位置、禁用手势缩放和移动 -->
          <ZoomAndPosition ref="ZoomAndPosition" v-bind="shareProps" @eventEmit="handler_ZoomAndPosition"></ZoomAndPosition>
          <!-- 雷达图已完成-->
          <RadarSetting class="table-info-wide-item" ref="RadarSetting" v-bind="shareProps" @eventEmit="handler_RadarSetting"></RadarSetting>
          <!-- 度量值统一标准 -->
          <MetricStandard ref="MetricStandard" v-bind="shareProps" @eventEmit="handler_RadarSetting"></MetricStandard>
          <!-- 散点图-四象限-设置已完成 -->
          <ScatterNormalSetting ref="ScatterNormalSetting" v-bind="shareProps" @eventEmit="handler_ScatterNormalSetting">
          </ScatterNormalSetting>
          <!-- 负值样式已修改 -->
          <SdpCollapse :key="15" :title="$t('sdp.views.NegativeStyle')" v-if="[
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_RING,
              CHART_ALIAS_TYPE.VE_SUNBURST,
              CHART_ALIAS_TYPE.VE_TREEMAP,
            ].includes(chartAlias)">
              <!-- 负值样式-->
              <NegativeStyle ref="NegativeStyle" v-bind="shareProps" @eventEmit="handler_NegativeStyle"></NegativeStyle>
          </SdpCollapse>
          <!-- 矩形树图配置已修改 -->
          <TreemapSetting ref="TreemapSetting" v-bind="shareProps" @eventEmit="handler_emptyMethod"></TreemapSetting>
          <!-- 累计值/对比值/瀑布图已完成 -->
          <AccumulativeValue class="table-info-wide-item" ref="AccumulativeValue" v-bind="shareProps" @eventEmit="handler_AccumulativeValue">
          </AccumulativeValue>
          <!-- 对数轴已完成 -->
          <LogAxis ref="LogAxis" v-bind="shareProps" @eventEmit="handler_LogAxis"></LogAxis>
          <!-- 坐标轴 已完成 -->
          <SdpCollapse :key="16" :title="$t('sdp.views.CoordinateAxis')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_THEMERIVER,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
              CHART_ALIAS_TYPE.VE_BAR_HEATMAP
            ].includes(chartAlias)">
            <template v-if="isDisplaySettingShow">
              <!-- x轴配置 -->
              <XAxisSetting ref="XAxisSetting" v-bind="shareProps" @eventEmit="handler_XAxisSetting"></XAxisSetting>
              <!-- y轴配置 -->
              <YAxisSetting ref="YAxisSetting" v-bind="shareProps" @eventEmit="handler_YAxisSetting"></YAxisSetting>
              <!-- 次轴-->
              <SecondAxisSetting ref="SecondAxisSetting" v-bind="shareProps" @eventEmit="handler_YAxisSetting">
              </SecondAxisSetting>
            </template>
          </SdpCollapse>
          <!-- 曲线设置 已完成 -->
          <CurveLine ref="CurveLine" v-bind="shareProps" @eventEmit="handler_CurveLineSetting"></CurveLine>
          <!-- 象形柱图设置已完成 -->
          <PictorialbarSetting class="table-info-wide-item" ref="PictorialbarSetting" v-bind="shareProps" @eventEmit="handler_PictorialbarSetting">
          </PictorialbarSetting>
          <!-- 圆角设置已修改 -->
          <SdpCollapse :key="17" :title="$t('sdp.views.RoundedCornerSetting')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
            ].includes(chartAlias) || (
              chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE
              && !chartUserConfig.compositeChart.stack
            )">
            <!-- 柱形圆角 -->
            <BarRadius ref="BarRadius" v-bind="shareProps" @eventEmit="handler_emptyMethod"></BarRadius>
          </SdpCollapse>
          <!-- DataZoom 已完成 -->
          <SdpCollapse :key="18" title="Datazoom" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_THEMERIVER,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
            ].includes(chartAlias)" noCollapse v-show="showhide.Datazoom.length">
            <DataZoom :showhide="showhide.Datazoom" ref="DataZoom" v-bind="shareProps" @eventEmit="handler_DataZoom"></DataZoom>
          </SdpCollapse>
          <!-- 滚动条已完成 -->
          <SdpCollapse :key="19" :title="$t('sdp.views.scrollBar')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
            ].includes(chartAlias)" v-show="showhide.ChartScrollBar.length">
            <ChartScrollBar :showhide="showhide.ChartScrollBar" ref="ChartScrollBar" v-bind="shareProps" @eventEmit="handler_ChartScrollBar"></ChartScrollBar>
          </SdpCollapse>
          <!-- 悬浮窗显示已完成 -->
          <SdpCollapse :key="20" :title="$t('sdp.views.showHover')" v-if="[
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_RING,
              CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_RADAR,
              CHART_ALIAS_TYPE.VE_SUNBURST,
              CHART_ALIAS_TYPE.VE_TREEMAP,
              CHART_ALIAS_TYPE.VE_FUNNEL,
              CHART_ALIAS_TYPE.VE_WORDCLOUD,
              CHART_ALIAS_TYPE.VE_TREE,
              CHART_ALIAS_TYPE.VE_THEMERIVER,
              CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
              CHART_ALIAS_TYPE.VE_CALENDAR,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
              CHART_ALIAS_TYPE.VE_GAUGE,
              CHART_ALIAS_TYPE.VE_MAP,
              CHART_ALIAS_TYPE.VE_LIQUIDFILL,
              CHART_ALIAS_TYPE.VE_BAR_HEATMAP
            ].includes(chartAlias)" noCollapse v-show="showhide.HoverDisplay.length">
            <HoverDisplay :showhide="showhide.HoverDisplay" ref="HoverDisplay" v-bind="shareProps" @eventEmit="handler_LabelDisplay"></HoverDisplay>
          </SdpCollapse>
        </div>
      </el-tab-pane>
      <!-- 功能设置 -->
      <el-tab-pane :label="$t('sdp.views.funSet')" name="2">
        <!-- 行数限制已修改 -->
        <div class="sdp-collapse-content">
          <SdpCollapse :key="21" :title="$t('sdp.views.rowsLimit')"
            v-if="![
              CHART_ALIAS_TYPE.VE_DECOMPOSITION,
            ].includes(chartAlias)"
            v-show="showhide.RowsLimit.length">
            <!-- 行数限制/条数设置 -->
            <ChartRankOf :showhide="showhide.RowsLimit" ref="ChartRankOf" v-bind="shareProps" @eventEmit="handler_ChartRankOf"></ChartRankOf>
            <!-- 移动端-每页显示条数 -->
            <MobilePagesize :showhide="showhide.RowsLimit" ref="MobilePagesize" v-bind="shareProps" @eventEmit="handler_MobilePagesize"></MobilePagesize>
          </SdpCollapse>
          <!-- 排名已完成 -->
          <SdpCollapse :key="22" :title="$t('sdp.views.Ranking')" v-if="[
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
            ].includes(chartAlias)">
            <!-- 带宽图：排名 -->
            <ChartRankSetting ref="ChartRankSetting" v-bind="shareProps" @eventEmit="handler_ChartRankSetting">
            </ChartRankSetting>
          </SdpCollapse>
          <!-- 完成率已修改 -->
          <SdpCollapse :key="23" :title="$t('sdp.views.PercentageComplete')" v-if="[
              CHART_ALIAS_TYPE.VE_BAR_PERCENT,
              CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
              CHART_ALIAS_TYPE.VE_GAUGE,
            ].includes(chartAlias)">
            <!-- 完成率 -->
            <CompletionSetting ref="CompletionSetting" v-bind="shareProps" @eventEmit="handler_CompletionSetting">
            </CompletionSetting>
          </SdpCollapse>
          <!-- 度量汇总已完成 -->
          <MeasureSummary ref="MeasureSummary" class="table-info-wide-item" v-bind="shareProps" @eventEmit="handler_MeasureSummary"></MeasureSummary>
          <!-- 日历图配置已完成-->
          <CalendarSetting ref="CalendarSetting" v-bind="shareProps" @eventEmit="handler_CalendarSetting" :type="2"></CalendarSetting>
          <!-- 转换率计算方式已完成 -->
          <SdpCollapse :key="24" :title="$t('sdp.views.conversionRateAlgorithm')" v-if="[
              CHART_ALIAS_TYPE.VE_FUNNEL,
            ].includes(chartAlias)">
            <!-- 漏斗图-转换率计算方式 -->
            <FunnelConversionRate ref="FunnelConversionRate" v-bind="shareProps" @eventEmit="handler_FunnelSetting">
            </FunnelConversionRate>
          </SdpCollapse>
          <SdpCollapse :key="25" :title="$t('sdp.views.indicators')" v-if="[
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_RING,
            ].includes(chartAlias)" noCollapse>
            <!-- 指标增长率 -->
            <GrowthRate ref="GrowthRate" v-bind="shareProps" @eventEmit="handler_GrowthRate"></GrowthRate>
          </SdpCollapse>
          <!-- 合并其他项已完成 -->
          <SdpCollapse :key="26" :title="$t('sdp.views.MergeOtherItems')" v-if="[
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_RING,
              CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
              CHART_ALIAS_TYPE.VE_RADAR,
              CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
            ].includes(chartAlias) || (chartAlias === CHART_ALIAS_TYPE.VE_LIQUIDFILL && chartUserConfig.liquidFillSetting && chartUserConfig.liquidFillSetting.mode === VE_LIQUIDFILL_MODE.DIMENSION)">
            <!-- 合并其他项 -->
            <MergeOthers ref="MergeOthers" v-bind="shareProps" @eventEmit="handler_MergeOthers"></MergeOthers>
            <!-- 层叠圆形图-最小间距 -->
            <RoundCascadesMinDistance ref="RoundCascadesMinDistance" v-bind="shareProps"
              @eventEmit="handler_RoundCascadesMinDistance"></RoundCascadesMinDistance>
            <!-- 显示复合饼图 -->
            <CombinePie ref="CombinePie" v-bind="shareProps" @eventEmit="handler_CombinePie"></CombinePie>
          </SdpCollapse>
          <!-- 辅助线已完成 -->
          <SdpCollapse :key="27" :title="$t('sdp.views.auxiliaryLine')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
              CHART_ALIAS_TYPE.VE_GAUGE,
            ].includes(chartAlias)">
            <!-- 辅助线 -->
            <AuxiliaryLine ref="AuxiliaryLine" v-bind="shareProps" @eventEmit="handler_AuxiliaryLine"></AuxiliaryLine>
          </SdpCollapse>
          <!-- 占比已完成 -->
          <SdpCollapse :key="28" :title="$t('sdp.views.percent')" v-if="[
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
            ].includes(chartAlias)">
              <!-- 蝴蝶图-占比 -->
              <ButterflyDataPercentSetting ref="ButterflyDataPercentSetting" v-bind="shareProps"
                @eventEmit="handler_ButterflyDataPercentSetting"></ButterflyDataPercentSetting>
          </SdpCollapse>
          <!-- 极值高亮已完成 -->
          <!-- 极值高亮 -->
          <ExtremeValueHighlightSetting ref="ExtremeValueHighlightSetting" v-bind="shareProps"
            @eventEmit="handler_ExtremeValueHighlight" />
          <!-- 地图飞线 -->
          <MapFlyLine ref="MapFlyLine" v-bind="shareProps" @eventEmit="handler_MapFlyLine" />
          <!-- 预警已完成 -->
          <SdpCollapse :key="30" :title="$t('sdp.views.chartWarning')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_GRID,
              CHART_ALIAS_TYPE.VE_WORDCLOUD,
              CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
              CHART_ALIAS_TYPE.VE_CALENDAR,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
              CHART_ALIAS_TYPE.VE_GAUGE,
              CHART_ALIAS_TYPE.VE_MAP,
              CHART_ALIAS_TYPE.VE_BAR_HEATMAP,
              CHART_ALIAS_TYPE.VE_DECOMPOSITION
            ].includes(chartAlias)">
            <!-- 预警线 -->
            <WarningLine ref="WarningLine" v-bind="shareProps" @eventEmit="handler_WarningLine" class="table-info-wide-warning-line"></WarningLine>
          </SdpCollapse>
          <!-- 排序设置 判断维度是否超过一个,超过不显示 -->
          <SortSetting
            v-if="SORT_SETTING_CHART.includes(chartAlias)
              && (chartUserConfig.dimensionList.length < 2)
              && (CHART_ALIAS_TYPE.VE_COMPOSITE !== chartAlias || chartUserConfig.compositeChart.stack === true)"
            v-bind="shareProps"
            @eventEmit="handler_SortSettingChange"
          >
          </SortSetting>
          <!-- 简单表格 -->
          <SimpleGridSetting ref="SimpleGridSetting" v-bind="shareProps" @eventEmit="handler_SimpleGirdSetting" class="table-info-wide-SimpleGridSetting">
          </SimpleGridSetting>
          <DataFiltering
              v-bind="DataFilteringOptions"
              @dataFilteringCallback="(val) => { $emit('data-filtering-callback', val) }"
            ></DataFiltering>
          <SdpCollapse :key="31" :title="$t('sdp.views.DateSettings')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_WATERFALL,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_RADAR,
              CHART_ALIAS_TYPE.VE_SUNBURST,
              CHART_ALIAS_TYPE.VE_TREEMAP,
              CHART_ALIAS_TYPE.VE_FUNNEL,
              CHART_ALIAS_TYPE.VE_GRID,
              CHART_ALIAS_TYPE.VE_WORDCLOUD,
              CHART_ALIAS_TYPE.VE_TREE,
              CHART_ALIAS_TYPE.VE_THEMERIVER,
              CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
              CHART_ALIAS_TYPE.VE_CALENDAR,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
              CHART_ALIAS_TYPE.VE_MAP,
              CHART_ALIAS_TYPE.VE_LIQUIDFILL,
              CHART_ALIAS_TYPE.VE_BAR_HEATMAP
            ].includes(chartAlias) && (!$getFeatureConfig || !$getFeatureConfig('dateSettings.hidden'))" v-show="showhide.DateSettings.length">
            <!-- 时间维度 && 日期维度切换 -->
            <DateDimensionSplit :showhide="showhide.DateSettings" ref="DateDimensionSplit" v-bind="shareProps" @eventEmit="handler_DateDimensionSplit">
            </DateDimensionSplit>

            <!-- 日期格式设置、跨年合并 -->
            <DateFormatterSetting :showhide="showhide.DateSettings" ref="dateFormatterSetting" v-bind="shareProps" @eventEmit="handler_DateFormatter">
            </DateFormatterSetting>
          </SdpCollapse>
          <!-- 地图方案 -->
          <SdpCollapse :key="32" v-if="mapSchemeCollapseShow" :collapsed="collapsed.mapScheme" @change="(val) => collapsed.mapScheme = val">
            <template #title>
              {{ $t('sdp.views.mapScheme') }}
              <ItemLabel slot="title" :level="2" icon :tips="$t('sdp.views.tipMapScheme')">
              </ItemLabel>
            </template>

            <!-- 地图方案 -->
            <MapScheme ref="MapScheme" v-bind="shareProps" :emptySomeProperty="emptySomeProperty" @change="chooseMapScheme">
            </MapScheme>
          </SdpCollapse>

          <!-- 数据查看框 -->
          <SdpCollapse
            no-collapse
            v-if="isMobile &&
                  chartAlias !== CHART_ALIAS_TYPE.VE_GRID &&
                  !(isNormalLiquidfill(element.content))"
          >
            <template slot="title">
              {{ $t('sdp.views.DataViewer') }}
            </template>
            <TableSwitch
              :formData="chartUserConfig"
              prop="dataViewer"
            />
          </SdpCollapse>
          <!-- 多语言 -->
          <SdpCollapse :key="33" :title="$t('sdp.views.language')" no-collapse>
            <template slot="title">
              <div class="languageSetting" @click="openCardLanguage">
                <div>
                  {{ $t('sdp.views.language') }}
                </div>
                <div class="sdp-collapse-icon">
                  <i :class="{'icon-sdp-shucangbiaogejiantou': true}"></i>
                </div>
              </div>
            </template>
          </SdpCollapse>
        </div>
      </el-tab-pane>
      <!-- 高级设置 -->
      <el-tab-pane :label="$t('sdp.views.AdvancedSettings')" name="3">
        <div class="sdp-collapse-content">
          <!-- 合并扩展项已完成 -->
          <MergeExtendItems ref="MergeExtendItems" v-bind="shareProps" @eventEmit="handler_MergeExtendItems">
          </MergeExtendItems>
          <SdpCollapse :key="33" :title="$t('sdp.views.ExtendedDimensionSettings')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_TREE,
            ].includes(chartAlias) && (!$getFeatureConfig || !$getFeatureConfig('extendedDimensionSettings.hidden'))"
              v-show="showhide.ExtendedDimensionSettings.length"
            >
            <!-- 扩展维度颜色 -->
            <ExtendDimensionColor :showhide="showhide.ExtendedDimensionSettings" ref="ExtendDimensionColor" v-bind="shareProps" @eventEmit="handler_ExtendDimensionColor">
            </ExtendDimensionColor>
            <!-- 同扩展纬度度量值同颜色 -->
            <SameExtendedDimensionSameColor :showhide="showhide.ExtendedDimensionSettings" ref="SameExtendedDimensionSameColor" v-bind="shareProps" @eventEmit="handler_SameExtendedDimensionSameColor">
            </SameExtendedDimensionSameColor>
            <!-- 扩展维度颜色 -->
            <MainDimensionMeasureShow :showhide="showhide.ExtendedDimensionSettings" ref="MainDimensionMeasureShow" v-bind="shareProps"
              @eventEmit="handler_MainDimensionMeasureShow"></MainDimensionMeasureShow>
            <!-- 度量计算值 -->
            <MetricCalculation :showhide="showhide.ExtendedDimensionSettings" ref="MetricCalculation" v-bind="shareProps" @eventEmit="handler_MetricCalculation">
            </MetricCalculation>
          </SdpCollapse>
          <SdpCollapse :key="34" :title="$t('sdp.views.emptyValue')" noCollapse v-show="showhide.EmptyValueDisplay.length && (!$getFeatureConfig || !$getFeatureConfig('emptyValueDisplay.hidden')) ">
            <!-- 空值显示 -->
            <EmptyValueDisplay :showhide="showhide.EmptyValueDisplay" ref="EmptyValueDisplay" v-bind="shareProps" @eventEmit="handler_EmptyValueDisplay">
            </EmptyValueDisplay>
          </SdpCollapse>
          <SdpCollapse freeMode :key="35" v-if="(!utils.isScreen || utils.isDataReport) && !isChildMapScheme" :title="indicatorSelectorLabel" :collapsed="collapsed.indicatorSelector" @change="(val) => collapsed.indicatorSelector = val">
            <div class="indicator-addition" @click="handleAddDimensions">
              <i class="el-icon-circle-plus-outline"></i>
            </div>
            <div class="dimension-select-tags" v-if="currentEditData.content.chioceTab &&
              currentEditData.content.chioceTab.length > 1
              ">
              <draggable ref="chioceTab" v-model="currentEditData.content.chioceTab" :options="{ sort: true }"
                @start="dragChioceTabStart" @end="dragChioceTabEnd">
                <el-tag v-for="(dimensions, i) in currentEditData.content.chioceTab" :key="i"
                  :class="{ checktag: currentEditData.content.saveIndex === i }" :closable="false">
                  <span @click="chooseDimension(i)" :class="['tag-span', { hidden: renameInputVisible[i] }]"
                    :title="dimensions.name">
                    <span>
                      {{ dimensions.name || dimensions.id }}
                    </span>
                  </span>
                  <el-input :ref="`tagInput${i}`" v-show="renameInputVisible[i]" v-model="dimensions.name" class="chart-input"
                    @blur="$set(renameInputVisible, i, false)" @change="validateChoiceTabName($event, dimensions)"
                    :maxlength="20"></el-input>
                  <i v-if="i !== 0" class="icon-sdp-zhibiaoxuanzeqishanchu" @click="closeDimension(i)"></i>
                  <i class="icon-sdp-zhibiaoxuanzeqifuzhi" @click="handleCopyChoiceTab(dimensions.id, i)"></i>
                  <i class="icon-sdp-zhibiaoxuanzeqibianji" @click="handleRenameInputStatus(i)"></i>
                </el-tag>
              </draggable>
            </div>
            <!-- 指标切换 -->
            <TableSwitch
                :value.sync="currentEditData.content.indicatorSelectorShow"
                :label="$t('sdp.views.IndexDisplaySwitch')"
                @change="handleindicatorSelectorShowChange"
            />
            <template v-if="currentEditData.content.indicatorSelectorShow">
              <ItemLabel
                  :label="$t('sdp.views.LegendType')"
                  :level="4"
                  icon
                  :tips="$t('sdp.views.indicatorSelectorGt3')"
                  display="block"
              ></ItemLabel>
              <RadioGroup
                  v-model="currentEditData.content.indicatorSelectorShowType"
                  type="IndicatorDisplayType"
                  @on-change="handleIndicatorSelectorShowType"
              ></RadioGroup>
              <!-- 指标选择器-样式设置-->
              <IndicatorStyleSetting
                  class="mt-4"
                  ref="IndicatorStyleSetting"
                  v-bind="shareProps"
              ></IndicatorStyleSetting>
            </template>

            <!-- <ItemLabel :label="$t('sdp.views.LegendType')" :level="4" icon :tips="$t('sdp.views.indicatorSelectorGt3')"
              display="block"></ItemLabel>
            <RadioGroup v-model="currentEditData.content.indicatorSelectorShowType" type="IndicatorDisplayType"
              @on-change="handleIndicatorSelectorShowType"></RadioGroup> -->

            <!-- 页签宽度 -->
            <IndicatorSelectorTabWidthSetting :currentEditData="currentEditData" v-bind="shareProps"></IndicatorSelectorTabWidthSetting>
          </SdpCollapse>
          <SdpCollapse :key="36" :title="$t('sdp.views.resultDataset')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_GRID,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
            ].includes(chartAlias) && featureConfigDatasetAssociation" noCollapse>
            <!-- 关联数据集 -->
            <TableSwitch v-if="featureConfigDatasetAssociation" :value.sync="datasetAssociation"
              :label="$t('sdp.views.resultDataset')" :disabled="!platformDataList.length"
              @change="handleAssocciationChange" />
          </SdpCollapse>
          <SdpCollapse freeMode :key="37" :title="$t('sdp.views.description')" v-if="[
              CHART_ALIAS_TYPE.VE_HISTOGRAM,
              CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
              CHART_ALIAS_TYPE.VE_PICTORIALBAR,
              CHART_ALIAS_TYPE.VE_COMPOSITE,
              CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR,
              CHART_ALIAS_TYPE.VE_BAR_PERCENT,
              CHART_ALIAS_TYPE.VE_BAR_STACK,
              CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
              CHART_ALIAS_TYPE.VE_PIE,
              CHART_ALIAS_TYPE.VE_ROSE,
              CHART_ALIAS_TYPE.VE_RING,
              CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
              CHART_ALIAS_TYPE.VE_LINE,
              CHART_ALIAS_TYPE.VE_LINE_AREA,
              CHART_ALIAS_TYPE.VE_SCATTER,
              CHART_ALIAS_TYPE.VE_RADAR,
              CHART_ALIAS_TYPE.VE_SUNBURST,
              CHART_ALIAS_TYPE.VE_TREEMAP,
              CHART_ALIAS_TYPE.VE_FUNNEL,
              CHART_ALIAS_TYPE.VE_GRID,
              CHART_ALIAS_TYPE.VE_WORDCLOUD,
              CHART_ALIAS_TYPE.VE_TREE,
              CHART_ALIAS_TYPE.VE_THEMERIVER,
              CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
              CHART_ALIAS_TYPE.VE_CALENDAR,
              CHART_ALIAS_TYPE.VE_BANDWIDTH,
              CHART_ALIAS_TYPE.VE_GAUGE,
              CHART_ALIAS_TYPE.VE_MAP,
              CHART_ALIAS_TYPE.VE_LIQUIDFILL,
              CHART_ALIAS_TYPE.VE_BAR_HEATMAP,
              CHART_ALIAS_TYPE.VE_DECOMPOSITION,
            ].includes(chartAlias)" >
            <el-input type="textarea" resize="none" :rows="5" :placeholder="$t('sdp.placeholder.pleaseInputContent')" @blur="
              $emit(
                'on-chart-set',
                chartUserConfig.chartDescription,
                'chartDescription'
              )
              " v-model="chartUserConfig.chartDescription">
            </el-input>
          </SdpCollapse>
          <tip-setting
            v-if="(!utils.isScreen || utils.isDataReport) && (!$getFeatureConfig || !$getFeatureConfig('tipSetting.hidden'))"
            v-bind="shareProps"
            :datasetList="datasetList"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getBandwidthInitData, isSurportMetricSplitDisplay } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/charts'
import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import {
  NEED_TIMEDIMENSION_CHART,
  NO_DISPLAY_SETTING_CHART,
  DATASET_ASSOCIATION_CHART,
  DIMENSION_WARNING_CHART,
  MEARSURE_DIS_CENTER,
  CHART_ALIAS_TYPE,
  VE_LIQUIDFILL_MODE,
  SORT_SETTING_CHART,
  IGNORING_UNSELECTED_METRICS_CHART,
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import {
  getThemeConfig,
  setThemeConfig
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/theme'
import eventBus from 'packages/assets/eventBus'
import EventData from 'packages/assets/EventData'
import {
  RadioGroup,
  TableSwitch,
  ItemLabel
} from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { SUPERLINK_CONST_TYPE, SUPER_LINK_CONST_SITE, THEME_TYPE } from 'packages/assets/constant'
import {
  handleWaterfallData,
  handleExtraSettings
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/bridge'
import * as defaultSetting from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/chartConfig'
import { generateAxisSetting, generateAxis } from 'packages/base/board/displayPanel/supernatant/chartSet/utils/generator'
import { ChartTitle, ChartAlias, ScatterNormalSetting, ChartRankOf, ChartLegend, ChartRankSetting, MeasureSummary, RingWithRatio, AccumulativeValue, WordCloudSetting, LiquidFillSetting, HistogramDisplayMode, BackgroundLine, BackgroundTrendLine, MetricCalculation, ExtendDimensionColor, AuxiliaryLine, WarningLine, DateFormatterSetting, LabelDisplay, CompletionSetting, GaugeDialTitle, DataZoom, DateDimensionSplit, SimpleGridSetting, MergeExtendItems, TreeSetting, TreemapSetting, CalendarSetting, EmptyValueDisplay, MapParentSetting, PictorialbarSetting, LogAxis, LegendSelection, GrowthRate, PieRadius, MergeOthers, CombinePie, AnimationSetting, RoundCascadesMinDistance, MaxStack, FunnelSetting, RegionNameDisplay, GaugeTickStyle, FunnelConversionRate, BarMinHeight, ChartScrollBar, CurveLine, XAxisSetting, YAxisSetting, SecondAxisSetting, MapScheme, MapTooltipSetting, RadarSetting, MobilePagesize, MetricSplitDisplay, NegativeStyle, MapDataRefresh, ChartBarSetting, ZoomAndPosition, BarRadius, SimpleGridStyle, MetricStandard, LabelWarp, DimensionAxisPostionSetting, ButterflyDataPercentSetting, LineAreaStack, MapDisplaySetting, TopLevelGraph, GaugeAxisLineSetting, SaveColorSchema, DimensionLangColor, ExtremeValueHighlightSetting, PlateSpacing, MainDimensionMeasureShow, ColorSetting, MapFlyLine, SortSetting, MetricSplitAliasDisplaySetting, SameExtendedDimensionSameColor, LegendAdaptive, IndicatorSelectorTabWidthSetting } from 'packages/base/board/displayPanel/supernatant/chartSet/functions'
import * as IndicatorSelectorSetting from 'packages/base/board/displayPanel/supernatant/chartSet/indicatorSelector'
import HoverDisplay from 'packages/base/board/displayPanel/supernatant/chartSet/functions/modules/HoverDisplay'
import SdpCollapse from 'packages/base/board/displayPanel/supernatant/chartSet/components/components/SdpCollapse'

import DataFiltering from 'packages/base/board/displayPanel/supernatant/common/ChartDataFiltering'
import {isNormalLiquidfill} from "../../boardElements/elementChart/constant";

import { getLangElement } from 'packages/base/board/displayPanel/boardLanguage'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import LegendDialog from 'packages/base/board/displayPanel/supernatant/chartSet/functions/modules/legend/legend'
import DecompositionTypeSetting from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/ve-decomposition/decompositionTypeSetting.vue";
import DecompositionPageSetting from "../functions/modules/ve-decomposition/decompositionPageSetting.vue";
import DecompositionSizeSetting from "../functions/modules/ve-decomposition/decompositionSizeSetting.vue";
import DecompositionBorderSetting from "../functions/modules/ve-decomposition/decompositionBorderSetting.vue";
import TipSetting from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting";
// 指标选择器支持最多添加 70 个
const chioceTabMaxCount = 70


export default {
  inject: {
    utils: { default: {} },
    chart: { default: {} },
    chartElement: { default: () => ({}) },
    selfChartDesignHandler: { default: () => null },
    USER_PROP: { default: {} },
    getCurrentThemeClass: { default: () => () => '' },
    configs: {default: () => false}
  },
   provide() {
    return {
      tableInfoWide: () => true,
      freeCollapse: () => this.freeCollapse
    }
  },
  mixins: [datasetMixin],
  components: {
    TipSetting,
    DecompositionPageSetting,
    DecompositionSizeSetting,
    DecompositionBorderSetting,
    DecompositionTypeSetting,
    ColorSetting,
    DataFiltering,
    SdpCollapse,
    ...IndicatorSelectorSetting,
    ItemLabel,
    // 图表标题
    ChartTitle,
    // 图表类型
    ChartAlias,
    // 散点图-四象限相关配置
    ScatterNormalSetting,
    // 行数限制/条数限制
    ChartRankOf,
    // 图例
    ChartLegend,
    // 蝴蝶图-维度轴-设置
    DimensionAxisPostionSetting,
    // 蝴蝶图-占比
    ButterflyDataPercentSetting,
    // 排名
    ChartRankSetting,
    // 度量汇总
    MeasureSummary,
    // 圆环比例
    RingWithRatio,
    // 累计值
    AccumulativeValue,
    // 词云图设置
    WordCloudSetting,
    // 水滴图设置
    LiquidFillSetting,
    // 柱状图展示方式（嵌套柱状图）
    HistogramDisplayMode,
    // 区域堆叠
    LineAreaStack,
    // 背景线
    BackgroundLine,
    // 背景趋势线
    BackgroundTrendLine,
    // 顶层图形
    TopLevelGraph,
    // 度量柱设置
    ChartBarSetting,
    // 度量计算值
    MetricCalculation,
    // 扩展维度颜色
    ExtendDimensionColor,
    // 同扩展维度值同颜色
    SameExtendedDimensionSameColor,
    // 辅助线
    AuxiliaryLine,
    // 预警线
    WarningLine,
    // 日期格式设置
    DateFormatterSetting,
    // 数值显示
    LabelDisplay,
    // 移动端-标签换行显示
    LabelWarp,
    // 负值显示
    NegativeStyle,
    // 悬浮框显示
    HoverDisplay,
    // 完成率
    CompletionSetting,
    // 仪表盘标题
    GaugeDialTitle,
    // dataZoom
    DataZoom,
    // 时间维度 && 日期维度切换
    DateDimensionSplit,
    // 简单表格
    SimpleGridSetting,
    // 简单表格-显示设置
    SimpleGridStyle,
    // 合并扩展项
    MergeExtendItems,
    // 树图配置
    TreeSetting,
    // 矩形图配置
    TreemapSetting,
    // 日历图配置
    CalendarSetting,
    // 空值显示
    EmptyValueDisplay,
    // 地图配置
    MapParentSetting,
    // 象形柱图配置
    PictorialbarSetting,
    // 对数轴
    LogAxis,
    // 图例全选反选
    LegendSelection,
    // 指标增长率
    GrowthRate,
    // 图形比例/固定半径
    PieRadius,
    // 度量值统一标准（雷达图多度量时有）
    MetricStandard,
    // 合并其他项
    MergeOthers,
    // 显示复合饼图
    CombinePie,
    // 动画效果
    AnimationSetting,
    // 地图-数据刷新
    MapDataRefresh,
    // 层叠圆形图-最小间距
    RoundCascadesMinDistance,
    // 最大堆叠数
    MaxStack,
    // 按度量拆分显示
    MetricSplitDisplay,
    // 按度量拆分显示-度量别名
    MetricSplitAliasDisplaySetting,
    // 漏斗图配置
    FunnelSetting,
    // 地图-区域名称显示
    RegionNameDisplay,
    // 地图-保存地图比例和位置、禁用手势缩放和移动
    ZoomAndPosition,
    // 表盘刻度
    GaugeTickStyle,
    // 仪表盘-表盘设置-轴线宽度、底色
    GaugeAxisLineSetting,
    // 漏斗图-转换率计算方式
    FunnelConversionRate,
    // 最小柱子高度
    BarMinHeight,
    // 滚动条
    ChartScrollBar,
    // 曲线设置
    CurveLine,
    // x轴配置
    XAxisSetting,
    // y轴配置
    YAxisSetting,
    // 次轴
    SecondAxisSetting,
    // 地图方案
    MapScheme,
    // 地图-提示
    MapTooltipSetting,
    // 雷达图配置
    RadarSetting,
    // 地图-显示设置
    MapDisplaySetting,
    // 柱形圆角
    BarRadius,
    // 移动端-每页显示条数
    MobilePagesize,
    RadioGroup,
    TableSwitch,
    // 保存色系
    SaveColorSchema,
    // 维度颜色多语言开关
    DimensionLangColor,
    // 极值高亮设置
    ExtremeValueHighlightSetting,
    // 间隔设置
    PlateSpacing,
    // 主维度度量
    MainDimensionMeasureShow,
    // 地图飞线
    MapFlyLine,
    // 排序设置
    SortSetting,
    // 图例（热力图）
    LegendDialog,
    LegendAdaptive,  // 图例自适应
    // 指标选择器页签宽度设置
    IndicatorSelectorTabWidthSetting
  },

  props: {
    isLiquidFill: {
      type: Boolean,
      default: false,
    },
    liquidFillMode: {
      type: String,
      default: ''
    },
    ColorSettingOptions: {
      type: Object,
      default: () => {}
    },
    SizeSettingOptions: {
      type: Object,
      default: () => {}
    },
    DataFilteringOptions: {
      type: Object,
      default: () => {}
    },
    currentEditData: {
      type: Object,
      default: () => ({})
    },
    datasetList: {
      type: Array
    },
    platformDataList: {
      type: Array
    },
    themeType: {
      type: String,
      default: THEME_TYPE.default
    },
    // 初始化图形标识，图形编辑弹框打开时为true，初始化完成后置为false
    initChart: {
      type: Boolean,
      default: false
    },
    indicatorIndex: {
      type: Number,
      default: 0
    },
    interactionReferencedInfo: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      freeCollapse: {},
      // 根据组件内部各项返回状态，判断当前页面是否展示包裹容器
      showhide: {
        ExtendedDimensionSettings: [], // 扩展纬度设置
        DateSettings: [], // 日期设置
        Datazoom: [], // datazoom
        ChartScrollBar: [], // 滚动条
        HoverDisplay: [], // 悬浮窗
        EmptyValueDisplay: [], // 空值显示
        HighlightExtremeValue: [], // 极值高亮
        picPosi: [], // 图例
        RowsLimit: [], // 行数限制
        maximumStack: [], // 最大堆叠数
      },
      // 当前页面保存展开状态，解决在某些情况下，展开后点击某些内容会自动收起的问题
      collapsed: {
        indicatorSelector: false, // 指标选择器
        mapScheme: false, // 地图方案
      },
      VE_LIQUIDFILL_MODE: VE_LIQUIDFILL_MODE,
      SORT_SETTING_CHART: SORT_SETTING_CHART,
      CHART_ALIAS_TYPE: CHART_ALIAS_TYPE,
      activeName: '1',
      activeNames: [],
      // isShowChartSetTips: false, // 配置图表样式操作指引
      collapseActive: {
        chartAlias: '1',
        functionName: '2',
        mapScheme: '6',
        mapTooltip: '7-1',
        indicatorSelector: '8',
        remarkName: '9'
      },

      chioceTabName: '',
      renameInputVisible: [],
      // 当前选中指标选择器名称
      currentChioceTabName: '',
      datasetAssociation: false,
      THEME_TYPE
    }
  },

  computed: {
    //  配置功能和样式的跟保存合一起
    // chartSetIsDone() {
    //   return !!this.$refs['dimensionDragBox'].dragList.length;
    // },
    // chartSetIsDoing() {
    //   return (
    //     !!this.drillSettings.dataSetId &&
    //     this.utils.kanbanGuide.isVisible &&
    //     !this.$parent.$parent.paramsSetIsDoing &&
    //     this.$parent.$parent.paramsSetIsDone
    //     // !(this.$parent.$refs['dimensionDragBox'].dragList.length||this.$parent.$refs['metricdefault'][0].dragList.length)
    //   );
    // },

    indicatorSelectorLabel() {
      const len = this.currentEditData.content?.chioceTab?.length || 0
      let label = this.$t('sdp.views.indicatorSelector')
      if (len > 1) {
        label += ` ( ${len} )`
      }
      return label
    },
    element() {
      return this.currentEditData || {}
    },
    shareProps() {
      return {
        element: this.currentEditData || {},
        themeType: this.themeType,
        datasetList: this.datasetList,
        isByTheInteraction: this.$attrs.isByTheInteraction,
        initChart: this.initChart,
        indicatorIndex: this.indicatorIndex
      }
    },
    drillSettings() {
      return this.currentEditData.content?.drillSettings || {}
    },
    displayCollapseShow() {
      return (
        this.isDisplaySettingShow ||
        ['ve-radar', 've-grid-normal', 've-map-parent'].includes(
          this.chartUserConfig.chartAlias
        )
      )
    },
    mapSchemeCollapseShow() {
      return (
        this.chartUserConfig.childChartAlias === 've-map-world' ||
        this.isChildMapScheme
      )
    },
    // isTickValueCustom() {
    //   if (!HAS_TICK_GAUGE.includes(this.chartUserConfig.childChartAlias)) return false
    //   const settingGauge = this.chartUserConfig.gaugeTarget.settingGauge || {}
    //   if (!settingGauge.tickValueSetting || !settingGauge.tickValueSetting.tickValueCustom) return false
    //   return true
    // },
    isChildMapScheme() {
      const fn = this.chart.isChildMapScheme
      return fn && fn()
    },
    chartAlias() {
      return this.chartUserConfig.chartAlias
    },
    isDisplaySettingShow() {
      return !NO_DISPLAY_SETTING_CHART.includes(
        this.chartUserConfig.chartAlias
      )
    },
    isMobile() {
      return this.utils.isMobile
    },
    // 地图
    isMap() {
      const { chartUserConfig } = this.currentEditData.content
      return chartUserConfig.chartType === 've-map'
    },
    // 图例类型
    legendType() {
      const chartConfig = this.currentEditData.content.chartConfig
      return chartConfig.legend
        ? chartConfig.legend.type || 'scroll'
        : 'scroll'
    },
    chartUserConfig() {
      return this.currentEditData.content.chartUserConfig
    },
    indicatorSelectorShowType: {
      get() {
        return (
          this.currentEditData.content.indicatorSelectorShowType || 'dropDown'
        )
      },
      set(val) {
        this.$set(
          this.currentEditData.content,
          'indicatorSelectorShowType',
          val
        )
      }
    },
    indicatorSelectorShow: {
      get() {
        if (this.currentEditData.indicatorSelectorShow === false) return false
        return true
      },
      set(val) {
        this.$set(
          this.currentEditData.content,
          'indicatorSelectorShow',
          val
        )
      }
    },
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    },
    featureConfigDatasetAssociation() {
      const hiddenDatasetAssociation = this.$getFeatureConfig?.('datasetAssociation.hidden')
      return DATASET_ASSOCIATION_CHART.includes(this.chartAlias) && (hiddenDatasetAssociation !== true)
    },
    chioceTabTitle() {
      let chioceTab = this.currentEditData.content?.chioceTab
      return chioceTab?.[0]?.name || chioceTab?.[0]?.id || ''
    },
  },
  watch: {
    // 配置功能和样式的跟保存合一起
    // chartSetIsDoing: {
    //   handler: function(value) {
    //     this.$nextTick(() => {
    //       this.isShowChartSetTips = value;
    //     });
    //   }
    // }
  },
  methods: {
    eventBus,
    isSurportMetricSplitDisplay,
    isNormalLiquidfill,
    handler_emptyMethod(prop, value) {
      console.log('handler_emptyMethod', prop, value)
      this.chartElement().resetAfterConfig({ source: prop, isMapInit: true })
    },
    handler_MobilePagesize(prop, value) {
      if (prop === this.USER_PROP.MOBILE_PAGESIZE) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MapTooltipSetting(prop, value) {
      if (
        [
          this.USER_PROP.MAP_TOOLTIP_FILL_COLOR,
          this.USER_PROP.AXIS_TITLE,
          this.USER_PROP.AXIS_LABEL,
          this.USER_PROP.ZERO_INTERVAL_ALIGN
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if ([this.USER_PROP.MAP_HOVER_DIMENSION].includes(prop)) {
        this.$emit('preview-chart')
      }
    },
    handler_YAxisSetting(prop, value) {
      if (prop === this.USER_PROP.RECALCULATE_MAX_AND_MIN) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.Y_AXIS_SETTING) {
        // 组合图次轴计算最大值最小值
        value === 1 && this.$emit('keep-axis-rang')
        this.handler_emptyMethod(prop, value)
      } else if (
        [
          this.USER_PROP.AXIS_LINE_STYLE,
          this.USER_PROP.AXIS_TITLE,
          this.USER_PROP.AXIS_LABEL,
          this.USER_PROP.ZERO_INTERVAL_ALIGN
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.HOLIDAY_STYLE) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_XAxisSetting(prop, value) {
      if (prop === this.USER_PROP.RECALCULATE_MAX_AND_MIN) {
        this.handler_emptyMethod(prop, value)
      } else if (
        [
          this.USER_PROP.AXIS_LINE_STYLE,
          this.USER_PROP.AXIS_TITLE,
          this.USER_PROP.AXIS_LABEL
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.HOLIDAY_STYLE) {
        this.handler_emptyMethod(prop, value)
      }
    },

    handler_CurveLineSetting(prop, value) {
      if ([this.USER_PROP.LINE_SMOOTH_TYPE].includes(prop)) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_ChartScrollBar(prop, value) {
      if (
        [
          this.USER_PROP.CHART_SCROLL_BAR_SHOW,
          this.USER_PROP.CHART_SCROLL_DATA_NUMBER
        ].includes(prop)
      ) {
        if (value) {
          this.selfChartDesignHandler()
            .reset({ trigger: prop, newValue: value })
            .next('dataZoomChecked')
        }
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_BarMinHeight(prop, value) {
      if ([this.USER_PROP.BAR_MIN_HEIGHT].includes(prop)) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_GaugeTickStyle(prop, value) {
      if ([this.USER_PROP.GAUGE_TICK].includes(prop)) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_FunnelSetting(prop, value) {
      if (
        [
          this.USER_PROP.FUNNEL_LABEL_STYLE,
          this.USER_PROP.FUNNEL_LABEL_SHOW,
          this.USER_PROP.FUNNEL_CONVERESION_RATE_SHOW,
          this.USER_PROP.FUNNEL_CONVERESION_RATE_LABEL,
          this.USER_PROP.FUNNEL_CONVERESION_RATE_ALGORITHM
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MaxStack(prop, value) {
      if ([this.USER_PROP.MAX_STACK].includes(prop)) {
        const { chartAlias } = this.element.content.chartUserConfig
        const stack = handleExtraSettings.call(
          this.chartElement(),
          'stack',
          this.element,
          chartAlias === 've-composite'
        )
        this.element.content.chartSettings.stack = stack
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MetricSplitDisplay(prop, value) {
      if (prop === this.USER_PROP.METRIC_SPLIT_DISPLAY) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MetricSplitAliasDisplay(prop, value) {
      if (
        [this.USER_PROP.METRIC_SPLIT_ALIAS_DISPLAY_SETTING,this.USER_PROP.METRIC_SPLIT_ALIAS_LABEL_STYLE].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_RoundCascadesMinDistance(prop, value) {
      if ([this.USER_PROP.ROUNDCASCADES_MIN_DISTANCE].includes(prop)) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_AnimationSetting(prop, value) {
      if ([this.USER_PROP.ANIMATION_SETTING].includes(prop)) {
        this.selfChartDesignHandler()
          .reset({ trigger: prop, newValue: value })
          .next('isAllowLabelOverlap')
        this.handler_emptyMethod(prop, value)
        this.$emit('change', 'animationSetting')
      } else if ([this.USER_PROP.ANIMATION_TYPE_MAP].includes(prop)) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MapDataRefresh(prop, value) {
      if ([this.USER_PROP.MAP_DATA_REFRESH].includes(prop)) {
        // 该配置只在大屏&预览时用到，设计时不用管
        // this.handler_emptyMethod(prop, value)
      }
    },
    handler_MapFlyLine(prop, value) {
      this.$emit('preview-chart')
    },
    handler_CombinePie(prop, value) {
      if ([this.USER_PROP.SHOW_COMBINE_PIE].includes(prop)) {
        // 需要重新请求数据的
        this.$emit('preview-chart')
      }
    },
    handler_MergeOthers(prop, value) {
      if ([this.USER_PROP.MERGE_OTHERS].includes(prop)) {
        // 需要重新请求数据的
        this.$emit('preview-chart')
      }
    },
    handler_PieRadius(prop, value) {
      if (
        [
          this.USER_PROP.CIRCLE_RADIUS_TYPE,
          this.USER_PROP.CIRCLE_RADIUS_VALUE
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_GrowthRate(prop, value) {
      if (prop === this.USER_PROP.INDICATOR_GROWTH_RATE) {
        if (!value) {
          this.handler_emptyMethod(prop, value)
        } else {
          this.$emit('preview-chart', { from: 'GrowthRate' })
        }
      }
    },
    handler_LegendSelection(prop, value) {
      if (prop === this.USER_PROP.LEGEND_SELECTION_SHOW) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_LegendAdaptive(prop, value) {
      if (prop === this.USER_PROP.LEGEND_ADAPTIVE) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_LogAxis(prop, value) {
      if (prop === this.USER_PROP.LOG_AXIS) {
        this.selfChartDesignHandler()
          .reset({ trigger: prop, newValue: value })
          .next('zeroIntevalAlign')
          .next('xAxisSetting')
          .next('yAxisSetting')
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_PictorialbarSetting(prop, value) {
      if ([this.USER_PROP.PICTORIALBAR_FLIP_AXIS].includes(prop)) {
        this.selfChartDesignHandler()
          .reset({ trigger: prop, newValue: value })
          .next('metricLabelDisplay')
          .next('xAxis')
          .next('yAxis')
        this.handler_emptyMethod(prop, value)
      } else if (
        [
          this.USER_PROP.PICTORIALBAR_SYMBOL_REPEAT,
          this.USER_PROP.PICTORIALBAR_SYMBOL_TYPE,
          this.USER_PROP.PICTORIALBAR_SYMBOL_ICON
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MapParentSetting(prop, value) {
      if (
        [
          this.USER_PROP.MAP_TYPE,
          this.USER_PROP.MAP_GROUND_COLOR,
          this.USER_PROP.MAP_BOUNDARY_LINE_STYLE
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.MAP_GRADIENTS_SHOW) {
        this.handler_emptyMethod(prop, value)
      } else if ([this.USER_PROP.MAP_DISPLAY_RANGE].includes(prop)) {
        this.chartElement().geoRoamInfo = {}
        this.$emit('preview-chart', { from: this.USER_PROP.MAP_DISPLAY_RANGE })
        const val =
          this.chartUserConfig.mapRangeVal === 'provice'
            ? this.chartUserConfig.mapProviceVal || 'beijing'
            : {}
        setTimeout(() => {
          this.$emit('do-register-map', val)
        })
      }
    },
    handler_EmptyValueDisplay(prop, value) {
      if (prop === this.USER_PROP.EMPTY_VALUE_DISPLAY) {
        this.$emit('preview-chart', { from: prop })
      }
    },
    handler_CalendarSetting(prop, value) {
      if (prop === this.USER_PROP.CALENDAR_INDICATOR_CALCULATION) {
        const {
          factorList = [],
          isShowMeasurementCalculation
        } = this.element.content.chartUserConfig.calendarSettings
        if (isShowMeasurementCalculation) {
          if (!factorList.length) {
            this.handler_emptyMethod(prop, value)
            return
          }
          this.$emit('preview-chart')
        } else {
          if (factorList.length) {
            this.handler_emptyMethod(prop, value)
          }
        }
      } else if (
        [
          this.USER_PROP.CALENDAR_DATE_RANGE,
          this.USER_PROP.CALENDAR_INDICATOR_CALCULATION_DOMAIN
        ].includes(prop)
      ) {
        this.$emit('preview-chart')
      } else if (
        [
          this.USER_PROP.CALENDAR_ARRANGEMENT,
          this.USER_PROP.CALENDAR_START_DATE,
          this.USER_PROP.CALENDAR_TYPE,
          this.USER_PROP.CALENDAR_MONTH_SPLIT_LINE_SHOW,
          this.USER_PROP.CALENDAR_MONTH_SPLIT_LINE_STYLE,
          this.USER_PROP.CALENDAR_ZEBRA_CROSSING,
          this.USER_PROP.CALENDAR_ZEBRA_CROSSING_COLOR,
          this.USER_PROP.CALENDAR_MONTH_PAGE,
          this.USER_PROP.CALENDAR_MOBILE_MONTH_PAGE_MODE,
          this.USER_PROP.CALENDAR_MONTHS_OF_ONE_PAGE,
          this.USER_PROP.CALENDAR_LABEL_SHOW,
          this.USER_PROP.CALENDAR_LABEL_STYLE,
          this.USER_PROP.CALENDAR_VISUAL_MAPPING,
          this.USER_PROP.CALENDAR_WEEK_COLOR,
          this.USER_PROP.CALENDAR_INDICATOR_CALCULATION_STYLE
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if ([this.USER_PROP.CALENDAR_FLIP_MONTHS].includes(prop)) {
        // nothing
      } else {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MergeExtendItems(prop, value) {
      if (prop === this.USER_PROP.MERGE_EXTEND_ITEMS) {
        this.$emit('preview-chart')
      }
    },
    handler_TreeSetting(prop, value) {
      this.handler_emptyMethod(this.USER_PROP.TREE_SETTING, value)
    },
    handler_SimpleGirdSetting(prop, value) {
      if (['eventBus'].includes(prop)) {
        this.$emit('eventBus', value)
      } else if (
        [
          this.USER_PROP.SIMPLE_GRID_FREEZE_TYPE,
          this.USER_PROP.SIMPLE_GRID_FREEZE_COLUMN
        ].includes(prop)
      ) {
        // nothing
        // 冻结行冻结列不需要处理
      } else {
        this.$emit('preview-chart', value)
      }
    },
    handler_DateDimensionSplit(prop, value) {
      if (prop === this.USER_PROP.TIME_DIMENSION_SPLITTING) {
        this.selfChartDesignHandler()
          .reset({ trigger: prop, newValue: value })
          .next('dimensionList')
      } else if (prop === this.USER_PROP.DATE_DIMENSION_ITEMS) {
        this.$emit('eventBus', value)
      } else if (prop === this.USER_PROP.DATE_DIMENSION_SPLIT) {
        if (this.chartUserConfig.chartAlias === 've-grid-normal') {
          this.$emit('preview-chart', { from: prop })
        }
      } else if (prop === this.USER_PROP.DATE_DIMENSION_WEEK_ITEMS) {
        this.$emit('preview-chart')
      }
    },
    handler_DateFormatter(prop, value) {
      this.$emit('preview-chart')
    },
    handler_ExtremeValueHighlight(prop, value) {
      this.$emit('preview-chart')
    },
    handler_DataZoom(prop, value) {
      if (prop === this.USER_PROP.DATA_ZOOM) {
        // dataZoom与图形滚动条功能互斥
        this.selfChartDesignHandler()
          .reset({ trigger: prop })
          .next('scrollBarShow')
        if (value) {
          const { grid, legend } = this.currentEditData.content.chartConfig
          // 匹配样式
          if (!grid) {
            this.$set(this.currentEditData.content.chartConfig, 'grid', {
              x: 10,
              y: 10,
              x2: 25,
              y2: 25
            })
          }
          if (!legend) {
            this.$set(this.currentEditData.content.chartConfig, 'legend', {
              bottom: 30,
              type: 'scroll'
            })
          }
        } else {
          this.$delete(this.currentEditData.content.chartConfig, 'dataZoom')
        }
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_GaugeDialTitle(prop, value) {
      if (
        [
          this.USER_PROP.GUAGE_DIAL_TITLE_SHOW,
          this.USER_PROP.GUAGE_DIAL_TITLE_TEXT,
          this.USER_PROP.GUAGE_DIAL_TITLE_STYLE
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_CompletionSetting(prop, value) {
      if (
        [
          this.USER_PROP.COMPLETION_SETTING_RATE,
          this.USER_PROP.COMPLETION_RATE_DISPLAY,
          this.USER_PROP.COMPLETION_RATE_STYLE
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_LabelDisplay(prop, value) {
      if (
        [
          this.USER_PROP.GAUGE_DATA_SHOW,
          this.USER_PROP.GAUGE_DATA_DISPLAY_POSITION,
          this.USER_PROP.GAUGE_DATA_SHOW_INDICATOR,
          this.USER_PROP.GAUGE_INDICATOR_STYLE,
          this.USER_PROP.GAUGE_DATA_SHOW_TARGET,
          this.USER_PROP.GAUGE_TARGET_STYLE,
          this.USER_PROP.LABEL_STYLE,
          this.USER_PROP.MAP_LONGITUDE_AND_LATITUDE_NAME_SHOW,
          this.USER_PROP.LABEL_SHOW,
          this.USER_PROP.FOLLOW_DIMENSION_COLOR,
          this.USER_PROP.LABEL_SHOW_LINES
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.METRIC_LABEL_DISPLAY) {
        this.$emit('preview-chart', value)
      } else if(prop === this.USER_PROP.PROPORTION_BASIS) {
        this.$emit('preview-chart', value)
      }
    },
    handler_NegativeStyle(prop) {
      this.handler_emptyMethod(prop)
    },
    handler_RegionNameDisplay(prop, value) {
      if (
        [
          this.USER_PROP.MAP_REGION_NAME_DISPLAY,
          this.USER_PROP.MAP_SHOW_ONLY_HAS_VALUE,
          this.USER_PROP.MAP_CITY_NAME_DISPLAY
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.METRIC_LABEL_DISPLAY) {
        this.$emit('preview-chart', value)
      }
    },
    handler_ZoomAndPosition(prop, value) {
      // 不需要更新视图
      if (prop === this.USER_PROP.MAP_SAVE_ZOOM_AND_POSITION) {
        if (!value) this.handler_emptyMethod(prop, value)
      }
    },
    handler_WarningLine(prop, value) {
      if (prop === this.USER_PROP.WARNING_LINE) {
        this.$emit('preview-chart', value)
      } else if (prop === this.USER_PROP.WARNING_ICON) {
        this.chartElement().resizeEcharts()
      }
    },
    handler_AuxiliaryLine(prop, value) {
      if (prop === this.USER_PROP.AUXILIARY_LINE) {
        this.$emit('preview-chart')
      } else if (
        [this.USER_PROP.AUXILIARY_LINE_DISPLAY_SWITCH_POSITION].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.AUXILIARY_LINE_DISPLAY_SWITCH_SHOW) {
        this.chartElement().resizeEcharts()
      }
    },
    handler_ExtendDimensionColor(prop, value) {
      if (prop === this.USER_PROP.EXTEND_DIMENSION_COLOR) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_SameExtendedDimensionSameColor(prop, value) {
      if (prop === this.USER_PROP.SAME_EXTENDED_DIMENSION_SAME_COLOR) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MainDimensionMeasureShow(prop, value) {
      if (prop === this.USER_PROP.MAIN_DIMENSION_MEASURE_ENABLE) {
        this.$emit('preview-chart')
      }
    },
    handler_SortSettingChange(prop, value) {
      if (prop === this.USER_PROP.SORT_TYPE) {
        this.$emit('preview-chart')
      }
    },
    handler_MetricCalculation(prop, value) {
      if (prop === this.USER_PROP.METRIC_CALCULATION) {
        this.$emit('preview-chart') // 这里刷新一下，因为会被 IGNORING_UNSELECTED_METRICS 影响
      } else if (prop === this.USER_PROP.IGNORE_EXTEND_DIMENSION) {
        this.$emit('preview-chart')
      } else if (prop === this.USER_PROP.IGNORING_UNSELECTED_METRICS) {
        this.$emit('preview-chart')
      }
    },
    handler_ChartBarSetting(prop, value) {
      if (prop === this.USER_PROP.BAR_SETTING) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_LineAreaStack(prop, value) {
      if (prop === this.USER_PROP.LINE_AREA_STACK) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_BackgroundLine(prop, value) {
      if (prop === this.USER_PROP.BACKGROUND_LINE) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_BackgroundTrendLine(prop, value) {
      if (prop === this.USER_PROP.BACKGROUND_TREND_LINE) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_HistogramDisplayMode(prop, value) {
      if (prop === this.USER_PROP.HISTOGRAM_DISPLAY_MODE) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_LiquidFillSetting(prop, value) {
      if (
        [
          this.USER_PROP.COMPLETE_RATE_TITLE_STYLE,
          this.USER_PROP.COMPLETE_RATE_TEXT,
          this.USER_PROP.WATER_DROP_COLOR,
          this.USER_PROP.LIQUIDFILL_WAVE_ANIMATION,
          this.USER_PROP.COMPLETION_RATE_DISPLAY
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_WordCloudSetting(prop, value) {
      this.handler_emptyMethod(prop, value)
    },
    handler_AccumulativeValue(prop, value) {
      if (
        [
          this.USER_PROP.ACCUMULATIVE_VALUE_SHOW,
          this.USER_PROP.ACCUMULATIVE_VALUE_NAME,
          this.USER_PROP.CONTRAST_VALUE_NAME,
          this.USER_PROP.CONTRAST_VALUE_SHOW
        ].includes(prop)
      ) {
        if (prop !== this.USER_PROP.CONTRAST_VALUE_SHOW || value) {
          this.updateWaterfallData()
        }
        this.handler_emptyMethod(prop, value)
      } else if (
        [
          this.USER_PROP.ACCUMULATIVE_VALUE_STYLE,
          this.USER_PROP.CONTRAST_VALUE_STYLE
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_RingWithRatio(prop, value) {
      if (prop === this.USER_PROP.RING_WITH_RATIO) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_MeasureSummary(prop, value) {
      if (prop === this.USER_PROP.MEASURE_SUMMARY_OPEN) {
        if (value) {
          this.$emit('preview-chart')
        } else {
          if (
            MEARSURE_DIS_CENTER.includes(this.chartUserConfig.chartAlias) &&
            this.chartUserConfig.measureConfig.displayPosotion === 'disCenter'
          ) {
            this.handler_emptyMethod(prop, value)
          } else {
            this.chartElement().resizeEcharts()
          }
        }
      } else if (prop === this.USER_PROP.MEASURE_SUMMARY_STYLE) {
        this.chartElement().resizeEcharts()
        if (value?.displayPosotion === 'disCenter') {
          this.handler_emptyMethod(prop, value)
        }
      } else if ([this.USER_PROP.MEASURE_SUMMARY_IGNORE_DIMENSION, this.USER_PROP.MEASURE_SUMMARY_METHOD, this.USER_PROP.MULTI_MEASURE_CONFIG_CHANGE].includes(prop)) {
        this.$emit('preview-chart')
      } else if ([this.USER_PROP.MEASURE_SUMMARY_POSITION, this.USER_PROP.MEASURE_SUMMARY_VALUE_POSITION, this.USER_PROP.MEASURE_SUMMARY_VALUE_ALIAS].includes(prop)) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_ChartRankSetting(prop, value) {
      if (
        [
          this.USER_PROP.BAND_WIDTH_RANK_FIELD,
          this.USER_PROP.BAND_WIDTH_IS_RANK
        ].includes(prop)
      ) {
        this.$emit('preview-chart')
      } else if (
        [
          this.USER_PROP.BAND_WIDTH_RANK_STYLE,
          this.USER_PROP.BAND_WIDTH_RANK_FORMAT,
          this.USER_PROP.BAND_WIDTH_RANK_TIED
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_ChartLegend(prop, value) {
      if (prop === this.USER_PROP.LEGEND_POSITION) {
        if (!value) {
          this.$set(this.chartUserConfig, 'showSaveLegendSelected', false)
        }
        this.onLegendChange(value)
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.LEGEND_TYPE) {
        this.onLegendChange(this.chartUserConfig.legend, value)
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.WATERFALL_LEGEND_NAME) {
        this.updateWaterfallData(false)
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.LEGEND_SELECTED_SAVE) {
        // nothing
        // 该功能在点击右上角保存时才取值
      }  else if (prop === this.USER_PROP.MEASURE_IN_LENGEND_SETTING) {
        if (
          this.element.content.chartUserConfig?.metricCalculation
          && this.element.content.chartUserConfig?.ignoringUnselectedMetrics
          && IGNORING_UNSELECTED_METRICS_CHART.includes(this.element.content.chartUserConfig.chartAlias)
          && this.element?.content?.chartSettings?.extendDimension?.length
        ) {
          this.$emit('preview-chart')
        }
      } else {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_DimensionAxisPostionSetting(prop, value) {
      this.handler_emptyMethod(prop, value)
    },
    handler_ButterflyDataPercentSetting(prop, value) {
      if (prop === this.USER_PROP.BUTTERFLY_DATA_PERCENT && value) {
        this.selfChartDesignHandler()
          .reset({ trigger: prop, newValue: value })
          .next('yAxisSetting')
        // .next('metricsContainer')
        let eventDataObj = {
          source: 'tableInfo',
          target: 'metricdefault',
          targetFn: 'emitFunction',
          data: {
            data: this.chartUserConfig.metricsContainer.default,
            targetFn: 'setList',
            setDragList: true
          }
        }
        const eventData = new EventData(eventDataObj)
        this.$emit('eventBus', eventData)
      } else {
        this.$emit('preview-chart')
      }
    },
    handler_ChartRankOf(prop, value) {
      if (prop === this.USER_PROP.DATA_AMOUNT_RANK) {
        const pageSize = !value
          ? undefined
          : this.chartUserConfig.pagination.pageSize
        this.handler_ChartRankOf(this.USER_PROP.DATA_AMOUNT_PAGESIZE, pageSize)
        this.isMap && this.chartElement().setUpdateChart()
      } else if (prop === this.USER_PROP.DATA_AMOUNT_PAGESIZE) {
        this.$emit('preview-chart', () => true)
      } else if (prop === this.USER_PROP.PAGINATION_RANK_TYPE) {
        this.$emit('preview-chart')
      }
    },
    handler_ChartAlias(key, value) {
      const decompositionChangeAlias = this.chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION && value.alias !== CHART_ALIAS_TYPE.VE_DECOMPOSITION
      if (key === this.USER_PROP.CHART_ALIAS) {
        if (this.chartUserConfig.chartAlias === value.alias) {
          return '相同图形进行切换'
        }

        this.selfChartDesignHandler()
          .reset({
            element: this.element,
            trigger: 'chartAlias',
            newValue: value.alias,
            vm: this.chartElement()
          })
          .next('colorScheme') // 色系，会影响维度和度量的颜色，要放在colors和dimensionColors的前面
          .next('opacityList') // 透明度，会影响维度和度量的颜色，要放在colors和dimensionColors的前面
          .next('metricsContainer') // 度量, 影响了辅助线、预警的配置，所以需要在前面
          .next('yAxis')
          .next('yAxisSetting')
          .next('xAxis')
          .next('gaugeTarget')
          .next('warnLineSettingList') // 预警
          .next('labelConfig') // 数值显示
          // .next('ringWidthRatio') // 圆环比例
          .next('dataZoomChecked') // datazoom
          .next('pictorialBarSettings') // 象形柱图
          .next('showColorBand') // 色带面积
          .next('compositeChart') // 组合图配置
          .next('gridSetting') // 简单表格配置
          .next('decompositionSetting') // 分解树配置
          .next('dimensionWarningList', { DIMENSION_WARNING_CHART }) // 指标维度预警
          .next('isShowEmptyValue') // 空值显示
          .next('isMapGradients') // 显示渐变
          .next('mapSetting') // 地图配置
          .next('mapTooltipSetting') // 提示
          .next('complationRate') // 完成率
          .next('dimensionColors') // 清空维度颜色(要在colors前面)
          .next('colors') // 颜色
          .next('quadrant') // 四象限
          .next('chartType', { chartType: value.elName }) //
          .next('childChartAlias', { chart: value }) // 子图表类型
          .next('metricLabelDisplay') // 显示度量
          .next('colorType') // 颜色类别
          .next('treemapSetting') // 矩形树图配置
          .next('wordCloudSetting') // 词云图配置
          .next('treeSetting') // 树图配置
          .next('calendarSettings') // 日历图配置
          .next('liquidFillSetting') // 水滴图配置
          .next('waterfallSetting') // 瀑布图配置
          .next('roundCascadesSetting') // 层叠圆形图
          .next('bandwidthData') // 带宽图
          .next('stackPercentSetting') // 百分比堆叠柱状图配置
          .next('extendDimensionList') // 维度
          .next('extendDimensionCascaderValue') //
          .next('hoverMetricList') //
          .next('hoverDimensionList') //
          .next('latitudeList') //
          .next('longitudeList') //
          .next('tooltipFormatterList') //
          .next('funnelSettings') // 漏斗图配置
          .next('funnelLabelConfig') // 漏斗图外部标签
          .next('mapBoundaryAttribute')
          .next('xAuxiliaryLineData') // yAuxiliaryLineData也一起处理了
          .next('userLineStyle') // 曲线设置
          .next('mapSchemeSetting') // 地图方案
          .next('negativeConfig') // 负值样式设置
          .next('barSetting') // 柱形圆角
          .next('radarSetting')
          .next('lineSetting')
          .next('measureSummarySetting') // 度量汇总
          .next('scatterSetting')
          .next('butterflySetting') // 蝴蝶图
          .next('linearDirectionType')
          .next('extremeValueSetting') // 极值设置
          .next('mainDimensionMeasureSetting') // 主维度度量
          .next('holidayStyle') // 事件日历标签

        this.emptyChartConfig(value)
        this.chartElement().geoRoamInfo = {}

        this.$set(this.chartUserConfig, 'chartAlias', value.alias)
        this.$emit('on-chart-set', value, this.USER_PROP.CHART_ALIAS)
      } else if (key === this.USER_PROP.CHILD_CHART_ALIAS) {
        if (this.chartUserConfig.childChartAlias === value.typeName) {
          return '相同子图形进行切换'
        }
        this.chartElement().geoRoamInfo = {}

        if (['ve-gauge-normal', 've-map-parent'].includes(value.alias)) {
          this.$set(
            this.chartUserConfig,
            'childChartAlias',
            value.childType.typeName
          )
        }
        this.selfChartDesignHandler()
          .reset({
            element: this.element,
            trigger: this.USER_PROP.CHILD_CHART_ALIAS,
            newValue: value.childType.typeName
          })
          .next('isMapGradients') // 显示渐变
          .next('colors') // 颜色
          .next('mapSchemeSetting') // 地图方案
        this.$emit('on-chart-set', value, key)
      }
      if (this.chartUserConfig?.sortType !== void 0 || decompositionChangeAlias || [CHART_ALIAS_TYPE.VE_DECOMPOSITION].includes(value.alias)) {
        this.$emit('preview-chart')
      }
    },
    change_dimensionList(value, params = {}) {
      this.selfChartDesignHandler()
        .reset({ trigger: 'dimensionList', newValue: value })
        .next('treeSetting')
        .next('dimensionValueIndicatorWarningList')
      if (
        !this.initChart &&
        params.hasOwnProperty('oldLength') &&
        params.oldLength !== value.length
      ) {
        this.$refs.ChartRankOf && this.$refs.ChartRankOf.setDefault()
        this.selfChartDesignHandler()
          .next('xAxis', params)
          .next('yAxis', params)
          .next('wordCloudSetting')
      }
      if (value.length === 2) {
        // 双维度清空时间维度拆分
        this.$set(
          this.currentEditData.content.drillSettings,
          'timeDimensionSplitting',
          '0'
        )
      }
    },
    // 度量发生改变
    change_metricsContainer(value) {
      this.selfChartDesignHandler()
        .reset({ trigger: 'metricsContainer', newValue: value })
        .next('gridSetting')
        .next('pictorialBarSettings')
        .next('dimensionValueIndicatorWarningList')
        .next('radarSetting')
      if (this.$refs.RadarSetting?.setDefault) {
        this.$refs.RadarSetting.setDefault()
      }
    },
    change_extendDimensionList(value, params = {}) {
      this.selfChartDesignHandler()
        .reset({ trigger: 'extendDimensionList', newValue: value })
        .next('dimensionValueIndicatorWarningList')
      if (
        !this.initChart &&
        params.hasOwnProperty('oldLength') &&
        params.oldLength !== value.length &&
        (params.oldLength === 0 || value.length === 0)
      ) {
        this.$refs.ChartRankOf && this.$refs.ChartRankOf.setDefault()
      }
    },
    // 颜色类型发生改变
    change_colorSetting(prop, value) {
      this.selfChartDesignHandler()
        .reset({ trigger: prop, newValue: value })
        .next('showSaveLegendSelected')
      this.handler_emptyMethod(prop, value)
    },
    // 指标选择器拖拽前，获取当前选中指标选择器
    dragChioceTabStart(event) {
      let chioceTab = this.currentEditData.content.chioceTab
      let saveIndex = this.currentEditData.content.saveIndex
      // 保存拖拽前选中指标选择器内容
      this.saveDimension(saveIndex)
      this.currentChioceTabName =
        chioceTab[saveIndex].name || chioceTab[saveIndex].id
    },
    // 指标选择器拖拽结束
    dragChioceTabEnd(event) {
      const { newIndex, oldIndex } = event
      const chioceTab = this.currentEditData.content.chioceTab
      // 获取拖拽后选中指标选择器的index
      const _index = chioceTab.findIndex(
        item =>
          item.name === this.currentChioceTabName ||
          item.id === this.currentChioceTabName
      )
      this.$set(this.currentEditData.content, 'saveIndex', _index)
      this.$emit('sort-chioce-tab')
    },
    initData(val) {
      if (val.chioceTab && val.chioceTab.length) {
        this.$nextTick(() => {
          const chosDeimension = val.saveIndex || 0
          this.chooseDimension(chosDeimension, true)
        })
      }
    },
    handler_ScatterNormalSetting(prop, value) {
      if (prop === this.USER_PROP.SHOW_COLOR_BAND) {
        const toolbox = {
          show: value
        }
        this.$set(this.currentEditData.content.chartConfig, 'toolbox', toolbox) // 重构好之后需要删掉
        this.handler_emptyMethod(prop, value)
      } else if (
        [
          this.USER_PROP.QUADRANT_LINE_COLOR,
          this.USER_PROP.QUADRANT_LINE,
          this.USER_PROP.QUADRANT_LINE_VALUE
        ].includes(prop)
      ) {
        this.handler_emptyMethod(prop, value)
      }
    },
    handler_ChartTitle(key, value) {
      if (
        [this.USER_PROP.TITLE_TEXT, this.USER_PROP.TITLE_TEXT_STYLE].includes(
          key
        )
      ) {
        // 不用重绘图形，因为不是用echarts做的，已经双向绑定了
        // 标题的改变会改变图形的大小
        this.chartElement().resizeEcharts()
      }
    },
    handler_MapDisplaySetting(prop, value) {
      this.handler_emptyMethod(prop, value)
    },
    handler_RadarSetting(prop, value) {
      if (prop === this.USER_PROP.X_AXIS_SETTING) {
        this.handler_emptyMethod(prop, value)
      } else if (prop === this.USER_PROP.RADAR_SETTING) {
        this.handler_emptyMethod(prop, value)
      }
    },
    mapGradientsShowHandler(val) {
      this.handler_emptyMethod('MAP_GRADIENTS_SHOW', val)
    },
    handler_DimensionLangColor(prop, value) {
      this.handler_emptyMethod(prop, value)
    },
    onLegendChange(position, legendType) {
      const map = {
        left: 'x',
        right: 'x2',
        top: 'y',
        bottom: 'y2'
      }
      const grid = {
        x: 10,
        y: 10,
        x2: 25,
        y2: 25
      }
      const legend = this.chartUserConfig.legend
      if (legend === 'top' || legend === 'bottom') {
        grid[map[legend]] = 60
      } else {
        grid[map[legend]] = 140
      }
      this.chartUserConfig.grid = grid
      const val = {
        position,
        type: legendType || this.legendType
      }
      if (!position) {
        this.$set(this.chartUserConfig, 'showSaveLegendSelected', false)
      }

      this.$emit('on-chart-set', val, 'legend')
    },
    // 清空图表配置
    emptyChartConfig(val) {
      const { chartAlias, metricsContainer } = this.chartUserConfig

      const newAlias = val.alias
      const oldAlias = chartAlias
      if (oldAlias === 've-grid-normal') {
        let eventDataObj = {
          source: 'tableInfo',
          target: 'metricdefault',
          targetFn: 'emitFunction',
          data: {
            data: metricsContainer.default.filter(m => !m.isGridCustom),
            targetFn: 'setList'
          }
        }
        const eventData = new EventData(eventDataObj)
        this.$emit('eventBus', eventData)
      }

      if (!NEED_TIMEDIMENSION_CHART.includes(newAlias)) {
        this.$set(
          this.currentEditData.content.drillSettings,
          'timeDimensionSplitting',
          '0'
        )
      }
    },
    addChoiceTab(tabContent) {
      let { saveIndex = 0, chioceTab } = this.currentEditData.content
      this.$set(
        this.currentEditData.content,
        'saveIndex',
        Math.max(saveIndex, 0)
      )
      if (!chioceTab) {
        this.$set(this.currentEditData.content, 'chioceTab', [])
        chioceTab = []
      }

      const {
        chartAlias,
        dimensionList = [],
        metricsContainer = {}
      } = tabContent.chartUserConfig

      // 没有维度、度量图形不支持新增指标
      const needMetricChart = ['ve-funnel', 've-gauge-normal', 've-waterfall'] // 必须存在度量的图形
      const needDimensionChart = ['ve-tree'] // 必须存在维度的图形
      const needDimensionOrMetricChart = ['ve-grid-normal', 've-liquidfill'] // 维度、度量存在一个即可

      const noDimension = !dimensionList.length
      const noMetric = !metricsContainer.default?.length

      const checkNormalChart =
        ![
          ...needMetricChart,
          ...needDimensionChart,
          ...needDimensionOrMetricChart
        ].includes(chartAlias) &&
        (noDimension || noMetric)
      const checkDimensionChart =
        needDimensionChart.includes(chartAlias) && noDimension
      const checkMetricChart = needMetricChart.includes(chartAlias) && noMetric
      const checkDimensionOrMetricChart =
        needDimensionOrMetricChart.includes(chartAlias) &&
        noDimension &&
        noMetric

      if (
        checkNormalChart ||
        checkDimensionChart ||
        checkMetricChart ||
        checkDimensionOrMetricChart
      ) {
        this.$message({
          message: this.$t('sdp.views.needMetricDimesion'),
          type: 'warning'
        })
        return
      }

      if (chioceTab.length >= chioceTabMaxCount) return

      // 增加指标同时新增需要保存的图表全部内容和指标Tab的索引
      const saveObj = this.$_deepClone(tabContent)
      Reflect.deleteProperty(saveObj, 'chioceTab')
      Reflect.deleteProperty(saveObj, 'saveIndex')
      Reflect.deleteProperty(saveObj, 'superLinkOptions')
      Reflect.deleteProperty(saveObj, 'interactionOptions')
      return saveObj
    },
    // 复制指标
    handleCopyChoiceTab(tabId, i) {
      const content = this.currentEditData.content
      // 复制当前选中指标时，需要先保存指标修改内容
      if (i === content.saveIndex) {
        this.saveDimension(content.saveIndex)
      }
      const chioceTab = content.chioceTab
      const copyTab = chioceTab.find(item => item.id === tabId)
      const saveObj = this.addChoiceTab(copyTab.saveObj)
      if (!saveObj) return

      let newTabName = `${copyTab.name}_copy`
      const copyNum = Math.max(
        ...chioceTab.map(item => {
          if (item.name.includes(newTabName)) {
            let num = item.name.split(newTabName)[1]
            return num ? Number(num) + 1 : 1
          }
          return 0
        })
      )
      const newTempEach = {
        id: this.$_generateUUID(),
        saveObj,
        name: copyNum ? `${newTabName}${copyNum}` : newTabName
      }

      const tipSetList = this.currentEditData.tipSetList || []
      if (tipSetList && tipSetList.length) {
        const target = tipSetList.find(e => e.id === tabId)
        if (target) {
          const data = this.$_deepClone(target)
          data.id = newTempEach.id
          tipSetList.push(data)
        }
      }

      chioceTab.push(newTempEach)
    },
    // todo kyz
    // 新增指标
    handleAddDimensions() {
      const saveObj = this.addChoiceTab(this.currentEditData.content)
      if (!saveObj) return
      if (
        saveObj.chartUserConfig.chartAlias === 've-gauge-normal' ||
        saveObj.chartUserConfig.chartAlias === 've-liquidfill'
      ) {
        const _alias = saveObj.chartUserConfig.chartAlias
        this.$set(
          saveObj.chartUserConfig,
          'gaugeTarget',
          defaultSetting.createGaugeTarget.call(
            this,
            this.themeType,
            saveObj.chartUserConfig.childChartAlias || _alias
          )
        )
      } else {
        this.$set(saveObj.chartUserConfig, 'gaugeTarget', {})
      }
      this.$set(saveObj.drillSettings, 'timeDimensionSplitting', '0')
      Reflect.deleteProperty(saveObj, 'mapSchemeSetting')

      const chioceTab = this.currentEditData.content.chioceTab
      const oldTempEach = {
        id: this.$_generateUUID(),
        saveObj: this.$_deepClone(saveObj),
        name: this.createTabName()
      }
      // 新增同时，清除部分图表配置
      this.emptySomeProperty(saveObj)
      const newTempEach = {
        id: this.$_generateUUID(),
        saveObj,
        name: this.createTabName()
      }
      if (!chioceTab.length) {
        // 如果有交互设置信息，并且是第一次增加指标选择器，则将老的交互设置变为第一个指标选择器的数据
        const interactionOptions = this.currentEditData.content.interactionOptions || []
        if (interactionOptions?.length) {
          interactionOptions.forEach(opt => {
            if (!opt.chioceTabId) {
              opt.chioceTabId = oldTempEach.id
            }
          })
        }
        // 如果有超链接设置，并且是第一次增加指标选择器，则将老的超链接变为第一个指标选择器的数据
        const superLinkOptions = this.currentEditData.content.superLinkOptions || []
        if (superLinkOptions?.length) {
          superLinkOptions.forEach(opt => {
            if (opt.labelBoard.type === SUPERLINK_CONST_TYPE.title && opt.labelBoard.site === SUPER_LINK_CONST_SITE.TITLE) {
              opt.labelBoard.site = `${oldTempEach.id}_${SUPER_LINK_CONST_SITE.TITLE}`
            } else if (opt.labelBoard.type === SUPERLINK_CONST_TYPE.variavle) {
              opt.labelBoard.site = oldTempEach.id
            }
          })
        }

        const tipSetList = this.currentEditData.tipSetList || []
        if (tipSetList && tipSetList.length) {
          const target = tipSetList.find(e => e.id === this.currentEditData.id)
          if (target) {
            target.id = oldTempEach.id
          }
        }
        chioceTab.push(oldTempEach, newTempEach)
      } else {
        chioceTab.push(newTempEach)
      }
    },
    // 切换地图方案
    chooseMapScheme(index) {
      resetDragBox.call(this)
      function resetDragBox() {
        Object.keys(this.$refs).forEach(refKey => {
          const refArray = Array.isArray(this.$refs[refKey])
            ? this.$refs[refKey]
            : [this.$refs[refKey]]
          refArray.forEach(r => {
            if (!r) return
            if (['Drag_Box', 'DragBox'].includes(r.$options.name)) {
              r.restoreChioceTabDimension(index, 'mapSchemeSetting')
            }
          })
        })
      }
      this.$emit('mapScheme-change', index)
    },
    saveCurrentScheme() {
      this.$refs.MapScheme && this.$refs.MapScheme.saveCurrentScheme()
    },
    // MARK:切换指标选择器
    chooseDimension(i, flag = false) {
      // 保存当前配置到上一个指标
      !flag && this.saveDimension(this.currentEditData.content.saveIndex)
      // 触发父组件事件，恢复当前指标配置
      this.$emit('init-metric-dimension', i)
      this.currentEditData.content.saveIndex = i
    },
    mapSchemeSwitch(content) {
      const chartElement = this.chartElement()
      const fn = chartElement.mapSchemeSwitch
      fn && fn(0, content)
    },
    async closeDimension(i) {
      let chioceTab = this.currentEditData.content.chioceTab
      const { interactionOptions, superLinkOptions } = this.currentEditData.content
      const deledChioce = chioceTab[i]
      // 如果被删的指标有交互数据或超链接，就提示删掉
      if (interactionOptions?.length || superLinkOptions?.length) {
        const currentOptions = (interactionOptions || []).filter(item => item.chioceTabId === deledChioce.id)
        const currentLinks = (superLinkOptions || []).filter(item => `${item.labelBoard.site}`.indexOf(deledChioce.id) > -1)
        if (currentOptions.length || currentLinks.length) {
          let isDelete = await this.secondConfirmMessage(this.$t('sdp.views.interactiveWidthDeleteIndicator'))
          if (!isDelete) return
        }
      }
      // 如果被删除的指标被卡片交互了，也进行提示
      if (this.interactionReferencedInfo.els?.length) {
        const actions = this.interactionReferencedInfo.actions
        const keys = Object.keys(actions).filter(key => (actions[key] || []).find(a => a?.actionType === 'indicator' && a?.indicators.includes(deledChioce.id)))
        if (keys.length) {
          let isDelete = await this.secondConfirmMessage(this.$t('sdp.message.chartDelIndicatorTip'))
          if (!isDelete) return
          // 记录被删除得指标id信息
          // 指标ID: [元素ID]
          this.$set(this.interactionReferencedInfo.delIndicators, deledChioce.id, keys)
        }
      }

      const tipSetList = this.currentEditData.tipSetList || []
      if (tipSetList && tipSetList.length) {
        this.$set(this.currentEditData, 'tipSetList', tipSetList.filter(e => e.id !== deledChioce.id))
      }

      chioceTab.splice(i, 1)

      if (this.currentEditData.content.saveIndex >= i) {
        this.currentEditData.content.saveIndex = i === 0 ? i : i - 1
        this.chooseDimension(this.currentEditData.content.saveIndex, true)
      }
      this.$nextTick(() => {
        if (chioceTab.length === 1) {
          this.currentEditData.content.chioceTab = []
          this.currentEditData.content.saveIndex = 0

          const tipSetList = this.currentEditData.tipSetList || []
          if (tipSetList && tipSetList.length) {
            const target = tipSetList[0]
            target.id = this.currentEditData.id
            this.$set(this.currentEditData, 'tipSetList', [target])
          }
        }
        // 如果被删的指标有交互数据，就删掉
        if (interactionOptions?.length || superLinkOptions?.length) {
          this.currentEditData.content.interactionOptions = (interactionOptions || []).filter(item => item.chioceTabId !== deledChioce.id)
          if (!this.currentEditData.content.interactionOptions?.length) {
            Reflect.deleteProperty(this.currentEditData.content, 'interactionOptions')
          }
          this.currentEditData.content.superLinkOptions = (superLinkOptions || []).filter(item => `${item.labelBoard.site}`.indexOf(deledChioce.id) < 0)
          if (!this.currentEditData.content.superLinkOptions?.length) {
            Reflect.deleteProperty(this.currentEditData.content, 'superLinkOptions')
          }
        }
        // TODO tang 更新保存时要删掉交互的元素ID和交互ID
      })
    },
    saveDimension(oldIndex) {
      const chioceTab = this.currentEditData.content.chioceTab
      if (!chioceTab || chioceTab.length < 1) return
      const dimension = chioceTab[oldIndex] || {}
      let saveObj = null
      if (this.currentEditData.content?.mapSchemeSetting?.schemeList?.length) {
        this.saveCurrentScheme()
        saveObj = this.$_deepClone(this.currentEditData.content)
        this.mapSchemeSwitch(saveObj)
      } else {
        saveObj = this.$_deepClone(this.currentEditData.content)
      }
      saveObj.chartUserConfig.updateTimestamp = Date.now()
      setThemeConfig(saveObj.chartUserConfig, this.themeType)
      Reflect.deleteProperty(saveObj, 'chioceTab')
      Reflect.deleteProperty(saveObj, 'saveIndex')
      Reflect.deleteProperty(saveObj, 'dateDimensionStyle')
      Reflect.deleteProperty(
        saveObj.chartUserConfig.themeConfig[this.themeType],
        'dateDimensionColor'
      )
      Object.assign(dimension, { saveObj })
    },
    // 修改指标选择器名称
    // onRename() {
    //   const content = this.currentEditData.content
    //   const isSameName = content.chioceTab.some(item => name && item.name === this.chioceTabName)
    //   if (isSameName) {
    //     return this.$message({
    //       message: this.$t('sdp.views.tagName'),
    //       type: 'warning'
    //     })
    //   }
    //   content.chioceTab[content.saveIndex].name = this.chioceTabName
    //   this.renameDialogVisible = false
    // },
    initChioceTabName() {
      const content = this.currentEditData.content
      this.chioceTabName = content.chioceTab[content.saveIndex].name || ''
    },
    emptySomeProperty(saveObj) {
      const chartUserConfig = saveObj.chartUserConfig
      const axisSetting = generateAxisSetting()
      const axis = generateAxis(this.themeType, chartUserConfig)
      // merge到axisSetting中的配置
      let oldXAxisSetting = {}
      let oldYAxisSetting0 = {}
      if (chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT) {
        oldXAxisSetting = { formatType: 'percentage' }
      }
      if (chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_STACK_PERCCENT) {
        oldYAxisSetting0 = { formatType: 'percentage' }
      }
      const { hoverDimensionList, hoverMetricList, dimensionDestinationList } = saveObj.chartUserConfig
      const initData = {
        dimensionList: [],
        extendDimensionList: [],
        longitudeList: [],
        latitudeList: [],
        metricsContainer: {
          default: []
        },
        dimensionCascaderValue: [],
        factorCascaderValue: [],
        contrastList: [],
        xAuxiliaryLineData: [],
        yAuxiliaryLineData: [],
        sampleData: true,
        warnLineSettingList: [],
        dimensionValueIndicatorWarningList: [],
        dimensionWarningList: [],
        opacityList: {},
        colorType: 'metric',
        tooltipFormatterList: [],
        colors: Color.getDefaultChartColor(saveObj.chartUserConfig, {
          colorThemeType: this.themeType,
          vm: this.chartElement()
        })
      }
      // 加个判断，原先有这个字段就重置，不然会一直有这些字段
      if (hoverDimensionList) {
        initData.hoverDimensionList = []
        initData.hoverDimensionCascaderValue = []
      }
      if (hoverMetricList) {
        initData.hoverMetricList = []
        initData.hoverMetricCascaderValue = []
      }
      if (dimensionDestinationList) {
        initData.dimensionDestinationList = []
      }
      Object.assign(saveObj.chartUserConfig, initData)
      if (saveObj.alias === 've-map-parent') {
        saveObj.chartUserConfig.mapSetting = defaultSetting.createMapSetting(
          this.themeType,
          saveObj.alias,
          undefined
        )
        this.$refs.MapTooltipSetting &&
          this.$refs.MapTooltipSetting.setDefault()
      } else {
        delete saveObj.chartUserConfig.mapSetting
      }
      // 需要监听排名数据
      this.$set(
        saveObj.chartUserConfig,
        'bandwidthData',
        getBandwidthInitData(this.themeType)
      )
      this.$set(
        saveObj.chartUserConfig,
        'xAxisSetting',
        this.$_deepClone({ ...axisSetting, ...oldXAxisSetting })
      )
      this.$set(saveObj.chartUserConfig, 'yAxisSetting', [
        this.$_deepClone({ ...axisSetting, ...oldYAxisSetting0 }),
        this.$_deepClone(axisSetting)
      ])
      this.$set(saveObj.chartUserConfig, 'xAxis', this.$_deepClone(axis))
      this.$set(saveObj.chartUserConfig, 'yAxis', [
        this.$_deepClone(axis),
        this.$_deepClone(axis)
      ])
      // Reflect.deleteProperty(saveObj.chartUserConfig, 'dimensionColors')
      Object.assign(saveObj.chartSettings, {
        dimension: [],
        metrics: []
      })
      this.$delete(saveObj.chartSettings, 'seriesData')
      Object.assign(saveObj.chartData, {
        columns: [],
        rows: []
      })
      saveObj.drillSettings.layers.forEach(item => {
        Object.assign(item, {
          dimension: [],
          metrics: [],
          extendDimension: [],
          contrastList: [],
          hoverMetrics: [],
          hoverDimension: [],
          factors: []
        })
        delete item.gaugeTarget
      })
      Object.assign(saveObj.drillSettings, {
        drillDimensions: [],
        pageInfo: {
          page: 1,
          pageSize: 100
        }
      })
      saveObj.drillList = []
      if (saveObj.chartUserConfig.calendarSettings) {
        saveObj.chartUserConfig.calendarSettings.factorList = []
      }
      // delete saveObj.superLinkOptions
      // delete saveObj.interactionOptions
    },
    createTabName() {
      const str = 'abcdefghijklmnopqrxtuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
      const arr = []
      for (let i = 0; i < 4; i++) {
        arr.push(str.charAt(parseInt(Math.random() * str.length + 1)))
      }
      return (
        arr.join('') +
        Math.random()
          .toString()
          .slice(2, 6)
      )
    },
    initDialogTableInfo() {
      this.selfChartDesignHandler()
        .reset({ trigger: 'init' })
        .next('labelConfig')
        .next('waterfallSetting')
        .next('userLineStyle')
        .next('xAxis')
        .next('yAxis')
        .next('xAxisSetting')
        .next('yAxisSetting')
        .next('auxiliaryLine')
        .next('radarSetting')
        .next('isAllowLabelOverlap')
        .next('wordCloudSetting')
        .next('negativeConfig') // 负值样式设置
        .next('measureSummarySetting')
        .next('scatterSetting')
        .next('lineSetting')
        .next('extremeValueSetting') // 初始化极值设置

      const chartUserConfig = this.currentEditData.content.chartUserConfig

      // 设置默认值
      const defaultList = {
        xAuxiliaryLineData: [],
        yAuxiliaryLineData: [],
        hoverDimensionList: [],
        hoverMetricList: [],
        showMeasureInLegend: true
      }
      Object.keys(defaultList).forEach(key => {
        if (!chartUserConfig.hasOwnProperty(key)) {
          this.$set(chartUserConfig, key, defaultList[key])
        }
      })

      if (chartUserConfig.hasOwnProperty('datasetAssociation')) {
        this.datasetAssociation = chartUserConfig.datasetAssociation
      }
      this.chartElement().geoRoamInfo =
        chartUserConfig.mapSetting?.geoRoamInfo || {}
      this.$nextTick(() => {
        const refsList = [
          'ChartTitle',
          'MeasureSummary',
          'ScatterNormalSetting',
          'AnimationSetting',
          'MergeOthers',
          'MetricCalculation',
          'GaugeTickStyle',
          'MapTooltipSetting',
          'MobilePagesize',
          'ChartLegend',
          'RadarSetting'
        ]
        refsList.forEach(item => {
          this.$refs[item]?.setDefault && this.$refs[item].setDefault()
        })
      })
    },
    handleRenameInputStatus(index) {
      const value = !this.renameInputVisible[index]
      this.renameInputVisible.forEach((item, i) => {
        this.$set(this.renameInputVisible, i, false)
      })
      this.$set(this.renameInputVisible, index, value)
      this.$nextTick(() => {
        if (value) {
          this.$refs[`tagInput${index}`][0].focus()
        }
      })
    },
    // 移动端效验指标选择器名字不超过20，中文2个长度，汉字数字一个长度
    validateChoiceTabName(value, chioceTab) {
      if (!this.isMobile) return
      let len = 0
      for (let e of value) {
        if (/^[\u4e00-\u9fa5]$/.test(e)) {
          len += 2
        } else {
          len += 1
        }
        if (len > 20) break
      }
      console.log(len)
      if (len > 20) {
        this.$message({
          message: this.$t('sdp.views.choiceTabNameLen'),
          type: 'warning'
        })
        chioceTab.name = chioceTab.name.substr(0, 10)
      }
    },
    handleindicatorSelectorShowChange(val) {
      this.indicatorSelectorShow = !!val
    },
    handleIndicatorSelectorShowType(param) {
      this.indicatorSelectorShowType = param
      if (param === 'tile-underLine') {
        const contentStyle = this.currentEditData.content.contentStyle
        const themeColor = getThemeConfig(this.themeType, {
          attributes: ['contentStyle'],
          chartAlias: param
        }).contentStyle
        this.$set(
          contentStyle.indicatorSelectorTextStyle,
          'color',
          themeColor.indicatorSelectorTextStyle.color
        )
        this.$set(
          contentStyle.indicatorSelectedTextStyle,
          'color',
          themeColor.indicatorSelectedTextStyle.color
        )
      }
    },
    updateWaterfallData(isUpdateRows = true) {
      const {
        chartData,
        chartUserConfig,
        chartResponse
      } = this.currentEditData.content
      if (!chartResponse || !chartData) return
      const { rows = [] } = chartData
      const { metrics = [], dimension = [] } = chartResponse
      if (metrics.length > 1 || !isUpdateRows) {
        this.$emit('init-colors-config')
        return
      }
      let waterfallRows = handleWaterfallData({
        rows,
        chartUserConfig,
        metrics,
        dimension,
        originalRowLen: chartResponse.rows.length
      })
      waterfallRows && (chartData.rows = this.$_JSONClone(waterfallRows))
    },
    handleAssocciationChange(val) {
      // 有指标选择器时，切换关联数据集开关，需要二次确认
      const { chioceTab } = this.currentEditData.content
      let message = !val
        ? this.$t('sdp.message.datasetAssociation_msg')
        : chioceTab && chioceTab.length > 1
          ? this.$t('sdp.message.datasetChoiceTab_msg')
          : ''
      if (message) {
        this.$sdp_eng_confirm(`${message}`, this.$t('sdp.dialog.hint'), {
          confirmButtonText: this.$t('sdp.button.ensure'),
          cancelButtonText: this.$t('sdp.button.cancel'),
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog ' + this.themeType,
          type: 'warning'
        })
          .then(() => {
            // 关闭关联数据集开关，需要清空数据集id、预警、辅助线、度量、维度、显示度量
            this.$set(this.chartUserConfig, 'datasetAssociation', val)
            // !val && (this.drillSettings.dataSetId = '')
            this.$emit('change-assocciation-switch', val)
          })
          .catch(() => {
            this.datasetAssociation = !!this.chartUserConfig.datasetAssociation
          })
      } else {
        this.$set(this.chartUserConfig, 'datasetAssociation', val)
        this.$emit('change-assocciation-switch', val)
      }
    },
    async secondConfirmMessage(message) {
      return this.$sdp_eng_confirm(message, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
        confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
        cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
        type: 'warning',
      }).then(res => true).catch(() => { })
    },
    openCardLanguage() {
      let moreLanguageContent = [
        {
          id: 'chart-name',
          isTop: true,
          value: this.onlyIndex ? this.$t('sdp.simSystem.selfServiceAnalysisName') : this.$t('sdp.views.chartName'),
          children: [
            {
              id: this.currentEditData.id,
              value: this.currentEditData.elName,
              children: [],
            },
          ],
        },
        {
          id: 'chart-description',
          isTop: true,
          value: this.$t('sdp.views.chartDescription'),
          children: [],
        },
        {
          id: 'chart-title',
          isTop: true,
          value: this.$t('sdp.views.tableExcelName'),
          children: [],
        },
        {
          id: 'chart-content',
          isTop: true,
          value: this.$t('sdp.views.chartContent'),
          children: [],
        },
      ]
      if (!this.currentEditData.elName) {
        moreLanguageContent[0].children = []
      }
      let chartElementLang = {
          children: [],
      }
      getLangElement[TYPE_ELEMENT.CHART].call(this, {
        el: this.currentEditData,
        languageList: chartElementLang
      })
      chartElementLang.children[0]?.children.forEach(v => {
        let type = v.id.split('_').slice(-1)
        switch (type[0]) {
          case 'chartTitle':
            moreLanguageContent[2].children.push(v)
            break
          case 'chartDescription':
            if (v.value !== '') {
              moreLanguageContent[1].children.push({
                ...v,
                isHideDesc: false
              })
            }
            break
          default:
            moreLanguageContent[3].children.push(v)
        }
      })
      const eventData = new EventData({
        source: 'tableInfo',
        target: ['displayPanel', 'displayPanelMobile', 'chartTemplateDesign'],
        targetFn: 'openLanguage',
        type: 'Language',
        data: {
          languageFrom: 'chart',
          boardTreeData: moreLanguageContent.filter(v => v.children.length > 0)
        },
      })
      this.$emit('eventBus', eventData)
    },
    // 热力图图例
    legendHandler(prop, value) {
      this.handler_emptyMethod(prop, value)
    },
    handleChartAlignChange(value) {
      this.$set(this.chartUserConfig, 'alignmentMethod', value)
      this.handler_emptyMethod('alignmentMethod', value)
      const { metricLabelDisplay = [], chartAlias = '' } = this.chartUserConfig
      if (metricLabelDisplay.length && chartAlias !== 've-bar-stack-percent') {
        metricLabelDisplay.forEach(item => {
          if (value === 'right') {
            if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT) {
              item.metricLabelPosition = 'inside'
            } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_PERCENT) {
              item.metricLabelPosition = 'insideRight'
            } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK) {
              item.metricLabelPosition = 'inside'
            } else {
              item.metricLabelPosition = 'left'
            }
          } else {
            if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT) {
              item.metricLabelPosition = 'inside'
            } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_PERCENT) {
              item.metricLabelPosition = 'insideLeft'
            } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK) {
              item.metricLabelPosition = 'inside'
            } else {
              item.metricLabelPosition = 'right'
            }
          }
        })
        this.$set(this.chartUserConfig, 'metricLabelDisplay', metricLabelDisplay)
      }
      if (this.chartUserConfig?.legend === 'left') {
        this.$set(this.chartUserConfig, 'legend', 'right')
      }
    }
  },
  created() {
    this.updateWaterfallData = this.$_debounce(this.updateWaterfallData)
  }
}
</script>
<style lang="scss" scoped>
.chart-design-pannel {
  line-height: 1;
  /deep/ {
    line-height: 1;
  }
}
/deep/ .mt-16 {
  margin-top: 16px !important;
}
/deep/ .mt-8 {
  margin-top: 8px !important;
}
/deep/ .mt-4 {
  margin-top: 4px !important;
}

.table-info-wide-item {
  /deep/ .table-switch-container{
    margin-top: 16px;
  }
  /deep/ {
    .table-switch-container {
      margin-top: 16px !important;
    }
    .item-label-container.item-label-block {
      margin-top: 16px !important;
    }
    .font-setting-container {
      margin-top: 4px !important;
      font-size: 12px !important;
    }
  }
}
.chart-type-title {
  margin-top: 16px;
  margin-bottom: 6px;
  /deep/ {
    font-size: 12px !important;
    font-style: normal !important;
    font-weight: 500 !important;
    line-height: 16px !important;
    color: var(--sdp-xxbt2) !important;
  }
}
.chart-type {
  /deep/ {
    .item-label__level-2 {
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      color: var(--sdp-xxbt2);
    }
    .first-font-color {
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      color: var(--sdp-xxbt2);
      margin-bottom: 6px;
      margin-top: 12px;
    }
    .chart-icon-box, .children-container {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin: 0;
      row-gap: 8px;
      column-gap: 8px;
      .svg-box {
        margin: 0;
        width: 32px;
        height: 32px;
      }
      .svg-box.is-actived {
        border-radius: 4px;
      }
    }
  }
}
.table-info-wide-warning-line{
    /deep/ .table-switch-container{
      margin-top:16px
    }
}
// .table-info-wide-SimpleGridSetting{
//     /deep/ .item-config-container{
//       overflow: inherit!important;
//       // margin-top: 10px !important;
//     }
//     // /deep/ .table-switch-container{
//     //   margin-top:16px!important
//     // }

// }

.setting-tabs {
  margin-top: 24px;
  flex-direction: column;
}
.setting-tabs /deep/ {
  .el-tab-pane {
    display: block !important;
  }
  .el-tab-pane[aria-hidden] {
    height: 0;
    overflow: hidden;
  }
  // sdp-collapse 中内容样式修改
  .sdp-no-collapse {
    .table-switch-container {
      margin-top: 0;
    }
    .table-switch-label {
      display: none;
    }
  }
  .el-tabs__header {
    margin-bottom: 12px!important;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__nav {
    display: flex;
    justify-content: space-between;
    background: var(--sdp-ycsz-hgls);
    border-radius: 4px;
    padding: 4px;
    font-style: normal;
    font-weight: 600;
    width: 312px; // 必须固定宽度，firefox会死
    box-sizing: border-box;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    color: var(--sdp-sztc-bts) !important;
    height: auto!important;
    line-height: 1!important;
    padding: 10px 16px!important;
    border-radius: 4px;
    flex: 1;
    text-align: center;
    &.is-active {
      background: var(--sdp-zs);
      color: var(--sdp-nngl )!important;
    }
  }

  .sdp-collapse-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    .item-label-container {
      > span {
        display: inline-flex;
        align-items: center;
      }
      &.item-label__level-3{
        >span{
          font-weight: 500;
        }
      }
    }
    .item-label-before {
      width: 4px!important;
      height: 4px!important;
      border-radius: 9999px!important;
      margin-top: 0!important;
    }

  }

}
.chart-design-pannel /deep/ {
  $height: 32px;

  @mixin lineHeight {
    height: $height - 2px;
    line-height: $height - 2px;
  }

  .el-input.is-disabled {
    .el-input__inner {
      // color: var(--sdp-jys);
      // background: var(--sdp-ycsz-srk-bgs) !important;
      // border-color: var(--sdp-srk-bks) !important;
    }

    // ::placeholder{
    //   color: var(--sdp-jys);
    // }
  }

  .item-config-container {
    // flex-direction: column;
    // align-items: start;
    // gap:4px;

    box-sizing: border-box;
    margin-top: 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    line-height: 16px;
    font-size: 12px;
    width: 100%;
    overflow: hidden;

    .item-label-container {
      &+* {
        flex-shrink: 0;
      }
    }

    &.item-config-container-column {
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;

      .item-config-label {
        margin-bottom: 10px;
      }
    }

    .el-color-picker,
    .el-color-picker__trigger {
      height: 22px;
    }

    .el-input__inner {
      padding: 0 4px;
    }
  }

  .boundary-attribute {
    .el-input__inner {
      padding: 0 4px;
    }

    .el-input-number {
      height: 32px;

      .el-input__inner {
        width: 100%;
        padding-right: 32px;
        box-sizing: border-box;
        text-align: center;
      }
    }
  }

  .el-input-number {
    width: 92px;
    height: 32px;

    .el-input__inner {
      width: 92px;
      height: 32px;
    }
  }

  .chart-design-splite-line {
    border-color: var(--sdp-ycfgx);
    border-bottom-width: 1px;
    border-bottom-style: solid;
    height: 1px;
    box-sizing: border-box;
  }

  .item-margin {
    margin-bottom: 10px;
  }

  .el-collapse {
    border-top: 0;
    border-bottom: 0;
    position: relative;

    .el-collapse-item__header {
      height: 36px;
      line-height: 36px;
      background: transparent;
      border: 0;
      color: var(--sdp-ycsz-rskbt);
    }

    .el-collapse-item__wrap {
      border: 0;
      background: transparent;
    }

    .el-collapse-item__content {
      padding-bottom: 12px;
    }
  }

  .el-input__inner {
    border-radius: 2px;
    padding-left: 8px;
    font-size: 12px;
    height: 32px;
    line-height: 26px;
  }

  .el-select__caret,
  .el-input__prefix,
  .el-input__suffix {
    @include lineHeight();
    top: 1px;
  }
  .el-input__prefix {
    overflow: hidden;
  }

  .cursor-icon {
    cursor: pointer;
    font-size: 12px;
    color: var(--sdp-zs);

    &.is-disabled {
      cursor: not-allowed;
      color: var(--sdp-jys);
    }
  }

  .show-auto {
    height: 152px;
    overflow-x: hidden;
    overflow-y: overlay;
    border: 0 none;
    outline: none;
    /*兼容火狐*/
    scrollbar-width: none;
    /* 兼容IE10+ */
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #ffffff;
    }
  }

  .show-color {
    margin-top: 6px;

    .color-box {
      display: inline-flex;
      width: 24px;
      height: 24px;
      border-radius: 2px;
    }
  }

  .indicator-addition {
    background: rgba(59, 51, 183, 0.064538);
    border: 1px solid var(--sdp-zs);
    color: var(--sdp-zs);
    border-radius: 2px;
    width: 44px;
    height: 26px;
    line-height: 24px;
    text-align: center;
    cursor: pointer;
  }
}

.chart-design-pannel .dimension-select-tags {
  margin-top: 16px;
  width: 100%;
  max-height: 216px;
  overflow-y: auto;
  overflow-x: hidden;

  .el-tag {
    margin-bottom: 8px;
    background-color: var(--sdp-ycsz-srk-bgs);
    border: 1px solid var(--sdp-ycsz-srk-bcs);
    color: var(--sdp-xxbt2);
    height: 28px;
    line-height: 26px;
    width: 100%;
    cursor: pointer;
    position: relative;

    &.checktag {
      border-color: var(--sdp-zs);
      color: var(--sdp-ycsz-glwzs);
    }

    &:hover {

      .icon-sdp-zhibiaoxuanzeqibianji,
      .icon-sdp-zhibiaoxuanzeqifuzhi,
      .icon-sdp-zhibiaoxuanzeqishanchu {
        display: inline-block;
      }

      .tag-span {
        width: calc(100% - 45px);
      }
    }

    .el-input {
      position: absolute;
      left: 0;

      /deep/ .el-input__inner {
        width: calc(100% - 45px);
        height: 26px;
        line-height: 26px;
        border: 0;
        font-size: 12px;
        vertical-align: top;
        padding: 0 8px;
        background-color: transparent;
      }
    }

    .tag-span {
      width: 100%;
      height: 26px;
      display: inline-block;
      vertical-align: top;

      &.hidden {
        span {
          color: transparent;
        }
      }

      span {
        width: 100%;
        overflow: hidden;
        display: inline-block;
        vertical-align: top;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .icon-sdp-zhibiaoxuanzeqibianji,
  .icon-sdp-zhibiaoxuanzeqifuzhi,
  .icon-sdp-zhibiaoxuanzeqishanchu {
    display: none;
    font-size: 12px;
    color: var(--sdp-zs);
    float: right;
    margin-left: 3px;
  }

  .icon-sdp-zhibiaoxuanzeqibianji {
    right: 38px;
  }

  .icon-sdp-zhibiaoxuanzeqifuzhi {
    right: 23px;
  }

  .icon-sdp-zhibiaoxuanzeqishanchu {
    right: 8px;
  }
}

.parent {
  /deep/ .el-checkbox {
    .el-checkbox__label {
      font-size: 12px;
    }

    .el-checkbox__inner {
      opacity: 0.9;
      width: 14px !important;
      height: 14px !important;

      &:after {
        top: -1px;
        left: 4px !important;
      }
    }
  }
}

.chart-input {
  /deep/ input {
    line-height: 12px;
  }
}

.histogram {
  /deep/ .sdp-collapse__content > div {
    width: 144px;
  }
}
.measure-summary {
  /deep/ .el-select {
    width: 100%;
  }
}
  /deep/ {
    .sdp-chart-set-btn {
      width: 100%;
      height: 32px;
      color: var(--sdp-zs);
      border-radius: 4px;
      border: 1px solid var(--sdp-zs) !important;
      background: transparent !important;

      text-align: center;
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
    }
    .languageSetting{
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 275px;
      cursor: pointer;
      i {
        color: var(--sdp-xlk-jts);
      }
    }
  }

</style>
