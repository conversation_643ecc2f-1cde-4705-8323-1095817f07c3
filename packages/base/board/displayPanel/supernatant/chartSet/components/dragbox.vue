<template>
  <div class="dragBox">
    <draggable
      :class="{'pd-lt':isHorizontal}"
      class="draggableBox"
      v-model="dragList"
      :options="mergedDragOption"
      @change="changeDraggable"
    >
      <div
        v-for="(item,i) in dragList"
        :key="(item.alias || item.labeName) + i"
        :class="{ inlineBox: isHorizontal,showBox: !isHorizontal ,'node-disable':!isHorizontal&&item.unavailable }"
      >
        <i :class="elTreeIconfont.iconArr[i]" :title="item.columnTpe" class="treeIcon" v-if="!isHorizontal"></i>
        <slot :item="item">
          <span v-if="!isHorizontal" style=" white-space: nowrap;" :title="item.comment?`${item[dragLabel]} (${item.comment})` :item[dragLabel]">{{item.comment?`${item[dragLabel]}   (${substring15(item.comment)})` :item[dragLabel]}}</span>

          <span :class="elTreeIconfont.content[i] !== ''?'ml-5':''" :title="item[dragLabel]" v-if="!isHorizontal" style="white-space: nowrap;">{{elTreeIconfont.content[i] !== '' ?`(${elTreeIconfont.content[i]})` : ''}}</span>
          <drag-cascader
            :trimMaxWidth='trimMaxWidth'
            ref="dragCascader"
            :type="type"
            :item="item"
            :index="i"
            :dragList="dragList"
            :dragBoxType="dragBoxType"
            :isMinBandwidth="isMinBandwidth"
            :isMaxBandwidth="isMaxBandwidth"
            :isAutoBandwidth="isAutoBandwidth"
            :isLongitude="isLongitude"
            :isLatitude="isLatitude"
            :isBoundToIntersectionLocation="isBoundToIntersectionLocation"
            :isShowMapDestination="isShowMapDestination"
            :current-edit-data="currentEditData"
            :is-metric="isMetric"
            :isTargetLable="isTargetLable"
            :drillSettings="drillSettings"
            :metric-type="metricType"
            v-if="isHorizontal"
            @clear-status="data => $emit('clear-status', data)"
            @selectMess="dragCascaderSelect"
            @customsort="customSort"
            @setGraphicalParam="setGraphicalParam"
            @alias-change="aliasChange"
            @deleteLists="deleteLists(i)"
          />
        </slot>
      </div>
      <slot name="placeholder"></slot>
    </draggable>
    <DialogSelectIndicator ref="refDialogSelectIndicator" @select="selectIndicator"/>

  </div>
</template>

<script>
import { DragCascader } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { SUPERLINK_CONST_TYPE } from 'packages/assets/constant'
import { DATE_DIMENSION_TYPE, DOUBLE_DIMENSION_CHART, SINGLE_METRIC_CHART } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { substring15 } from '../../../utils'
import { CHART_ALIAS_TYPE } from '../../boardElements/elementChart/constant'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
// import DialogSelectIndicator from '../../common/components/DialogSelectIndicator.vue'
import { EVENT_BUS } from 'packages/base/board/displayPanel/constants'

import indicatorMixin from '../../mixins/indicatorMixin'
import {updatePolymericFilters} from "packages/base/board/displayPanel/supernatant/boardElements/elementChart/bridge";

export default {
  name: 'Drag_Box',
  mixins: [datasetMixin,indicatorMixin],
  components: {
    DragCascader,
    // DialogSelectIndicator
  },
  inject: {
    chart: { default: {} },
    utils: { default: {} },
    isPending: { default: () => () => false },
    sdpBus: { default: () => ({}) },
  },
  props: {
    isHorizontal: {
      type: Boolean,
      default: false,
    },
    // 拖拽框类型
    dragBoxType: {
      /**
       * dimension: 维度
       * dimension-extend: 扩展维度
       * dimension-hover: 地图-提示-维度字段
       * metric-hover: 地图-提示-度量字段
       * dimension-destination: 地图-目的地-维度字段
       */
      type: String,
      default: '',
    },
    dragOption: {
      type: Object,
      default: () => ({}),
    },
    dragLabel: {
      type: String,
      default: 'labeName',
    },
    isBoundToIntersectionLocation: {
      type: Boolean,
      default: false,
    },
    isMetric: {
      type: Boolean,
      default: false,
    },
    prevent: {
      type: Boolean,
      default: false,
    },
    drillSettings: {
      type: Object,
      default: () => ({}),
    },
    indicatorIndex: {
      type: Number,
      default: 0,
    },
    chartUserConfig: {
      default: () => ({}),
    },
    metricType: {
      default: '',
    },
    currentEditData: {
      type: Object,
      default: () => ({}),
    },
    // 最大宽度，超过这个会被截取
    trimMaxWidth: {
      type: Number,
      default: 0
    },
    isTargetLable: {
      type: Boolean,
      default: false
    },
    // 初始化图形标识，图形编辑弹框打开时为true，初始化完成后置为false
    initChart: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: '',
    },
    // 带宽
    isMinBandwidth: {
      type: Boolean,
      default: false
    },
    isMaxBandwidth: {
      type: Boolean,
      default: false
    },
    // 自动带宽
    isAutoBandwidth: {
      type: Boolean,
      default: false
    },
    // 经度
    isLongitude: {
      type: Boolean,
      default: false
    },
    // 纬度
    isLatitude: {
      type: Boolean,
      default: false
    },
    // 开启地图飞线
    isShowMapDestination: {
      type: Boolean,
      default: false
    },
    dimensionType: {
      default: '',
    }
  },

  data () {
    return {
      mapSchemeIndex: 0,
      dragList: [],
      preListLength: 0,
      // dragLists: [],
      notNeedValidate: false,
      onlyMetricChart: ['ve-liquidfill', 've-gauge-normal', 've-bar-percent', 've-themeRiver', 've-ring-multiple'],
      translation: {
        've-histogram-normal': this.$t('sdp.views.histogram'),
        've-histogram-stack': this.$t('sdp.views.stackedHistogram'),
        've-line-normal': this.$t('sdp.views.lineChart'),
        've-line-area': this.$t('sdp.views.lineAreaChart'),
        've-bar-normal': this.$t('sdp.views.barGraph'),
        've-bar-percent': this.$t('sdp.views.barPercent'),
        've-pie-normal': this.$t('sdp.views.pieChart'),
        've-pie-rose': this.$t('sdp.views.roseChart'),
        've-ring-normal': this.$t('sdp.views.ringGraph'),
        've-ring-multiple': this.$t('sdp.views.ringMultiple'),
        've-scatter-normal': this.$t('sdp.views.scatterDiagram'),
        've-composite': this.$t('sdp.views.combineGraphics'),
        've-map-parent': this.$t('sdp.views.map'),
        've-radar': this.$t('sdp.views.radar'),
        've-liquidfill': this.$t('sdp.views.liquidfill'),
        've-funnel': this.$t('sdp.views.funnel'),
        've-wordcloud': this.$t('sdp.views.wordcloud'),
        've-grid-normal': this.$t('sdp.views.simpleGrid'),
        've-pictorialbar': this.$t('sdp.views.pictorialbar'),
        've-calendar': this.$t('sdp.views.calendar'),
        've-themeRiver': this.$t('sdp.views.themeRiver'),
        've-sunburst': this.$t('sdp.views.sunburst'),
        've-tree': this.$t('sdp.views.tree'),
        've-treemap': this.$t('sdp.views.treemap'),
        've-bar-stack': this.$t('sdp.views.barStack'),
        've-waterfall': this.$t('sdp.views.waterfall'),
        've-roundCascades': this.$t('sdp.views.roundCascades'),
        [CHART_ALIAS_TYPE.VE_BANDWIDTH]: this.$t('sdp.views.bandwidthReport'),
        [CHART_ALIAS_TYPE.VE_STACK_PERCCENT]: this.$t('sdp.views.stackPercent'),
        [CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]: this.$t('sdp.views.barStackPercent'),
        [CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]: this.$t('sdp.views.barButterfly'),
        [CHART_ALIAS_TYPE.VE_BAR_HEATMAP]: this.$t('sdp.views.barHeatmap'),
        [CHART_ALIAS_TYPE.VE_DECOMPOSITION]: this.$t('sdp.views.decomposition'),
      }
    }
  },

  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex')
    },
    mergedDragOption() {
      return this.$_deepMergeObjects({
        group: {
          name: 'people',
          // pull: 'clone',
          pull: true,
          put: true,
        },
        filter: '.ignore-chart-elements',
        sort: true,
      }, this.dragOption)
    },
    elTreeIconfont() {
      const tempObj = { iconArr: [], content: [] }
      this.dragList.forEach(item => {
        let sdpiconfont = ''
        switch (item.columnTpe) {
          case 'string':
            sdpiconfont = 'icon-sdp-Fonttype'
            break
          case 'number':
            sdpiconfont = 'icon-sdp-Numerical'
            break
          case 'date':
            sdpiconfont = 'icon-sdp-Calendar'
            break
          default:
            sdpiconfont = 'icon-sdp-Fonttype'
            break
        }
        if (item.tagName) {
          tempObj.content.push(item.tagName)
        } else {
          tempObj.content.push('')
        }
        tempObj.iconArr.push(sdpiconfont)
      })
      return tempObj
    },
    defaultCascader: {
      get() {
        return this.currentEditData.content.chartUserConfig.defaultCascader || []
      },
      set(val) {
        this.$set(this.currentEditData.content.chartUserConfig, 'defaultCascader', val)
      }
    },
    fieldList() {
      return this.chart.fieldList
    },
  },

  watch: {
    'dragList.length'(val, oldVal) {
      let isDimension =  this.dragBoxType ==='dimension' || this.dragBoxType === 'dimension-extend' //如果是维度或者扩展维度
      // this.indicatorDragAfter(this.dragList,isDimension)
      // 当前图形最多可以选择几个维度
        let maxDimension = this.getMaxDimension()
        let otherParam = {
          drillSettings: this.drillSettings || {}
        }
        let list =  this.indicatorDragAfter(this.dragList,isDimension,maxDimension, this.fieldList, otherParam)
        // 是否被弹窗阻断了
        if(!!list?.length && list!=='clogExecute'){
            this.selectIndicator(list)
        }

      this.dragListSettings(val, oldVal, this.currentEditData.content.mapSchemeSetting && this.mapSchemeIndex !== (this.currentEditData.content.mapSchemeSetting?.schemeIndex || 0))
    },
    indicatorIndex: {
      handler(val, oldVal) {
        this.restoreChioceTabDimension(val)
      },
      immediatel: true,
    }
  },

  methods: {
    substring15,
    getMaxDimension(){
      const chartAlias = this.chartUserConfig.chartAlias
      let maxDimension = 2
      if (chartAlias === 've-gauge-normal') {
        maxDimension = 0
        // nothing to do
        // 仪表盘不需要维度，如果拖入了维度，不对其进行验证，隐藏目标值
      } else if (chartAlias === 've-grid-normal') {
        // 第四行第二个的情况
        maxDimension = 10
      } else if (this.dragList.length > 0 && chartAlias === 've-waterfall' && this.chartUserConfig.metricsContainer.default.length > 1) {
        maxDimension = 10
      } else if (this.dragList.length > 1 && !DOUBLE_DIMENSION_CHART.includes(this.currentEditData.content.alias)) {
        // 单维度图形
        maxDimension = 1
      } else if (DOUBLE_DIMENSION_CHART.includes(this.currentEditData.content.alias)) {
        // 如果已经存在一个日期维度，不允许第二个维度也是日期维度
        if (this.dragList.length > 1 && this.dragList.every(item => item.columnTpe === 'date')) {
          // this.preListLength = this.dragList.length
          // this.dragList.splice(1, 1)
          // notNeedRequst = true
          maxDimension = 0
        }
        if (this.dragList.length > 2) {
          maxDimension = 2
        }

        if (this.currentEditData.content.alias === CHART_ALIAS_TYPE.VE_BAR_HEATMAP) {
          maxDimension = 1
        }
      }else {
        let oneDimension = [CHART_ALIAS_TYPE.VE_PIE,CHART_ALIAS_TYPE.VE_ROSE,CHART_ALIAS_TYPE.VE_RING,CHART_ALIAS_TYPE.VE_RING_MULTIPLE,CHART_ALIAS_TYPE.VE_RADAR,
        CHART_ALIAS_TYPE.VE_WORDCLOUD,CHART_ALIAS_TYPE.VE_CALENDAR,CHART_ALIAS_TYPE.VE_ROUNDCASCADES,CHART_ALIAS_TYPE.VE_BANDWIDTH,CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,CHART_ALIAS_TYPE.VE_MAP
        ]
        if(DOUBLE_DIMENSION_CHART.includes(chartAlias)){
          maxDimension = 2
        }else if(oneDimension.includes(chartAlias) || chartAlias ==='ve-liquidfill'){
          maxDimension = 1
        }else if( chartAlias === CHART_ALIAS_TYPE.VE_GAUGE){
          maxDimension = 0
        }else if(chartAlias=== CHART_ALIAS_TYPE.VE_FUNNEL){
          //  第四行的第一个 漏斗图
          if(this.chartUserConfig.metricsContainer.default.length>2){
            maxDimension = 0
          } else{
            maxDimension = 1
          }

        }
      }

      return maxDimension
    },
    selectIndicator(dragList){
      this.dragList = dragList
    },
    // 使用混入代替，这里先不删除
    // indicatorDragAfter(dragList){
    //   // 如果拖拽的是指标数据
    //   let hasIndicator = dragList.find(item => item.isIndicator);
    //   if (hasIndicator) {
    //     let index = dragList.findIndex(item => item.isIndicator);
    //     if(this.dragBoxType ==='dimension'){
    //       let options = hasIndicator.children.filter(item => {
    //         return item.columnTpe === 'string'
    //       });
    //        // 将指标拖过来后dragList.length === 1 ,但是当前未选择其他维度，所以需要-1
    //        if(options.length > 1){
    //         this.$refs.refDialogSelectIndicator.show(options, dragList.length ? dragList.length - 1 : 0, 2, dragList);
    //         return
    //       }

    //     }else{
    //       // 不管拖拽到哪一个(维度除外)，只填充度量到拖拽的那一行,在此处找出度量的数据
    //       let current = hasIndicator.children.find(item => item.columnTpe === 'number');
    //       // 删除指标整个的数据，并塞入度量数据
    //       this.dragList.splice(index, 1, current);
    //     }
    //   }
    // },
    messageInDragbox(message = '', type = 'warning', showClose = false, duration = 3000) {
      this.$message.closeAll()
      this.$message({
        type,
        message,
        duration,
        showClose,
      })
    },
    dragListSettings(val, oldVal, preventRequest) {
      // 指标选择器单轴图形切换到散点图的时候，会校验，需要阻止
      if (this.metricType === 'default' && this.chartUserConfig.chartAlias === 've-scatter-normal') return
      // 是否需要请求数据标识
      let notNeedRequst = false
      if (this.isMetric) {
        notNeedRequst = this.metricChange(val, oldVal)
        // 度量变化时更新聚合过滤数据
        this.updatePolymericFilters()
        // 度量有变更，删除对应的预警和辅助线配置
        const { chartAlias, metricsContainer, } = this.chartUserConfig
        const metricsList = metricsContainer?.default?.filter(m => !m.isGridCustom).map(e => e.alias || e.labeName)
        const keys = ['warnLineSettingList', 'xAuxiliaryLineData', 'yAuxiliaryLineData', 'dimensionWarningList']
        keys.forEach((key) => {
          if (key === 'warnLineSettingList' && chartAlias === 've-gauge-normal') return
          if (this.chartUserConfig[key] && this.chartUserConfig[key].length) {
            if (key === 'dimensionWarningList') {
              this.chartUserConfig[key] = this.$_JSONClone(this.chartUserConfig[key].filter(item => {
                return metricsContainer?.default?.find(m => m.keyName === item.metricKeyName)
              }))
            } else {
              // 辅助线选固定值、其他字段时不进行过滤
              this.chartUserConfig[key] = this.$_JSONClone(this.chartUserConfig[key].filter(item => {
                let filterFlag = metricsList.includes(item.metric)
                if (key !== 'warnLineSettingList') {
                  filterFlag = metricsList.includes(item.metricValue) || item.fieldType === 'fixedValue' || item.metricValue === 'otherField' || item.auxiliaryLineType === 'custom'
                }
                return filterFlag
              }))
            }
          }
        })
      } else if (this.isTargetLable) {
        // 特殊处理目标值
        const message = this.$t('sdp.views.propTargetLimit', { prop: this.translation[this.chartUserConfig.chartAlias] })
        if (this.dragList.length > 1) {
          this.messageInDragbox(message, 'error')
          this.dragList.splice(this.dragList.length - 1, 1)
          notNeedRequst = true
        }
      } else if (this.dragBoxType === 'dimension') {
        // 维度
        notNeedRequst = this.dimensionChange(val, oldVal)
        const { dimensionWarningList = [], dimensionList = [] } = this.chartUserConfig
        const dateDimensionList = dimensionList.filter(d => d.columnTpe === 'date')
        if (dateDimensionList.length !== 1) {
          dimensionWarningList.forEach(d => {
            d.warningList = d.warningList.filter(w => w.fieldColumnType !== 'date')
          })
        } else if (dateDimensionList.length === 1) {
          dimensionWarningList.forEach(d => {
            d.warningList = d.warningList.filter(w => w.fieldKeyName !== dateDimensionList[0].keyName || w.fieldColumnType === 'date')
          })
        }
        this.chartUserConfig.dimensionWarningList = dimensionWarningList.filter(d => d.warningList.length)
      } else if (this.dragBoxType === 'dimension-extend') {
        // 扩展维度
        notNeedRequst = this.extendDimensionChange(val, oldVal)
      } else if (this.isLongitude || this.isLatitude || this.isMaxBandwidth || this.isMinBandwidth || this.isAutoBandwidth) {
        // 经纬度or带宽
        notNeedRequst = this.longitudeAndLatitudeAndBandwidthChange(val, oldVal)
      } else if (this.dragBoxType === 'chartContrastValue') {
        // 对比值
        notNeedRequst = this.contrastValueChange(val, oldVal)
      } else if (this.dragBoxType === 'dimension-destination') {
        // 目的地
        notNeedRequst = this.dimensionDestinationChange(val, oldVal)
      }
      const result = this.repeatMetricHandler(this.dragList)
      const isRepeat = typeof result === 'string'
      if (isRepeat) {
        this.messageInDragbox(`${result}：${this.$t('sdp.views.modifyFiled')}`, 'warning', true)
        this.preListLength = this.dragList.length
        this.$emit('update:prevent', true)
        // 拖入字段需要重置图形配色
        this.$emit('restore-chart-color')
        // return false
      }
      // 清除目标值
      // if ((this.dragList.length === 0)) {
      //   this.deleteTarget()
      // }
      if (this.prevent) { this.$emit('update:prevent', false) }
      !isRepeat && (this.dragList = result)
      this.onDragListChange(preventRequest || notNeedRequst)
    },
    // 更新聚合过滤数据
    updatePolymericFilters() {
      updatePolymericFilters(this.drillSettings)
    },
    // 恢复当前索引的度量或维度
    restoreChioceTabDimension(val, propKey = 'chioceTab') {
      let contentKey = propKey
      if (propKey === 'mapSchemeSetting') {
        contentKey = 'mapSchemeSetting.schemeList'
      }
      const chioceTabList = this.$_getProp(this.currentEditData.content, contentKey)
      if (chioceTabList && chioceTabList.length) {
        // 恢复当前索引的度量或维度
        const chioceTab = this.$_deepClone(chioceTabList[val].saveObj)
        const { dimensionList: dimension = [], metricsContainer, extendDimensionList,
          contrastList, hoverDimensionList = [], hoverMetricList = [], longitudeList = [], latitudeList = [], dimensionDestinationList = [], bandwidthData = {
            maxBandwidthList: [], minBandwidthList: [], bandwidthList: []
          } } = chioceTab.chartUserConfig
        if (this.dragBoxType === 'dimension') {
            if(this.dimensionType === 'x' || this.dimensionType === 'y') {
              this.dragList = dimension?.length ? dimension.filter(d => d.DIMENSIONTYPE === this.dimensionType) : []
            } else {
              this.dragList = dimension
            }
        } else if (this.dragBoxType === 'dimension-extend') {
          this.dragList = extendDimensionList || []
        } else if (this.dragBoxType === 'chartContrastValue') {
          this.dragList = contrastList || []
        } else if (this.isTargetLable) {
          const defaults = this.chartUserConfig.gaugeTarget.defaults
          this.dragList = defaults ? [defaults] : []
        } else if (this.isMetric) {
          const keys = Object.keys(metricsContainer)
          if (keys.length === 1) {
            if (this.metricType !== 'line' && this.metricType !== 'y') {
              this.dragList = metricsContainer.default || []
            } else {
              this.dragList = []
            }
          } else {
            this.dragList = metricsContainer[this.metricType] || []
          }
        } else if (this.dragBoxType === 'dimension-hover') {
          this.dragList = hoverDimensionList
        } else if (this.dragBoxType === 'metric-hover') {
          this.dragList = hoverMetricList
        } else if (this.isLongitude) {
          this.dragList = longitudeList
        } else if (this.isLatitude) {
          this.dragList = latitudeList
        } else if (this.isMaxBandwidth) {
          this.dragList = bandwidthData.maxBandwidthList
        } else if (this.isMinBandwidth) {
          this.dragList = bandwidthData.minBandwidthList
        } else if (this.isAutoBandwidth) {
          this.dragList = bandwidthData.bandwidthList
        } else if (this.dragBoxType === 'dimension-destination') {
          this.dragList = dimensionDestinationList || []
        }
        this.preListLength = this.dragList.length
        this.$nextTick(() => {
          this.mapSchemeIndex = val
        })
      }
    },
    // 设置对比类型
    setCompareMetirc() {
      // const compare = {
      //   dateField: '', //
      //   dateType: '', // ChartCalendarTypeEnum   营运日历(...)，财务日历(...)
      //   compSubType: '', // 枚举类型AdvancedSubTypeEnum 增长率，增长值，对比值
      // }
    },
    deleteLists(index) {
      if (this.isPending()) {
        return this.messageInDragbox(this.$t('sdp.views.awaitChartData'), 'warning')
      }
      const oldDateDimensionLength = this.dragList.filter(d => d.columnTpe === 'date').length
      if (this.isHorizontal) {
        this.$delete(this.dragList, index)
      }
      const dateDimensionList = this.dragList.filter(d => d.columnTpe === 'date')
      if (this.dragBoxType === 'dimension' && dateDimensionList.length === 1 && oldDateDimensionLength !== 1) {
        this.dragList.some(e => {
          const flag = e.columnTpe === 'date'
          if (flag) {
            this.changeCascader('setDateDimensionVal')
          }
          return flag
        })
      }
      // 删除字段时会触发dragList.length的监听，所以此处不需要请求接口
      this.$emit('on-change', { dragList: this.dragList, notPreview: true, oldLength: this.preListLength })
    },
    clearList() {
      this.dragList = []
    },
    setList(list) {
      this.dragList = list
    },
    emitFunction(eventData) {
      const { data } = eventData
      if (data.targetFn) {
        this[data.targetFn](data.data)
      }
      if (data.setDragList) {
        this.$emit('on-change', this.dragList)
      }
    },
    getList() {
      return this.dragList
    },
    // 选中级联菜单的某一项的事件
    dragCascaderSelect({ selectValue, selectItem, selectList }) {
      const { dateDimension, SORT } = selectValue
      if (this.dragBoxType === 'dimension') {
        if (SORT !== 'customSort') {
          const key = selectList.findIndex(e => (e.alias || e.labeName) === (selectItem.alias || selectItem.labeName))
          delete selectList[key].orderList
        }
      }
      this.$emit('on-change', selectList)
    },
    setGraphicalParam(Param) {
      this.$emit('on-change-graphical', Param)
    },
    aliasChange(selectList) {
      if (this.prevent) { this.$emit('update:prevent', false) }
      this.$emit('on-change', selectList)
      if (this.isMetric) {
        this.$emit('clear-lengend-select-settings')
        // 修改别名时，需要更新图形度量标签显示列表
        this.$emit('update-metric-label')
      }
    },
    repeatMetricHandler(dragList) {
      let keyNameList = new Set()
      // 避免相同度量指向同一块地址
      for (let i = 0; i < dragList.length; i++) {
        const newVal = { ...dragList[i] }
        // 避免重复keyName
        if (keyNameList.has(dragList[i].keyName)) {
          newVal.keyName = this.$_generateKeyName(this.getDatasetLabel(newVal), i)
          keyNameList.add(newVal.keyName)
        } else if (dragList[i].keyName) {
          keyNameList.add(dragList[i].keyName)
        }
        dragList[i] = newVal
      }
      // 设置 alias
      dragList.forEach((e, i) => {
        // e.alias = e.aliasName || e.alias || e.labeName
        // 兼容老看板
        if (e.expression) {
          const [a, b] = e.expression
          e.exp = `${a.aggType}(${a.columnName})${a.operator}${b.aggType}(${b.columnName})`
          delete e.expression
        }
      })
      // 处理相同别名的度量
      const aliasList = new Set()
      for (let i = 0; i < dragList.length; i++) {
        const alias = this.getDatasetLabel(dragList[i])
        if (aliasList.has(alias)) {
          return alias
        }
        aliasList.add(alias)
      }
      return dragList
    },
    clearStatus(data) {
      const dragCascaderComp = this.$refs.dragCascader
      dragCascaderComp && dragCascaderComp.forEach(comp => {
        comp.clearStatus(data)
      })
    },
    initCascader(data) {
      this.changeCascader('initCascader')
      // const dragCascaderComp = this.$refs.dragCascader
      // dragCascaderComp && dragCascaderComp.forEach(comp => {
      //   comp.initCascader(data)
      // })
    },
    initCompareData() {
      this.changeCascader('initCompareData')
      // const dragCascaderComp = this.$refs.dragCascader
      // dragCascaderComp && dragCascaderComp.forEach(comp => {
      //   comp.initCompareData()
      // })
    },
    changeCascader(method, data) {
      const dragCascaderComp = this.$refs.dragCascader
      dragCascaderComp && dragCascaderComp.forEach(comp => {
        comp[method](data)
      })
    },
    // 自定义排序
    customSort(orderList) {
      this.$emit('on-customsort', orderList)
    },
    // 当列表长度发生变化的时候触发
    onDragListChange(notNeedRequst) {
      if (this.preListLength !== this.dragList.length) {
        if (this.dragBoxType === 'dimension') {
          const dateDimensionList = this.dragList.filter(d => d.columnTpe === 'date')
          this.dragList.forEach(e => {
            if (e.columnTpe === 'date' && dateDimensionList.length === 1) {
              e.timeDimensionSplittingRule = e.timeDimensionSplittingRule || DATE_DIMENSION_TYPE.Day_vs_day
            }
          })
          // 图形维度变更时，需要清空维度超链接设置
          const superLinkOptions = this.currentEditData.content.superLinkOptions
          if (superLinkOptions && !this.initChart) {
            const titleSuperLink = superLinkOptions.filter(item => item.labelBoard.type !== SUPERLINK_CONST_TYPE.variavle)
            titleSuperLink.length ? this.$set(this.currentEditData.content, 'superLinkOptions', this.$_deepClone(titleSuperLink)) : this.$delete(this.currentEditData.content, 'superLinkOptions')
          }
        }

        if (this.dragBoxType === 'dimension-extend' && !this.initChart) {
          // 修改扩展维度字段，图例变更，需要清空保存图例配置
          this.$emit('clear-lengend-select-settings')
        }
        console.log('dragList1',this.$_deepClone(this.dragList))
        this.$emit('on-change', { dragList: this.dragList, notPreview: this.chartUserConfig.chartAlias !== 've-scatter-normal' && notNeedRequst, oldLength: this.preListLength }, this.$attrs['dimension-type'])
        console.log('dragList2',this.$_deepClone(this.dragList))
        this.preListLength = this.dragList.length
        // 度量长度变化时，需要更新图形度量标签显示列表
        if (this.isMetric) {
          this.$emit('update-metric-label')
        }
      }
    },
    validateDimensionLength(len = 1) {
      // hbw
      let notNeedRequst = false
      if (this.notNeedValidate) return
      const prop = this.translation[this.currentEditData.content.alias]
      const str = len === 0 ? this.$t('sdp.views.WaterfallDimensionWarn') : len === 1
        ? prop + this.$t('sdp.views.onlyOneDimension') : len === 2
          ? prop + this.$t('sdp.views.doubleDimension') : this.$t('sdp.views.dimensionLengthProp', { prop: prop, n: len })
      this.messageInDragbox(str, 'error')

      let deleteKey = this.dragList.findIndex(e => !this.chartUserConfig.dimensionList.map(d => d.labeName).includes(e.labeName))
      deleteKey = (deleteKey === -1 && this.dragList.length > 2) ? this.dragList.length - 1 : deleteKey
      if (len === 1) deleteKey = 1
      this.$delete(this.dragList, deleteKey)
      notNeedRequst = true
      this.preListLength = this.dragList.length + 1
      return notNeedRequst
    },
    deleteTarget() {
      // 删除目标值
      const { drillSettings } = this.currentEditData.content
      const gaugeTarget = this.chartUserConfig.gaugeTarget
      if (gaugeTarget === undefined) return
      Object.assign(gaugeTarget, { exp: '', type: 'aggType' })
      this.$delete(drillSettings.layers[0], 'gaugeTarget')
    },
    changeDimensionLength(val, oldVal) {
      // 打开弹窗初始化或者指标选择器标识
      const init = oldVal === 0 && val === 2
      // 双维度需要调整的一些设置,可能会有问题，先加上去
      this.changeCascader('doubleDimensionSetting', { init })
      this.$nextTick(() => {
        const dateDimensionList = this.dragList.filter(d => d.columnTpe === 'date')
        if (dateDimensionList.length !== 1) {
          if (this.dragList.some(item => item.columnTpe === 'date')) {
            // 多个日期类型，隐藏日期维度
            this.changeCascader('hiddenDateFunc')
          }
        }
        if (this.dragList.length === 2) {
          // 双维度需要调整的一些设置
          this.changeCascader('doubleDimensionSetting', { init })
        } else if (this.dragList.length === 1) {
          // 单维度需要恢复的一些设置
          this.changeCascader('initCascader')
        }
      })
    },
    dimensionChange(val, oldVal) {
      let notNeedRequst = false
      const chartAlias = this.chartUserConfig.chartAlias
      if (chartAlias === 've-gauge-normal') {
        // nothing to do
        // 仪表盘不需要维度，如果拖入了维度，不对其进行验证，隐藏目标值
      } else if (this.currentEditData.content.alias === CHART_ALIAS_TYPE.VE_BAR_HEATMAP) {
        // 只允许拖拽一个，多余一个进入检测
        if (val > 2) {
          notNeedRequst = this.validateDimensionLength()
        } else if (val > oldVal && oldVal !== 0) {
          // 验证后，如果有日期字段并且开启了日期维度切换，会重新进下面判断，解决拖一个普通字段一个日期出现死循环的问题
          if (
            this.dragList.filter(item => item.DIMENSIONTYPE === 'x').length === 1
            && this.dragList.filter(item => item.DIMENSIONTYPE === 'y').length === 1
            && this.chartUserConfig.dimensionList.filter(item => item.DIMENSIONTYPE === 'x').length === 1
            && this.chartUserConfig.dimensionList.filter(item => item.DIMENSIONTYPE === 'y').length === 1
          ) {
            console.log('数量正确跳过验证');
          } else {
            notNeedRequst = this.validateDimensionLength()
          }
        }
      } else if (chartAlias === 've-grid-normal') {
        if (this.dragList.length > 10) {
          notNeedRequst = this.validateDimensionLength(10)
        }
      } else if (chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION) {
        if (this.dragList.length > 5) {
          notNeedRequst = this.validateDimensionLength(5)
        }
      } else if (this.dragList.length > 0 && chartAlias === 've-waterfall' && this.chartUserConfig.metricsContainer.default.length > 1) {
        this.dragList.splice(0, 1)
        notNeedRequst = this.validateDimensionLength(0)
      } else if (this.dragList.length > 1 && !DOUBLE_DIMENSION_CHART.includes(this.currentEditData.content.alias)) {
        // 单维度图形
        notNeedRequst = this.validateDimensionLength()
      } else if (DOUBLE_DIMENSION_CHART.includes(this.currentEditData.content.alias)) {
        // 如果已经存在一个日期维度，不允许第二个维度也是日期维度
        if (this.dragList.length > 1 && this.dragList.every(item => item.columnTpe === 'date')) {
          this.preListLength = this.dragList.length
          this.dragList.splice(1, 1)
          notNeedRequst = true
        }

        if (this.dragList.length > 2) {
          // 双维度图形
          notNeedRequst = this.validateDimensionLength(2)
        }
        // 维度变化时的一些变化
        this.changeDimensionLength(val, oldVal)
      }
      // 删除维度和替换维度 新增第二个维度 清空钻取数据(初始化、切换指标选择器不做处理)
      // 非初始化时清空钻取数据,防止图形编辑框打开钻取数据被清空,不能放在dragCascader中清空,清空后activeCascader没有重新计算
      // 注释替换维度条件 dimension.length === this.dragList.length && dimension[0] !== this.dragList[0].labeName
      const moreDimension = this.dragList.length === 2
      const dimension = this.currentEditData.content.chartSettings.dimension
      if (!this.initChart && chartAlias !== 've-grid-normal' && ((oldVal === 2 && val === 1) || (val === 0 && oldVal === 1) || moreDimension)) {
        // 清空钻取数据
        this.currentEditData.content.drillList = []
        // this.drillSettings.drillDimensions = []
        this.$set(this.drillSettings, 'drillDimensions', [])
        // 清空度量数据
      }
      this.notNeedValidate = false
      return notNeedRequst
    },
    metricChange(val, oldVal) {
      let notNeedRequst = false
      let message = ''
      const arr = [...SINGLE_METRIC_CHART].concat('ve-scatter-normal')
      const { chartAlias: alias, dimensionList = [] } = this.chartUserConfig
      // 特殊处理仪表盘
      if (arr.includes(alias) && val > 1 && !this.isTargetLable) {
        if (alias === 've-gauge-normal' && !dimensionList.length) {
          message = this.$t('sdp.views.dialSpportsOnlyOneMeasure')
        } else if (alias === 've-scatter-normal') {
          message = this.$t(this.onlyIndex ? 'sdp.simSystem.scatterIndicatorLimit' : 'sdp.views.scatterMetricLimit')
        } else if (arr.filter(item => item !== 've-gauge-normal').includes(alias)) {
          message = this.$t(this.onlyIndex ? 'sdp.simSystem.dripIndicatorLimit' : 'sdp.views.dripMetricLimit', { prop: this.translation[alias] })
        }
      } else if ([CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT].includes(alias) && val > 20) {
        message = this.$t('sdp.views.metricLengthProp', { prop: this.translation[alias], n: 20 })
      } else if (alias === 've-grid-normal' && val > 50) {
        message = this.$t('sdp.views.metricLengthProp', { prop: this.translation[alias], n: 50 })
      } else if (alias === CHART_ALIAS_TYPE.VE_DECOMPOSITION && val > 1) {
        message = this.$t('sdp.views.metricLengthProp', { prop: this.translation[alias], n: 1 })
      } else if (alias === 've-waterfall' && dimensionList.length && val > 1) {
        message = this.$t('sdp.views.WaterfallMetricsWarn')
      } else if (alias === 've-funnel' && dimensionList.length === 1 && val > 2) {
        message = this.$t('sdp.views.FunnelMetricsWarn')
      } else if (alias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY && val > 2) {
        message = this.$t('sdp.views.metricLengthProp', { prop: this.translation[alias], n: 2 })
      }
      if (message) {
        this.messageInDragbox(message, 'error')
        this.dragList.splice(this.dragList.length - 1, 1)
        notNeedRequst = true
        this.preListLength = this.dragList.length + 1
      }
      return notNeedRequst
    },
    sizeChange() {
      const oldGragList = this.chartUserConfig.metricsContainer.size || []
      if (this.dragList.length > 1 && this.dragList.length !== oldGragList.length) {
        const labelName = Array.isArray(oldGragList) && oldGragList.map(el => { return el.labeName })
        const newDragList = this.dragList.filter(dragEl => { return !labelName.includes(dragEl.labeName) })
        this.dragList = newDragList
      }
    },
    changeDraggable(e) {
      // 维度/度量发生变化时需要重置维度颜色
      const { chartAlias, dimensionList } = this.chartUserConfig
      // if (hasDimensionColor(chartAlias, dimensionList.length)) {
      //   Reflect.deleteProperty(this.chartUserConfig, 'dimensionColors')
      // }
      if (e.added) {
        let { element, newIndex } = e.added
        Reflect.deleteProperty(element, 'order')
        Reflect.deleteProperty(element, 'orderList')
        !element.keyName && (element.keyName = this.$_generateKeyName(element.labeName, newIndex))
        if (this.dragBoxType === 'dimension') {
          this.$delete(element, 'aggType')
        }
        if (newIndex < this.dragList.length - 1) {
          this.changeCascader('initCascader')
        }
      }
      if (e.moved) {
        this.$nextTick(() => {
          if (this.dragList.length === 2) {
            this.changeCascader('doubleDimensionSetting')
          }
          this.changeCascader('initCascader')
          this.$emit('on-change', this.dragList)
        })
      }
      // 维度，度量变更，清除用户保存的图例选择
      this.$set(this.chartUserConfig, 'saveLegendSelected', undefined)
    },
    contrastValueChange(val, oldVal) {
      let message = ''
      let notNeedRequst = false
      if (this.dragList.length > 1) {
        message = this.$t('sdp.views.onlyOneContrastValue')
      }
      if (message) {
        this.messageInDragbox(message, 'warning', true)
        this.dragList.splice(this.dragList.length - 1, 1)
        notNeedRequst = true
        this.preListLength = this.dragList.length + 1
      }
      return notNeedRequst
    },
    extendDimensionChange(val, oldVal) {
      let message = ''
      let notNeedRequst = false
      if (this.dragList.length > 2) {
        message = this.$t('sdp.views.onlyTwoExtendDimenison')
      }
      if (this.chartUserConfig.chartAlias === 've-bar-stack' && this.dragList.length > 1) {
        message = this.$t('sdp.views.onlyOneExtendDimenison')
      }
      if (message) {
        this.messageInDragbox(message, 'warning', true)
        this.dragList.splice(this.dragList.length - 1, 1)
        notNeedRequst = true
        this.preListLength = this.dragList.length + 1
      }
      // 新增扩展维度，清空设置
      if (val > oldVal) {
        const extendDimensionList = this.chartUserConfig.extendDimensionList || []
        const newItem = this.dragList.find(dragItem => {
          return !extendDimensionList.some(item => item.labeName === dragItem.labeName)
        })
        newItem && newItem.order && this.$delete(newItem, 'order')
      }
      return notNeedRequst
    },
    longitudeAndLatitudeAndBandwidthChange(val, oldVal) {
      let notNeedRequst = false
      if (this.dragList.length > 1) {
        this.preListLength = this.dragList.length
        this.dragList.splice(1, 1)
        notNeedRequst = true
        let message = ''
        if (this.isLongitude || this.isLatitude) {
          message = this.$t('sdp.views.latitudeDragWarn')
        } else if (this.isMaxBandwidth || this.isMinBandwidth) {
          message = this.$t(`sdp.views.${this.isMaxBandwidth ? 'maxBandwidthDragWarn' : 'minBandwidthDragWarn'}`)
        } else if (this.isAutoBandwidth) {
          message = this.$t(`sdp.views.bandwidthDragWarn`)
        }
        this.messageInDragbox(message, 'warning', true)
      }
      return notNeedRequst
    },
    dimensionDestinationChange(val, oldVal) {
      let message = ''
      let notNeedRequst = false
      const { dimensionList } = this.chartUserConfig
      if (this.dragList.length > 1) {
        message = this.$t('sdp.views.desOnlyOne')
      }
      if (this.dragList.length === 1) {
        if (dimensionList?.[0]?.labeName === this.dragList[0]?.labeName) {
          message = this.$t('sdp.views.depDesNotSame')
        }
      }
      if (message) {
        this.messageInDragbox(message, 'warning', true)
        this.dragList.splice(this.dragList.length - 1, 1)
        notNeedRequst = true
        this.preListLength = this.dragList.length + 1
      }
      return notNeedRequst
    }
  },
  created() {
    // this.onDragListChange = this.$_debounce(this.onDragListChange)
    this.dragListSettings = this.$_debounce(this.dragListSettings)
  },
  mounted(){
    this.sdpBus.$on(EVENT_BUS.DBLCLICK_INDICATOR,(current) => {
        if(this.type ==='metric'){
            let flag =  this.dragList.findIndex(item=>item.id===current.id)
            // if(flag===-1){
            //   this.dragList.push(current);
            // }else{
            //   this.dragList.splice(flag,1,current)
            // }
            this.dragList.push(current);
      }
    })
  },
  beforeDestroy() {
    this.sdpBus.$off(EVENT_BUS.DBLCLICK_INDICATOR)
  },
}
</script>

<style lang="scss" scoped>
  .dragBox{
    min-width: 104px;
    max-width: 100%;
    position: relative;
    .draggableBox{
      width: 100%;
      max-height: 100%;
      min-height: 30px;
      padding-left: 10px;
      overflow: auto;
      overflow-x: hidden;
    }
    .pd-lt {
      padding-right: 76px;
    }
    /deep/ .content-item.drag.sortable-chosen{
      display: inline-block;
      background: transparent !important;
      margin-right: 7px;
    }
  }
  .showBox{
    cursor: pointer;
    border: 0;
    min-width: 16px;
    width: 100%;
    //margin:1px;防止拖拽出现其他元素的边框
    display: flex;
    align-items: center;
    margin-top: 10px;
  }
  .inlineBox{
    cursor: pointer;
    border: 0;
    min-width: 16px;
    display: inline-block;
    // height: 100%;
    margin-right: 7px;
    margin-top: 7px;
    //margin: 1px;防止拖拽出现其他元素的边框
  }
  .treeIcon {
    font-size: 12px;
    margin-right: 3px;
  }
  .showBox {
    > span {
      // width: calc(100% - 18px);
      display: inline-block;
    }
  }
  .ml-5 {
    margin-left: 5px;
  }
</style>
