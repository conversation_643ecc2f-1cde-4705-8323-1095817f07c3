import { generateUUID, efficientNumericId, deepMergeObjects } from 'packages/assets/utils/globalTools'
import { getThemeConfig } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/theme'
import { CHART_ALIAS_TYPE } from '../../boardElements/elementChart/constant'
import Vue from 'vue'

export function generateViewFormat(params: any = {}) {
  let result: any = {}
  switch (params.format) {
    case 'normal':
      Object.assign(result, {
        convention: 'normal'
      })
      break
    case 'number':
      Object.assign(result, {
        numerical: {
          decimalNum: params.decimals
        }
      })
      break
    case 'percent':
      Object.assign(result, {
        percentage: {
          decimalNum: params.decimals,
          grayed: !!params.numberDisabled
        }
      })
      break
    case 'currencyUnit':
      Object.assign(result, {
        currencyUnit: {
          decimalNum: params.decimals,
          grayed: !!params.numberDisabled && !params.decimals && params.decimals !== 0,
        }
      })
      if (params.showSmallShortSetting) {
        Object.assign(result.currencyUnit, {
          smallShort: params.smallShort,
        })
      }
      break
    default:
      // tempObj.result = {}
  }
  return result
}

export function restoreViewformat(params: any = {}, helper: any = {}) {
  let result: any = {}
  let _format = ''
  let decimals = ''
  const keys = ['convention', 'numerical', 'percentage', 'currencyUnit']
  const _key = Object.keys(params).find(k => keys.includes(k))
  // 非货币字段默认小数点位数为0
  switch (_key) {
    case 'convention':
      _format = 'normal'
      break
    case 'numerical':
      _format = 'number'
      decimals = params.numerical.decimalNum || 0
      break
    case 'percentage':
      _format = 'percent'
      decimals = helper.numberDisabled ? '' : params.percentage.decimalNum
      break
    case 'currencyUnit':
      _format = 'currencyUnit'
      // 选择货币单位简写时，货币字段默认小数2位，非货币字段默认小数0
      decimals = params.currencyUnit.decimalNum || params.currencyUnit.decimalNum === 0 ? params.currencyUnit.decimalNum : helper.numberDisabled ? 2 : 0
      // 最小单位以下值简写
      result.smallShort = params.currencyUnit.hasOwnProperty('smallShort') ? params.currencyUnit.smallShort : false
      break
  }
  result.format = _format
  const arr = helper.decimalsList.filter(item => item.value === decimals || item.label === decimals)
  result.decimals = arr.length ? arr[0].value : ''
  return result
}

export function generateAxisSetting(oldVal) {
  let defaultResult = {
    maxTickValueCustom: false,
    maxTickValue: '',
    minTickValueCustom: false,
    minTickValue: '',
    decimalPlace: 0,
    chartAxisTitleHidden: true,
    chartAxisTitle: '',
    formatType: 'normal',
  }
  if (oldVal) {
    defaultResult = deepMergeObjects(defaultResult, oldVal)
  }
  return defaultResult
}
export function generateAxis(themeType, chartUserConfig, oldVal) {
  let defaultResult = getThemeConfig(themeType, { attributes: ['axisConfig'], chartAlias: chartUserConfig.chartAlias, }).axisConfig
  if (oldVal) {
    defaultResult = deepMergeObjects(defaultResult, oldVal)
  }
  return defaultResult
}

export function generateMetricLabelDisplayItem(params: any = {}) {
  const { metricsContainer = {}, chartAlias, compositeChart = {}, pictorialBarSettings = {}, alignmentMethod = '' } = params.chartUserConfig
  let newChartAlias = chartAlias
  if (chartAlias === 've-composite') {
    if (compositeChart.stack) {
      newChartAlias = 've-histogram-stack'
    } else {
      newChartAlias = 've-histogram-normal'
    }
  }

  const defaultPositionMap = {
    inside: ['ve-bar-stack', 've-histogram-stack'],
    right: ['ve-bar-normal'],
    insideLeft: ['ve-bar-percent'],
    top: ['ve-histogram-normal', 've-pictorialbar', 've-waterfall'],
  }
  let displayItem = {
    labelType: 'origin',
    origin: {
      showMetricValue: true,
      showMetricPercent: [CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT].includes(newChartAlias), // 百分比堆叠柱状图和百分比堆叠条形图占比默认勾选
      showMetricTotalPercent: false,
      showMetricDimensionValPercent: false,
      showMetricPercentLabel: false,
    },
    customMetricList: [],
    metricLabelPosition: Object.keys(defaultPositionMap).find(k => defaultPositionMap[k].includes(newChartAlias)) || 'inside',
    metricLabelRotationAngle: '0',
  }
  if (chartAlias === 've-pictorialbar' && pictorialBarSettings.flipAxis) {
    displayItem.metricLabelPosition = 'right'
  }
  let needShowOptionChart = [
    CHART_ALIAS_TYPE.VE_BAR,
    CHART_ALIAS_TYPE.VE_BAR_PERCENT,
    CHART_ALIAS_TYPE.VE_BAR_STACK,
    CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT
  ]
  if (needShowOptionChart.includes(chartAlias) && !!alignmentMethod && alignmentMethod === 'right') {
    if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT) {
        displayItem.metricLabelPosition = 'inside'
    } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_PERCENT) {
        displayItem.metricLabelPosition = 'insideRight'
    } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK) {
      displayItem.metricLabelPosition = 'inside'
    } else {
        displayItem.metricLabelPosition = 'left'
    }
  }
  return displayItem
}

export function generateMetricLabelDisplayCustomItem() {
  let result = {
    customMetric: {},
    customCascader: [],
    decimals: 0,
    showMetricValue: false,
    showMetricPercent: false,
    showMetricTotalPercent: false,
    showMetricDimensionValPercent: false,
    showMetricPercentLabel: false,
    customMetricAlias: '',
    isNotShowIcon: true
  }
  return result
}

function uuid() {
  const onlyIndex = Vue.prototype?.$getSystemConfig?.('onlyIndex')
  if (onlyIndex) {
    return efficientNumericId()
  }
  return generateUUID()
}

export function generateAuxiliaryLineItem(option: any = {}) {
  const item = {
    id: uuid(),
    name: '',
    fieldType: 'calculatedValue',
    value: '',
    metricValue: '',
    otherMetricValue: '',
    valueType: '',
    color: '#7EC3FB',
    format: option.format || 'currencyUnit',
    decimalNum: option.decimalNum || 2,
    lineType: 'dashed',
    lineWidth: 1,
    auxiliaryLineType: 'dimension',
    filed1: {},
    filed2: {},
    customCalType: '',
  }
  return item
}
