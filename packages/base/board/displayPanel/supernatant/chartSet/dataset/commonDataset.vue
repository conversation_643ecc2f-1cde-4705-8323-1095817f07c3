<template>
  <div class="dataset-drag-wrap">
    <div class="dataset-drag-placeholder" :style="{ width: DATASET_MIN_WIDTH + 'px' }" v-if="style.position === 'absolute'"></div>
    <!-- 挑选数据集 -->
    <div class="dataset dataset-drag" :style="{
      position: style.position,
      zIndex: style.zIndex,
      width: style.width + 'px',
    }" v-show="isDisplayDataSet">
      <div class="dataset-drag-bar" @mousedown="drag"></div>
      <SourceDataSwitch v-model="drillSettings.sourceDataType" @change="() => {
        indexFlagIds = []
        drillSettings.indexFlagIds = []
        changeIndexFlag()
      }" v-if="!onlyIndex && isShowSourceDataSwitch"></SourceDataSwitch>
      <div v-show="drillSettings.sourceDataType === SOURCE_DATA_TYPE.dataset" v-if="!onlyIndex" :style="{
        height: `calc(100% - ${isShowSourceDataSwitch ? 62 : 0}px)`
      }">
        <div>
          <div class="dataset-header">
            <div style="display: flex;align-items: center;">
              <span class="dataset-header-left">{{
                $t('sdp.views.dataSet')
              }}</span>
            </div>
            <div class="dataset-header-right">
              <i
                v-if="drillSettings.dataSetId"
                class="icon-sdp-yulan delete-icon"
                @click="openDataDetailPreview(drillSettings.dataSetId)"
              ></i>
              <i
                v-if="configs && configs.type === 'template'"
                style="margin-left: 10px"
                class="icon-sdp-yinyongshujuji"
                @click="openLinkDatasetDialog"
              ></i>
            </div>
          </div>
          <div class="select-data" v-if="drillSettings">
            <ChartGuidePopover
              :content="$t('sdp.guide.addChartDataset')"
              :value.sync="isShowDatasetTips"
              :markPaddingLeft="0"
              :markPaddingRight="0"
              :markPaddingTop="0"
              :markPaddingBottom="0"
              :arrowOffsetX="-20"
              :tipsOffsetY="-20"
            >
              <ChartSelect v-model="drillSettings.dataSetId" @change="getSelections('point')" :placeholder="$t('sdp.views.plsDataSet')" :loading="datasetLoading" :class="{ 'hide-text': datasetLoading }" style="width: 100%">
                <span slot="prefix"  class="indicator-select-prefix" v-if="drillSettings.indexFlag && drillSettings.dataSetId">
                    <i class="indicator-icon" :class="drillSettings.indexType===2 ? 'icon-sdp-paishengzhibiao':'icon-sdp-fuhezhibiao'"></i>
                </span>

                <el-option
                  v-for="item in datasetArrayfilter"
                  :key="item.id"
                  :label="item.labeName"
                  :disabled="item.enableFlag==='0'"
                  :value="item.id">
                  <span>
                    <i v-if="item.indexFlag" class="indicator-icon" style="font-size: 16px;" :class="item.indexType===2 ? 'icon-sdp-paishengzhibiao':'icon-sdp-fuhezhibiao'"></i>
                    {{ item.labeName }}
                  </span>
                </el-option>
              </ChartSelect>
            </ChartGuidePopover>
          </div>
        </div>
        <!-- 拖拽字段 -->
        <div
          style="overflow-y: auto"
          :style="{
            height: `calc(100% - 60px)`
          }"
          v-loading="datasetLoading"
          element-loading-spinner="sdp-loading-gif"
        >
          <!-- 数据集 -->
          <DatasetContent
            v-if="selectedDataset && selectedDataset.id"
            ref="DatasetContent"
            :highLightItem="selectedDataset"
            :dragParam="dragParam"
            :needDrag="true"
            :elementType="elementType"
            :needResizing="'middle'"
            @clickField="fieldClickHandler"
          ></DatasetContent>
        </div>
      </div>
      <div v-show="drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag" v-if="!onlyIndex && isShowSourceDataSwitch" :style="{
        height: `calc(100% - 62px)`
      }">
        <div style="position: relative; padding-bottom: 12px;">
          <div class="select-tree">
            <span :class="[drillSettings.indexFlagIds.length ? 'like-placeholder' : 'like-placeholders']">{{indexFlagText}}</span>
            <i :class="['el-icon-arrow-down', 'like-input', isShowSelectHide ? 'is-reverse':'']"></i>
          </div>
          <ChartSelect @visible-change="(val) => {
            isShowSelectHide = val
            if (val) return

            const isCommon = $_sameArray(drillSettings.indexFlagIds, indexFlagIds)
            oldDatasetArrayFlag = showDatasetArrayFlag
            drillSettings.indexFlagIds = [...indexFlagIds]
            changeIndexFlag(isCommon)
          }" v-model="indexFlagIds" multiple :placeholder="$t('sdp.views.plsDataSet')" collapse-tags
          style="opacity: 0;width: 100%">
            <el-option
              v-for="item in datasetArrayFlag"
              :key="item.id"
              :label="item.labeName"
              :disabled="item.enableFlag==='0'"
              :value="item.id">
              <span style="padding-right: 15px;">{{ item.labeName }}</span>
            </el-option>
          </ChartSelect>
        </div>
        <!-- <div class="pt-12 font-style" v-if="drillSettings.indexFlagIds.length">{{$t('sdp.views.Index')}}</div> -->
        <div style="overflow-x: hidden;overflow-y: auto; flex-shrink: 0; height: 100px; transform: translateX(-16px); width: calc(100% + 32px); margin-bottom: 4px;" v-if="drillSettings.indexFlagIds.length">
          <component :is="'draggable'"
            v-model="showDatasetArrayFlag"
            :options="{
              group: {
                name: 'dataset-metric',
                pull:'clone',
                put: false,
              },
              sort: false,
            }"
           >
            <div
               v-for="(datasetItem) in showDatasetArrayFlag"  @click="() => {
                if (drillSettings.dataSetId === datasetItem.id) return

                indexClick(datasetItem)
               }"
              :key="(datasetItem.alias || datasetItem.labeName) + datasetItem.id"
              class="dataset-item"
              :style="{
                width: style.width + 'px',
              }"
              :class="{
                active: selectedDataset.id === datasetItem.id
              }"
            >
                <span class="dataset-name" :title="datasetItem.labeName" v-sdp-dataset-drag="{ ...datasetItem, fieldType: 'indicator' }">
                  <i class="indicator-icon" :class="{
                      1: 'icon-sdp-paishengzhibiao',
                      2: 'icon-sdp-fuhezhibiao',
                      0: 'icon-sdp-yuanzizhibiao'
                    }[datasetItem.indexType]"></i>
                  <span>{{ datasetItem.labeName }}</span>
                </span>
                <div class="dataset-oprate">
                  <i class="icon-sdp-yulan" :class="{ disabledIcon: datasetItem.enableFlag === '0' }" @click="openDataDetailPreview(datasetItem.id)"></i>
                  <i class="el-icon-delete-solid" @click="handleDeleteDataset(datasetItem.id, datasetItem.labeName)"></i>
                </div>
            </div>
          </component>
        </div>
        <!-- 拖拽字段 -->
        <div
          style="overflow-y: auto"
          :style="{
            height: `calc(100% - 144px)`
          }"
          v-loading="datasetLoading"
          element-loading-spinner="sdp-loading-gif"
        >
          <!-- 数据集 -->
          <DatasetContent
            v-if="selectedDataset && selectedDataset.id"
            ref="IndexFlagContent"
            :highLightItem="selectedDataset"
            :dragParam="{
              topHeight: 66,
              middleHeight: 200,
              bottomHeight: 200,
              startY: 0,
              startTopHeight: 0,
              startMiddleHeight: 0,
              startBottomHeight: 0,
              resizing: null,
              topMinHeight: 206,
              middleMinHeight: 100
            }"
            :needDrag="true"
            :elementType="elementType"
            :needResizing="'middle'"
          ></DatasetContent>
        </div>
      </div>
      <div v-show="drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag" v-if="onlyIndex" :style="{
        height: `calc(100% - 0px)`
      }">
        <div style="position: relative; padding-bottom: 12px;">
          <div class="dataset-header">
            <div style="display: flex;align-items: center;">
              <span class="dataset-header-left">{{
                $t('sdp.views.Index')
              }}</span>
              <el-tooltip v-if="showDatasetArrayFlag.length > 1" :content="$t('sdp.simSystem.multipleIndexTips')" placement="right">
                <i class="icon-sdp-info icon-indicator-tips"></i>
              </el-tooltip>
            </div>
            <div class="dataset-header-right">
              <i
                v-if="configs && configs.type === 'template'"
                style="margin-left: 10px"
                class="icon-sdp-yinyongshujuji"
                @click="openLinkDatasetDialog"
              ></i>
            </div>
          </div>
        </div>
        <!-- <div class="pt-12 font-style" v-if="drillSettings.indexFlagIds.length">{{$t('sdp.views.Index')}}</div> -->
        <div style="overflow-x: hidden;overflow-y: auto; flex-shrink: 0; height: 100px; transform: translateX(-16px); width: calc(100% + 32px); margin-bottom: 4px;" v-if="drillSettings.indexFlagIds.length">
          <component :is="'draggable'"
            v-model="showDatasetArrayFlag"
            :options="{
              group: {
                name: 'dataset-metric',
                pull:'clone',
                put: false,
              },
              sort: false,
            }"
           >
            <div
               v-for="(datasetItem) in showDatasetArrayFlag"  @click="() => {
                if (drillSettings.dataSetId === datasetItem.id) return

                indexClick(datasetItem)
               }"
              :key="(datasetItem.alias || datasetItem.labeName) + datasetItem.id"
              class="dataset-item"
              :style="{
                width: style.width + 'px',
              }"
              :class="{
                active: selectedDataset.id === datasetItem.id
              }"
            >
                <span class="dataset-name" :title="datasetItem.labeName" v-sdp-dataset-drag="{ ...datasetItem, fieldType: 'indicator' }">
                  <i class="indicator-icon" :class="{
                      1: 'icon-sdp-paishengzhibiao',
                      2: 'icon-sdp-fuhezhibiao',
                      0: 'icon-sdp-yuanzizhibiao'
                    }[datasetItem.indexType]"></i>
                  <span>{{ datasetItem.labeName }}</span>
                </span>
                <div class="dataset-oprate">
                  <i class="icon-sdp-yulan" :class="{ disabledIcon: datasetItem.enableFlag === '0' }" @click="openDataDetailPreview(datasetItem.id)"></i>
                  <i class="el-icon-delete-solid" @click="handleDeleteDataset(datasetItem.id, datasetItem.labeName)"></i>
                </div>
            </div>
          </component>
        </div>
        <!-- 拖拽字段 -->
        <div
          style="overflow-y: auto"
          :style="{
            height: `calc(100% - 144px)`
          }"
          v-loading="datasetLoading"
          element-loading-spinner="sdp-loading-gif"
        >
          <!-- 数据集 -->
          <DatasetContent
            v-if="selectedDataset && selectedDataset.id"
            ref="IndexFlagContent"
            :highLightItem="selectedDataset"
            :dragParam="{
              topHeight: 66,
              middleHeight: 200,
              bottomHeight: 200,
              startY: 0,
              startTopHeight: 0,
              startMiddleHeight: 0,
              startBottomHeight: 0,
              resizing: null,
              topMinHeight: 206,
              middleMinHeight: 100
            }"
            :needDrag="true"
            :elementType="elementType"
            :needResizing="'middle'"
          ></DatasetContent>
        </div>
      </div>
    </div>
    <!-- 隐藏数据集按钮 -->
    <div :class="isDisplayStyle" @click="changeDataSetDisplay">
      <i :class="displayIcon"></i>
    </div>
    <DataSetPreview
      :dataSets="dataSets"
      :visible.sync="dialogVisible"
    />
    <DataSetIndicatorPreview
      :dataSets="dataSets"
      :visible.sync="dataSetIndicatorPreviewShow"
    />
  </div>
</template>

<script>
import { substring15 } from 'packages/base/board/displayPanel/utils';
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin';
import DataSetPreview from 'packages/base/common/dataSet/dialogComponents/dataSetPreview';
import DatasetContent from 'packages/base/common/dataSet/datasetContent.vue';
import ChartSelect from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/chart-select';
import { ChartGuidePopover } from 'packages/base/KanbanGuide/components';
import DataSetIndicatorPreview from 'packages/base/common/dataSet/dialogComponents/dataSetIndicatorPreview'
import { kanbanGuideStepEntryAddDatasetsMixin } from "packages/base/KanbanGuide";
import SourceDataSwitch from './sourceDataSwitch.vue';
import { SOURCE_DATA_TYPE } from '../../boardElements/elementChart/constant';
import sdpDatasetDrag from 'packages/base/common/dataSet/directives/index.ts'
import { SIM_TYPE } from 'packages/base/common/referenceDataSet/constants'
import { EVENT_BUS, EXTERNAL_CALL_TYPE } from 'packages/base/board/displayPanel/constants'
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin'
import { MicroApp } from '../../../../../../../src/pages/elementTemplate/utils.ts'

const DATASET_MIN_WIDTH = 232
export default {
  inject: {
    configs: { default: () => false },
    getDictAlias: { default: () => () => {} },
    utils: { default: () => ({}) },
    sdpBus: { default: () => ({}) }
  },
  directives: { sdpDatasetDrag },
  mixins: [kanbanGuideStepEntryAddDatasetsMixin, datasetMixin],
  components: {
    DataSetPreview,
    DataSetIndicatorPreview,
    DatasetContent,
    ChartSelect,
    ChartGuidePopover,
    SourceDataSwitch
  },
  props: {
    drillSettings: {
      type: Object,
      default: () => {}
    },
    datasetLoading: {
      type: Boolean,
      default: true
    },
    datasetArray: {
      type: Array,
      default: () => []
    },
    elementType: {
      type: [String, Number],
      default: '2'
    }
  },
  computed: {
    isSbi() {
      return this.utils.env?.projectName === ALL_PROJECT_NAME.SBI
    },
    datasetIsDone() {
      return !!this.drillSettings.dataSetId;
    },
    datasetIsDoing() {
      let flag = (this.utils.kanbanGuide?.chartList || []).length === (this.utils.kanbanGuide?.invalidChartList || []).length
      return !this.drillSettings.dataSetId && this.utils?.kanbanGuide?.isVisible
    },

    displayIcon() {
      return this.isDisplayDataSet
        ? 'el-icon-arrow-left'
        : 'el-icon-arrow-right';
    },
    isDisplayStyle() {
      return [
        'displayIcon',
        this.isDisplayDataSet ? 'displayDataSet' : 'hiddenDataSet'
      ];
    },
    isChart() {
      return +this.elementType === 2
    },
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs.options?.onlyIndex
    },
    isShowSourceDataSwitch() {
      return this.isChart && this.isSbi
    },
    datasetArrayfilter() {
      return this.isShowSourceDataSwitch ? this.datasetArray.filter(e => !e.indexFlag) : this.datasetArray
    },
    datasetArrayFlag() {
      return this.datasetArray.filter(e => e.indexFlag)
    },
    showDatasetArrayFlag() {
      return this.datasetArrayFlag.filter(item => this.drillSettings.indexFlagIds.includes(item.id))
    },
    indexFlagText() {
      const len = this.drillSettings.indexFlagIds.length
      if (len === 0) {
        return this.$t('sdp.placeholder.pleaseChoose')
      }
      if (len === 1) {
        const item = this.datasetArrayFlag.find(item => item.id === this.drillSettings.indexFlagIds[0])
        return `${item.labeName}`
      }
      return this.$t('sdp.views.multiple')
    },
  },

  data() {
    return {
      isSim: SIM_TYPE.SIM,
      SOURCE_DATA_TYPE,
      indexFlagIds: [],
      isShowSelectHide: false,
      isDisplayDataSet: true,
      datasetProps: [],
      dialogVisible: false,
      dataSetIndicatorPreviewShow: false,
      dataSets: {},
      selectedDataset: {},

      domCreate: false,
      isShowDatasetTips: false, // 操作指引
      style: {
        width: DATASET_MIN_WIDTH,
        zIndex: 2023, // 高于加载层 2000 低于选择器层 2048 今年 2023 那就用 2023 吧
        position: 'relative'
      },
      DATASET_MIN_WIDTH,
      dragParam: {
        topHeight: 66,
        middleHeight: 200,
        bottomHeight: 200,
        startY: 0,
        startTopHeight: 0,
        startMiddleHeight: 0,
        startBottomHeight: 0,
        resizing: null,
        topMinHeight: 60,
        middleMinHeight: 100
      },
      oldDatasetArrayFlag: [],
      isFirstDatasetArrayWatch: true,
      microAppClass: null
    };
  },
  created() {
    if (this.onlyIndex) {
      this.microAppClass = new MicroApp(()=>{})
      this.$set(this.drillSettings, 'sourceDataType', 2)
    }

    if (this.drillSettings.indexFlagIds === undefined) {
      this.$set(this.drillSettings, 'indexFlagIds', [])
    }
    if (this.drillSettings.sourceDataType === undefined) {
      this.$set(this.drillSettings, 'sourceDataType', SOURCE_DATA_TYPE.dataset)
    }
    this.indexFlagIds = [...this.drillSettings.indexFlagIds]
    this.$nextTick(() =>{
      this.oldDatasetArrayFlag = (this.datasetArray || []).filter(e => e?.indexFlag).filter(item => (this.drillSettings?.indexFlagIds || []).includes(item?.id))
    })
  },
  watch: {
    datasetIsDoing: {
      handler: function(value) {
        setTimeout(() => {
          // 如果同时添加了多个元素，第一个元素完成了配置元素内容，则编辑第二个元素时，默认关闭操作指引面板
          let flag = (this.utils.kanbanGuide?.chartList || []).length === (this.utils.kanbanGuide?.invalidChartList || []).length
          this.isShowDatasetTips = value && flag
        }, 500)
      },
      immediate: true // 首次加载的时候执行函数
    },
    isShowSourceDataSwitch: {
      handler(val) {
        val && (this.dragParam.topHeight = 122)
      },
      immediate: true
    },
    datasetIsDone: {
      handler: function(value) {
        value && (this.isShowDatasetTips = false);
      }
    },
    datasetArray: {
      handler(newVal, oldVal) {
        if (this.onlyIndex && JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
          const { options } = this.configs
          let _arr
          if(this.isFirstDatasetArrayWatch && ((options.operateType === 'add' && oldVal === undefined) || (options.operateType === 'edit' && oldVal !== undefined))) {
            _arr = newVal
            this.isFirstDatasetArrayWatch = false
          } else {
            _arr = oldVal
          }
          this.indexFlagIds = this.drillSettings?.indexFlagIds?.length > 0 ? [...this.drillSettings.indexFlagIds] : []
          this.$nextTick(() =>{
            this.oldDatasetArrayFlag = _arr && _arr.length ? (_arr || []).filter(e => e?.indexFlag).filter(item => (this.drillSettings?.indexFlagIds || []).includes(item?.id)) : []
            this.changeIndexFlag(false, false)
          })
        }
      },
      deep:true,
      immediate: true
    }
  },

  methods: {
    // SIM 自助分析，共有维度字段判断逻辑
    isFieldEqual(a, b) {
      if (a.labeName !== b.labeName) return false
      if (this.onlyIndex) {
        // 类型必须一致
        if (a.columnTpe !== b.columnTpe) return false
        // 业务日期字段必须都为true,并且 timeType必须一致 才算共有
        if (a.calendarColumn || b.calendarColumn) {
          if (!(a.calendarColumn && b.calendarColumn) || !(a.timeType === b.timeType)) return false
        }
      }
      return true
    },
    getCommonAndIndependent(selectedDataset) {
      const indexFields = this.showDatasetArrayFlag.map(item => {
        const data = item.children.find(item1 => item1.labeName.toLocaleUpperCase().startsWith('INDEX_NUM'))
        return {
          ...data,
          isFromIndex: true,
          indexName: item.labeName,
          indexId: item.indexId,
          dataSetId: data.parentId // 指标字段后端需要用到 dataSetId
        }
      })
      if (!selectedDataset) {
        selectedDataset = this.showDatasetArrayFlag.find(item => item.id === this.drillSettings.dataSetId)
      }
      if (this.showDatasetArrayFlag.length === 1) {
        return {
          common: [],
          independent: selectedDataset.children?.filter(item => !item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM')) || [],
          indexFields
        }
      }
      const first = this.showDatasetArrayFlag[0]

      let labeNames = first?.children.map(item => item.labeName)

      this.showDatasetArrayFlag.forEach((item, index) => {
        if (index) {
          labeNames = item.children.filter(item => labeNames.includes(item.labeName)).map(item => item.labeName)
        }
      })

      let common = []
      let independent = []
      if (this.onlyIndex) {
        let commonFields = first?.children || []
        this.showDatasetArrayFlag.forEach((item, index) => {
          if (index === 0) return
          commonFields = commonFields.filter(fieldA =>
            item.children.some(fieldB => this.isFieldEqual(fieldA, fieldB))
          )
        })
        common = selectedDataset?.children?.filter(item => commonFields.some(item1 => this.isFieldEqual(item, item1)) && !item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM')) || []
        independent = selectedDataset?.children?.filter(item => !commonFields.some(item1 => this.isFieldEqual(item, item1)) && !item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM')) || []
      } else {
        common = selectedDataset?.children?.filter(item => labeNames.includes(item.labeName) && !item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM')) || []
        independent = selectedDataset?.children?.filter(item => !labeNames.includes(item.labeName) && !item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM')) || []
      }
      return {
        common,
        independent,
        indexFields
      }
    },
    compareArrays(oldArr, newArr) {
      const oldIds = new Set(oldArr.map(item => item.id))
      const newIds = new Set(newArr.map(item => item.id))

      // 删除的数据
      let _deletedItems = oldArr.filter(item => !newIds.has(item.id))
      // 新增的数据
      let _addedItems = newArr.filter(item => !oldIds.has(item.id))
      return {
        deletedItems: _deletedItems || [],
        addedItems: _addedItems || []
      }
    },
    changeIndexFlag(isCommon, isGetSelectionsList) {
      const len = this.drillSettings.indexFlagIds.length

      if (len) {
        const isHas = this.drillSettings.indexFlagIds.includes(this.selectedDataset?.id)
        let item = this.showDatasetArrayFlag[0]

        if (isHas) {
          let data = this.showDatasetArrayFlag.find(item => item.id === this.selectedDataset?.id)
          if (data === item && isCommon) return
          item = data
        }
        // this.datasetClick(item)
        let _compareObj = this.compareArrays(this.oldDatasetArrayFlag, this.showDatasetArrayFlag)
        this.indexClick(item)
        this.$nextTick(() => {this.oldDatasetArrayFlag = JSON.parse(JSON.stringify((this.showDatasetArrayFlag)))})
        this.$emit('on-change-index', _compareObj, this.selectedDataset?.id, isGetSelectionsList)
      } else {
        this.datasetClick({ id: '', children: [] })
      }
    },
    datasetClick(datasetItem) {
      this.selectedDataset = datasetItem
      this.drillSettings.dataSetId = datasetItem.id
      this.getSelections('point')
    },
    // 点击切换指标只需要更新独立维度
    indexClick(datasetItem) {
      this.selectedDataset = datasetItem
      this.drillSettings.dataSetId = datasetItem.id
      this.setList(this.datasetProps, 'point')
    },
    handleDeleteDataset(id, name) {
      this.$sdp_eng_confirm(this.$t('sdp.message.deleteHint', {prop: name}), this.$t('sdp.dialog.hint'), {
        confirmButtonText: this.$t('sdp.button.ensure'),
        cancelButtonText: this.$t('sdp.button.cancel'),
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'theme-messagebox sdp-dialog',
        type: 'warning',
      }).then(() => {
        const index = this.drillSettings.indexFlagIds.findIndex(item => item === id)
        this.drillSettings.indexFlagIds.splice(index, 1)
        this.indexFlagIds = [...this.drillSettings.indexFlagIds]
        this.changeIndexFlag()
      })
    },
    resetWidth() {
      this.style.width = DATASET_MIN_WIDTH
    },
    substring15,
    drag (e) {
      this.style.position = 'relative' // 改为改变宽度影响右边，如果需要浮起改变宽度不影响右边，这里改成 'absolute' 即可
      const left = e.target.parentNode
      const leftW = left.offsetWidth
      const startX = e.clientX
      const _this = this
      document.onmousemove = (e) => {
        this.closeDurativeWithAddDatasets()
        e.preventDefault()
        const distX = e.clientX - startX
        _this.style.width = leftW + distX
        if (_this.style.width <= DATASET_MIN_WIDTH) {
          _this.style.width = DATASET_MIN_WIDTH
        }
        if (_this.style.width >= 600) {
          _this.style.width = 600
        }
      }
      document.onmouseup = () => {
        if (_this.style.width <= DATASET_MIN_WIDTH) {
          this.style.position = 'relative'
        }
        document.onmousemove = null
      }
    },
    getPropIcon(propItem) {
      if (propItem.columnTpe === 'number') return 'icon-sdp-Numerical';
      if (propItem.columnTpe === 'date') return 'icon-sdp-Calendar';
      return 'icon-sdp-Fonttype';
    },
    getPropLabel(propItem, labelTrim = 1, commentTrim = 0) {
      // 0表示不用显示，1表示要显示全名，2表示显示钱15个字符
      let label =
        labelTrim > 0
          ? this.getUnknownName(propItem.parentId, propItem.labeName)
          : '';
      if (labelTrim > 1) label = substring15(label);
      let comment =
        commentTrim > 1 ? substring15(propItem.comment) : propItem.comment;
      if (commentTrim > 0 && comment) label = `${label} (${comment})`;
      return label || '';
    },
    fieldClickHandler(datasetItem) {
      const labelName = this.getPropLabel(datasetItem, 1, 2);
      const tagName = datasetItem.tagName ? `(${datasetItem.tagName})` : '';
      this.$emit('clickField', datasetItem, `${labelName} ${tagName}`);
    },
    createDatasetField(datasetItem) {
      const labelName = this.getPropLabel(datasetItem, 1, 2);
      const tagName = datasetItem.tagName ? `(${datasetItem.tagName})` : '';
      return `${labelName} ${tagName}`
    },
    openLinkDatasetDialog() {
      this.$emit('open-link-dataset')
    },
    getSelections(type) {
      this.$emit('on-change-dataset', type)
      const refEl = this.drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag ? this.$refs.IndexFlagContent : this.$refs.DatasetContent
      if (!refEl) return
      refEl.reset({ id: '', children: [] })
      // this.resetHeights()
      setTimeout(() => {
        refEl.updateHeights()
      }, 500)
    },
    // resetHeights() {
    //   this.dragParam = {
    //     topHeight: 66,
    //     middleHeight: 200,
    //     bottomHeight: 200,
    //     startY: 0,
    //     startTopHeight: 0,
    //     startMiddleHeight: 0,
    //     startBottomHeight: 0,
    //     resizing: null,
    //     topMinHeight: 60,
    //     middleMinHeight: 100
    //   }
    // },
    setList(paramList, type = '') {
      this.datasetProps = paramList
      if (this.drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag) {
        const datasetItem = this.showDatasetArrayFlag.find(item => item.id === this.drillSettings.dataSetId)

        this.selectedDataset = {
          ...datasetItem,
          isMoreIndex: this.showDatasetArrayFlag.length > 1,
          commonAndIndependent: this.getCommonAndIndependent(datasetItem)
        }
      } else {
        // 此结构不满足，需要额外加上isIndicator跟labelName
        this.selectedDataset = { id: this.drillSettings.dataSetId, children: paramList, indexFlag: this.drillSettings?.indexFlag, indexType: this.drillSettings?.indexType, labeName: this.drillSettings?.dataSetLabelName }
      }
      const refEl = this.drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag ? this.$refs.IndexFlagContent : this.$refs.DatasetContent
      refEl && refEl.reset(this.selectedDataset)

      if (type === 'point') {
        this.$parent?.replaceJson?.()
      }
    },
    changeDataSetDisplay() {
      this.isDisplayDataSet = !this.isDisplayDataSet
    },
    // 数据集具体数据查询
    openDataDetailPreview(dataSetId) {
      if (!dataSetId) return
      let data = this.datasetArray.find(item => item.id === dataSetId)
      if (data.enableFlag === '0') return void ''

      Object.assign(this.dataSets, { 'activeObj': data })
      if (this.onlyIndex) {
        this.microAppClass.forceDispatch({ type: 'showIndicatorDetail', ...this.dataSets, ...data })
        return
      }
      if (this.drillSettings?.indexFlag) {
        if (SIM_TYPE.isSim) {
          this.sdpBus.$emit(EVENT_BUS.EXTERNAL_CALL, {
            type: EXTERNAL_CALL_TYPE.INDICATOR_RREVIEW,
            data: {
              id: data.indexId
            }
          })
        } else {
          this.dataSetIndicatorPreviewShow = true
        }
      } else {
        this.dialogVisible = true
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.icon-indicator-tips {
  font-weight: normal;
  font-size: 18px;
  color: var(--sdp-zs);
  cursor: pointer;
}
.dataset-drag-wrap {
  position: relative;
}
.dataset-drag-placeholder {
  height: 0;
}
.dataset-drag {
  .dataset-drag-bar {
    content: '';
    width: 10px;
    height: 100%;
    position: absolute;
    top: 0;
    right: -10px;
    bottom: 0;
    cursor: col-resize;
  }
}
.dataset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 700;
  .icon-sdp-yulan {
    color: var(--sdp-zs) !important;
    font-size: 18px;
    cursor: pointer;
  }
  .dataset-header-right {
    i {
      cursor: pointer;
      font-weight: normal;
      color: var(--sdp-is);
      &:hover {
        color: var(--sdp-zs);
      }
    }
  }
}
.dataset {
  height: 100%;
  width: 232px;
  background: #f7f7f7;
  padding: 16px;
  // overflow-x: scroll;
  // overflow-y: auto;
  box-shadow: 2px 2px 2px 0 rgba(0, 0, 0, 0.1);
  color: var(--sdp-cszjsz-wzs1);
  background-color: var(--sdp-zcsjj-bj);

  .select-data /deep/ {
    margin-top: 6px;
    .hide-text .el-input:not(.is-disabled) .el-input__inner {
      color: transparent !important;
    }
  }
  .indicator-select-prefix{
    display: flex;align-items: center;height:100%;
    .indicator-icon{
      font-size:16px;color:var(--sdp-zs);vertical-align: bottom;
    }
  }
}
.displayIcon {
  height: 60px;
  box-shadow: -6px 6px 6px 0 rgba(0, 0, 0, 0.1);
  // background-color: var(--sdp-cy-bjs);
  z-index: 2024;
  right: -16px;
  position: absolute;
  top: 50%;
  cursor: pointer;
  background: var(--sdp-gjys-bjs);
  i {
    line-height: 60px;
  }
}
.displayDataSet {
  width: 16px;
}
.hiddenDataSet {
  width: 16px;
  z-index: 100;
}
.select-tree {
  border:  solid 1px var(--input-border-color);
  background-color: var(--input-background-color);
  position: absolute;
  width: 100%;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
  border-radius: 2px;

  .like-placeholders{
    color: var(--sdp-ycsz-srtswz);
  }
  .like-placeholders, .like-placeholder {
    float: left;
    margin-left: 10px;
    font-size: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 80%;
  }
  .like-placeholder{
    color: var(--input-color);
  }
  .like-input {
    float: right;
    margin-right: 8px;
    font-size: 14px;
    margin-top: 6px;
    color: #ccc;
    transition: transform .3s;
    transform: rotateZ(0);
  }

  .is-reverse{
    transform: rotateZ(180deg);
  }
}
.dataset-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  font-size: 12px;
  color: var(--sdp-cszjsz-wzs1);
  &:not(.disable):hover, &:not(.disable).active {
    background-color: var(--sdp-sjj-hs);
    color: var(--sdp-zs);
  }
  .dataset-name{
    @include ellipsis;
    >span{
      font-size: 14px;
    }
    .indicator-icon{
      font-size:16px;color:var(--sdp-zs);vertical-align: bottom;margin-right: 3px;
    }
    .cited-data-icon {
      color: var(--sdp-jys);
      font-size: 14px;
      margin-right: 6px;
    }
    .cited-data-active-icon {
      color: var(--sdp-zs);
    }
  }
  &:not(.disable):hover .dataset-oprate {
    display: flex;
  }
  .dataset-oprate{
    flex-shrink: 0;
    height: 100%;
    line-height: 30px;
    justify-content: space-between;
    align-items: center;
    display: none;
    .is-card {
      margin-right: 0;
    }
    i{
      font-size: 12px;
      cursor: pointer;
    }
  }
  .icon-sdp-yulan{
    margin-right: 12px;
    color: var(--sdp-zs);
  }
  .el-icon-delete-solid{
    color: var(--sdp-qcgls);
  }
  &:not(.disable).dataset-high-light {
    color: var(--sdp-zs) !important;
  }
  &.disabled{
    cursor: not-allowed;
    .dataset-name{
      color: var(--sdp-jys)
    }
  }
  .disabledIcon {
    cursor: not-allowed !important;
    color: var(--sdp-jys)
  }
}
.font-style {
  color: var(--sdp-title-color, #111);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.pt-12 {
  padding-bottom: 12px;
}
</style>
