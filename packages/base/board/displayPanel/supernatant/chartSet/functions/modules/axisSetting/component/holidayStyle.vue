<template>
  <!-- 事件日历样式 -->
  <div v-if="componentShow" class="chart-holidayStyle-container">
    <TableSwitch not-need-switch :label="$t('sdp.views.EventsLabel')"></TableSwitch>
    <FontSetting
      :fontStyle="holidayStyle"
      type="font"
      :themeType="themeType"
      default="holidayStyle.color"
      @change="holidayStyleHandler"
    ></FontSetting>
  </div>
</template>

<script>
import { config } from '@vue/test-utils'
import { isReverseAxisChart } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/chartSetting/handleChartSetting'
import {
  HOLIDAY_STYLE_CHART,
  CHART_ALIAS_TYPE,
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'

export default {
  mixins: [componentMixin],
  name: 'HolidayStyle',
  inject: {
    utils: { default: {} },
    configs: { default: () => false }
  },
  props: {
    axisType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  computed: {
    // 根据图形和坐标判断是否显示
    componentShow() {
      if (this.$getSystemConfig?.('onlyIndex')) return false
      // 次轴不显示
      if (this.axisType === 'yAxis-1') return false
      // 不在列表不显示
      if (!HOLIDAY_STYLE_CHART.includes(this.chartUserConfig.chartAlias)) return false
      // 树图展示在功能设置中
      if (this.axisType === '' && [CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_TREE].includes(this.chartUserConfig.chartAlias)) {
        return true
      }
      // 条形图展示在 Y 轴
      if ([CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK].includes(this.chartUserConfig.chartAlias)) {
        // 显示在 Y 轴
        return this.axisType === 'yAxis-0'
      } else {
        // 显示在 X轴
        return this.axisType === 'xAxis'
      }
    },
    // 字体保存位置
    holidayStyle() {
      return this.chartUserConfig.holidayStyle
    },
  },

  methods: {
    holidayStyleHandler(val) {
      this.$emit('change', this.holidayStyle)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
