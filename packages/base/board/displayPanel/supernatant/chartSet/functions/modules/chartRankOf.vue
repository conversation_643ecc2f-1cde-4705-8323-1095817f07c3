<template>
  <!-- 行数限制/条数限制 -->
  <div v-if="componentShow" class="chart-rankof-container">
    <TableSwitch
      :disabled="isDisabledTableSwitch"
      :formData="chartUserConfig.pagination"
      prop="rankOf"
      @change="rankOfHandler"
      :label="$t('sdp.views.rowsLimit')"
      style="margin-top: 0;"
    ></TableSwitch>
    <div class="item-config-container mt-4">
      <ChartSelect
        v-model="chartUserConfig.pagination.rankType"
        :disabled="!chartUserConfig.pagination.rankOf || getRankTypeList().length <= 1"
        style="flex: 1; height: 32px;"
        @change="rankTypeHandler"
      >
        <el-option v-for="item in getRankTypeList()" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </ChartSelect>
      <el-input-number
        :disabled="!chartUserConfig.pagination.rankOf"
        v-model="chartUserConfig.pagination.pageSize"
        :min="1"
        :max="10000"
        step-strictly
        controls-position="right"
        @change="pageSizeHandler"
      ></el-input-number>
    </div>
  </div>
</template>

<script>
import { MOBILE_PAGE_CHART, VE_LIQUIDFILL_MODE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'
import element_showhide_mixins from 'packages/base/board/displayPanel/supernatant/chartSet/components/element_showhide_mixins'

export default {
  mixins: [componentMixin, element_showhide_mixins],
  name: 'ChartRankOf',
  inject: {
    utils: { default: {} },
  },
  data() {
    return {}
  },
  computed: {
    componentShow() {
      if (this.chartUserConfig.chartAlias === 've-liquidfill') {
        return this.chartUserConfig.liquidFillSetting?.mode === VE_LIQUIDFILL_MODE.DIMENSION
      }
      return this.chartUserConfig.chartAlias !== 've-gauge-normal'
    },
    // 移动端需要分页展示的图形
    isMobilePageChart() {
      return this.utils.isMobile && MOBILE_PAGE_CHART.includes(this.chartUserConfig.chartAlias)
    },
    dimensionsLength() {
      // 如果返回的不是字符串，dimensionList和extendDimensionList的任何改变都会重新计算，会触发watch
      const { extendDimensionList = [] } = this.chartUserConfig
      return `${ this.dimensionList.length }-${ extendDimensionList.length }`
    },
    isDisabledTableSwitch() {
      return this.chartUserConfig?.gridSetting?.animationOption?.isOptionAnimation
    }
  },
  methods: {
    getRankTypeList() {
      const { extendDimensionList = [] } = this.chartUserConfig
      const [dimensionLength, extendDimensionLength] = [this.dimensionList.length, extendDimensionList.length]
      if(this.chartUserConfig.chartAlias === 've-bar-Heatmap') {
        const typeArr= [
          { value: 'lastDimension', label: `X${this.$t('sdp.views.axisDimension')}`},
          { value: 'firstDimension', label: `Y${this.$t('sdp.views.axisDimension')}` },
        ]
        return typeArr
      }
      const typeMap = {
        type1: { value: 'firstDimension', label: this.$t('sdp.views.firstDimension') },
        type2: { value: 'lastDimension', label: this.$t('sdp.views.childDimension') },
        type3: { value: 'extendDimension', label: this.$t('sdp.views.extendDimension') },
      }
      let resultList = []
      if (dimensionLength > 1 && extendDimensionLength) {
        resultList = [typeMap.type1, typeMap.type2, typeMap.type3]
      } else if (dimensionLength === 1 && extendDimensionLength) {
        resultList = [typeMap.type1, typeMap.type3]
      } else if (dimensionLength > 1) {
        resultList = [typeMap.type1, typeMap.type2]
      } else {
        resultList = [typeMap.type1]
      }
      // 隐藏扩展维度跟子维度
      resultList = resultList.filter(item => {
        const getCfg = this.$getFeatureConfig?.bind(this)
        const isHideExtend = getCfg && getCfg('extendDimensionRow.hidden')
        const isHideLast = getCfg && getCfg('lastDimension.hidden')

        return !(
          (isHideExtend && item.value === 'extendDimension') ||
          (isHideLast && item.value === 'lastDimension')
        )
      })
      return resultList
    },
    rankOfHandler(val) {
      this.setDrillSettings()
      this.eventEmitHandler('DATA_AMOUNT_RANK', val)
    },
    rankTypeHandler(val) {
      this.setDrillSettings()
      this.eventEmitHandler('PAGINATION_RANK_TYPE')
    },
    pageSizeHandler(val) {
      if (!val) {
        val = 1
      }
      this.$set(this.chartUserConfig.pagination, 'pageSize', val)
      this.setDrillSettings()
      this.eventEmitHandler('DATA_AMOUNT_PAGESIZE', val)
    },
    setDefault() {
      const rankTypeList = this.getRankTypeList()
      if(this.chartUserConfig.chartAlias === 've-bar-Heatmap') {
        this.$set(this.chartUserConfig.pagination, 'rankType', rankTypeList[0].value)
      } else {
        this.$set(this.chartUserConfig.pagination, 'rankType', rankTypeList[rankTypeList.length - 1].value)
      }
      this.rankTypeHandler(this.chartUserConfig.pagination.rankType)
    },
    setDrillSettings() {
      // 是否支持条数设置
      const rankOf = this.chartUserConfig.pagination.rankOf
      let requestPageSize = rankOf ? this.chartUserConfig.pagination.pageSize : undefined

      if (requestPageSize === true || requestPageSize === undefined || requestPageSize > 100) {
        requestPageSize = (rankOf && requestPageSize < 100)
          ? this.chartUserConfig.pagination.pageSize
          : 100
      }

      if (rankOf) {
        const rankTypeMap = {
          firstDimension: '0',
          lastDimension: '1',
          extendDimension: '2',
        }
        this.element.content.drillSettings.maxCountDimension = rankTypeMap[this.chartUserConfig.pagination.rankType]
      } else {
        delete this.element.content.drillSettings.maxCountDimension
      }
      this.element.content.drillSettings.pageInfo.pageSize = requestPageSize || -1
      const currentPageSize = rankOf ? requestPageSize : null

      if (this.isMobilePageChart) {
        Object.assign(this.element.content.drillSettings.pageInfo, {
          page: 1,
          pageSize: currentPageSize || 100,
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-rankof-container /deep/{
  .item-config-container {
    display: flex;
    gap: 4px;
  }
  .item-config-container .el-input__suffix{
    line-height: 28px;
    height: 28px;
  }
}
</style>
