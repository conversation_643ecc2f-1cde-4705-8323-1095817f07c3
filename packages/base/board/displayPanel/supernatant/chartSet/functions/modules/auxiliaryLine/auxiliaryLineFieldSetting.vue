<template>
  <div class="auxiliary-line-field-setting">
    <ChartSelect v-model="item[metricKey]" @change="handleField($event, item, index)" :disabled="disabled">
      <el-option
        v-for="e in fieldList"
        :key="getDatasetLabel(e)"
        :label="getDatasetLabel(e)"
        :title="getDatasetLabel(e)"
        :value="e.labeName"
      >
        <span>{{getDatasetLabel(e)}}</span>
        <!-- <span v-if="e.comment">{{`   (${substring15(e.comment)})`}}</span> -->
      </el-option>
    </ChartSelect>

    <div class="dis-ib el-input">
      <div class="el-input__inner">
        <DragCascader
          v-if="fieldList.some(e => e.labeName === item[metricKey])"
          :ref="dragcascaderRef"
          :item="item"
          :index="index"
          :type="dragBoxType"
          :disabled="disabled"
          :dragBoxType="dragBoxType"
          :dragList="fieldList.filter(e => e.labeName === item[metricKey])"
          :trimMaxWidth="149"
          :current-edit-data="currentEditData"
          :is-metric="false"
          :is-auxiliary-line-setting="isAuxiliaryLineSetting"
          :is-extend-dimension="false"
          :isTargetLable="false"
          :cascader-unique-key="cascaderUniqueKey"
          @selectMess="dragCascaderSelect($event, index)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { substring15 } from 'packages/base/board/displayPanel/utils'
import { DragCascader, ChartSelect } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import { setLgeTypeValue } from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'
export default {
  mixins: [datasetMixin],
  props: {
    currentEditData: {
      type: Object,
      default: () => ({})
    },
    fieldList: {
      type: Array,
      default: () => []
    },
    dragcascaderRef: {
      type: String,
      default: '',
    },
    item: {
      type: Object,
      default: () => {}
    },
    index: {
      type: Number,
      default: 0,
    },
    auxiliaryLineType: {
      type: String,
      default: 'dimension'
    },
    metricKey: {
      type: String,
      default: 'otherMetricValue',
    },
    // cascaderValue为对象时，需要传入一个当前cascader的唯一key
    cascaderUniqueKey: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    dragBoxType: {
      type: String,
      default: 'auxiliaryLine'
    },
    isAuxiliaryLineSetting: {
      type: Boolean,
      default: false
    },
  },
  components: {
    DragCascader,
    ChartSelect,
  },
  data() {
    return {

    }
  },
  methods: {
    substring15,
    // 删除自定义计算
    deleteCustom(item) {
      Reflect.deleteProperty(item, 'expression')
      Reflect.deleteProperty(item, 'exp')
    },
    // 删除对比
    deleteContrast(item) {
      Reflect.deleteProperty(item, 'selectedConttast')
      Reflect.deleteProperty(item, 'selectedConttastMode')
      Reflect.deleteProperty(item, 'compareInfo')
    },
    // 选中级联菜单的某一项的事件
    dragCascaderSelect({ selectValue, selectItem, selectList }, i) {
      debugger
      const { COMMON, CUSTOM, CONTRAST } = selectValue
      if (this.auxiliaryLineType !== 'custom') {
        selectItem.fieldCalcType = COMMON
      }
      if (!COMMON || (CONTRAST && !CONTRAST.hasOwnProperty('none'))) {
        this.$set(selectItem, 'percent', 100)
      }
      Object.assign(selectItem, {
        compareInfo: selectItem.aggType === 'CONTRAST' && selectItem.selectedConttast ? { compSubType: selectItem.selectedConttast } : {},
        compareRule: selectItem.aggType === 'CONTRAST' && selectItem.selectedConttastMode ? selectItem.selectedConttastMode : '',
      })
      setLgeTypeValue(selectItem) // 自定义、业务函数、排序时，需要设置bFxType和lgeType
      selectItem.aggType !== 'CONTRAST' && Reflect.deleteProperty(selectItem, 'type')
      Object.assign(this.item, selectItem)
    },
    handleField(e, value, index) {
      const activeField = this.fieldList.find(item => item.labeName === e)
      const updatedItem = Object.assign({}, value, {
        ...activeField,
        // alias: activeField.labeName,
        columnName: activeField._rawLabeName || activeField.labeName,
        columnType: activeField.columnTpe,
      })
      delete updatedItem.alias
      this.$set(updatedItem, 'aggType', ['string', 'date'].includes(activeField.columnTpe) ? 'COUNT' : 'SUM')
      // 汇总类型辅助线不需要 otherMetricValue fieldCalcType metricValue
      if (this.auxiliaryLineType !== 'custom') {
        this.$set(updatedItem, 'fieldCalcType', updatedItem.aggType || 'SUM')
        updatedItem.otherMetricValue = e
      }
      setLgeTypeValue(updatedItem)
      Reflect.deleteProperty(updatedItem, 'type')
      this.deleteCustom(updatedItem)
      this.deleteContrast(updatedItem)
      this.$nextTick(() => {
        this.changeCascaderComponents(index, 'initCreated')
      })

      if (!updatedItem._rawLabeName) {
        updatedItem._rawLabeName = activeField._rawLabeName
        updatedItem._uniqueLabeName = activeField._uniqueLabeName
        updatedItem.indexName = activeField.indexName
        updatedItem.indexId = activeField.indexId
        updatedItem.isFromIndex = activeField.isFromIndex
      }

      this.$emit('updateItem', updatedItem)
    },
    changeCascaderComponents(index, value, param) {
      const ref = this.$refs[this.dragcascaderRef]
      if (!ref) return
      const component = Array.isArray(ref) ? ref[0] : ref
      component[value](param)
    },
  },
}
</script>

<style lang="scss" scoped>
  .el-select {
    width: 132px;
  }
  .dis-ib {
    display: inline-block;
    // 修改字段框宽度时，trimMaxWidth需要一起修改，trimMaxWidth = width + 9
    width: 140px !important;
    margin-left: 6px;
    height: 32px;
    vertical-align: top;
    .el-input__inner{
      width: 140px;
      vertical-align: middle;
      height: 100%;
      padding: 0;
    }
    /deep/ .dragCascader{
      vertical-align: top;
      .el-tag__close {
        display: none;
      }
    }
  }
</style>
