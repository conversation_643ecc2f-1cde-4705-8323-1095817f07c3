<template>
  <div v-if="componentShow && showTab" class="chartWarningSetting-container">
    <div> <!-- 指定维度值指标预警 -->
      <el-tabs v-model="dimensionMethodTabValue" type="card">
        <el-tab-pane v-if="dimensionSettingShow.dimensionWarningList && (!$getFeatureConfig || !$getFeatureConfig('indicatorDimensionWarning.hidden'))" name="3">
          <ItemLabel
            slot="label"
            :label="$t('sdp.views.indicatorDimensionWarning')"
            :class="{ 'selected': warningMethod.dimensionMethod === 3 }"
            :level="2"
            icon
            :tips="$t('sdp.views.indicatorDimensionWarningTip')"
            ></ItemLabel>
        </el-tab-pane>
        <el-tab-pane v-if="dimensionSettingShow.dimensionValueIndicatorWarningList && (!$getFeatureConfig || !$getFeatureConfig('dimensionValueIndicatorWarning.hidden'))" name="4">
          <ItemLabel
            slot="label"
            :label="$t('sdp.views.dimensionValueIndicatorWarning')"
            :class="{ 'selected': warningMethod.dimensionMethod === 4 }"
            :level="2"
            ></ItemLabel>
        </el-tab-pane>
      </el-tabs>
      <span class="warningline-title-icon" @click="$emit('addWarningItem', warningMethod.dimensionMethod)">
        <i class="icon-sdp-add"></i>{{$t('sdp.views.add')}}
      </span>
      <div style="height: 24px;"></div>
      <component :is="componentName" v-bind="{ warningLineData, chartUserConfig, apiResultMap, element, genarateWarningItem, warningMethod, dimensionSettingShow }" @getDimensionValue="(a, b) => $emit('getDimensionValue', a, b)"></component>
    </div>
  </div>
</template>

<script>
import { ItemLabel } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import DimensionValueIndicatorWarning from '../components/dimensionValueIndicatorWarning'
import IndicatorDimensionWarning from '../components/indicatorDimensionWarning'

export default {
  name: 'ChartWarningSetting',
  components: { ItemLabel, DimensionValueIndicatorWarning, IndicatorDimensionWarning },
  inject: {
    authority: { default: {} },
    utils: { default: {} },
  },
  props: {
    element: {
      type: Object,
      default: () => ({}),
    },
    apiResultMap: {
      type: Object,
      default: () => ({}),
    },
    warningLineData: {
      type: Object,
      default: () => ({}),
    },
    warningMethod: {
      type: Object,
      default: () => ({}),
    },
    genarateWarningItem: {
      type: Function,
      default: () => {},
    },
    dimensionSettingShow: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    showTab() {
      let indicatorDimensionWarning = this.dimensionSettingShow.dimensionWarningList && (!this.$getFeatureConfig || !this.$getFeatureConfig('indicatorDimensionWarning.hidden'))
      let dimensionValueIndicatorWarning = this.dimensionSettingShow.dimensionValueIndicatorWarningList && (!this.$getFeatureConfig || !this.$getFeatureConfig('dimensionValueIndicatorWarning.hidden'))
      return indicatorDimensionWarning || dimensionValueIndicatorWarning
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig
    },
    componentName() {
      const { dimensionWarningList, dimensionValueIndicatorWarningList } = this.dimensionSettingShow
      const componentMap = {
        3: IndicatorDimensionWarning,
        4: DimensionValueIndicatorWarning,
      }
      if (
        (this.warningMethod.dimensionMethod === 3 && !dimensionWarningList) ||
        (this.warningMethod.dimensionMethod === 4 && !dimensionValueIndicatorWarningList)
      ) return ''
      return componentMap[this.warningMethod.dimensionMethod]
    },
    componentShow() {
      const { dimensionWarningList, dimensionValueIndicatorWarningList } = this.dimensionSettingShow
      return this.element.type === 'chart' && (dimensionWarningList || dimensionValueIndicatorWarningList)
    },
    dimensionMethodTabValue: {
      get() {
        return String(this.warningMethod.dimensionMethod)
      },
      set(val) {
        if (this.warningMethod.dimensionMethod === Number(val)) return
        this.$set(this.warningMethod, 'dimensionMethod', Number(val))
      }
    },
  },
  data() {
    return {
    }
  },
  methods: {
    compareMethodHandler(method) {
      if (this.warningMethod.dimensionMethod === method) return
      this.warningMethod.dimensionMethod = method
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-design-splite-line{
  margin-bottom: 24px;
}
.warning-title-container{
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.label-split{
  margin: 0 28px;
  font-weight: bolder;
  font-size: 20px;
  color: var(--kyz-mobileInformationDisabledColor, 'zx-kyz-0.012108279024307889');
}
.chartWarningSetting-container /deep/{
  .warningline-title-icon{
    margin-bottom: 20px;
    color: $color-main;
    cursor: pointer;
    .icon-sdp-add{
      font-size: 12px;
      margin-right: 8px;
    }
  }
  .item-label-container{
    cursor: pointer;
  }
  .selected{
    &.item-label-container{
      color: $color-main;
    }
  }
  // .el-tabs--card > .el-tabs__header {
  //   border-color: var(--sdp-cszj-bkfgx);
  //   .el-tabs__item {
  //     background-color: var(--sdp-szk-bjs);
  //     border: 1px solid transparent;
  //     border-bottom-color: var(--sdp-cszj-bkfgx);
  //     padding: 0 8px;
  //     line-height: 36px;
  //     height: 36px;
  //   }
  //   .el-tabs__item.is-active {
  //     border-left-color: var(--sdp-cszj-bkfgx);
  //     border-top-color: var(--sdp-cszj-bkfgx);
  //     border-right-color: var(--sdp-cszj-bkfgx);
  //     border-bottom-color: var(--sdp-szk-bjs);
  //   }
  // }
  // .el-tabs--card>.el-tabs__header .el-tabs__nav{
  //   border: none;
  // }
}
</style>
