<template>
  <DialogRoot
    :title="$t('sdp.views.warning')"
    :visible="dialogVisible"
    :width="isCard ? '1300px' : '1250px'"
    :confirmLoading="confirmLoading"
    :confirmDisabled="confirmLoading"
    @open="initData"
    @confirm="confirm"
    @close="cancel"
  >
    <div class="warningline">
      <template v-if="!isCard">
        <div class="chart-design-splite-line"></div>
        <ItemLabel :label="$t('sdp.views.indicatorWarning')" :level="2" display="block" style="line-height: 36px; height: 36px; margin-top: 0;"></ItemLabel>
        <template v-if="!isCommonChart">
          <LineGroup
            ref="LineGroup_warningEffect"
            :lineData="warningEffect"
            :lineConfig="getLineConfig(warningEffect)"
            @change="lineGroupChange">
          </LineGroup>
        </template>
        <div class="warningline-title warningline-title-customer" style="line-height: 24px;">
          <span class="warningline-title-icon" @click="addDetailSetting"><i class="icon-sdp-add"></i> {{$t('sdp.views.add')}}</span>
        </div>
        <warnLineSettings
          :elementAlertNumberLimit="elementChartAlertNumberLimit"
          ref="warnLineSettings"
          :boardInfo="boardInfo"
          v-bind="{ element, warningLineData, apiResultMap, dimensionSettingShow, datasetList, warningMethod, isWarnTypeLinear }">
        </warnLineSettings>
        <div style="height: 25px;"></div>
        <ChartWarningSetting ref="ChartWarningSetting" v-bind="{ element, warningLineData, apiResultMap, dimensionSettingShow, genarateWarningItem, datasetList, warningMethod }" @getDimensionValue="getRemoteDimensionValueList" @addWarningItem="addWarningItem"></ChartWarningSetting>
      </template>
      <!-- 卡片预警线设置 -->
      <div v-else class="card-warningline-setting" :class="tagNewCardContent">
        <el-tabs v-model="activeTab">
          <el-tab-pane v-for="(item, cardIndex) in warningLineData.warnLineSettingList" :key="cardIndex" :label="`${$t('sdp.views.indexValue')}${cardIndex + 1}`">
            <div class="card-name" v-if="tagNewCardContent === 'twoIndeces'">{{ content.optionArray[cardIndex] ? content.optionArray[cardIndex].cardName : '' }}</div>
            <div v-for="(cardWarnLineType, keyIndex) in getCardWarningKeys(cardIndex)" :key="keyIndex" class="content">
              <div class="warningline-title">
                <span>{{ getWarningText(cardWarnLineType, cardIndex) }}</span>
                <span class="warningline-title-icon" @click="addDetailSetting(cardWarnLineType, cardIndex)"><i class="icon-sdp-add"></i> {{$t('sdp.views.add')}}</span>
              </div>
              <warnLineSettings
                isCard
                :elementAlertNumberLimit="elementCardAlertNumberLimit"
                :warningLineData="getCurrentWarnLineList(item, cardWarnLineType, cardIndex)"
                ref="warnLineSettings"
                v-bind="{ element, cardWarnLineType, dimensionSettingShow, datasetList, warningMethod, cardIndex }"
                @delete-detail="deleteDetailSetting"></warnLineSettings>
              <div class="cutline"></div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </DialogRoot>
</template>

<script>
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog'
import { LineGroup, ItemLabel } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import warnLineSettings from './warnLineSettings'
import { TAGNEWCARD, WARNLINE_TYPE_CONNECT_STR, THEME_TYPE } from 'packages/assets/constant'
import ChartWarningSetting from './chartWarningSetting'
import { BARCHART_WARN_TYPES, PURE_COLOR_WARNING_CHART, DIMENSION_WARNING_CHART, DIMENSION_VALUE_INDICATOR_WARNING_CHART } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { NO_METRIC_WARNING_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementTagNewCard/constant'
import {
  getColumnValues
} from 'packages/base/board/displayPanel/params/paramElement/selectOrdinary/api'
import { efficientNumericId } from 'packages/assets/utils/globalTools'
export default {
  name: 'WarningLineDialog',
  inject: {
    chart: { default: {} },
    utils: { default: {} },
    langCode: { default: 'zh' },
    authority: { default: {} },
    boardWarningSubscribeData: { default: null },
    boardInfo: { default: {} },
    cardList: { default: [] },
    configs: { default: () => false },
  },
  mixins: [mixin_dialog],
  props: {
    isCard: {
      type: Boolean,
      default: false
    },
    datasetList: {
      type: Array,
      default: () => [],
    }
  },
  components: {
    warnLineSettings, ChartWarningSetting, LineGroup, ItemLabel
  },
  data() {
    return {
      condition: '',
      confirmLoading: false,
      // disabledPercentArr: [],
      // 只支持指标汇总比较的图形
      commonChart: ['ve-grid-normal'],
      activeTab: '0',
      apiResultMap: {},
      warningLineData: {
        warnLineSettingList: [],
        dimensionWarningList: [],
        dimensionValueIndicatorWarningList: [],
      },
      warningMethod: { indicatorMethod: 1, dimensionMethod: 3, },
      warningEffect: {
        barchartWarnType: BARCHART_WARN_TYPES.LINEAR,
        linearDirectionType: '',
      },
    }
  },
  provide() {
    return {
      getApiResultMap: () => this.apiResultMap,
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs.options?.onlyIndex
    },
    needViewColumnName() {
      if (this.isCard) {
        return ![TAGNEWCARD.RATECARD, TAGNEWCARD.COMPARECARD].includes(this.tagNewCardContent)
      } else if (this.element.type === TYPE_ELEMENT.CHART) {
        return this.chartUserConfig.chartAlias === 've-gauge-normal'
      }
      return false
    },
    metricsList() {
      return this.getMetricsList()
    },
    dimensionListOption() {
      const { dimensionList = [], extendDimensionList = [] } = this.chartUserConfig
      let orderList = [];
      [...dimensionList, ...extendDimensionList].forEach(d => {
        if (d.order === 'customSort' && d.orderList?.length) {
          if (orderList.find(s => s.alias === d.orderList[0].alias)) return
          orderList.push(d.orderList[0])
        }
      })
      const dimensionArray = [...dimensionList, ...extendDimensionList, ...orderList]
      return dimensionArray.map(d => {
        return {
          label: d.alias || d.labeName || d.columnName,
          datasetName: d.labeName || d.columnName,
          value: d.keyName,
          columnTpe: d.columnTpe || d.columnType,
          order: d.order !== 'none' ? d.order : '',
        }
      })
    },
    dimensionSettingShow() {
      const { chartAlias, dimensionList = [], extendDimensionList = [] } = this.chartUserConfig
      return {
        dimensionWarningList: DIMENSION_WARNING_CHART.includes(chartAlias),
        dimensionValueIndicatorWarningList: [...dimensionList, ...extendDimensionList].length && DIMENSION_VALUE_INDICATOR_WARNING_CHART.includes(chartAlias),
      }
    },
    chartType() {
      return this.element.content.alias
    },
    isCommonChart() {
      return this.commonChart.includes(this.element.content.chartUserConfig.chartAlias)
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig
    },
    // 卡片类型
    tagNewCardContent() {
      return this.$_getProp(this.element, 'content.tagNewCardContent', '')
    },
    content() {
      return this.element.content
    },
    // 比率卡片维度
    rateCardDimension() {
      if (this.tagNewCardContent !== TAGNEWCARD.RATECARD) return []
      const optionArray = this.$_getProp(this.element, 'content.optionArray', [])
      return optionArray[0] ? optionArray[0].dimension : []
    },
    isCanSetPureWarn() {
      const { stack } = this.chartUserConfig.compositeChart || {}
      return this.chartType === 've-composite' ? !stack : PURE_COLOR_WARNING_CHART.includes(this.chartType)
    },
    isWarnTypeLinear() {
      return this.isCanSetPureWarn && this.warningEffect.barchartWarnType === BARCHART_WARN_TYPES.LINEAR
    },
    linearDisabled() {
      return this.hasSetLinear(this.chartUserConfig.colors) || this.hasSetLinear(this.chartUserConfig.dimensionColors || [])
    },
    // 计算图形元素剩余可设置预警数量
    elementChartAlertNumberLimit() {
      if (!this.isCard) {
        // 看板其他元素的预警数量
        if (!this.boardWarningSubscribeData) return 0
        const boardWarnNumber = this.boardWarningSubscribeData.getOtherElementWarnLengthTotal(this.element.id)
        // 当前预警的数量
        const chioceTab = this.element.content.chioceTab
        let sum = 0
        if (chioceTab && chioceTab.length) {
          chioceTab.forEach((item, index) => {
            if (item.saveObj.mapSchemeSetting?.schemeList?.length) {
              item.saveObj.mapSchemeSetting.schemeList.forEach((val, i) => {
                sum += getWarningLength.call(this, val.saveObj, val.saveObj.chartUserConfig.childChartAlias === this.chartUserConfig.childChartAlias)
              })
            } else {
              sum += getWarningLength.call(this, item.saveObj, this.element.content.saveIndex === index)
            }
          })
        } else {
          const mapSchemeSetting = this.element.content.mapSchemeSetting
          if (mapSchemeSetting?.schemeList?.length) {
            mapSchemeSetting.schemeList.forEach((temp, i) => {
              sum += getWarningLength.call(this, temp.saveObj, temp.saveObj.chartUserConfig.childChartAlias === this.chartUserConfig.childChartAlias)
            })
          } else {
            sum += getWarningLength.call(this, this.element.content, true)
          }
        }
        // 总数量
        const alertNumberLimit = this.boardInfo?.elementAlert?.ALERT_NUMBER || 5
        // 剩余可设置数
        const lastNumber = alertNumberLimit - boardWarnNumber - sum
        return lastNumber
      }
      return 0
      function getWarningLength(content, iscurrent) {
        let sum = 0
        if (iscurrent) {
          sum += this.warnLineSettingListLen
        } else {
          const { warnLineSettingList = [], dimensionValueIndicatorWarningList = [], warningMethod = {}, chartAlias } = content.chartUserConfig
          const warningArray = [...warnLineSettingList]
          if (DIMENSION_VALUE_INDICATOR_WARNING_CHART.includes(chartAlias) && warningMethod.dimensionMethod === 4) {
            warningArray.push(...dimensionValueIndicatorWarningList)
          }
          sum += warningArray.filter(v => v.allowSubscribe).length
        }
        return sum
      }
    },
    warnLineSettingListLen() {
      const { warnLineSettingList = [], dimensionValueIndicatorWarningList = [] } = this.warningLineData
      const warningArray = [...warnLineSettingList]
      if (this.dimensionSettingShow.dimensionValueIndicatorWarningList && this.warningMethod.dimensionMethod === 4) {
        warningArray.push(...dimensionValueIndicatorWarningList)
      }
      return !this.isCard && warningArray.filter(v => v.allowSubscribe).length
    },
    // 计算卡片元素剩余可设置预警数量
    elementCardAlertNumberLimit() {
      if (this.isCard) {
        // 看板其他元素的预警数量
        if (!this.boardWarningSubscribeData) return 0
        const boardWarnNumber = this.boardWarningSubscribeData.getOtherElementWarnLengthTotal(this.element.id) || 0
        // 当前预警的数量
        let sum = this.warningLineData.warnLineSettingList.reduce((pre, current) => {
          const warnLen = current?.length && current.filter(v => v.allowSubscribe).length
          return pre + warnLen
        }, 0)
        // 移动端组合卡片预警订阅数量
        let combineRestWarnLen = 0
        if (this.utils.isMobile && this.cardList?.length) {
          combineRestWarnLen = this.cardList.filter(c => c.id !== this.element.id).reduce((pre, current) => {
            return pre + current.content.chartUserConfig.warnLineSettingList.filter(arr => arr.filter(v => v.allowSubscribe)).length
          }, 0)
        }
        // 总数量
        const alertNumberLimit = this.boardInfo?.elementAlert?.ALERT_NUMBER || 5
        // 剩余可设置数
        const lastNumber = alertNumberLimit - boardWarnNumber - sum - combineRestWarnLen
        return lastNumber
      }
      return 0
    }
  },
  watch: {
    'tagNewCardContent'() {
      this.changeCardWarningLine()
    },
    'rateCardDimension.length'() {
      this.changeCardWarningLine()
    },
  },
  created() {
    this.confirm = this.$_debounce(this.confirm)
  },
  methods: {
    compatibility() {
      // 加载时做兼容，这里不再做兼容

      // 兼容生成折线颜色数据 8682 polygonalColor
      // console.log('this.warningLineData.warnLineSettingList: ', this.warningLineData.warnLineSettingList, this)
      // this.warningLineData.warnLineSettingList.forEach(item => {
      //   if ([1, 2].includes(item.compareMethod)) {
      //     if (!item.polygonalColor) {
      //       const metricsContainerDefaultIndex = this.chartUserConfig.metricsContainer.default.findIndex(item1 => item1.keyName === item.metricKeyName)
      //       const polygonalColor = this.chartUserConfig.colors?.[metricsContainerDefaultIndex]?.color
      //       this.$set(item, 'polygonalColor', polygonalColor || 'rgba(255,0,0,1)')
      //       console.log('warnLineSettingList item: ', item)
      //     }
      //   }
      // })
    },
    getRemoteDimensionValueList(dataItem, originName) {
      let filedName = originName || (dataItem.calcType === 'exactMatch' && dataItem.matchField.columnName)
      if (filedName && dataItem.fieldKeyName && !this.apiResultMap[`${filedName}_geted`] && !this.apiResultMap[`${filedName}_loading`]) {
        this.$set(this.apiResultMap, `${filedName}_loading`, true)
        getData.call(this, dataItem.matchField).then(res => {
          this.$set(this.apiResultMap, res.key, res.data)
          this.apiResultMap[`${filedName}_geted`] = true
          this.$set(this.apiResultMap, `${filedName}_loading`, false)
        })
      }
      async function getData(params) {
        const apiResult = await getColumnValues(this.utils.api, { dataSets: [params], languageCode: '1', datasetPermission: this.authority?.datasetPermission, tenantId: this.utils.tenantId }).catch(() => {
          return []
        })
        return { data: apiResult, key: params.columnName }
      }
    },
    // 预警线交集检查
    warningConditionsValidate(warningArray) {
      if (this.isCard) return true
      // 不允许同时出现>或>=,<或<=, 选择不为空或不等于，只允许该字段出现一次
      // 获取可设置预警的字段
      const metricsList = this.getMetricsList()
      const rulesNotPass = metricsList.some(item => {
        const bigArr = ['>', '>=']
        const smallArr = ['<', '<=']
        const onlyOnceArr = ['!=', 'isNotNull', 'isNull']
        const filterArr = warningArray.filter(e => e.metricKeyName === item.keyName)
        const bigFilterArr = filterArr.filter(e => bigArr.includes(e.calcType))
        const smallFilterArr = filterArr.filter(e => smallArr.includes(e.calcType))
        const onlyOnceFilter = filterArr.some(e => onlyOnceArr.includes(e.calcType))
        return bigFilterArr.length > 1 || smallFilterArr.length > 1 || (onlyOnceFilter && filterArr.length > 1)
      })
      if (rulesNotPass) {
        this.$message({ message: this.$t('sdp.views.warnlineOverlap'), type: 'warning' })
      }
      return !rulesNotPass
    },
    dimensionWarningValidate() {
      if (this.isCard || !this.dimensionSettingShow.dimensionWarningList || this.warningMethod.dimensionMethod !== 3) return true
      const validateMap_1 = ['name', 'fieldKeyName', 'calcType', 'chartColor']
      const validateMap_2 = {
        calcType: ['exactMatch', 'matchArray'],
        fieldColumnType: ['date', 'matchDateValue'],
      }
      const valueMap = {}
      for (let i = 0; i < this.warningLineData.dimensionWarningList.length; i++) {
        const currentWarning = this.warningLineData.dimensionWarningList[i]
        if (!currentWarning.metricKeyName) {
          this.$message.warning(this.$t('sdp.message.warningMetricTip'))
          return false
        }
        valueMap[currentWarning.metricKeyName] = {}
        let complationFlag = currentWarning.warningList.some(w => {
          if (validateMap_1.some(k => !w[k])) return true
          if (!valueMap[currentWarning.metricKeyName][w.fieldKeyName]) {
            valueMap[currentWarning.metricKeyName][w.fieldKeyName] = {}
          }
          if (!valueMap[currentWarning.metricKeyName][w.fieldKeyName][w.calcType]) {
            valueMap[currentWarning.metricKeyName][w.fieldKeyName][w.calcType] = []
          }

          let isString = true

          let innerFlag = Object.keys(validateMap_2).some(keyPath => {
            if (w[keyPath] === validateMap_2[keyPath][0]) {
              const val = w[validateMap_2[keyPath][1]]
              isString = false
              valueMap[currentWarning.metricKeyName][w.fieldKeyName][w.calcType].push(val)
              if (Array.isArray(val)) return !val.length
              if (typeof val === 'string') return !val.trim()
              if (val === undefined) return true
            }
          })
          if (innerFlag) return true

          if (isString) {
            valueMap[currentWarning.metricKeyName][w.fieldKeyName][w.calcType].push(w.matchValue.trim())
            return !w.matchValue.trim()
          }
        })
        // 信息填写不完整
        if (complationFlag) {
          this.$message.warning(this.$t('sdp.views.plsFillComplete'))
          return false
        }
      }

      const validateMap_3 = [['>', '>='], ['<', '<='], ['!=', 'isNotNull', 'isNull']]
      let repeatFlag = Object.keys(valueMap).some(vk => {
        // vk是metricKeyName
        return Object.keys(valueMap[vk]).some(dk => {
          // dk是维度的keyname
          let lengthFlag = validateMap_3.some(c => {
            let calcValue = []
            c.forEach(k => valueMap[vk][dk][k] && calcValue.push(valueMap[vk][dk][k]))
            return calcValue.length > 1
          })
          // 不允许同时出现>或>=,<或<=, 选择不为空或不等于
          if (lengthFlag) return true
          return Object.keys(valueMap[vk][dk]).some(ck => {
            // ck是计算方式
            let calcValue = valueMap[vk][dk][ck].map(cv => {
              if (Array.isArray(cv)) return cv
              if (['=', '!='].includes(ck)) return cv.split(/;|；/)
              return cv
            })
            let newCalcValue = this.$_flatten(calcValue)
            if (new Set(newCalcValue).size !== newCalcValue.length) return true
          })
        })
      })
      if (repeatFlag) {
        this.$message.warning(this.$t('sdp.views.warnlineOverlap'))
        return false
      }
      return true
    },
    confirm() {
      if (this.confirmLoading) return
      this.confirmLoading = true

      const { warnLineSettingList = [], dimensionValueIndicatorWarningList = [] } = this.warningLineData

      let warningArray = [...warnLineSettingList]
      // 指标维度预警有自己的验证
      if (this.warningMethod.dimensionMethod === 4 && this.dimensionSettingShow.dimensionValueIndicatorWarningList) warningArray.push(...dimensionValueIndicatorWarningList)
      warningArray = this.$_flatten(warningArray)

      const functionArr = [
        'requiredValidate',
        'nameRepeatValidate',
        'warningConditionsValidate',
        'dimensionWarningValidate',
      ]

      const validateResult = functionArr.some(funcName => !this[funcName](warningArray))

      if (validateResult) {
        this.confirmLoading = false
        return
      }

      this.warningDataFix(warningArray)

      if (this.isCard) {
        this.$set(this.chartUserConfig, 'warnLineSettingList', this.warningLineData.warnLineSettingList)
        this.$emit('confirm')
        this.cancel(true)
      } else {
        if (this.isWarnTypeLinear) {
          this.$set(this.chartUserConfig, 'linearDirectionType', this.warningEffect.linearDirectionType)
        } else {
          this.$delete(this.chartUserConfig, 'linearDirectionType')
        }
        // 清掉不要的数据
        if (this.warningMethod.dimensionMethod === 3 && this.dimensionSettingShow.dimensionWarningList) {
          this.chartUserConfig.dimensionWarningList = this.warningLineData.dimensionWarningList
          this.chartUserConfig.dimensionValueIndicatorWarningList = []
        } else if (this.warningMethod.dimensionMethod === 4 && this.dimensionSettingShow.dimensionValueIndicatorWarningList) {
          this.chartUserConfig.dimensionWarningList = []
          this.chartUserConfig.dimensionValueIndicatorWarningList = this.warningLineData.dimensionValueIndicatorWarningList
        } else {
          this.chartUserConfig.dimensionWarningList = []
          this.chartUserConfig.dimensionValueIndicatorWarningList = []
        }
        // 图形关闭弹窗留到请求校验成功以后再执行
        this.$set(this.chartUserConfig, 'warningMethod', this.$_deepClone(this.warningMethod))
        this.$set(this.chartUserConfig, 'barchartWarnType', this.warningEffect.barchartWarnType)
        this.$emit('confirm', { func: this.cancel, type: 'warnline', cancelLoading: this.cancelLoading, data: this.warningLineData.warnLineSettingList, isNeedWarnCheck: this.warningMethod.indicatorMethod === 2, compareMethod: this.warningMethod.indicatorMethod })
        if (this.warningMethod.indicatorMethod === 1 || this.warningMethod.indicatorMethod === 2) {
          this.cancel(true)
        }
      }
    },
    // 数据补充
    warningDataFix(warningArray) {
      warningArray.forEach(item => {
        // 如果有 _rawLabeName labeName 和 field 都换成 _rawLabeName
        if (item._rawLabeName) {
          item.labeName = item._rawLabeName
          item.columnName = item._rawLabeName
          if (item.fieldType === 'other') {
            item.field = item._rawLabeName
          }
        }
        Object.assign(item, {
          field: item.fieldType === 'other' ? item.field : '',
          matchField: item.fieldType === 'other' ? item.matchField : {},
          matchFieldCascader: item.fieldType === 'other' ? item.matchFieldCascader : [],
          percent: !item.percent ? '100' : item.percent,
          fieldCalcType: item.fieldType === 'other' ? item.fieldCalcType : '',
          value: item.fieldType === 'other' ? '' : item.value,
          rangeMax: item.calcType === 'range' ? item.rangeMax : '',
          rangeMin: item.calcType === 'range' ? item.rangeMin : '',
          compareMethod: item.compareMethod > 2 ? item.compareMethod : this.warningMethod.indicatorMethod,
          fieldType: item.fieldType || 'fixed',
        })
        if (this.needViewColumnName && !item.viewColumnName) {
          // viewColumnName:  indexValue指标值、  growthValue增长值、  growthRate增长率、  complateRate完成率、 compareValue对比值、 ratioValue比率值
          if (this.isCard) {
            const warningType = item.cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)
            item.viewColumnName = warningType[0] === 'dimension' ? 'indexValue' : warningType[0]
          } else {
            item.viewColumnName = 'complateRate'
          }
        }
        this.clearUselessData(item)
      })
    },
    // 预警必填验证，指标维度预警有自己的验证
    requiredValidate(warningArray) {
      let message = this.$t('sdp.views.plsFillComplete')

      // 卡片增长值、增长率、完成率显示固定文字
      const disabledConfirm = warningArray.some(item => {
        // 是否需要输入预警字段
        const isNeedMetric = !NO_METRIC_WARNING_TYPE.includes(item.cardWarnLineType)
        if (!item.name || (isNeedMetric && !item.metric)) {
          return true
        }
        switch (item.calcType) {
          case 'isNull':
          case 'isNotNull':
            break
          case 'range':
            if (!item.rangeMax || !item.rangeMin) {
              return true
            }
            if (!/^-?\d+(\.?\d+)?$/.test(item.rangeMax) || !/^-?\d+(\.?\d+)?$/.test(item.rangeMin)) {
              message = this.$t('sdp.views.plsNum')
              return true
            }
            if (+item.rangeMax <= +item.rangeMin) {
              message = `${this.$t('sdp.views.range')}${this.$t('sdp.views.setFormatErr')}`
              return true
            }
            break
          default:
            if (!item.fieldType ||
                (!item.percent && this.chartType === 've-gauge-normal') ||
                (item.fieldType === 'other' && !item.field) ||
                (item.fieldType !== 'other' && !item.value)
            ) {
              return true
            }
            if (!/^([0-9]{1,2}|100)$/.test(item.percent)) {
              message = `${this.$t('sdp.views.plsInZeroTohundred')}`
              return true
            }
            if (item.fieldType !== 'other') {
              if (!/^-?\d+(\.?\d+)?$/.test(item.value)) {
                message = this.$t('sdp.views.plsNum')
                return true
              }
            }
            break
        }

        if (Number(item.compareMethod) === 4) {
          return item.dimensionSetting.some(ds => !ds.fieldKeyName || !ds.selectValues.length)
        }
      })
      if (disabledConfirm) {
        this.$message({ message, type: 'warning' })
        return false
      }
      return true
    },
    // 预警名称重叠验证
    nameRepeatValidate(warningArray) {
      const { dimensionWarningList = [] } = this.warningLineData
      const { dimensionMethod } = this.warningMethod
      let nameArray = warningArray.map(w => w.name)
      if (Number(dimensionMethod) === 3 && this.dimensionSettingShow.dimensionWarningList) {
        dimensionWarningList.forEach(dw => {
          dw.warningList.forEach(w => {
            nameArray.push(w.name)
          })
        })
      }
      if (new Set(nameArray).size !== nameArray.length) {
        this.$message.warning(this.$t('sdp.views.duplicate'))
        return false
      }
      return true
    },
    // 清空预警无用数据
    clearUselessData(warningItem) {
      if (warningItem.fieldType === 'fixed') {
        const needClear = ['alias', 'columnName', 'field', 'columnTpe', 'keyName', 'labeName', 'modelType']
        needClear.forEach(key => this.$delete(warningItem, key))
      }
    },
    cancel(isConfirmData = false) {
      if (typeof isConfirmData === 'boolean' && isConfirmData) {
        this.element.content.chartUserConfig.warnLineSettingList = this.$_JSONClone(this.warningLineData.warnLineSettingList)
        this.element.content.chartUserConfig.condition = this.condition // TODO 这个暂时后台用不着，后期会给到后台
        this.element.content.chartUserConfig.compareMethod = this.warningMethod.indicatorMethod
      }
      this.confirmLoading = false
      this.dialogVisible = false
    },
    cancelLoading() {
      this.confirmLoading = false
    },
    addWarningItem(warningType) {
      if (warningType !== 3 && warningType !== 4) return
      const key = warningType === 4 ? 'dimensionValueIndicatorWarningList' : 'dimensionWarningList'
      if (this.warningLineData[key].length >= 20) return
      this.warningLineData[key].push(this.genarateWarningItem(warningType))
    },
    uuid() {
      const onlyIndex = this?.$getSystemConfig?.('onlyIndex')
      if (onlyIndex) {
        return efficientNumericId()
      }
      return this.$_generateUUID()
    },
    genarateWarningItem(compareMethod) {
      let defaultWarnLine = {
        id: this.uuid(),
        name: '', // 预警名称
        metric: '', // 预警条件字段
        metricKeyName: '',
        calcType: '', // 计算方式：等于 =，不等于!=,为空isNull,不为空isNotNull，大于>,大于等于>=,小于<，小于等于<=，区间range
        fieldType: 'fixed', // 其他字段other或者固定值fixed
        field: '', // 选择的数据集字段
        fieldCalcType: '', // 数据集字段计算方式,求和SUM，最大MAX，最小MIN，平均AVG，普通计数COUNT，去重计数COUNT_DISTINCT
        percent: '100', // 数据集字段计算方式百分比,0-100的整数
        value: '', // 固定值输入的值
        rangeMin: '', // 区间左侧的值
        rangeMax: '', // 区间右侧的值
        warnLineColor: 'rgba(255,0,0,1)', // 预警线颜色
        chartColor: 'rgba(255,0,0,1)', // 渐变颜色
        polygonalColor: 'rgba(255,0,0,1)', // 折线颜色



        warnLineColorTheme:{
            [THEME_TYPE.classicWhite]: 'rgba(255,0,0,1)',
            [THEME_TYPE.darkBlue]: 'rgba(255,0,0,1)',
            [THEME_TYPE.deepBlue]: 'rgba(255,0,0,1)',
        },
        chartColorTheme:{
            [THEME_TYPE.classicWhite]: 'rgba(255,0,0,1)',
            [THEME_TYPE.darkBlue]: 'rgba(255,0,0,1)',
            [THEME_TYPE.deepBlue]: 'rgba(255,0,0,1)',
        },
        polygonalColorTheme:{
            [THEME_TYPE.classicWhite]: 'rgba(255,0,0,1)',
            [THEME_TYPE.darkBlue]: 'rgba(255,0,0,1)',
            [THEME_TYPE.deepBlue]: 'rgba(255,0,0,1)',
        },
      }
      if (Number(compareMethod) === 4) {
        defaultWarnLine.matchField = {}
        defaultWarnLine.matchFieldCascader = []
        defaultWarnLine.compareMethod = 4
        defaultWarnLine.dimensionSetting = [
          {
            fieldColumnName: '',
            fieldAlias: '',
            fieldKeyName: '',
            selectValues: [],
            id: this.$_generateUUID(),
            joinType: 'and',
          }
        ]
      } else if (compareMethod === 3) {
        const warningItem = {
          name: '', // 预警名称
          fieldAlias: '', // 预警字段
          fieldKeyName: '', // 预警字段的keyName
          fieldColumnType: 'string',
          compareMethod: 3,
          calcType: '', // 预警比较方式：><===!==这种
          matchType: '', // 匹配方式：固定值、其他字段、精确匹配
          matchValue: '', // 如果是字符类型，用户输入的字符串
          id: this.$_generateUUID(),
          matchField: {}, // 如果是精确匹配，字段信息存在这
          matchDateValue: '', // 如果维度类型是日期，相关的信息存在这
          matchArray: [], // 如果是精确匹配，存为一个数组
          lineStyle: 'solid',
          chartColor: 'rgba(255,0,0,1)',
          chartColorTheme:{
            [THEME_TYPE.classicWhite]: 'rgba(255,0,0,1)',
            [THEME_TYPE.darkBlue]: 'rgba(255,0,0,1)',
            [THEME_TYPE.deepBlue]: 'rgba(255,0,0,1)',
          },
        }
        let result = {
          metricAlias: '',
          metricKeyName: '',
          warningList: [warningItem],
        }
        return result
      }
      if (this.isCard) {
        // 预警线类型 图形预警、卡片指标值预警、增长率预警等等
        Object.assign(defaultWarnLine, {
          cardWarnLineType: compareMethod,
          unitColor: 'rgba(255,0,0,1)', // 卡片指标单位预警颜色
          checkedChartColor:false,
          unitColorTheme:{
            [THEME_TYPE.classicWhite]: 'rgba(255,0,0,1)',
            [THEME_TYPE.darkBlue]: 'rgba(255,0,0,1)',
            [THEME_TYPE.deepBlue]: 'rgba(255,0,0,1)',
          }
        })
        if (this.needViewColumnName) {
          // viewColumnName:  indexValue指标值、  growthValue增长值、  growthRate增长率、  complateRate完成率、 compareValue对比值、 ratioValue比率值
          const warningType = compareMethod.split(WARNLINE_TYPE_CONNECT_STR)
          const viewColumnName = warningType[0] === 'dimension' ? 'indexValue' : warningType[0]
          defaultWarnLine.viewColumnName = viewColumnName
        }
      }
      return defaultWarnLine
    },
    getLineConfig(lineData) {
      const isGauge = this.chartUserConfig.chartAlias === 've-gauge-normal'
      const isHeatmap = this.chartUserConfig.chartAlias === 've-bar-Heatmap'
      const lineConfig = [
        {
          type: 'radio',
          model: this.warningMethod,
          activeValue: 1,
          key: 'indicatorMethod',
          label: this.$t('sdp.views.compareMethods'),
          labelStyle: 'line-height: 31px; position: absolute;',
          innerLabel: this.$t('sdp.views.dimensionCompareMethods'),
          style: 'margin-right: 22px; position: relative;',
          innerStyle: 'margin-top: 31px;',
        },
        {
          type: 'radio',
          model: this.warningMethod,
          activeValue: 2,
          key: 'indicatorMethod',
          innerLabel: this.$t('sdp.views.choiceCompareMethods'),
        },
        {
          type: 'radio',
          model: this.warningEffect,
          activeValue: BARCHART_WARN_TYPES.PURE,
          key: 'barchartWarnType',
          label: isGauge ? this.$t('sdp.views.warnEffect') : this.$t('sdp.views.warnChartEffect'),
          labelStyle: 'line-height: 31px; position: absolute;',
          innerLabel: this.$t('sdp.views.warnSolidColor'),
          style: `margin-right: 22px; position: relative;${ !isGauge && !this.onlyIndex ? 'margin-left: 50px;' : '' }`,
          innerStyle: 'margin-top: 31px;',
        },
        {
          type: 'radio',
          model: this.warningEffect,
          activeValue: BARCHART_WARN_TYPES.LINEAR,
          key: 'barchartWarnType',
          disabled: this.linearDisabled,
          innerLabel: this.$t('sdp.views.warnLinearColor'),
        },
        {
          type: 'select',
          model: this.warningEffect,
          activeValue: BARCHART_WARN_TYPES.LINEAR,
          label: this.$t('sdp.views.linearDirection'),
          labelStyle: 'line-height: 31px;',
          key: 'linearDirectionType',
          clearable: true,
          placeholder: this.$t('sdp.views.default'),
          style: { width: '193px', flexShrink: 0, marginLeft: '50px', },
          options: [
            { value: 'toRight', label: this.$t('sdp.views.toRight') },
            { value: 'toLeft', label: this.$t('sdp.views.toLeft') },
            { value: 'toTop', label: this.$t('sdp.views.toTop') },
            { value: 'toBottom', label: this.$t('sdp.views.toBottom') },
          ],
        },
      ]
      if (isGauge) {
        const linearDirectionOptions = lineConfig.find(l => l.key === 'linearDirectionType')
        linearDirectionOptions.options = [
          { value: 'clockwise', label: this.$t('sdp.views.clockwise') },
          { value: 'anticlockwise', label: this.$t('sdp.views.anticlockwise') },
        ]
      }
      
      return lineConfig.filter(l => {
        if (l.key === 'barchartWarnType') return this.isCanSetPureWarn
        if (l.key === 'linearDirectionType') return this.isCanSetPureWarn && this.warningEffect.barchartWarnType === BARCHART_WARN_TYPES.LINEAR && !this.linearDisabled
        if (l.key === 'indicatorMethod' && l.activeValue=== 2) return !isHeatmap && (!this.$getFeatureConfig || !this.$getFeatureConfig('choiceCompareMethods.hidden'))
        if (l.key === 'indicatorMethod' && l.activeValue=== 1) return !isGauge && (!this.$getFeatureConfig || !this.$getFeatureConfig('choiceCompareMethods.hidden'))
        return true
      })
    },
    lineGroupChange(key, val, dataItem, metricParams, lineItem) {
      if (key === 'indicatorMethod') {
        const arr = Array.isArray(this.warningLineData.warnLineSettingList) ? this.warningLineData.warnLineSettingList : []
        if (!arr.length) return
        arr.forEach((item, i) => {
          if (val === 1) {
            item.percent = 100
          }
        })
        this.changeCascader(false)
      } else if (key === 'barchartWarnType') {
        this.$set(this.warningEffect, 'linearDirectionType', '')
      }
    },
    initData() {
      this.initWarningMethod()
      this.initWarningEffect()
      this.initDimensionWarningList()
      this.initDimensionValueIndocatorWarningData()
      // 指标预警的初始化需要放在所有类型的预警初始化的后面
      this.initWarnLineSettingList()

      this.compatibility()

      // 弹窗打开时默认选中第一个标签页
      this.activeTab = '0'
      this.changeCascader('open')
    },
    initWarningEffect() {
      let { barchartWarnType, linearDirectionType } = this.element.content.chartUserConfig
      this.warningEffect = {
        barchartWarnType: this.linearDisabled ? BARCHART_WARN_TYPES.PURE : (barchartWarnType || BARCHART_WARN_TYPES.LINEAR),
        linearDirectionType: linearDirectionType,
      }
      if (this.linearDisabled) {
        this.element.content.chartUserConfig.barchartWarnType = BARCHART_WARN_TYPES.PURE
      }
    },
    initWarnLineSettingList() {
      const chartUserConfigKey = 'warnLineSettingList'
      const warningSetted = this.chartUserConfig[chartUserConfigKey]?.length
      if (warningSetted) {
        this.$set(this.warningLineData, chartUserConfigKey, this.$_deepClone(this.chartUserConfig[chartUserConfigKey]).map(item => {
          if (item._uniqueLabeName) {
            return {
              ...item,
              labeName: item._uniqueLabeName,
              field: item._uniqueLabeName,
            }
          }
          return item
        }))
      } else if (this.isCard) {
        let _warnLineSettingList = []
        if (!this.element.content) {
          _warnLineSettingList = []
        } else {
          const { tagNewCardContent } = this.element.content
          _warnLineSettingList = tagNewCardContent === TAGNEWCARD.TWOINDICES ? [[], []] : [[]]
        }

        this.$set(this.warningLineData, chartUserConfigKey, _warnLineSettingList)
      } else {
        const { dimensionWarningList = [], dimensionValueIndicatorWarningList = [] } = this.warningLineData
        const { dimensionMethod } = this.warningMethod
        this.$set(
          this.warningLineData,
          chartUserConfigKey,
          []
        )
      }
    },
    initDimensionValueIndocatorWarningData() {
      const chartUserConfigKey = 'dimensionValueIndicatorWarningList'
      if (!this.dimensionSettingShow[chartUserConfigKey]) return
      const warningSetted = this.chartUserConfig[chartUserConfigKey]?.length
      this.$set(
        this.warningLineData,
        chartUserConfigKey,
        warningSetted ? this.$_deepClone(this.chartUserConfig[chartUserConfigKey]) : []
      )
      this.warningLineData[chartUserConfigKey].forEach(w => {
        w.dimensionSetting.forEach(d => {
          this.getRemoteDimensionValueList(d, d.fieldColumnName)
        })
      })
    },
    initWarningMethod() {
      const { warningMethod, compareMethod } = this.chartUserConfig
      if (!this.isCard) {
        this.warningMethod.indicatorMethod = this.isCommonChart ? 2 : (compareMethod || 1)
        const { dimensionWarningList, dimensionValueIndicatorWarningList } = this.dimensionSettingShow
        if (dimensionWarningList && dimensionValueIndicatorWarningList) {
          this.warningMethod.dimensionMethod = warningMethod?.dimensionMethod || 3
        } else if (dimensionValueIndicatorWarningList) {
          this.warningMethod.dimensionMethod = 4
        } else {
          this.warningMethod.dimensionMethod = 3
        }
      }
    },
    initDimensionWarningList() {
      const chartUserConfigKey = 'dimensionWarningList'
      if (!this.dimensionSettingShow[chartUserConfigKey]) return

      let dimensionWarningList = this.$_deepClone(this.chartUserConfig.dimensionWarningList || [])
        .filter(d => this.metricsList.find(m => m.keyName === d.metricKeyName))
        .map(d => {
          const metric = this.metricsList.find(m => m.keyName === d.metricKeyName)
          d.metricAlias = metric.alias || metric.labeName
          d.warningList = d.warningList.filter(w => {
            return this.dimensionListOption.find(d => d.value === w.fieldKeyName)
          }).map(w => {
            return {
              ...w,
              fieldAlias: this.dimensionListOption.find(d => d.value === w.fieldKeyName).alias,
            }
          })
          d.warningList.forEach(w => {
            this.getRemoteDimensionValueList(w)
          })
          return d
        })
        .filter(d => d.warningList?.length)
      this.$set(this.warningLineData, chartUserConfigKey, dimensionWarningList)
    },
    addDetailSetting(type, cardIndex) {
      console.log(type);
      console.log(cardIndex);
      if (this.isCard) {
        const warnLineSettingList = this.warningLineData.warnLineSettingList[cardIndex].filter(item => item.cardWarnLineType === type)
        if (warnLineSettingList.length >= 5) return
        this.warningLineData.warnLineSettingList[cardIndex].push(this.genarateWarningItem(type))
      } else {
        if (this.warningLineData.warnLineSettingList.length >= 5) return
        this.warningLineData.warnLineSettingList.push(this.genarateWarningItem())
      }
    },
    deleteDetailSetting(lineId, cardIndex) {
      if (!this.isCard) return
      const warnLineArray = this.warningLineData.warnLineSettingList[cardIndex]
      if (Array.isArray(warnLineArray)) {
        this.$set(this.warningLineData.warnLineSettingList, cardIndex, this.$_JSONClone(warnLineArray.filter(item => item.id !== lineId)))
      }
    },
    getCurrentWarnLineList(item, cardWarnLineType, cardIndex) {
      let lineList = []
      item.forEach(line => {
        if (line.cardWarnLineType === cardWarnLineType) {
          if (!line.metricKeyName) {
            const fieldType = cardWarnLineType.includes(WARNLINE_TYPE_CONNECT_STR) ? cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)[0] : cardWarnLineType
            let alias = cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)[1]
            const cardContent = this.element.content.optionArray[cardIndex]
            if (cardContent && cardContent[fieldType]) {
              const metric = alias ? cardContent[fieldType].find(item => alias === (item.alias || item.labeName)) : cardContent[fieldType][0]
              if (metric.keyName) {
                line.metricKeyName = metric.keyName
                line.metric = metric.alias || metric.labeName
              }
            }
          }
          lineList.push(line)
        }
      })
      return {
        warnLineSettingList: lineList,
      }
    },
    // 卡片支持设置预警的值
    getCardWarningKeys(cardIndex) {
      if (!this.isCard) return []
      const { tagNewCardContent: cardType, optionArray } = this.element.content
      let keys = []
      const dimensionKeys = optionArray[cardIndex]?.dimension ? optionArray[cardIndex].dimension.map((item, index) => `dimension${WARNLINE_TYPE_CONNECT_STR}${item.alias || item.labeName}`) : []
      const compareValueKeys = optionArray[cardIndex]?.compareValue ? optionArray[cardIndex].compareValue.map((item, index) => `compareValue${WARNLINE_TYPE_CONNECT_STR}${item.alias || item.labeName}`) : []
      if (cardType === TAGNEWCARD.RATECARD) {
        keys = ['ratioValue', ...compareValueKeys, ...dimensionKeys, 'referenceValue']
      } else if (cardType === TAGNEWCARD.COMPARECARD) {
        keys = [...dimensionKeys, ...compareValueKeys]
      } else if (cardType === TAGNEWCARD.RANKCARD) {
        keys.push(...dimensionKeys, 'growthValue', 'growthRate')
      } else {
        keys.push(...dimensionKeys, 'growthValue', 'growthRate', 'complateRate')
      }
      return keys
    },
    changeCascader(isOpen) {
      this.$nextTick(() => {
        const component = this.$refs['warnLineSettings']
        if (Array.isArray(component)) {
          component.forEach(item => {
            item.changeCascader(isOpen)
          })
        } else if (component) {
          component.changeCascader(isOpen)
        }
      })
    },
    getWarningText(type, cardIndex) {
      const langObj = {
        dimension: this.$t('sdp.views.indexValue'),
        metrics: this.$t('sdp.views.targetVal'),
        ratioValue: this.$t('sdp.views.ratioValue'),
        referenceValue: this.$t('sdp.views.referenceValue'),
        compareValue: this.$t('sdp.views.compareValue'),
        growthValue: this.$t('sdp.views.growthValue'),
        growthRate: this.$t('sdp.views.growthRate'),
        complateRate: this.$t('sdp.views.complateRate'),
      }
      let key = type.split(WARNLINE_TYPE_CONNECT_STR)[0]
      let text = langObj[key]
      // 比率卡片指标值、比较卡片对比值特殊处理
      if ((key === 'dimension' && this.tagNewCardContent === TAGNEWCARD.RATECARD) || (key === 'compareValue' && this.tagNewCardContent === TAGNEWCARD.COMPARECARD)) {
        const cardContent = this.element.content.optionArray[cardIndex]
        if (!cardContent) return ''
        // 获取字段别名
        const alias = type.split(WARNLINE_TYPE_CONNECT_STR)[1] || ''
        debugger
        text += ': ' + alias
      }
      return text
    },
    // 获取可设置预警的字段
    getMetricsList() {
      if (this.isCard) {
        const keys = ['ratioValue', 'compareValue', 'dimension']
        const optionArray = this.element.content.optionArray
        let metricsList = []
        Array.isArray(optionArray) && optionArray.forEach(cardContent => {
          keys.forEach(item => cardContent[item] && metricsList.push(...cardContent[item]))
        })
        return metricsList
      }
      return this.chartUserConfig.metricsContainer.default
    },
    // 切换卡片类型或比率卡片拖维度字段时，需要修改预警线值
    changeCardWarningLine() {
      const { warnLineSettingList } = this.chartUserConfig
      if (!warnLineSettingList) return
      const newWarnLineList = []
      this.content.optionArray.forEach((card, index) => {
        const warnLineItem = warnLineSettingList[index]
        if (!warnLineItem) {
          newWarnLineList[index] = []
        } else {
          // 过滤不可转换预警线数据
          const cardWarningKeys = this.getCardWarningKeys(index)
          const line = warnLineItem.filter(item => cardWarningKeys.includes(item.cardWarnLineType))
          newWarnLineList[index] = line
        }
      })
      this.$set(this.chartUserConfig, 'warnLineSettingList', this.$_JSONClone(newWarnLineList))
    },
    hasSetLinear(colors) {
      return colors?.some(item => {
          if (Object.prototype.toString.call(item) === '[object Object]') {
          return item.type === 'gradient'
          }
          return false
        })
    },
  }
}
</script>

<style lang="scss" scoped>
.warningline /deep/{
  overflow-y: auto;
  max-height: 400px;
  font-size: 12px;
  .chart-design-splite-line{
    border-color: var(--sdp-cszj-bkfgx);
    border-bottom-width: 1px;
    border-bottom-style: solid;
    height: 1px;
    box-sizing: border-box;
  }
  .table-switch-label{
      font-size: 12px;
      line-height: 14px;
      display: inline-block;
      vertical-align: middle;
      color: var(--sdp-ycsz-rskbt);
      @include font-medium;
    }
  .dimension-warning-container{
    .line-item-button{
      color: var(--sdp-qcgls);
      cursor: pointer;
      font-size: 16px;
      line-height: 32px;
      margin-left: 20px;
    }
  }
  .icon-sdp-info{
    color: $color-main;
    font-weight: 400;
    font-size: 20px;
    margin-top: 1px;
  }
}
.warningline-title {
  margin: 14px 0 24px;
  overflow: hidden;
  > .warningline-title-icon {
    float: right;
    color: $color-main;
    cursor: pointer;
    > i {
      vertical-align: middle;
      font-size: 12px;
    }
  }
  > span:nth-child(1) {
    margin-right: 10px;
    font-family: PingFangSC-Semibold;
    font-size: 12px;
    letter-spacing: 0;
  }
}
.warningline-title-customer {
  margin: 0 0 16px;
  .warningline-title-icon{
    float: none;
    .icon-sdp-add{
      vertical-align: top;
    }
  }
}
.card-warningline-setting /deep/ {
  .warningline-title {
    margin: 14px 0 0;
    > span:nth-child(1) {
      font-weight: bold;
    }
  }
  .warnline-content-item:nth-child(1) {
    margin-top: 12px;
  }
  .el-tabs .el-tabs__header {
    margin-bottom: 0px;
  }
  .card-name {
    margin-top: 14px;
  }
  .content:last-child {
    .cutline {
      display: none;
    }
  }
  .el-tabs__item:not(.is-active){
    color: var(--sdp-cszjsz-wzs1);
  }
  .el-tabs__item,.is-active {
    color: var(--sdp-sztc-bts);
  }
  .el-tabs__nav-wrap::after{
    content: "";
    background-color: var(--sdp-cszj-bkfgx);
  }
}
.rate, .singleIndex, .compare, .rank {
  /deep/ .el-tabs__nav-scroll {
    display: none;
  }
}
.cutline {
  background-color: var(--sdp-ycfgx);
  height: 1px;
  margin-top: 12px;
}
/deep/ .multiple-select{
    .el-select__tags{
      pointer-events: none;
      display: none;
    }
    .el-input__inner{
      padding-left: 15px;
    }
    .el-input__prefix{
      left: 15px;
      height: 30px;
      background-color: transparent;
      color: var(--sdp-cszj-tswzs);
      border-color: transparent;
      // @extend .el-input__inner;
      .el-input__inner{
        border: 0;
        padding: 0;
        height: 30px;
        line-height: 30px !important;
      }
    }
}
</style>
