<template>
  <DialogRoot
    :title="onlyIndex ? $t('sdp.simSystem.showIndicator') : $t('sdp.views.showMeasures')"
    :visible="dialogVisible"
    width="1000px"
    @open="initData"
    @confirm="confirm"
    @close="closeDialog"
  >
    <div class="content">
      <div>{{ onlyIndex ? $t('sdp.simSystem.Indicator') : $t("sdp.views.measure") }}</div>
      <div
        class="measure-item-box"
        v-for="(item, index) in metricLabelDisplay"
        :key="index"
      >
        <div class="line-group-list">
          <LineGroup
            :ref="'LineGroup_1_' + item.keyName"
            :lineData="item"
            :params="{}"
            :customClass="['border-radius_2']"
            :lineConfig="getLineConfig_1(item, 'all', {})"
            :style="{ marginTop: '10px', padding: '12px 16px 0' }"
            @change="lineGroupChange"
          >
          </LineGroup>
          <LineGroup
            :ref="'LineGroup_3_' + item.keyName"
            :lineData="item"
            :params="{}"
            :customClass="['border-radius_2']"
            :lineConfig="getLineConfig_3(item, 'all', {})"
            :style="{padding: '0px 16px' }"
            @change="lineGroupChange"
          >
          </LineGroup>
        </div>
        <TableSwitch
          v-if="CUSTOM_METRIC.includes(chartUserConfig.chartAlias) && isShowMetricWithCustom"
          class="table-switch-customMetric"
          :formData="item"
          activeValue="custom"
          inactiveValue="origin"
          prop="labelType"
          :label="$t('sdp.views.customMetric')"
          @change="(prop, value) => changeMetricValue(prop, value, item)"
        ></TableSwitch>
        <template v-if="item.labelType === 'custom' && CUSTOM_METRIC.includes(chartUserConfig.chartAlias)">
          <div class="add-custom">
            <span :class="item.customMetricList.length > 9 ? 'add-custom-icon not-allowed' : 'add-custom-icon'" @click="addCustomMetric(item)"><i class="icon-sdp-add"></i> {{$t('sdp.views.add')}}</span>
          </div>
          <div class="line-group-list">
            <LineGroup
              v-for="(obj, index) in item.customMetricList"
              :key="obj.customMetric.keyName"
              :ref="'LineGroup_2_' + obj.customMetric.keyName"
              :lineData="obj"
              :params="{}"
              :lineConfig="getLineConfig_2(obj, 'all', {})"
              :style="{ padding: '12px 16px' }"
              @change="(key, val, lineData, params, lineItem) => lineGroupChange(key, val, lineData, params, lineItem, item, index)"
            >
            </LineGroup>
          </div>
        </template>

      </div>
    </div>
  </DialogRoot>
</template>

<script>
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog'
import { LineGroup, TableSwitch } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { DISPLAY_LABEL_PERCENT, CUSTOM_METRIC, METRIC_POSITION_CHART, CHART_ALIAS_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import {
  generateMetricLabelDisplayCustomItem,
  generateMetricLabelDisplayItem,
  generateViewFormat
} from 'packages/base/board/displayPanel/supernatant/chartSet/utils/generator'
import { generateKeyName } from '../../../../../../../../../assets/utils/globalTools'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
export default {
  mixins: [mixin_dialog, datasetMixin],
  inject: ['chart'],
  name: 'MetricLabelDisplayDialog',
  components: { LineGroup, TableSwitch },
  data() {
    return {
      CUSTOM_METRIC,
      metricLabelDisplay: [],
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    },
    isShowMetricPercent() {
      const bool = this.$getFeatureConfig?.('showMetricWithPercent.hidden')
      const { butterflySetting = {}, chartAlias } = this.chartUserConfig
      if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY && butterflySetting.dataPercent) return false
      return DISPLAY_LABEL_PERCENT.includes(this.chartUserConfig.chartAlias) && bool !== true
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig
    },
    isShowMetricWithCustom() {
      const bool = this.$getFeatureConfig?.('showMetricWithCustom.hidden')
      return bool !== true
    }
  },
  watch: {
    isShowMetricPercent: {
      handler(val) {
        if (!val && this.dialogVisible) {
          this.metricLabelDisplay.forEach((m) => {
            m.origin.showMetricPercent = false
          })
        }
      },
      immediate: true,
    },
  },
  methods: {
    changeMetricValue (prop, val, item) {
      if (val && !item.customMetricList?.length) {
        const customMetricObj = generateMetricLabelDisplayCustomItem()
        customMetricObj.customMetric.keyName = generateKeyName(customMetricObj.customMetric.labeName, 0)
        item.customMetricList.push({ ...customMetricObj, isNotShowIcon: true })
      }
    },
    lineGroupChange: function (key, val, dataItem, metricParams, lineItem, item, index) {
      if (key === 'labeName') {
        dataItem.customCascader = []
        dataItem.customMetric = {}
        this.$nextTick(() => {
          const fieldItem = this.chart.fieldList.find(f => (f.alias || f.labeName) === val)
          dataItem.customMetric = Object.assign({}, {
            ...fieldItem,
            keyName: this.$_generateKeyName(fieldItem.alias || fieldItem.labeName, 0),
          })
        })
      } else if (['format', 'decimals'].includes(key)) {
        dataItem.customMetric.viewFormat = generateViewFormat({
          format: dataItem.format,
          decimals: dataItem.decimals,
        })
      } else if (key === 'showMetricValue') {
        if (item) {
          // 自定义度量显示度量值勾选，剩余其他显示度量值取消勾选
          if (val) {
            if (item.origin.showMetricValue) item.origin.showMetricValue = false
            item.customMetricList.forEach((val, i) => {
              if (i !== index && val.showMetricValue) {
                val.showMetricValue = false
              }
            })
          }
        } else {
          if (dataItem.origin.showMetricValue) {
            dataItem.customMetricList.forEach(val => {
              if (val.showMetricValue) val.showMetricValue = false
            })
          }
        }
      } else if (key === 'showMetricPercent') {
        console.log('showMetricPercent', val)
        dataItem.origin.showMetricPercentLabel = val
        if(val) {
          this.$set(dataItem.origin, 'showMetricDimensionValPercent', val)
          this.$set(dataItem.origin, 'showMetricTotalPercent', !val)
        } else {
          this.$set(dataItem.origin, 'showMetricTotalPercent', false)
          this.$set(dataItem.origin, 'showMetricDimensionValPercent', false)
        }
      } else if(key === 'showMetricTotalPercent') {
        dataItem.origin.showMetricTotalPercent = true
        this.$set(dataItem.origin, 'showMetricDimensionValPercent', false)
      } else if(key === 'showMetricDimensionValPercent') {
        dataItem.origin.showMetricDimensionValPercent = true
        this.$set(dataItem.origin, 'showMetricTotalPercent', false)
      }else if (key === 'delete') {
        // 删除自定义度量
        item.customMetricList.splice(index, 1)
      }
    },
    addCustomMetric(item) {
      if (item.customMetricList.length > 9) return
      const customMetricObj = generateMetricLabelDisplayCustomItem()
      customMetricObj.customMetric.keyName = generateKeyName(customMetricObj.customMetric.labeName, 0)
      item.customMetricList.push({ ...customMetricObj, isNotShowIcon: false })
    },
    getLineConfig_1(dataItem, key, metricParams) {
      const showMetricPositionConfig = METRIC_POSITION_CHART.includes(this.chartUserConfig.chartAlias)
      const itemsArray = [
        {
          key: 'showMetricValue',
          model: dataItem.origin,
          label: this.getAliasByKeyName(dataItem.keyName),
          labelStyle: { fontSize: '12px', width: '436px', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' },
          innerLabel: this.onlyIndex ? this.$t('sdp.simSystem.showIndicatorValue') : this.$t('sdp.views.showMeasuresValue'),
          type: 'checkbox',
          style: { width: '170px', flexShrink: 0, marginRight: '24px' },
        },
        {
          key: 'metricLabelPosition',
          type: 'select',
          label: this.$t('sdp.views.DisplayPosition'),
          style: { width: '120px', flexShrink: 0, marginRight: '24px', visibility: this.isLineMetricPercent(dataItem) ? 'hidden' : 'visible', display: !showMetricPositionConfig ? 'none' : 'block', },
          options: [
            { value: 'top', label: this.$t('sdp.views.positionTop'), },
            { value: 'bottom', label: this.$t('sdp.views.positionBottom'), },
            { value: 'inside', label: this.$t('sdp.views.positionInside'), },
            { value: 'insideTop', label: this.$t('sdp.views.positionInsideTop'), },
            { value: 'insideBottom', label: this.$t('sdp.views.positionInsideBottom'), },
            { value: 'left', label: this.$t('sdp.views.positionLeft'), },
            { value: 'insideLeft', label: this.$t('sdp.views.positionInsideLeft'), },
            { value: 'right', label: this.$t('sdp.views.positionRight'), },
            { value: 'insideRight', label: this.$t('sdp.views.positionInsideRight'), },
            { value: 'insideTopLeft', label: this.$t('sdp.views.positionInsideTopLeft'), },
            { value: 'insideTopRight', label: this.$t('sdp.views.positionInsideTopRight'), },
            { value: 'insideBottomLeft', label: this.$t('sdp.views.positionInsideBottomLeft'), },
            { value: 'insideBottomRight', label: this.$t('sdp.views.positionInsideBottomRight'), },
          ],
          title: '',
        },
        {
          key: 'metricLabelRotationAngle',
          type: 'input-number',
          min: -360,
          max: 360,
          step: 10,
          label: this.$t('sdp.views.angleRotate'),
          style: { width: '132px', flexShrink: 0,  visibility: this.isLineMetricPercent(dataItem) ? 'hidden' : 'visible', display: !showMetricPositionConfig ? 'none' : 'block', },
        },
      ]
      return itemsArray
    },
    getLineConfig_3(dataItem, key, metricParams) {
      const notshowMetricPercentConfig = ['ve-stack-percent', 've-bar-stack-percent'].includes(this.chartUserConfig.chartAlias)
      const itemsArray = [
        {
          key: 'showMetricPercent',
          model: dataItem.origin,
          innerLabel: this.$t('sdp.views.percent'),
          type: 'checkbox',
          style: { width: '170px', flexShrink: 0, marginRight: '24px',  display: !this.isShowMetricPercent? 'none' : 'block'},
        },
        {
          key: 'showMetricTotalPercent',
          model: dataItem.origin,
          innerLabel: this.$t('sdp.views.totalProportion'),
          type: 'radio',
          activeValue: true,
          style: { width: '170px', flexShrink: 0, marginRight: '24px', display: notshowMetricPercentConfig ? 'none' : ''},
          innerStyle: !this.isShowMetricPercent || !dataItem.origin.showMetricPercent  ? { display: 'none' } : {},
        },
        {
          key: 'showMetricDimensionValPercent',
          model: dataItem.origin,
          innerLabel: this.$t('sdp.views.selectedDimensionVal'),
          type: 'radio',
          activeValue: true,
          style: { width: '250px', flexShrink: 0, marginRight: '24px', display: notshowMetricPercentConfig ? 'none' : ''},
          innerStyle: !this.isShowMetricPercent || !dataItem.origin.showMetricPercent ?  { display: 'none' } : {},
        },
        {
          key: 'showMetricPercentLabel',
          model: dataItem.origin,
          innerLabel: this.$t('sdp.views.labelDisplay'),
          type: 'checkbox',
          style: { width: '170px', flexShrink: 0, marginRight: '24px'},
          innerStyle: !this.isShowMetricPercent || !dataItem.origin.showMetricPercent ? { display: 'none' } : {},
        }
      ]
      return itemsArray
    },
    getLineConfig_2(dataItem, key, metricParams) {
      const itemsArray = [
        {
          key: 'customMetricAlias',
          label: this.$t('sdp.views.customMetricName'),
          type: 'input',
          maxlength: 30,
          style: { width: '170px', flexShrink: 0, },
        },
        {
          key: 'labeName',
          model: dataItem.customMetric,
          label: this.$t('sdp.views.fieldSelection'),
          type: 'select-field',
          style: { width: '110px', flexShrink: 0, marginLeft: '24px', },
          options: this.chart.fieldList,
          title: this.getUnknownName(dataItem.customMetric?.parentId, dataItem.customMetric?.labeName),
        },
        {
          key: 'customMetricCascader',
          type: 'input-dragcascader',
          style: { width: '152px', flexShrink: 0, marginLeft: '4px', },
          trimMaxWidth: 161,
          cascaderDragList: [dataItem.customMetric],
          cascaderItem: dataItem.customMetric,
          cascaderType: 'metric-short',
          cascaderCustomCascader: dataItem.customCascader,
          element: this.element,
        },
        {
          key: 'format',
          model: dataItem,
          label: this.$t('sdp.views.displayFormat'),
          type: 'select',
          style: { width: '130px', flexShrink: 0, marginLeft: '24px', },
          options: [
            { value: 'number', label: this.$t('sdp.views.number'), },
            { value: 'percent', label: this.$t('sdp.views.percentage'), },
            { value: 'currencyUnit', label: this.$t('sdp.views.currencyUnit'), },
          ],
        },
        {
          key: 'decimals',
          model: dataItem,
          type: 'select',
          style: { width: '142px', flexShrink: 0, marginLeft: '4px', marginRight: '24px', },
          options: [
            { value: 0, label: '0', },
            { value: 1, label: '0.0', },
            { value: 2, label: '0.00', },
            { value: 3, label: '0.000', },
            { value: 4, label: '0.0000', },
          ],
        },
        {
          key: 'showMetricValue',
          model: dataItem,
          label: this.onlyIndex ? this.$t('sdp.simSystem.showIndicatorValue') : this.$t('sdp.views.showMeasuresValue'),
          labelStyle: { fontSize: '12px', width: '80px', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' },
          type: 'checkbox',
          style: { width: '90px', flexShrink: 0, textAlign: 'center' },
        },
        {
          key: 'delete',
          type: 'button',
        }
      ]
      const itemMap = {
        origin: ['metricLabelPosition', 'metricLabelRotationAngle'],
      }
      if (dataItem.labelType === 'origin') return itemsArray.filter(m => itemMap.origin.includes(m.key))
      return itemsArray
    },
    isLineMetricPercent(val) {
      const { metricsContainer = {}, chartAlias } = this.chartUserConfig
      let lineMetric = []
      if (['ve-line-normal', 've-line-area'].includes(chartAlias)) {
        lineMetric = metricsContainer.default || []
      } else if (chartAlias === 've-composite') {
        lineMetric = metricsContainer?.line || []
      }
      return lineMetric.find((m) => m.keyName === val.keyName)
    },
    getAliasByKeyName(keyName) {
      const {
        metricsContainer = [],
        dimensionList = [],
        extendDimensionList = [],
      } = this.chartUserConfig
      const dimensionArray = [
        ...(metricsContainer.default || []),
        ...dimensionList,
        ...extendDimensionList,
      ]
      const thisDimenison = dimensionArray.find((d) => d.keyName === keyName)
      return this.getDatasetLabel(thisDimenison)
    },
    initData() {
      const { metricLabelDisplay = [], } = this.chartUserConfig
      const metrics = this.chartUserConfig.metricsContainer.default || []
      let selectMetric = []

      metrics.forEach((item) => {
        let curMetricLabel = metricLabelDisplay.find((metric) => metric.keyName === item.keyName)
        let metricLabelItem = this.$_JSONClone(curMetricLabel || generateMetricLabelDisplayItem({ chartUserConfig: this.chartUserConfig }))

        if (metricLabelItem.customMetricList?.length) {
          metricLabelItem.customMetricList.forEach(m => {
            m.customMetric?.alias && (delete m.customMetric.alias)
          })
        }

        !metricLabelItem.keyName && (metricLabelItem.keyName = item.keyName)
        metricLabelItem.origin.showMetricPercent = metricLabelItem.origin.showMetricPercent || false
        metricLabelItem.origin.showMetricDimensionValPercent = metricLabelItem.origin?.showMetricPercent && metricLabelItem.origin.showMetricTotalPercent ? false : metricLabelItem.origin?.showMetricPercent
        selectMetric.push(metricLabelItem)
      })
      this.metricLabelDisplay = selectMetric
    },
    confirm() {
      const aliasArray = []
      for (let i = 0; i < this.metricLabelDisplay.length; i++) {
        const currentItem = this.metricLabelDisplay[i]
        if (currentItem.labelType === 'custom') {
          for (let j = 0; j < currentItem.customMetricList.length; j++) {
            const customMetricItem = currentItem.customMetricList[j]
            const customMetricAlias = customMetricItem.customMetricAlias?.trim()
            if (!customMetricAlias || !customMetricItem.customMetric.labeName || !customMetricItem.customCascader[0]) {
              this.$message.warning(this.$t('sdp.views.plsFillData'))
              return
            }
            customMetricItem.customMetricAlias = customMetricAlias
            aliasArray.push(customMetricAlias)
          }
        }
      }
      const { dimensionList = [], metricsContainer = {} } = this.chartUserConfig
      const allList = [...dimensionList, ...(metricsContainer.default || [])]
      allList.forEach(p => {
        aliasArray.push(this.getDatasetLabel(p))
      })
      if (new Set(aliasArray).size !== aliasArray.length) {
        this.$message.warning(this.$t('sdp.views.repeatMetricAlias'))
        return
      }
      this.$set(
        this.chartUserConfig,
        'metricLabelDisplay',
        this.metricLabelDisplay
      )
      this.$emit('change')
      this.dialogVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.content {
  height: 500px;
  overflow-y: auto;
  .measure-item-box {
    & + .measure-item-box {
      margin-top: 16px;
      border-top-width: 1px;
      border-top-style: solid;
      border-color: var(--sdp-cszj-bkfgx);
    }
    .metric-name {
      display: block;
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .metric-controls {
      display: flex;
      /deep/ .el-input__inner{
        border-radius: 2px;
      }
    }
  }
  .checkbox {
    margin-top: 23px;
    .el-checkbox {
      margin-bottom: 18px;
    }
  }
  .table-switch-customMetric {
    height: 90px;
    display: inline-flex;
    margin: 0;
    padding-top: 10px;
    /deep/ .table-switch-label {
      margin-right: 10px;
    }
    /deep/ .el-switch {
      height: 20px;
    }
  }
  .add-custom {
    margin: 10px 0 10px;
    overflow: hidden;
    float: right;
    top: 64px;
    position: relative;
    > .add-custom-icon {
      float: right;
      color: var(--sdp-zs);
      cursor: pointer;
      margin-right: 10px;
      font-family: PingFangSC-Semibold;
      font-size: 12px;
      letter-spacing: 0;
      >i {
        font-size: 12px;
      }
    }
    >.not-allowed {
      cursor: not-allowed;
    }
  }
  .line-group-list {
    margin-top: 10px;
    border-radius: 4px;
    background-color: var(--sdp-fs1);
  }
}
</style>
