<template>
  <DialogRoot
    :title="onlyIndex ? $t('sdp.simSystem.indicatorSummary') : $t('sdp.views.measureSummary')"
    :visible="dialogVisible"
    width="900px"
    @open="initData"
    @confirm="confirm"
    @close="cancel"
  >
    <div>
      <!-- 新增 -->
      <div class="btn-add" @click="handleAdd">
        <i class="icon-sdp-xinzeng"></i>
        <span>{{ $t('sdp.button.add') }}</span>
      </div>
      <div class="switch-ignore" v-if="showIgnoreSwitch">
        <span>{{ $t('sdp.views.ignoreDimension') }}</span>
        <el-switch v-model="ignoreDimension"></el-switch>
      </div>
      <div class="row is-header" v-if="form.summaryList.length">
        <div class="rowitem metric">
          <span class="title">{{$t('sdp.views.measure')}}</span>
        </div>
        <div class="rowitem summary-value">
          <span class="title">{{$t('sdp.views.calculationLogic')}}</span>
        </div>
        <div class="rowitem summary-alias">
          <span class="title">{{ onlyIndex ? $t('sdp.simSystem.IndicatorSummaryName') : $t('sdp.views.MeasurementSummaryName')}}</span>
        </div>
        <div class="rowitem text-style">
          <span class="title">{{$t('sdp.views.font')}}</span>
        </div>
        <div class="rowitem action">
          <span class="title">{{$t('sdp.views.operation')}}</span>
        </div>
      </div>
      <div
        class="row"
        v-for="(item, index) in form.summaryList"
        :key="item.keyName"
      >
        <div class="rowitem metric">
          <el-select :popper-class="getCurrentThemeClass()" v-model="item.keyName" :placeholder="$t('sdp.placeholder.plsInput')" clearable>
            <el-option
              v-for="listItem in metricList"
              :disabled="!!form.summaryList.find(r => r.keyName === listItem.keyName && r.keyName !== item.keyName)"
              :key="listItem.keyName"
              :label="getDatasetLabel(listItem)"
              :value="listItem.keyName">
            </el-option>
          </el-select>
        </div>
        <div class="rowitem summary-value">
          <el-select
            :popper-class="getCurrentThemeClass()"
            v-model="item.summaryValue"
            :placeholder="$t('sdp.placeholder.plsInput')"
            clearable
            @change="handleSummaryValueChange(item)"
          >
            <el-option
              v-for="listItem in measureSummarylist"
              :key="listItem.value"
              :label="listItem.label"
              :value="listItem.value">
            </el-option>
          </el-select>
        </div>

        <!-- 度量汇总名称 -->
        <div class="rowitem summary-alias">
          <el-input
            v-model="item.summaryAlias"
            :placeholder="$t('sdp.placeholder.pls')"
            maxlength="30"
          />
        </div>

        <div class="rowitem text-style">
          <!-- 样式设置 -->
          <FontSetting
            :fontStyle="item.subtextStyle"
            style="margin-bottom: 6px;"
            type="font"
            :themeType="themeType"
            default="item.subtextStyle.color"
          ></FontSetting>
        </div>
        <div class="rowitem action">
          <i :title="item.visible ? $t('sdp.views.visible') : $t('sdp.views.invisible')" class="icon-visible" :class="item.visible ? 'icon-sdp-Hxianshi' : 'icon-sdp-hide'" @click="toggleVisible(index)"></i>
          <i :title="$t('sdp.button.delete')" class="icon-sdp-danceng_shanchu icon-delete" @click="deleteRow(item)"></i>
        </div>
      </div>
    </div>
  </DialogRoot>
</template>

<script>
import mixin_dialog from 'packages/base/board/displayPanel/supernatant/chartSet/common/mixins/mixin_dialog'
import { getThemeConfig } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/theme'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'

export default {
  name: 'MeasurSummaryDialog',
  mixins: [mixin_dialog, componentMixin],
  props: {
    datasetList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        summaryList: [],
      },
      ignoreDimension: false
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig
    },
    measureSummarylist() {
      const options = [
        { label: this.$t('sdp.views.measureSum'), value: 'Sum' },
        { label: this.$t('sdp.views.measureMax'), value: 'Max' },
        { label: this.$t('sdp.views.measureMin'), value: 'Min' },
        { label: this.$t('sdp.views.measureAvg'), value: 'Avg' },
        { label: this.$t('sdp.views.measureRecently'), value: 'Recently' }
      ]
      const dateDimensionList = this.dimensionList.filter(d => d.columnTpe === 'date')

      if (dateDimensionList.length === 1) return options
      return options.filter(o => o.value !== 'Recently')
    },
    showIgnoreSwitch() {
      if (!this.form.summaryList.length) return false
      if (!this.form.summaryList.some(item => item.summaryValue === 'Sum')) return false
      return true
    }
  },
  watch: {
    showIgnoreSwitch(val, oldVal) {
      if (val === oldVal) return
      if (!val) {
        this.ignoreDimension = false
      }
    }
  },
  created() {
    this.confirm = this.$_debounce(this.confirm)
  },
  methods: {
    handleAdd() {
      const list = this.form.summaryList
      if (list.length >= this.metricList.length) return
      const restField = this.metricList.find(item => !list.find(f => f.keyName === item.keyName))
      const measureSummaryConfig = getThemeConfig(this.themeType, { attributes: ['measureSummaryConfig'] }).measureSummaryConfig
      const o = {
        keyName: restField ? restField.keyName : '',
        summaryValue: 'Sum',
        summaryAlias: this.$t('sdp.views.measureSumShow'),
        ignoreDimension: false,
        subtext: '',
        subtextStyle: {
          ...measureSummaryConfig.subtextStyle
        },
        visible: true
      }
      list.push(o)
    },
    handleSummaryValueChange(item) {
      const alias = this.$t(`sdp.views.measure${item.summaryValue}Show`)
      this.$set(item, 'summaryAlias', item.summaryValue ? alias: '')
    },
    toggleVisible(index) {
      const list = this.form.summaryList
      const row = list[index]
      row.visible = !row.visible
    },
    deleteRow(row) {
      const list = this.form.summaryList
      this.form.summaryList = list.filter(item => item.keyName !== row.keyName)
    },
    initData() {
      const { measureConfig = {} } = this.chartUserConfig
      const { summaryList = [] } = measureConfig
      if (!summaryList.length) return (this.form.summaryList = [])
      let _ignoreDimension = false
      this.form.summaryList = summaryList.map(item => {
        if (item.ignoreDimension) _ignoreDimension = item.ignoreDimension
        const measureSummaryConfig = getThemeConfig(this.themeType, { attributes: ['measureSummaryConfig'] }).measureSummaryConfig
        return {
          ...item,
          subtextStyle: {
            ...item.subtextStyle,
            color: item.subtextStyle.color || measureSummaryConfig.subtextStyle.color
          }
        }
      })
      this.ignoreDimension = _ignoreDimension
    },
    confirm() {
      let canSave = true
      this.form.summaryList.forEach(item => {
        if (!item.keyName || !item.summaryValue) {
          canSave = false
        }
        item.ignoreDimension = this.ignoreDimension
      })
      if (!canSave) return this.$message.warning(this.$t('sdp.views.plsFillComplete'))
      const { measureConfig } = this.chartUserConfig
      this.$set(this.chartUserConfig, 'measureConfig', { ...measureConfig, ...this.form })
      this.cancel()
      this.$emit('confirm')
    },
    cancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog__wrapper /deep/ {
  .el-dialog {
    .el-dialog__body {
      padding: 20px 24px;
      .btn-add{
        margin-right: 15px;
        display: inline-flex;
        justify-content: flex-start;
        align-items: center;
        color: var(--sdp-zs);
        cursor: pointer;
        i{
          margin-right: 8px;
        }
        span{
          line-height: 17px;
        }
      }
      .switch-ignore{
        margin-top: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        >span{
          margin-right: 10px;
          color: var(--sdp-xxbt1);
        }
      }
      .row{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        &.is-header{
          .rowitem{
            padding: 18px 0 4px 0;
            height: inherit;
            line-height: inherit;
            font-weight: 500;
            font-size: 12px;
            color: var(--sdp-xxbt1);
          }
        }
      }
      .rowitem {
        padding: 4px 0;
        display: flex;
        align-items: center;
        height: 32px;
        box-sizing: content-box;
      }
      .metric {
        width: 180px;
        .el-select {
          width: 100%;
        }
      }
      .summary-value {
        width: 150px;
        .el-select {
          width: 100%;
        }
      }
      .summary-alias {
        width: 170px;
      }
      .text-style{
        width: 230px;
        .font-setting-container{
          height: 32px;
          .el-select{
            margin-right: 8px;
            .el-input__inner{
              height: 100%;
            }
          }
          .sdp-number-autocomplete.font-size-set{
            width: 60px !important;
          }
          .el-autocomplete{
            margin-right: 8px;
            height: 100%;
          }
        }
      }
      .icon-visible{
        margin-right: 16px;
        font-size: 28px;
        color: var(--sdp-zs);
        cursor: pointer;
        width: 28px;
        text-align: center;
      }
      .icon-sdp-Hxianshi{
        font-size: 21px;
        display: block;
      }
      .icon-delete{
        color: #DC0600;
        font-size: 16px;
        cursor: pointer;
      }
      .action{
        width: 80px;
      }
    }
    .el-dialog__footer {
      padding-top: 0;
      .dialog-footer {
        display: block;
        text-align: right;
      }
    }
  }
}
</style>
