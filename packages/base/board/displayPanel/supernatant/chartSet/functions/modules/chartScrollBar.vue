<template>
  <div class="chart-scroll-bar" v-if="componentShow">
    <!-- 滚动条设置 -->
    <TableSwitch
      :formData="chartUserConfig"
      :disabled="isSetAnimation"
      prop="scrollBarShow"
      @change="(value) => scrollBarHandler('CHART_SCROLL_BAR_SHOW', value)"
    >
      <ItemLabel slot="label" :label="$t('sdp.views.scrollBar')" :level="4"></ItemLabel>
    </TableSwitch>
    <div class="item-config-container mt-16" v-if="showDataNumberSetting()">
      <ItemLabel slot="title" :label="$t('sdp.views.dataNumberPerPage')" icon :tips="onlyIndex ? $t('sdp.simSystem.scrollBarTips') : $t('sdp.views.scrollBarTips')"></ItemLabel>
      <el-input-number
        v-model="dataNumberPerPage"
        size=""
        :min="1"
        @change="scrollBarHandler('CHART_SCROLL_DATA_NUMBER')"
        controls-position="right"
        step-strictly>
      </el-input-number>
    </div>
  </div>
</template>

<script>
import { SCROLL_BAR_CHART } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { isScrollAccordingToDimension } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/chartSetting/handleChartSetting'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'
import element_showhide_mixins from 'packages/base/board/displayPanel/supernatant/chartSet/components/element_showhide_mixins'

export default {
  mixins: [componentMixin, element_showhide_mixins],
  name: 'ChartScrollBar',
  inject: {
    utils: { default: {} },
  },
  data() {
    return {
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    },
    componentShow() {
      return !this.utils.isMobile && SCROLL_BAR_CHART.includes(this.chartUserConfig.chartAlias)
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig || {}
    },
    isSetAnimation() {
      return this.chartUserConfig?.animationSetting?.enable
    },
    dataNumberPerPage: {
      get() {
        return this.chartUserConfig.dataNumberPerPage
      },
      set(val) {
        this.$set(this.chartUserConfig, 'dataNumberPerPage', val)
      }
    },
  },
  watch: {},
  methods: {
    scrollBarHandler(prop, value) {
      this.eventEmitHandler(prop, value)
    },
    showDataNumberSetting() {
      const { chartUserConfig, chartSettings } = this.element.content
      const { scrollBarShow, extendDimensionList = [] } = chartUserConfig
      const isDimensionDataScroll = isScrollAccordingToDimension({ chartUserConfig, chartSettings })
      return scrollBarShow && (!extendDimensionList.length || isDimensionDataScroll)
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-scroll-bar /deep/ {
  .el-input-number {
    width: 75px !important;
  }
}
</style>
