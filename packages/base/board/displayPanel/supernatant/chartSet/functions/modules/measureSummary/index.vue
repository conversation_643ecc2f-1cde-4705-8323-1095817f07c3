<template>
  <!-- 度量汇总 -->
  <div v-if="componentShow" class="chart-measure-summary-container">
    <SdpCollapse :title="onlyIndex ? $t('sdp.simSystem.indicatorSummary') : $t('sdp.views.measureSummary')">
      <!-- 度量汇总 -->
      <TableSwitch :formData="chartUserConfig.measureConfig" prop="hide" :label="onlyIndex ? $t('sdp.simSystem.indicatorSummary') : $t('sdp.views.measureSummary')" @change="hideMeasureConfigHandler"></TableSwitch>

      <template v-if="chartUserConfig.measureConfig.hide && !isMultiMetrics">
        <!-- 样式设置 -->
        <FontSetting
          :fontStyle="chartUserConfig.measureConfig.subtextStyle"
          style="margin-bottom: 6px;"
          type="font"
          :themeType="themeType"
          default="measureSummaryConfig.subtextStyle.color"
          @change="subtextStyleHandler"
        ></FontSetting>
        <!-- 汇总类型 -->
        <ChartSelect style="width: 100%;" v-model="chartUserConfig.measureConfig.measureSummaryValue" @change="measureSummaryValueHandler">
          <el-option
            v-for="item in measureSummarylist"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </ChartSelect>

        <!-- 度量汇总值名称 -->
        <template>
          <div>
            <ItemLabel :label="onlyIndex ? $t('sdp.simSystem.IndicatorSummaryName') : $t('sdp.views.MeasurementSummaryName')" :level="4" display="block"></ItemLabel>
            <el-input
              v-model="chartUserConfig.measureConfig.measureSummaryAlias"
              :placeholder="$t('sdp.placeholder.pls')"
              size="mini"
              maxlength="30"
              @blur="handleAliasBlur"
            />
          </div>
        </template>

        <template v-if="displayPositionShow">
          <!-- 汇总值显示位置 -->
          <ItemLabel :label="$t('sdp.views.value_position')" :level="4" display="block"></ItemLabel>
          <RadioGroup v-model="chartUserConfig.measureConfig.displayPosotion" type="MeasureSummaryDisplayPosition" @on-change="displayPosotionHandler"></RadioGroup>
          <template v-if="chartUserConfig.measureConfig.displayPosotion === 'disCenter'">
            <!-- 值位置 -->
            <ItemLabel :label="$t('sdp.views.sum_value_display_position')" :level="4" display="block"></ItemLabel>
            <RadioGroup v-model="chartUserConfig.measureConfig.valueDirection" type="MeasureSummaryValueDirection" @on-change="valueDirectionHandler"></RadioGroup>
          </template>
        </template>
        <TableSwitch v-if="chartUserConfig.measureConfig.measureSummaryValue === 'Sum' && (!$getFeatureConfig || !$getFeatureConfig('ignoreDimension.hidden'))" :formData="chartUserConfig.measureConfig" prop="ignoreDimension" :label="$t('sdp.views.ignoreDimension')" @change="ignoreDimensionHandler"></TableSwitch>
      </template>
      <div v-if="chartUserConfig.measureConfig.hide && isMultiMetrics" class="item-config-container">
        <ItemLabel :label="$t('sdp.views.Setup')" :level="4"></ItemLabel>
        <i :class="['icon-sdp-guizeyinqing', 'cursor-icon']" @click="openSetDialog"></i>
      </div>
      <SetDialog
        ref="SetDialog"
        :element="element"
        :themeType="themeType"
        :datasetList="datasetList"
        :visible.sync="dialogVisible"
        @confirm="confirm"
      ></SetDialog>
    </SdpCollapse>
  </div>
</template>

<script>
import { GRAPHARR, MULTI_GRAPHARR, MEARSURE_DIS_CENTER } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { getThemeConfig } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/theme'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'
import SetDialog from './dialog/setDialog.vue'
import SdpCollapse from 'packages/base/board/displayPanel/supernatant/chartSet/components/components/SdpCollapse'

export default {
  mixins: [componentMixin],
  name: 'MeasureSummary',
  props: ['datasetList'],
  inject: {
    utils: { default: {} },
  },
  components: { SetDialog, SdpCollapse },
  data() {
    return {
      dialogVisible: false
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    },
    componentShow() {
      const { chartSettings } = this.element.content
      const { chartAlias, extendDimensionList = [], measureConfig } = this.chartUserConfig

      if (!GRAPHARR.includes(chartAlias) || !measureConfig) return false

      // 双维度、维度扩展不需要度量汇总
      if (this.dimensionList.length > 1 || extendDimensionList.length) return false

      const { dimension = [] } = chartSettings
      if (dimension.length !== 1) return false

      if (['ve-composite', 've-grid-normal', 've-themeRiver'].includes(chartAlias)) return false

      return true
    },
    isMultiMetrics() {
      const { chartSettings } = this.element.content
      const { metrics = [] } = chartSettings
      return metrics.length > 1
    },
    displayPositionShow() {
      return MEARSURE_DIS_CENTER.includes(this.chartUserConfig.chartAlias)
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig || {}
    },
    measureSummarylist() {
      const options = [
        { label: this.$t('sdp.views.measureSum'), value: 'Sum' },
        { label: this.$t('sdp.views.measureMax'), value: 'Max' },
        { label: this.$t('sdp.views.measureMin'), value: 'Min' },
        { label: this.$t('sdp.views.measureAvg'), value: 'Avg' },
        { label: this.$t('sdp.views.measureRecently'), value: 'Recently' }
      ]
      const dateDimensionList = this.dimensionList.filter(d => d.columnTpe === 'date')

      if (dateDimensionList.length === 1) return options
      return options.filter(o => o.value !== 'Recently')
    },
  },
  watch: {
    componentShow(val) {
      const { measureConfig } = this.chartUserConfig
      const { metrics = [], dimension = [] } = this.element.content.chartSettings
      if (!measureConfig) return
      if ((metrics.length === 0 || dimension.length === 0) && val) {
        const measureDefultConfig = getThemeConfig(
          this.themeType,
          {
            attributes: ['measureSummaryConfig'],
          }
        ).measureSummaryConfig
        Object.assign(measureConfig, measureDefultConfig)
      }
      const dateDimensionList = this.dimensionList.filter(d => d.columnTpe === 'date')
      if (this.dimensionList.length && !measureConfig.subtext) {
        const measureSummaryValue = measureConfig.measureSummaryValue || (dateDimensionList.length === 1 ? 'Recently' : 'Sum')
        Object.assign(measureConfig, { measureSummaryValue })

        const alias = this.$t(`sdp.views.measure${measureSummaryValue}Show`)
        Object.assign(measureConfig, { measureSummaryAlias: alias })
      }
    },
    dimensionList(val) {
      const dateDimensionList = val.filter(d => d.columnTpe === 'date')
      const isDate = dateDimensionList.length === 1
      const measureConfig = this.chartUserConfig.measureConfig || {}
      // Bugfix: 69102 【图表】将日期维度换成普通维度，度量汇总应取汇总值
      if (!isDate && measureConfig.measureSummaryValue === 'Recently') {
        measureConfig.measureSummaryValue = 'Sum'
        measureConfig.measureSummaryAlias = this.$t(`sdp.views.measure${measureConfig.measureSummaryValue}Show`)
      }
    }
  },
  methods: {
    setDefault() {
      const dateDimensionList = this.dimensionList.filter(d => d.columnTpe === 'date')
      const isDate = dateDimensionList.length === 1

      const measureConfig = this.chartUserConfig.measureConfig || {}

      measureConfig.measureSummaryValue = measureConfig.measureSummaryValue || (isDate ? 'Recently' : 'Sum')
    },
    handleAliasBlur() {
      const val = this.chartUserConfig.measureConfig.measureSummaryAlias
      this.$set(this.chartUserConfig.measureConfig, 'measureSummaryAlias', val.trim())

      this.eventEmitHandler('MEASURE_SUMMARY_VALUE_ALIAS', this.chartUserConfig.measureConfig.measureSummaryAlias)
    },
    hideMeasureConfigHandler(val) {
      this.setDefault()
      this.eventEmitHandler('MEASURE_SUMMARY_OPEN', val)
    },
    subtextStyleHandler(val) {
      this.eventEmitHandler('MEASURE_SUMMARY_STYLE', this.chartUserConfig.measureConfig)
    },
    measureSummaryValueHandler(value) {
      const alias = this.$t(`sdp.views.measure${this.chartUserConfig.measureConfig.measureSummaryValue}Show`)
      if (alias) {
        this.$set(this.chartUserConfig.measureConfig, 'measureSummaryAlias', alias)
      }
      this.eventEmitHandler('MEASURE_SUMMARY_METHOD', value)
    },
    displayPosotionHandler(value) {
      this.eventEmitHandler('MEASURE_SUMMARY_POSITION', value)
    },
    valueDirectionHandler(value) {
      this.eventEmitHandler('MEASURE_SUMMARY_VALUE_POSITION', value)
    },
    ignoreDimensionHandler(value) {
      this.eventEmitHandler('MEASURE_SUMMARY_IGNORE_DIMENSION', value)
    },
    openSetDialog(val) {
      this.dialogVisible = true
    },
    confirm(val) {
      this.eventEmitHandler('MULTI_MEASURE_CONFIG_CHANGE', val)
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
