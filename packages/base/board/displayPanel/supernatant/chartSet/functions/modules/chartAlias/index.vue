<template>
  <!-- 图表类型 -->
  <div class="chart-alias-container">
    <div class="chart-icon-box">
      <el-tooltip
        effect="dark"
        placement="top"
        v-for="(item, index) in chartAliasList"
        popper-class="chart-el-tooltip__popper"
        :key="index"
      >
        <div slot="content" style="white-space: nowrap">
          {{ `${item.cnName}:${getImgTitle(item.alias)}` }}
        </div>
        <div
          :class="{
            'svg-box': true,
            'is-actived': chartAlias === item.alias || currentCategory.alias === item.alias,
            'arrow-down': childTypes.length && item.alias === chartAlias,
            'deactive-img': isChartDisable(item.alias)._isChartDisable,
          }"
          @click="switchChartType(item, isChartDisable(item.alias)._isChartDisable)"
        >
          <svg class="icon svg-icon" aria-hidden="true">
            <template v-if="!isChartDisable(item.alias)._isChartDisable">
              <use :xlink:href="'#icon-sdp-' + item.alias + '-active'"></use>
            </template>
            <template v-else>
              <use :xlink:href="'#icon-sdp-' + item.alias"></use>
            </template>
          </svg>
        </div>
      </el-tooltip>
    </div>
    <!-- 子分类 -->
    <template v-if="categoryTypes.length">
      <div class="first-font-color">
        {{ $t("sdp.views.subtypes") }}
      </div>
      <div class="children-container">
        <el-tooltip
          effect="dark"
          placement="top"
          v-for="(item, index) in categoryTypes"
          popper-class="chart-el-tooltip__popper"
          :key="index"
        >
          <div slot="content" style="white-space: nowrap">
            {{ `${item.cnName}:${getImgTitle(item.alias)}` }}
          </div>
          <div
            :class="{
              'svg-box': true,
              'is-actived': chartAlias === item.alias,
              'arrow-down': childTypes.length && item.alias === chartAlias,
              'deactive-img': isChartDisable(item.alias)._isChartDisable,
            }"
            @click="switchChartType(item, isChartDisable(item.alias)._isChartDisable)"
          >
            <svg class="icon svg-icon" aria-hidden="true">
              <template v-if="!isChartDisable(item.alias)._isChartDisable">
                <use :xlink:href="'#icon-sdp-' + item.alias + '-active'"></use>
              </template>
              <template v-else>
                <use :xlink:href="'#icon-sdp-' + item.alias"></use>
              </template>
            </svg>
          </div>
        </el-tooltip>
      </div>
    </template>
    <template v-if="childTypes.length">
      <div class="first-font-color" style="margin-top: 8px">
        {{ $t("sdp.views.subtypes") }}
      </div>
      <div class="children-container">
        <el-tooltip
          effect="dark"
          placement="top"
          v-for="(child, index) in childTypes"
          popper-class="chart-el-tooltip__popper"
          :disabled="!child.i18n"
          :key="index"
        >
          <div slot="content" style="white-space: nowrap">
            {{ $t(child.i18n) }}
          </div>
          <div
            :class="{
              'svg-box': true,
              'is-actived': childChartAlias === child.typeName,
            }"
            :key="index"
            @click="
              changeChildrenType(
                child,
                chartAlias,
                childTypeImgSrc(child.typeName)._isTypeDisable
              )
            "
          >
            <template v-if="child.typeName === 've-map-china'">
              <img class="icon img-icon" v-if="!childTypeImgSrc(child.typeName)._isTypeDisable" :src="chartUrlMap[child.typeName].active" alt="">
              <img class="icon img-icon" v-else :src="chartUrlMap[child.typeName].normal" alt="">
            </template>
            <svg v-else class="icon svg-icon" aria-hidden="true">
              <template v-if="!childTypeImgSrc(child.typeName)._isTypeDisable">
                <use
                  :xlink:href="'#icon-sdp-' + child.typeName + '-active'"
                ></use>
              </template>
              <template v-else>
                <use :xlink:href="'#icon-sdp-' + child.typeName"></use>
              </template>
            </svg>
          </div>
        </el-tooltip>
      </div>
    </template>
  </div>
</template>
<script>
import {
  HAS_TARGET_CHART,
  SPEICAL_DATA_STRUCTURECHART,
  EXTEND_DIMENSION_CHART,
  CHART_ALIAS_TYPE,
  DATASET_ASSOCIATION_CHART,
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import {
  getChartsList,
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/charts'
import chartUrlMap from './svg'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'
import { hideChartOfMobile } from '../../../../boardElements/elementChart/constant'

export default {
  mixins: [componentMixin],
  name: 'ChartAlias',
  inject: ['utils', 'isPending', 'chart'],
  props: {
  },
  data() {
    return {
      hideChartOfMobile,
      chartUrlMap,
      currentCategory: {}
    }
  },
  watch: {
    chartAlias: {
      handler(val, oldVal) {
        if (val && val !== oldVal) {
          const chartList = getChartsList()
          const o = chartList.find(item => item.alias === val)
          console.log('????', o, this.currentCategory)
          if (o?.category) {
            this.currentCategory = o
          } else if (o?.belong) {
            this.currentCategory = chartList.find(item => item.category === o.belong)
          } else {
            this.currentCategory = {}
          }
        }
      },
      immediate: true
    }
  },

  computed: {
    chartUserConfig() {
      return this.element.content?.chartUserConfig || {}
    },
    chartAlias() {
      return this.chartUserConfig.chartAlias
    },
    isChildMapScheme() {
      const fn = this.chart.isChildMapScheme
      return fn && fn()
    },
    childChartAlias() {
      return this.chartUserConfig.childChartAlias
    },
    extendDimensionList() {
      return this.chartUserConfig.extendDimensionList || []
    },
    chartAliasList() {
      let chartList = getChartsList()
      const cfg = this.$getFeatureConfig?.('chartTypes')
      if (cfg) {
        const { hidden = [] } = cfg
        chartList = chartList.filter(item => !hidden.includes(item.alias))
      }
      let hasSubChart = []
      let noSubChart = []
      chartList.forEach((c) => {
        if (c.children && Array.isArray(c.children) && c.children.length) {
          hasSubChart.push(c)
        } else {
          if (c.category || !c.belong) {
            noSubChart.push(c)
          }
        }
      })
      const arr = noSubChart.concat(hasSubChart)
      return arr.filter(
        (e) =>
          !(this.utils.isMobile && this.hideChartOfMobile.includes(e.alias))
      )
    },
    hasDimensionAndMetrics() {
      return this.dimensionList.length || this.metricList.length
    },
    childTypes() {
      let chart = this.chartAliasList.find((ca) => ca.alias === this.chartAlias)
      if (chart && !this.isChartDisable(this.chartAlias)._isChartDisable) {
        return chart.children || []
      } else {
        return []
      }
    },
    categoryTypes() {
      let chart = this.chartAliasList.find((ca) => ca.alias === this.currentCategory.alias)
      if (!chart?.category) return []
      const chartList = getChartsList()
      const list = chartList.filter(item => item.belong === chart.category)
      return [chart].concat(list)
    },
  },

  methods: {
    switchChartType(chart, flag) {
      if (flag) return
      if (this.chartUserConfig.chartAlias === chart.alias) return
      const _disable =
        [...HAS_TARGET_CHART, ...SPEICAL_DATA_STRUCTURECHART].includes(
          chart.alias
        ) && this.isPending()
      if (_disable) {
        this.$message({
          message: this.$t('sdp.views.awaitChartData'),
          type: 'warning',
        })
        return
      }
      this.eventEmitHandler('CHART_ALIAS', chart)
      if (chart.category) {
        this.currentCategory = chart
      } else if (chart.children?.length || (!chart.category && !chart.belong)) {
        this.currentCategory = {}
      }
    },
    changeChildrenType(child, parentAlias = this.chartAlias, isDisabled) {
      if (isDisabled) return
      if (this.chartUserConfig.childChartAlias === child.typeName) return
      this.eventEmitHandler(
        'CHILD_CHART_ALIAS',
        { alias: parentAlias, childType: child }
      )
    },
    childTypeImgSrc(typeName) {
      let result = {}
      // 仪表盘的子类型
      if (this.chartAlias === 've-gauge-normal') {
        result._isTypeDisable = false
      } else if (this.chartAlias === 've-map-parent') {
        if (this.isChildMapScheme) {
          result._isTypeDisable =
            this.childChartAlias !== typeName
        } else {
          result._isTypeDisable = false
        }
      }
      return result
    },
    isChartDisable(chartName) {
      if (this.isChildMapScheme) { return { _isChartDisable: chartName !== 've-map-parent' } }
      let _isChartActive = false
      if (!this.hasDimensionAndMetrics) {
        return { _isChartDisable: !_isChartActive }
      }
      const doubleDimensionChart =
        this.dimensionList.length && this.dimensionList.length < 3
      const metricList = this.metricList.filter((m) => !m.isGridCustom)
      const hasExtendDimensionList = this.chartUserConfig.extendDragBoxShow
      switch (chartName) {
        case 've-line-normal':
        case 've-line-area':
        case 've-histogram-stack':
        case 've-bar-normal':
        case 've-histogram-normal':
        case 've-pictorialbar':
        case CHART_ALIAS_TYPE.VE_STACK_PERCCENT:
        case CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT:
          _isChartActive = doubleDimensionChart
          break
        case 've-bar-stack':
          _isChartActive =
            doubleDimensionChart && this.extendDimensionList.length <= 1
          break
        case 've-composite':
          _isChartActive = doubleDimensionChart
          break
        case CHART_ALIAS_TYPE.VE_LIQUIDFILL:
          _isChartActive =
          (this.dimensionList.length === 1 && metricList.length < 2) || (metricList.length === 1 && this.dimensionList.length === 0)
          break
        case 've-pie-rose':
        case 've-ring-normal':
        case 've-ring-multiple':
        case 've-pie-normal':
        case 've-map-parent':
        case 've-wordcloud':
        case 've-calendar':
        case 've-roundCascades':
        case CHART_ALIAS_TYPE.VE_BANDWIDTH:
          _isChartActive =
            this.dimensionList.length === 1 && metricList.length < 2
          break
        case 've-treemap':
        case 've-bar-percent':
        case 've-tree':
        case 've-sunburst':
        case 've-themeRiver':
        case 've-bar-Heatmap':
          _isChartActive = doubleDimensionChart && metricList.length < 2
          break
        case 've-scatter-normal':
          _isChartActive = doubleDimensionChart && metricList.length < 4
          break
        case 've-radar':
          _isChartActive = this.dimensionList.length === 1
          break
        case 've-gauge-normal':
          _isChartActive = !this.dimensionList.length && metricList.length < 2
          break
        case 've-funnel':
          _isChartActive =
            (this.dimensionList.length < 2 && metricList.length <= 2) ||
            !this.dimensionList.length
          break
        case 've-grid-normal':
          _isChartActive = true
          break
        case 've-waterfall':
          _isChartActive =
            (!this.dimensionList.length && metricList.length > 1) ||
            (doubleDimensionChart && metricList.length < 2)
          break
        case CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY:
          _isChartActive = this.dimensionList.length === 1 && metricList.length <= 2
          break
        case CHART_ALIAS_TYPE.VE_DECOMPOSITION:
          _isChartActive = this.dimensionList.length <= 5 && metricList.length <= 1
          break
      }
      // 如果图形类型是简单表格且有两个日期维度，其他图形禁止点击
      if (this.chartAlias === 've-grid-normal' && chartName !== 've-grid-normal') {
        const arr = this.dimensionList.filter((e) => e.columnTpe === 'date')
        if (arr.length > 1) {
          _isChartActive = false
        }
      }
      if (
        !EXTEND_DIMENSION_CHART.includes(chartName) &&
        hasExtendDimensionList
      ) {
        _isChartActive = false
      }
      // 打开关联数据集之后，不支持关联数据集的图形置灰
      if (
        this.chartUserConfig.datasetAssociation &&
        !DATASET_ASSOCIATION_CHART.includes(chartName)
      ) {
        _isChartActive = false
      }
      const key = chartName + (_isChartActive ? '-active' : '')
      return { _isChartDisable: !_isChartActive }
    },
    // 图片title提示
    getImgTitle(type) {
      let imgtitle = ''
      switch (type) {
        case 've-ring-normal':
        case 've-ring-multiple':
        case 've-pie-rose':
        case 've-pie-normal':
        case 've-map-parent':
        case 've-wordcloud':
        case 've-calendar':
        case 've-roundCascades':
          imgtitle = this.$t('sdp.views.oneDimension')
          break
        case 've-histogram-normal':
        case 've-histogram-stack':
        case 've-line-normal':
        case 've-line-area':
        case 've-bar-normal':
        case 've-composite':
        case 've-pictorialbar':
        case CHART_ALIAS_TYPE.VE_STACK_PERCCENT:
        case CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT:
          imgtitle = this.$t('sdp.views.moreDimension')
          break
        case 've-scatter-normal':
          imgtitle = this.$t('sdp.views.twoXY')
          break
        case 've-gauge-normal':
          imgtitle = this.$t('sdp.views.oneY')
          break
        case 've-radar':
          imgtitle = this.$t('sdp.views.twoDimension')
          break
        case 've-treemap':
        case 've-bar-percent':
          imgtitle = this.$t('sdp.views.moreDimensionAndOneMeTric')
          break
        case 've-tree':
          imgtitle = this.$t('sdp.views.treeMessage')
          break
        case 've-sunburst':
        case 've-themeRiver':
        case 've-bar-Heatmap':
          imgtitle = this.$t('sdp.views.twoDimensionAndOneMeTric')
          break
        case 've-funnel':
          imgtitle =
            this.$t('sdp.views.oneDimensionMoreMeTric') +
            '；' +
            this.$t('sdp.views.noDimension')
          break
        case 've-grid-normal':
          imgtitle = this.$t('sdp.views.zeroOrMultipleDimension')
          break
        case 've-bar-stack':
          imgtitle = this.$t('sdp.views.containsCategoryDimension')
          break
        case 've-waterfall':
          imgtitle =
            this.$t('sdp.views.moreDimensionAndOneMeTric') +
            '；' +
            this.$t('sdp.views.noDimension')
          break
        case CHART_ALIAS_TYPE.VE_BANDWIDTH:
          imgtitle = this.$t('sdp.views.oneDimensionAndMoreMeTric')
          break
        case CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY:
          imgtitle = this.$t('sdp.views.oneDimensionAndOneMetric')
          break
        case CHART_ALIAS_TYPE.VE_LIQUIDFILL:
          imgtitle = this.$t('sdp.views.oneDimensionOrOnlyOneMetric')
          break
        case CHART_ALIAS_TYPE.VE_DECOMPOSITION:
          imgtitle = this.$t('sdp.views.mulDimensionOneMeasures')
          break
      }
      return imgtitle
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-icon-box {
  display: flex;
  flex: 0;
  flex-wrap: wrap;
}
.svg-box:nth-child(5n) {
  margin-right: 0;
}
// 最后一排图标的下边距为0
.svg-box:nth-child(5n + 1),
.svg-box:nth-child(5n + 2),
.svg-box:nth-child(5n + 3),
.svg-box:nth-child(5n + 4) {
  margin-bottom: 0;
}
.svg-box {
  display: inline-block;
  vertical-align: top;
  margin: 0px 2px 15px;
  cursor: pointer;
  text-align: center;
  box-sizing: border-box;
  line-height: 32px;
  width: 32px;
  height: 32px;
  border-radius: 2px;
  margin-left: 2px;
  &.is-actived {
    background-color: var(--sdp-sjj-hs);
  }
  .svg-icon {
    width: 100%;
    height: 100%;
    fill: currentColor;
    overflow: hidden;
    display: block;
    font-size: 32px;
  }
  .img-icon{
    height: 24px;
    width: 24px;
    vertical-align: middle;
  }
  .children-container & {
    padding: 0px;
  }
  &.deactive-img .svg-icon {
    opacity: 0.4;
  }
}

.first-font-color {
  color: var(--sdp-ycsz-rskbt);
}
// .arrow-down {
//   position: relative;
//   &::after {
//     content: "";
//     position: absolute;
//     bottom: -3px;
//     left: 12px;
//     height: 4px;
//     width: 4px;
//     border: 4px solid #fff;
//     border-top-color: #fff;
//     box-shadow: 2px 2px 2px 0 rgba(59, 51, 183, 0.53);
//     transform: rotate(45deg);
//   }
// }
</style>

<style lang="scss">
.chart-el-tooltip__popper {
  max-width: unset !important;
}
</style>
