<template>
  <!-- 图形:数值显示/显示标签、显示数值、显示占比、单位简写、显示位置 -->
  <div v-if="componentShow">
    <!-- 度量值 -->
<!--    <ItemLabel-->
<!--      v-if="chartUserConfig.childChartAlias !== 've-map-world' && chartUserConfig.chartAlias !== 've-themeRiver'"-->
<!--      :label="$t('sdp.views.Metric')"-->
<!--      :level="3"-->
<!--      display="block"-->
<!--      style="margin: 6px 0 -6px;"-->
<!--    ></ItemLabel>-->
    <!-- 数值显示/显示标签 -->
    <TableSwitch
      :formData="chartUserConfig"
      prop="labelLineShow"
      :notNeedSwitch="chartUserConfig.chartAlias === 've-tree'"
      :label="chartUserConfig.chartAlias === 've-themeRiver' ? $t('sdp.views.showLabels') : (isLabelOrNumber ? $t('sdp.views.labelDisplay') : $t('sdp.views.numericalDisplay'))"
      @change="labelDiaplayShowHandler"
    ></TableSwitch>
    <ChartSelect
      v-if="labelAmountShow"
      v-model="chartUserConfig.animationSetting.labelAmount"
      style="width: 100%; margin-top: 16px;"
      @change="labelDiaplayShowHandler('labelAmount')"
    >
      <el-option :label="$t('sdp.views.showCurrentLabel')" value="single"></el-option>
      <el-option :label="$t('sdp.views.showAllLabel')" value="all"></el-option>
    </ChartSelect>
    <!-- 标签跟随维度颜色 -->
    <TableSwitch
      v-if="isFollowDimensionColor"
      class="mt-16"
      :formData="chartUserConfig"
      prop="followDimensionColor"
      :disabled="isDisabledDimensionColor"
      :label="$t('sdp.views.followDimensionColor')"
      @change="changeFollowDimensionColor"
    ></TableSwitch>
    <!-- 标签样式 -->
    <FontSetting
      :fontStyle="chartUserConfig.labelConfig"
      style="margin-top: 4px;"
      type="font"
      :themeType="themeType"
      :chartAlias="chartUserConfig.chartAlias"
      :disabled="{color: isFollowDimensionColor && !!chartUserConfig.followDimensionColor}"
      default="labelConfig.color"
      @change="labelStyleHandler"
    ></FontSetting>
    <template v-if="chartUserConfig.labelLineShow || chartUserConfig.chartAlias === 've-tree'">
      <!-- 经纬度显示 -->
      <TableSwitch
        v-if="isLongitudeAndLatitude"
        :formData="chartUserConfig"
        prop="longitudeAndLatitudeNameShow"
        :label="$t('sdp.views.coordinateDisplay')"
        @change="longitudeAndLatitudeNameShowHandler"
      ></TableSwitch>
    </template>
    <!-- 显示数值 -->
    <TableSwitch
      v-if="needSetLabel"
      class="mt-16"
      :formData="chartUserConfig"
      prop="dataShow"
      :label="$t('sdp.views.showValues')"
      @change="labelDisplayHandler"
    ></TableSwitch>
    <!-- 显示占比 -->
    <TableSwitch
      v-if="chartUserConfig.chartAlias !== 've-roundCascades' && needSetLabel"
      class="mt-16"
      :formData="chartUserConfig"
      prop="percentageShow"
      :label="$t('sdp.views.showPercentage')"
      @change="labelDisplayHandler"
    ></TableSwitch>
    <MoreLines ref="MoreLines" v-bind="{ element, themeType }" @change="labelDisplayHandler"></MoreLines>
    <!-- 允许重叠 -->
    <TableSwitch
      v-if="isShowAllowOverlap"
      class="mt-16"
      :formData="chartUserConfig"
      prop="isAllowLabelOverlap"
      :label="$t('sdp.views.allowOverlap')"
      :disabled="isAllowLabelOverlapDisabled"
      @change="labelDisplayHandler"
    ></TableSwitch>
    <!-- 单位简写 -->
    <TableSwitch
      v-if="isShowUnitShort"
      class="mt-16"
      :formData="chartUserConfig"
      prop="isLabelLogogram"
      :label="$t('sdp.views.unitShorthand')"
      @change="labelDisplayHandler"
    ></TableSwitch>
    <!-- 显示位置：饼图、圆环图、水滴图 -->
    <template v-if="showLabelDisplayPosition">
      <table-switch style="margin-top: 16px;">
        <template slot="label">
          <ItemLabel :label="$t('sdp.views.displayPosition')" :level="4" display="block" style="margin-top: 0;height: auto;line-height: 1;"></ItemLabel>
        </template>
        <div slot="component" style="width: 144px;">
      <RadioGroup
        type="LabelPosition"
        v-model="chartUserConfig.labelConfig.position"
        @on-change="val => labelDisplayHandler(val, 'labelConfigPosition')"
      ></RadioGroup>
        </div>
      </table-switch>
    </template>
    <!-- 显示度量 -->
    <template v-if="metricDisplayComponentShow">
      <div class="item-config-container mt-16">
        <ItemLabel :label="onlyIndex ? $t('sdp.simSystem.showIndicator') : $t('sdp.views.showMeasures')" :level="4"></ItemLabel>
        <i class="icon-sdp-zhibiaoxuanzeqibianji cursor-icon" @click="measuresDisplayDialogVisiable = true"></i>
      </div>
      <div>
        <div v-for="(item, index) in metricLabelDisplay" :key="index">
          <ItemLabel :key="index"  v-if="item.origin.showMetricValue || (isCustomMetric && item.labelType === 'custom' && item.customMetricList.find(v => v.showMetricValue)) || ((item.origin.showMetricPercent) && isShowMetricPercent)" :label="getAliasByKeyName(item.keyName)" :level="5" display="block" :style="{
            marginTop: index === 0 ? '2px' : '-4px',
          }"></ItemLabel>
        </div>
      </div>
    </template>
    <!-- 显示度量-弹框 -->
    <MetricLabelDisplayDialog
      ref="MetricDisplayDialog"
      :element="element"
      :visible.sync="measuresDisplayDialogVisiable"
      @change="metricLabelDisplayDialogHandler"
    ></MetricLabelDisplayDialog>
  </div>

</template>

<script>
import {
  DISPLAY_LABEL_PERCENT,
  CONTROL_MEASURES_CHART,
  FOLLOWS_DIMENSION_COLOR,
  CUSTOM_METRIC,
  ALLOW_LABEL_OVERLAP_CHART,
  ANIMATION_CHART,
  CHART_ALIAS_TYPE,
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import MetricLabelDisplayDialog from './dialog/metricLabelDisplay'
import MoreLines from './component/moreLines'
import { generateMetricLabelDisplayItem } from 'packages/base/board/displayPanel/supernatant/chartSet/utils/generator'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'

export default {
  mixins: [componentMixin],
  name: 'LabelSetting',
  components: { MetricLabelDisplayDialog, MoreLines },
  inject: {
    utils: { default: () => ({}) },
  },
  data() {
    return {
      measuresDisplayDialogVisiable: false,
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    },
    componentShow() {
      return ![
        've-wordcloud',
        've-grid-normal',
      ].includes(this.chartUserConfig.chartAlias)
    },
    chartUserConfig() {
      return this.getChartSetData('chartUserConfig')
    },
labelAmountShow() {
      const { animationSetting = {}, chartAlias, labelLineShow, dataShow, percentageShow } = this.chartUserConfig
      return (labelLineShow || dataShow || percentageShow) && animationSetting.enable && ['ve-pie-normal', 've-pie-rose', 've-ring-normal'].includes(chartAlias) && animationSetting.animationType === 'rotation-normal'
    },
    isAllowLabelOverlapDisabled() {
      const { animationSetting = {}, chartAlias } = this.chartUserConfig
      return animationSetting.enable && animationSetting.animationType === 'rotation-normal' && ANIMATION_CHART.includes(chartAlias) && chartAlias !== 've-map-parent'
    },
    metricLabelDisplay() {
      return this.chartUserConfig.metricLabelDisplay || []
    },
    isShowMetricPercent() {
      const { butterflySetting = {}, chartAlias } = this.chartUserConfig
      if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY && butterflySetting.dataPercent) return false
      return DISPLAY_LABEL_PERCENT.includes(this.chartUserConfig.chartAlias)
    },
    isLabelOrNumber() {
      return [
        've-pie-normal',
        've-pie-rose',
        've-ring-normal',
        've-liquidfill',
        've-ring-multiple',
        've-roundCascades'
      ].includes(this.chartUserConfig.chartAlias)
    },
    isFollowDimensionColor() {
      return FOLLOWS_DIMENSION_COLOR.includes(this.chartUserConfig.chartAlias)
    },
    needSetLabel() {
      return (
        this.isLabelOrNumber || this.chartUserConfig.chartAlias === 've-tree'
      )
    },
    isShowUnitShort() {
      const isShowChartLabel = [
        've-wordcloud',
        've-grid-normal',
        've-themeRiver'
      ].includes(this.chartUserConfig.chartAlias)
      if (isShowChartLabel) return false
      return this.needSetLabel ? this.chartUserConfig.dataShow : this.chartUserConfig.labelLineShow
    },
    isShowAllowOverlap() {
      const { animationSetting = {}, chartAlias, labelLineShow, dataShow, percentageShow, } = this.chartUserConfig
      if (!ALLOW_LABEL_OVERLAP_CHART.includes(chartAlias)) return false
      if (ANIMATION_CHART.includes(chartAlias) && chartAlias !== 've-map-parent' && animationSetting.enable) return false
      return labelLineShow || (this.needSetLabel && (dataShow || percentageShow))
    },
    isLongitudeAndLatitude() {
      const { chartAlias, mapMode } = this.chartUserConfig
      return chartAlias === 've-map-parent' && mapMode === 'longitudeAndLatitude'
    },
    showLabelDisplayPosition() {
      return this.getLabelDisplayPosition()
    },
    metricDisplayComponentShow() {
      const { chartAlias, labelLineShow } = this.chartUserConfig
      return labelLineShow && CONTROL_MEASURES_CHART.includes(chartAlias)
    },
    isDisabledDimensionColor() {
      return this.showLabelDisplayPosition && this.chartUserConfig.labelConfig.position === 'inside'
    },
    isCustomMetric() {
      return CUSTOM_METRIC.includes(this.chartUserConfig.chartAlias)
    }
  },
  watch: {
    showLabelDisplayPosition: {
      handler(val) {
        if (val) {
          this.setDefault(this.element.content, true)
        }
      },
      immediate: true,
    },
  },
  created() {
    this.setDefault()
  },
  methods: {
    labelDiaplayShowHandler(val) {
      if (this.metricLabelDisplay.length) {
        const defaultItem = generateMetricLabelDisplayItem({ chartUserConfig: this.chartUserConfig })
        this.metricLabelDisplay.forEach(m => {
          m.metricLabelPosition = defaultItem.metricLabelPosition
          m.metricLabelRotationAngle = defaultItem.metricLabelRotationAngle
        })
      }
      this.labelDisplayHandler(val)
    },
    changeFollowDimensionColor(val) {
      this.eventEmitHandler('FOLLOW_DIMENSION_COLOR', val)
    },
    labelDisplayHandler(val, key) {
      if (key === 'labelConfigPosition') {
        this.initFollowDimensionColor()
      }
      this.eventEmitHandler('LABEL_SHOW', val)
    },
    labelStyleHandler(val) {
      this.eventEmitHandler('LABEL_STYLE', val)
    },
    longitudeAndLatitudeNameShowHandler(val) {
      this.eventEmitHandler('MAP_LONGITUDE_AND_LATITUDE_NAME_SHOW', val)
    },
    getAliasByKeyName(keyName) {
      const { metricsContainer = [], dimensionList = [], extendDimensionList = [] } = this.chartUserConfig
      const dimensionArray = [...(metricsContainer.default || []), ...dimensionList, ...extendDimensionList]
      const thisDimenison = dimensionArray.find(d => d.keyName === keyName)
      return this.getDatasetLabel(thisDimenison)
    },
    setDefault(elContent = this.element.content, noUpdateChioceTab = false) {
      this.initFollowDimensionColor()
      setLabelConfig.call(this, elContent)
      if (elContent.chioceTab?.length && !noUpdateChioceTab) {
        elContent.chioceTab.forEach(c => {
          setLabelConfig.call(this, c.saveObj)
        })
      }
      function setLabelConfig(content) {
        const componentShow = this.getLabelDisplayPosition(content)
        const position = this.$_getProp(content, 'chartUserConfig.labelConfig.position')
        if (componentShow) {
          const optional = ['inside', 'outside']
          if (!optional.includes(position)) {
            this.$set(content.chartUserConfig.labelConfig, 'position', 'outside')
          }
        }
      }
    },
    initFollowDimensionColor() {
      if (this.isDisabledDimensionColor) {
        this.$set(this.chartUserConfig, 'followDimensionColor', false)
      }
    },
    getLabelDisplayPosition(content = this.element.content) {
      const hasDisplayPosition = content.chartUserConfig.labelLineShow || content.chartUserConfig.dataShow || content.chartUserConfig.percentageShow
      return ['ve-pie-normal', 've-ring-normal', 've-liquidfill'].includes(content.chartUserConfig.chartAlias) && hasDisplayPosition
    },
    metricLabelDisplayDialogHandler() {
      this.eventEmitHandler('METRIC_LABEL_DISPLAY')
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
