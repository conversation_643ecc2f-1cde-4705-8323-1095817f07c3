<template>
  <div>
    <div v-for="(item, i) in warningLineData.warnLineSettingList" :key="item.id" class="warnline-content-item">
      <div class="warn-allowSubscribe" v-if="showAlertSubscription && !isReferenceType && !onlyIndex">
        <span class="allowSubscribe table-switch-label">{{$t('sdp.views.alertSubscription')}}</span>
        <el-switch v-model="item.allowSubscribe" :disabled="isWarnSubscribe(item.id, item)" @change="(val) => changAllowSubscribe(val, item)"></el-switch>
      </div>
      <div class="warn-input-title">
        <span class="table-switch-label">{{$t('sdp.views.warnlineName')}}</span>
        <span class="table-switch-label">{{$t('sdp.views.warnlineConditions')}}</span>
        <span class="table-switch-label" v-show="isShowPercent(item.calcType)">{{$t('sdp.views.percentage')}} %</span>
        <span class="table-switch-label" v-if="isFlyLineEnable">{{$t('sdp.views.warnStyle')}}</span>
        <span class="table-switch-label" v-show="item.allowSubscribe && showAlertSubscription">
          {{ $t('sdp.views.displayWarningEffect') }}
          <el-tooltip
            effect="dark"
            :content="$t('sdp.tooltips.displayWarningEffectTips')"
            placement="top"
          >
            <i class="el-tooltip icon-sdp-info" style="vertical-align: text-bottom;"></i>
          </el-tooltip>
        </span>
      </div>

      <div class="warnline-content">
        <!-- 预警名称 -->
        <el-input v-model="item.name" :disabled="isWarnSubscribe(item.id, item)" :placeholder="$t('sdp.placeholder.pls')" maxlength="20"></el-input>
        <!-- 预警字段 -->
        <ChartSelect v-if="isShowMetric" :disabled="isWarnSubscribe(item.id, item)" v-model="item.metricKeyName" class="type-option metric-option" :title="getDatasetLabel(warningMetricChange(item.metricKeyName))" @change="v => warningMetricChange(v, item)">
          <el-option
            v-for="(e, i) in metricsList"
            :key="i"
            :label="getDatasetLabel(e)"
            :title="getDatasetLabel(e)"
            :disabled="isMetricSelected(e, item)"
            :value="e.keyName">
          </el-option>
        </ChartSelect>
        <!-- 卡片增长值、增长率、完成率显示固定文字 -->
        <div v-if="!isShowMetric" class="metric-text">{{ $t(`sdp.views.${item.cardWarnLineType}`) }}</div>
        <!-- 预警条件 -->
        <ChartSelect v-model="item.calcType" :disabled="isWarnSubscribe(item.id, item)" class="type-option" @change="handleCalcType($event,item)" :title="item.calcType ? calcTypeList.find(e => e.value === item.calcType).label : ''">
          <el-option
            v-for="e in calcTypeList"
            :key="e.value"
            :label="e.label"
            :value="e.value">
          </el-option>
        </ChartSelect>
        <template v-if="item.calcType !== 'range'">
          <!-- 预警条件字段 -->
          <ChartSelect
            v-model="item.fieldType"
            :title="item.fieldType ? fieldTypeList.find(e => e.value === item.fieldType).label : ''"
            class="type-option"
            :disabled="item.calcType === 'isNull' || item.calcType === 'isNotNull' || isWarnSubscribe(item.id, item)"
            @change="handleFieldType($event,item)">
            <el-option
              v-for="e in fieldTypeList"
              :key="e.value"
              :label="e.label"
              :disabled="(chartType === 've-gauge-normal') && (e.value === 'other')"
              :value="e.value">
            </el-option>
          </ChartSelect>

          <template v-if="item.fieldType === 'other'">
            <!-- 其他字段 -->
            <ChartSelect v-model="item.field" :disabled="isWarnSubscribe(item.id, item)" class="type-option" @change="handleField($event,item, i)" :title="getDatasetLabel(item)">
              <el-option
                v-for="e in allFieldList"
                :key="e.labeName"
                :title="getDatasetLabel(e)"
                :label="getDatasetLabel(e)"
                :value="e.labeName">
                <span>{{ getDatasetLabel(e) }}</span>
                <!-- <span v-if="e.comment">{{`   (${substring15(e.comment)})`}}</span> -->
              </el-option>
            </ChartSelect>
            <div class="dis-ib el-input">
              <div class="el-input__inner">
                <DragCascader
                  v-if="allFieldList.some(e => e.labeName === item.field)"
                  :disabled="isWarnSubscribe(item.id, item)"
                  :ref="`dragCascader${[i]}`"
                  :item="item"
                  :index="0"
                  dragBoxType="warningLine"
                  :dragList="getDragList(item)"
                  :trimMaxWidth="205"
                  :current-edit-data="element"
                  :is-metric="false"
                  :is-warn-setting="true"
                  :is-extend-dimension="false"
                  :isTargetLable="false"
                  @selectMess="dragCascaderSelect($event, i)"
                />
              </div>
            </div>
          </template>

          <template v-else>
            <!-- 固定值 -->
            <el-input :class="['fixed-field-input', !isShowPercent(item.calcType) ? 'fixed-field-input-more-width' : '']" v-model="item.value" :placeholder="$t('sdp.placeholder.pls')" :disabled="item.calcType === 'isNull' || item.calcType === 'isNotNull' || isWarnSubscribe(item.id, item)"></el-input>
          </template>

          <el-input
            v-if="isShowPercent(item.calcType)"
            class="other-field-input"
            v-model="item.percent"
            :disabled="isDisablePercent(item, i) || isWarnSubscribe(item.id, item)"
            :placeholder="$t('sdp.placeholder.pls')">
          </el-input>

        </template>
        <template v-else>
          <!-- 区间最小值 -->
          <el-input v-model="item.rangeMin" :disabled="isWarnSubscribe(item.id, item)" :placeholder="$t('sdp.placeholder.pls')" class="warnline-section warnline-section-left"></el-input>
          <span> ~ </span>
          <!-- 区间最大值 -->
          <el-input v-model="item.rangeMax" :disabled="isWarnSubscribe(item.id, item)" :placeholder="$t('sdp.placeholder.pls')" class="warnline-section"></el-input>
        </template>
        <div class="el-switch" style="margin-left:32px" v-if="isFlyLineEnable">{{ $t('sdp.views.flyLine') }}</div>
        <el-switch v-model="item.showFlag" style="margin-left: 52px;" v-show="item.allowSubscribe && showAlertSubscription"></el-switch>
        <i class="el-icon-delete" @click="deleteDetailSetting(i, item.id)"></i>
      </div>

      <div class="warnline-color-picker-content">
        <div class="warnline-color-picker-item" v-if="!isMap && !isHeatmap && !isDecomposition">
          <span class="table-switch-label" v-if="!isMap && !isHeatmap">{{ isCard ? $t('sdp.views.indicatorsColor') : $t('sdp.views.chartWarningLine') }}</span>
          <!-- 预警线颜色 -->
          <ColorPicker
            v-show="!isMap"
            class="warnline-color-picker"
            :color="getWarningTitleThemeColor(item,'warnLineColor',themeType)"
            defaultPureColor="rgba(255,0,0,1)"
            @change="changeConfig($event, item, 'warnLineColor')"
            :disabled="isWarnSubscribe(item.id, item) || (!isHeatmap && !isCard && (warningMetrics.noWarningLineMetrics.includes(item.metric) || ((warningMethod.indicatorMethod === 1 || warningMethod.dimensionMethod === 4) && item.fieldType === 'other')))"
          ></ColorPicker>
        </div>

        <div class="warnline-color-picker-item" v-if="cardUnitColorSetting">
          <span class="table-switch-label" v-if="cardUnitColorSetting">{{ $t('sdp.views.unitColor') }}</span>
          <!-- 卡片指标单位预警色 -->
          <ColorPicker
            class="warnline-color-picker warnline-color-picker2"
            v-if="cardUnitColorSetting"
            :color="getWarningTitleThemeColor(item,'unitColor',themeType)"
            :disabled="isWarnSubscribe(item.id, item)"
            defaultPureColor="rgba(255,0,0,1)"
            @change="changeConfig($event, item, 'unitColor')"
          ></ColorPicker>
        </div>

        <div  class="warnline-color-picker-item" v-if="isLineChats || isCompositeChartLineMetric(item.metricKeyName)">
          <span class="table-switch-label" v-if="isLineChats || isCompositeChartLineMetric(item.metricKeyName)">{{ $t('sdp.views.warnPolygonalColor') }}</span>
            <!-- 折线颜色 polygonalColor -->
          <ColorPicker
            v-if="isLineChats || isCompositeChartLineMetric(item.metricKeyName)"
            class="warnline-color-picker"
            :class="['warnline-color-picker2']"
            :color="getWarningTitleThemeColor(item,'polygonalColor',themeType) "
            defaultPureColor="rgba(255,0,0,1)"
            :disabled="(warnColorDisabled(item)) || isWarnSubscribe(item.id, item)"
            @change="changeConfig($event, item, 'polygonalColor')"
          ></ColorPicker>
        </div>

        <div class="warnline-color-picker-item checkbox-font-middle" v-if="!isReferenceType">
          <el-checkbox :checked="isCheckedChartColor(item.checkedChartColor)" v-model="item.checkedChartColor"  v-if="isCard">
             <span class="table-switch-label">{{$t('sdp.views.titleColor')}}</span>
          </el-checkbox>
          <template v-else>
            <span class="table-switch-label" v-if="!isReferenceType">{{ isCard ?  $t('sdp.views.titleColor') : (isDataPointChart || isCompositeChartLineMetricOrLineArea(item.metricKeyName) ? $t('sdp.views.warnDataPoint') : $t('sdp.views.warnColor')) }}</span>
          </template>
          <!-- 预警色 -->
          <ColorPicker
            class="warnline-color-picker"
            :class="[isMap || isHeatmap? 'right' : 'warnline-color-picker2']"
            :color="getWarningTitleThemeColor(item,'chartColor',themeType)"
            defaultPureColor="rgba(255,0,0,1)"
            @change="changeConfig($event, item, 'chartColor')"
            :disabled="(!isHeatmap && !isCard && warnColorDisabled(item)) || isWarnSubscribe(item.id, item) || (item.checkedChartColor===false && (isCard|| isHeatmap))"
          ></ColorPicker>
        </div>

        <div class="warnline-color-picker-item" v-if="!isCard && isSupportWarnIconChart">
          <span class="table-switch-label" v-if="!isCard">{{$t('sdp.views.icon')}}</span>

          <el-button @click="handelShowDialogRetoucher(item)" v-if="!(getWarningIconOption(item).imgOption.pictureUrl)">{{$t('sdp.views.pleSelect')}}</el-button>
          <div class="imgBox" @click="handelShowDialogRetoucher(item)" v-else>
            <img :src="$_getAssetsUrl(getWarningIconOption(item).imgOption.pictureUrl)" alt="" style="width:32px; height:32px">
          </div>

          <DialogRetoucher
            :visible.sync="dialogRetoucherVisible"
            :retoucherOption="activeRetoucherItem ? getWarningIconOption(activeRetoucherItem).imgOption : getWarningIconOption(item).imgOption"
            @confirm="dataChangeStyleConfirm"
            :item="item"
            :themeType="themeType"
            :boardInfo="boardInfo"
            :title="$t('sdp.views.iconSettings')"
          />
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import { DragCascader, ColorPicker } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { getWarningMetrics } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/chartSetting/chartWarn'
import { substring15 } from 'packages/base/board/displayPanel/utils'
import { NO_METRIC_WARNING_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementTagNewCard/constant'
import { WARNLINE_TYPE_CONNECT_STR, THEME_TYPE } from 'packages/assets/constant'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import ChartSelect from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/chart-select'
import { setLgeTypeValue } from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'
import { CHART_ALIAS_TYPE, HAS_LINE_CHART_LIST, ALLOW_WARNING_ICON_CONFIG_CHART } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { getWarningTitleThemeColor } from 'packages/base/board/displayPanel/supernatant//boardElements/elementTagNewCard/tool'
import DialogRetoucher from 'packages/base/board/displayPanel/supernatant/cardSet/components/DialogRetoucher.vue'

export default {
  mixins: [datasetMixin],
  inject: {
    chart: { default: {} },
    configs: { default: null },
    boardWarningSubscribeData: { default: null },
    getCurrentThemeClass: { default: () => () => '' },
    utils: { default: {} },
  },
  props: {
    element: {
      type: Object,
      default: () => ({}),
    },
    // 设置预警线的值（卡片指标值、目标值、增长率等）
    cardWarnLineType: {
      type: String,
      default: ''
    },
    // 卡片指标索引
    cardIndex: {
      type: Number,
    },
    isCard: {
      type: Boolean,
      default: false
    },
    warningMethod: {
      type: Object,
      default: () => ({}),
    },
    dimensionSettingShow: {
      type: Object,
      default: () => ({}),
    },
    isWarnTypeLinear: {
      type: Boolean,
      default: false
    },
    datasetList: {
      type: Array,
      default: () => [],
    },
    elementAlertNumberLimit: {
      type: Number,
    },
    warningLineData: {
      type: Object,
      default: () => ({}),
    },
    boardInfo: {
      type: Object,
      default: () => ({})
    },
  },
  components: {
    ColorPicker,
    DragCascader,
    ChartSelect,
    DialogRetoucher
  },
  data() {
    return {
      activeRetoucherItem: null,
      calcTypeList: [
        {
          label: this.$t('sdp.views.equal'), // 等于
          value: '='
        },
        {
          label: this.$t('sdp.views.notEqual'), // 不等于
          value: '!='
        },
        {
          label: this.$t('sdp.views.empty'), // 为空
          value: 'isNull'
        },
        {
          label: this.$t('sdp.views.notEmpty'), // 不为空
          value: 'isNotNull'
        },
        {
          label: this.$t('sdp.views.Morebig'), // 大于
          value: '>'
        },
        {
          label: this.$t('sdp.views.MorebEq'), // 大于等于
          value: '>='
        },
        {
          label: this.$t('sdp.views.moreLess'), // 小于
          value: '<'
        },
        {
          label: this.$t('sdp.views.LessEq'), // 小于等于
          value: '<='
        },
        {
          label: this.$t('sdp.views.RangeDial'), // 区间
          value: 'range'
        },
      ],
      fieldTypeList: [
        {
          label: this.$t('sdp.views.fixedValue'),
          value: 'fixed'
        },
        {
          label: this.$t('sdp.views.otherField'),
          value: 'other'
        }
      ],
      numberFieldCalcTypeList: [
        {
          value: 'SUM',
          label: this.$t('sdp.views.BoardSum'),
        },
        {
          value: 'AVG',
          label: this.$t('sdp.views.average'),
        },
        {
          value: 'MAX',
          label: this.$t('sdp.views.largest'),
        },
        {
          value: 'MIN',
          label: this.$t('sdp.views.MIN'),
        },
        // {
        //   value: 'MEDIAN',
        //   label: this.$t('sdp.views.middleNum'),
        // },
      ],
      commonArr: ['SUM', 'AVG', 'MAX', 'MIN', 'COUNT', 'COUNT_DISTINCT'],
      dialogRetoucherVisible: false,
    }
  },
  computed: {
    onlyIndex() {
      return this.configs?.options?.onlyIndex
    },
    isDataPointChart() {
      return HAS_LINE_CHART_LIST.includes(this.element.content.chartUserConfig.chartAlias)
    },
    warningMetrics() {
      return getWarningMetrics(this.element.content)
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig
    },
    isReferenceType() {
      return this.isCard && 'referenceValue' === this.cardWarnLineType
    },
    metricsList() {
      if (this.chartType === 've-gauge-normal') {
        return [
          {
            metric: 'complateRate',
            alias: this.$t('sdp.views.complateRate'),
            labeName: 'complateRate',
            keyName: 'complateRate',
          }
        ]
      }
      if (this.cardWarnLineType && this.isCard) {
        // 比率卡片-参考值-选择项返回数据
        if ('referenceValue' === this.cardWarnLineType) {
          const referenceValueList = this.$_getProp(this.element, 'content.chartUserConfig.referenceValueList', [])
          const optionArray = this.$_getProp(this.element, 'content.optionArray', [])
          const dimensionList = optionArray?.[0]?.dimension || []
          return dimensionList.map(item => {
            const target = referenceValueList.find(e => e.referenceValueKey === `ReferenceValue_${item.alias || item.labeName}`)
            if (target && target.show) {
              return {
                ...item,
                keyName: target.referenceValueKey
              }
            }
          }).filter(e => e)
        }
        const optionArray = this.$_getProp(this.element, 'content.optionArray', [])
        const fieldType = this.cardWarnLineType.includes(WARNLINE_TYPE_CONNECT_STR) ? this.cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)[0] : this.cardWarnLineType
        let alias = this.cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)[1]
        const cardContent = optionArray[this.cardIndex]
        if (!cardContent || !cardContent[fieldType]) return []
        const metric = alias ? cardContent[fieldType].find(item => alias === (item.alias || item.labeName)) : cardContent[fieldType][0]
        return metric ? [metric] : []
      }
      return this.element.content.chartUserConfig.metricsContainer.default.filter(m => !m.isGridCustom)
    },
    // 图形类型
    chartType() {
      return this.element.content.alias
    },
    // 当前字段所属数据集
    fieldList() {
      return this.isCard ? this.chart.fieldList(this.cardIndex) : this.chart.fieldList
    },
    allFieldList() {
      // onlyIndex 说明是在指标系统，否则还是走之前的逻辑，指标系统的 eng 后面会单独分离出来，这里是因为现在还都在测试环境，所有做区分

      // 指标所有字段，包括共有维度、独立维度、指标字段（指标字段 labeName 添加了 _$$_datasetId 后缀来保证唯一，使用时需要处理）
      return this.onlyIndex ? this.chart.allFieldList() : this.fieldList
    },
    // 是否为折线图，预警折线颜色判断使用
    isLineChats() {
      return [
        CHART_ALIAS_TYPE.VE_LINE,
        CHART_ALIAS_TYPE.VE_BANDWIDTH
      ].includes(this.element.content.chartUserConfig.chartAlias)
    },
    // 是否支持预警图标的图形
    isSupportWarnIconChart() {
      return ALLOW_WARNING_ICON_CONFIG_CHART.includes(this.element.content.chartUserConfig.chartAlias)
    },
    isMap() {
      return this.element.content.chartUserConfig.chartType === 've-map'
    },
    // 热力图
    isHeatmap() {
      return this.element.content.chartUserConfig.chartType === 've-bar-Heatmap'
    },
    // 树图
    isDecomposition() {
      return this.element.content.chartUserConfig.chartType === CHART_ALIAS_TYPE.VE_DECOMPOSITION
    },
    isFlyLineEnable() {
      const { mapSetting, dimensionDestinationList } = this.element.content.chartUserConfig
      return !!(this.isMap && mapSetting?.flyLine?.enable && dimensionDestinationList?.[0])
    },
    warningLineDisabled() {
      const warningLineDisabledChart = ['ve-grid-normal']
      const warningLineDisabledChartType = ['ve-map']
      return warningLineDisabledChartType.includes(this.element.content.chartUserConfig.chartType) || warningLineDisabledChart.includes(this.chartType)
    },
    // 是否显示预警字段下拉框
    isShowMetric() {
      return !NO_METRIC_WARNING_TYPE.includes(this.cardWarnLineType)
    },
    // 是否显示卡片指标单位、比率值单位预警色配置
    cardUnitColorSetting() {
      if (this.isCard) {
        const warnLineType = ['dimension', 'ratioValue']
        return warnLineType.some(item => this.cardWarnLineType.includes(item))
      }
      return false
    },
    // 判断是否是看板元素页面
    isTemplateBoard() {
      return (this.configs && this.configs.type === 'template') || false
    },
    // 是否显示元素订阅
    showAlertSubscription() {
      return this.boardWarningSubscribeData?.showWarningSubscribeComponent(!this.isTemplateBoard)
    },
    // 主题类型
    themeType() {
      return this.utils.themeParameters.themeType || THEME_TYPE.default
    },
  },
  methods: {
    substring15,
    getWarningTitleThemeColor,
    isCheckedChartColor(value){
      if(value ===false){
        return false
      }else{ // value可能有undefined的情况, undefined说明是老数据，等同于true
        return true
      }
    },
    warningMetricChange(v, item) {
      const metricItem = this.metricsList.find(m => m.keyName === v)
      item && (item.metric = (metricItem.alias || metricItem.labeName))
      item && (item.parentId = metricItem.parentId)
      item && (item.dataSetId = metricItem.parentId)
      return metricItem
    },
    isCompositeChartLineMetricOrLineArea(keyName) {
      // 是折柱混合图中并且是折线图字段或是折线面积图
      return [CHART_ALIAS_TYPE.VE_COMPOSITE].includes(this.element.content.chartUserConfig.chartAlias) &&
        (this.element.content.chartUserConfig?.metricsContainer?.line || []).map(item => item.keyName).includes(keyName)
    },
    isCompositeChartLineMetric(keyName) {
      // 是折柱混合图中并且是折线图字段，并且不是折线面积图
      return [CHART_ALIAS_TYPE.VE_COMPOSITE].includes(this.element.content.chartUserConfig.chartAlias) &&
        (this.element.content.chartUserConfig?.metricsContainer?.line || []).map(item => item.keyName).includes(keyName) &&
        !this.element.content.chartUserConfig?.compositeChart?.area
    },
    isWarnSubscribe(id) {
      return this.boardWarningSubscribeData?.isWarningItemDisabled(id, this.element.id)
    },
    getDragList(item) {
      return this.$_deepClone(this.allFieldList.filter(e => e.labeName === item.field))
    },
    changAllowSubscribe(val, item) {
      this.$nextTick(() => {
        if (val) {
          if (this.elementAlertNumberLimit < 0) {
            this.$message.warning(this.$t('sdp.message.alertSubscriptionsLimit'))
            item.allowSubscribe = false
          }
        } else {
          if (this.isWarnSubscribe(item.id)) {
            this.$sdp_eng_confirm(`${this.$t('sdp.views.closeWarnSubscribeTips')}`, this.$t('sdp.dialog.hint'), {
              confirmButtonText: this.$t('sdp.button.ensure'),
              cancelButtonText: this.$t('sdp.button.cancel'),
              type: 'warning',
              cancelButtonClass: 'el-button--sdp-cancel',
              confirmButtonClass: 'el-button--sdp-ensure',
              customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
              callback: (action) => {
                if (action === 'confirm') {
                  item.allowSubscribe = false
                }
                if (action === 'cancel') {
                  item.allowSubscribe = true
                }
              }
            })
          }
        }
      })
    },
    handleField(e, warningItem, index) {
      const chartUserConfig = this.element.content.chartUserConfig
      const activeField = this.allFieldList.find(item => item.labeName === e)

      if (activeField.id) activeField.id = warningItem.id || activeField.id
      
      // 创建新对象，避免引用问题
      const updatedItem = Object.assign({}, warningItem, activeField, {
        columnName: activeField.labeName,
        // alias: this.getDatasetLabel(activeField),
      })
      delete updatedItem.alias
      
      if (updatedItem._rawLabeName && updatedItem.labeName !== updatedItem._rawLabeName) {
        updatedItem._rawLabeName = activeField._rawLabeName
        updatedItem._uniqueLabeName = activeField._uniqueLabeName
        updatedItem.indexName = activeField.indexName
        updatedItem.indexId = activeField.indexId
        updatedItem.isFromIndex = activeField.isFromIndex
      }
      this.$set(updatedItem, 'aggType', ['string', 'date'].includes(activeField.columnTpe) ? 'COUNT' : 'SUM')
      this.$set(updatedItem, 'fieldCalcType', updatedItem.aggType || 'SUM')
      setLgeTypeValue(updatedItem)

      this.deleteCustom(updatedItem)
      this.deleteContrast(updatedItem)
      if (!Array.isArray(chartUserConfig.warnCascaderValue)) {
        this.$set(chartUserConfig, 'warnCascaderValue', [])
      }
      
      // 用新对象替换数组中的旧对象，这样Vue可以检测到变化
      this.$set(this.warningLineData.warnLineSettingList, index, updatedItem)
      
      this.$nextTick(() => {
        this.changeCascaderComponents(index, 'initCreated')
      })
    },
    handleFieldType(value, item) {
      this.$set(item, 'percent', 100)
      // 删除度量函数数据
      this.$set(item, 'fieldCalcType', '')
      Reflect.deleteProperty(item, 'aggType')
      setLgeTypeValue(item)
    },
    // 删除自定义计算
    deleteCustom(item) {
      Reflect.deleteProperty(item, 'expression')
      Reflect.deleteProperty(item, 'exp')
    },
    // 删除对比
    deleteContrast(item) {
      Reflect.deleteProperty(item, 'selectedConttast')
      Reflect.deleteProperty(item, 'selectedConttastMode')
    },
    deleteDetailSetting(index, warnLineId) {
      if (this.isWarnSubscribe(warnLineId)) {
        this.$sdp_eng_confirm(`${this.$t('sdp.views.deleteWarnSubscribeTips')}`, this.$t('sdp.dialog.hint'), {
          confirmButtonText: this.$t('sdp.button.ensure'),
          cancelButtonText: this.$t('sdp.button.cancel'),
          type: 'warning',
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
          callback: (action) => {
            if (action === 'confirm') {
              this.deleteWarnLineSetting(index, warnLineId)
            }
          }
        })
      } else {
        this.deleteWarnLineSetting(index, warnLineId)
      }
    },
    deleteWarnLineSetting(index, warnLineId) {
      if (this.isCard) {
        this.$emit('delete-detail', warnLineId, this.cardIndex)
      } else {
        this.warningLineData.warnLineSettingList.splice(index, 1)
      }
    },
    changeConfig(color, item, type) {
      // 多主题色也需要存一份
      let key = type + 'Theme'
      if(item[key] && item[key][this.themeType] ){
        this.$set(item[key], this.themeType, color)
      }else if(item[key] && !item[key][this.themeType]){ //理论上不会走这里了
        this.$set(item, key, Object.assign({}, item[key], { [this.themeType]: color }));
      }else{
        let ketTheme = {
          [THEME_TYPE.classicWhite]:item[type],
          [THEME_TYPE.darkBlue]:item[type],
          [THEME_TYPE.deepBlue]:item[type],
        }
        this.$set(item, key, ketTheme)
        this.$set(item[key], this.themeType, color)
      }
      this.$set(item, type, color || 'rgba(255,0,0,1)')
    },
    handleCalcType(value, item) {
      // 如果切换为区间，需要将后面改成固定值，Bug 69165
      if (value === 'range') {
        this.$set(item, 'fieldType', 'fixed')
        this.handleFieldType(null, item)
      }
      if (value === 'isNull' || value === 'isNotNull') {
        Object.assign(item, {
          fieldType: '',
          value: '',
          warnLineColor: 'rgba(255,0,0,1)',
          chartColor: 'rgba(255,0,0,1)',
          polygonalColor: 'rgba(255,0,0,1)',
          warnLineColorTheme:{
            ...item.warnLineColorTheme,
            [this.themeType]: 'rgba(255,0,0,1)'
          },
          chartColorTheme:{
            ...item.warnLineColorTheme,
            [this.themeType]: 'rgba(255,0,0,1)'
          },
          polygonalColorTheme:{
            ...item.warnLineColorTheme,
            [this.themeType]: 'rgba(255,0,0,1)'
          },

        })
        if (this.isCard) {
          item.unitColor = 'rgba(255,0,0,1)'

          item.unitColorTheme = {
            ...item.unitColorTheme,
            [this.themeType]: 'rgba(255,0,0,1)'
          }
        }
      } else {
        if (item.fieldType) return
        item.fieldType = 'fixed'
        this.handleFieldType(value, item)
      }
    },
    // 选中级联菜单的某一项的事件
    dragCascaderSelect({ selectValue, selectItem, selectList }, i) {
      const { COMMON, CUSTOM, BUSINESS, CONTRAST } = selectValue
      selectItem.fieldCalcType = COMMON
      if (!COMMON || (CONTRAST && !CONTRAST.hasOwnProperty('none'))) {
        this.$set(selectItem, 'percent', 100)
      }
      Object.assign(selectItem, {
        compareInfo: selectItem.aggType === 'CONTRAST' && selectItem.selectedConttast ? { compSubType: selectItem.selectedConttast } : {},
        compareRule: selectItem.aggType === 'CONTRAST' && selectItem.selectedConttastMode ? selectItem.selectedConttastMode : '',
      })
      setLgeTypeValue(selectItem) // 自定义、业务函数、排序时，需要设置bFxType和lgeType

      Object.assign(this.warningLineData.warnLineSettingList[i], selectItem)
      // this.disabledPercentArr.splice(i, 0, !COMMON || (CONTRAST && !CONTRAST.hasOwnProperty('none')))
    },
    changeCascaderComponents(index, value, param) {
      const ref = this.$refs[`dragCascader${[index]}`]
      const _ref = ref && ref[0]
      _ref && _ref[value](param)
    },
    // 指标维度预警和指定维度值指标预警选的度量，指标预警中不能再选
    isMetricSelected(metricItem, warningItem) {
      const { dimensionWarningList = [], dimensionValueIndicatorWarningList = [] } = this.warningLineData
      let metricKeyNameArray = []
      if (this.dimensionSettingShow.dimensionValueIndicatorWarningList && this.warningMethod.dimensionMethod === 4) {
        metricKeyNameArray = dimensionValueIndicatorWarningList.map(dw => dw.metricKeyName)
      } else if (this.dimensionSettingShow.dimensionWarningList && this.warningMethod.dimensionMethod === 3) {
        metricKeyNameArray = dimensionWarningList.map(dw => dw.metricKeyName)
      }
      return metricKeyNameArray.includes(metricItem.keyName)
    },
    changeCascader(isOpen) {
      const arr = Array.isArray(this.warningLineData.warnLineSettingList) ? this.warningLineData.warnLineSettingList : []
      if (!arr.length) return
      arr.forEach((item, i) => {
        this.changeCascaderComponents(i, 'disableNotNormalMeasureOptions', { isOpen })
      })
    },
    // 是否显示百分比
    isShowPercent(calcType) {
      const notShowCalcType = ['range', 'isNull', 'isNotNull']
      // 选择区间、为空、不为空，或者仪表盘选择固定值时不显示百分比
      return !notShowCalcType.includes(calcType) && this.chartType !== 've-gauge-normal'
    },
    // 是否禁用百分比输入框
    isDisablePercent(item, index) {
      let disable = false
      // 图形同维度指标汇总比较时，禁用百分比
      if (!this.isCard && this.warningMethod.indicatorMethod === 1) {
        disable = true
      }
      if (item.fieldType !== 'other' && this.chartType === 've-grid-normal') {
        disable = true
      }
      // 预警字段设置自定义计算时，禁用百分比
      if (this.chartUserConfig.warnCascaderValue?.[index]?.CUSTOM) {
        disable = true
      }
      return disable
    },
    warnColorDisabled(item) {
        // 如果是渐变图形
        if ((this.isWarnTypeLinear && ['isNull', '='].includes(item.calcType)) && !this.isMap) {
          return true
        }
        if (['isNull'].includes(item.calcType)) {
          return true
        }
        return false
    },
    handelShowDialogRetoucher(item) {
      this.activeRetoucherItem = item
      this.$nextTick(() => {
        this.dialogRetoucherVisible = true
      })
     
    },
    getWarningIconOption(item) {
      if (!item?.warningIconOption) {
          this.$set(item, 'warningIconOption', {
            visible: true, // 这个字段无意义
            imgOption: {
              type: 'system', // custom
              isSaveAsMaterial: false,
              pictureId: '',
              pictureUrl: '',
              uploadIconHeight: '',
              uploadIconId: '',
              uploadIconUrl: '',
              uploadIconWidth: '',
            }
          })
      }
      console.log('%c [ item?.warningIconOption ]-749', 'font-size:13px; background:#32b637; color:#76fa7b;', item.warningIconOption)

      return item?.warningIconOption
    },
    dataChangeStyleConfirm(data, item) {
      this.warningLineData.warnLineSettingList.find((i, index) => {
        if (i.id === this.activeRetoucherItem.id) {
          this.$set(this.warningLineData.warnLineSettingList[index].warningIconOption, 'imgOption', data)
        }
      })
      this.activeRetoucherItem = null
    },
  },
}
</script>

<style lang="scss" scoped>
.warnline-content-item {
  margin-top: 20px;
  .warn-allowSubscribe {
    margin-bottom: 15px;
    .allowSubscribe {
      margin-right: 20px;
      font-size: 12px;
    }
    .el-switch {
      height: 16px;
      line-height: 16px;
    }
  }
}
.warnline-content {
  position: relative;
  .el-input:first-child {
    width: 180px;
  }
  .metric-text {
    display: inline-block;
    margin-left: 8px;
    width: 85px;
    font-size: 14px;
  }
  .el-select {
    /deep/ .el-input {
      width: 80px;
    }
    margin-left: 8px;
  }
  .metric-option {
    /deep/ .el-input {
      width: 85px !important;
    }
  }
  .other-field-input {
    width: 94px;
    margin-left: 8px;
  }
  .fixed-field-input {
    width: 283px;
    margin-left: 8px;
  }
  .fixed-field-input-more-width {
    width: 385px;
  }
  .warnline-color-picker {
    display: inline-block;
    vertical-align: middle;
    margin-left: 44px;
  }
  .warnline-color-picker2:last-of-type {
    margin: 0 46px 0 46px;
  }
  .right {
    margin-right: 52px;
  }
  .el-icon-delete {
    font-size: 16px;
    cursor: pointer;
    color: var(--sdp-qcgls);
    vertical-align: middle;
    position: absolute;
    right: 20px;
    top: 8px;
  }
}
.warn-input-title {
  position: relative;
  margin-bottom: 8px;
  height: 20px;
  span:nth-child(1) {
    position: absolute;
    left: 0;
  }
  span:nth-child(2) {
    position: absolute;
    left: 188px;
  }
  span:nth-child(3) {
    position: absolute;
    left: 750px;
  }
  span:nth-child(4) {
    position: absolute;
    left: 875px;
  }
  span:nth-child(5) {
    position: absolute;
    left: 937px;
  }
  span:nth-child(6) {
    position: absolute;
    left: 1002px;
  }
  span:nth-child(7) {
    position: absolute;
    left: 1067px;
  }
}
.warnline-section-left {
  margin-left: 9px;
}
.warnline-section {
  width: 229px;
}
.dis-ib {
  display: inline-block;
  // 修改字段框宽度时，trimMaxWidth需要一起修改，trimMaxWidth = width + 9
  width: 196px;
  margin-left: 6px;
  height: 32px;
  vertical-align: top;
  margin-top: 1px;
  .el-input__inner{
    vertical-align: middle;
    height: 100%;
    padding: 0;
  }
  /deep/ .dragCascader{
    vertical-align: top;
    margin-top: 1px;
  }
}

.warnline-color-picker-content{
  display: flex;
  align-items: center;
  gap: 105px;
  margin: 15px 0;
  .warnline-color-picker-item{
    display: flex;
    align-items: center;
    gap: 10px;
    line-height: normal;
    /deep/ .el-checkbox{
      margin-right: 0;
    }

  }
  .checkbox-font-middle{
    .el-checkbox__label{
      line-height: normal;
      .table-switch-label{
        vertical-align: baseline;
      }
    }
  }
}
</style>
