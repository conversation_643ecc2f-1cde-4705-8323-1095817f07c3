<template>
  <div class="sdp-tip-render">
    <rich-text
      v-if="!emptyRichText"
      ref="richTextRef"
      class="rich-text"
      :customStyle="customStyle"
      :element="element"
      :isTipList="isTipList"
      :tipSetIndex="tipSetIndex"
      :textResponse="textResponse"
      :scope="contentScope.content.richTextHtml && !onlyHTMLTags(contentScope.content.richTextHtml) ? contentScope.scope : ''"
    >
    </rich-text>
  </div>
</template>

<script>

import RichText from 'packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/richText.vue'
import { transfromZeroFormat } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/bridge'
import {
  getAggType,
  setLgeTypeValue
} from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import mediator from 'packages/base/board/displayPanel/utils/mediator'
import { updateReportLogObj } from 'packages/assets/utils/reportLog'
import { preview } from 'packages/base/board/displayPanel/supernatant/boardElements/elementCustomer/editorSetting/api'
import {
  setElementTextContent, setTipSetContent
} from "packages/base/board/displayPanel/supernatant/boardElements/elementText/colorConfig";
import {
  getRenderRichTextHtml,
  onlyHTMLTags,
  getContentScope,
} from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/utils";

export default {
  name: 'tipRender',
  mixins: [datasetMixin],
  inject: {
    utils: { default: {} },
    commonData: { default: {} },
    themeData: { default: {} },
    langCode: { default: 'zh' },
  },
  components: {
    RichText,
  },
  props: {
    element: {
      type: Object,
      default: () => ({}),
    },
    customStyle: {
      type: Object,
      default: () => ({}),
    },
    datasetList: {
      type: Array,
      default: () => ([])
    },
    elList: {
      type: Array,
      default: () => []
    },
    isTipList: {
      type: Boolean,
      default: false
    },
    tipSetIndex: {
      type: [String, Number],
      default: '0'
    },
  },
  data() {
    return {
      hasResponseData: false,
      textResponse: {},
    }
  },
  computed: {
    api () {
      return this.utils.api || function () { }
    },
    themeType() {
      return this.themeData.themeType || 'sdp-classic-white'
    },
    emptyRichText() {
      return !this.renderRichTextHtml || this.onlyHTMLTags(this.renderRichTextHtml)
    },
    tipSetComputed() {
      if (this.isTipList) return this.element.tipSetList.find(e => e.id === this.tipSetIndex)
      return this.element.tipSet
    },
    contentScope() {
      return getContentScope({
        commonData: this.commonData,
        utils: this.utils,
        isTipList: this.isTipList,
        tipSetIndex: this.tipSetIndex,
        element: this.element,
        langCode: this.langCode
      })
      // const boardLang = this.commonData.boardSlectLang ? this.commonData.boardSlectLang() : ''
      // const isDailyConcerned = !!this.utils?.isDailyConcerned
      // const lang = isDailyConcerned ? boardLang || this.langCode : boardLang
      // if ((this.commonData.isPreview || isDailyConcerned) && lang) {
      //   const locales = this.isTipList ? this.element?.tipSetList?.find?.(e => e.id === this.tipSetIndex)?.locales : this.element.tipSet?.locales || {}
      //   if (locales[lang]) {
      //     return {
      //       isTipList: this.isTipList,
      //       tipSetIndex: this.tipSetIndex,
      //       content: locales[lang],
      //       // scope: this.isTipList ? `tipSetList.${this.tipSetIndex}.locales.${lang}` : `tipSet.locales.${lang}`
      //       scope: this.isTipList ? `locales.${lang}` : `tipSet.locales.${lang}`
      //     }
      //   }
      // }
      // let content
      // if (this.isTipList) {
      //   content = this.element.tipSetList.find(e => e.id === this.tipSetIndex)
      // } else {
      //   content = this.element.tipSet
      // }
      // return {
      //   isTipList: this.isTipList,
      //   tipSetIndex: this.tipSetIndex,
      //   content,
      //   // scope: this.isTipList ? `tipSetList.${this.tipSetIndex}` : `tipSet`
      //   scope: this.isTipList ? `` : `tipSet`
      // }
    },
    // 用于渲染的文本
    renderRichTextHtml() {
      return getRenderRichTextHtml({ contentScope: this.contentScope, tipSetComputed: this.tipSetComputed })
      // const { content, scope } = this.contentScope
      // if (scope === 'content') {
      //   return content.richTextHtml
      // }
      // if (content.richTextHtml && !this.onlyHTMLTags(content.richTextHtml)) {
      //   return content.richTextHtml
      // }
      // return this.tipSetComputed?.richTextHtml || this.contentScope?.content?.richTextHtml
    },
    langArr() {
      return this.utils.languageList
    },
  },
  watch: {
    contentScope: {
      handler(val, old) {
        if (JSON.stringify(val) !== JSON.stringify(old)) {
          this.$nextTick(() => {
            this.request()
          })
        }
      },
      deep: true,
    },
    'commonData.isPreview'() {
      this.$nextTick(() => {
        this.request()
      })
    },
    themeType: {
      handler(val, oldVal) {
        setTipSetContent(this.tipSetComputed, this.themeType)
        // 多语言的主题样式
        if (this.tipSetComputed.locales) {
          Object.keys(this.tipSetComputed.locales).forEach(key => {
            setTipSetContent(this.tipSetComputed, this.themeType, this.tipSetComputed.locales[key], `locales.${key}`)
          })
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.request()
  },
  beforeDestroy() {
    this.$emit('refresh', 0)
  },
  methods: {
    onlyHTMLTags(htmlString) {
      return onlyHTMLTags(htmlString)
      // // 创建一个新的DOM元素
      // const tempDiv = document.createElement('div')
      // // 设置元素的内容为要检查的HTML字符串
      // tempDiv.innerHTML = htmlString
      // // 获取元素的纯文本内容
      // const textContent = tempDiv.textContent || tempDiv.innerText || ''
      // // 返回一个布尔值，表示是否只有HTML标签
      // return !textContent.length
    },
    refreshRender() {
      const ref = this.$refs.richTextRef
      if (ref) {
        ref.initData('response')
        this.$nextTick(() => {
          console.log('10641 refresh height', this.$el.offsetHeight)
          this.$emit('refresh', this.$el.offsetHeight)
        })
      }
    },
    getAggType(e) {
      if (e.customerExprDim) return undefined
      if (e.columnTpe === 'date' && (e.aggType !== 'expression')) return 'MAX'
      return getAggType(e)
    },
    extendFieldByKeyName(data, originField, newField) {
      const extendObj = {
        metricGroupInfo: this.getMetricGroupInfo(originField),
        columnName: newField.columnName,
        customerExprField: true,
        exp: newField.exp,
        keyName: newField.keyName,
        customerExprDim: newField.customerExprDim,
        aggType: newField.customerExprDim ? undefined : 'expression',
        webFieldType: newField.webFieldType,
      }
      // 自定义字段信息
      this.$set(data, 'customFieldInfo', extendObj)
    },
    requestAdapter() {
      const { referenceField = [], dragcascaderValue = {} } = this.tipSetComputed
      if (!referenceField.length) {
        this.hasResponseData = true
        this.$set(this, 'textResponse', {})
        this.refreshRender()
        return false
      }

      const metrics = referenceField.map(item => {
        const metric = dragcascaderValue[item.uniqueKey]
        const metricParams = this.$_JSONClone(metric)
        transfromZeroFormat(metricParams)
        let result = Object.assign(
          metricParams,
          {
            columnType: metric.columnTpe,
            columnName: metric.labeName,
            alias: metric.alias || metric.labeName,
            aggType: this.getAggType(metric),
            dataSetId: metric.parentId,
          },
          metric.columnTpe === 'date' ? {
            compareShowNameType: metric.aggType === 'MAX' ? undefined : 'periodtext_curr_day',
          } : {
            compareInfo: metric.aggType === 'CONTRAST' && metric.selectedConttast ? { compSubType: metric.selectedConttast } : {},
            compareRule: metric.aggType === 'CONTRAST' && metric.selectedConttastMode ? metric.selectedConttastMode : '',
          },
          metric.customFieldInfo || {}
        )

        setLgeTypeValue(result, metric) // 自定义、业务函数、排序时，需要设置bFxType和lgeType
        const o = this.getCustomDatasetFieldDetail(result, this.element.type)
        if (item.webFieldType === 'customComputed') {
          this.extendFieldByKeyName(metric, item, o)
        }
        return o
      })

      const requestParams = {
        id: this.element.id,
        metrics: [...metrics],
      }
      return {
        requestKey: 'textElement',
        requestParams,
      }
    },
    getRequestParam() {
      const requestData = this.requestAdapter()
      if (!requestData) return false

      const { id, name, code, currency } = this.displayBoardInfo || {}
      const { isSubscribe, isMobileApp, isRemind } = this.commonData || {}
      const { tenantId, isMobile, isLargeScreen, isDataReport } = this.utils || {}

      const requestParams = {
        tenantId,
        currency,
        [requestData.requestKey]: [requestData.requestParams]
      }

      requestParams.reportLog = updateReportLogObj({
        id,
        name,
        code,
        elList: this.elList,
        isSubscribe,
        isRemind,
        isMobileApp,
        isLargeScreen,
        isDataReport,
        isMobile,
        type: 'text',
        langCode: this.commonData.boardSlectLang() || this.langCode,
      })

      return requestParams
    },
    async request() {
      const params = this.getRequestParam()
      if (!params) {
        this.$set(this, 'textResponse', {})
        this.refreshRender()
        return
      }

      const responseData = await preview(this.api, params)

      const response = responseData.textElement[0].textResponse
      console.log('response', response)
      this.$set(this, 'textResponse', response || {})
      setTimeout(() => {
        this.refreshRender()
      }, 20)
    },
  },
}
</script>

<style lang="scss" scoped>
.sdp-tip-render {
  .rich-text {
    color: var(--sdp-cszjsz-wzs1);
    font-size: 12px;
    font-family: NotoSansHans-Regular;
    line-height: 1;
    justify-content: left;
    text-align: left;
  }
}
</style>
