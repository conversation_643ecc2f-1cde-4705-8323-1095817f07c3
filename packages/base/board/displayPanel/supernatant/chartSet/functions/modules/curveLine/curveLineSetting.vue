<template>
  <!-- 曲线样式 -->
  <div v-if="userLineStyle" :class="['chart-curveSetting', areaColorShow ? 'areaColorShow' : '']">
    <div class="chart-curveSetting-container">
      <!-- 度量名称 -->
      <div class="measure">
        <div class="text">{{ onlyIndex ? $t('sdp.simSystem.IndicatorName') : $t('sdp.views.measureName') }}</div>
        <div class="metric" :title="getDatasetLabel(userLineStyle)">{{ getDatasetLabel(userLineStyle) }}</div>
      </div>
      <!-- 线条样式 -->
      <div class="line-style" v-if="chartUserConfig.chartAlias !== 've-radar'">
        <ItemLabel :label="$t('sdp.views.line_style')" :level="4" display="block" class="text"></ItemLabel>
        <RadioGroup v-model="userLineStyle.smooth" type="LineSmoothType"></RadioGroup>
      </div>
      <!-- 线型 -->
      <div class="line-linear">
        <ItemLabel :label="$t('sdp.views.line_linear')" :level="4" display="block" class="text"></ItemLabel>
        <FontSetting
          type="lineStyle"
          :themeType="themeType"
          :prop="{ lineType: 'type', lineWidth: 'width' }"
          :fontStyle="userLineStyle"
        >
        </FontSetting>
      </div>
      <!-- 显示拐点 -->
      <TableSwitch :formData="userLineStyle" prop="showSymbol" class="line-point">
        <ItemLabel slot="label" :label="$t('sdp.views.line_inflectionPoint')" :level="4" icon :tips="currentLang" display="block" class="text"></ItemLabel>
      </TableSwitch>
      <!-- 拐点样式 -->
      <div class="line-point-style symbolType-container">
        <ItemLabel :label="$t('sdp.views.line_point_style')" :level="4" display="block" class="text"></ItemLabel>
        <div class="flex-row">
          <ChartSelect v-model="userLineStyle.symbol">
            <el-option v-for="item in symbolList" :key="item.value" :value="item.value">
              <i :class="item.icon"></i>
            </el-option>
            <i :class="symbolList.find(symbolItem => symbolItem.value === userLineStyle.symbol).icon" slot="prefix"></i>
          </ChartSelect>
          <SdpColorPick
            :width="25"
            :height="25"
            :showChange="false"
            :colorType="symbolColor.type"
            :angle="symbolColor.angle"
            :gradientColor="symbolColor.gradientColor"
            :pureColor="symbolColor.pureColor"
            :disabledType="'gradient'"
            :referenceStyle="{borderRadius:'3px',marginLeft:'8px',marginTop:'3px'}"
            :gradientArrowShow="[true, false, true, false, true, false, true, false]"
            :chartUserConfig.sync="chartUserConfig"
            @change="handColorChange($event, 'symbolColor')" />
        </div>
      </div>
      <!-- 区域颜色 -->
      <div v-if="areaColorShow && areaColor" class="line-linear">
        <ItemLabel :label="$t('sdp.views.areaColor1')" :level="4" display="block" class="text"></ItemLabel>
        <SdpColorPick
          :width="25"
          :height="25"
          :colorType="areaColor.type"
          :angle="areaColor.angle"
          :gradientColor="areaColor.gradientColor"
          :pureColor="areaColor.pureColor"
          :disabledType="false"
          :referenceStyle="{borderRadius:'3px',marginRight:'5px',marginTop:'3px'}"
          :gradientArrowShow="[true, false, true, false, true, false, true, false]"
          :chartUserConfig.sync="chartUserConfig"
          @change="handColorChange($event, 'areaColor')" />
      </div>
    </div>
  </div>
</template>

<script>
import COLOR_SYSTEM from 'packages/base/board/displayPanel/supernatant/chartSet/colorSystem'
import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import { LINE_STYLE_SETTING_CHART, CHART_ALIAS_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { curveLineSymbolList } from '../../../../boardElements/elementChart/chartsList/chartSetting/handleChartSetting'
import SdpColorPick from 'packages/base/common/sdpColorPicker/index.vue'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'

export default {
  mixins: [componentMixin, datasetMixin],
  name: 'CurveLineSetting',
  components: { SdpColorPick },
  inject: {
    utils: { default: {} },
    getCurrentThemeClass: { defalt: () => '' },
  },
  props: {
    userLineStyle: {
      type: Object,
    },
    areaColor: {
      type: Object,
    },
    symbolColor: {
      type: Object,
    },
  },
  data() {
    return {
      symbolList: curveLineSymbolList
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs?.options?.onlyIndex
    },
    // 是否为折线图，预警折线颜色判断使用
    isLineChats() {
      return [CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_BANDWIDTH].includes(this.chartUserConfig.chartAlias)
    },
    // 是折线图，并且添加了折线预警，先禁用拐点颜色修改，临时方案，需要后续修改
    hasWarningAndLine() {
      return (this.isLineChats && this.chartUserConfig.warnLineSettingList && this.chartUserConfig.warnLineSettingList.length) ||
      (this.chartUserConfig.warnLineSettingList || []).some(item => this.isCompositeChartLineMetric(item.metricKeyName))
    },
    currentLang() {
      const langMap = new Map([
        ['ve-line-normal', 'line_tips_chart'],
        [ 've-line-area', 'line_tips_area' ],
        ['ve-composite', 'line_tips_composite'],
        [CHART_ALIAS_TYPE.VE_BANDWIDTH, 'line_tips_bandwidth'],
        [CHART_ALIAS_TYPE.VE_RADAR, 'line_tips_radar'],
      ])
      return this.$t(`sdp.views.${langMap.get(this.chartUserConfig.chartAlias)}`)
    },
    areaColorShow() {
      const { compositeChart, chartAlias, extendDimensionList = [] } = this.chartUserConfig
      if (extendDimensionList.length) return false
      return chartAlias === 've-line-area' || (chartAlias === 've-composite' && compositeChart.area)
    },
    colorPanelList() {
      const { colorScheme = 'default', opacityList = {} } = this.chartUserConfig
      return (COLOR_SYSTEM[colorScheme] || []).map(color => {
        return Color.getOpacityColor(color, opacityList[colorScheme])
      })
    }
  },
  watch: {
  },
  methods: {
    isCompositeChartLineMetric(keyName) {
      // 是折柱混合图中并且是折线图字段，并且不是折线面积图
      return [CHART_ALIAS_TYPE.VE_COMPOSITE].includes(this.chartUserConfig.chartAlias) &&
        (this.chartUserConfig?.metricsContainer?.line || []).map(item => item.keyName).includes(keyName) &&
        !this.chartUserConfig?.compositeChart?.area
    },
    handColorChange(param = {}, colorKey = 'areaColor') {
      this.$set(this[colorKey], param.key, param.value)
      param.key === 'pureColor' && (this.$set(this[colorKey], 'color', param.value))
      this.$set(this.userLineStyle, colorKey, this[colorKey])
    },
    onSymbolColorChange(param = {}) {
      const _color = this.userLineStyle.symbolColor
      _color[param.key] = param.value
      param.key === 'pureColor' && (_color.color = param.value)
      this.$set(this.userLineStyle, 'symbolColor', _color)
    }
  },
}
</script>

<style lang="scss" scoped>
.chart-curveSetting-container /deep/ {
  .radio-group-container {
    width: 148px;
    padding-top: 4px;
  }
  .font-setting-container {
    margin-top: 0;
    height: 32px;
    .el-input__inner, .el-input__suffix, .el-input__prefix {
      line-height: 32px;
    }
    .font-lineType-set {
      margin-right: 18px;
      width: 87px !important;
      .el-input__prefix {
        margin-left: 18px;
      }
    }
    .font-lineWidth-adapt {
      width: 105px !important;
    }
  }
  .table-switch-container {
    margin: 0px;
    display: block;
    .el-switch {
      // width: 100%;
      // justify-content: center;
      height: 32px;
    }
  }
  .text {
    height: 23px;
    line-height: 23px !important;
    margin: 0;
    font-size: 14px;
    color: var(--sdp-ycsz-rskbt);
  }
  .symbolType-container {
    .flex-row{
      display: flex;
      align-items: center;
    }
    .el-select {
      width: 140px;
    }

    .el-input__suffix{
      right: 0;
    }
    .el-input__prefix {
      line-height: 32px;
      margin-left: 10px;
      color: var(--sdp-zs);
    }
    .el-input__inner{
      color: transparent !important;
      &::selection {
        color: transparent !important;
        background: transparent !important;
      }
    }
  }
}
.chart-curveSetting {
  display: flex;
  align-items: center;
  height: 100px;
  margin-top: 18px;
  background-color: var(--sdp-fs1);
  .chart-curveSetting-container {
    display: flex;
    .text {
      text-align: left;
      margin-bottom: 8px;
    }
    .measure {
      margin-left: 14px;
      width: 100px;
      margin-right: 20px;
      .text {
        text-align: left;
      }
      .metric {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 32px;
      }
    }
    .line-linear {
      margin-left: 40px;
    }
    .line-point {
      margin-left: 40px;
      width: 115px;
    }
    .line-point-style {
      margin-left: 40px;
    }
  }
  &.areaColorShow{
    .chart-curveSetting-container{
      .line-linear {
        margin-left: 15px;
      }
      .line-point {
        margin-left: 15px;
        width: 115px;
      }
      .line-point-style {
        margin-left: 15px;
      }
    }
  }
}
</style>
