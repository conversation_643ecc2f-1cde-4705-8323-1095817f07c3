<template>
  <!-- 图例内容自适应 -->
  <TableSwitch
    v-if="componentShow"
    :formData="chartUserConfig"
    prop="isLegendAdaptive"
    :label="$t('sdp.views.legendAdaptive')"
    :disabled="!(chartUserConfig.legend === 'bottom' || chartUserConfig.legend === 'top')"
    @change="legendAdaptiveHandler"
  ></TableSwitch>
</template>

<script>
import { NOT_NEED_LEGEND, VE_LIQUIDFILL_MODE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'
import element_showhide_mixins from 'packages/base/board/displayPanel/supernatant/chartSet/components/element_showhide_mixins'

export default {
  mixins: [componentMixin, element_showhide_mixins],
  name: 'LegendAdaptive',
  inject: {
    utils: { default: {} },
  },
  data() {
    return {}
  },
  computed: {
    componentShow() { 
      if (this.chartUserConfig.chartAlias === 've-liquidfill') {
        return !this.utils.isMobile && this.chartUserConfig.liquidFillSetting?.mode === VE_LIQUIDFILL_MODE.DIMENSION
      }
      if (this.chartUserConfig.chartAlias === 've-bar-Heatmap') {
        return true
      }
      return (!NOT_NEED_LEGEND.includes(this.chartUserConfig.chartAlias)) && (this.chartUserConfig.legend === 'bottom' || this.chartUserConfig.legend === 'top')
    },
    chartUserConfig() {
      return this.getChartSetData('chartUserConfig')
    },
  },
  watch: {
    'chartUserConfig.legend': {
      handler(val) {
        if(this.chartUserConfig.isLegendAdaptive && !(val === 'bottom' || val === 'top')) {
          this.$set(this.chartUserConfig, 'isLegendAdaptive', false)
        }
      },
      deep: true
    }
  },
  methods: {
    legendAdaptiveHandler(val) {
      this.eventEmitHandler('LEGEND_ADAPTIVE', val)
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
