<template>
  <div style="margin-bottom: 16px;">
    <div class="colorSetting-purple-block" v-popover="'colorSchemePopover'">
      <div>{{$t('sdp.views.ColorScheme')}}</div>
      <i :class="{'icon-sdp-shucangbiaogejiantou': true}"></i>
    </div>
    <!-- 色系 -->
    <el-popover
      ref="colorSchemePopover"
      :popper-class="'sdp-popover sdp-popover-chartSet sdp-dialog ' + getCurrentThemeClass()"
      placement="left"
      width="356"
      trigger="click"
      v-model="colorSchemeObject.visible"
      @show="openColorScheme"
    >
      <div class="popover-title">{{$t('sdp.views.ColorScheme')}}</div>
      <div style="width: 152px; margin-bottom: 20px;">
        <ChartSelect
          v-model="colorSchemeObject.currentColorScheme"
          @change="changeColorScheme">
          <el-option
            v-for="item in colorSchemeList"
            :key="item.value"
            :value="item.value"
            :label="item.label">
            <img v-if="checkIsSystemScheme(item.value)" style="width: 14px; height: 18px; margin-right: 8px;display: inline-block;" :src="item.iconPath" />
            <div class="scheme-preview" v-else>
              <div v-for="i in 6" :key="i" :style="{backgroundColor: _COLOR_SYSTEM[item.value][i - 1] || 'rgba(0,0,0,0)'}"></div>
            </div>
            <span>{{ item.label }}</span>
          </el-option>
        </ChartSelect>
      </div>
      <div class="color-system-container">
        <ColorPanel :colors="colorPanelList" :clickable="false"></ColorPanel>
      </div>
      <!-- 透明度滑条 -->
      <div class="color-scheme-opacity-container" v-if="isSystemScheme">
        <el-slider
          style="width: 220px;"
          ref="colorSlider"
          v-model="colorSchemeObject.opacity"
          :max="1"
          :step="0.01"
          :show-tooltip="false"
          @change="changeOpacity"></el-slider>
        <div class="opacity-value">{{ parseInt(colorSchemeObject.opacity * 100) }}%</div>
      </div>
      <div style="text-align: right;" class="sdp-chart-button">
        <el-button
          type="primary"
          size="mini"
          @click="colorSchemeSettingHandler"
        >{{$t('sdp.button.ensure')}}</el-button>
      </div>
    </el-popover>
  </div>
</template>

<script>
import COLOR_SYSTEM from 'packages/base/board/displayPanel/supernatant/chartSet/colorSystem'
import ColorPanel from 'packages/base/board/displayPanel/supernatant/chartSet/common/components/color-panel'
import { THEME_TYPE, STATIC_BASE_PATH } from 'packages/assets/constant'
import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import componentMixin from 'packages/base/board/displayPanel/supernatant/chartSet/mixins/component'
import { GLOBAL_CHART_CUSTOM_SCHEME } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'

export default {
  mixins: [componentMixin],
  inject: ['sdpBus'],
  name: 'ColorSetting_Scheme',
  components: { ColorPanel },
  props: {
    chartVm: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      colorPanelList: [],
      colorSchemeObject: {
        visible: false,
        opacity: 1,
        currentColorScheme: 'default',
      },
    }
  },
  computed: {
    globalChartColorScheme() {
      let scheme = this.chartVm?.globalChartColorScheme || this.chartVm?.boardInfo?.globalChartColorScheme
      if (scheme) scheme = scheme[this.themeType]
      return scheme
    },
    _COLOR_SYSTEM() {
      let result = this.$_deepClone(COLOR_SYSTEM)
      if (this.globalChartColorScheme?.colorScheme && this.globalChartColorScheme?.colors) {
        if (this.globalChartColorScheme?.colorScheme === GLOBAL_CHART_CUSTOM_SCHEME) {
          result[GLOBAL_CHART_CUSTOM_SCHEME] = this.globalChartColorScheme?.colors
        }
      }
      const standardList = this.sdpBus.standardChartSchemeList
      if (standardList?.length) {
        standardList.forEach(item => {
          result[item.id] = item.colors.slice(0)
        })
      }
      return result
    },
    colorSchemeList() {
      let colorSchemeArray = [
        { label: this.$t('sdp.views.systemDefault'), value: 'default', path: 'DefaultScheme.png', },
        { label: this.$t('sdp.views.BrightScheme'), value: 'Bright', path: 'Bright.png', },
        { label: this.$t('sdp.views.OceanScheme'), value: 'Ocean', path: 'Ocean.png', },
        { label: this.$t('sdp.views.GrapePurpleScheme'), value: 'GrapePurple', path: 'GrapePurple.png', },
        { label: this.$t('sdp.views.GlassScheme'), value: 'GlassBlue​', path: 'GlassBlue​.png', },
        { label: this.$t('sdp.views.TechBlueScheme'), value: 'TechBlue', path: 'TechBlue.png', },
      ]
      const colorSchemeListMap = {
        [THEME_TYPE.darkBlue]: ['default', 'TechBlue'],
        default: ['default', 'Bright', 'Ocean', 'GrapePurple', 'GlassBlue​'],
      }
      // 全局设置的色系
      if (this.globalChartColorScheme?.colorScheme && this.globalChartColorScheme?.colors) {
        if (this.globalChartColorScheme?.colorScheme === GLOBAL_CHART_CUSTOM_SCHEME) {
          colorSchemeArray.push({ label: this.$t('sdp.views.Custom'), value: GLOBAL_CHART_CUSTOM_SCHEME, path: 'TechBlue.png' })
          Object.keys(colorSchemeListMap).forEach(key => {
            colorSchemeListMap[key].push(GLOBAL_CHART_CUSTOM_SCHEME)
          })
        }
      }
      // 标准色系
      const standardList = this.sdpBus.standardChartSchemeList
      let standardIds = []
      if (standardList?.length) {
        standardList.forEach(item => {
          colorSchemeArray.push({ label: item.name, value: item.id, path: 'TechBlue.png' })
          standardIds.push(item.id)
        })
      }
      let colorSchemeList = colorSchemeListMap[this.themeType] || colorSchemeListMap.default
      colorSchemeList = colorSchemeList.concat(standardIds)

      // 如果有设置业务默认色系，则将其指定并修改名称
      let changeFirst = false
      let busiChartColorScheme = this.sdpBus?.busiChartColorScheme
      if (busiChartColorScheme && busiChartColorScheme[this.themeType]) {
        busiChartColorScheme = busiChartColorScheme[this.themeType]
      }
      if (busiChartColorScheme?.colorScheme) {
        const idx = colorSchemeList.findIndex(item => item === busiChartColorScheme?.colorScheme)
        if (idx > 0) {
          const o = colorSchemeList.splice(idx, 1)[0]
          colorSchemeList.unshift(o)
          changeFirst = true
        }
      }

      return colorSchemeList.map((cs, index) => {
        const schemeItem = colorSchemeArray.find(c => c.value === cs)
        if (changeFirst && index === 0) {
          schemeItem.label = `${schemeItem.label} (${this.$t('sdp.views.default')})`
        }
        return {
          ...schemeItem,
          iconPath: STATIC_BASE_PATH.images + 'chartsImgs/' + schemeItem.path,
        }
      })
    },
    // 是否为系统默认内置色系
    isSystemScheme() {
      return Color.isSystemScheme(this.colorSchemeObject.currentColorScheme)
    },
    checkIsSystemScheme() {
      return (scheme) => {
        const keys = Object.keys(COLOR_SYSTEM)
        return keys.includes(scheme)
      }
    }
  },
  methods: {
    // 切换色系
    changeColorScheme() {
      const { currentColorScheme, opacity } = this.colorSchemeObject
      this.colorPanelList = this._COLOR_SYSTEM[currentColorScheme]
      // 切换色系时需要保留透明度
      this.changeOpacity(opacity)
    },
    // 点击色系，打开弹框
    openColorScheme() {
      let { opacityList = {}, colorScheme = 'default' } = this.chartUserConfig

      this.colorSchemeObject.currentColorScheme = colorScheme
      this.colorPanelList = this._COLOR_SYSTEM[colorScheme]

      this.colorSchemeObject.opacity = opacityList[colorScheme] || 1
      this.changeOpacity(this.colorSchemeObject.opacity)
    },
    // 调整透明度
    changeOpacity(opacity) {
      if (!this.isSystemScheme) return
      this.colorPanelList = this.colorPanelList.map(color => {
        const index = color.lastIndexOf(',') + 1
        return color.replace(color.substr(index), `${opacity})`)
      })
      // const tooltip = this.$_getProp(this.$refs, 'colorSlider.$refs.button1.$refs.tooltip', '')
      // tooltip && (tooltip.showPopper = false)
    },
    // 点击确定
    colorSchemeSettingHandler() {
      const { colorScheme = 'default' } = this.chartUserConfig
      // 保存色系
      if (this.colorSchemeObject.currentColorScheme !== colorScheme) {
        this.$set(this.chartUserConfig, 'colorScheme', this.colorSchemeObject.currentColorScheme)
      }

      // 保存透明度
      this.changeSchemeOpacity()

      // 重置维度颜色
      this.setDimensionColors()
      // 重置度量颜色
      this.setMetricColors()

      this.eventEmitHandler('COLOR_SCHEME')
      this.colorSchemeObject.visible = false
    },
    // 更新维度颜色
    setDimensionColors() {
      this.$delete(this.chartUserConfig, 'dimensionColors')
      const { dimension = [], metrics = [] } = this.element.content.chartSettings
      // 没有度量或者维度时先不管
      if (!dimension.length || !metrics.length) return []

      // const params = {
      //   chartUserConfig: this.getChartSetData('chartUserConfig'),
      //   chartData: this.getChartSetData('chartData'),
      //   themeType: this.themeType,
      // }
      const opacity = this.colorSchemeObject.opacity
      const colorScheme = this.chartUserConfig.colorScheme
      const dimensionColors = Color.getDimensionColor(this.element.vm || this.chartVm, this.chartUserConfig.colorType)
      let oldDimensionColors = (this.chartUserConfig.dimensionColors || []).map(c => {
        if (!c.type) {
          const colorFormatter = Color.generateColorItem(c.color, { name: c.name, colorScheme, opacity, })
          return Object.assign({}, c, colorFormatter)
        }
        return Color.setColorOpacity(c, opacity)
      })
      // dimensionColors.needRefeash：之前没有的维度数据
      this.$set(this.chartUserConfig, 'dimensionColors', [...oldDimensionColors, ...dimensionColors.needRefeash])
    },
    setMetricColors() {
      if (this.colorSchemeObject.currentColorScheme !== 'default') {
        this.$set(this.chartUserConfig, 'colors', this.$_deepClone(this.colorPanelList))
      } else {
        this.$set(
          this.chartUserConfig,
          'colors',
          Color.getDefaultChartColor(
            this.chartUserConfig,
            { colorThemeType: this.themeType, chartResponse: this.element.content.chartResponse, vm: this.element.vm || this.chartVm }
          )
        )
      }
    },
    // 保存透明度
    changeSchemeOpacity() {
      // 如果更换了透明度
      let { opacityList = { [this.colorSchemeObject.currentColorScheme]: 1 } } = this.chartUserConfig
      if (this.colorSchemeObject.opacity !== opacityList[this.colorSchemeObject.currentColorScheme]) {
        const copyOpacityList = Object.assign(
          {},
          opacityList,
          { [this.colorSchemeObject.currentColorScheme]: this.colorSchemeObject.opacity }
        )
        this.$set(this.chartUserConfig, 'opacityList', copyOpacityList)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.color-system-container /deep/{
  margin-bottom: 38px;
  .color-system-example{
      .color-example {
        &:not(:nth-child(8n)) {
          margin-right: 9px;
        }
        &:nth-child(8n) {
          margin-right: 0px;
        }
      }
  }
}
.color-scheme-opacity-container{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  margin-bottom: 20px;
  .opacity-value{
    border: 1px solid var(--sdp-srk-bks);
    background-color: var(--sdp-ycsz-srk-bgs);
    width: 68px;
    height: 32px;
    line-height: 30px;
    border-radius: 2px;
    font-size: 14px;
    text-align: center;
    color: var(--sdp-cszjq-is);
  }
}
.scheme-preview{
  position: relative;
  top: -8px;
  margin-right: 8px;
  width: 14px;
  height: 18px;
  display: inline-flex;
  flex-wrap: wrap;
  >div{
    width: 7px;
    height: 6px;
  }
}
</style>
