<template>
  <!-- :data-theme="(themeData.screenMode && themeData.themeType) || 'sdp-classic-white'" -->
  <el-dialog
    :class="[
      'dialog-container board-design-pop sdp-chartSet-dialog',
      getCurrentThemeClass()
    ]"
    :title="$t('sdp.views.tableExcelSet')"
    fullscreen
    ref="chartSetDialog"
    :visible.sync="visible"
    :append-to-body="true"
    :show-close="false"
    :modal="false"
    :close-on-click-modal="false"
    @opened="handleDialogOpened"
  >
    <div class="flex-row justify-between" slot="title">
      <div class="flex-row align-center">
        <i class="icon-sdp-fanhui back-icon pointer" @click="closeDialog"></i>
        <template v-if="configs && configs.type === 'template'">
          <div class="chart-edit first-font-color" v-if="configs.options">
            {{
              configs.options.operateType === 'add'
                ? $t('sdp.views.addChart')
                : $t('sdp.views.editChart')
            }}
          </div>
          <div class="input-container-header">
            <el-input
              class="chart-input"
              v-model="currentEditData.elName"
              :maxlength="onlyIndex ? 128 : 30"
              :placeholder="
                $t('sdp.placeholder.pleaseInputProp', {
                  prop: onlyIndex ? $t('sdp.views.name') : $t('sdp.views.chartName')
                })
              "
              :disabled="isNameDisabled"
            >
            </el-input>
            <el-input
              v-if="onlyIndex"
              class="chart-input"
              v-model="currentEditData.remark"
              maxlength="500"
              :placeholder="$t('sdp.placeholder.pleaseInputProp', { prop: onlyIndex ? $t('sdp.views.remark') : $t('sdp.views.chartDescription') })"
            >
            </el-input>
            <el-radio-group
              v-if="!onlyIndex"
              v-model="templateTerminalType"
              class="template-type"
              @change="templateTerminalTypeChange"
            >
              <el-radio label="1" style="margin-right: 34px;">PC</el-radio>
              <el-radio label="2">APP</el-radio>
            </el-radio-group>
          </div>
        </template>
      </div>
      <div class="sdp-chart-button">
        <!-- 预览 -->
        <el-dropdown
          v-if="configs && configs.type === 'template'"
          class="preview-dropdown"
          size="small"
          placement="bottom-start"
          trigger="click"
          split-button
          @click="previewHandler()"
        >
          {{ $t('sdp.button.preview') }}
          <el-dropdown-menu :class="`${getCurrentThemeClass()} sdp-dialog sdp-dropdown sdp-min-width-140`" slot="dropdown">
            <el-dropdown-item
              v-for="lang in languageList"
              :key="lang.isoCode"
              @click.native="previewHandler(lang.isoCode)"
            >
              {{ lang.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <ChartGuidePopover
          :content="$t('sdp.guide.clickToSaveTheDashboard')"
          :value.sync="isShowSaveTips"
          :markPaddingLeft="7"
          :markPaddingRight="7"
          :markPaddingTop="6"
          :markPaddingBottom="6"
          :arrowOffsetX="-12"
          :tipsOffsetY="-18"
        >
          <el-button
            type="primary"
            size="mini"
            @click="saveChange"
            class="el-button--sdp-ensure"
            >{{ $t('sdp.button.save') }}</el-button
          >
        </ChartGuidePopover>
      </div>
    </div>
    <keep-alive>
      <div v-if="visible" class="body-cotainer flex-row">
        <!-- 数据集区域 -->
        <compontent
          :is="datasetType"
          :ref="datasetType"
          type="chart"
          :indicatorIndex="indicatorIndex"
          :chartUserConfig="chartUserConfig"
          :currentEditData="currentEditData"
          :drillSettings="drillSettings"
          :datasetLoading="datasetLoading"
          :datasetArray="datasetArray"
          :gridDatasetList.sync="selectedDataset"
          :associatedDataset="associatedDataset"
          :boardDatasetList="platformDataList"
          :cellDatas="selectedDataset.map(item => item.id)"
          @remove-dataset-list="removeDatasetList"
          @delete-result-dataset="deleteResultDataset"
          @open-link-dataset="openLinkDatasetDialog"
          @on-change-dataset="getSelections"
          @set-clone-selection-list="setCloneSelectionList"
          @open-dataset-association="
            $refs['datasetAssociation'].beforeOpenDialog()
          "
          @on-change-index="changeIndex"
        />
        <div class="layout-center flex-column">
          <!-- 配置维度度量 -->
          <GuidePopover
            :content="$t('sdp.guide.configureDimensionAndMeasure')"
            :value.sync="isShowParamsSetTips"
            :markPaddingLeft="0"
            :markPaddingRight="0"
            :markPaddingTop="0"
            :markPaddingBottom="0"
            :arrowOffsetX="0"
            :tipsOffsetY="-20"
            :needComputed="true"
          >
            <div class="param-setting ">
              <!-- 维度 -->
                <div
                  class="param-selection dragbox-outter-container"
                  v-show="showDimensionDragbar"
                >
                  <span class="param-selection-title" style="font-weight: 700;"
                    >{{
                      isBarStack
                        ? $t('sdp.views.categoryAxisDimension')
                        : $t('sdp.views.dimensions')
                    }}
                  </span>
                  <div class="flex1">
                    <drag-box
                      ref="dimensionDragBox"
                      isHorizontal
                      dragBoxType="dimension"
                      type="dimension"
                      :isBoundToIntersectionLocation="
                        isBoundToIntersectionLocation
                      "
                      :isShowMapDestination="isShowMapDestination"
                      :drillSettings="drillSettings"
                      :init-chart="initChart"
                      :drag-option="{
                        group: {
                          name: 'dimension',
                          put: checkDimensionPut
                        }
                      }"
                      :indicator-index="indicatorIndex"
                      :chart-user-config="chartUserConfig"
                      :current-edit-data="currentEditData"
                      :prevent.sync="preventRequest"
                      @clear-status="clearStatus"
                      @on-change="onDimensionChange"
                      @on-customsort="onCustomsort"
                    />
                    <!-- 维度扩展 -->
                    <span
                      v-if="isShowExtendDimension"
                      @click="clickDimensionExtend"
                      class="drag-box-text"
                      :title="$t('sdp.views.addExtendDimension')"
                      ><i class="icon-sdp-xinzenganniu"></i
                    ></span>
                    <!-- 地图经纬度模式 -->
                    <RadioGroup
                      v-if="isMap"
                      :disabled="isHotMap"
                      style="width: 180px; position: absolute; top: 8px; right: 0;"
                      type="MapChartMode"
                      v-model="chartUserConfig.mapMode"
                      @on-change="handleMapMode"
                    ></RadioGroup>
                    <!-- 带宽模式 -->
                    <RadioGroup
                      v-if="isBandwidth"
                      style="width: 180px; position: absolute; top: 8px; right: 0;"
                      type="BandWidthChartMode"
                      v-model="chartUserConfig.bandwidthMode"
                      @on-change="handleBandwidthMode"
                    ></RadioGroup>
                    <!-- 水滴图 -->
                    <RadioGroup
                      v-if="
                        isLiquidFill &&
                          liquidFillMode === VE_LIQUIDFILL_MODE.DIMENSION
                      "
                      style="width: 200px; position: absolute; top: 8px; right: 0;"
                      type="LiquidFillMode"
                      v-model="liquidFillMode"
                      @on-change="handleLiquidFillMode"
                    ></RadioGroup>
                  </div>
                </div>

              <div
                class="param-selection dragbox-outter-container"
                v-show="isShowMapDestination">
                <span class="param-selection-title" style="font-weight: 700;">{{ $t('sdp.views.destinationField') }}</span>
                <!-- 地图目的地 -->
                <drag-box
                  ref="destinationDragBox"
                  isHorizontal
                  dragBoxType="dimension-destination"
                  :drillSettings="drillSettings"
                  :init-chart="initChart"
                  :drag-option="{
                    group: {
                      name: 'dimension'
                    }
                  }"
                  :indicator-index="indicatorIndex"
                  :chart-user-config="chartUserConfig"
                  :current-edit-data="currentEditData"
                  @on-change="
                    onMapDestinationChange($event, 'destination')
                  "
                />
              </div>

              <!-- 地图经纬度 -->
              <div
                class="map-mode_box dragbox-outter-container"
                v-show="
                  isMap && mapMode === 'longitudeAndLatitude' && !isHotMap
                "
              >
                <div class="param-selection">
                  <span
                    class="param-selection-title"
                    style="font-weight: 700;"
                    >{{ $t('sdp.views.longitude') }}</span
                  >
                  <div class="flex1">
                    <drag-box
                      ref="longitudeDragBox"
                      isHorizontal
                      isLongitude
                      :drillSettings="drillSettings"
                      :init-chart="initChart"
                      :drag-option="{
                        group: {
                          name: 'dimension'
                        }
                      }"
                      :indicator-index="indicatorIndex"
                      :chart-user-config="chartUserConfig"
                      :current-edit-data="currentEditData"
                      @on-change="
                        onLongitudeAndLatitudeChange($event, 'longitude')
                      "
                    />
                  </div>
                </div>
                <!-- 地图经纬度 -->
                <div class="param-selection">
                  <span
                    class="param-selection-title"
                    style="font-weight: 700;"
                    >{{ $t('sdp.views.latitude') }}</span
                  >
                  <div class="flex1">
                    <drag-box
                      ref="latitudeDragBox"
                      isHorizontal
                      isLatitude
                      :drillSettings="drillSettings"
                      :init-chart="initChart"
                      :drag-option="{
                        group: {
                          name: 'dimension'
                        }
                      }"
                      :indicator-index="indicatorIndex"
                      :chart-user-config="chartUserConfig"
                      :current-edit-data="currentEditData"
                      @on-change="
                        onLongitudeAndLatitudeChange($event, 'latitude')
                      "
                    />
                  </div>
                </div>
              </div>

              <!-- 扩展维度 -->
              <div
                class="param-selection dragbox-outter-container"
                v-show="extendDragBoxShow"
              >
                <span class="param-selection-title" style="font-weight: 700;">{{
                  isBarStack
                    ? $t('sdp.views.colorExtendDimension')
                    : $t('sdp.views.extend')
                }}</span>
                <div class="flex1">
                  <drag-box
                    ref="extendDimensionDragBox"
                    isHorizontal
                    dragBoxType="dimension-extend"
                    type="extendDimension"
                    :init-chart="initChart"
                    :drillSettings="drillSettings"
                    :drag-option="{
                      group: {
                        name: 'dimension'
                      }
                    }"
                    :indicator-index="indicatorIndex"
                    :chart-user-config="chartUserConfig"
                    :current-edit-data="currentEditData"
                    :prevent.sync="preventRequest"
                    @clear-status="clearStatus"
                    @on-change="onExtendDimensionChange"
                    @on-customsort="onCustomsort"
                    @clear-lengend-select-settings="clearLengendSelectSettings"
                  />
                  <!-- 移除维度扩展 -->
                  <span
                    class="drag-box-text"
                    @click="moveExtendDimension"
                    :title="$t('sdp.views.moveExtendDimension')"
                    ><i class="icon-sdp-liebiao_shibai"></i
                  ></span>
                </div>
              </div>
              <!-- X轴/Y轴维度 -->
              <div
                v-show="isBarHeatmap"
                class="param-selection dragbox-outter-container"
                v-for="(dimension, index) of dimensionTypes"
                :key="dimension.type+'1'"
              >
                <span class="param-selection-title" style="font-weight: 700;">
                 {{
                  dimension.type === 'x' || dimension.type === 'y'
                      ? dimension.type.toUpperCase() +
                        `-${$t('sdp.views.axisDimension')}`
                      : ''
                  }}
                </span>
                <div class="flex1">
                  <drag-box
                    :ref="'dimensionDragBox'+ dimension.type"
                    isHorizontal
                    dragBoxType="dimension"
                    type="dimension"
                    :init-chart="initChart"
                    :drag-option="{
                      group: {
                        name: 'dimension',
                        put: checkDimensionPut
                      },
                      sort: true
                    }"
                    :drillSettings="drillSettings"
                    :indicator-index="indicatorIndex"
                    :chart-user-config="chartUserConfig"
                    :dimension-type="dimension.type"
                    :current-edit-data="currentEditData"
                    :prevent.sync="preventRequest"
                    @clear-status="clearStatus"
                    @on-change="onDimensionChange($event, dimension.type)"
                    @on-customsort="onCustomsort"
                  />
                </div>
              </div>
              <!-- 度量 -->
              <div
                class="param-selection dragbox-outter-container"
                v-for="(metric, index) of metricTypes"
                :key="metric.type"
              >
                <span class="param-selection-title" style="font-weight: 700;">
                  {{
                    currentEditData.content && currentEditData.content.alias === 've-scatter-normal'
                      ? ''
                      : onlyIndex ? $t('sdp.views.Index') : $t('sdp.views.measure')
                  }}{{
                    (metric.type === 'x' || metric.type === 'y') && !isBarHeatmap
                      ? metric.type.toUpperCase() +
                        `-${$t('sdp.views.baseShaft')}`
                      : ''
                  }}
                </span>
                <chart-metric-type-select
                  ref="chartMetricTypeSelect"
                  v-show="
                    currentEditData.content &&
                      currentEditData.content.alias === 've-composite'
                  "
                  :type="index === 0 ? 'histogram' : 'line'"
                  :selectedChart="chartUserConfig.compositeChart"
                  @change-metric-chart-type="
                    type =>
                      changeMetricChartType(
                        index === 0 ? 'histogram' : 'line',
                        type
                      )
                  "
                />
                <!-- 度量 -->
                <div class="flex1">
                  <drag-box
                    :ref="'metric' + metric.type"
                    isMetric
                    isHorizontal
                    type="metric"
                    :init-chart="initChart"
                    :drag-option="{
                      group: {
                        name: 'dimension'
                      },
                      sort: true
                    }"
                    :drillSettings="drillSettings"
                    :indicator-index="indicatorIndex"
                    :chart-user-config="chartUserConfig"
                    :metric-type="metric.type"
                    :current-edit-data="currentEditData"
                    :prevent.sync="preventRequest"
                    @clear-status="clearStatus"
                    @restore-chart-color="changeColor"
                    @sort="onMetricsChange(arguments, metric.type)"
                    @on-change="onMetricsChange(arguments, metric.type)"
                    @clear-lengend-select-settings="clearLengendSelectSettings"
                    @update-metric-label="updateMetricLabel"
                  />
                  <!-- 双轴图形，添加次轴 -->
                  <span
                    v-if="index === 0 && isShowAddDeputyAxis"
                    @click="setIsShowDeputyAxis(true)"
                    class="drag-box-text"
                    :title="$t('sdp.views.addSecondAxis')"
                  >
                    <i class="icon-sdp-xinzenganniu"></i>
                  </span>
                  <!-- 双轴图形，删除次轴 -->
                  <span
                    v-if="index === 1 && isShowDeleteDeputyAxis"
                    class="drag-box-text"
                    @click="setIsShowDeputyAxis(false)"
                    :title="$t('sdp.views.deleteSecondAxis')"
                  >
                    <i class="icon-sdp-liebiao_shibai"></i>
                  </span>
                  <!-- 水滴图 -->
                  <RadioGroup
                    v-if="
                      metric.type === 'default' &&
                        isLiquidFill &&
                        liquidFillMode === VE_LIQUIDFILL_MODE.NORMAL
                    "
                    style="width: 200px; position: absolute; top: 8px; right: 0;"
                    type="LiquidFillMode"
                    v-model="liquidFillMode"
                    @on-change="handleLiquidFillMode"
                  ></RadioGroup>
                </div>
              </div>
              <!-- 配置目标值 -->
              <div
                class="param-selection dragbox-outter-container"
                v-if="componentShow.targetDragbox && currentEditData.content"
              >
                <span class="param-selection-title" style="font-weight: 700;">{{
                  $t('sdp.views.TargetValueDial')
                }}</span>
                <chart-target-change
                  ref="chartTargetChange"
                  type="target"
                  @clear-target="clearTarget"
                  :gaugeTarget="gaugeTarget"
                ></chart-target-change>
                <!-- oninput="value=value.replace(/[^\-\d.]/g,'').replace(/^\-/, '$#$').replace(/\./, '#$#').replace(/[\-\.]/g, '').replace('$#$', '-').replace('#$#', '.')" -->
                <el-input
                  style="width:140px; margin-left:10px"
                  clearable
                  @change="codeNumber"
                  @clear="onTargetChange"
                  v-model="gaugeTarget.exp"
                  :class="[{ none: gaugeTarget.type === 'aggType' }]"
                ></el-input>
                <div
                  class="flex1"
                  :disabled="gaugeTarget.type !== 'aggType'"
                  :class="[{ none: gaugeTarget.type !== 'aggType' }]"
                >
                  <drag-box
                    ref="targetDragBox"
                    isHorizontal
                    isMetric
                    isTargetLable
                    type="target"
                    metricType="ve-gauge-normal"
                    :drillSettings="drillSettings"
                    :drag-option="{
                      group: {
                        name: 'target'
                      }
                    }"
                    :indicator-index="indicatorIndex"
                    :chart-user-config="chartUserConfig"
                    :current-edit-data="currentEditData"
                    :prevent.sync="preventRequest"
                    @clear-status="clearStatus"
                    @sort="onTargetChange"
                    @on-change="onTargetChange"
                  />
                </div>
              </div>
              <!-- 配置对比值 -->
              <div
                class="param-selection dragbox-outter-container"
                v-if="showContrastDragBox"
              >
                <span class="param-selection-title" style="font-weight: 700;">{{
                  $t('sdp.views.contrastValue')
                }}</span>
                <div class="flex1">
                  <drag-box
                    ref="contrastValueDragBox"
                    isHorizontal
                    type="contrastValue"
                    dragBoxType="chartContrastValue"
                    :init-chart="initChart"
                    :drillSettings="drillSettings"
                    :drag-option="{
                      group: {
                        name: 'dimension'
                      }
                    }"
                    :indicator-index="indicatorIndex"
                    :chart-user-config="chartUserConfig"
                    :current-edit-data="currentEditData"
                    :prevent.sync="preventRequest"
                    @clear-status="clearStatus"
                    @on-change="onContrastValueChange"
                    @on-customsort="onCustomsort"
                  />
                </div>
              </div>
              <!-- 带宽模式 -->
              <div
                class="map-mode_box dragbox-outter-container"
                v-show="isBandwidth"
              >
                <!-- 最大值 -->
                <div
                  class="param-selection"
                  v-show="bandwidthMode === VE_BANDWIDTH_TYPE.CUSTOM_BANDWIDTH"
                >
                  <span
                    class="param-selection-title"
                    style="font-weight: 700;"
                    >{{ $t('sdp.views.maximumBandwidth') }}</span
                  >
                  <div class="flex1">
                    <drag-box
                      ref="maxBandwidthDragBox"
                      type="bandwidth"
                      isHorizontal
                      isMaxBandwidth
                      :drillSettings="drillSettings"
                      :init-chart="initChart"
                      :drag-option="{
                        group: {
                          name: 'dimension'
                        }
                      }"
                      :indicator-index="indicatorIndex"
                      :chart-user-config="chartUserConfig"
                      :current-edit-data="currentEditData"
                      @on-change="onBandwidthChange($event, 'maximumBandwidth')"
                    />
                  </div>
                </div>
                <!-- 最小值 -->
                <div
                  class="param-selection"
                  v-show="bandwidthMode === VE_BANDWIDTH_TYPE.CUSTOM_BANDWIDTH"
                >
                  <span
                    class="param-selection-title"
                    style="font-weight: 700;"
                    >{{ $t('sdp.views.minimumBandwidth') }}</span
                  >
                  <div class="flex1">
                    <drag-box
                      ref="minBandwidthDragBox"
                      type="bandwidth"
                      isHorizontal
                      isMinBandwidth
                      :drillSettings="drillSettings"
                      :init-chart="initChart"
                      :drag-option="{
                        group: {
                          name: 'dimension'
                        }
                      }"
                      :indicator-index="indicatorIndex"
                      :chart-user-config="chartUserConfig"
                      :current-edit-data="currentEditData"
                      @on-change="onBandwidthChange($event, 'minimumBandwidth')"
                    />
                  </div>
                </div>
                <!-- 带宽 -->
                <div
                  class="param-selection"
                  v-show="
                    bandwidthMode === VE_BANDWIDTH_TYPE.AUTOMATIC_BANDWIDTH
                  "
                >
                  <span
                    class="param-selection-title"
                    style="font-weight: 700;"
                    >{{ $t('sdp.views.bandwidth') }}</span
                  >
                  <div class="flex1">
                    <drag-box
                      ref="bandwidthDragBox"
                      type="bandwidth"
                      isHorizontal
                      isAutoBandwidth
                      :drillSettings="drillSettings"
                      :init-chart="initChart"
                      :drag-option="{
                        group: {
                          name: 'dimension'
                        }
                      }"
                      :indicator-index="indicatorIndex"
                      :chart-user-config="chartUserConfig"
                      :current-edit-data="currentEditData"
                      @on-change="onBandwidthChange($event, 'bandwidth')"
                    />
                  </div>
                </div>
              </div>
            </div>
          </GuidePopover>
          <div class="flex-row chart-background" :class="getChartHeightStyle()">
            <!-- 图表显示区域 -->
            <chart-content
              v-if="drillSettings"
              ref="chartContent"
              class="chart-content flex1"
              :element="currentEditData"
              :is-chart-set="true"
              :elList="elList"
              :boardInfo="boardInfo"
              @preview-chart="previewChart"
              @init-colors-config="initColorsConfig"
            />

            <div class="flex-column" style="width: 140px;" v-if="false">
              <!-- 分割线 -->
              <!-- <div class="cutline color-cutline">
                指标选择器预留位置 （内容在chartContent组件中）
              </div> -->
              <el-tabs
                v-if="
                  chartUserConfig &&
                    !(
                      isLiquidFill &&
                      liquidFillMode === VE_LIQUIDFILL_MODE.NORMAL
                    )
                "
                v-model="activeTabsName"
                :stretch="true"
                value="color"
                class="flex1"
              >
                <el-tab-pane
                  :label="$t('sdp.placeholder.color')"
                  name="color"
                  v-if="
                    !['ve-grid-normal'].includes(chartUserConfig.chartAlias)
                  "
                >
                  <ColorSetting
                    ref="colorSetting"
                    :chartSettings="chartSettings"
                    :chart-data="formatChartData"
                    :chartVm="$refs.chartContent"
                    :themeType="themeType"
                    :element="currentEditData"
                    @eventEmit="handler_ColorSetting"
                  />
                </el-tab-pane>
                <el-tab-pane
                  :label="$t('sdp.views.boardSize')"
                  name="size"
                  v-if="alias === 've-scatter-normal'"
                >
                  <size-setting
                    ref="sizeSetting"
                    :size-metric="sizeMetric"
                    :symbol-size-max="symbolSizeMax"
                    :chart-data="chartData"
                    :current-edit-data="currentEditData"
                    @clear-status="clearStatus"
                    @on-metric-change="onMetricsChange(arguments, 'size')"
                    @on-change="handleConfigChange"
                  />
                </el-tab-pane>
              </el-tabs>
              <!-- 分割线 -->
              <div class="cutline" v-if="!isGaugeType"></div>
              <!-- 数据过滤 -->
              <DataFiltering
                :datasetList="filteringDatasetList"
                elementType="chart"
                :drillSettings="drillSettings"
                @dataFilteringCallback="dataFilteringCallback"
              ></DataFiltering>
            </div>
          </div>
        </div>
        <!-- 图表属性配置 -->
        <div class="setting setting-wide" :class="{'has-sub-type-chart': chartHasSubType}" ref="settingRef" v-if="true">
          <!-- 图表配置 -->
          <el-scrollbar
            style="height:100%"
            v-sdp-el-scrollbar="utils.themeParameters"
          >
            <div style="padding:0 16px 16px 16px">
              <table-info-wide
                ref="tableInfo"
                :data-filtering-options="{
                  datasetList: filteringDatasetList,
                  elementType: 'chart',
                  drillSettings: drillSettings,
                }"
                @data-filtering-callback="dataFilteringCallback"

                :current-edit-data="currentEditData"
                :themeType="themeType"
                :datasetList="datasetArray"
                :platformDataList="platformDataList"
                :init-chart="initChart"
                :indicator-index="indicatorIndex"
                :isByTheInteraction="
                  $refs.chartContent && $refs.chartContent.isByTheInteraction
                "
                @on-chart-set="handleConfigChange"
                @mapScheme-change="mapSchemeChange"
                @change-assocciation-switch="changeAssocciationSwitch"
                @init-metric-dimension="initMetricDimension"
                @sort-chioce-tab="sortChioceTab"
                @preview-chart="previewChart"
                @init-colors-config="initColorsConfig"
                @keep-axis-rang="$refs.chartContent.keepAxisRang()"
                @do-register-map="doRegisterMap"
                @set-update-chart="setUpdateChart"
                @change="tableInfoChangeHandler"
                @eventBus="eventBus"
              >
              <template #sizeSetting>
                  <size-setting
                    ref="sizeSetting"
                    :size-metric="sizeMetric"
                    :symbol-size-max="symbolSizeMax"
                    :chart-data="chartData"
                    :current-edit-data="currentEditData"
                    @clear-status="clearStatus"
                    @on-metric-change="onMetricsChange(arguments, 'size')"
                    @on-change="handleConfigChange"
                  />
              </template>
              <template #colorSetting>
                <ColorSetting
                    ref="colorSetting"
                    :chartSettings="chartSettings"
                    :chart-data="formatChartData"
                    :chartVm="$refs.chartContent"
                    :themeType="themeType"
                    :element="currentEditData"
                    @eventEmit="handler_ColorSetting"
                  />
              </template>
            </table-info-wide>
            </div>
          </el-scrollbar>
        </div>
        <!-- 图表属性配置 -->
        <div class="setting" ref="settingRef" v-else>
          <!-- <span class="sdp-title-prefix-stamp"></span> -->
          <!-- 图表配置 -->
          <div>
            <table-info
              ref="tableInfo"
              :current-edit-data="currentEditData"
              :themeType="themeType"
              :datasetList="datasetArray"
              :platformDataList="platformDataList"
              :init-chart="initChart"
              :indicator-index="indicatorIndex"
              :isByTheInteraction="
                $refs.chartContent && $refs.chartContent.isByTheInteraction
              "
              :interactionReferencedInfo.sync="interactionReferencedInfo"
              @on-chart-set="handleConfigChange"
              @mapScheme-change="mapSchemeChange"
              @change-assocciation-switch="changeAssocciationSwitch"
              @init-metric-dimension="initMetricDimension"
              @sort-chioce-tab="sortChioceTab"
              @preview-chart="previewChart"
              @init-colors-config="initColorsConfig"
              @keep-axis-rang="$refs.chartContent.keepAxisRang()"
              @do-register-map="doRegisterMap"
              @set-update-chart="setUpdateChart"
              @change="tableInfoChangeHandler"
              @eventBus="eventBus"
            />
          </div>
        </div>
      </div>
    </keep-alive>
    <component
      :is="componentId"
      @closeDia="closeDia"
      :visible.sync="datasetDialogVisible"
      :dataWarehouseModelingSwitch="dataWarehouseModelingSwitch"
      @updateDaSetIds="updateDaSetIds"
      :dataSets="{
        type: 'chart' + ((configs && configs.type && '-' + configs.type) || ''),
        datasetList: datasetArray,
        selection: 'single'
      }"
    ></component>
    <DatasetAssociation
      ref="datasetAssociation"
      isChart
      :datasetList="selectedDataset"
      :table-default-config="
        currentEditData.content && currentEditData.content.associatedData
      "
      @set-result-dataset="setResultDataset"
      @delete-result-dataset="deleteResultDataset"
    />
    <!-- CR_8319: 此面板是否能与"筛选器指引面板"合并 -->
    <div
      class="kanban-guide-panel"
      ref="kanbanGuidePanelRef"
      v-if="kanbanGuideIsVisible"
    >
      <div class="guide-header">
        <div class="guide-title">{{ $t('sdp.guide.operatingGuide') }}</div>
        <i
          class="el-icon-close guide-header-close"
          @click="closeGuidePanel"
        ></i>
      </div>
      <div class="guide-content-box">
        <!-- 配置元素内容 -->
        <div class="guide-entry">
          <span class="guide-entry-name">
            <span class="guide-entry-index">3.</span>
            <span>{{ $t('sdp.guide.configurationElementContent') }}</span>
          </span>
        </div>
        <div class="sdp-steps">
          <!-- 编辑图表 -->
          <div class="sdp-step-item is-done">
            <div class="sdp-step-dot"></div>
            <div class="sdp-step-text not-allowed">
              <span>{{ $t('sdp.guide.editTheChart') }}</span>
            </div>
            <div class="sdp-step-line">
              <div class="sdp-step-border"></div>
            </div>
          </div>
          <!-- 添加图表数据 -->
          <div
            class="sdp-step-item"
            v-if="$refs.CommonDataset"
            :class="[
              getStepClassName({
                isDone: $refs.CommonDataset.datasetIsDone,
                isDoing: $refs.CommonDataset.datasetIsDoing
              })
            ]"
          >
            <div class="sdp-step-dot"></div>
            <div class="sdp-step-text" @click="showTheStepTips('addChartData')">
              <span>{{ $t('sdp.guide.addChartDataset') }}</span>
            </div>
            <div class="sdp-step-line">
              <div class="sdp-step-border"></div>
            </div>
          </div>
          <!-- 拖拽字段至维度,度量区域 -->
          <div
            class="sdp-step-item"
            v-if="$refs['dimensionDragBox']"
            :class="[
              getStepClassName({
                isDone: paramsSetIsDone,
                isDoing: paramsSetIsDoing
              })
            ]"
          >
            <div class="sdp-step-dot"></div>
            <div class="sdp-step-text" @click="showTheStepTips('dragParams')">
              <span>{{ $t('sdp.guide.dragFieldsToDimensionAndMeasureArea') }}</span>
            </div>
            <div class="sdp-step-line">
              <div class="sdp-step-border"></div>
            </div>
          </div>
          <!-- paramsSetIsDone 配置图表功能和样式跟保存合一起-->
          <!-- <div class="sdp-step-item " :class="!!paramsSetIsDone ? '' : 'is-todo'">
            <div class="sdp-step-dot"></div>
            <div class="sdp-step-text" @click="showTheStepTips('setChartStyle')">
              <span>配置图表功能和样式</span>
            </div>
            <div class="sdp-step-line">
              <div class="sdp-step-border"></div>
            </div>
          </div> -->
          <!-- 保存图表 -->
          <!-- initializedByKanbanFilterAllMixin -->
          <div class="sdp-step-item" v-if="$refs['dimensionDragBox']" :class="[
              getStepClassName({
                isDone: saveIsDone,
                isDoing: saveIsDoing
              })
            ]">
            <div class="sdp-step-dot"></div>
            <div class="sdp-step-text" @click="showTheStepTips('saveChart')">
              <span>{{ $t('sdp.guide.saveChart') }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import ReferenceDataSet from 'packages/base/common/referenceDataSet'
import ChartContent from '../boardElements/elementChart'
import DataFiltering from 'packages/base/board/displayPanel/supernatant/common/dataFiltering'
import tableInfo from './components/tableInfoOld'
import tableInfoWide from './components/tableInfo'
import dragBox from './components/dragbox'
import { ColorSetting } from './functions'
import SizeSetting from './components/sizeSetting'
import chartMetricTypeSelect from './components/chartMetricTypeSelect'
import chartTargetChange from './components/chartTargetChange.vue'
import { ChartGuidePopover, GuidePopover } from 'packages/base/KanbanGuide/components'
// import elRenderHelperMixin from '../elRenderHelperMixin'
import bridge, { handleSettings, handleExtraSettings, isSetMeasureConfig, warningToAlterLogic, updatePolymericFilters } from '../boardElements/elementChart/bridge'
import { chartPreview, getCompareTypeList } from './api'
import { getChartsList, handleFourQuadrantLine, getBandwidthData, getBandwidthInitData, isThemeRiver } from '../boardElements/elementChart/chartsList/charts'
import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import { EVENT_TYPE } from 'packages/base/elementTemplate/constants'
import DatasetAssociation from 'packages/base/grid/components/dataset/DatasetAssociation.vue'
import { generateMetricLabelDisplayItem } from 'packages/base/board/displayPanel/supernatant/chartSet/utils/generator'
import {
  createDimension,
  createMeasure,
  EXTEND_DIMENSION_CHART,
  HANDLE_MAX_MIN_VALUE_CHART,
  WARNING_LINE_CHART,
  CIRCULAR_CHART,
  SINGLE_METRIC_CHART,
  HAS_TARGET_CHART,
  SPEICAL_DATA_STRUCTURECHART,
  MOBILE_PAGE_CHART,
  NO_DIMENSION_CHART,
  DOUBLE_DIMENSION_CHART,
  SUPPORT_DEPUTY_AXIS,
  SPECIAL_SUPPORT_DEPUTY_AXIS,
  NEED_TIMEDIMENSION_CHART,
  STACK_ACC_TYPES,
  CUSTOM_METRIC,
  CHART_ALIAS_TYPE,
  VE_BANDWIDTH_TYPE,
  STACK_ACC_CHART,
  CUSTOM_DATE_DIMENSION_CHART,
  ANIMATION_HISTOGRAM_CHART,
  ANIMATION_TOOLTIP_CHART,
  DATE_DIMENSION_TYPE,
  VE_LIQUIDFILL_MODE,
  chartsList,
  SOURCE_DATA_TYPE,
} from '../boardElements/elementChart/constant'
import {
  setThemeChartUserConfig,
  setThemeConfig,
  getThemeConfig
} from '../boardElements/elementChart/chartsList/theme'
import { isReverseAxisChart } from '../boardElements/elementChart/chartsList/chartSetting/handleChartSetting'
import * as defaultSetting from '../boardElements/elementChart/chartsList/chartConfig'
import EventData from 'packages/assets/EventData'
import {
  EVENT_DATA_PARAMS,
  SUPERLINK_CONST_TYPE,
  THEME_TYPE
} from 'packages/assets/constant'
import eventBus from 'packages/assets/eventBus'
import {
  EVENT_BUS,
  GET_PLAT_VALUE_BY_CODE_PID_TYPE,
  paramsBindDatasets,
} from 'packages/base/board/displayPanel/constants'
import { handleChartSingleSetting, handleTotalChartSetting, intoScatterCombination } from './handlerChartSettingChange'
import { getAggType, setLgeTypeValue } from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'
import { updateReportLogObj } from 'packages/assets/utils/reportLog'
import { TYPE_PARAM_ELEMENT } from '../../params/utils/constants'
import { PARAM_FILTER_LABLE_TYPE_LABEL_SELECT } from '../../params/paramElement/locationNew/settingMixin_intersectionLocation'
import { getDataSetTreeByIds } from '../../../mixins/api'
import { ChartDesignHandler } from 'packages/base/board/displayPanel/supernatant/chartSet/functions/propHandler'
import { USER_PROP } from 'packages/base/board/displayPanel/supernatant/chartSet/functions/assets/index'
import { AssociateDataset, CommonDataset } from './dataset/index'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import { getDataWarehouseModelFlag } from '../../params/api'
import { RadioGroup } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
import { changeThemeType } from 'packages/assets/theme/theme-style/utils'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import { isRealScreenFn } from 'packages/helpers'

// ----------------分割线---------------------------------------

const DIMENSION = createDimension()
const METRICS = createMeasure()
const createSizeMetric = filed => {
  return Object.assign({}, METRICS, {
    columnType: 'number',
    columnName: filed,
    alias: filed,
    replaceable: true
  })
}

export default {
  mixins: [datasetMixin],
  inject: {
    utils: { default: {} },
    langCode: { default: 'zh' },
    commonData: { default: {} },
    themeData: { default: () => () => {} },
    boardData: { default: () => () => {} },
    configs: { default: () => false },
    authority: { default: {} },
    sdpBus: { default: () => ({}) },
    boardWarningSubscribeData: { default: null },
    getCurrentThemeClass: { default: () => () => '' }
  },
  name: 'ChartSet',
  directives: { SdpElScrollbar },
  components: {
    ReferenceDataSet,
    RadioGroup,
    dragBox,
    tableInfo,
    tableInfoWide,
    ChartContent,
    ColorSetting,
    SizeSetting,
    chartMetricTypeSelect,
    chartTargetChange,
    DatasetAssociation,
    AssociateDataset,
    CommonDataset,
    DataFiltering,
    ChartGuidePopover,
    GuidePopover
  },

  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String
    },
    datasetList: {
      type: Array,
      default: () => []
    },
    elList: {
      type: Array,
      default: () => []
    },
    boardInfo: {
      type: Object,
      default: () => {}
    },
    elId: String
  },
  provide() {
    return {
      chart: {
        fieldList: this.fieldList,
        allFieldList: () => this.allFieldList,
        datasetList: () => this.datasetArray,
        currentEditData: () => this.currentEditData,
        isChildMapScheme: () => this.$refs.chartContent.isChildMapScheme,
        isAssociationDataset: () => this.isAssociationDataset
      },
      chartElement: () => this.$refs.chartContent,
      themeType: this.themeType,
      isPending: () => this.isPending,
      selfChartDesignHandler: () => this.selfChartDesignHandler,
      USER_PROP,
      // 子组件获取本组件的值
      getChartSetData: k => this[k],
      isRelease: () => this.boardInfo.isRelease
    }
  },
  created() {
    this.isFirstChange = this.$_debounce(this.isFirstChange, 200)
    this.initSpecialAliasMetrics = this.$_debounce(
      this.initSpecialAliasMetrics
    )
  },
  mounted() {
    document.addEventListener('click', this.closeDurativesWhenClick, true)
    setTimeout(() => {
      let element = this.$refs.settingRef
      element?.addEventListener('scroll', this.clearAllSelect, true)
    }, 300)
    window.addEventListener('resize', this.clearAllSelect)
  },
  destroyed() {
    document.removeEventListener('click', this.closeDurativesWhenClick, true)
    let element = this.$refs.settingRef
    element?.removeEventListener('scroll', this.clearAllSelect)
    window.removeEventListener('resize', this.clearAllSelect)
  },
  data() {
    return {
      initializedByKanbanFilterAllMixin: false, // 延时计算的标识符
      saveIsDone: false,
      isShowParamsSetTips: false, // 拖拽操作指引
      isShowSaveTips: false, // 保存操作指引

      selfChartDesignHandler: null,
      componentId: '',
      datasetDialogVisible: false,
      datasetLoading: true,
      dataSetRes: [],
      isPending: false, // 正在请求数据标识
      cloneSelectionList: '', // 备份数据集字段列表，防止drag组件clone,影响业务
      initChart: false, // 图形初始化标识（进入弹窗或者指标选择器切换）
      activeTabsName: 'color',
      preventRequest: false,
      initCompleted: false,
      sizeMetric: {},
      isRowsReverse: false,
      dataWarehouseModelingSwitch: '0',
      metricTypes: [
        {
          type: 'default'
        }
      ],
      dimensionTypes: [
        {
          type: 'default'
        }
      ],
      currentEditData: {},
      prevChartSetting: null,
      dimensionList: [],
      metricsContainer: {},
      olddataSetId: null,
      isFirstDataSet: false,
      // 修改数据
      // 这里是所有用户修改配置参数的方法触发入口
      // 把在图表配置项绑定的chartUserConfig的修改映射到图表渲染的实际数据中去
      // key不传表示替换整个content下的整个chartConfig
      handleConfigChange: this.$_debounce((value, key) => {
        if (this.chartConfig) {
          if (key) {
            // 如果直接受到chartConfig影响的数据,不需要额外提供方法,图形会再config变更的时候更新
            this.$nextTick(() => {
              switch (key) {
                case USER_PROP.CHART_ALIAS: {
                  this.handleChartTypeSwitch(value, key)
                  // 切换图表类型需处理xy轴最大值、最小值数据
                  const el = this.currentEditData
                  const alias = el.content.alias
                  const needCaculate = [
                    ...HANDLE_MAX_MIN_VALUE_CHART,
                    've-scatter-normal'
                  ].includes(alias)
                  if (
                    needCaculate &&
                    !SPEICAL_DATA_STRUCTURECHART.includes(alias)
                  ) {
                    this.$refs.chartContent.resetLegendSelected({}, true)
                  }
                  // 切换图表类型清空扩展维度
                  if (!EXTEND_DIMENSION_CHART.includes(alias)) {
                    this.clearExtendDimension()
                  }
                  if (!this.isMap) {
                    this.clearLongitudeLatitude()
                    this.clearTooltopField()
                    this.cleardimensionDestination()
                  }
                  // 切换类型，更新图形实例
                  this.$refs.chartContent.resetAfterConfig({
                    source: 'change chart type',
                    isMapInit: true
                  })
                  this.$refs.chartContent.changeChartType()
                  break
                }
                case USER_PROP.CHILD_CHART_ALIAS: {
                  this.onChangeChildChartType(value, key)
                  break
                }
                case 'maxStack': {
                  const stack = handleExtraSettings.call(
                    this,
                    'stack',
                    this.currentEditData,
                    this.chartUserConfig.chartAlias === 've-composite'
                  )
                  this.currentEditData.content.chartSettings.stack = stack
                  break
                }
                case 'legend': {
                  const keys = [key, 'grid']
                  keys.forEach(k => {
                    const val =
                      k === 'legend' ? value : this.chartUserConfig[k]
                    this.$set(
                      this.chartConfig,
                      k,
                      handleChartSingleSetting(val, k)
                    )
                  })
                  break
                }
                // 圆形图条数限制
                case 'limitNum': {
                  this.previewChart({ from: 'limitNum' })
                  break
                }
                case 'toolbox': {
                  this.$set(this.chartConfig, key, value)
                  break
                }
                // 圆形图条数限制
                case 'metricDisplay': {
                  this.previewChart({ from: 'metricDisplay' })
                  break
                }
                // 散点图气泡属性变化
                case 'symbolChange': {
                  const { key, val } = value
                  // 在设置symbolSizeMax时，可以防止下次请求后台后，会重置symbolSizeMax值
                  this.$set(this.chartUserConfig, key, val)

                  if (key === 'symbolSizeMax') {
                    this.$set(this.chartSettings, key, val)
                    this.$refs['chartContent'].resetAfterConfig({
                      isSizeChange: true
                    })
                  } else if (key === 'symbolSizeMetric') {
                    const metricName = val
                    const yMetrics = this.chartUserConfig.metricsContainer.y[0]
                    const requestMetrics = this.drillSettings.layers[0].metrics
                    const lastIndex = requestMetrics.length - 1
                    const lastMetric = requestMetrics[lastIndex]

                    if (yMetrics.labeName !== metricName) {
                      const sizeMetric = createSizeMetric(metricName)
                      lastMetric.replaceable
                        ? requestMetrics.splice(lastIndex, 1, sizeMetric)
                        : requestMetrics.push(sizeMetric)
                      setTimeout(() => {
                        this.previewChart({ from: 'symbolChange' })
                      }, 50)
                    } else {
                      this.$delete(this.chartUserConfig, 'symbolSizeMetric')
                      lastMetric.replaceable && lastMetric.pop()
                    }
                  }
                  break
                }
                case 'title': {
                  this.$set(
                    this.chartConfig,
                    key,
                    handleChartSingleSetting(value, key)
                  )
                  if (this.chartUserConfig.chartType === 've-map') {
                    this.$refs['chartContent'].resetAfterConfig({
                      isMapInit: true
                    })
                  } else if (
                    this.chartUserConfig.chartAlias === 've-grid-normal'
                  ) {
                    this.$refs['chartContent'].resetAfterConfig({
                      isSizeChange: true
                    })
                  }
                  break
                }
                default: {
                  this.$set(
                    this.chartConfig,
                    key,
                    handleChartSingleSetting(value, key)
                  )
                }
              }
            })
          } else {
            this.$set(
              this.currentEditData.content,
              'chartConfig',
              handleTotalChartSetting(value)
            )
          }
        }
      }),
      // MARK:即时预览图表
      previewChart: this.$_debounce(async previewCheck => {
        const from = previewCheck?.from || 'chartSet'
        // 清除图形动画
        this.$refs.chartContent.clearMapAnimation()
        const notNeedResponseChartData =
          typeof previewCheck === 'object' && !!previewCheck.isNeedWarnCheck
        if (this.chartUserConfig.chartAlias === 've-grid-normal') {
          if (notNeedResponseChartData && previewCheck.type === 'warsnline') {
            previewCheck.func(true)
          } else if (
            !previewCheck ||
            typeof previewCheck === 'object' ||
            previewCheck()
          ) {
            this.$set(this.currentEditData, 'content', {
              ...this.currentEditData.content
            })
          }
          const preventRequest =
            (previewCheck && previewCheck.preventRequest) || false
          const firstRender =
            (previewCheck && previewCheck.firstRender) || false
          this.$refs['chartContent'].createTable({
            firstRender,
            from,
            preventRequest
          })
          return
        }
        // this.setScatterSize()
        function inMetrics(metrics, m) {
          for (const metric of metrics) {
            if (metric.alias === m.alias) {
              return true
            }
          }
          return false
        }
        // 目标值
        if (this.isGaugeType || this.isLiquidFill) {
          // 检查目标值
          this.checkGaugeRequest()
        } else {
          // 检查 度量和维度 以及目标值
          this.checkRequest()
        }
        if (!this.preventRequest && this.currentEditData.content) {
          // 图形预警交集不查询图形数据
          if (!notNeedResponseChartData) {
            // 控制loading状态
            this.$refs.chartContent.setElementLoadState(true)
            this.$refs.chartContent.resetLegendSelected({})
            // 清空之前数据
            const defaultRows = Array.isArray(this.chartData.rows)
              ? []
              : Object.create(null)
            this.$set(this.chartData, 'rows', defaultRows)
            if (
              this.currentEditData.content.chartUserConfig.chartAlias ===
              've-scatter-normal'
            ) {
              const metrics = this.currentEditData.content.drillSettings
                .layers[0].metrics
              const newMetrics = []
              for (const metric of metrics) {
                if (!inMetrics(newMetrics, metric)) {
                  newMetrics.push(metric)
                }
              }
              this.currentEditData.content.drillSettings.layers[0].metrics = newMetrics
            }
          }

          // 设计编辑时请求
          const {
            drillSettings,
            chartUserConfig
          } = this.currentEditData.content
          // 度量汇总
          if (this.needMeasureConfig) {
            const rule = [
              {
                rule: chartUserConfig.measureConfig.measureSummaryValue,
                noDim: !!chartUserConfig.measureConfig.ignoreDimension
              }
            ]
            this.$set(drillSettings, 'measureSummary', rule)
          } else {
            this.$delete(drillSettings, 'measureSummary')
          }

          // 处理自定义维度传值
          this.handleCustomDateDimensionParam()

          // 处理日历区域指标传值
          this.handleCalendarFactorsParam()

          // if (chartUserConfig && !this.isMobile) {
          //   const rule = [{ rule: chartUserConfig.measureConfig.measureSummaryValue }]
          //   this.$set(drillSettings, 'measureSummary', rule)
          // } else {
          //   this.$delete(drillSettings, 'measureSummary')
          // }
          if (!drillSettings.designStageRequest) {
            drillSettings.designStageRequest = true
          }
          const scenario3 = (
            chartUserConfig.dimensionWarningList || []
          ).some(d => d.warningList.find(w => w.fieldColumnType === 'date'))
          if (scenario3) {
            this.$set(this.currentEditData, 'needTitleCalendarCacheMap', true)
          }

          // 合并扩展项
          const {
            extendDimensionList = [],
            mergeExtendItems,
            xAuxiliaryLineData = [],
            yAuxiliaryLineData = []
          } = this.chartUserConfig
          this.$set(
            this.drillSettings,
            'mergeExtendItems',
            !!extendDimensionList.length && mergeExtendItems
          )

          warningToAlterLogic.call(
            this,
            this.drillSettings,
            this.chartUserConfig
          )
          // 图形预警交集不查询图形数据
          this.$set(
            this.drillSettings,
            'judgeIntersection',
            notNeedResponseChartData
          )
          this.getCustomMetricConfig()

          const newChartData = await this.getElResponse(
            this.currentEditData,
            notNeedResponseChartData,
            previewCheck
          )
          // 维度扩展颜色设置
          // if (this.chartUserConfig.extendDimensionList && this.chartUserConfig.extendDimensionList.length) {
          //   const { alias, chartResponse } = this.currentEditData.content
          //   // 获取默认颜色
          //   const defaultColors = Color.getDefaultChartColor(this.chartUserConfig, { chartResponse, chartAlias: alias, colorThemeType: this.themeType, vm: this.$refs.chartContent })
          //   const colors = defaultColors.map((item, index) => this.chartUserConfig.colors[index] || item)
          //   this._changeColor(colors)
          // }

          // 请求完成校验预警线是否通过来关闭弹窗
          if (notNeedResponseChartData && previewCheck.type === 'warnline') {
            if (
              !newChartData ||
              (Array.isArray(
                newChartData.chartResponse.alertLogicIntersection
              ) &&
                newChartData.chartResponse.alertLogicIntersection.length)
            ) {
              // 存在交集
              if (newChartData) {
                this.$message({
                  message: this.$t('sdp.views.warnlineOverlap'),
                  type: 'warning'
                })
              }
              previewCheck.cancelLoading()
            } else {
              // 校验成功
              previewCheck.func(true)
              this.previewChart()
            }
          }

          if (!notNeedResponseChartData) {
            this.handSetAnimationStatus({
              source: 'response',
              chartData: newChartData.chartData,
              delay: 2000
            })
          }

          if (
            !previewCheck ||
            typeof previewCheck === 'object' ||
            previewCheck()
          ) {
            this.$set(this.currentEditData, 'content', {
              ...this.currentEditData.content,
              ...newChartData
            })
          }
        }
        if (this.preventRequest) {
          this.preventRequest = false
        }
      }, 100),
      // 当前左边的字段
      fieldList: [],
      allFieldList: [],
      indicatorIndex: 0,
      isFistChangeField: true,
      isShowDeputyAxis: false,
      VE_BANDWIDTH_TYPE,
      selectedDataset: [], // 图形选中数据集
      associatedDataset: '', // 关联后数据集
      templateTerminalType: this.isMobile ? '2' : '1',
      VE_LIQUIDFILL_MODE,
      isShowDataset: false,
      interactionReferencedInfo: { els: [], actions: {}, delIndicators: {} } // 被其他元素引用的交互信息
    }
  },
  computed: {
    onlyIndex() {
      return this.$getSystemConfig?.('onlyIndex') || this.configs.options?.onlyIndex
    },
    isNameDisabled() {
      return this.onlyIndex && this.currentEditData?.analysisType === 2
    },
    chartHasSubType() {
      return chartsList.some(item => {
        if (this.currentEditData.content.alias === item.alias) {
          if (item.category || item.belong || (item.children && item.children)) {
            return true
          }
        }
        return false
      })
    },
    languageList() {
      return this.utils.languageList
    },
    isSelectedDimensionOrMetric() {
      // CR_8319: 实现逻辑待推敲, 考虑是否要配合 `metric.type` 进行处理;
      // $refs 有缺陷： 在paramsSetIsDoing方法中console.log(this.$refs['dimensionDragBox']?.dragList.length)是1，但是isSelectedDimensionOrMetric不会重新计算
      return !!(this.$refs['dimensionDragBox']?.dragList.length || this.$refs['metricdefault']?.[0]?.dragList.length)
    },
    metricTypesListHasDragList() {
      let flag = null
      this.metricTypes.forEach(metric => {
        const metricsBox = this.$refs[`metric${metric.type}`]
        const metricComp = Array.isArray(metricsBox) ? metricsBox[0] : metricsBox
        if (metricComp && metricComp.dragList.length > 0) {
          flag = true
        }
      })
      return flag
    },
    paramsSetIsDone() {
       // 这种写法有问题
      // let flag =  this.metricTypesListHasDragList
      let flag = null
      this.metricTypes.forEach(metric => {
        const metricsBox = this.$refs[`metric${metric.type}`]
        const metricComp = Array.isArray(metricsBox) ? metricsBox[0] : metricsBox
        if (metricComp && metricComp.dragList.length > 0) {
          flag = true
        }
      })
      let _dimensionFlag = null
      if(this.dimensionTypes?.length) {
        this.dimensionTypes.forEach(dimension => {
          const dimensionBox = this.$refs[`dimensionDragBox${dimension.type}`]
          const dimensionComp = Array.isArray(dimensionBox) ? dimensionBox[0] : dimension
          if (dimensionComp && dimensionComp.dragList && dimensionComp.dragList.length > 0) {
            _dimensionFlag = true
          }
        })
      }
      // } else {
        _dimensionFlag = this.$refs['dimensionDragBox']?.dragList.length
      // }
      // return !!this.drillSettings.dataSetId  && !!(this.$refs['dimensionDragBox']?.dragList.length || this.$refs['metricdefault']?.[0]?.dragList.length);
      return !!this.drillSettings.dataSetId && !!(_dimensionFlag || flag)
    },
    paramsSetIsDoing() {
      // 这种写法有问题
      // let flag =  this.metricTypesListHasDragList
      let flag = null
      this.metricTypes.forEach(metric => {
        const metricsBox = this.$refs[`metric${metric.type}`]
        const metricComp = Array.isArray(metricsBox) ? metricsBox[0] : metricsBox
        if (metricComp && metricComp.dragList.length > 0) {
          flag = true
        }
      })
      let _dimensionFlag = null
      // if(this.currentEditData.content.alias === 've-bar-Heatmap'){
        this.dimensionTypes.forEach(dimension => {
          const dimensionBox = this.$refs[`dimensionDragBox${dimension.type}`]
          const dimensionComp = Array.isArray(dimensionBox) ? dimensionBox[0] : dimension
          if (dimensionComp && dimensionComp.dragList && dimensionComp.dragList.length > 0) {
            _dimensionFlag = true
          }
        })
      // } else {
        _dimensionFlag = this.$refs['dimensionDragBox']?.dragList.length
      // }
      return (
        this.visible &&
        !!this.drillSettings.dataSetId &&
        this.kanbanGuideIsVisible &&
        // !this.isSelectedDimensionOrMetric
        !( _dimensionFlag || flag)
      )
    },
    saveIsDoing() {
      return !!this.drillSettings.dataSetId &&
      // this.configs && this.configs.type === 'template' &&
        !this.saveIsDone &&
        this.kanbanGuideIsVisible &&
        this.$refs.CommonDataset &&
        !!this.$refs.CommonDataset.datasetIsDone &&
        !!this.paramsSetIsDone
    },
    // 指引步骤的状态
    kanbanGuideIsVisible() {
      //  let flag = this.utils.kanbanGuide.chartList.length === this.utils.kanbanGuide.invalidChartList.length
      //  console.log('%c [ flag ]-1482', 'font-size:13px; background:#d08976; color:#ffcdba;', flag)
      return this.initializedByKanbanFilterAllMixin && this.utils.kanbanGuide?.isVisible
    },
    datasetArray() {
      if (this.isAssociationDataset) {
        return [this.associatedDataset]
      } else if (this.configs && this.configs.type === 'template') {
        return this.dataSetRes || []
      } else {
        return this.datasetList || []
      }
    },
    platformDataList() {
      // 非企业数据集 平台数据集
      return this.datasetList.filter(item => {
        return !this.$_getProp(item, 'dataSourceId', '')
      })
    },
    datasetType() {
      return this.isShowDataset
        ? this.chartUserConfig.datasetAssociation
          ? 'AssociateDataset'
          : 'CommonDataset'
        : ''
    },
    // 主题类型
    themeType() {
      return this.themeData.enableTheme || this.themeData.screenMode
        ? this.themeData.themeType
        : 'sdp-classic-white'
    },
    symbolSizeMax: {
      get() {
        const mobileSymbolSizeMax = [20, 40]
        return this.isMobile &&
          !mobileSymbolSizeMax.includes(this.chartUserConfig.symbolSizeMax)
          ? 20
          : this.chartUserConfig.symbolSizeMax
      },
      set(val) {
        this.$set(this.chartUserConfig, 'symbolSizeMax', val)
      }
    },
    alias() {
      return this.$_getProp(this.currentEditData, 'content.alias')
    },
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 请求接口数据存放处
    drillSettings() {
      return this.currentEditData.content
        ? this.currentEditData.content.drillSettings
        : {}
    },
    chartData() {
      return this.currentEditData.content
        ? this.currentEditData.content.chartData
        : {}
    },
    chartSettings() {
      return this.currentEditData.content
        ? this.currentEditData.content.chartSettings
        : {}
    },
    // 直接应用到图形上的参数
    chartConfig: {
      get() {
        return this.currentEditData.content
          ? this.currentEditData.content.chartConfig
          : {}
      },
      set(val) {
        this.currentEditData.content &&
          (this.currentEditData.content.chartConfig = val)
      }
    },
    // 用户配置的图形属性 在boardElType中定义
    chartUserConfig() {
      return this.currentEditData.content?.chartUserConfig || {}
    },
    metricList() {
      return (
        this.currentEditData.content?.chartUserConfig?.metricsContainer
          ?.default || []
      )
    },
    // 特殊图形,需要切换成两列维度
    specialAlias() {
      try {
        const alias = this.currentEditData.content.alias
        if (['ve-composite', 've-scatter-normal','ve-bar-Heatmap'].includes(alias)) {
          return alias
        } else if (
          SPECIAL_SUPPORT_DEPUTY_AXIS.includes(alias) &&
          this.isShowDeputyAxis
        ) {
          return alias
        }
      } catch (e) {
        void 0
      }
      return false
    },
    filteringDatasetList() {
      const {
        metricsContainer
      } = this.chartUserConfig
      if (this.onlyIndex) {
        return this.datasetArray.filter(item => {
          return (metricsContainer?.default || []).some(metric => metric.indexId === item.id)
        })
      }
      return this.datasetArray.length > 1
        ? this.datasetArray.filter(
            item => item.id === this.drillSettings.dataSetId
          )
        : this.datasetArray
    },
    api() {
      return this.utils.api || function() {}
    },
    doubleMetric() {
      const alias = this.currentEditData?.content?.alias
      // return alias === 've-scatter-normal' || alias === 've-composite'
      return this.specialAlias && this.specialAlias.includes(alias)
    },
    // 移动端需要分页展示的图形
    isMobilePageImg() {
      return (
        this.isMobile &&
        MOBILE_PAGE_CHART.includes(this.currentEditData.content.alias)
      )
    },
    isMobile() {
      return !!this.utils.isMobile
    },
    // 度量汇总
    needMeasureConfig() {
      return isSetMeasureConfig.call(this, this.currentEditData.content)
    },
    componentShow() {
      const chartAlias = this.chartUserConfig.chartAlias
      return {
        targetDragbox:
          chartAlias === 've-gauge-normal'
            ? this.isGauge
            : HAS_TARGET_CHART.includes(chartAlias) // 是否显示目标值
      }
    },
    // 是否出现维度扩展按钮
    isShowExtendDimension() {
      const hasStackAcc = this.metricList.some(v =>
        STACK_ACC_TYPES.STACK_ACC_MAP.has(v.addup)
      )
      const hiddenExtendDimension = this.$getFeatureConfig?.('extendDimension.hidden')
      return (
        EXTEND_DIMENSION_CHART.includes(this.chartUserConfig.chartAlias) &&
        !this.extendDragBoxShow &&
        !hasStackAcc &&
        !hiddenExtendDimension
      )
    },
    isShowAddDeputyAxis() {
      return (
        SPECIAL_SUPPORT_DEPUTY_AXIS.includes(this.chartUserConfig.chartAlias) &&
        this.metricTypes.length === 1
      )
    },
    isShowDeleteDeputyAxis() {
      return (
        SPECIAL_SUPPORT_DEPUTY_AXIS.includes(this.chartUserConfig.chartAlias) &&
        this.metricTypes.length === 2
      )
    },
    isDoubleAxis() {
      return (
        this.chartUserConfig.chartAlias === 've-composite' ||
        this.isShowDeleteDeputyAxis
      )
    },
    isMap() {
      return this.chartUserConfig.chartType === 've-map'
    },
    isBandwidth() {
      return this.chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH
    },
    isHotMap() {
      return this.chartUserConfig.mapType === 'HEATMAP'
    },
    isShowMapDestination() {
      return this.isRealScreen && this.isMap && this.chartUserConfig.mapSetting.flyLine.enable
    },
    // 扩展维度数据
    extendDimension() {
      return this.chartUserConfig.extendDimension
    },
    extendDragBoxShow: {
      get() {
        return !!this.chartUserConfig.extendDragBoxShow
      },
      set(val) {
        this.$set(this.chartUserConfig, 'extendDragBoxShow', val)
      }
    },
    // 仪表盘
    isGauge() {
      const { chartSettings } = this.currentEditData.content || {}
      const metrics = chartSettings.metrics || []
      const dimension = this.chartUserConfig.dimensionList || []
      const isGauge = this.chartUserConfig.chartType === 've-gauge'
      // 仪表盘是否可以发起请求
      return isGauge && !dimension.length && metrics.length === 1
    },
    // 仪表盘属性
    gaugeTarget() {
      return this.chartUserConfig.gaugeTarget
    },
    // 仪表盘
    isGaugeType() {
      return this.chartUserConfig.chartAlias === 've-gauge-normal'
    },
    // 水球图
    isLiquidFill() {
      return this.chartUserConfig.chartAlias === 've-liquidfill'
    },
    // 堆叠条形图
    isBarStack() {
      return this.chartUserConfig.chartAlias === 've-bar-stack'
    },
    // 热力图
    isBarHeatmap() {
      return this.chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BAR_HEATMAP
    },
    isBoundToIntersectionLocation() {
      let paramsPanelList = this.boardData.paramsPanelList

      switch (paramsPanelList.length) {
        case 0:
          return false
        case 1:
          paramsPanelList = paramsPanelList[0].content
          break
        default:
          paramsPanelList = paramsPanelList.reduce((list, item) => {
            list.push(...item.content)
            return list
          }, [])
      }

      return paramsPanelList.some(item => {
        const { type, content = {} } = item
        const { bindElements = [], paramFilterLableType } =
          content.options || {}

        return (
          type === TYPE_PARAM_ELEMENT.LOCATION_NEW &&
          bindElements.includes(this.elId) &&
          paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT
        )
      })
    },
    showContrastDragBox() {
      const {
        chartAlias,
        waterfallSetting = {},
        dimensionList
      } = this.chartUserConfig
      return (
        chartAlias === 've-waterfall' &&
        dimensionList.length &&
        waterfallSetting?.contrast?.show
      )
    },
    bandwidthMode: {
      get() {
        if (!this.chartUserConfig.bandwidthMode) {
          this.$set(
            this.chartUserConfig,
            'bandwidthMode',
            VE_BANDWIDTH_TYPE.CUSTOM_BANDWIDTH
          )
        }
        return this.chartUserConfig.bandwidthMode
      },
      set(val) {
        this.$set(this.chartUserConfig, 'bandwidthMode', val)
      }
    },
    mapMode: {
      get() {
        return this.chartUserConfig.mapMode || 'administrativeRegional'
      },
      set(val) {
        this.$set(this.chartUserConfig, 'mapMode', val)
      }
    },
    dimensionDateFormat() {
      const arr = ['yyyy-MM', 'yyyy']
      return (
        Array.isArray(this.chartUserConfig.dimensionList) &&
        this.chartUserConfig.dimensionList.length &&
        arr.includes(this.chartUserConfig.dimensionList[0].dateFormat)
      )
    },
    isShowTimeDimension() {
      return (
        NEED_TIMEDIMENSION_CHART.includes(this.chartUserConfig.chartAlias) &&
        this.chartUserConfig.dimensionList.length !== 2 &&
        !this.dimensionDateFormat
      )
    },
    formatChartData() {
      return this.utils.isScreen &&
        [...ANIMATION_HISTOGRAM_CHART, ...ANIMATION_TOOLTIP_CHART].includes(
          this.chartUserConfig.chartAlias
        )
        ? this.$refs?.chartContent?.staticChartData
        : this.chartData
    },
    isMoreAuxiliaryLine() {
      const metricsContainer = this.chartUserConfig.metricsContainer
      const alias = this.currentEditData.content?.alias
      return (
        ['ve-composite', 've-scatter-normal'].includes(alias) ||
        (SPECIAL_SUPPORT_DEPUTY_AXIS.includes(alias) &&
          metricsContainer.hasOwnProperty('line'))
      )
    },
    // 是否使用关联数据集
    isAssociationDataset() {
      return (
        this.chartUserConfig.datasetAssociation &&
        this.associatedDataset &&
        !!Object.keys(this.associatedDataset).length
      )
    },
    // 水球图模式
    liquidFillMode: {
      get() {
        if (!this.chartUserConfig.liquidFillSetting) return ''
        if (!this.chartUserConfig.liquidFillSetting.mode) {
          this.$set(
            this.chartUserConfig.liquidFillSetting,
            'mode',
            VE_LIQUIDFILL_MODE.DIMENSION
          )
        }
        const mode = this.chartUserConfig.liquidFillSetting.mode
        if (
          mode === VE_LIQUIDFILL_MODE.NORMAL &&
          this.chartUserConfig.dimensionList.length
        ) {
          this.handleLiquidFillMode(mode)
        }
        return mode
      },
      set(val) {
        this.$set(this.chartUserConfig.liquidFillSetting, 'mode', val)
      }
    },
    showDimensionDragbar() {
      if (
        (this.isLiquidFill &&
        this.liquidFillMode === VE_LIQUIDFILL_MODE.NORMAL) ||
        this.isBarHeatmap || this.isGaugeType
      ) { return false }
      return true
    },
    isRealScreen() {
      return isRealScreenFn(this.utils)
    }
  },

  watch: {
    paramsSetIsDoing: {
      handler: function(value) {
        // this.$nextTick(() => {
        //   this.isShowParamsSetTips = value;
        // });
        setTimeout(() => {
          this.isShowParamsSetTips = value
        }, 500)
      }
    },
    paramsSetIsDone: {
      handler: function(value) {
        value && (this.isShowParamsSetTips = false)
      }
    },
    saveIsDoing: {
      handler: function(value) {
        // 表示弹窗是打开的
        value && this.value && (this.isShowSaveTips = value)
      }
    },
    // 监控图形设置看板的打开，需要刷新数据
    value: {
      handler(val) {
        val &&
          setTimeout(() => {
            this.$refs.chartContent &&
              this.$refs.chartContent.mapSchemeSwitch(
                0,
                this.currentEditData.content
              )
            this.initChartSettings()
            this.previewChart({
              from: 'chartSet-valueWatch',
              preventRequest: true
            })
            this.templateTerminalType = this.isMobile ? '2' : '1'
          }, 50)
        val && setTimeout(() => {
          this.initializedByKanbanFilterAllMixin = true

          let _dimensionFlag = null
          // if(this.currentEditData.content.alias === 've-bar-Heatmap'){
            this.dimensionTypes.forEach(dimension => {
              const dimensionBox = this.$refs[`dimensionDragBox${dimension.type}`]
              const dimensionComp = Array.isArray(dimensionBox) ? dimensionBox[0] : dimension
              if (dimensionComp?.dragList?.length > 0) {
                _dimensionFlag = true
              }
            })
          // } else {
            _dimensionFlag = this.$refs['dimensionDragBox'].dragList
          // }
            // this.$refs['dimensionDragBox']?.dragList.length || this.$refs['metricdefault']?.[0]?.dragList.length
            this.saveIsDone = this.$refs.CommonDataset && this.$refs.CommonDataset.datasetIsDone && (!!_dimensionFlag)
        }, 300)
        if (!val) {
          this.isShowDataset = false
          this.associatedDataset = ''
          this.saveIsDone = false
          this.currentEditData = {}
        }
      },
      immediate: true
    },
    // 变更为多维度列图形的时候
    // 储存维度的对象的数据
    // 并变更拖拽拦的数据
    specialAlias(val) {
      // 如果是因为图表类型切换触发的watch，就不需要处理metricsContainer
      this.initSpecialAliasMetrics(
        !['ve-composite', 've-scatter-normal', 've-bar-Heatmap'].includes(val)
      )
    },
    metricsContainer: {
      handler(val) {
        if (val.y) {
          this.sizeMetric = val.y[0] || {}
        }
        this.chartUserConfig.metricsContainer = this.$_JSONClone(val)
      },
      deep: true
    },
    'drillSettings.dataSetId': function name(newVal, oldVal) {
      if (!oldVal) {
        // 第一次进入时切换数据集不需要提示
        if (!this.currentEditData.content.drillSettings.dataSetId) { this.isFirstDataSet = true }
        this.olddataSetId = newVal
      } else {
        this.olddataSetId = oldVal
      }
    },
    alias(val, oldVal) {
      let isRunPreview = false // 图形渲染标识 只渲染一次
      if (['ve-grid-normal'].includes(val)) {
        return
      }
      const hasDateDimension = this.chartUserConfig.dimensionList?.some(
        item => item.timeDimensionSplittingOffset
      )
      const fromButterfly = oldVal === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY
      // 切换水滴图时初始化目标值
      if (HAS_TARGET_CHART.includes(val) || HAS_TARGET_CHART.includes(oldVal)) {
        this.initTargetBox()
        this.previewChart({ from: 'switch chart type' })
        isRunPreview = true
      } else if (
        SPEICAL_DATA_STRUCTURECHART.includes(val) ||
        SPEICAL_DATA_STRUCTURECHART.includes(oldVal) ||
        hasDateDimension ||
        fromButterfly
      ) {
        // 切换至特殊数据结构图形时 || 设置了自定义日期维度，需要重新请求后台数据
        this.previewChart({ from: 'switch chart type' })
        isRunPreview = true
      }
      // 切换图形时初始化漏斗图数据
      if (val === 've-funnel') {
        this.callMetricBoxFunction('setCascaderDefaultData')
      }
      this.changeAuxiliary(this.chartUserConfig.chartAlias)
      // const chartContent = this.$refs.chartContent
      // chartContent.handleLegendSelected()
      // oldVal && this.clearSaveLengendSelect()
      this.doRegisterMap(
        this.chartUserConfig.mapRangeVal === 'provice'
          ? this.chartUserConfig.mapProviceVal || 'beijing'
          : {}
      )
      if (STACK_ACC_CHART.includes(val) && !STACK_ACC_CHART.includes(oldVal)) {
        isRunPreview || this.previewChart({ from: 'switch chart type' })
      }
    },
    showContrastDragBox(val) {
      const { chartAlias, contrastList = [] } = this.chartUserConfig
      if (chartAlias !== 've-waterfall') return
      // 对比值开关关闭时，需要请求数据
      if (!val) {
        // 如果纬度和度量没有就不请求
        if (!this.chartSettings.dimension?.lenth && !this.chartSettings.metrics?.lenth) return
        this.previewChart({ from: 'showContrastDragBox' })
      } else if (val && contrastList.length) {
        this.$nextTick(() => {
          this.setDragList('contrastValueDragBox', contrastList)
        })
      }
    }
    // 好像可以删掉,先注释一下
    // chartUserConfig(newVal) {
    //   newVal && this.handleConfigChange(newVal)
    // }
  },

  methods: {
    checkOfflineMetrics(responseParam) {
      const indexInfoList = responseParam?.indexInfoList || responseParam?.data?.indexInfoList
      if (!indexInfoList || !Array.isArray(indexInfoList)) {
        return
      }
      
      const offlineMetrics = []
      const noPermissionMetrics = []
      
      indexInfoList.forEach(indexInfo => {
        const metricName = indexInfo.indexName || indexInfo.name || indexInfo.labeName || indexInfo.indexId || '未知指标'
        
        // 检查指标是否下线 (releaseFlag === 2 表示下线)
        if (indexInfo.releaseFlag === 2) {
          offlineMetrics.push(metricName)
        }
        // 检查指标权限 (priv 为布尔值，false表示无权限)
        else if (indexInfo.hasOwnProperty('priv') && indexInfo.priv === false) {
          noPermissionMetrics.push(metricName)
        }
      })
      
      // 构建提示消息
      const messages = []
      
      // 下线提示优先级高于权限
      if (offlineMetrics.length > 0) {
        const indicatorNames = offlineMetrics.join('、')
        messages.push(this.$t('sdp.views.indicatorOfflineMessage', { indicatorNames }))
      }
      
      if (noPermissionMetrics.length > 0) {
        const indicatorNames = noPermissionMetrics.join('、')
        messages.push(this.$t('sdp.views.indicatorNoPermissionMessage', { indicatorNames }))
      }
      
      // 显示提示消息
      if (messages.length > 0) {
        const message = messages.join('\n')
        
        if (this.$message) {
          this.$message({
            message,
            type: 'warning',
            duration: 5000,
            showClose: true,
            dangerouslyUseHTMLString: false
          })
        } else {
          console.warn(message)
        }
      }
    },
    resetDataSetWidth() {
      this.$refs?.[this.datasetType].resetWidth?.()
    },
    getStepClassName(step) {
      if (step.isDone) {
        return 'is-done'
      } else if (step.isDoing) {
        return 'is-doing'
      } else {
        return 'is-todo'
      }
    },
    // 清除全部的操作指引弹窗
    clearAllSelect() {
      this.visible && (this.$refs.CommonDataset.isShowDatasetTips = false)
      this.isShowParamsSetTips = false
      // 配置功能和样式的跟保存合一起
      // this.visible && (this.$refs.tableInfo.isShowChartSetTips = false);
      this.isShowSaveTips = false
    },
    showTheStepTips(type) {
      this.resetDataSetWidth()
      let typeList = {
        addChartData: () => {
          const refName = this.datasetType
          const refComponent = Array.isArray(this.$refs[refName])
            ? this.$refs[refName][0]
            : this.$refs[refName]
          //  打开侧边栏的数据栏
          refComponent.isDisplayDataSet = true
          this.$refs.CommonDataset.isShowDatasetTips = !this.$refs.CommonDataset
            .isShowDatasetTips
        },
        dragParams: () => {
          this.isShowParamsSetTips = !this.isShowParamsSetTips
        },
        // 配置功能和样式的跟保存合一起
        // setChartStyle: () => {
        //   this.$refs.tableInfo.isShowChartSetTips = !this.$refs.tableInfo
        //     .isShowChartSetTips;
        // },
        saveChart: () => {
          this.isShowSaveTips = !this.isShowSaveTips
        }
      }
      this.clearAllSelect()
      typeList[type]()
    },
    closeGuidePanel() {
      // "TypeError: Cannot set property isVisible of #<KanbanGuide> which has only a getter"
      // this.utils.kanbanGuide.isVisible = false;
      this.utils.kanbanGuide.closeVisible()
    },
    // 点击关闭所有弹窗
    closeDurativesWhenClick(ev) {
      const kanbanGuidePanelDOM = this.$refs['kanbanGuidePanelRef']
      kanbanGuidePanelDOM &&
        !kanbanGuidePanelDOM.contains(ev.targe) &&
        this.clearAllSelect()
    },
    handler_ColorSetting(prop, value) {
      if (
        [
          USER_PROP.DIMENSION_COLOR,
          USER_PROP.COLOR,
          USER_PROP.COLOR_SCHEME,
          USER_PROP.COLOR_OPACITY
        ].includes(prop)
      ) {
        this.$refs.tableInfo.handler_emptyMethod(prop, value)
      } else if ([USER_PROP.COLOR_SERIES_PROP].includes(prop)) {
        if (this.chartUserConfig.chartAlias !== 've-wordcloud') return false
        if (!value) {
          this.currentEditData.content.chartSettings.extendDimension = []
        } else {
          this.currentEditData.content.chartSettings.extendDimension = [value]
        }

        this.previewChart()
      } else if ([USER_PROP.COLOR_TYPE].includes(prop)) {
        this.$refs.tableInfo.change_colorSetting(prop, value)
      }
    },
    getCustomMetricConfig() {
      const customMetric = []
      const {
        metricsContainer = {},
        metricLabelDisplay = [],
        chartAlias
      } = this.chartUserConfig

      const allMetric =
        (CUSTOM_METRIC.includes(chartAlias) && metricsContainer.default) || []
      allMetric.forEach(m => {
        const currentMetricLabel = metricLabelDisplay.find(
          l => l.keyName === m.keyName
        )
        if (currentMetricLabel && currentMetricLabel.labelType === 'custom') {
          if (currentMetricLabel.customMetricList?.length) {
            currentMetricLabel.customMetricList.forEach(item => {
              const ret = this.getMetricItem(item.customMetric, item)
              customMetric.push(ret)
            })
          }
        }
      })
      this.drillSettings.layers[0].customMetric = customMetric
    },
    checkDimensionPut(a, b, c) {
      const dragCascaderRef = c?.querySelector('.dragCascader-container')
      if (dragCascaderRef) {
        return !dragCascaderRef.__vue__.item.isGridCustom
      }
      return true
    },
    handleDialogOpened() {
      changeThemeType({
        theme: this.themeType, dom: this.$el, projectName: this.utils.env?.projectName
      })
    },
    templateTerminalTypeChange(val) {
      this.$sdp_eng_confirm(
        `${this.$t('sdp.views.confirmChangeTemplateType')}`,
        `${this.$t('sdp.message.tipsConfirmTitle')}`,
        {
          confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
          cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
          type: 'warning'
        }
      )
        .then(() => {
          this.sdpBus.$emit(EVENT_TYPE.CHANNEL, {
            channel: val,
            resetElement: true
          })
        })
        .catch(() => {
          this.templateTerminalType = `${3 -
            Number(this.templateTerminalType)}`
        })
    },
    // 打开引用数据集弹窗
    async openLinkDatasetDialog() {
      if (!this.onlyIndex) {
        await getDataWarehouseModelFlag(this.api).then(
          res => {
            // 0 数据建模  1 数仓建模
            this.dataWarehouseModelingSwitch = res
          },
          () => {
            this.dataWarehouseModelingSwitch = '0'
          }
        )
      }
      this.$nextTick(() => {
        this.$set(this, 'datasetDialogVisible', true)
        this.componentId = ReferenceDataSet
      })
    },
    // 关闭弹窗
    closeDia() {
      this.$nextTick(() => {
        this.$set(this, 'datasetDialogVisible', false)
        this.componentId = ''
      })
    },
    // 开启关联数据集开关，删除真实数据集
    removeDatasetList(removeObjId) {
      if (removeObjId === this.drillSettings.dataSetId) {
        this.drillSettings.dataSetId = ''
        this.emptyChartDatasetConfig()
      }
    },
    // 删除结果数据集
    deleteResultDataset() {
      this.associatedDataset = ''
      this.drillSettings.dataSetId = ''
      this.$set(this.currentEditData.content, 'associatedData', {})
      // 数据集变更，需要清空维度、度量相关数据
      this.emptyChartDatasetConfig()
      this.$refs.AssociateDataset &&
        this.$refs.AssociateDataset.datasetClick({
          dateType: 'associateDataset'
        })
    },
    // 设置结果数据集
    setResultDataset() {
      const {
        dataSetJoinsColumns,
        associateDatasetName,
        mainDatasetId,
        associatedDataset = {}
      } = this.currentEditData.content.associatedData
      let datset = {
        dateType: 'associateDataset',
        labeName: associateDatasetName,
        children: dataSetJoinsColumns,
        id: associatedDataset.id || this.$_generateUUID()
      }
      this.$set(this.drillSettings, 'dataSetId', mainDatasetId)

      // 判断已存在字段内容是否变更
      let isChange =
        !this.associatedDataset?.children ||
        this.associatedDataset.children.some((field, index) => {
          if (!dataSetJoinsColumns[index]) return true
          const { dataSetId, labeName, alias, aggType } = field
          const {
            dataSetId: newDataSetId,
            labeName: newLabeName,
            alias: newAlias,
            aggType: newAggType
          } = dataSetJoinsColumns[index]
          return (
            newLabeName !== labeName ||
            newDataSetId !== dataSetId ||
            newAlias !== alias ||
            newAggType !== aggType
          )
        })

      this.$set(
        this.currentEditData.content.associatedData,
        'associatedDataset',
        datset
      )
      this.associatedDataset = datset
      // 数据集字段变更，需要清空维度、度量相关数据
      if (isChange && this.boardInfo.clearChartField !== false) {
        // 系统参数控制是否清空 true: 清空 false: 不清空
        this.emptyChartDatasetConfig()
      } else {
        // 需要处理指标选择器
        const chioceTab = this.currentEditData.content.chioceTab || []
        if (chioceTab.length > 1) {
          chioceTab.forEach(item => {
            this.$set(
              item.saveObj,
              'associatedData',
              this.$_deepClone(this.currentEditData.content.associatedData)
            )
          })
        }
        this.getSelectionsList(mainDatasetId)
        this.previewChart({ from: 'set result dataset' })
      }
      // 修改关联数据集后，需要默认展开数据集
      this.$refs.AssociateDataset &&
        this.$refs.AssociateDataset.datasetClick(this.associatedDataset)
    },
    // 打开/关闭关联数据集开关
    changeAssocciationSwitch(switchStatus) {
      if (switchStatus) {
        if (this.drillSettings.dataSetId) {
          this.selectedDataset = this.datasetList.filter(
            item => item.id === this.drillSettings.dataSetId
          )
        }
        const chioceTab = this.currentEditData.content.chioceTab || []
        if (chioceTab.length) {
          // 记录被删除的指标，为清除卡片的交互配置
          if (this.interactionReferencedInfo.els?.length) {
            const actions = this.interactionReferencedInfo.actions
            chioceTab.forEach(tab => {
              const keys = Object.keys(actions).filter(key => (actions[key] || []).find(a => a?.actionType === 'indicator' && a?.indicators.includes(tab.id)))
              if (keys.length) {
                // 指标ID: [元素ID]
                this.$set(this.interactionReferencedInfo.delIndicators, tab.id, keys)
              }
            })
          }
          chioceTab.forEach(item => {
            this.$refs.tableInfo.emptySomeProperty(item.saveObj)
          })
          this.currentEditData.content.chioceTab = []
          this.currentEditData.content.saveIndex = this.indicatorIndex = 0
        }
      } else {
        this.deleteResultDataset()
      }
    },
    updateDaSetIds(Ids = []) {
      if (this.onlyIndex) {
        if (!Ids.length) return
        this.drillSettings.indexFlagIds = [...new Set([...Ids, ...this.drillSettings.indexFlagIds])]
        this.drillSettings.dataSetId || (this.drillSettings.dataSetId = Ids[0] || '')
        this.getSelectionsList( this.drillSettings.dataSetId, false)
        return
      } else {
        const datasetId = Ids[0] || ''
        if (!datasetId || datasetId === this.drillSettings.dataSetId) return
        this.drillSettings.dataSetId = datasetId
      }
      this.relChangeSelections()
    },
    // 度量调用
    onBandwidthChange(params, type) {
      function getKey() {
        switch (type) {
          case 'bandwidth':
            return 'bandwidthList'
          case 'minimumBandwidth':
            return 'minBandwidthList'
          case 'maximumBandwidth':
            return 'maxBandwidthList'
        }
      }
      this.$set(
        this.chartUserConfig.bandwidthData,
        getKey(),
        Array.isArray(params) ? params : params.dragList
      )

      this.previewChart({ from: 'onBandwidthChange' })
    },
    // 清除带宽数据
    clearBandwidthData(bool) {
      this.$set(
        this.chartUserConfig,
        'bandwidthMode',
        VE_BANDWIDTH_TYPE.CUSTOM_BANDWIDTH
      )
      this.$set(
        this.chartUserConfig,
        'bandwidthData',
        getBandwidthInitData(this.themeType)
      )
      if (bool) {
        this.setDragList('maxBandwidthDragBox', [])
        this.setDragList('minBandwidthDragBox', [])
        this.setDragList('bandwidthDragBox', [])
      }
    },
    // 切换带宽类型
    handleBandwidthMode(param = this.chartUserConfig.bandwidthMode) {
      this.bandwidthMode = param
      this.$nextTick(() => {
        if (!this.chartUserConfig.bandwidthData) {
          this.$set(
            this.chartUserConfig,
            'bandwidthData',
            getBandwidthInitData(this.themeType)
          )
        }
        const {
          maxBandwidthList = [],
          minBandwidthList = [],
          bandwidthList = []
        } = this.chartUserConfig.bandwidthData
        if (param === VE_BANDWIDTH_TYPE.CUSTOM_BANDWIDTH) {
          this.setDragList('maxBandwidthDragBox', maxBandwidthList)
          this.setDragList('minBandwidthDragBox', minBandwidthList)
        } else {
          this.setDragList('bandwidthDragBox', bandwidthList)
        }
        this.previewChart({ from: 'switch bandwidth mode' })
      })
    },
    setUpdateChart() {
      this.$refs.chartContent.setUpdateChart()
    },
    handleMapMode(param) {
      if (this.isHotMap) return
      this.mapMode = param
      if (param === 'longitudeAndLatitude') {
        const { longitudeList, latitudeList } = this.chartUserConfig
        if (!longitudeList) {
          this.$set(this.chartUserConfig, 'longitudeList', [])
          this.$set(this.chartUserConfig, 'latitudeList', [])
        } else {
          this.$nextTick(() => {
            this.setDragList('longitudeDragBox', longitudeList)
            this.setDragList('latitudeDragBox', latitudeList)
            this.previewChart()
          })
        }
      } else {
        this.$refs.tableInfo.handler_emptyMethod('mapMode', param)
      }
    },
    clearSaveLengendSelect() {
      this.clearLengendSelectSettings()
      this.$set(this.chartUserConfig, 'showSaveLegendSelected', false)
      // if (!LEGEND_SETTING_SAVE_CHART.includes(alias)) {
      //   this.$set(this.chartUserConfig, 'saveLegendSelected', undefined)
      // }
    },
    clearLengendSelectSettings() {
      this.$set(this.chartUserConfig, 'saveLegendSelected', undefined)
    },
    // 修改别名或度量长度变化时，需要更新图形度量标签显示列表
    updateMetricLabel() {
      const { metricLabelDisplay = [] } = this.chartUserConfig

      const metrics = this.chartUserConfig.metricsContainer.default || []

      let selectMetricList = []
      metrics.forEach((item, index) => {
        const curMetricLabel = metricLabelDisplay.find(
          metric => metric.keyName === item.keyName
        )
        let metricLabelItem = this.$_JSONClone(
          curMetricLabel ||
            generateMetricLabelDisplayItem({
              chartUserConfig: this.chartUserConfig
            })
        )

        !metricLabelItem.keyName && (metricLabelItem.keyName = item.keyName)
        metricLabelItem.origin.showMetricPercent =
          metricLabelItem.origin.showMetricPercent || false
        selectMetricList.push(metricLabelItem)
      })
      this.$set(this.chartUserConfig, 'metricLabelDisplay', selectMetricList)
    },
    // 切换图形类型适配辅助线
    changeAuxiliary(alias) {
      const {
        xAuxiliaryLineData = [],
        yAuxiliaryLineData = []
      } = this.chartUserConfig
      if (
        CIRCULAR_CHART.includes(alias) ||
        ![...xAuxiliaryLineData, ...yAuxiliaryLineData].length
      ) { return }
      setTimeout(() => {
        this.previewChart({ from: 'changeAuxiliary' })
      }, 300)
    },
    initColorSetting() {
      this.$refs['colorSetting'] && this.$refs['colorSetting'].init()
    },
    // 更新图形颜色列表
    initColorsConfig(alias) {
      this.$refs['colorSetting'] &&
        this.$refs['colorSetting'].initColors(alias)
    },
    // 获取图形高度样式
    getChartHeightStyle() {
      let className = 'other-chart-height'
      if (this.doubleMetric) {
        className = 'scatter-height'
      }
      if (this.extendDragBoxShow || this.showContrastDragBox) {
        className = this.isDoubleAxis
          ? 'composite-chart-height'
          : 'scatter-height'
      }
      // 对带宽和地图(有经度纬度)
      if (
        this.isBandwidth ||
        (this.isMap &&
          this.mapMode === 'longitudeAndLatitude' &&
          !this.isHotMap)
      ) {
        className = 'scatter-height'
      }
      if (
        this.isLiquidFill ||
        ['ve-bar-percent', 've-ring-multiple'].includes(
          this.chartUserConfig.chartAlias
        ) ||
        this.isGauge
      ) {
        className = 'liquidFill-height'
        if (
          (this.isLiquidFill &&
          this.liquidFillMode === VE_LIQUIDFILL_MODE.NORMAL) || this.isGauge
        ) {
          className = 'other-chart-height'
        }
      }
      if (this.isShowMapDestination) {
        if (this.mapMode !== 'longitudeAndLatitude') {
          className = 'scatter-height'
        } else {
          className = 'map-flyline-lonlat-height'
        }
      }
      return className
    },
    eventBus,
    clickDimensionExtend() {
      this.extendDragBoxShow = true
      this.$set(this.chartUserConfig, 'extendDimensionList', [])
    },
    // 清除扩展维度
    clearExtendDimension() {
      this.extendDragBoxShow = false
      // this.$set(this.chartUserConfig, 'extendDimensionList', [])
      // this.$set(this.chartUserConfig, 'extendDimensionCascaderValue', [])
      this.setDragList('extendDimensionDragBox', [])
      this.setDragList('contrastValueDragBox', [])
    },
    moveExtendDimension() {
      this.$set(this.chartUserConfig, 'extendDimensionList', [])
      this.$set(this.chartUserConfig, 'extendDimensionCascaderValue', [])
      this.clearExtendDimension()
      if (this.drillSettings) {
        this.previewChart({ from: 'move extend dimension' })
      }
    },
    padDate(val) {
      val = val < 10 ? '0' + val : val
      return val
    },
    dataFilteringCallback(val) {
      setTimeout(() => {
        this.previewChart({ from: 'dataFilteringCallback' })
      }, 50)
    },
    // 日期格式化
    formatDate(val) {
      const value = new Date(val)
      const year = value.getFullYear()
      const month = this.padDate(value.getMonth() + 1)
      const day = this.padDate(value.getDate())
      const hour = this.padDate(value.getHours())
      const minutes = this.padDate(value.getMinutes())
      const milliseconds = this.padDate(value.getMilliseconds())
      return (
        year +
        '-' +
        month +
        '-' +
        day +
        ' ' +
        hour +
        ':' +
        minutes +
        ':' +
        milliseconds
      )
    },
    // 图表对比环形功能
    // onChangeGraphical(params){
    //   const  formatDate =  this.formatDate(new Date())
    //    const calendarComponent = {
    //      bindElements:[this.currentEditData.id],
    //      calendarParseParam:{
    //        quickType:params.ConsttastParam.quickType,
    //        quickClauses: params.ConsttastParam.quickClauses,
    //        dateNormal: formatDate,
    //        dataSets:[
    //          {
    //            dataSetId: params.dataSetParam.id,
    //            columnName: params.dataSetParam.label
    //          }
    //        ],
    //        calendarCompType:"2",
    //        dayOrPeriod:params.ConsttastParam.dayOrPeriod
    //      }
    //    }
    //    const calendarGroupComp = {
    //      columnName:params.ConsttastParam.prefixName,
    //      columnTpe: 'string'
    //    }
    //
    //    this.$set(this.currentEditData,'calendarComponent',calendarComponent)
    //    this.$set(this.currentEditData,'calendarGroupComp',calendarGroupComp)
    // },
    // 多维度
    updateDimensionTypes() {
      let _dimensionTypes = [
        {
          type: 'default'
        }
      ]
      if (this.specialAlias) {
        switch (this.specialAlias) {
          case 've-bar-Heatmap': {
            _dimensionTypes = [
              {
                type: 'x'
              },
              {
                type: 'y'
              }
            ]
            break
          }
          default: {
            _dimensionTypes = [
              {
                type: 'default'
              }
            ]
            break
          }
        }
      }
      this.dimensionTypes = _dimensionTypes
      console.log('%c [ this.dimensionTypes ]-2521', 'font-size:13px; background:#c20b27; color:#ff4f6b;', this.dimensionTypes)
    },
    // 度量
    // 变为多维度或单维度列
    updateMetricTypes() {
      let _metricTypes = [
        {
          type: 'default'
        }
      ]
      if (this.specialAlias) {
        switch (this.specialAlias) {
          case 've-scatter-normal': {
            _metricTypes = [
              {
                type: 'x'
              },
              {
                type: 'y'
              }
            ]
            break
          }
          case 've-bar-Heatmap': {
            _metricTypes = [
              {
                type: 'default'
              }
            ]
            break
          }
          default: {
            _metricTypes = [
              {
                type: 'histogram'
              },
              {
                type: 'line'
              }
            ]
            break
          }
        }
      }
      this.metricTypes = _metricTypes
      console.log('%c [ this.metricTypes ]-2521', 'font-size:13px; background:#c20b27; color:#ff4f6b;', this.metricTypes)
    },
    // 切换图表类型时
    // 需要根据chartUserConfig配置项去处理各处属性
    async handleChartTypeSwitch(chart, key) {
      const { alias } = chart

      if (this.drillSettings.pageInfo) {
        this.drillSettings.pageInfo.page = 1
      }

      this.clearUserConfigStatus()

      if (['ve-gauge-normal'].includes(alias)) {
        this.$refs['chartContent'].setHasChartData(false)
      }


      // 图表类型处理对应的content
      // 这里涉及到合并机制的问题详见方法内部

      // 切换条形图 排序
      this.reverseConfigChartType('v-line', alias)

      this.$set(
        this.currentEditData,
        'content',
        this.handleChart(this.currentEditData.content, chart)
      )

      // 特殊数据结构图形会重新请求数据，不需要多次初始化颜色列表
      // chartData变化之后会走一遍initColors，所以注释掉这里，防止之前的数据顺序对后面的数据顺序产生影响
      if (
        ![...HAS_TARGET_CHART, ...SPEICAL_DATA_STRUCTURECHART].includes(alias)
      ) {
        this.initColorsConfig(alias)
      }

      if (this.chartUserConfig.chartType !== 've-map') {
        if (this.chartUserConfig.chartAlias === 've-grid-normal') {
          this.$refs['chartContent'].createTable({
            from: 'chartSet-handleChartTypeSwitch',
            firstRender: true,
            getCanvasSize: true,
            resetOriginColumn: true
          })
        } else {
          this.$refs['chartContent'].resetAfterConfig()
        }
      }

      if (
        [
          ...ANIMATION_HISTOGRAM_CHART,
          ...ANIMATION_TOOLTIP_CHART,
          've-radar',
          've-themeRiver'
        ].includes(alias) &&
        this.chartUserConfig?.animationSetting?.enable
      ) {
        this.previewChart({ from: 'chartTypeSwitch' })
      }

      if(['ve-bar-Heatmap'].includes(alias)){
        await this.updateDimensionTypes()
        this.chartUserConfig.dimensionList.forEach((d,i) => {
          this.setDragList(`dimensionDragBox${d.DIMENSIONTYPE}`, [d])
        })
      } else {
        this.setDragList('dimensionDragBox', this.chartUserConfig.dimensionList)
      }

      if(['ve-bar-stack'].includes(alias)) {
        if(this.chartUserConfig?.barSetting?._isEditBarStack) return
        this.$set(this.chartUserConfig,'metricSplitDisplay', true)
      }
    },
    initMetricLabelPosition() {
      const { metricLabelDisplay = [] } = this.chartUserConfig
      if (metricLabelDisplay.length) {
        const defaultItem = generateMetricLabelDisplayItem({
          chartUserConfig: this.chartUserConfig
        })
        metricLabelDisplay.forEach(m => {
          m.metricLabelPosition = defaultItem.metricLabelPosition
        })
      }
    },
    // 子类型切换
    onChangeChildChartType(chart, key) {
      const { alias, childType } = chart
      if (['ve-map-parent'].includes(alias)) {
        this.initColorsConfig()
        this.doRegisterMap(
          this.chartUserConfig.mapRangeVal === 'provice'
            ? this.chartUserConfig.mapProviceVal || 'beijing'
            : {}
        )
      }
      this.$refs.chartContent.resetAfterConfig({ isMapInit: true })
      // 切换地图子类型播放动画
      this.$nextTick(() => {
        if (this.currentEditData?.vm?.animationPlay) {
          this.currentEditData.vm.animationPlay({ type: 'reset' })
        }
      })
    },
    changeColor(alias = this.currentEditData.content?.alias) {
      // 图形色系
      const colors = Color.getDefaultChartColor(this.chartUserConfig, {
        chartResponse: this.currentEditData.content.chartResponse,
        chartAlias: alias,
        colorThemeType: this.themeType,
        vm: this.$refs.chartContent
      })
      this._changeColor(colors)
    },

    // 通过按钮切换条形图 不需要从后台返回数据  需要和ChartVeBarNormal 标记结合
    // chartAlias  旧的别名  alias 新的别名
    // ChartVeBarNormal false && isRowsReverse false  表示条形图没有置换
    // ChartVeBarNormal true 表示条形图置换了
    // isRowsRevers true 表示条形图置换了
    reverseConfigChartType(chartAlias, alias) {
      const isReverseChart = isReverseAxisChart({
        chartUserConfig: this.chartUserConfig
      })
      if (this.chartUserConfig.chartAlias === chartAlias || isReverseChart) {
        if (!this.isRowsReverse && !this.chartUserConfig.ChartVeBarNormal) {
          if (this.chartConfig) {
            this.isRowsReverse = this.drillSettings.layers[0].metrics.some(
              metricsItem => metricsItem.order
            )
            if (this.isRowsReverse && Array.isArray(this.chartData.rows)) {
              this.chartData.rows.reverse()
            }
          }
        }
      } else if (
        this.chartUserConfig.chartAlias !== chartAlias &&
        !isReverseChart
      ) {
        if (this.chartUserConfig.ChartVeBarNormal) {
          this.$set(this.chartUserConfig, 'ChartVeBarNormal', false)
          this.isRowsReverse = false
          this.chartData.rows.reverse()
        } else if (this.isRowsReverse) {
          this.isRowsReverse = false
          this.chartData.rows.reverse()
        }
      }
    },

    clearUserConfigStatus() {
      // 散点图
      if (this.chartConfig) {
        this.$delete(this.chartConfig, 'markLine')
        this.$delete(this.chartConfig, 'xAxis')
        this.$delete(this.chartConfig, 'yAxis')

        this.chartConfig.toolbox &&
          this.$set(this.chartConfig.toolbox, 'show', false)

        this.chartConfig.extend &&
          this.$delete(this.chartConfig.extend, 'tooltip')
        this.activeTabsName = 'color'
      }
      this.preventRequest = true
      this.$nextTick(() => {
        this.preventRequest = false
      })

      // 增长率
      this.$set(this.chartUserConfig, 'growthRate', false)
      delete this.drillSettings.pieChart
      // 清除指标选择器
      // this.$set(this.chartUserConfig, 'choiceDimensions', [])
    },
    // 选择组合图形的时候,下拉选择当前维度的图表种类
    changeMetricChartType(chartType, metricChartType) {
      let key = ''
      switch (chartType) {
        case 'histogram': {
          key = 'stack'
          break
        }
        case 'line': {
          key = 'area'
          break
        }
      }
      this.$set(
        this.chartUserConfig.compositeChart,
        key,
        metricChartType !== 'normal'
      )
      this.initMetricLabelPosition()
      this.changeColor()
      this.previewChart({ from: 'changeMetricChartType' })
    },
    // 处理图表切换后的content
    handleChart(content, newContent) {
      if (Object.prototype.toString.call(newContent) !== '[object Object]') { return content }
      // 根据维度度量以及图表类型获得chartSettings
      // 堆叠图和柱状+线图是要多处理settings下面的showline,stack字段
      const chartSettings = handleSettings.call(
        this,
        content.chartSettings,
        newContent.chartSettings,
        this.currentEditData
      )

      if(content.alias === 've-bar-Heatmap') {
        // 从热力图切换到其他图形，如果有两个维度时，第一个维度默认升序
        // 双维度时第一个维度默认升序
        if (
          this.chartUserConfig.dimensionList.length === 2 &&
          DOUBLE_DIMENSION_CHART.includes(newContent.alias)
        ) {
          this.chartUserConfig.dimensionList.map(
            (e, i) => {
              if((!e.order || e.order === 'none') && i === 0) {
                this.$set(e, 'order', 'asc')
              }
            }
          )
        }
        this.$refs.tableInfo && this.$refs.tableInfo.$refs.ChartRankOf && this.$refs.tableInfo.$refs.ChartRankOf.setDefault()
      }
      if(newContent.alias === 've-bar-Heatmap' && content.alias !== 've-bar-Heatmap') {
         // 从其他图形切换到热力图, 如果度量和维度都设置了排序，则只保留维度的排序
        const { dimensionList =[] } = this.chartUserConfig
        if(dimensionList.length && dimensionList.some(e => e.order && e.order !== 'none')) {
          this.clearStatus({
            type: 'SORT',
            itemType: 'metric'
          })
        }
      }

      if (content.alias === 've-scatter-normal' && this.prevChartSetting) {
        // 从散点图切换其他图形时，需要恢复 维度和度量
        // chartSettings.dimension = this.prevChartSetting.dimension
        // 双维度散点图切换成其他图形有点bug，需要这么处理维度
        chartSettings.dimension = this.chartUserConfig.dimensionList.map(
          e => e.alias || e.labeName
        )
        chartSettings.metrics = this.prevChartSetting.metrics
        this.prevChartSetting = null
      }
      if (newContent.alias === 've-line-area') {
        chartSettings.area = newContent.chartSettings.area
      }
      // 合并数据
      // 切换图表类型的时候应该保留请求的数据(content.chartData)
      // 但是配置项需要进行更改成新图表对应的(上面获取到的chartSettings)

      return {
        ...content,
        ...newContent,
        chartConfig: this.$_deepMergeObjects(
          content.chartConfig || {},
          newContent.chartConfig || {}
        ),
        chartData: content.chartData,
        chartSettings
      }
    },

    // 引用类型放到组件内部来维护
    // 需要从外部主动调用这个方法来设置图表编辑面板的状态
    // 在Supernatant/index.vue chartOptionsOnSet方法中被调用
    setData(data) {
      // MARK:初始化图表数据
      this.initCompleted = false
      this.initChart = true
      this.isFistChangeField = true
      this.isShowDeputyAxis = false
      this.metricTypes = [
        {
          type: 'default'
        }
      ]
      if (!this.onlyIndex) {
        this.getCompareTypeData()
      }
      this.initCurrentEditData(data)
      this.isShowDataset = true

      // if(this.currentEditData.content.alias === 've-bar-Heatmap'){
        this.updateDimensionTypes()
      // }

      // 设置各个子组件的参数
      setTimeout(() => {
        this.initComponents()
      }, 50)

      // 记录被卡片交互的信息
      const id = data.id
      const els = []
      const actions = {}
      this.elList.forEach(el => {
        if (el.type === 'element-tag-new-card' && el.content.interactionOptions?.length) {
          el.content.interactionOptions.forEach(opt => {
            if (opt.associElements?.length && opt.associElements.find(ass => ass.id === id)) {
              els.push(el)
              if (!actions[el.id]) {
                actions[el.id] = [ opt ]
              } else {
                actions[el.id].push(opt)
              }
            }
          })
        }
      })
      this.interactionReferencedInfo = { els, actions, delIndicators: {} }
    },
    // 获取度量函数对比方式
    async getCompareTypeData() {
      const compareModeInfo = await getCompareTypeList(
        this.api,
        this.utils.tenantId
      )
      let compareObj = {
        compareList: [],
        defaultValue: ''
      }
      Array.isArray(compareModeInfo) &&
        compareModeInfo.map(item => {
          item.isDefault === '1' &&
            (compareObj.defaultValue = item.configValue)
          compareObj.compareList.push({
            value: item.configValue,
            label: item.configName
          })
        })
      this.$set(this.chartUserConfig, 'compareModeData', compareObj)
      // 初始化指标选择器对比方式
      const chioceTab = this.currentEditData.content.chioceTab || []
      if (chioceTab.length) {
        chioceTab.forEach(item => {
          item.saveObj.chartUserConfig.compareModeData = compareObj
        })
      }
      // 初始化度量函数对比方式
      this.callMetricBoxFunction('initCompareData')
    },
    // 调用度量dragbox函数
    callMetricBoxFunction(method, data) {
      this.$nextTick(() => {
        const metricTypes = this.metricTypes
        const mTypes = Array.isArray(metricTypes)
          ? metricTypes
          : Object.keys(metricTypes)
        mTypes.forEach(metric => {
          const type = typeof metric === 'string' ? metric : metric.type
          const metricsBox = this.$refs[`metric${type}`]
          metricsBox.forEach(comp => comp['changeCascader'](method, data))
        })
      })
    },
    // 初始化存储整个图形数据的对象
    // 这个方法由外部调用,每次打开弹窗的时候,传入对应的图形
    initCurrentEditData(data) {
      const drillSettings = {
        id: data.id || '',
        dataSetId: '',
        // 请求全部数据
        // pageInfo: {},
        pageInfo: {
          page: 1,
          pageSize: 9999
        },
        layers: [
          {
            dimension: [],
            metrics: [],
            extendDimension: [],
            orderList: [],
            level: 0
          }
        ],
        detailedReport: {
          reportId: '',
          paraName: ''
        },
        drills: []
      }
      const chartConfig = {}
      const _data = this.$_JSONClone(data)
      _data.content = this.$_deepMergeObjects(
        { drillSettings, chartConfig },
        _data.content
      )
      // 编辑界面清空钻取数据
      const drillDimensions = this.$_getProp(
        _data,
        'content.drillSettings.drillDimensions',
        []
      )
      if (drillDimensions.length) {
        this.$set(_data.content.drillSettings, 'drillDimensions', [])
        if (
          _data.content.chartUserConfig?.childChartAlias === 've-map-britain'
        ) {
          this.$set(_data.content.chartSettings, 'position', 'britain')
        }
      }
      if (_data.content.chioceTab && _data.content.chioceTab.length) {
        _data.content.chioceTab.forEach(tab => {
          const drillDimensions = this.$_getProp(
            tab,
            'saveObj.drillSettings.drillDimensions',
            []
          )
          drillDimensions.length &&
            this.$set(tab.saveObj.drillSettings, 'drillDimensions', [])
        })
      }
      this.currentEditData = _data
      this.selfChartDesignHandler = new ChartDesignHandler(
        this.currentEditData,
        {
          themeType: this.themeType,
          i18n: this.$t.bind(this),
          getDatasetLabel: this.getDatasetLabel.bind(this),
          vm: this.$refs.chartContent
        }
      )
      // 初始化图形使用数据集
      if (_data.content.chartUserConfig.datasetAssociation) {
        const selectedDataset = []
        _data.content.dataset &&
          _data.content.dataset.forEach(item => {
            if (item.dateType === 'associateDataset') {
              this.associatedDataset = item
            } else {
              selectedDataset.push(item)
            }
          })
        this.selectedDataset = selectedDataset
      } else {
        this.associatedDataset = ''
        this.selectedDataset = []
      }

      // 初始化漏斗图数据
      this.initFunnel()
      // 初始化空值显示设置
      if (!this.chartUserConfig.hasOwnProperty('isShowEmptyValue')) {
        this.$set(this.chartUserConfig, 'isShowEmptyValue', true)
      }
      if (this.chartUserConfig.isShowLegendSelection === undefined) {
        // this.$set(this.chartUserConfig, 'isShowLegendSelection', true)
        this.$set(this.chartUserConfig, 'isShowLegendSelection', false)
      }
      if (!this.currentEditData.content.hasOwnProperty('associatedData')) {
        this.$set(this.currentEditData.content, 'associatedData', {})
      }
      this.setFontTitleStyle('dateDimensionStyle', 'dateDimensionColor')
    },
    // 设置指标选择器/时间维度的字体样式
    setFontTitleStyle(fontStyle, colorField) {
      // 配置指标选择器的文字样式
      const themeFontColor = this.$_getProp(
        this.currentEditData.content.chartUserConfig.themeConfig[
          this.themeType
        ],
        colorField,
        {}
      )
      let dimensionFontColor =
        colorField === 'dateDimensionColor' &&
        this.themeData?.appThemeType === THEME_TYPE.deepBlue &&
        this.isMobile
          ? '#428AFF'
          : this.themeType === THEME_TYPE.classicWhite
          ? '#222'
          : '#E9E9EF'
      let fontColor = themeFontColor?.color
        ? themeFontColor
        : { color: dimensionFontColor }
      let curFontStyle = this.currentEditData.content?.[fontStyle]
      if (!curFontStyle || !Object.keys(curFontStyle).length) {
        // 游标样式
        let defaultCursorStyle = {
          'font-family': 'NotoSansHans-Regular', // 'Roboto-Regular',
          'font-size': '13px',
          'font-weight': '', // '',
          'text-decoration': '',
          color: '' // #8BA1C7
        }
        Object.assign(this.currentEditData.content, {
          [fontStyle]: defaultCursorStyle
        })
      }
      Object.assign(this.currentEditData.content[fontStyle], fontColor)
    },
    // 将对应主题下的配置映射到chartuserconfig
    getThemefromContent(content) {
      if (content.chioceTab && content.chioceTab.length) {
        content.chioceTab.forEach(ct => this._getThemefromContent(ct.saveObj))
      } else {
        this._getThemefromContent(content)
      }
    },
    _getThemefromContent(content) {
      let { chartUserConfig } = content
      // 如果没有经典白的配置，那chartuserconfig里面肯定全部是经典白的配色
      if (
        !chartUserConfig.themeConfig ||
        !chartUserConfig.themeConfig['sdp-classic-white'] ||
        this.$_isEmptyObject(chartUserConfig.themeConfig['sdp-classic-white'])
      ) {
        // 先保存经典白的配色
        setThemeConfig(chartUserConfig, 'sdp-classic-white')
      }
      const chartChild = getChartsList().find(
        e => e.alias === chartUserConfig.chartAlias
      ).children
      if (
        !chartUserConfig.childChartAlias &&
        chartChild &&
        Array.isArray(chartChild)
      ) {
        let defaultChild =
          chartChild.find(c => c.isDefault === true) || chartChild[0]
        this.$set(chartUserConfig, 'childChartAlias', defaultChild.typeName)
      }
      setThemeChartUserConfig.call(
        this,
        chartUserConfig,
        this.themeType,
        content
      )
    },
    initComponents() {
      const { content } = this.currentEditData
      if (
        !content ||
        !content.drillSettings ||
        !content.drillSettings.dataSetId
      ) {
        this.datasetLoading = false
      }
      if (content) {
        const { drillSettings, chartUserConfig } = content
        // 初始化数据集字段
        if (drillSettings && !!drillSettings.dataSetId) {
          this.getSelectionsList(drillSettings.dataSetId)
          const {
            layers: [{ dimension }]
          } = drillSettings
          // 编辑看板进入，图形类型不是默认值时，会触发 specialAlias Watch
          // 需要 在 specialAlias Watch 之后 再初始化维度和度量
          this.initSpecialAliasMetrics(
            this.doubleMetric &&
              Object.keys(this.chartUserConfig.metricsContainer).length === 1
          )
          // if(this.currentEditData.content.alias === 've-bar-Heatmap'){
            chartUserConfig.dimensionList.forEach((d,i) => {
              this.setDragList(`dimensionDragBox${d.DIMENSIONTYPE}`, this.chartFieldsToDatasetFields([d], 'demension'))
            })
          // } else {
            this.setDragList(
              'dimensionDragBox',
              this.chartFieldsToDatasetFields(dimension, 'demension')
            )
          // }

          this.initMetricBox()
          // 初始化完成
          this.initCompleted = true
        } else {
          this.clearComponentsStatus()
        }
        // 初始化tableInfo
        this.$refs.tableInfo && this.$refs.tableInfo.initDialogTableInfo()
      }
    },
    initSpecialAliasMetrics(flag) {
      this.updateMetricTypes()
      this.updateDimensionTypes()
      const { metricTypes } = this
      const _metricsContainer = this.chartUserConfig.metricsContainer
      function setDefault(data, vm) {
        const _default = vm.$_deepClone(_metricsContainer['default']) || []
        if (
          _metricsContainer.size?.length &&
          vm.alias !== 've-scatter-normal'
        ) {
          const sizeKeyName = _metricsContainer.size.map(im => im.keyName)
          const sizeMetricIndex = _default
            .map(m => m.keyName)
            .lastIndexOf(sizeKeyName[0])

          sizeMetricIndex > -1 && _default.splice(sizeMetricIndex, 1)
        }
        vm.$set(data, 'default', _default)
      }
      if (flag) {
        this.chartUserConfig.metricsContainer = metricTypes.reduce(
          (pre, current) => {
            const type = current.type
            const xValue =
              _metricsContainer.x || _metricsContainer.histogram || []
            const yValue = _metricsContainer.y || _metricsContainer.line || []
            if (['x', 'histogram'].includes(type)) {
              if (xValue?.length) {
                this.$set(pre, type, this.$_deepClone(xValue) || [])
              } else {
                this.$set(
                  pre,
                  type,
                  this.$_deepClone(_metricsContainer['default']) || []
                )
              }
              setDefault(pre, this)
            } else if (['y', 'line'].includes(type)) {
              this.$set(pre, type, this.$_deepClone(yValue))
            } else {
              setDefault(pre, this)
            }
            return pre
          },
          {}
        )
        if (!this.initChart) {
          intoScatterCombination(this, this.chartUserConfig.chartAlias)
        }
      }
      this.$nextTick(() => {
        this.setMetricBox()
      })
    },
    setMetricBox() {
      this.preventRequest = true
      Object.keys(this.chartUserConfig.metricsContainer).forEach(type => {
        if (this.$refs[`metric${type}`]) {
          const metricComp = Array.isArray(this.$refs[`metric${type}`])
            ? this.$refs[`metric${type}`][0]
            : this.$refs[`metric${type}`]
          metricComp &&
            metricComp.setList &&
            metricComp.setList(this.chartUserConfig.metricsContainer[type])
        }
      })
      // this.setDragList(ref, list)
      this.$nextTick(() => {
        this.preventRequest = false
      })
    },
    // 初始化目标值 以及恢复目标值
    initTargetBox() {
      this.$nextTick(() => {
        const { targetDragBox, chartTargetChange } = this.$refs
        if (!targetDragBox) return
        const { gaugeTarget = {} } = this.chartUserConfig
        const defaultTagdetList = gaugeTarget
          ? gaugeTarget.defaults
          : undefined
        if (gaugeTarget.type === 'aggType') {
          targetDragBox.setList(
            gaugeTarget.defaults ? [gaugeTarget.defaults] : []
          )
        } else {
          this.$set(
            this.drillSettings.layers[0],
            'gaugeTarget',
            defaultTagdetList
          )
        }
        chartTargetChange && chartTargetChange.setSelected()
      })
    },
    // 初始化维度和度量
    initMetricBox() {
      const {
        metricsContainer,
        dimensionList,
        extendDimensionList,
        contrastList,
        longitudeList,
        latitudeList,
        dimensionDestinationList,
      } = this.chartUserConfig
      const keys = Object.keys(metricsContainer)
      // 维度
      this.dimensionList = dimensionList
      if (dimensionList && dimensionList.length) {
        // if(this.currentEditData.content.alias === 've-bar-Heatmap'){
          dimensionList.forEach((d,i) => {
            this.setDragList(`dimensionDragBox${d.DIMENSIONTYPE}`, [d])
          })
        // } else {
           this.setDragList('dimensionDragBox', dimensionList)
        // }
      }
      if (longitudeList?.length) {
        this.setDragList('longitudeDragBox', longitudeList)
      }
      if (latitudeList?.length) {
        this.setDragList('latitudeDragBox', latitudeList)
      }
      if (dimensionDestinationList?.length) {
        this.setDragList('destinationDragBox', dimensionDestinationList)
      }
      // 带宽图初始化维度和度量
      const { max, min, bw } = getBandwidthData(this.chartUserConfig)
      if (max) {
        this.setDragList('maxBandwidthDragBox', [max])
        this.setDragList('minBandwidthDragBox', [min])
      } else if (bw) {
        this.setDragList('bandwidthDragBox', [bw])
      }
      // 扩展维度
      if (extendDimensionList && extendDimensionList.length) {
        this.extendDragBoxShow = true
        this.setDragList('extendDimensionDragBox', extendDimensionList)
      }
      // 对比值
      if (contrastList?.length && this.showContrastDragBox) {
        this.setDragList('contrastValueDragBox', contrastList)
      }
      this.initDeputyFlag()
      if (keys.length === 1) {
        this.setDragList('metricdefault', metricsContainer.default)
      } else {
        keys.splice(keys.indexOf('default'), 1)
        keys.forEach(key => {
          const metricName = `metric${key}`
          this.setDragList(metricName, metricsContainer[key])
        })
      }
      // 初始化目标值
      this.initTargetBox()
      try {
        this.$refs.tableInfo.initData(this.currentEditData.content)
      } catch (err) {}
    },

    clearComponentsStatus() {
      this.preventRequest = true
      this.initCompleted = true

      this.dimensionList = []
      // this.metricList = []
      this.setDragList('fieldList', [])
      this.cloneSelectionList = []
      this.setDragList('dimensionDragBox', [])
      this.setDragList('metricdefault', [])
      if (Array.isArray(this.metricTypes)) {
        this.metricTypes.forEach(metric => {
          const metricsBox = this.$refs[`metric${metric.type}`]
          const metricComp = Array.isArray(metricsBox)
            ? metricsBox[0]
            : metricsBox
          metricComp && metricComp.setList([])
        })
      }
      this.$nextTick(() => {
        // 初始化完成后，图形初始化标识置为false
        this.initChart = false
      })
      setTimeout(() => {
        this.preventRequest = false
      }, 100)
    },

    // 将图表中的字段格式转为数据集中格式
    chartFieldsToDatasetFields(chartFields, type) {
      const DIMENSION = {
        columnTpe: '',
        labeName: '',
        parentId: this.drillSettings.dataSetId,
        reportFieldType: type
      }
      return chartFields.map(field =>
        Object.assign({}, DIMENSION, {
          columnTpe: field.columnType,
          labeName: field.columnName
        })
      )
      // const dimension = dragArr.map(e => Object.assign({}, DIMENSION, {
      //   columnType: e.columnTpe,
      //   columnName: e.labeName,
      //   alias: e.labeName,
      //   order: e.order,
      // }))
    },
    // 清空数据集相关配置
    emptyChartDatasetConfig() {
      const chioceTab = this.currentEditData.content.chioceTab || []
      // 切换数据集清除过滤
      this.$delete(this.drillSettings, 'filters')
      this.clearGauge()
      this.resetSomePropperty(this.currentEditData.content)
      if (!this.$refs.chartContent?.isChildMapScheme && chioceTab.length) {
        chioceTab.forEach(item => {
          this.$refs.tableInfo.emptySomeProperty(item.saveObj)
        })
        this.currentEditData.content.chioceTab = []
        this.currentEditData.content.saveIndex = this.indicatorIndex = 0
      }
      // 清空当前数据
      this.$refs.tableInfo.emptySomeProperty(this.currentEditData.content)
      this.getSelectionsList(this.drillSettings.dataSetId, true)
    },
    relChangeSelections() {
      // 当有指标选择器时，切换数据集，清空所有指标选择器数据
      const chioceTab = this.currentEditData.content.chioceTab || []
      if (!this.$refs.chartContent?.isChildMapScheme && chioceTab.length) {
        this.$sdp_eng_confirm(
          `${this.$t('sdp.views.confirmChangeDateSet')}`,
          `${this.$t('sdp.message.tipsConfirmTitle')}`,
          {
            confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
            cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
            cancelButtonClass: 'el-button--sdp-cancel',
            confirmButtonClass: 'el-button--sdp-ensure',
            customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
            type: 'warning'
          }
        )
          .then(() => {
            this.emptyChartDatasetConfig()
          })
          .catch(() => {
            this.drillSettings.dataSetId = this.olddataSetId
          })
      } else {
        this.emptyChartDatasetConfig()
      }
    },
    resetSomePropperty(content) {
      // 清除列总计数据
      if (content.alias === 've-grid-normal') {
        this.$set(
          content.chartUserConfig,
          'gridSetting',
          defaultSetting.gridSetting.create(this.themeType)
        )
      }
    },
    // 指标切换
    changeIndex(compareObj, datasetId, isGetSelectionsList = true) {
      const {
        metricsContainer,
        dimensionList,
        extendDimensionList
      } = this.chartUserConfig
      // 度量要自动填充和删除
      const keys = Object.keys(metricsContainer)?.length > 0 ? Object.keys(metricsContainer) : ['default']
      if (keys.length === 1) {
        const _list = this.getMetricList(metricsContainer.default, compareObj)
        this.onMetricsChange([{ dragList: _list, notPreview: true, oldLength: metricsContainer.default.length }], 'default')
        this.setDragList('metricdefault', _list)
      } else {
        keys.splice(keys.indexOf('default'), 1)
        // 双轴，每个轴 单度量
        keys.forEach(async (key, index) => {
          const metricName = `metric${key}`
          // 收集所有其他 key 的数据（排除当前 key）
         let otherKeysData = []
          Object.keys(metricsContainer).forEach((k) => {
            if (k !== key && !['size', 'default'].includes(k) && metricsContainer[k]?.length) {
              otherKeysData.push(...metricsContainer[k])
            }
            if(['default'].includes(k)) {
              this.$set(metricsContainer, k, [])
            }
          })

          let metricParam = {
            key: key || null,
            index: !otherKeysData.length ? index : null,
            otherKeysData: this.$_deepClone(otherKeysData) || []
          }
          const _list = ['default'].includes(key) ? [] : this.getMetricList(metricsContainer[key], compareObj, metricParam)
          this.onMetricsChange([{ dragList: _list, notPreview: true, oldLength: metricsContainer[key].length }], key)
          this.setDragList(metricName, _list)
        })
      }
      // 维度
      const refComponent = Array.isArray(this.$refs[this.datasetType])
      ? this.$refs[this.datasetType][0]
      : this.$refs[this.datasetType]
      const _dimensionlist = this.drillSettings?.indexFlagIds?.length > 1
      ? (refComponent?.getCommonAndIndependent()?.common || [])
      : (refComponent?.getCommonAndIndependent()?.independent || [])
      // 删除不是共有维度或不存在的维度
      const needDimensionList = dimensionList?.length > 0 ? dimensionList.filter(item => _dimensionlist.some(x => x.labeName === item.labeName)) : []
      if (dimensionList && dimensionList.length) {
        dimensionList.forEach((d,i) => {
          const list = needDimensionList?.length > 0 ? needDimensionList.filter(n => n.labeName === d.labeName) : []
          this.setDragList(`dimensionDragBox${d.DIMENSIONTYPE}`, list)
        })
      }
      this.setDragList('dimensionDragBox', needDimensionList)
      // 扩展维度 - 删除不是共有维度或不存在的维度
      const needExtendDimensionList = extendDimensionList?.length > 0 ? extendDimensionList.filter(item => _dimensionlist.some(x => x.labeName === item.labeName)) : []
      this.setDragList('extendDimensionDragBox', needExtendDimensionList)
      // 下钻
      this.checkAndClearDrillList(_dimensionlist)
       if (isGetSelectionsList) {
        this.getSelectionsList(datasetId, false)
      }
      // 清除不存在的指标过滤信息
      if (this.drillSettings.filters?.length) {
        this.drillSettings.filters = this.drillSettings.filters.filter(item => {
          return this.datasetArray.some(dataset => dataset.id === item.dataSetId)
        })
      }
    },
    getMetricList(metricsList, compareObj, metricParam = {}) {
      const { key = null, index = null, otherKeysData = [] } = metricParam
      let result = metricsList?.length > 0 ? [...metricsList] : []
      const addedItemsFlag = !!compareObj?.addedItems?.length
      const deletedItemsFlag = !!compareObj?.deletedItems?.length
      if(deletedItemsFlag) {
        let _delMetric = []
        compareObj.deletedItems.map(a =>{
          _delMetric.push(a.children.find(item => item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM')))
        })
        const _ids = new Set(_delMetric.map(x => x.parentId))
        result = result.filter(item => !_ids.has(item.parentId))
      }

      if(addedItemsFlag) {
        let _addMetric = []
        compareObj.addedItems.map(a =>{
          _addMetric.push(a.children.find(item => item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM')))
        })
        if(key && ['x', 'y','line'].includes(key) && result.length) {
          return result
        } else if(key && !result.length && !otherKeysData?.length && (typeof index === 'number')) {
          result = result.concat(_addMetric[index])
        } else if(key && !result.length && otherKeysData?.length) {
          const duplicateItems = _addMetric.filter(item =>
            otherKeysData.some(existingItem => existingItem.parentId !== item.parentId)
          )
          result = result.concat(duplicateItems[0])
        } else if( key && ['histogram'].includes(key) && !otherKeysData?.length && result.length ) {
          const _arr = _addMetric.slice(1)
          result = result.concat(_arr)
        } else {
          result = result.concat(_addMetric)
        }
      }
      return result
    },
    // 切换指标-共有维度发生变化调整钻取逻辑
    // 下钻维度、自定义排序中只要有一个没有匹配上，就清空全部
    checkAndClearDrillList(_dimensionlist) {
      const { drillList } = this.currentEditData.content || {}
      if (!drillList?.length) {
        return
      }
      if (!_dimensionlist?.length) {
        this.$set(this.currentEditData.content, 'drillList', [])
        return
      }
      const dimensionLabels = _dimensionlist.map(item => item?.labeName?.toUpperCase()).filter(Boolean)

      // 检查 drillList 是否有不匹配的字段
      const hasMismatch = drillList.some(drillItem => {
        if (!drillItem) return true
        const columnName = drillItem?.columnName?.toUpperCase()
        const columnNameExists = dimensionLabels.includes(columnName)
        // 检查 orderList 里的每一项 columnName 是否在 dimensionLabels 中
        const orderListMismatch = drillItem?.orderList?.some(orderItem => {
          if (!orderItem) return true
          const orderColumnName = orderItem?.columnName?.toUpperCase()
          return !dimensionLabels.includes(orderColumnName)
        }) || false

        return !columnNameExists || orderListMismatch
      })
      // 如果有不匹配的项，清空 drillList
      if (hasMismatch) {
        this.$set(this.currentEditData.content, 'drillList', [])
      }
    },
    // 数据集切换
    getSelections(data) {
      // 暂时只能做到如果探明有被参数组件绑定的时候就提示，还做不到根据绑定多少参数组件，是否是所有参数组件的最后一个
      // 来给出提示，如果需要完善，则需要一个方法获取所有参数组件下的看板元素绑定的数据集
      this.$nextTick(() => {
        let dataSets = []
        this.boardData.paramsPanelList.map(v => {
          if (v.content && v.content.length > 0) {
            v.content.map(i => {
              dataSets.push(
                ...paramsBindDatasets(i, this.$parent.nowElementId).map(
                  d => d.datasetId
                )
              )
            })
          }
        })
        dataSets = Array.from(new Set(dataSets))
        if (dataSets.includes(this.olddataSetId) && !this.isFirstDataSet) {
          this.$sdp_eng_confirm(
            `${this.$t('sdp.views.confirmChangeDataSet')}`,
            `${this.$t('sdp.message.tipsConfirmTitle')}`,
            {
              confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
              cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
              cancelButtonClass: 'el-button--sdp-cancel',
              confirmButtonClass: 'el-button--sdp-ensure',
              customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
              type: 'warning'
            }
          )
            .then(() => {
              this.relChangeSelections()
            })
            .catch(() => {
              this.drillSettings.dataSetId = this.olddataSetId
            })
        } else {
          this.onlyIndex ? this.emptyChartDatasetConfig() : this.relChangeSelections()
        }
      })
    },
    setCloneSelectionList(list) {
      this.cloneSelectionList = this.$_deepClone(list)
    },
    // MARK:数据集id变动,更新维度和度量列表
    async getSelectionsList(datasetId, isClear) {
      if (this.drillSettings) {
        // 根据id查找指标数据
        let indexItem = this.datasetList.find(item => item.id === datasetId)
        this.drillSettings.dataSetId = datasetId
        // 此结构不满足，需要额外加上isIndicator跟labelName\indexType
        if (indexItem) {
          this.drillSettings.dataSetLabelName = indexItem.labeName
          this.drillSettings.indexFlag = indexItem.indexFlag
          this.drillSettings.indexType = indexItem.indexType
        }
      }
      let list
      let allList = []
      if (this.onlyIndex) {
        this.drillSettings.indexFlag = true
        this.datasetLoading = true
        await getDataSetTreeByIds(this.api, {
          ids: [...this.drillSettings.indexFlagIds],
        }, this.aliasDict).then((res) => {
          this.dataSetRes = res
        })
      }

      const setAllList = () => {
        const refComponent = Array.isArray(this.$refs[this.datasetType])
        ? this.$refs[this.datasetType][0]
        : this.$refs[this.datasetType]
        const commonAndIndependent = refComponent?.getCommonAndIndependent()
        // 合并共有维度+独立维度+指标字段
        const common = (commonAndIndependent?.common || []).map(item => {
          return {
            ...item,
            allFieldType: 'common'
          }
        })
        const independent = (commonAndIndependent?.independent || []).map(item => {
          return {
            ...item,
            allFieldType: 'independent'
          }
        })
        const indexFields = (commonAndIndependent?.indexFields || []).map(item => {
          return {
            ...item,
            _rawLabeName: item.labeName,
            _uniqueLabeName: item.labeName + '_$$_' + item.dataSetId,
            labeName: item.labeName + '_$$_' + item.dataSetId, // 指标字段需要修改值，否则值都相同
            allFieldType: 'indexField'
          }
        })
        allList = [...common, ...independent, ...indexFields]
        this.$set(this, 'allFieldList', allList)
      }
      if (this.drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag) {
        setAllList()
        const chioceTab = this.currentEditData.content.chioceTab || []
        if (!this.$refs.chartContent?.isChildMapScheme && chioceTab.length) {
          chioceTab.forEach(item => {
            this.$set(item.saveObj.drillSettings, 'indexFlagIds', this.drillSettings.indexFlagIds)
            const itemDataSetId = item?.saveObj?.drillSettings?.dataSetId
            const indexFlagIds = item?.saveObj?.drillSettings?.indexFlagIds || []
            if (!indexFlagIds.includes(itemDataSetId)) {
              this.$set(item.saveObj.drillSettings, 'dataSetId', this.drillSettings.dataSetId)
            }
          })
        }
      }

      if (this.drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag && this.drillSettings.indexFlagIds.length > 1) {
        const refComponent = Array.isArray(this.$refs[this.datasetType])
        ? this.$refs[this.datasetType][0]
        : this.$refs[this.datasetType]
        list = refComponent?.getCommonAndIndependent()?.common || []
      } else if(this.drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag && this.drillSettings.indexFlagIds.length === 1) {
        const refComponent = Array.isArray(this.$refs[this.datasetType])
        ? this.$refs[this.datasetType][0]
        : this.$refs[this.datasetType]
        list = refComponent?.getCommonAndIndependent()?.independent || []
      }else if (this.isAssociationDataset) {
        list = this.associatedDataset.children
      } else if (datasetId) {
        const params = {
          ids: [datasetId],
          tenantId: this.utils.tenantId,
          boardId: this.authority.authorityBoardId
        }
        this.datasetLoading = true
        const res = await getDataSetTreeByIds(this.api, params, this.aliasDict)
        this.datasetLoading = false
        this.dataSetRes = res || []
        list = res?.[0]?.children || []
      }
      if (this.datasetLoading) {
        this.datasetLoading = false
      }
      this.cloneSelectionList = this.$_deepClone(list)
      list && this.setDragList('fieldList', list)
      if (isClear) {
        this.clearDragField()
        // 清空扩展维度数据
        this.$set(this.chartUserConfig, 'extendDimensionList', [])
        this.$set(this.chartUserConfig, 'extendDimensionCascaderValue', [])
        this.clearExtendDimension()
        // 清除散点图size轴
        if (this.alias === 've-scatter-normal') {
          this.$refs.sizeSetting.clearList()
        }
        this.$set(this.chartUserConfig, 'longitudeList', [])
        this.$set(this.chartUserConfig, 'latitudeList', [])
        this.clearLongitudeLatitude()
        this.clearBandwidthData(true)
        this.$set(this.chartUserConfig, 'hoverDimensionList', [])
        this.$set(this.chartUserConfig, 'hoverMetricList', [])
        this.$set(this.chartUserConfig, 'tooltipFormatterList', [])
        this.clearTooltopField()
        this.cleardimensionDestination()
        this.clearMapprovinceField()
        this.clearAuxLineMetrics()
      }
    },
    clearLongitudeLatitude() {
      this.setDragList('longitudeDragBox', [])
      this.setDragList('latitudeDragBox', [])
    },
    clearMapprovinceField() {
      this.$set(this.chartUserConfig, 'provinceField', null)
    },
    clearTooltopField() {
      Reflect.deleteProperty(this.drillSettings.layers[0], 'hoverDimension')
      Reflect.deleteProperty(this.drillSettings.layers[0], 'hoverMetrics')
    },
    cleardimensionDestination() {
      this.setDragList('destinationDragBox', [])
    },
    clearAuxLineMetrics(){
      Reflect.deleteProperty(this.drillSettings.layers[0], 'auxLineMetrics')
      Reflect.deleteProperty(this.drillSettings.layers[0], 'customAuxLineMetrics')
    },
    setSelectionsList() {},
    // 清空拖拽拦的维度和度量
    clearDragField() {
      this.$refs['dimensionDragBox'] &&
        this.$refs['dimensionDragBox'].clearList()
      const metricTypes = Array.isArray(this.metricTypes)
        ? this.metricTypes.map(metric => metric.type)
        : [this.metricTypes.default.type]
      metricTypes.forEach(type => {
        const metricName = `metric${type}`
        const metricComp = Array.isArray(this.$refs[metricName])
          ? this.$refs[metricName][0]
          : this.$refs[metricName]
        metricComp && metricComp.clearList()
      })
    },
    // 将图形所有配置数据还原
    clearConfigData() {
      const { content } = this.currentEditData
      if (!content) {
        return false
      }
      // 拼图和玫瑰图的elName相同，需要过滤
      const { img } = getChartsList(content.elName).filter(e => {
        return e.alias === content.alias
      })[0]
      this.$set(this.currentEditData.content, 'img', img)
    },
    // 添加地图类型到chartUserConfig，兼容未来地图需求变更(后续删除)
    tempAddMapType(list, config) {
      let mapConfig = {
        heatmapDimensions: [],
        scatterDimensios: []
      }
      list.forEach(item => {
        if (item.MAPTYPE === 'SCATTER') {
          mapConfig.scatterDimensios.push(item)
        } else if (item.MAPTYPE === 'HEATMAP') {
          mapConfig.heatmapDimensions.push(item)
        }
      })
      config.mapConfig = mapConfig
    },
    handleCustomDateDimensionParam() {
      const { chartAlias, dimensionList = [] } = this.chartUserConfig
      const customDateDimension = CUSTOM_DATE_DIMENSION_CHART.includes(
        chartAlias
      )
      let dateDimensionList = dimensionList.filter(d => d.columnTpe === 'date')
      if (
        !this.drillSettings.layers[0].dimension ||
        dateDimensionList.length !== 1
      ) { return }

      this.drillSettings.layers[0].dimension.forEach((item, i) => {
        if (!dimensionList[i]) return
        const { columnTpe, timeDimensionSplittingOffset } = dimensionList[i]
        if (
          customDateDimension &&
          columnTpe === 'date' &&
          timeDimensionSplittingOffset
        ) {
          item.timeDimensionSplittingOffset = timeDimensionSplittingOffset
        } else {
          Reflect.deleteProperty(item, 'timeDimensionSplittingOffset')
        }
      })
    },
    handleCalendarFactorsParam() {
      const drillSettings = this.currentEditData.content.drillSettings
      const { calendarSettings, chartAlias } = this.chartUserConfig
      if (!calendarSettings) return
      const {
        factorList = [],
        isShowMeasurementCalculation,
        calculationDomain
      } = calendarSettings
      if (
        chartAlias !== 've-calendar' ||
        !isShowMeasurementCalculation ||
        !factorList.length
      ) {
        Reflect.deleteProperty(drillSettings.layers[0], 'factors')
        return
      }

      drillSettings.layers[0].factors = this.$_JSONClone(factorList)

      const formatList = {
        number: 'numerical',
        percent: 'percentage',
        currencyUnit: 'currencyUnit'
      }
      drillSettings.layers[0].factors.forEach(item => {
        item.timeDimensionSplittingRule = calculationDomain

        const format = formatList[item.format] || item.format
        item.viewFormat = {
          [format]: {
            decimalNum: item.decimalNum
          }
        }
        Reflect.deleteProperty(item, 'format')
        Reflect.deleteProperty(item, 'decimalNum')
      })
    },
    onLongitudeAndLatitudeChange(params, type) {
      const key = type === 'longitude' ? 'longitudeList' : 'latitudeList'
      this.$set(
        this.chartUserConfig,
        key,
        Array.isArray(params) ? params : params.dragList
      )
      // layers[0].dimension[index] = this.chartUserConfig[key].map(item => Object.assign(item, {
      //   columnName: item.labeName
      // }))
      // if (this.chartUserConfig.longitudeList?.length && this.chartUserConfig.latitudeList?.length) {
      this.previewChart({ from: 'longitudeAndLatitudeChange' })
      // }
    },
    // 维度变更,处理并保存维度,请求数据刷新图形
    onDimensionChange(params, type) {
      // 清除目标值
      // this.isGaugeType && this.clearGauge()
      if (!this.drillSettings) return
      let alias =  this.currentEditData?.content?.alias ? this.currentEditData.content.alias : ''
      const dragArr = Array.isArray(params) ? params : params.dragList
      let needRefresh = true
      if (this.$_isObject(params)) {
        needRefresh = !params.notPreview
      }
      // 初始化地图类型
      dragArr.forEach((v,i) => {
        if (!v['MAPTYPE']) {
          this.$set(v, 'MAPTYPE', 'SCATTER')
        }
        if(alias === 've-bar-Heatmap') {
          this.$set(v, 'DIMENSIONTYPE', v.DIMENSIONTYPE || (type ? type : (i === 0 ? 'y' : 'x')))
        } else {
          this.$set(v, 'DIMENSIONTYPE', i === 0 ? 'y' : 'x')
        }
      })
      let order
      if(alias !=='ve-bar-Heatmap') {
        // 只有维度时第一个维度默认升序
        if (this.dimensionList?.length === 0 && dragArr.length === 1) {
          if (!dragArr[0].order) {
            this.clearStatus({
              type: 'SORT',
              itemType: 'dimension'
            })
          }
          dragArr[0].order = dragArr[0].order || 'asc'
        }
        this.dimensionList = this.$_deepClone(dragArr)
        this.$set(
            this.chartUserConfig,
            'dimensionList',
            this.$_deepClone(dragArr)
          )

        // this.chartUserConfig.dimensionList = this.$_deepClone(dragArr)
        this.tempAddMapType(this.dimensionList, this.chartUserConfig)
        this.$refs.tableInfo.change_dimensionList(this.dimensionList, params)
        // let order
        let dateDimensionList = this.chartUserConfig.dimensionList.filter(
          d => d.columnTpe === 'date'
        )

        const dimension = dragArr.map((e, i) => {
          const ret = Object.assign({}, DIMENSION, {
            MAPTYPE: e.MAPTYPE,
            bindColumnValuePattern:
              e.columnTpe === 'date' ? e.dateFormat : undefined,
            columnType: e.columnTpe,
            columnName: e.labeName,
            alias: e.alias || e.labeName,
            order: !e.order || e.order === 'none' ? '' : e.order,
            orderList: e.orderList,
            // 简单表格需要keyName
            keyName: e.keyName,
            timeDimensionSplittingRule:
              dateDimensionList.length !== 1
                ? undefined
                : [
                    DATE_DIMENSION_TYPE.Day_vs_day,
                    DATE_DIMENSION_TYPE.Day_vs_day_d
                  ].includes(e.timeDimensionSplittingRule)
                ? `customize_${e.timeDimensionSplittingRule}`
                : e.timeDimensionSplittingRule === '_vs_none'
                ? `customize_${DATE_DIMENSION_TYPE.Day_vs_day}`
                : e.timeDimensionSplittingRule,
            presetDimensionLabel: e.presetDimensionLabel ? '1' : '0',
            timeDimensionSwitch: !!e.timeDimensionSwitch,
            trendsDimensionSwitch: !!e.trendsDimensionSwitch,
            eventDimensionSwitch: !!e.eventDimensionSwitch
          })
          if (e.customDimension?.expression) {
            Object.assign(ret, {
              customerExprDim: true,
              exp: e.customDimension.expression
            })
          }
          this.handleMetricGroupField(ret, e)
          if (e.dataSetId) {
            ret.dataSetId = e.dataSetId
          }
          if (
            !order &&
            e.order &&
            e.order !== 'none' &&
            i === dragArr.length - 1
          ) {
            order = e.order
          }
          if (e.columnTpe === 'date' && dateDimensionList.length === 1) {
            const viewFormat = ret.viewFormat || {}
            e.viewFormat?.dateCustomFormat &&
              (viewFormat.dateCustomFormat = e.viewFormat.dateCustomFormat)
            e.viewFormat?.timeCustomFormat &&
              (viewFormat.timeCustomFormat = e.viewFormat.timeCustomFormat)
            Object.keys(viewFormat) && (ret.viewFormat = viewFormat)
          }
          return ret
        })
        this.drillSettings.layers[0].dimension = dimension
        // 双维度时第一个维度默认升序
        if (
          dimension.length === 2 &&
          DOUBLE_DIMENSION_CHART.includes(this.currentEditData.content.alias)
        ) {
          dimension[0].order = dimension[0].order || 'asc'
        }
        this.drillSettings.layers[0].orderList = dragArr.length
          ? dragArr[0].orderList
          : []
        this.$set(
          this.currentEditData.content.chartSettings,
          'dimension',
          dimension.map(e => e.alias || e.columnName)
        )
      } else {
        let _dragArr = this.$_deepClone(dragArr)
        // 应该只能有一个数据
        if (_dragArr.filter((e) => {return e.DIMENSIONTYPE === 'x'}).length > 1) {
          const firstIndex = _dragArr.findIndex((e) => {return e.DIMENSIONTYPE === 'x'})
          _dragArr = _dragArr.filter((item, index) => (item.DIMENSIONTYPE !== 'x' || index === firstIndex))
        }
        if (_dragArr.filter((e) => {return e.DIMENSIONTYPE === 'y'}).length > 1) {
          const firstIndex = _dragArr.findIndex((e) => {return e.DIMENSIONTYPE === 'y'})
          _dragArr = _dragArr.filter((item, index) => (item.DIMENSIONTYPE !== 'y' || index === firstIndex))
        }
        this.dimensionList.forEach(item => {
          item.__checked = true
        })

          if (this.dimensionList?.length === 0 || _dragArr?.length > 1) {
            this.dimensionList = this.$_deepClone(_dragArr)
          } else {
            let _index = this.dimensionList.findIndex(d => d.DIMENSIONTYPE === type)
            if(_index === -1 && type && type === 'x') {
              this.dimensionList.push(..._dragArr)
            } else if(_index === -1 && type && type === 'y') {
              this.dimensionList.unshift(..._dragArr)
            } else if(_index !== -1){
              this.dimensionList.splice(_index, 1, ..._dragArr)
            }
          }
          this.$set(
            this.chartUserConfig,
            'dimensionList',
            this.$_deepClone(this.dimensionList)
          )

          this.tempAddMapType(this.dimensionList, this.chartUserConfig)
          this.$refs.tableInfo.change_dimensionList(this.dimensionList, params)
          let dateDimensionList = this.chartUserConfig.dimensionList.filter(
            d => d.columnTpe === 'date'
          )

        const dimension = this.dimensionList.map((e, i) => {
          const ret = Object.assign({}, DIMENSION, {
            MAPTYPE: e.MAPTYPE,
            bindColumnValuePattern:
              e.columnTpe === 'date' ? e.dateFormat : undefined,
            columnType: e.columnTpe,
            columnName: e.labeName,
            alias: e.alias || e.labeName,
            order: !e.order || e.order === 'none' ? '' : e.order,
            orderList: e.orderList,
            // 简单表格需要keyName
            keyName: e.keyName,
            timeDimensionSplittingRule:
              dateDimensionList.length !== 1
                ? undefined
                : [
                    DATE_DIMENSION_TYPE.Day_vs_day,
                    DATE_DIMENSION_TYPE.Day_vs_day_d
                  ].includes(e.timeDimensionSplittingRule)
                ? `customize_${e.timeDimensionSplittingRule}`
                : e.timeDimensionSplittingRule === '_vs_none'
                ? `customize_${DATE_DIMENSION_TYPE.Day_vs_day}`
                : e.timeDimensionSplittingRule,
            presetDimensionLabel: e.presetDimensionLabel ? '1' : '0',
            timeDimensionSwitch: !!e.timeDimensionSwitch,
            trendsDimensionSwitch: !!e.trendsDimensionSwitch,
            eventDimensionSwitch: !!e.eventDimensionSwitch
          })
          if (e.customDimension?.expression) {
            Object.assign(ret, {
              customerExprDim: true,
              exp: e.customDimension.expression
            })
          }
          this.handleMetricGroupField(ret, e)
          if (e.dataSetId) {
            ret.dataSetId = e.dataSetId
          }
          if (
            !order &&
            e.order &&
            e.order !== 'none' &&
            i === dragArr.length - 1
          ) {
            order = e.order
          }
          if (e.columnTpe === 'date' && dateDimensionList.length === 1) {
            const viewFormat = ret.viewFormat || {}
            e.viewFormat?.dateCustomFormat &&
              (viewFormat.dateCustomFormat = e.viewFormat.dateCustomFormat)
            e.viewFormat?.timeCustomFormat &&
              (viewFormat.timeCustomFormat = e.viewFormat.timeCustomFormat)
            Object.keys(viewFormat) && (ret.viewFormat = viewFormat)
          }
          return ret
        })
        this.drillSettings.layers[0].dimension = dimension
      }

      // 清除维度或度量属性
      order && this._clearMetricOrDimensionStatus('metrics', 'order')
      if (!needRefresh) return
      this.previewChart({ from: 'dimension value change' })
    },
    // 度量变更
    // 度量是可能有多列的
    // 根据当前维度列的type(传入的key)保存到metricsContainer中
    // 同样最后保存并请求数据
    onMetricsChange(params, key) {
      // 图表排序
      const dragArr = Array.isArray(params[0]) ? params[0] : params[0].dragList
      const metricsContainer = this.chartUserConfig.metricsContainer
      this.metricsContainer = metricsContainer
      // const preLen = params[1]
      if (!this.drillSettings) return
      if (key === 'default') {
        this.$set(metricsContainer, key, dragArr)
      } else {
        this.$set(metricsContainer, key, dragArr)
        if (this.chartUserConfig.chartAlias !== 've-scatter-normal') {
          this.$set(
            metricsContainer,
            'default',
            Object.keys(metricsContainer).reduce((prev, next) => {
              if (next !== 'default') {
                prev.push(...this.$_deepClone(metricsContainer[next]))
              }
              return prev
            }, [])
          )
        } else {
          this.setScatterSize()
          const metricsType = ['x', 'y', 'size']
          const metricsContainerDefault = []
          metricsType.forEach(type => {
            metricsContainer[type] &&
              metricsContainerDefault.push(
                ...this.$_deepClone(metricsContainer[type])
              )
          })
          this.$set(metricsContainer, 'default', metricsContainerDefault)
        }
      }
      this.$refs.tableInfo.change_metricsContainer(metricsContainer)
      let order
      const metrics = []
      metricsContainer.default.forEach(e => {
        const ret = this.getMetricItem(e)
        this.handleMetricGroupField(ret, e)
        metrics.push(ret)
      })

      // 货币字段老看板加上企业小数点格式
      this.changeCurrencyField(metrics)

      // 处理散点图中气泡大小度量字段
      this.drillSettings.layers[0].metrics = this.$_deepClone(metrics)
      // 以别名优先
      this.currentEditData.content.chartSettings.metrics = metrics.map(
        e => e.alias || e.columnName
      )
      updatePolymericFilters(this.drillSettings)
      // 清除维度或度量属性
      order && this._clearMetricOrDimensionStatus('dimension', 'order')
      this.isFirstChange(params)
      let needRefresh = true
      if (this.$_isObject(params[0])) {
        needRefresh = !params[0].notPreview
      }
      if (!needRefresh) return
      this.previewChart({ from: 'metric.default.change' })
    },
    // 地图目的地变更
    onMapDestinationChange(params, type) {
      const key = type === 'destination' ? 'dimensionDestinationList' : ''
      this.$set(this.chartUserConfig, key, Array.isArray(params) ? params : params.dragList)
      this.previewChart({ from: 'onMapDestinationChange' })
    },
    getMetricItem(metric, parentObj) {
      const metircItem = this.$_deepClone(metric)
      metircItem.order === 'none' && (metircItem.order = '')
      const result = Object.assign({}, METRICS, metircItem, {
        columnType: metric.columnTpe || 'SUM',
        columnName: metric.labeName || '',
        alias: parentObj
          ? parentObj.customMetricAlias
          : metric.alias || metric.labeName,
        aggType: getAggType(metric),
        compareInfo:
          metric.aggType === 'CONTRAST' && metric.selectedConttast
            ? { compSubType: metric.selectedConttast }
            : {},
        compareRule:
          metric.aggType === 'CONTRAST' && metric.selectedConttastMode
            ? metric.selectedConttastMode
            : '',
        order: !metric.order || metric.order === 'none' ? '' : metric.order,
        orderList: metric.orderList
      })
      const { mergeOthers = {} } = this.chartUserConfig
      if (mergeOthers.isMerge) {
        result.percentValue = true
        this.drillSettings.mergeOtherMetricAlias =
          mergeOthers.mergeRule === 'completionRate'
            ? 'COMPLETION_RATE'
            : mergeOthers.mergeRule === 'dataPercent'
            ? `METRIC_PERCENT_${metric.alias}`
            : metric?.alias || metric?.labeName
      }
      if (metric.dataSetId) {
        result.dataSetId = metric.dataSetId
      }
      setLgeTypeValue(result, metric) // 自定义、业务函数、排序时，需要设置bFxType和lgeType
      return result
    },
    setScatterSize() {
      const {
        chartAlias,
        metricsContainer = {},
        dimensionList = []
      } = this.chartUserConfig
      if (chartAlias !== 've-scatter-normal' || !metricsContainer.y?.length) { return }
      const metricXAlias = this.getDatasetLabel(metricsContainer.x[0])
      const metricYAlias = this.getDatasetLabel(metricsContainer.y[0])
      if (!metricsContainer.size && metricXAlias !== metricYAlias) {
        const sizeItem = this.$_deepClone(metricsContainer.y)

        sizeItem[0].keyName = this.$_generateKeyName(sizeItem[0].labeName, 5)

        if (!sizeItem[0].alias) {
          this.$set(sizeItem[0], 'alias', metricYAlias)
        }
        if (!/SIZE/.test(sizeItem[0].alias)) {
          sizeItem[0].alias += '_SIZE'
        }
        this.$set(metricsContainer, 'size', sizeItem)
      }
      out: for (const metricX of metricsContainer.x) {
        for (const metricY of metricsContainer.y) {
          const metricXAlias = this.getDatasetLabel(metricX)
          const metricYAlias = this.getDatasetLabel(metricY)
          if (
            !metricsContainer.size &&
            metricXAlias &&
            metricXAlias === metricYAlias
          ) {
            this.$message({
              message: `${metricXAlias}：${this.$t('sdp.views.modifyFiled')}`,
              type: 'warning'
            })
          }
          for (const metricSize of metricsContainer.size) {
            const metricSizeAlias = this.getDatasetLabel(metricSize)
            if (metricXAlias && metricXAlias === metricYAlias) {
              // this.$message({
              //   message: `${metricXAlias}：${this.$t('sdp.views.modifyFiled')}`,
              //   type: 'warning',
              //   duration: 5000,
              // })
              break out
            } else if (
              metricSizeAlias === metricXAlias ||
              metricSizeAlias === metricYAlias ||
              dimensionList.map(e => e.alias).includes(metricSizeAlias)
            ) {
              if (!/SIZE/.test(metricSizeAlias)) {
                metricSize.alias = metricSizeAlias + '_SIZE'
              }
            }
          }
        }
      }
    },
    isFirstChange(params) {
      if (this.isFistChangeField) {
        this.$nextTick(() => {
          this.isFistChangeField = false
        })
      } else if (
        !this.isFistChangeField &&
        SUPPORT_DEPUTY_AXIS.includes(this.currentEditData.content.alias) &&
        !Array.isArray(params[0])
      ) {
        !this.chartUserConfig.saveColorSchema && this.changeColor()
      }
    },
    // 对比值变更
    onContrastValueChange(params) {
      const dragArr = Array.isArray(params) ? params : params.dragList
      this.chartUserConfig.contrastList = dragArr
      let order = dragArr[0].order && dragArr[0].order !== 'none'

      // 清除维度或度量属性
      order && this._clearMetricOrDimensionStatus('dimension', 'order')
      this.previewChart({ from: 'contrast value change' })
    },
    // 扩展维度变更
    onExtendDimensionChange(params) {
      if (!this.drillSettings) return
      const dragArr = Array.isArray(params) ? params : params.dragList
      this.chartUserConfig.extendDimensionList = dragArr
      this.$refs.tableInfo.change_extendDimensionList(dragArr, params)

      this.currentEditData.content.chartSettings.extendDimension = dragArr.map(
        e => e.labeName
      )

      this.previewChart({ from: 'extend dimension change' })
    },
    // 获取维度及扩展维度自定义排序值
    getDimensionOrderList() {
      const {
        dimensionList = [],
        extendDimensionList = []
      } = this.chartUserConfig
      let orderList = []
      dimensionList.concat(extendDimensionList).map(item => {
        item.orderList && orderList.push(item.orderList[0])
      })
      return orderList
    },
    handleMetricGroupField(ret, e) {
      if (e.webFieldType) {
        ret.webFieldType = e.webFieldType
        ret.metricGroupInfo = this.getMetricGroupInfo(e)
      }
      return ret
    },
    // 处理目标值
    onTargetChange(params, key = 'aggType') {
      if (!this.drillSettings) return
      if (
        !params ||
        (Array.isArray(params) && !params[0]) ||
        (this.$_isObject(params) && !params?.dragList?.[0])
      ) {
        this.clearGauge()
        return
      }
      const defaults = {}
      params =
        (Array.isArray(params) && params[0]) ||
        (this.$_isObject(params) && params.dragList[0])
      const isAggType = key === 'aggType'
      const _param = this.$_JSONClone(params)
      delete _param.settingGauge
      const _exp =
        params.exp &&
        (params.exp.includes('%')
          ? params.exp.replace('%', '') / 100
          : params.exp)
      Object.assign(defaults, _param, {
        columnName: isAggType ? params.labeName : `GAUGE_TARGET`,
        columnTpe: isAggType ? params.columnTpe : '',
        alias: isAggType ? params.alias : `GAUGE_TARGET`,
        order: isAggType ? params.order || '' : '',
        aggType: isAggType ? getAggType(params) : params.type,
        exp: _exp,
        compareInfo:
          params.aggType === 'CONTRAST' && params.selectedConttast
            ? { compSubType: params.selectedConttast }
            : {},
        compareRule:
          params.aggType === 'CONTRAST' && params.selectedConttastMode
            ? params.selectedConttastMode
            : ''
      })
      if (params?.exp?.includes('%')) {
        const length = params.exp.split('.')[1]?.length
        Object.assign(defaults, {
          viewFormat: {
            percentage: {
              decimalNum: length ? length - 1 : 0
            }
          }
        })
      }
      isAggType && this.handleMetricGroupField(defaults, params)
      setLgeTypeValue(defaults, params) // 自定义、业务函数、排序时，需要设置bFxType和lgeType
      const gaugeTarget = this.gaugeTarget

      Object.assign(gaugeTarget, { defaults })
      this.drillSettings.layers[0].gaugeTarget = gaugeTarget.defaults
      this.$set(this.currentEditData.content.chartSettings, 'target', [
        gaugeTarget.defaults.alias || gaugeTarget.defaults.columnName
      ])
      this.previewChart({ from: 'target value change' })
    },
    // 校验
    codeNumber() {
      const gaugeTarget = this.gaugeTarget
      let val = gaugeTarget.exp.match(/(-?\d+(\.\d+)?%?).*/, '$1')
      val = val ? val[1] : ''
      gaugeTarget.exp = val
      if (!val) return
      this.onTargetChange([gaugeTarget], 'expression')
      // if (number && !(number > 0)) {
      //   return this.$message.warning(this.$t('sdp.views.inputFormatError'))
      // }
    },
    // 处理维度的的自定义排序
    onCustomsort(customArr) {
      // if(this.drillSettings.layers[0].dimension){
      //   this.drillSettings.layers[0].dimension.order = ''
      // }
      // this.drillSettings.layers[0].orderList = this.getDimensionOrderList()
      this.drillSettings.layers[0].orderList = customArr
      this.previewChart({ from: 'custom sort' })
    },
    // 给拖拽拦设置数据
    setDragList(ref, paramList) {
      ref === 'fieldList' &&
        this.fieldList.splice(0, this.fieldList.length, ...paramList)
      const refName = ref === 'fieldList' ? this.datasetType : ref
      const refComponent = Array.isArray(this.$refs[refName])
        ? this.$refs[refName][0]
        : this.$refs[refName]
      if (!refComponent) return
      if (refName === 'AssociateDataset') {
        paramList && refComponent.callChartDataSetMethod(paramList)
      } else {
        refComponent.setList(paramList)
      }
    },
    clearStatus(data) {
      // 清除目标值
      this.$refs['targetDragBox'] &&
        this.$refs['targetDragBox'].clearStatus(data)
      // 清除维度
      this.$refs['dimensionDragBox'].clearStatus(data)
      // 清除对比值
      this.$refs['contrastValueDragBox'] &&
        this.$refs['contrastValueDragBox'].clearStatus(data)
      if (this.alias !== 've-bar-Heatmap' || (this.alias === 've-bar-Heatmap'&& data?.itemType && data.itemType==='dimension')) {
        // 清除度量
        const metricTypes = this.metricTypes
        const mTypes = Array.isArray(metricTypes)
          ? metricTypes
          : Object.keys(metricTypes)
        mTypes.forEach(metric => {
          const type = typeof metric === 'string' ? metric : metric.type
          const metricsBox = this.$refs[`metric${type}`]
          metricsBox.forEach(comp => comp.clearStatus(data))
        })
      }
      if(this.alias === 've-bar-Heatmap'&& data?.itemType && data.itemType==='metric') {
        // 清除热力图维度
        const dimensionTypes = this.dimensionTypes
        const dTypes = Array.isArray(dimensionTypes)
          ? dimensionTypes
          : Object.keys(dimensionTypes)
          dTypes.forEach(d => {
            const dimensionBox = this.$refs[`dimensionDragBox${d.type}`]
            dimensionBox && dimensionBox.forEach(comp => comp.clearStatus(data))
          })
      }
      // 清除散点图size轴
      if (this.alias === 've-scatter-normal') {
        this.$refs.sizeSetting.clearStatus(data)
      }
    },
    // 度量汇总发送请求
    onMeasureConfig() {
      this.previewChart({ from: 'measure config' })
    },
    // 清除目标值数据
    clearTarget() {
      this.$nextTick(() => {
        this.clearGauge()
      })
    },
    // 清除数据
    clearGauge() {
      const gaugeTarget = this.gaugeTarget
      const drillSettings = this.drillSettings
      if (gaugeTarget === undefined) return
      if (drillSettings === undefined) return
      Object.assign(gaugeTarget, { exp: '' })
      delete gaugeTarget.defaults
      this.$set(
        this.chartUserConfig,
        'gaugeTarget',
        this.$_deepClone(gaugeTarget)
      )
      if (!Array.isArray(drillSettings.layers)) return
      this.$delete(drillSettings.layers[0], 'gaugeTarget')
      this.$refs.targetDragBox && this.$refs.targetDragBox.clearList()
    },
    // 检查目标值是否阻止发送请求
    checkGaugeRequest() {
      // 仪表盘
      const gaugeTarget = this.gaugeTarget
      const isGaugeRequest =
        gaugeTarget.type === 'aggType' &&
        gaugeTarget.hasOwnProperty('defaults')
      const isExcRequest =
        gaugeTarget.type === 'expression' && gaugeTarget.exp !== ''
      // 仪表盘请求条件
      const gaugeRequest = this.isGauge
      // 水滴图请求条件
      const dimensionLengthRequired = this.liquidFillMode === VE_LIQUIDFILL_MODE.DIMENSION ? 1 : 0
      const liquidFillRequest = this.isLiquidFill && this.metricsContainer.default?.length === 1 && this.dimensionList.length === dimensionLengthRequired
      if ((liquidFillRequest || gaugeRequest) && (isGaugeRequest || isExcRequest)) {
        this.preventRequest = false
      } else {
        this.isGaugeType && this.clearGauge()
        this.preventRequest = true
      }
    },
    // 阻止本次 Tick 的数据请求
    checkRequest() {
      const metrics = this.metricsContainer.default || []
      const chartAlias = this.chartUserConfig.chartAlias
      // 主题流图必须两个维度，单度量，并且第一个维度为日期类型
      if (chartAlias === 've-themeRiver' && !isThemeRiver(this.chartUserConfig)) {
        this.preventRequest = true
        return
      }
      // 初始化仪表盘默认数据
      // this.clearGauge()
      // 需要双维度图形
      const needTwoDimensionChart = ['ve-sunburst']
      // 维度判断
      const judgeDimension =
        (needTwoDimensionChart.includes(chartAlias) &&
          this.dimensionList.length !== 2) ||
        !this.dimensionList.length
      // 普通图形无维度或无度量时，不请求数据
      const isNormalChartPrevent =
        (!NO_DIMENSION_CHART.includes(chartAlias) && judgeDimension) ||
        !metrics.length
      // 部分图形特殊判断
      // 树图仅存在维度时也需要请求数据
      const treeNotPrevent =
        chartAlias === 've-tree' && this.dimensionList.length
      // 瀑布图存在多个度量时，不拖入维度也需要请求数据
      const waterfallNotPrevent =
        chartAlias === 've-waterfall' && metrics.length > 1 && judgeDimension

      const calendarDimensionDatePrevent =
        this.dimensionList.some(d => d.columnTpe !== 'date') &&
        chartAlias === 've-calendar'
      if (calendarDimensionDatePrevent) {
        if (!this.messageCalendar || this.messageCalendar.closed) {
          this.messageCalendar = this.$message.warning(
            this.$t('sdp.views.calendarDateDimensionTips')
          )
        }
        this.preventRequest = true
        return
      }

      if (isNormalChartPrevent && !treeNotPrevent && !waterfallNotPrevent) {
        this.preventRequest = true
        this.clearConfigData()
      }
    },
    refreshEl(data) {
      const target = this.isMobile
        ? EVENT_DATA_PARAMS.displayPanelMobile
        : EVENT_DATA_PARAMS.displayPanel
      const eventData = new EventData({
        source: 'chartSet',
        target,
        targetFn: 'refreshEl',
        data: data
      })
      this.$emit('eventBus', eventData)
    },
    validateSettings() {
      const { content } = this.currentEditData

      // 元素相关的验证
      const elementFnList = [validateElName].map(fn => fn.bind(this))
      let result = elementFnList.some(fn => fn())
      if (result) return false

      // content里面的验证
      const contentFnList = [
        validateIndicatorAndTargetLabel,
        validateMapSchemeSetting
      ].map(fn => fn.bind(this))
      // 对当前内容进行验证
      result = contentFnList.some(fn => fn(content))
      if (result) return false

      if (content.chioceTab && content.chioceTab.length > 1) {
        // 指标选择器重名，验证不通过
        const chioceNameArray = Array.from(
          new Set(content.chioceTab.map(ct => ct.name))
        )
        if (chioceNameArray.length !== content.chioceTab.length) {
          this.$message.warning(this.$t('sdp.views.indicatorNameDuplicated'))
          return false
        }
        result = content.chioceTab.some((chioce, index) => {
          // 当前指标选择器已经在上面统一验证过了
          if (index === this.indicatorIndex) return
          return contentFnList.some(fn => fn(chioce.saveObj))
        })
      }
      if (result) return false

      if (!SINGLE_METRIC_CHART.includes(this.alias)) return true
      return this.chartSettings.metrics.length <= 1
      // 仪表盘指标值目标值文案必填验证
      function validateIndicatorAndTargetLabel(content) {
        const { gaugeTarget = {}, chartAlias } = content.chartUserConfig
        if (chartAlias !== 've-gauge-normal') return
        if (
          (gaugeTarget.settingGauge?.dataDisplay?.showIndicator &&
            !gaugeTarget.settingGauge.dataDisplay.indicatorLabel?.trim()) ||
          (gaugeTarget.settingGauge?.dataDisplay?.showTarget &&
            !gaugeTarget.settingGauge.dataDisplay.targetLabel?.trim())
        ) {
          this.$message.warning(this.$t('sdp.views.plsFillComplete'))
          return true
        }
      }
      // 地图方案维度和度量必填验证
      function validateMapSchemeSetting(content) {
        let schemeList = content.mapSchemeSetting?.schemeList || []
        if (content.alias !== 've-map-parent' || !schemeList.length) return
        const noValidateScheme = schemeList.find(({ saveObj }) => {
          if (!saveObj) return
          const currentChartUserConfig =
            content.chartUserConfig.childChartAlias ===
            saveObj.chartUserConfig.childChartAlias
              ? content.chartUserConfig
              : saveObj.chartUserConfig
          const { dimensionList = [], metricsContainer = {} } =
            currentChartUserConfig || {}
          return !dimensionList.length || !metricsContainer.default?.length
        })

        if (noValidateScheme) {
          const mapAlias = noValidateScheme.saveObj.chartUserConfig.childChartAlias.replace(
            've-map-',
            ''
          )
          const aliasMap = {
            china: this.$t('sdp.views.mapChina'),
            britain: this.$t('sdp.views.mapBritain'),
            world: this.$t('sdp.views.mapWorld')
          }
          this.$message.warning(
            this.$t('sdp.views.mapSchemeWithoutDimensionAndMetric', {
              prop: aliasMap[mapAlias]
            })
          )
          return true
        }
      }
      // 元素模板名称必填验证
      function validateElName() {
        if (!this.configs || this.configs.type !== 'template') return
        const { elName } = this.currentEditData
        if (!elName.trim()) {
          this.$message.warning(
            this.$t('sdp.message.blankProp', {
              prop: this.onlyIndex ? this.$t('sdp.views.name') : this.$t('sdp.views.chartName')
            })
          )
          return true
        }
      }
    },
    async secondConfirmMessage(message) {
      return this.$sdp_eng_confirm(
        message,
        `${this.$t('sdp.message.tipsConfirmTitle')}`,
        {
          confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
          cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog ' + this.getCurrentThemeClass(),
          type: 'warning'
        }
      )
        .then(res => true)
        .catch(() => {})
    },
    async saveChange() {
      // 保存之前需要校验的设置
      const isSave = this.validateSettings()
      if (!isSave) {
        // TODO 产品还没给提示语句
        return
      }
      const elementWarningData = this.$refs.chartContent.getElementWarningData()
      const {
        changedWarningWebIds,
        changedAlias,
        originWarningList
      } = this.validateWarningDataChange(elementWarningData)
      if (changedAlias) {
        let isSave = await this.secondConfirmMessage(
          this.$t('sdp.views.fieldChangeWarningDataTips', {
            prop: changedAlias
          })
        )
        if (!isSave) return;
        (elementWarningData?.warningList || []).forEach(w => {
          if (changedWarningWebIds.includes(w.webWarningId)) {
            delete w.id
            delete w.subscribeFlag
          }
        })
        originWarningList.forEach(w => {
          w.webWarningId = this.$_generateUUID()
        })
      }

      // 检查全局参数是否被其他卡片交互过
      const isActionByCard = this.checkIsActionByCards()
      if (isActionByCard) {
        let isSave = await this.secondConfirmMessage(this.$t('sdp.message.chartDellobalParamTip'))
        if (!isSave) return
      }

      const { content, id: elId } = this.currentEditData
      // 获取保存前图形数据
      const oldElement = this.elList.find(item => item.id === elId)

      if (!content.drillSettings.timeDimensionSplitting) {
        content.drillSettings.timeDimensionSplitting = '0'
      }
      if (this.currentEditData.content.drillSettings) {
        this.$delete(
          this.currentEditData.content.drillSettings,
          'currentDrillData'
        )
        // this.$delete(this.currentEditData.content.drillSettings, 'banDrill')
      }
      this.$refs.chartContent.saveElementChart()

      if (content?.mapSchemeSetting?.schemeList?.length) {
        this.$refs.tableInfo.saveCurrentScheme()
      }

      // 保存图形使用数据集
      const dataset = this.isAssociationDataset
        ? this.selectedDataset.concat([this.associatedDataset])
        : this.selectedDataset
      this.$set(this.currentEditData.content, 'dataset', dataset)
      if (content.chioceTab && content.chioceTab.length > 1) {
        content.chioceTab.forEach(tab => {
          tab.saveObj.dataset = dataset
        })
      }

      // 不支持维度超链接的图形或设置了地图方案 需要进行二次确认
      const superLinkOptions = content.superLinkOptions || []
      const superLinkDimension = superLinkOptions.find(
        item => item.labelBoard.type === SUPERLINK_CONST_TYPE.variavle
      )
      const hasMapSchemeSetting = content.mapSchemeSetting?.schemeList?.length
      const oldAlias = oldElement?.content?.alias
      // 其他图形切换至简单表格
      const isSwitchToSimpleGrid =
        oldAlias !== 've-grid-normal' && content.alias === 've-grid-normal'
      // 简单表格切换至其他图形
      const isSwitchToOtherChart =
        oldAlias === 've-grid-normal' && content.alias !== 've-grid-normal'

      // 保存当前配置到当前索引的指标选择器
      if (content.chioceTab && content.chioceTab.length > 1) {
        this.$refs.tableInfo.saveDimension(this.indicatorIndex)
        this.saveContent(elementWarningData)
      } else if (superLinkDimension && (hasMapSchemeSetting || isSwitchToSimpleGrid || isSwitchToOtherChart)) {
        let message = this.$t('sdp.views.chartDisableDimensionHyperlink')
        if (hasMapSchemeSetting) { message = this.$t('sdp.views.mapSchemeDisabledDimensionHyperlink') }
        if (isSwitchToSimpleGrid) { message = this.$t('sdp.views.switchToGridDisabledHyperlink') }
        if (isSwitchToOtherChart) { message = this.$t('sdp.views.swtichChartDisabledHyperlink') }

        let isSave = await this.secondConfirmMessage(message)
        if (!isSave) return
        deleteDimensionSuperLink(this.currentEditData.content, superLinkOptions)
        // 设置了地图方案需要清空schemeList内的superLinkOptions
        if (hasMapSchemeSetting) {
          content.mapSchemeSetting.schemeList.forEach(item => {
            deleteDimensionSuperLink(
              item.saveObj,
              item.saveObj.superLinkOptions
            )
          })
        }
        this.saveContent(elementWarningData)
      } else {
        this.saveContent(elementWarningData)
      }

      function deleteDimensionSuperLink(content, superLinkOptions) {
        if (!superLinkOptions) return
        const titleSuperLink = superLinkOptions.filter(
          item => item.labelBoard.type !== SUPERLINK_CONST_TYPE.variavle
        )
        titleSuperLink.length
          ? (content.superLinkOptions = titleSuperLink)
          : Reflect.deleteProperty(content, 'superLinkOptions')
      }
    },
    // 保存配置
    async saveContent(elementWarningData) {
      // 图形配置有变化需要清空的一些配置
      // changeBoardTreeData()
      const { clearLanguage = [], isClearInteractionOptions } =
        this.configs.type === 'template' ? {} : this.changeDeleteSetting()

      if (isClearInteractionOptions && this.currentEditData.content?.interactionOptions) {
        const bool = await this.$sdp_eng_confirm(`${this.$t('sdp.views.FieldInteractionTip')}`, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
          confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
          cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
          closeOnHashChange: false,
          type: 'warning',
          closeOnClickModal: false,
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog',
        }).then(() => {
          Reflect.deleteProperty(this.currentEditData.content, 'interactionOptions')
          return true
        }).catch(() => {
          return false
        })

        if (!bool) return
      }

      // 仪表盘提示用户输入目标值
      if (this.isGaugeType) {
        const isType = this.gaugeTarget.type === 'expression'
        if (isType && !this.gaugeTarget.exp) { return this.$message.warning(this.$t('sdp.views.PleaseInputDial')) }
      }
      // 不是带宽图清空数据
      if (!this.isBandwidth) {
        this.clearBandwidthData(false)
      }
      setTimeout(() => {
        const currentEditData = this.$_deepClone(this.currentEditData)
        const content = currentEditData.content
        const schemeList = content.mapSchemeSetting?.schemeList || []
        if (
          content.chioceTab?.length &&
          schemeList.length &&
          content.chartUserConfig.childChartAlias !== 've-map-world'
        ) {
          this.$_filterDeepClone(
            content,
            content.chioceTab[content.saveIndex].saveObj,
            [
              'chioceTab',
              'saveIndex',
              'indicatorSelectorShowType',
              'indicatorSelectorShow',
              'superLinkOptions',
              'interactionOptions'
            ]
          )
          content.mapSchemeSetting = content.chioceTab[content.saveIndex]
            .saveObj.mapSchemeSetting
            ? this.$_deepClone(
                content.chioceTab[content.saveIndex].saveObj.mapSchemeSetting
              )
            : undefined
        }
        content.chartUserConfig.updateTimestamp = Date.now()
        const numberPage = this.isMobilePageImg ? 100 : 9999
        // 保存之前清除设计时样例数据
        content.drillSettings.pageInfo.pageSize = content.chartUserConfig
          .pagination.rankOf
          ? content.chartUserConfig.pagination.pageSize
          : numberPage

        const baseMap =
          content.chartUserConfig.childChartAlias?.replace('ve-map-', '') ||
          'china'
        content.chartSettings.position = baseMap

        // 更新看板设计界面日期控件选中值
        const dateDimension =
          Array.isArray(this.chartUserConfig.dimensionList) &&
          this.chartUserConfig.dimensionList.find(
            item => item.columnTpe === 'date'
          )
        if (dateDimension) {
          const selectedDateVal = dateDimension.timeDimensionSplittingRule
          content.chartUserConfig.selectedDateVal = selectedDateVal
          // 处理指标选择器
          if (content.chioceTab && content.chioceTab.length > 1) {
            content.chioceTab.forEach(tab => {
              tab.saveObj.chartUserConfig.selectedDateVal = selectedDateVal
            })
          }
        }

        // this.MobilePageImg()
        // 保存主题相关配置
        this.saveThemeConfig(content)
        this.isFirstDataSet = false
        const eventData = new EventData({
          target: ['displayPanel', 'displayPanelMobile', 'chartTemplateDesign'],
          targetFn: 'saveEditLanguage',
          type: 'Language',
          data: {},
        })
        this.$emit('eventBus', eventData)
        this.$emit('on-save', currentEditData, this.$refs.chartContent)

        console.log('追踪，往上传', (elementWarningData))
        this.sdpBus.$emit(
          EVENT_BUS.CHANGE_ELEMENT_WARNING_DATA,
          this.currentEditData.id,
          { elementWarningData }
        )

        if (content.chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION) {
          let idTemp = this.currentEditData.id
          if (this.currentEditData.content.chioceTab) {
            idTemp = `${this.currentEditData.id}_${this.currentEditData.content.saveIndex}`
          }
          this.sdpBus.$emit(EVENT_BUS.DECOMPOSITION, idTemp, 'renderInitDefaultFlag')
        }

        if (this.configs && this.configs.type === 'template') return
        if (content.chartUserConfig.chartAlias !== 've-grid-normal') {
          this.refreshEl({
            ids: [this.currentEditData.id]
          })
        }
        // 清空多语言数据
        if (clearLanguage.length) {
          const eventData = new EventData({
            source: 'chartSet',
            target: EVENT_DATA_PARAMS.displayPanel,
            targetFn: 'updateLanguageData'
          })
          this.$emit('eventBus', eventData)
        }
        // 8697：如果删除了被卡片交互的指标，清理掉数据
        const indicatorDelKeys = Object.keys(this.interactionReferencedInfo.delIndicators)
        if (indicatorDelKeys.length) {
          indicatorDelKeys.forEach(key => {
            const elIds = this.interactionReferencedInfo.delIndicators[key] || []
            elIds.forEach(eid => {
              const el = this.elList.find(item => item.id === eid)
              ;(el?.content?.interactionOptions || []).forEach(opt => {
                if (opt.id === eid && opt.indicators?.length && opt.associElements[0]?.id === this.elId) {
                  this.$set(opt, 'indicators', opt.indicators.filter(i => i !== key))
                }
              })
              if (el?.content?.interactionOptions) {
                el.content.interactionOptions = el.content.interactionOptions.filter(item => {
                  return item.actionType !== 'indicator' || item?.indicators?.length
                })
              }
            })
          })
        }
        // 8697：如果删除了全局参数
        const interKeys = Object.keys(this.interactionReferencedInfo.actions)
        const globalParamIds = this.$refs.chartContent?.getElementGlobalParamIdList ? this.$refs.chartContent.getElementGlobalParamIdList() : []
        interKeys.forEach(eid => {
          const el = this.elList.find(item => item.id === eid)
          ;(el?.content?.interactionOptions || []).forEach(opt => {
            if (opt.id === eid && opt?.selectGranularity?.length && opt.associElements[0]?.id === this.elId) {
              let delIds = []
              opt.selectGranularity.forEach(gran => {
                if (!globalParamIds.includes(gran.id)) {
                  delIds.push(gran.id)
                }
              })
              const _selectGranularity = opt.selectGranularity.filter(gran => !delIds.includes(gran.id))
              this.$set(opt, 'selectGranularity', _selectGranularity)
            }
          })
          if (el?.content?.interactionOptions) {
            el.content.interactionOptions = el.content.interactionOptions.filter(item => {
              return item.actionType !== 'params' || item?.selectGranularity?.length
            })
          }
        })

        this.closeDialog()
      }, 50)
    },
    validateWarningDataChange(elementWarningData) {
      // 改了就返回字段名
      if (!this.boardWarningSubscribeData) return false
      const originWarningData = this.boardWarningSubscribeData.originWarningList.find(
        o => o.webElementId === this.currentEditData.id
      )
      const originWarningList = this.$_deepClone(
        originWarningData?.warningList || []
      )
      let elementWarningList = elementWarningData?.warningList || []

      const fieldKeys = [
        'aggType',
        'alias',
        'displayExpression',
        'exp',
        'lgeType',
        'type',
        'selectedConttast',
        'selectedConttastMode',
        'addup'
      ]
      let changedAlias = ''

      const originWarningIds = originWarningList
        .filter(w =>
          this.boardWarningSubscribeData.isWarningItemDisabled(
            w.webWarningId,
            this.currentEditData.id
          )
        )
        .map(w => w.webWarningId)
      const elementWarningIds = elementWarningList.map(w => w.webWarningId)
      // 如果原先没有已订阅的预警，或者现在没有预警
      if (!originWarningIds.length || !elementWarningIds.length) return false
      const changedWarningWebIds = originWarningIds.filter(
        (webWarningId, warnIndex) => {
          if (!elementWarningIds.includes(webWarningId)) return
          const originRule =
            typeof originWarningList[warnIndex].ruleParameter === 'string'
              ? JSON.parse(originWarningList[warnIndex].ruleParameter)
              : originWarningList[warnIndex].ruleParameter
          const elementWarningItem = elementWarningList.find(
            w => w.webWarningId === webWarningId
          )
          const elementRule =
            typeof elementWarningItem.ruleParameter === 'string'
              ? JSON.parse(elementWarningItem.ruleParameter)
              : elementWarningItem.ruleParameter
          const originBeComparedObject = originRule.beComparedObject
          const elementBeComparedObject = elementRule.beComparedObject
          // 先看被比较字段有没有改动过
          if (
            fieldKeys.some(k => {
              // 如果一个有一个没有，说明一定改过
              if (
                Boolean(originBeComparedObject[k]) !==
                Boolean(elementBeComparedObject[k])
              ) { return true }
              // 如果两个都没有，说明没改过
              if (!originBeComparedObject[k]) return
              return (
                String(originBeComparedObject[k]) !==
                String(elementBeComparedObject[k])
              )
            })
          ) {
            if (!changedAlias) {
              const { parentId, labeName } = elementBeComparedObject
              changedAlias =
                elementBeComparedObject.alias ||
                this.getUnknownName(parentId, labeName)
            }
            return true
          }
          if (Number(elementRule.compareMethod) !== 4) return
          // 如果是指定维度值指标预警，还得看维度改了没
          const originDimensionSetting = originRule.dimensionSetting || []
          const elementDimensionSetting = elementRule.dimensionSetting || []
          const changedDimension = elementDimensionSetting.find(
            (ds, dsIndex) =>
              ds.fieldAlias !== originDimensionSetting[dsIndex].fieldAlias
          )
          if (changedDimension) {
            if (!changedAlias) {
              const {
                dataSetId: parentId,
                columnName: labeName
              } = changedDimension.matchField
              changedAlias =
                changedDimension.fieldAlias ||
                this.getUnknownName(parentId, labeName)
            }
            return true
          }
        }
      )

      return {
        changedWarningWebIds,
        originWarningList: originWarningData.warningList.filter(w =>
          changedWarningWebIds.includes(w.webWarningId)
        ),
        changedAlias
      }
    },
    // 保存主题设置
    saveThemeConfig(content) {
      let dateDimensionColor = {
        color: content?.dateDimensionStyle?.color || ''
      }
      if (content.chioceTab && content.chioceTab.length) {
        content.chioceTab.forEach(ct => {
          this.resaveDimensionColors(ct.saveObj)
          // setThemeChartUserConfig(ct.saveObj.chartUserConfig, 'sdp-classic-white', content)
        })
        Object.assign(
          content.chartUserConfig,
          content.chioceTab[content.saveIndex].saveObj.chartUserConfig
        )
        content.chartData.rows = []
        delete content.chartResponse
      } else {
        const { chartUserConfig } = content
        setThemeConfig(chartUserConfig, this.themeType)
        Object.assign(chartUserConfig.themeConfig[this.themeType], {
          dateDimensionColor
        })
        this.resaveDimensionColors(content)
        // setThemeChartUserConfig(content.chartUserConfig, 'sdp-classic-white', content)
      }
      setThemeConfig(content, this.themeType, 'content')
    },
    resaveDimensionColors(content) {
      const { chartUserConfig, chartData } = content
      const {
        dimensionColors = [],
        colorType,
        chartAlias,
        pieSetting = {},
        dimensionList = [],
        wordCloudSetting = {}
      } = chartUserConfig
      const { rows = [] } = chartData
      let rowsData = Array.isArray(rows)
        ? rows
        : Object.keys(rows).map(k => ({ dimensionName: k }))
      if (pieSetting.showCombinePie) {
        const othersPieData =
          rows.find(
            r =>
              r.hasOwnProperty('others') &&
              Array.isArray(r.others) &&
              r.others.length
          )?.others || []
        rowsData = rowsData.concat(othersPieData)
      }
      const dimensionIndex = chartAlias === 've-themeRiver' ? 1 : 0
      let dimensionKey =
        rowsData.length && dimensionList.length
          ? `VIEWFORMAT_${dimensionList[dimensionIndex].alias ||
              dimensionList[dimensionIndex].labeName}`
          : ''
      if (chartAlias === 've-scatter-normal') {
        dimensionKey = 'dimensionName'
      } else if (chartAlias === 've-wordcloud' && wordCloudSetting.colorFieldKey) {
        dimensionKey = wordCloudSetting.colorSeriesProp
      }
      const usedDimensionColors =
        colorType === 'metric'
          ? []
          : dimensionColors.filter(dc =>
              rowsData.find(r => r[dimensionKey] === dc.name)
            )
      Object.assign(chartUserConfig, { dimensionColors: usedDimensionColors })
      content.chartData.rows = []
      delete content.chartResponse
    },
    closeDialog() {
      const eventData = new EventData({
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'cleanEditLanguage',
        type: 'Language',
        data: {},
      });
      this.$emit('eventBus', eventData);
      this.$emit('on-back')
    },
    tableInfoChangeHandler(key, params) {
      if (key === 'animationSetting') {
        this.handSetAnimationStatus({ source: 'changeConfig' })
      }
    },
    handSetAnimationStatus({ source, chartData, delay }) {
      this.$refs.chartContent.animationPlay({ type: 'init' })
      this.$refs.chartContent.histogramAnimation({
        type: 'init',
        source,
        chartData,
        delay
      })
    },
    _changeColor(color) {
      // chartUserConfig.colors
      const colors = typeof color === 'string' ? [color] : color
      this.$set(this.chartUserConfig, 'colors', colors)
    },
    _clearMetricOrDimensionStatus(type, prop) {
      const layers = this.drillSettings.layers
      const fields = layers[0][type]
      fields.forEach((e, i) => {
        const doubleNotDelete =
          type === 'dimension' && fields.length === 2 && !i
        if (doubleNotDelete) return
        this.$delete(e, prop)
      })
      const chartUserConfig = this.chartUserConfig
      if (type === 'dimension') {
        chartUserConfig.dimensionList.forEach((item, i) => {
          if (chartUserConfig.dimensionList.length === 2 && i) {
            this.$delete(item, prop)
          }
        })
      } else {
        const metrics = this.drillSettings.layers[0].metrics
        chartUserConfig.metricsContainer.default.forEach(item => {
          metrics.some(eve => {
            if (eve.alias === item.alias) {
              this.$delete(item, prop)
            }
            return eve.alias === item.alias
          })
        })
      }
    },
    initDeputyFlag() {
      if (
        SPECIAL_SUPPORT_DEPUTY_AXIS.includes(this.chartUserConfig.chartAlias)
      ) {
        this.setIsShowDeputyAxis(
          !!this.chartUserConfig.metricsContainer.line?.length
        )
      }
    },
    setIsShowDeputyAxis(value) {
      this.isShowDeputyAxis = value
      if (this.selfChartDesignHandler) {
        this.selfChartDesignHandler.reset({ isShowDeputyAxis: value })
      }
    },
    // 切换地图方案
    mapSchemeChange(index) {
      this.initChart = true
      const dimensionDragBox = this.$refs.dimensionDragBox
      dimensionDragBox && (dimensionDragBox.notNeedValidate = true)

      resetDragBox.call(this)
      function resetDragBox() {
        Object.keys(this.$refs).forEach(refKey => {
          const refArray = Array.isArray(this.$refs[refKey])
            ? this.$refs[refKey]
            : [this.$refs[refKey]]
          refArray.forEach(r => {
            if (!r) return
            console.log('ref $options', r.$options)
            if (['Drag_Box', 'DragBox'].includes(r?.$options?.name)) {
              r.restoreChioceTabDimension(index, 'mapSchemeSetting')
            }
          })
        })
      }

      this.$nextTick(() => {
        const newContent = this.currentEditData.content.mapSchemeSetting
          .schemeList[index].saveObj
        setThemeChartUserConfig.call(
          this,
          newContent.chartUserConfig,
          this.themeType,
          newContent
        )
        newContent.chartUserConfig.title = this.currentEditData.content.mapSchemeSetting.schemeList[0].saveObj.chartUserConfig.title
        newContent.chartConfig.title = this.currentEditData.content.mapSchemeSetting.schemeList[0].saveObj.chartConfig.title
        this.$_filterDeepClone(this.currentEditData.content, newContent)

        this.getSelectionsList(
          this.currentEditData.content.drillSettings.dataSetId
        )

        const {
          metricsContainer,
          extendDimensionList
        } = this.currentEditData.content.chartUserConfig
        // 堆叠条形图默认显示维度扩展拖拽框
        this.extendDragBoxShow = !!(
          extendDimensionList && extendDimensionList.length
        )
        this.metricsContainer = metricsContainer

        this.setMetricBox()

        // 切换指标选择器初始化维度度量
        this.handleChioceTabDimensionMetric('initCascader')
        // 初始化tableInfo
        this.$refs.tableInfo.initDialogTableInfo()

        // 恢复目标值
        this.initTargetBox()
        this.initColorSetting()

        this.initPageSize()
        if (
          this.currentEditData.content.mapSchemeSetting.schemeList.length <= 1
        ) {
          this.$nextTick(() => {
            this.$delete(this.currentEditData.content, 'mapSchemeSetting')
          })
        }
        this.previewChart({ firstRender: true, from: 'mapSchemeChange' })
      })
    },
    // MARK:切换指标选择器事件
    initMetricDimension(index) {
      this.initChart = true
      // 恢复维度度量的显示
      // 切换指标选择器不需要校验
      const dimensionDragBox = this.$refs.dimensionDragBox
      dimensionDragBox && (dimensionDragBox.notNeedValidate = true)
      this.indicatorIndex = index
      // 恢复图表配置
      this.$nextTick(() => {
        this.$_filterDeepClone(
          this.currentEditData.content,
          this.currentEditData.content.chioceTab[index].saveObj,
          [
            'chioceTab',
            'saveIndex',
            'indicatorSelectorShowType',
            'indicatorSelectorShow',
            'superLinkOptions',
            'interactionOptions',
            'contentStyle',
            `themeConfig`,
            'dateDimensionStyle',
            `chartUserConfig.themeConfig.${this.themeType}.dateDimensionColor`
          ]
        )
        this.currentEditData.content.mapSchemeSetting = this.currentEditData
          .content.chioceTab[index].saveObj.mapSchemeSetting
          ? this.$_deepClone(
              this.currentEditData.content.chioceTab[index].saveObj
                .mapSchemeSetting
            )
          : undefined
        const {
          dimensionList,
          metricsContainer,
          extendDimensionList
        } = this.currentEditData.content.chartUserConfig
        const { sizeSetting = {}, chartMetricTypeSelect = [] } = this.$refs
        // 堆叠条形图默认显示维度扩展拖拽框
        this.extendDragBoxShow = !!(
          extendDimensionList && extendDimensionList.length
        )
        this.metricsContainer = metricsContainer
        this.dimensionList = dimensionList
        this.initDeputyFlag()

        intoScatterCombination(this, this.chartUserConfig.chartAlias)
        this.setMetricBox()

        // 切换指标选择器初始化维度度量
        this.handleChioceTabDimensionMetric('initCascader')
        // 初始化tableInfo
        this.$refs.tableInfo.initDialogTableInfo()

        // 恢复目标值
        this.initTargetBox()
        this.initColorSetting()

        this.initPageSize()

        dimensionDragBox && (dimensionDragBox.notNeedValidate = false)

        if (this.currentEditData.content.chioceTab.length === 1) {
          this.currentEditData.content.chioceTab = []
          this.currentEditData.content.saveIndex = 0
        }
        try {
          if (this.alias === 've-scatter-normal') {
            sizeSetting.initCascader()
          }
          if (this.alias === 've-composite') {
            this.$nextTick().then(() => {
              chartMetricTypeSelect.length &&
                chartMetricTypeSelect.forEach(item => {
                  item.initData()
                })
            })
          }
          if(this.alias === 've-bar-Heatmap') {
              if (this.dimensionList && this.dimensionList.length) {
                setTimeout(() => {
                  this.dimensionList.forEach((d) => {
                      this.setDragList(`dimensionDragBox${d.DIMENSIONTYPE}`, [d])
                    })
                    this.setDragList('dimensionDragBox', this.dimensionList)
                }, 200)
              }
          }
        } catch (e) {
          console.warn(e)
        }
        this.previewChart({ firstRender: true, from: 'innerIndicatorChange' })
      })
    },
    // 指标选择器排序，恢复维度、度量
    sortChioceTab() {
      const saveIndex = this.currentEditData.content.saveIndex
      this.indicatorIndex = saveIndex
    },
    // 处理指标选择器维度度量(初始化、恢复)
    handleChioceTabDimensionMetric(name, data) {
      const {
        dimensionDragBox,
        extendDimensionDragBox,
        longitudeDragBox,
        latitudeDragBox,
        contrastValueDragBox
      } = this.$refs
      dimensionDragBox[name](data)
      extendDimensionDragBox[name](data)
      if (this.isMap) {
        // 经纬度初始化
        longitudeDragBox && longitudeDragBox[name](data)
        latitudeDragBox && latitudeDragBox[name](data)
      }
      contrastValueDragBox && contrastValueDragBox[name](data)
      const metricTypes = this.metricTypes
      const mTypes = Array.isArray(metricTypes)
        ? metricTypes
        : Object.keys(metricTypes)
      mTypes.forEach(metric => {
        const type = typeof metric === 'string' ? metric : metric.type
        const metricsBox = this.$refs[`metric${type}`]
        metricsBox.forEach(comp => comp[name](data))
      })
    },
    // 初始化漏斗图数据
    initFunnel() {
      if (this.chartUserConfig.chartAlias !== 've-funnel') return
      if (!this.chartUserConfig.funnelSettings) {
        this.$set(this.chartUserConfig, 'funnelSettings', {
          // 转换率计算方式
          conversionRateAlgorithm: 'previousPercent',
          // 转换率描述
          converesionRateDescribe: this.$t('sdp.views.conversionRate'),
          // 转换率显示设置
          converesionRateShow: true,
          // 漏斗图外部标签名称显示设置
          funnelLabelShow: true
        })
      }
      // 漏斗图外部标签初始化
      if (!this.chartUserConfig.funnelLabelConfig) {
        const defaultConfig = getThemeConfig(this.themeType, {
          attributes: ['funnelLabelConfig']
        }).funnelLabelConfig
        this.$set(this.chartUserConfig, 'funnelLabelConfig', defaultConfig)
      }
    },
    // 打开图形配置页时的操作
    initChartSettings() {
      this.initPageSize()
      const {
        tableInfo,
        chartMetricTypeSelect = [],
        chartContent
      } = this.$refs

      if (!['china', 'world'].includes(this.chartSettings.position)) {
        chartContent && chartContent.doRegisterMap()
      }

      // 每次打开看板，判断设计时样例数据
      const content = this.currentEditData.content
      this.indicatorIndex = content.saveIndex || 0
      // 主题颜色替换
      // this.getThemefromContent(this.currentEditData.content)
      tableInfo.initDialogTableInfo()
      this.initColorSetting()
      chartMetricTypeSelect.length &&
        chartMetricTypeSelect.forEach(item => {
          item.initData()
        })
    },
    initPageSize(val = this.currentEditData.content) {
      const rankOf = val ? val.chartUserConfig.pagination.rankOf : false
      val.drillSettings.pageInfo.pageSize = rankOf
        ? this.chartUserConfig.pagination.pageSize
        : 100
    },
    // updateColorNameKey (value) {
    //   this.colorNameKey = value
    // },
    // 移动看板的部分图形分页请求
    MobilePageImg(currentPageSize) {
      if (this.isMobilePageImg) {
        Object.assign(this.drillSettings.pageInfo, {
          page: 1,
          pageSize: currentPageSize || 100
        })
      }
    },
    // 货币字段老看板加上企业小数点格式
    changeCurrencyField(metrics) {
      metrics.forEach(item => {
        if (item.columnName.includes('CURRENCY') && item.viewFormat) {
          const key = Object.keys(item.viewFormat)[0]
          const arr = ['percentage']
          if (arr.includes(key)) {
            item.viewFormat[key].grayed = true
          }
        }
      })
    },
    changeDeleteSetting() {
      const { content, id: elId } = this.currentEditData
      // 维度、度量别名变化后，清空多语言数据
      const currentEl = this.elList.find(item => item.id === elId)
      const {
        metricsContainer = {},
        dimensionList = []
      } = currentEl.content.chartUserConfig

      let clearLanguage = []
      const saveIndex =
        content.chioceTab && content.chioceTab.length ? content.saveIndex : 'x'
      // 散点图key匹配规则不同，需按照编辑前图形类型 拼接key
      Array.isArray(this.metricList) &&
        this.metricList.map((newMetric, index) => {
          // 通过labeName获取改变前别名
          const oldAlias =
            metricsContainer.default &&
            metricsContainer.default.find(
              oldMetric => oldMetric.labeName === newMetric.labeName
            )
          if (oldAlias && newMetric.alias !== oldAlias.alias) {
            const type =
              currentEl.content.alias === 've-scatter-normal'
                ? 'columnsAlias'
                : 'metricsAlias'
            const key = `${elId}_${index}_${saveIndex}_${type}`
            clearLanguage.push(key)
          }
        })
      this.dimensionList.map((newDimension, index) => {
        const oldAlias = dimensionList.find(
          oldDimension => oldDimension.labeName === newDimension.labeName
        )
        if (oldAlias && newDimension.alias !== oldAlias.alias) {
          const type =
            currentEl.content.alias === 've-scatter-normal'
              ? 'columnsAlias'
              : 'dimensionAlias'
          const key = `${elId}_${index}_${saveIndex}_${type}`
          clearLanguage.push(key)
        }
      })

      // 图形维度变更后，清除交互设置
      const isChangeDimension = this.dimensionList.map(e => e.labeName).toString() !== dimensionList.map(e => e.labeName).toString()
      console.log(isChangeDimension, content.chioceTab, content.saveIndex)
      let isClearInteractionOptions = false
      if (isChangeDimension) {
        if (content.chioceTab?.length && content.interactionOptions?.length) {
          const tabId = content.chioceTab[content.saveIndex].id
          let newOptions = content.interactionOptions.filter(opt => opt.chioceTabId !== tabId)
          if (newOptions?.length) {
            this.$set(this.currentEditData.content, 'interactionOptions', newOptions)
          } else {
            isClearInteractionOptions = true
          }
        } else {
          isClearInteractionOptions = true
        }
      }
      if (content.interactionOptions && Array.isArray(content.interactionOptions) && content.interactionOptions.length) {
        content.interactionOptions.map((interaction, index) => {
          if (!interaction.site) {
            interaction.site =
              content.chartUserConfig.dimensionList[index].keyName
          }
        })
      }
      // 切换了数据集需要清空交互了该元素的对应的配置
      if (
        content.drillSettings.dataSetId !==
        currentEl.content.drillSettings.dataSetId
      ) {
        this.elList.map(elItem => {
          if (
            elItem.content.interactionOptions &&
            Array.isArray(elItem.content.interactionOptions) &&
            elItem.content.interactionOptions.length
          ) {
            elItem.content.interactionOptions.map(interaction => {
              interaction.associElements = interaction.associElements.filter(
                a => a.id !== elId
              )
            })
            elItem.content.interactionOptions = elItem.content.interactionOptions.filter(
              interaction => interaction.associElements.length > 0
            )
          }
          if (
            elItem.content.interactionOptions &&
            !elItem.content.interactionOptions.length
          ) {
            delete elItem.content.interactionOptions
          }
        })
      }

      return { clearLanguage, isClearInteractionOptions }
    },
    doRegisterMap(param) {
      if (!this.$refs.chartContent) return
      const chartContent = this.$refs.chartContent
      chartContent.doRegisterMap()
      chartContent.getLanguageProvice(param)
    },
    // ----------------分割线---------------------------------------
    async getElResponse(el, notNeedResponseChartData, checkObj = {}) {
      this.isPending = true
      const { response, requestKeyFn, request } = bridge.adapter
      const param = {
        languageCode: '',
        currency: '',
        tenantId: this.utils.tenantId,
        origin: el?.content?.drillSettings?.sourceDataType === SOURCE_DATA_TYPE.indexFlag ? 'index' : undefined
      }
      // 获得元素的参数
      const paramItem = this.$_deepClone(
        request.call(this.$refs.chartContent, el, {
          isChartSet: true,
          compareMethod: checkObj.compareMethod
        })
      )
      // 清除图表编辑的钻取
      paramItem.drillDimensions = []
      // 获得元素对应 param 上的 key
      const key = requestKeyFn()
      if (Object.keys(paramItem).includes('calendarComponent')) {
        param['calendarComponents'] = param['calendarComponents'] || []
        param['calendarComponents'].push(paramItem.calendarComponent)
        param.calendarGroupComp = paramItem.calendarGroupComp
        param[key] = param[key] || []
        param[key].push(paramItem.drillSettings)
      } else {
        param[key] = param[key] || []
        param[key].push(paramItem)
      }

      // 添加操作日志字段
      const { id, elName: name } = this.currentEditData
      const { isSubscribe, isRemind, isMobileApp } = this.commonData || {}
      const { isLargeScreen, isDataReport } = this.utils || {}
      param.reportLog = updateReportLogObj({
        id,
        name,
        isSubscribe,
        isRemind,
        isMobileApp,
        isLargeScreen,
        isDataReport,
        isMobile: this.isMobile,
        type: 'chart',
        langCode: this.langCode
      })
      this.$refs.chartContent.requestData = param.chartElements[0].layers[0]
      param.chartElements[0].legendsHidden = checkObj.legendsHidden || void 0

      const _responseParam = await chartPreview(this.api, param)
      
      // 检查下线指标提示
      this.checkOfflineMetrics(_responseParam)
      
      this.isPending = false
      // 初始化完成后，图形初始化标识置为false
      this.initChart && (this.initChart = false)
      const responseParam = this.onlyIndex ? _responseParam?.data : _responseParam
      if (!responseParam) {
        // 87924 请求失败场景，设置图表加载状态为false，并设置图表数据为空
        this.$refs.chartContent?.setElementLoadState(false)
        this.$refs.chartContent?.setHasChartData(false)
        return false
      }
      return notNeedResponseChartData
        ? responseParam.chartElements[0]
        : this.$refs.chartContent?.responseAdapter(
            responseParam.chartElements[0]
          )
    },
    previewHandler(previewCode = '') {
      this.$refs.chartContent.saveElementChart()
      if (this.currentEditData.content.chioceTab?.length) {
        this.$refs.tableInfo.saveDimension(this.indicatorIndex)
      } else {
        this.$refs.tableInfo.saveCurrentScheme()
      }
      this.$emit(EVENT_TYPE.PREVIEW, this.$_deepClone(this.currentEditData), previewCode)
    },
    // 切换水滴图模式
    handleLiquidFillMode(mode) {
      this.liquidFillMode = mode
      if (mode === VE_LIQUIDFILL_MODE.NORMAL) {
        let eventDataObj = {
          source: 'tableInfo',
          target: 'dimensionDragBox',
          targetFn: 'emitFunction',
          data: { data: [], targetFn: 'setList', setDragList: true }
        }
        const eventData = new EventData(eventDataObj)
        this.$refs.dimensionDragBox &&
          this.$refs.dimensionDragBox.emitFunction(eventData)
      }
    },
    // 检测是否有外部卡片交互当前图形
    checkIsActionByCards() {
      let flag = false
      const interKeys = Object.keys(this.interactionReferencedInfo.actions).filter(key => {
        const o = this.interactionReferencedInfo.actions[key]
        return o && o.find(s => s.actionType === 'params')
      })
      const globalParamIds = this.$refs.chartContent?.getElementGlobalParamIdList ? this.$refs.chartContent.getElementGlobalParamIdList() : []
      console.log('???', interKeys, globalParamIds)
      for (let i = 0; i < interKeys.length; i++) {
        let eid = interKeys[i]
        const el = this.elList.find(item => item.id === eid)
        ;(el?.content?.interactionOptions || []).forEach(opt => {
          if (opt.id === eid && opt?.selectGranularity?.length) {
            opt.selectGranularity.forEach(gran => {
              if (!!gran && !globalParamIds.includes(gran.id)) {
                flag = true
              }
            })
          }
        })
        if (flag) return flag
      }
      return flag
    }
  }
}
</script>

<style lang="scss" scoped>
@import 'packages/base/board/displayPanel/supernatant/chartSet/functions/index.scss';
.template-type {
  margin-left: 24px;
  /deep/ {
    .el-radio__label {
      font-size: 12px;
    }
  }
}
.chart-background {
  background-color: var(--sdp-cy-bjs);
  width: 100%;
  box-sizing: border-box;
  margin-top: 16px;
  border-radius: 2px;
  box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
  overflow: hidden;
}
.first-font-color {
  color: var(--sdp-ycsz-rskbt);
}
.pointer {
  cursor: pointer;
}
.filerSapce {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.icon-sdp-shujuguolv {
  cursor: pointer;
  font-size: 25px;
}
.chart-edit {
  font-size: 14px;
  font-weight: bold;
  margin-left: 16px;
  width: 190px;
  text-align: left;
}

.back-icon {
  font-size: 16px;
}
.body-cotainer /deep/ {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .layout-center {
    height: 100%;
    flex: 1;
    margin: 0;
    padding: 16px;
    overflow: hidden;
    background-color: var(--sdp-cy-bjs);
    .dragbox-outter-container {
      .el-select .el-input,
      .el-input {
        .el-input__inner {
          background: var(--sdp-tb-srbj);
        }
      }
      & + .dragbox-outter-container {
        margin-top: 4px;
      }
    }
    .param-selection {
      display: flex;
      align-items: center;
      padding: 0 16px;
      height: 40px;
      border-radius: 2px;
      background-color: var(--sdp-jddlsrbj);
      color: var(--sdp-cszjsz-wzs1);
      .flex1 {
        height: 40px;
        position: relative;
        .draggableBox {
          height: 40px;
          // padding-top: 6px;
          overflow-y: auto;
          overflow-x: hidden;
        }
        // .ctrl-y-axis {
        //   position: absolute;
        //   top: 5px;
        //   right: 10px;
        //   height: 30px;
        //   line-height: 30px;
        //   cursor: pointer;
        // }
      }
    }
    .param-selection-title {
      margin-right: 16px;
    }
    .chart-content {
      flex: 1;
      width: calc(100% - 156px);
      background-color: var(--sdp-gjys-bjs);
      overflow: hidden;
      /*outline: 1px solid pink;*/
    }
    .map-mode_box {
      display: flex;
      .param-selection {
        flex: 1;
        margin-top: 0;
      }
      > div:nth-child(1) {
        margin-right: 4px;
      }
      > div:nth-child(2) {
        margin-left: 0;
      }
    }
    .flex-row {
      // padding-left: 16px;
      .flex-column {
        display: flex;
        flex-direction: column;
        margin-left: 16px;
        overflow-x: hidden;
        background-color: var(--sdp-cy-bjs);
        .flex1 {
          padding: 0;
          flex: 0;
          .color-selector {
            font-size: 12px;
          }
        }
        .color-cutline {
          margin-top: 70px;
        }
      }
    }
  }
  .setting {
    width: 212px;
    padding: 12px 10px;
    overflow: hidden;
    font-family: NotoSansHans-Regular;
    font-weight: 500;
    background-color: var(--sdp-szk-bjs);
    color: var(--sdp-xxbt2);
    &::before {
      content: '' !important;
      display: none !important;
    }
    .el-form {
      padding: 0;
    }
  }
  .setting-wide {
    width: 344px;
    padding: 16px 0 0 0;
    /deep/ .el-scrollbar .el-scrollbar__bar.is-horizontal {
      display: none;
    }
    /deep/ .el-scrollbar__wrap{
      overflow-x: hidden;
    }
  }
}
@-moz-document url-prefix() {
  .setting {
    box-sizing: content-box;
  }
}
.cutline {
  width: 100%;
  height: 1px;
  margin-bottom: 10px;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: var(--sdp-ycfgx);
}

.dialog-container /deep/ {
  .flex-row .input-container-header .chart-input {
    width: 240px;
    margin-left: 16px;
    .el-input__inner {
      padding: 0 12px;
      font-size: 12px;
      opacity: 0.9;
      width: 100%;
      height: 24px;
      line-height: 22px;
      border-radius: 0;
      background-color: var(--sdp-fs2);
      color: var(--sdp-cszjsz-wzs1);
    }
  }
  // .el-range-input{
  //   background-color: var(--sdp-ycsz-srk-bgs);
  //   color: var(--sdp-xxbt2);
  //   border-color: var(--sdp-ycsz-srk-bcs);
  // }
  .hide-text .el-input:not(.is-disabled) .el-input__inner {
    color: transparent !important;
  }
  .el-input:not(.is-disabled),
  .el-textarea:not(.is-disabled),
  .el-input-number:not(.is-disabled) {
    .el-input__inner,
    .el-textarea__inner {
      // background-color: var(--sdp-ycsz-srk-bgs);
      // color: var(--sdp-xxbt2);
      // border-color: var(--sdp-ycsz-srk-bcs);
    }
  }
  .el-radio__label {
    color: var(--sdp-fx-mrwzs);
  }
  .el-radio.is-checked {
    .el-radio__label {
      color: var(--sdp-zs);
    }
  }
  .el-dialog__body {
    height: calc(100% - 49px);
    padding: 0;
    word-break: normal;
  }
  .is-fullscreen > .el-dialog__header {
    position: relative;
    padding: 0;
    height: 49px;
    background-color: var(--sdp-tb-bj);
    color: var(--sdp-cyfhwzs);
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  }
  .el-tabs__item:not(.is-active) {
    color: var(--sdp-cyfhwzs);
  }
  .el-tabs__nav-wrap::after {
    content: '';
    background-color: var(--sdp-ycfgx);
  }
  .justify-between {
    height: 48px;
    opacity: 0.9;
    .icon-sdp-fanhui {
      margin-left: 10px;
    }
    > div:last-child {
      // width: 72px;
      height: 32px;
      margin-right: 16px;
      margin-top: 10px;
    }
  }
}
.scatter-height,
.liquidFill-height {
  height: calc(100% - 134px - 10px);
}
.other-chart-height {
  height: calc(100% - 92px - 10px);
}
.composite-chart-height, .map-flyline-lonlat-height {
  height: calc(100% - 180px - 10px);
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex1 {
  flex: 1;
}
.justify-between {
  justify-content: space-between;
}
.align-center {
  align-items: center;
}
/deep/ .el-tabs {
  .el-tabs__header {
    margin-bottom: 8px;
  }
}
.drag-box-text {
  position: absolute;
  top: 5px;
  right: 10px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  i {
    color: var(--sdp-zs);
  }
}
.none {
  display: none;
}
.legend-type-options {
  width: 180px;
  border: 1px solid $color-main;
  display: flex;
  border-radius: 3px;
  height: 26px;
  line-height: 24px;
  margin-top: 2px;
  font-size: 12px;
  > div {
    flex: 1;
    text-align: center;
    cursor: pointer;
    background-color: transparent;
  }
  > .active-option {
    background-color: var(--sdp-zs);
    color: var(--sdp-nngl) !important;
  }
  .disabled {
    cursor: not-allowed;
  }
}
</style>
<style lang="scss">
@import './styles/global.scss';
</style>

<!-- 弹窗中的操作指引 -->
<style lang="scss" scoped>
.guide-entry {
  width: 186px !important;
}
.kanban-guide-panel {
  z-index: 2002;
  position: fixed;
  top: 375px;
  right: 225px;
  // z-index: 99;
  padding: 12px;
  background: var(--sdp-guide-panel-bg);
  border-radius: 4px;
  box-shadow: var(--sdp-guide-panel-box-shadow);

  .guide-header {
    display: flex;
    justify-content: space-between;
    height: 14px;
    align-items: center;
    margin: 2px 2px 12px;

    .guide-title {
      font-size: 14px;
      font-weight: 600;
      line-height: 14px;
      color: var(--sdp-sztc-bts);
    }

    .guide-header-close {
      color: var(--sdp-srk-bxwzs);
      padding: 2px;
      cursor: pointer;
      &:hover {
        color: var(--sdp-zs);
      }
    }
  }

  .guide-entry {
    width: 184px;
    height: 26px;
    line-height: 26px;
    border-width: 1px;
    border-style: solid;
    // border-color: var(--sdp-guide-entry-bg);
    border-color: var(--sdp-guide-content-box-border);
    background: var(--sdp-guide-entry-bg);
    border-radius: 14px;
    margin-top: 4px;
    padding: 0 8px 0 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
    }

    &.is-doing {
      font-weight: bold;
      border-color: var(--sdp-guide-entry-border-color-by-done);
    }

    &.is-done {
      border-color: var(--sdp-guide-entry-border-color-by-done);
    }
    .guide-entry-index {
      font-weight: bold;
      margin-right: 8px;
    }

    .guide-entry-name {
      font-family: PingFang SC;
      font-size: 12px;
      line-height: 12px;
      color: var(--sdp-guide-entry-color);
    }

    .done-icon {
      width: 16px;
      height: 16px;
      line-height: 16px;
      border-radius: 50%;
      text-align: center;
      background-color: var(--sdp-zs);
      > i {
        color: var(--sdp-nngl);
        font-size: 16px;
        transform: scale(0.6);
        transform-origin: center;
        font-weight: bold;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
@import "packages/base/grid/theme/index";

.preview-dropdown {
  margin: 0 8px;
  /deep/ .el-button {
    @include common-header-preview-button;
    height: 28px;
    line-height: 28px;
  }
  &:hover {
    /deep/ .el-button {
      @include common-header-preview-hover-button;
      opacity: 1 !important;
    }
  }
}
.setting-wide {
  /deep/ {
    .setting-tabs > .el-tabs__header {
      position: sticky;
      top: 0;
      z-index: 2;
      background: linear-gradient(to bottom, var(--sdp-szk-bjs), transparent);
    }
    & > .el-scrollbar > .el-scrollbar__wrap > .el-scrollbar__view {
      min-height: calc(100% + 306px - 66px);
    }
  }
}
.setting-wide.has-sub-type-chart {
  /deep/ {
    & > .el-scrollbar > .el-scrollbar__wrap > .el-scrollbar__view {
      min-height: calc(100% + 306px);
    }
  }
}
.guide-content-box{
  // background: #F4F2FF;
  background: var(--sdp-guide-content-box-background);
  border-radius: 14px;
}
.sdp-steps {
  display: flex;
  flex-direction: column;
  padding: 5px 10px 0 10px;
}
.sdp-step-item {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex: 1;
  text-align: center;
  position: relative;
  min-width: 44px;
  min-height: 30px;
  /* font-size: 11px; */
  color: var(--sdp-chart-set-step-text);
  align-items: center;
}
.sdp-step-dot {
  width: 8px;
  height: 8px;
  display: flex;
  flex-direction: row;
  border-radius: 50%;
  background-color: var(--sdp-chart-set-step-dot);
}
.sdp-step-text {
  margin-left: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  font-size: 12px;
  &.not-allowed {
    cursor: not-allowed;
  }
}
.sdp-step-line {
  position: absolute;
  z-index: 0;
  left: calc(7.5px / 2);
  top: 62%;
  height: 74%;
}
.sdp-step-border {
  border-color: var(--sdp-chart-set-step-dot);
  border-left-width: 1px;
  border-left-style: dotted;
  height: 100%;
  vertical-align: middle;
}

.is-done {
  .sdp-step-dot {
    background-color: var(--sdp-chart-set-step-dot-active);
  }
  .sdp-step-text {
    color: var(--sdp-chart-set-step-text-active);
  }
  .sdp-step-border {
    border-color: var(--sdp-chart-set-step-dot-active);
    border-left-style: solid;
  }
}

.is-doing {
  .sdp-step-dot {
    background-color: var(--sdp-chart-set-step-dot-active);
  }
  .sdp-step-text {
    // color: var(--sdp-chart-set-step-text-active);
  }
  .sdp-step-border {
    // border-color: var(--sdp-chart-set-step-dot-active);
    // border-left-style: solid;
  }
}
.is-doing:hover{
  .sdp-step-text {
    color: var(--sdp-chart-set-step-text-active);
  }
}
.is-todo {
  cursor: not-allowed !important;
}
.is-todo .sdp-step-text {
  pointer-events: none;
}
.is-todo .sdp-step-border {
}

</style>

<style>
.sdp-min-width-140 {
  min-width: 140px;
}
</style>
