import { translateExpression } from 'packages/base/board/displayPanel/utils'
import { TYPE_ELEMENT } from '../../constants'
/**
 * 度量分组字段：webFieldType === 'metricGroup'
 * 计算字段：webFieldType === 'customComputed'
 * 关联数据集：webFieldFrom === 'associationDataset'
**/
import { OnlyField } from 'packages/base/board/displayPanel/datasetReplace'
const datasetFieldKey = 'webDatasetFieldId'

export default {
  inject: {
    aliasDict: { default: () => () => ({}) },
    getUnknownName: { default: () => (a, b) => b },
    getFieldName: { default: () => (a, b) => b },
    getDictAlias: { default: () => (a) => a },
    getBoardInfo: { default: () => () => ({}) },
  },
  computed: {
    displayBoardInfo() {
      if (this.boardInfo?.content) return this.boardInfo
      return this.getBoardInfo()
    },
  },
  methods: {
    // 获取自定义字段，原始配置（不同类型返回的格式可能不一样）需对返回结果判空
    // 如果不是自定义字段，返回undefined
    getCustomFieldItem(propItem, _webFieldType = '') {
      const { metricGroupMap = {}, customComputedMap = {} } = this.displayBoardInfo || {}
      const webFieldType = _webFieldType || propItem.webFieldType
      const { metricGroupInfo = {}, ...fieldItem } = propItem
      const dataMap = webFieldType === 'metricGroup' ? metricGroupMap : customComputedMap
      const metricGroupList = dataMap[metricGroupInfo.parentId || propItem.parentId] || []
      const thisMetricField = metricGroupList.find(m => m.id === (metricGroupInfo.id || propItem.id))
      return this.$_deepClone(thisMetricField)
    },
    // 获取后台接口preview需要的字段详细配置，此方法返回的数据格式理论上是一致的
    // 如果不是自定义字段，返回propItem
    // 不同元素类型后台需要的格式可能不一致，这里要传elementType(element.type)
    getCustomDatasetFieldDetail(propItem, elementType) {
      let result = null
      if ([TYPE_ELEMENT.CHART, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE, TYPE_ELEMENT.CUSTOMER_ELEMENT].includes(elementType)) {
        result = this.getCustomFieldPreview(propItem, elementType)
      }
      return result || propItem
    },
    // 获取要显示的字段名，第二个参数为是否不显示别名，默认取别名
    getDatasetLabel(propItem: any = {}, noAlias = false) {
      if (!propItem) return ''
      // 指标对应的字段显示指标名称
      if (propItem.isFromIndex) return propItem.indexName
      const fieldAlias = propItem.hasOwnProperty('alias_origin') ? propItem.alias_origin : propItem.alias
      if (!noAlias && fieldAlias) return fieldAlias
      const _propItem = this.getCustomFieldDatasetItem(propItem, propItem.webFieldType)
      const { webFieldFrom, labeName } = propItem
      const customName = (webFieldFrom !== 'associationDataset' && _propItem?.customFieldName) || labeName
      return this.getUnknownName(propItem.parentId, customName)
    },
    // 卡片交互存储的是别名||字段名，点击时传值以前是getDatasetLabel，会取数据集字段别名，导致
    // 不匹配，所以用这个方法 取 别名||字段名
    getDatasetAliasOrLabel(propItem: any = {}, noAlias = false) {
      if (!propItem) return ''
      const fieldAlias = propItem.hasOwnProperty('alias_origin') ? propItem.alias_origin : propItem.alias
      if (!noAlias && fieldAlias) return fieldAlias
      const _propItem = this.getCustomFieldDatasetItem(propItem, propItem.webFieldType)
      const { webFieldFrom, labeName } = propItem
      const customName = (webFieldFrom !== 'associationDataset' && _propItem?.customFieldName) || labeName
      return customName
    },
    translateExpression,
    // 返回和正常数据集字段项一样格式的数据，展示数据集字段列表时可能要用到
    getCustomFieldDatasetItem(propItem, webFieldType) {
      const thisMetricField = this.getCustomFieldItem(propItem, webFieldType)
      if (!thisMetricField) return null
      let labeName = thisMetricField.relativeFieldLabeName || thisMetricField.labeName
      if (!labeName && webFieldType === 'customComputed') labeName = thisMetricField.customFieldName
      const res = {
        columnTpe: thisMetricField.columnTpe,
        id: thisMetricField.id,
        labeName: labeName,
        parentId: thisMetricField.parentId,
        webFieldType: thisMetricField.webFieldType,
        customFieldName: thisMetricField.customFieldName,
        customExpressionType: thisMetricField.customExpressionType,
        relativeFieldLabeName: thisMetricField.relativeFieldLabeName,
        webFieldFrom: propItem.webFieldFrom,
      }
      return res
    },
    getCustomFieldPreview(propItem, elementType = TYPE_ELEMENT.CHART) {
      // todo: 这里还没有兼容计算字段，可以分元素类型返回不同的值
      const { metricGroupInfo = {}, ...fieldItem } = propItem || {}
      if (!propItem || !['metricGroup', 'customComputed'].includes(propItem.webFieldType)) return fieldItem
      const thisMetricField = this.getCustomFieldItem(propItem)
      if (!thisMetricField) return propItem
      const webFieldFrom = metricGroupInfo.webFieldFrom || propItem.webFieldFrom
      const commonParams = {
        ...fieldItem,
        parentId: metricGroupInfo.parentId || propItem.parentId,
        columnType: thisMetricField.columnTpe,
        alias: (webFieldFrom === 'associationDataset' && propItem.alias) || metricGroupInfo.customFieldName || thisMetricField.customFieldName,
        id: thisMetricField.id || metricGroupInfo.id,
        customFieldName: thisMetricField.customFieldName,
        webFieldType: thisMetricField.webFieldType,
        metricGroupInfo,
      }
      let otherParams: any = {}
      if (propItem.webFieldType === 'metricGroup') {
        otherParams = {
          columnName: thisMetricField.relativeFieldLabeName,
          from: thisMetricField.startValueData,
          to: thisMetricField.endValueData,
          interval: thisMetricField.groupStepValue,
          relativeFieldLabeName: thisMetricField.relativeFieldLabeName,
        }
      } else if (propItem.webFieldType === 'customComputed') {
        otherParams = {
          columnName: thisMetricField.customExpression,
          customerExprField: true,
          exp: thisMetricField.customExpression,
        }
        if (thisMetricField.customExpressionType === 'dimension') {
          otherParams.customerExprDim = true
          delete commonParams.aggType
        } else {
          otherParams.aggType = 'expression'
        }
      }
      return { ...commonParams, ...otherParams }
    },
    // 获取数据集中的自定义字段列表
    getCustomFieldByDatasetId(id, webFieldType = 'metricGroup') {
      const { metricGroupMap = {}, customComputedMap = {} } = this.displayBoardInfo || {}
      if (webFieldType === 'customComputed') {
        return (customComputedMap[id] || []).map(m => this.getCustomFieldDatasetItem(m, m.webFieldType))
      } else if (webFieldType === 'metricGroup') {
        return (metricGroupMap[id] || []).map(m => this.getCustomFieldDatasetItem(m, m.webFieldType))
      } else if (webFieldType === 'ALL') {
        const metricGroupList = metricGroupMap[id] || []
        const customComputedList = customComputedMap[id] || []
        return ([...metricGroupList, ...customComputedList]).map(m => this.getCustomFieldDatasetItem(m, m.webFieldType))
      }
      return []
    },
    // 获取自定义字段关键信息，表格应该用不到
    getMetricGroupInfo(e) {
      return {
        parentId: e.parentId,
        id: e.id,
        customFieldName: e.alias || '',
        webFieldFrom: e.webFieldFrom,
      }
    },
    getOriginDatasetField(propItem) {
      if (propItem.webFieldType || propItem.webFieldFrom) return propItem
      if (propItem[datasetFieldKey]) return OnlyField.useOnlyFieldIdGetFieldData(propItem.parentId, propItem[datasetFieldKey])
      if (propItem.parentId && propItem.labeName) return OnlyField.useDatasetIdAndFieldNameGetFieldData(propItem.parentId, propItem.labeName)
    }
  },
}
