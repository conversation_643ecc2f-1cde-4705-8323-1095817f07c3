import DialogSelectIndicator from '../common/components/DialogSelectIndicator.vue'

export default {
  components: {
    DialogSelectIndicator
  },
  methods: {
    // dragList-拖拽后最新的list    isDimension-当前拖拽的是否是维度行   maxSelectOptions-当前模式最多选中几个维度 默认2 是卡片中的情况
    // fieldList-处理过的维度字段（多指标时是共有维度）     otherParam-其他一些参数
    indicatorDragAfter(dragList,isDimension,maxSelectOptions=2, fieldList = [], otherParam = {}){
      // 如果拖拽的是指标数据
      let hasIndicator = dragList.find(item => item.indexFlag);
      if (hasIndicator) {
        let index = dragList.findIndex(item => item.indexFlag);
        // 如果拖拽的是维度行，则需要弹出弹窗让用户选择维度
        if (isDimension) {
          const drillSettings = otherParam?.drillSettings || {};
          // 多指标-不能拖入指标到维度栏
          if(drillSettings?.indexFlagIds?.length > 1) {
            dragList.splice(index, 1)
            this.$message({
              message: this.$t('sdp.message.dragCommonDimensionsOnly'),
              type: 'warning'
            })
          } else {
            // let options = hasIndicator.children.filter(item => {
            //   return item.columnTpe != 'number'
            // });
            // 多指标展示共有维度
            let options = fieldList.filter(item => {
              return item
            });
            // 将指标拖过来后dragList.length === 1 ,但是当前未选择其他维度，所以需要-1
            if(options.length > 1){
              this.$refs.refDialogSelectIndicator.show(options, dragList.length ? dragList.length - 1 : 0, maxSelectOptions, dragList);
              return 'clogExecute'
            }else{
              // 待验证
              // 选第一个指标
              if(index>=dragList.length){
                dragList.push(hasIndicator.children[0]);
              }else{
                dragList.splice(index, 1,hasIndicator.children[0]);
              }
            }
          }
        
        } else {
          // let index = dragList.findIndex(item => item.isIndicator);
           // 不管拖拽到哪一个，只填充度量到拖拽的那一行,在此处找出度量的数据
          let current = hasIndicator.children.find(item => item.labeName.toLocaleUpperCase().startsWith('INDEX_NUM'));
          // 删除指标整个的数据，并塞入度量数据
          dragList.splice(index, 1, current);
        }
      }

      return dragList
    }

  },
}
