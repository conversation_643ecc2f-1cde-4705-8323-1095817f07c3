import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import { JSONClone } from '../../../../../assets/utils/globalTools'
import { getParamElementLabels } from '../../components/utils'
import { isHasElBind } from '../../utils'
import { chartSaveByFilterSorter } from '../filterDialog/utils'
import {addPersonalAttention, batchAddPersonalAttention} from '../../../dailyConcernMetrics/api'
import {getMoreLanguageContent, rollBackChartLangObj, switchElLanguage} from "../../boardLanguage";

// 清除不需要的Style
// export function cleanStyle(style) {
//   const arr = ['zIndex', 'transform', 'rotate', 'left', 'top']
//   for (const key in style) {
//     if (arr.includes(key)) {
//       delete style[key]
//     }
//   }
// }
// 转换位置函数
export function getPosition({ x, y, w, h }, layoutWidth) {
  const width = layoutWidth
  const colWidth = (width - (this.margin[0] * (this.colNum + 1))) / this.colNum
  return {
    // left: Math.round(colWidth * x + (x + 1) * this.margin[0]) + 'px',
    // top: Math.round(this.rowHeight * y + (y + 1) * this.margin[1]) + 'px',
    width: w === Infinity ? w : Math.round(colWidth * w + Math.max(0, w - 1) * this.margin[0]) + 'px',
    height: h === Infinity ? h : Math.round(this.rowHeight * h + Math.max(0, h - 1) * this.margin[1]) + 'px',
  }
}

// 看板元素编辑处理函数
export const elContentSetHelper = {
  [TYPE_ELEMENT.CHART](element) {
    this.chartData = element
    const fn = element.vm?.animationInstance?.stop
    fn && fn.call(element.vm?.animationInstance, 'into-isChartSet')
    this.chartSet.setData(element)
  },
  [TYPE_ELEMENT.FOUR_QUADRANT](element) {
    this.chartData = element
    this.chartSet.setData(element)
  },
  [TYPE_ELEMENT.TABLE](element) {
    this.elSettingProps = {
      gridConfig: element
    }
    this.elSettingVisible = true
  },
  [TYPE_ELEMENT.CONTAINER](element) {
    this.chartSet.setData(element)
  },
  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD](element) {
    this.chartData = element
    this.chartSet.setData(element)
  },
  [TYPE_ELEMENT.DUPONT_ANALYSIS](element) {
    this.chartData = element
    this.chartSet.setData(element)
  },
  [TYPE_ELEMENT.COMBINE_CARD](element) {
    this.chartData = element
    this.chartSet.setData(element)
  },
  [TYPE_ELEMENT.TEXT](element) {
    this.chartData = element
    this.chartSet.setData(element)
  },
  [TYPE_ELEMENT.ELEMENT_TITLE](element) {
    this.chartData = element
    this.chartSet.setData(element)
  },
}

function clearCardResponseData(el) {
  delete el.content.responseData
  delete el.content.rateCardResponse
  el.content.optionArray && el.content.optionArray.map(item => {
    const notSaveKeys = ['indexValue', 'rateGrowth', 'growthValue', 'rateCompletion', 'isNone', 'classText', 'arrowClass']
    Object.keys(item).forEach(key => {
      notSaveKeys.includes(key) && delete item[key]
    })
  })
}

// 看板元素保存处理函数
export const elContentSaveHelper = {
  [TYPE_ELEMENT.CHART]({ content, id, tipSet, tipSetList }, elList = []) {
    const oldAlias = this.chartData.content.alias
    this.chartData.content = content
    if (tipSet) {
      this.$set(this.chartData, 'tipSet', tipSet)
    } else {
      this.$delete(this.chartData, 'tipSet')
    }
    if (tipSetList) {
      this.$set(this.chartData, 'tipSetList', tipSetList)
    } else {
      this.$delete(this.chartData, 'tipSetList')
    }
    const oldSaveIndex = content.saveIndex
    const chartElRef = this.chartData.vm

    chartElRef.mapSchemeSwitch(0)
    chartElRef.resetLegendSelected({})
    chartElRef.updateUserConfig()
    // 清除卡片指标交互的临时数据
    this.$delete(chartElRef.element, 'cardInteractionState')
    this.$set(chartElRef, 'tempSaveIndex', content.saveIndex)

    if (chartElRef.metricChioceTab && chartElRef.metricChioceTab.isRelated) {
      chartElRef.metricSwitch(chartElRef.metricChioceTab.metricChioceTab, chartElRef.metricChioceTab.isRelated)
    }
    if (oldSaveIndex === content.saveIndex && content.alias === 've-grid-normal') {
      // 简单表格保存之后须重新获取画布大小，防止编辑时修改了标题的字号
      chartElRef.createTable({ firstRender: !!chartElRef.showGrid, from: 'set-save', getCanvasSize: (!chartElRef.metricChioceTab || chartElRef.metricChioceTab.isRelated) })
    }

    chartSaveByFilterSorter(this.chartData, elList, this)
  },
  [TYPE_ELEMENT.FOUR_QUADRANT]({ content, tipSetList }) {
    this.chartData.content = content
    if (tipSetList) {
      this.$set(this.chartData, 'tipSetList', tipSetList)
    }
  },
  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD](element) {
    const { content, tipSet, tipSetList } = element
    this.chartData.content = content
    if (tipSet) {
      this.$set(this.chartData, 'tipSet', tipSet)
    } else {
      this.$delete(this.chartData, 'tipSet')
    }
    if (tipSetList) {
      this.$set(this.chartData, 'tipSetList', tipSetList)
    } else {
      this.$delete(this.chartData, 'tipSetList')
    }

    clearCardResponseData(this.chartData)
    // 删除卡片颜色
    if (this.$_getProp(this.chartData, 'style.background', '')) {
      delete this.chartData.style.background
    }
    console.trace(element)
  },
  [TYPE_ELEMENT.COMBINE_CARD](element) {
    const { choiceTab, fullCard, borderRadius } = element.content
    let obj = {
      choiceTab,
      borderRadius,
      activePageId: this.chartData.content.activePageId || 1,
      fullCard
    }
    // 组合卡片弹框内切换page页不影响弹框外，替换弹窗外当前页内容
    let currentPage = choiceTab.find(item => item.id === obj.activePageId)
    obj.cardList = currentPage.saveObj.cardList
    Object.assign(this.chartData.content, obj)
    typeof this.chartData.content?.choiceTab === 'function' && this.chartData.content.choiceTab(page => {
      page.saveObj.cardList.map(el => {
        clearCardResponseData(el)
      })
    })
    this.chartData.content.cardList.map(el => {
      clearCardResponseData(el)
    })
  },
  [TYPE_ELEMENT.TEXT]({ content }) {
    this.chartData.content = content
  },
  [TYPE_ELEMENT.ELEMENT_TITLE]({ content }) {
    this.chartData.content = content
  },
}
const chartTable = 've-grid-normal'

export function isTableType(element) {
  if (!element) return false
  return element.type === TYPE_ELEMENT.TABLE || (element.type === TYPE_ELEMENT.CHART && element.content?.chartUserConfig?.chartAlias === chartTable)
}
export function isChartType(element) {
  if (!element) return false
  return element.type === TYPE_ELEMENT.CHART && element.content?.chartUserConfig?.chartAlias !== chartTable
}

export function getCurrency(languageAndCurrency, tenantData, isEnterPriseCurrency = true) {
  let languaeAndCurrency = ''
  if (Object.keys(languageAndCurrency).length > 0) {
    if (tenantData.currencyType === '2') {
      languaeAndCurrency = tenantData.currency
    } else {
      if (isEnterPriseCurrency) {
        languaeAndCurrency = languageAndCurrency.shopCurrency || null
      } else {
        languaeAndCurrency = languageAndCurrency.tenantCurrency || null
      }
    }
  }
  return languaeAndCurrency
}

export function getContainerOnElementId(el) {
  const { type, subType } = el
  let sonEl = []
  // 容器和高级容器
  if (type === TYPE_ELEMENT.CONTAINER && subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
    const { tabList = [] } = el.content

    const sonElIds: string[] = []

    tabList.forEach(element => {
      const { includedElsIdList = [] } = element.content

      sonElIds.push(...includedElsIdList)
    })

    sonEl = sonElIds
  } else if (type === TYPE_ELEMENT.CONTAINER) {
    const { includedElIds = [] } = el.content

    sonEl = includedElIds
  }

  return sonEl
}

export function getContainerOnElement(el, elList) {
  const { type, subType } = el
  let sonEl = []
  // 容器和高级容器
  if (type === TYPE_ELEMENT.CONTAINER && subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
    const { tabList = [] } = el.content

    const sonElIds: string[] = []

    tabList.forEach(element => {
      const { includedElsIdList = [] } = element.content

      sonElIds.push(...includedElsIdList)
    })

    sonEl = elList.filter(el => sonElIds.includes(el.id))
  } else if (type === TYPE_ELEMENT.CONTAINER) {
    const { includedElIds = [] } = el.content

    sonEl = elList.filter(el => includedElIds.includes(el.id))
  }

  return sonEl
}

// function clearDrillAndAssoci(previewData) {
//   // 拷贝一份处理
//   previewData = JSONClone(previewData)
//   for (let key in previewData.elementRequest) {
//     const item = previewData.elementRequest[key]

//     item.datas.forEach(element => {
//       // 如果元素在被交互状态时点击添加到每日关注指标时，添加进每日关注指标看板画布时该元素为未交互状态；
//       // if (element?.associComponents) {
//       //   delete element.associComponents
//       // }
//       // 如果元素下钻到子层级时点击添加到每日关注指标中，添加进每日关注指标看板画布时无需保留下钻状态，恢复成初始状态，可手动点击进行下钻；
//       const drillDimensions = element.drillDimensions
//       if (drillDimensions) delete element.drillDimensions
//     })

//   }
//   return previewData
// }

export function clearSuperLinkAndInteraction(allEl) {
  // 清除超链接，交互，仅交互时渲染数据
  allEl.forEach((el) => {
    const { isRenderInteraction = false } = el.elAttr || {}
    const { superLinkOptions, interactionOptions, interactionState } = el.content

    // 仅交互时渲染数据
    if (isRenderInteraction) {
      el.elAttr.isRenderInteraction = false
    }

    // 清除超链接
    if (superLinkOptions) delete el.content.superLinkOptions
    // 清除交互
    if (interactionOptions) delete el.content.interactionOptions
    // 清除交换状态
    if (interactionState) delete el.content.interactionState
    // 清除钻取
  })
  return allEl
}

// 重置元素的多语言为默认值
// 参考 initBoardLanguage
function resetElementLanguage(elList) {
  if (!this.standradBoardList.length) {
    this.standradBoardList = getMoreLanguageContent.call(this)
  }
  const arr = this._recursion(this.standradBoardList)

  switchElLanguage.call(this, {
    arr,
    contentKey: 'id',
    elList
  })

  rollBackChartLangObj.call(this, this.boardSlectLang || this.langCode, this.newBoardContent, elList)

  return elList
}

export enum IS_ADD_TYPE {
  ADD = '1',
  UPDATE = '0'
}

// 保存元素数据
export function saveElementData({
  api,
  folderType,
  personalAttentionType,
  classifyId,
  dashboardId,
  boardInfo,
  elList,
  elementId,
  tabId,
  paramsPanelList,
  previewData,
  metaDashboardElementLanList,
  filterServerNeedParams,
  languageAndCurrency,
  isEnterPriseCurrency,
  tenantData,
  isEMS,
  isGetParam,
  isEditToSave = false, // 保存编辑时候的标识
  errorCall = () => {},
  successCall = () => {}
}) {
  const el = this.elList.find(item => item.id === elementId)
  const paramsPanel = paramsPanelList.find(item => item.active)
  const langIds: string[] = []

  if (!el) return console.error('没有找到该元素')

  if (!paramsPanel) return console.error('没有找到参数组件')

  const { filterData } = el

  const linkageData = {}
  if (filterData?.isEnable) {
    filterData.form.reduce((pre, next) => {
      const ids = next?.associate?.[paramsPanel.id] || []

      ids.forEach(id => {
        pre[id] = filterServerNeedParams._params[id]
      })

      return pre
    }, linkageData)
  }

  const elementContent: any = {
    // !看板元素(导入到首页看板需要重置tabId)
    elList: [],
    // 看板相关数据
    boardInfo: {
      folderId: classifyId,
      name: boardInfo?.name || '',
      code: boardInfo?.code || '',
    },
    // !参数组件(导入到首页看板需要重置tabId)
    paramsPanelList: {},
    paramsPanelActiveId: '',
    tabId,
    // 接口请求
    previewData,
    // 多语言
    metaDashboardElementLanList: [],
    // 已选参数
    selectedParamsInfo: [],
    // 筛选器的联动数据
    linkageData
  }

  const apiParam: {
    folderType: string,
    personalAttentionType: string,
    classifyId: string,
    dashboardId: string,
    elementId: string,
    elementContent: string,
  } = {
    // 看板类型
    folderType,
    // 看板类型 用于区分发布给我的看板
    personalAttentionType,
    // 分类(文件夹)id
    classifyId,
    // 看板id
    dashboardId,
    // 元素Id
    elementId,
    // 元素内
    elementContent: ''
  }

  let sonEl = getContainerOnElement(el, elList)

  const allEl = JSONClone([...sonEl, el])
  elementContent.elList = clearSuperLinkAndInteraction(allEl)
  // 编辑的时候跳过
  if (!isEditToSave) { // 看板保存的时候会将标题替换成初始标识，所以加一个标识跳过这个方法
    elementContent.elList = resetElementLanguage.call(this.$parent, elementContent.elList)
  }
  const allElIds = allEl.map(el => el.id)
  // 元素id
  langIds.push(...allElIds)

  const { content = [], id } = paramsPanel

  // 存储当时的tab id
  elementContent.paramsPanelActiveId = id
  elementContent.tabId = tabId

  elementContent.selectedParamsInfo = {
    Currency: getCurrency(languageAndCurrency, tenantData, isEnterPriseCurrency),
  }

  // 当前tabsId
  langIds.push(id)
  // 参数组件id
  if (content.length) {
    const paramsPanels = content.filter(element => isHasElBind(element, allElIds))

    // 已选参数
    elementContent.selectedParamsInfo.allElIds = allElIds
    // 参数组件
    // ...paramsPanels.map(paramEl => ({
    //   [paramEl.elName]: getParamElementLabels.call(this, paramEl, paramsPanel)
    // }))

    const paramsPanelIds = paramsPanels.map(el => el.id)

    elementContent.paramsPanelList = JSONClone(paramsPanel)

    langIds.push(...paramsPanelIds)
  }

  // 获取多语言
  if (langIds.length) {
    elementContent.metaDashboardElementLanList = metaDashboardElementLanList.filter(item => {
      return langIds.some(key => item.key.includes(key)) || item.key.includes('_currency')
    })
  }
  // 转换成字符
  apiParam.elementContent = JSON.stringify(elementContent)
  console.log('v1.0.2 -- sbi 保存元素数据', apiParam, elementContent)
  if (isGetParam) {
    return apiParam
  }
  if (isEMS) {
    batchAddPersonalAttention(api, [apiParam]).then(successCall, errorCall)
    return
  }
  // 请求接口
  addPersonalAttention(api, apiParam).then(successCall, errorCall)
}

export function getPreviewData() {
}

/**
 * 注（多合一多标题返回空，图形多指标返回空，双指标卡片返回空）
 * @param element 元素
 * @returns 元素标题
 */
export function getElementTitle(element, lang) {
  const content = element?.content
  switch (element.type) {
    case TYPE_ELEMENT.TABLE:
      const tableControlsLanguageList = content?.dataScreenRes?.tableControlsLanguageList || []
      const titTxt = content?.tableDefaultConfig?.title?.text
      if (lang) {
        const res = tableControlsLanguageList.find(({ type, languageCode }) => type === 'title' && languageCode === lang)
        return res?.value || titTxt
      }
      return content?.tableDefaultConfig?.title?.text
    case TYPE_ELEMENT.CHART:
      return content?.chioceTab?.length > 1 ? '' : content?.chartUserConfig?.title?.text
    case TYPE_ELEMENT.CONTAINER:
      return content?.settings?.title
    case TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD:
      return content?.optionArray?.length === 1 ? content?.optionArray?.[0]?.cardName : ''
    case TYPE_ELEMENT.TEXT:
      if (lang && content?.locales?.[lang]) {
        return content.locales[lang]?.title || content?.title
      }
      return content?.title
    default:
      return ''
  }
}
