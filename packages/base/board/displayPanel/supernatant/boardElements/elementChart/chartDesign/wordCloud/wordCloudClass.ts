
import BasicsEchartsChart from '../chartBasicClass/BasicsEchartsChartClass'
import { InitParams, RefreshChartParams } from '../chartInterface/baseInterface'
import { SeriesOption, TooltipOption } from '../chartInterface/echartsOptionInterface'
import { afterConfigHandler } from '../../chartsList/charts'
import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import { getSeriesAnimation } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/chartSetting/chartLabel'
import { CHART_REFRESH_SOURCES, removeChartRefreshAnimation } from '../../constant'

interface WordCloudOptions {
  animation: boolean,
  series: SeriesOption[],
}
class WordCloud extends BasicsEchartsChart {
  handleFuncList: string[]
  options: WordCloudOptions
  echartsInstance: any
  vm: any
  constructor(params: InitParams) {
    const { element, injectData, echartsInstance, vm } = params
    super(element, injectData)
    this.handleFuncList = [...this.basicRanderFunc, 'wordCloudSetting', 'setLabelOption', 'allImgSettings']
    this.echartsInstance = echartsInstance
    this.vm = vm
    this.options = {
      animation: true,
      series: [],
    }
  }
  groupChartOptions() {
    this.handleFuncList.forEach(handlerFnKey => {
      this[handlerFnKey] ? this[handlerFnKey](this.vm, this.options) : afterConfigHandler[handlerFnKey].call(this, this.vm, this.options)
    })
  }
  // 渲染图形方法
  refreshChart(params?: RefreshChartParams) {
    console.log('refreshChart', params && params.source)
    this.options = {
      animation: true,
      series: [],
    }
    // 初始化图形基础数据
    this.initChartData()
    // 组装图形配置
    this.groupChartOptions()
    // 必要时移除动画
    removeChartRefreshAnimation(params?.source || '', this.options)
    // SIM 自助分析不开启动画,不然显示不出来
    if (this.vm?.$getSystemConfig?.('onlyIndex')) {
      this.options.layoutAnimation = false
      this.options.series?.[0]?.layoutAnimation = false
    }
    this.echartsInstance.setOption(this.options, true)
    this.echartsInstance.resize([CHART_REFRESH_SOURCES.NO_ANIMATION].includes(params?.source) ? {} : getSeriesAnimation(true))
    this.vm.echartOption = this.echartsInstance.getOption()
  }

  wordCloudSetting(vm, options) {
    const { chartUserConfig, isMobile, isChartSet, themeType, chartData = {} } = vm
    const { series } = options
    const { wordCloudSetting = {} } = chartUserConfig
    if (!chartData.rows?.length) return
    // 获取维度颜色
    const color = Color.getDimensionColor(vm).noRepeat
    if (!Array.isArray(series[0].data) || !color?.length) return
    const defaultColors = Color.getDefaultChartColor(chartUserConfig, { colorThemeType: themeType, vm: vm })
    let mobileHeight = '70%'
    let mobileTop = '20%'
    series[0].data.forEach((dataItem, dataIndex) => {
      const dataItemColorName = wordCloudSetting.colorFieldKey && dataItem.row ? dataItem.row[wordCloudSetting.colorSeriesProp] : dataItem.name
      if (!dataItem.textStyle) {
        dataItem.textStyle = {}
      }
      const oldColor = color.find(colorItem => colorItem.name === dataItemColorName)
      let _color = oldColor ? oldColor.color : defaultColors[dataIndex % defaultColors.length]
      dataItem.textStyle.color = _color
      dataItem.emphasis = {
        textStyle: {
          borderColor: _color,
          borderWidth: 1,
        }
      }
    })
    let baseSizeRange = [12, 48]
    let sizeRange = baseSizeRange
    // 根据元素尺寸及数据条数两个基准自适应 图形默认尺寸为 932*304，数据默认基准为50条，移动端跟图形编辑界面不需要处理
    if (wordCloudSetting.fontSizeMode !== 'fixedSize') {
      if (!isChartSet) {
        const chartDom = document.querySelector(`.${vm.chartClassName} .widget-chart`)
        const originalDataScale = 50 / chartData.rows.length

        // 避免比例过大或过小，增加一个系数
        let coefficient = 1
        if (originalDataScale > 2 && originalDataScale <= 4) {
          coefficient = 0.5
        } else if (originalDataScale > 4) {
          coefficient = 2.1 / originalDataScale
        } else if (originalDataScale < 0.5) {
          coefficient = 2
        }
        const dataScale = originalDataScale * coefficient
        const sizeScale = isMobile ? 1 : chartDom.offsetWidth / 932

        sizeRange = baseSizeRange.map((size, index) => {
          let result = Math.ceil(size * sizeScale * dataScale)
          // PC 最大字体不超过80px，最小字体不超过20px
          // MOBILE 最大字体不超过48px，最小字体不超过12px
          let min = isMobile ? 12 : 20
          let max = isMobile ? 48 : 80
          let baseOverSize = index === 0 ? min : max
          if (result > baseOverSize) {
            result = baseOverSize
          }
          return vm.getPxOfHighResolution(result)
        })
      }
    } else {
      sizeRange = [wordCloudSetting.minFontSize, wordCloudSetting.maxFontSize]
    }

    Object.assign(series[0], {
      width: isMobile ? '80%' : '90%',
      height: isMobile ? mobileHeight : '85%',
      top: (isMobile && !isChartSet) ? mobileTop : 'center',
      sizeRange,
      rotationRange: [0, wordCloudSetting.arrangementMode === 'horizontal' ? 0 : 90],
      rotationStep: 90,
      shape: wordCloudSetting.wordCloudShape || 'circle',
      getDataColor: function(di) {
        const defaultColOndex = di % defaultColors.length
        return this.data[di].textStyle.color || defaultColors[defaultColOndex]
      },
      setDataColor: function(di, _newColor) {
        this.data[di].textStyle.color = _newColor
        this.data[di].emphasis.textStyle.borderColor = _newColor
      }
    })
    Object.assign(options, {
      legend: { show: false },
      color: [],
      brush: undefined,
      grid: {}
    })
  }

}

export default WordCloud
