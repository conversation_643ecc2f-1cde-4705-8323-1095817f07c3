import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import { LINE_STYLE_SETTING_CHART } from '../../constant'
import iconPath from '../../chartsList/iconPath'

// 线状图样式设计
export function lineNormal(vm, options) {
  const { series } = options
  const { isMobile, chartUserConfig } = vm
  const { metricFirstList } = vm.UserConfig
  series.forEach((item, seriesIndex) => {
    if (item.type !== 'line') return
    // item.smooth = false
    const seriesObj = {
      symbolSize: 6,
      itemStyle: {
        borderWidth: 2,
      },
      lineStyle: {
        width: 3,
      },
      smooth: 0,
      emphasis: { scale: 2, },
    }
    Object.assign(item, seriesObj)
    // 折线面积图，堆叠所有度量
    const { chartAlias, yAxis = [], lineSetting = {}, compositeChart = {} } = chartUserConfig
    function getStackIndex() {
      return metricFirstList.some(e => e.lang_alias === item.name) || !yAxis[1]?.axisLabel?.show ? '1' : '2'
    }
    if (!lineSetting?.areaStack) {
      if (chartAlias === 've-line-area' || (chartAlias === 've-composite' && compositeChart.area)) {
        item.stack = seriesIndex
      }
    } else if (chartAlias === 've-line-area') {
      item.stack = getStackIndex()
    }
  })
  // 移动端折线图增加点击范围
  if (isMobile) {
    series.push({
      type: 'line',
      markLine: {
        label: {
          show: false
        },
        symbol: 'none',
        data: [],
        lineStyle: {
          opacity: 0,
          type: 'solid',
          width: 20
        },
        emphasis: {
          lineStyle: {
            width: 20
          },
        }
      }
    })
    series[0].data.forEach((_, index) => {
      series[series.length - 1].markLine.data.push({
        xAxis: index,
        // 补充移动端点击线独特标识
        name: 'mobileClickArea'
      })
    })
  }

  // 折线面积图等线型样式
  handSetLineStyle(vm, options)
}

// 折线图面积图 组合图支持设置曲线，线形以及圆点
function handSetLineStyle(vm, options) {
  const { series } = options
  const { chartUserConfig: { userLineStyle, chartAlias, colors = [] } } = vm
  const { dimensionExtendList, metricAllList } = vm.UserConfig
  if (!LINE_STYLE_SETTING_CHART.includes(chartAlias) || !userLineStyle?.length) return
  options.series = series.map((item, seriesIndex) => {
    const lineStyleItem = userLineStyle.find(lineStyle => lineStyle.keyName === item.metricKeyName)
    const { lineStyle, type: sType, symbolSize, areaStyle, itemStyle } = item
    if (sType !== 'line' || !lineStyleItem) return item
    const { type, width, smooth, showSymbol, symbol = 'emptyCircle', areaColor, symbolColor } = lineStyleItem
    const nLineStyle = { ...lineStyle, type, width }
    const colorIndex = metricAllList.findIndex(m => m.keyName === item.metricKeyName)
    if (!dimensionExtendList.length) {
      if (colorIndex > -1 && colors[colorIndex]) nLineStyle.color = Color.getEchartsColorItem(colors[colorIndex])
    } else {
      if (colors[seriesIndex]) nLineStyle.color = Color.getEchartsColorItem(colors[seriesIndex])
    }
    // 需要替换矢量路径的symbol
    const map = {
      fivePointedStar: iconPath.fivePointedStar,
      emptyFivePointedStar: iconPath.emptyFivePointedStar,
    }
    const changeInfo = { lineStyle: nLineStyle, smooth: smooth || false, symbol: showSymbol ? map[symbol] || symbol : 'path://' }
    if (showSymbol && map[symbol]) {
        Object.assign(changeInfo, { symbolSize: 10 })
    }
    if (areaColor && areaStyle && !dimensionExtendList.length) {
      item.areaStyle = {
        color: Color.getEchartsColorItem(areaColor),
        opacity: 1,
      }
    }
    if (symbolColor && itemStyle) {
      item.itemStyle = {
        ...item.itemStyle,
        color: Color.getEchartsColorItem(symbolColor)
      }
      // 折线面积图但又没设置面积颜色，就继承默认线颜色
      if (areaStyle && !item.areaStyle.color) {
        item.areaStyle.color = nLineStyle.color ? Color.getEchartsColorItem(nLineStyle.color) : {}
      }
    }
    if (item.itemStyle && !item.itemStyle.color) {
      item.itemStyle.color = nLineStyle.color ? Color.getEchartsColorItem(nLineStyle.color) : {}
    }
    return { ...item, ...changeInfo }
  })
}
