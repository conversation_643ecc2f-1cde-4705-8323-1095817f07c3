import { TYPE_ELEMENT, TYPE_ELEMENT_GROUP } from '../../../constants'
import i18n from 'packages/assets/locale'
import Vue from 'vue'
import { setBandwidthRequestData } from './chartsList/charts'
import { isReverseAxis<PERSON><PERSON> } from './chartsList/chartSetting/handleChartSetting'
import {
  chartUserConfig,
  GRAPHARR,
  HAS_TARGET_CHART,
  HANDLE_MAX_MIN_VALUE_CHART,
  NO_DIMENSION_CHART,
  NEED_TIMEDIMENSION_CHART,
  DOUBLE_DIMENSION_CHART,
  SUPPORT_DEPUTY_AXIS,
  MEARSURE_DIS_CENTER,
  HIDE_MEARSURE_CHART,
  STACK_ACC_CHART,
  SPECIAL_SUPPORT_DEPUTY_AXIS,
  DIMENSION_VALUE_INDICATOR_WARNING_CHART,
  WARNING_LINE_CHART,
  CHART_ALIAS_TYPE,
  VE_LIQUIDFILL_MODE,
  MULTI_GRAPHARR,
  createDimension,
  SORT_SETTING_CHART,
  PROPORTION_BASIS_TYPE
} from './constant'
import { getThemeConfig } from './chartsList/theme'
import {Content, Layout, Styles, StyleConfig, StoreLayout} from '../elementClasses'
import { deepClone } from 'packages/assets/utils/globalTools'
import { DATA_FILTER } from 'packages/base/grid/helpers/constants/index'
import mapConstant from './chartsList/mapConstant'
import faseResponse from './response.json'
import DatasetBase, { FieldType, ReplaceRuleType } from '../../../datasetReplace/datasetReplace'
import { getAggType } from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'
import previewHandler from './previewHandler'
import { compatibilityDatasetField, getUsedDatasetField, datasetFieldReplace } from './helper/dataset'
import {setDefaultElementPadding} from "../../../utils";
import {PREVIEW_STATUS} from "../../../../../../assets/constant";
import {
  replaceTipSetListUsedDataSetAndField,
  replaceTipSetUsedDataSetAndField
} from '../../chartSet/functions/modules/tipSetting/dataset'

const element = {
  name: i18n.t('sdp.views.chart'),
  groupName: i18n.t('sdp.views.chart'),
  groupType: TYPE_ELEMENT_GROUP.CHART,
  componentName: 'element-chart',
  type: 'chart',
}

export class ElementChartDataset extends DatasetBase {
  includesDataPath = []
  excludesDataPath = []
  elementType = TYPE_ELEMENT.CHART
  isBindField = false
  callDatasetReplace(element: any, replaceRule: ReplaceRuleType): void {
    console.log(element, replaceRule)
    replaceTipSetListUsedDataSetAndField(element, replaceRule)
    datasetFieldReplace(element, replaceRule)
  }
  getElementDatasetIdAndUseField(element: any, datasetList: any[]): { [id: string]: FieldType[] } | boolean {
    return getUsedDatasetField(element, datasetList)
  }
  getElementDatasetIdAndUseFieldDecorator(...rest: [any, any[]]) {
    return this.decorator(...rest)
  }
  compatibility(el) {
    compatibilityDatasetField.call(this, el)
  }
}

class ElContent extends Content {
  constructor(type) {
    super(type)
    this.drillSettings = {}
    this.chartUserConfig = Vue.prototype.$_JSONClone(chartUserConfig)
  }
}

class ElStyle extends Styles {
  constructor(styleOpt, options = {}) {
    super(styleOpt, options)
    if (options.boardType === 'dataReport') {
      this.width = options.pageSize?.pageWidth || 929
      this.height = 320
    } else if (options.isScreen) {
      this.width = 929
      this.height = 304
    }
  }
}

export class ElLayout extends Layout {
  constructor(element, options) {
    super(element, options)
    this.h = 24
    this.w = options.isMobile ? 144 : 72
  }
}
class ElStoreLayout extends StoreLayout {
  constructor(element, options) {
    super(element, options)
    this.pcLayout.h = 24
    this.pcLayout.w = 72
    this.mobileLayout.h = 24
    this.mobileLayout.w = 144
  }
}

export class ElStyleConfig extends StyleConfig {
  constructor(styleOpt, options = {}) {
    super(styleOpt, options)
    if (options.boardType === 'dataReport') {
      this.size.width = options.pageSize?.pageWidth || 929
      this.size.height = 320
    } else if (options.isScreen) {
      this.size.width = 929
      this.size.height = 304
    }
    setDefaultElementPadding(this, TYPE_ELEMENT.CHART, options.isMobile)
  }
}

const adapter = {
  // 表格后台交互的请求字段
  requestKeyFn() {
    return 'chartElements'
    // const vm = el.vm
    // if (vm.application === vm.tableStatus.drill) {
    //   return 'tableDrills'
    // }
    // return 'tableElements'
  },
  // 请求参数获取方法
  request(el, { isChartSet, compareMethod = false }) {
    console.log('request')

    const { chartUserConfig } = el.content
    const { chartAlias, extendDimensionList = [], bandwidthData, butterflySetting = {}, mapSetting = {}, liquidFillSetting = {}, dimensionLangColor = false, metricLabelDisplay=[] } = chartUserConfig
    const DELETE_FLAG = 'DELETE_FLAG'

    let ret = Object.keys(el).includes('calendarComponent') ? {
      calendarGroupComp: el.calendarGroupComp,
      calendarComponent: el.calendarComponent,
      drillSettings: el.content['drillSettings'],
    } : el.content['drillSettings']
    ret = deepClone(ret)
    Object.assign(ret, {
      id: ret.id || el.id,
      // 增加图形类型字段
      chartType: chartAlias,
      // 多语言维度颜色绑定
      langColor: dimensionLangColor,
      language: this.langCode,
      // 矩形树图、旭日图 按维度顺序逐级分层
      hierarchyDimension: ['ve-sunburst', 've-treemap'].includes(chartAlias),
      // 日期维度交互、钻取传参
      needTitleCalendarCacheMap: !!el.needTitleCalendarCacheMap || DELETE_FLAG,
      // 限制条数
      maxCountNum: !this.isMobile ? (ret.pageInfo.pageSize || 9999) : DELETE_FLAG,
      pageInfo: !this.isMobile ? DELETE_FLAG : ret.pageInfo?.pageSize === -1 ? { page: 1, } : ret.pageInfo,
      // 空值显示
      filterMetricsNullValue: !!(chartUserConfig.hasOwnProperty('isShowEmptyValue') && !chartUserConfig.isShowEmptyValue) || DELETE_FLAG,
      // 忽略维度扩展
      totalNoExtDim: !!(extendDimensionList.length && chartUserConfig.metricCalculation && chartUserConfig.totalNoExtDim) || DELETE_FLAG,
      // 不支持时间维度图形删除时间维度
      timeDimensionSplitting: !this.isShowTimeDimension ? DELETE_FLAG : ret.timeDimensionSplitting,
      filterData: el.filterData || DELETE_FLAG,
      sorterData: el.sorterData || DELETE_FLAG,
      // 地图增量数据
      showVariation: !!(this.isMap && !isChartSet && mapSetting.showDataChange && this.commonData.isPreview && this.utils.isScreen) || DELETE_FLAG,
      // 蝴蝶图-占比-计算方式
      rateRule: (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY && butterflySetting.dataPercent && butterflySetting.dataPercentType) || DELETE_FLAG,
      // 排序属性
      // 判断维度是否超过一个，超过设置为 DELETE_FLAG
      orderBySumMetrics: (
        (
          chartUserConfig.dimensionList.length < 2
        )
        &&
        (
          // 在列表中并且，不是组合图或是组合图但选中的是堆叠
          SORT_SETTING_CHART.includes(chartAlias)
          && (CHART_ALIAS_TYPE.VE_COMPOSITE !== chartAlias || chartUserConfig.compositeChart.stack === true)
          && chartUserConfig.sortType
        )
      ) || DELETE_FLAG,
    })
    previewHandler.call(this, ret.layers[0], el)

    // 组合图时为度量字段添加图形类型
    if (chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE) {
      (ret?.layers || []).forEach(item => {
        (item?.metrics || []).forEach(item => {
          if (chartUserConfig?.metricsContainer?.histogram.some(d => d.keyName === item.keyName)) {
            // 判断是堆叠图还是柱状图
            if (chartUserConfig?.compositeChart?.stack === true) {
              item.chartType = CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK
            } else {
              item.chartType = CHART_ALIAS_TYPE.VE_HISTOGRAM
            }
          } else if (chartUserConfig?.metricsContainer?.line.some(d => d.keyName === item.keyName)) {
            // 判断是线图还是面积图
            if (chartUserConfig?.compositeChart?.area === true) {
              item.chartType = CHART_ALIAS_TYPE.VE_LINE_AREA
            } else {
              item.chartType = CHART_ALIAS_TYPE.VE_LINE
            }
          }
        })
      })
    }

    Object.keys(ret).forEach(k => {
      if (ret[k] === DELETE_FLAG) delete ret[k]
    })

    // 双维度第一个维度添加保持排序参数，不会被3300需求的排序器清除
    if (DOUBLE_DIMENSION_CHART.includes(chartAlias) && ret.layers[0].dimension.length === 2) {
      ret.layers[0].dimension[0].retainOrder = true
    }

    const _compareMethod = compareMethod || chartUserConfig.compareMethod
    // 预警是否需要带上参数组件
    ret.layers[0].alterWithParamComp = !isChartSet
    // 预警是否需要带上维度(预警方式)
    ret.layers[0].alterWithDimension = chartAlias !== CHART_ALIAS_TYPE.VE_GAUGE && _compareMethod === 1

    handleCalendarParam.call(this, ret, isChartSet, chartUserConfig)

    handelRoundCascadeParam.call(this, ret, chartUserConfig)

    // 处理关联数据集数据
    associationDatasetParam.call(this, ret, el, chartUserConfig)
    // 预警线
    warningToAlterLogic.call(this, ret, chartUserConfig)
    // 辅助线数据
    auxiliaryLineParam.call(this, ret, el, chartUserConfig)

    // 处理日期维度格式设置
    handelDateDimensionFormatter.call(this, ret, chartUserConfig)

    // 删除对比值数据
    Reflect.deleteProperty(ret.layers[0], 'contrastList')

    // 处理度量汇总
    handleMesureConfig.call(this, ret, el.content)

    // 处理聚合过滤
    handlePolymericFilterConfig.call(this, ret, el.content)

    if (!HAS_TARGET_CHART.includes(chartAlias)) {
      delete ret.layers[0].gaugeTarget
    }

    // 中国地图筛选省份数据
    if (chartUserConfig.childChartAlias === 've-map-china' && chartUserConfig.mapRangeVal === 'provice') {
      let province = chartUserConfig.mapProviceVal || 'beijing'
      province = mapConstant.provinceList.find((v) => v.spell === province)?.name
      const provinceDimension = chartUserConfig.provinceField
        ? [Object.assign({}, chartUserConfig.provinceField, { columnName: chartUserConfig.provinceField.labeName })]
        : []
      ret.areaOutline = {
        province,
        provinceDimension
      }
    }
    // 把带宽度量添加进metrics中
    setBandwidthRequestData.call(this, ret, this.chartUserConfig)
    // 地图经纬度、tooltip维度度量
    // 把地图目的地维度字段过滤掉
    if (ret.layers[0]?.extendDimension?.length) {
      ret.layers[0].extendDimension = ret.layers[0].extendDimension.filter(d => !d._mapDestination)
    }
    if (chartAlias === 've-liquidfill' && liquidFillSetting.mode === VE_LIQUIDFILL_MODE.NORMAL) {
      delete ret.layers[0].dimension
    } else if (chartAlias !== 've-map-parent') {
      Object.assign(ret.layers[0], {
        hoverDimension: undefined,
        hoverMetrics: undefined,
      })
    }

    // 如果打开了地图飞线，则传入目的地维度
    const isRealScreen = this.utils.isScreen && !this.utils.isDataReport
    if (isRealScreen && chartAlias === 've-map-parent' && mapSetting.flyLine?.enable && chartUserConfig.dimensionDestinationList?.[0]) {
      const DIMENSION = createDimension()
      ret.layers[0].dimension = chartUserConfig.dimensionList.map(item => {
        return Object.assign({}, DIMENSION, {
          MAPTYPE: item.MAPTYPE,
          columnType: item.columnTpe,
          columnName: item.labeName,
          alias: item.alias || item.labeName,
          keyName: item.keyName,
        })
      })
      const e = chartUserConfig.dimensionDestinationList?.[0]
      const o = Object.assign({}, DIMENSION, {
        MAPTYPE: e.MAPTYPE,
        columnType: e.columnTpe,
        columnName: e.labeName,
        alias: e.alias || e.labeName,
        keyName: e.keyName,
        _mapDestination: true
      })
      ret.layers[0].extendDimension.push(o)
    }

    if (Array.isArray(ret.filters)) {
      ret.filters.forEach(retItem => {
        retItem.referToCalendar = ret.caculationMethodType === DATA_FILTER.targetCaculationMethod
        if (retItem.referToCalendar && ret.hasOwnProperty('disabledCalendarFnFilter')) {
          retItem.disabledCalendarFnFilter = ret.disabledCalendarFnFilter
        }
      })
    }

    // 处理0值显示
    handleZeroFormatParam.call(this, ret)
    datasetFieldHandler.call(this, ret, el)

    handleMetric.call(this, ret, metricLabelDisplay, ret.layers[0])

    decompositionRequestSet.call(this, ret, chartAlias, this.chartUserConfig)

    return ret
  },
  // 处理后台数据方法
  response(response, el, elList = [], params = {}) {
    // response = faseResponse
    let { chartSettings, chartUserConfig } = el.content
    const { chartConfig } = el.content
    const { chartResponse } = response
    if (!chartResponse) {
      return {}
    }

    // this._chartResponseTmp = this.$_deepClone(chartResponse)

    setAliasNameIntoChartSettings.call(this, chartSettings, el)
    const { chartAlias, metricLabelDisplay = [], metricsContainer = {}, dimensionList = [] } = chartUserConfig

    let { legendData, metrics = [], dimension = [], pageInfo, measurementNullValue, dimensionNullValue, auxLineResult = [], titleCalendarCacheMap, extendDims, factor, dataMaskRuleParamMap = {} } = chartResponse
    const hasOtherFieldChart = ['ve-scatter-normal', 've-liquidfill']
    if (!hasOtherFieldChart.includes(chartUserConfig.chartAlias) && metrics.length > chartSettings.metrics.length) {
      metrics.length = chartSettings.metrics.length
    }
    // 特殊处理仪表盘
    let columns = []
    if ((dimension === undefined) && chartUserConfig.chartType === 've-gauge') {
      columns = metrics
    } else {
      columns = dimension.concat(metrics)
    }
    // 将度量数据转为数值类型
    chartResponse.rows.map((item, i) => {
      // handleDesensibilisationRule(item, dataMaskRuleParamMap, metrics)
      metrics.map(metric => {
        if (dataMaskRuleParamMap[metric]) return
        if (!isNaN(item[metric])) {
          item[metric] = Number(item[metric])
        }
        // 增量数据字段
        const variationMetricName = `${metric}_Variation_`
        if (item.hasOwnProperty(variationMetricName)) {
          if (params?.source !== 'websocketMessage') {
            delete item[variationMetricName]
          } else if (!isNaN(item[variationMetricName])) {
            item[variationMetricName] = Number(item[variationMetricName])
          }
        }
        // 模拟数据
        // item[variationMetricName] = Math.floor(Math.random() * 100) * (i % 3 === 0 ? 0 : (i % 2 ? 1 : -1))
        // item[`VIEWFORMAT_${ variationMetricName }`] = `${item[variationMetricName]}.00`
      })
      if (item.hasOwnProperty('others') && Array.isArray(item.others) && item.others.length) {
        item.others.forEach(otherData => {
          // handleDesensibilisationRule(otherData, dataMaskRuleParamMap, metrics)
          metrics.map(metric => {
            if (dataMaskRuleParamMap[metric]) return
            if (!isNaN(otherData[metric])) {
              otherData[metric] = Number(otherData[metric])
            }
          })
        })
      }
    })
    Object.keys(auxLineResult).forEach(k => {
      handleDesensibilisationRule(auxLineResult[k], dataMaskRuleParamMap, metrics)
    })
    chartResponse.measureSummary && Object.keys(chartResponse.measureSummary).forEach(k => {
      handleDesensibilisationRule(chartResponse.measureSummary[k], dataMaskRuleParamMap, metrics)
    })
    // 保存响应数据
    Vue.set(el.content, 'chartResponse', {
      metrics,
      dimension,
      rows: chartResponse.rows,
      measurementNullValue,
      dimensionNullValue,
      auxLineResult,
      titleCalendarCacheMap,
      extendDims,
      hiddenKeys: chartResponse.hiddenKeys,
      measureSummary: chartResponse.measureSummary,
      dataMaskRuleParamMap: chartResponse.dataMaskRuleParamMap || {},
    })

    if (!titleCalendarCacheMap) {
      delete el.content.chartResponse.titleCalendarCacheMap
    }

    if (factor) {
      el.content.chartResponse.factor = factor
    }

    // 瀑布图在chartData中增加对比值、累计值数据
    let chartDataRow = chartResponse.rows
    const afterHandleRow = handleWaterfallData({ rows: chartDataRow, metrics, dimension, chartUserConfig, originalRowLen: chartDataRow.length })
    afterHandleRow && (chartDataRow = afterHandleRow)
    const chartData = {
      columns: columns || [],
      rows: chartDataRow || [],
    }
    // 保存图形预警数据
    Vue.set(el.content, 'alertResult', chartResponse.alertResult)

    // 对维度字段 数字化
    chartData.rows.forEach((row, index) => {
      for (const key in row) {
        if (metrics.includes(key)) {
          const data = row[key] === measurementNullValue ? 0 : (isNaN(row[key]) ? row[key] : parseFloat(row[key]))
          row[key] = typeof data === 'number' ? data : 0
        }
      }
      if (row.hasOwnProperty('others') && Array.isArray(row.others) && row.others.length) {
        row.others.forEach(otherData => {
          for (const key in otherData) {
            if (metrics.includes(key)) {
              const data = otherData[key] === measurementNullValue ? 0 : (isNaN(otherData[key]) ? otherData[key] : parseFloat(otherData[key]))
              otherData[key] = typeof data === 'number' ? data : 0
            }
          }
        })
      }
    })

    // 图表排序
    chartUserConfig.ChartVeBarNormal = false
    // 排序器
    // let isSorterDataActive = false
    // let sorterData = el.sorterData
    // if (el._containerId) {
    //   elList.forEach(elItem => {
    //     if (elItem.id === el._containerId) {
    //       sorterData = elItem.sorterData
    //     }
    //   })
    // }
    // sorterData && Array.isArray(sorterData.form) && sorterData.form.forEach(sortItem => {
    //   if (sortItem.active && sortItem.order) {
    //     isSorterDataActive = true
    //   }
    // })
    // const layers = el.content.drillSettings.layers[0]
    // // 维度排序
    // const dimensionOrder = layers.dimension.some(item => item.order) || el.content.drillSettings.drillDimensions.some(item => item.orderList && item.orderList.length)
    // // TODO 扩展维度排序也需要处理
    // let isFlag = layers.metrics.some(metricsItem => metricsItem.order) || isSorterDataActive || dimensionOrder
    // if (!isFlag && ['ve-bar-percent'].includes(chartUserConfig.chartAlias)) {
    //   let order = layers.gaugeTarget && layers.gaugeTarget.order
    //   isFlag = (order && order !== 'none') || isSorterDataActive || dimensionOrder
    // }
    // 条形图的展示echarts默认是从下往上顺序展示，产品要求条形图从上往下展示数据，所以将条形图的数据顺序反转
    if (isReverseAxisChart({ chartUserConfig })) {
      chartUserConfig.ChartVeBarNormal = true
      chartData.rows.reverse()
    }

    Vue.set(chartConfig, 'responsePageInfo', pageInfo)
    // 转换为想要的chartSettings
    // 处理多轴图形的chartSettings，兼容老看板
    const isCompositeChart = SUPPORT_DEPUTY_AXIS.includes(chartUserConfig.chartAlias)
    if (isCompositeChart && Object.keys(chartSettings).every(item => item !== 'showLine')) {
      Object.assign(chartSettings, {
        showLine: [],
        stack: {},
      })
    }

    let customMetric = []
    metricsContainer.default.forEach((m, i) => {
      const currentMetricLabel = metricLabelDisplay.find(ml => ml.keyName === m.keyName)
      if (currentMetricLabel && currentMetricLabel.labelType === 'custom' && currentMetricLabel.customMetricList?.length) {
        const customMetricArr = []
        currentMetricLabel.customMetricList.forEach(item => {
          customMetricArr.push(item.customMetricAlias)
        })
        // 存入自定义度量二维数组格式
        customMetric.push(customMetricArr)
      } else {
        customMetric.push(undefined)
      }
    })
    chartSettings.customMetric = customMetric
    chartSettings = handleSettings.call(this, chartSettings, chartSettings, el)
    // 无维度图形
    if (NO_DIMENSION_CHART.includes(chartUserConfig.chartAlias) && chartResponse.dimension === undefined) {
      chartSettings.dimension = []
    } else if ([CHART_ALIAS_TYPE.VE_DECOMPOSITION].includes(chartUserConfig.chartAlias)) {
      // chartSettings.dimension = response.chartResponse.dimension
    } else if (response.chartResponse.dimension[0] !== chartSettings.dimension[0]) {
      chartSettings.dimension[0] = response.chartResponse.dimension[0]
    }
    const originDimension = chartSettings.dimension

    // 处理图形xy轴最大值
    const needCaculate = [...HANDLE_MAX_MIN_VALUE_CHART, 've-scatter-normal'].includes(chartAlias)
    if (needCaculate) {
      if (chartAlias === 've-scatter-normal') {
        // 对后台请求结果进行处理
        const { symbolSizeMax, } = chartUserConfig
        // TODO 可以直接从chartResponse 中取dimension，metrics数据， 后期再改
        const { rows } = chartData

        /**
         * metrics[0] 横轴字段
         * newMetrics 数组的第一项展示为纵轴，第二项展示为散点大小, [xAxis, yAxis]
         */
        const group = chartResponse.dimension[1] || `VIEWFORMAT_${chartResponse.dimension[0]}`
        const { dimension, metrics } = chartResponse
        const chartSettings = {
          dimension: dimensionList.map(m => this.getDatasetLabel(m)),
          // dimension: dimension,
          xAxisType: 'value', // 散点图X坐标轴的默认类型
          metrics: metricsContainer.default.map(m => this.getDatasetLabel(m)),
          symbolSizeMax: symbolSizeMax || 50,
          originDimension,
        }
        const columns = [...dimension, ...metrics]
        // 提取维度分组
        const newChartData = {
          columns: columns,
          rows: (group && Array.isArray(rows)) ? rows.reduce((prev, row) => {
            const dimensionKey = row[group]
            prev[dimensionKey] ? prev[dimensionKey + ''].push(row)
              : (prev[dimensionKey + ''] = [row])
            return prev
          }, {}) : rows,
        }

        return {
          chartData: newChartData,
          chartSettings,
        }
      }
    }

    if (legendData) {
      chartData.legendData = legendData
    }
    // 处理度量汇总
    const measureConfig = chartUserConfig.measureConfig
    if (chartResponse.measureSummary) {
      if (chartSettings.dimension.length <= 1 && measureConfig && !['ve-bar-percent'].includes(chartUserConfig.chartAlias)) {
        const { measureSummaryValue, summaryList } = measureConfig
        if (chartSettings.metrics.length <= 1) {
          const arr = [ 'measureSummary', measureSummaryValue, chartResponse.metrics[0], 'VIEWFORMAT', 'Recently', chartResponse.dimension[0] ]
          const key1 = (arr[0].replace(/([A-Z])/g, '_$1').toLowerCase()).toUpperCase()
          const key2 = arr[1] === arr[4] ? arr[5] : arr[2]
          const key = `${key1 + '_' + arr[1] + '_' + key2}`
          if (!chartResponse.measureSummary[key]) {
            measureConfig.subtext = ''
          } else if (arr[1] === arr[4]) {
            measureConfig.subtext = chartResponse.measureSummary[key][`${arr[3] + '_' + arr[2]}`] + ';' + chartResponse.measureSummary[key][key2]
          } else {
            measureConfig.subtext = chartResponse.measureSummary[key][`${arr[3] + '_' + arr[2]}`]
          }
        } else {
          ;(summaryList || []).forEach(mItem => {
            const metricItem = (chartUserConfig.metricsContainer.default || []).find(m => m.keyName === mItem.keyName)
            if (!metricItem) return
            const arr = [ 'measureSummary', mItem.summaryValue, metricItem.alias || metricItem.labeName, 'VIEWFORMAT', 'Recently', chartResponse.dimension[0] ]
            const key1 = (arr[0].replace(/([A-Z])/g, '_$1').toLowerCase()).toUpperCase()
            const key2 = arr[1] === arr[4] ? arr[5] : arr[2]
            const key = `${key1 + '_' + arr[1] + '_' + key2}`
            if (!chartResponse.measureSummary[key]) {
              mItem.subtext = ''
            } else if (arr[1] === arr[4]) {
              mItem.subtext = chartResponse.measureSummary[key][`${arr[3] + '_' + arr[2]}`] + ';' + chartResponse.measureSummary[key][key2]
            } else {
              mItem.subtext = chartResponse.measureSummary[key][`${arr[3] + '_' + arr[2]}`]
            }
          })
        }
      }
      // const completionRate = chartResponse.measureSummary['MEASURE_SUMMARY_Sum_COMPLETION_RATE']
      // if (completionRate && chartUserConfig.liquidFillSetting) {
      //   chartUserConfig.liquidFillSetting.completionRate = completionRate
      // }
    } else {
      if (measureConfig) measureConfig.subtext = ''
    }

    if (el?.vm?.animationPlay) {
      el.vm.animationPlay({ type: 'reset', chartData, from: 'if (el?.vm?.animationPlay) {' })
    }
    // TODO 处理图形预警设置

    return {
      chartData,
      chartSettings
    }
  },
  renderFns(el, data) {
    // Vue.set(el.vm, 'response', data)
    Vue.set(el, 'content', { ...el.content, ...data })
  }
}
function handleDesensibilisationRule(dataItem, dataMaskRuleParamMap = {}, fieldList = []) {
  const _fieldList = Array.from(new Set([...fieldList, 'COMPLETION_RATE']))
  _fieldList.forEach(k => {
    if (dataMaskRuleParamMap[k]) {
        delete dataItem[k]
        delete dataItem[`VFMT_RAW_${ k }`]
        delete dataItem[`VIEWFORMAT_${ k }`]
        delete dataItem[`${k}_Variation_`]
        delete dataItem[`VFMT_RAW_${k}_Variation_`]
        delete dataItem[`VIEWFORMAT_${k}_Variation_`]
    }
  })
  return dataItem
}
function setAliasNameIntoChartSettings(chartSettings, element) {
  const { metricsContainer = {}, extendDimensionList = [], dimensionList = [], gaugeTarget = {} } = element.content.chartUserConfig
  if (Array.isArray(metricsContainer.default)) {
    chartSettings.metrics = metricsContainer.default.map(e => this.getDatasetLabel(e))
  }
  if (extendDimensionList.length) {
    this.$set(chartSettings, 'extendDimension', extendDimensionList.map(item => this.getDatasetLabel(item)))
  }
  if (dimensionList.length) {
    chartSettings.dimension = dimensionList.map(e => this.getDatasetLabel(e))
  }
  if (gaugeTarget.defaults?.labeName) {
    chartSettings.target = [this.getDatasetLabel(gaugeTarget.defaults)]
  }
}
/**
 * @description 切换到新的图形时, 生成新的 chartSettings
 * @param  {String} 图表名称
 * @param  {Object} 图表的设置参数
 * @param  {Object} 额外参数的数据
 * @returns {Object} 图表的设置项
 */
export function handleSettings(oldChartSettings, defaultChartSettings, el) {
  if (Object.prototype.toString.call(defaultChartSettings) !== '[object Object]' ||
    Object.prototype.toString.call(oldChartSettings) !== '[object Object]') return oldChartSettings
  const isCompositeChart = SUPPORT_DEPUTY_AXIS.includes(chartUserConfig.chartAlias)
  const newChartSetting = Object.keys(defaultChartSettings).reduce((pre, key) => {
    if (key === 'stack' || key === 'showLine') {
      pre[key] = handleExtraSettings.call(this, key, el, isCompositeChart)
    } else if (oldChartSettings[key] === undefined) {
      pre[key] = deepClone(defaultChartSettings[key])
    } else {
      pre[key] = deepClone(oldChartSettings[key])
    }
    return pre
  }, {})
  if (isCompositeChart) {
    const { compositeChart } = chartUserConfig
    newChartSetting.area = chartUserConfig.chartAlias === 've-line-area' || compositeChart.area
    newChartSetting.axisSite = { right: newChartSetting.showLine ? [...newChartSetting.showLine] : [] }
  }
  return newChartSetting
}

export function handleExtraSettings(key, element, isCompositeChart) {
  const { chartUserConfig: { maxStack, metricsContainer, compositeChart }, chartData: { columns } } = element.content
  let setting
  const metrics = [...columns]
  metrics.splice(0, metrics.length - metricsContainer.default.length)
  const getDatasetLabel = element.vm ? element.vm.getDatasetLabel : this.getDatasetLabel
  switch (key) {
    case 'stack': {
      // 如果是组合图形,使用组合图形柱轴
      const columns = isCompositeChart ? metricsContainer.histogram || [] : metricsContainer.default
      if ((isCompositeChart && compositeChart.stack) || !isCompositeChart) {
        // 如果有堆叠设置,将 userConfig 的设置转为图表配置
        setting = columns.reduce((prev, column, index) => {
          const lastPrev = prev[prev.length - 1]
          // 判断最大堆叠数量
          const stackName = getDatasetLabel(column)
          if (lastPrev.length >= maxStack) {
            prev[prev.length] = [stackName]
          } else {
            lastPrev.push(stackName)
          }
          return prev
        }, [[]])
      } else {
        setting = []
      }
      break
    }
    case 'showLine': {
      // 如果是组合图形,使用组合图形线轴
      const columns = isCompositeChart ? metricsContainer.line || [] : metricsContainer.default
      setting = columns.map(column => getDatasetLabel(column))
      break
    }
  }
  return setting || []
}
// 需要设置度量汇总的图形
export function isSetMeasureConfig(content) {
  const { chartAlias, extendDimensionList = [] } = content.chartUserConfig
  const { dimension } = content.chartSettings
  const isGraph = GRAPHARR.includes(chartAlias)
  return isGraph && dimension.length === 1 && !extendDimensionList.length

}

function handleMesureConfig(ret, content) {
  if (isSetMeasureConfig.call(this, content)) {
    const { chartUserConfig, chartSettings } = content
    const { measureSummaryValue, ignoreDimension = false, summaryList = [] } = chartUserConfig.measureConfig || {}
    if (chartSettings.metrics.length === 1) {
      const rule = [{
        rule: measureSummaryValue || 'Sum',
        noDim: ignoreDimension,
      }]
      Object.assign(ret, { measureSummary: rule })
    } else if (summaryList.length && MULTI_GRAPHARR.includes(chartUserConfig.chartAlias)) {
      const metricAll = chartUserConfig.metricsContainer.default || []
      // 先筛掉已经删掉的度量设置项
      const newSubmmaryList = summaryList.filter(su => {
        return !!metricAll.find(m => su.keyName === m.keyName)
      })
      const rules = newSubmmaryList.map(item => {
        const m = metricAll.find(me => me.keyName === item.keyName)
        return {
          rule: item.summaryValue,
          noDim: item.ignoreDimension,
          // 如果类型是Rencently则要传递维度字段别名
          metricsAlias: item.summaryValue === 'Recently' ? chartSettings.dimension : [m.alias || m.labeName]
        }
      })
      if (newSubmmaryList.length !== summaryList.length) {
        this.$set(chartUserConfig.measureConfig, 'summaryList', newSubmmaryList)
      }
      Object.assign(ret, { measureSummary: rules })
    } else {
      delete ret.measureSummary
    }
  } else {
    delete ret.measureSummary
  }
}

// 处理聚合过滤参数，根据聚合过滤配置获取对应的度量的配置，组合成参数metricsFilters
function handlePolymericFilterConfig(ret, content) {
  // console.log('9489> ', ret, content)
  const { drillSettings = {} } = content
  if (!drillSettings?.layers?.[0]?.metrics?.length) return
  if (!drillSettings?.polymericFilters?.length) return

  const polymericFilters = drillSettings.polymericFilters || []
  const metrics = drillSettings.layers[0].metrics
  const metricsFilters = []

  polymericFilters.forEach(item => {
    const target = metrics.find(e => e.keyName === item.metric)
    if (target) {
      metricsFilters.push({
        dataSetId: target.dataSetId,
        groupFilterType: item.groupFilterType,
        columnName: target.alias || target.labeName,
        columnType: target.columnType,
        filterType: item.filterType,
        filterValueType: item.filterValueType,
        values: [String(item.values)],
        parentId: target.parentId,
        referToCalendar: false
      })
    }
  })
  Object.assign(ret, { metricsFilters })

  delete ret.polymericFilters
}

// 更新聚合过滤数据
export function updatePolymericFilters(drillSettings) {
  if (!drillSettings?.layers?.[0]?.metrics?.length) return

  const polymericFilters = drillSettings.polymericFilters || []
  if (!polymericFilters || !polymericFilters?.length) return

  const metrics = drillSettings.layers[0].metrics
  const keyNameList = metrics.map(e => e.keyName)
  // 过滤为当前度量的聚合过滤配置
  const newList = polymericFilters.filter(e => keyNameList.includes(e.metric))
  const valid = JSON.stringify(polymericFilters) !== JSON.stringify(newList)
  if (valid) {
    // 将第一条的对比方式改为and 对应bug70686
    if (newList.length) {
      newList[0].groupFilterType = 'and'
    }
    Vue.set(drillSettings, 'polymericFilters', Vue.prototype.$_JSONClone(newList))
  }
}

function handleCalendarParam(ret, isChartSet, chartUserConfig) {
  if (chartUserConfig.chartAlias === 've-calendar') {
    let isBindParam = false
    const content = this?.getCurrentParamsPanel?.('content') || []
    if (!isChartSet && Array.isArray(content)) {
      const bindParamArr = ['CalendarQuick', 'DateQuickOperation', 'bussinessCalendar', 'financialCalendar']
      content.some(e => {
        const isBind = bindParamArr.includes(e.type) && e.content.bindElement.includes(ret.id)
        if (isBind) {
          isBindParam = isBind
        }
        return isBind
      })
    }
    let range = chartUserConfig.calendarSettings.date
    if ((!range && isChartSet) || (!isBindParam && !isChartSet)) {
      const time = new Date()
      const year = time.getFullYear()
      let month = time.getMonth() + 1
      month = month < 10 ? `0${month}` : month
      const date = new Date(year, month, 0).getDate()
      range = [`${year}-${month}-01`, `${year}-${month}-${date}`]
    }
    range = range ? range.map(e => e + ' 00:00:00') : undefined
    Object.assign(ret, {
      dtList: (isBindParam && !isChartSet) ? undefined : range,
    })
  } else {
    Object.assign(ret, {
      dtList: undefined,
    })
  }
}

// 层叠圆形图默认降序
function handelRoundCascadeParam(ret, chartUserConfig) {
  if (chartUserConfig.chartAlias === 've-roundCascades') {
    const { dimension } = ret.layers[0]
    dimension.map(dimensionItem => {
      Reflect.deleteProperty(dimensionItem, 'order')
      Reflect.deleteProperty(dimensionItem, 'orderList')
    })
    Reflect.deleteProperty(ret.layers[0], 'orderList')
  }
}

// 存在维度的瀑布图需要增加对比值或累计值来兼容维度颜色
export function handleWaterfallData({ rows, metrics, dimension, chartUserConfig, originalRowLen }) {
  const { chartAlias, waterfallSetting, dimensionList, contrastList = [] } = chartUserConfig
  if (chartAlias !== 've-waterfall' || !waterfallSetting || !dimensionList.length || !rows.length) return
  let rowsCopy = deepClone(rows)
  const keys = ['accumulative', 'contrast']
  const dimensionAlias = dimension[0]
  keys.forEach((key) => {
    const metricsAlias = key === 'accumulative' ? metrics[0] : contrastList[0]?.alias || contrastList[0]?.labeName
    if (!metricsAlias) return
    rowsCopy = handleWaterfallRows({ rows: rowsCopy, metricsAlias, dimensionAlias, key, waterfallSetting, originalRowLen })
  })
  return rowsCopy
}

function handleWaterfallRows({ rows, metricsAlias, dimensionAlias, key, waterfallSetting, originalRowLen }) {
  const rowFlag = key === 'accumulative' ? 'isAccumulative' : 'isContrast'
  // 累计值保存在最后一条数据，对比值保存在第一条数据
  const rowsIndex = key === 'accumulative' ? rows.length : 0
  if (!waterfallSetting[key].show) {
    rows = rows.filter(rowItem => !rowItem[rowFlag])
    return rows
  }
  const addRow = rows.find(rowItem => rowItem[rowFlag])
  if (!addRow) {
    const totalStr = `VFMT_TOTAL_${metricsAlias}`
    const totalFormatStr = `VIEWFORMAT_TOTAL_${metricsAlias}`
    rows.splice(rowsIndex, 0, {
      [rowFlag]: true,
      [dimensionAlias]: waterfallSetting[key].name,
      [`VIEWFORMAT_${dimensionAlias}`]: waterfallSetting[key].name,
      [totalStr]: rows[originalRowLen - 1][totalStr],
      [totalFormatStr]: rows[originalRowLen - 1][totalFormatStr],
    })
  } else {
    addRow[dimensionAlias] = waterfallSetting[key].name
  }
  return rows
}

export function transfromZeroFormat(item) {
  if (item && item.viewFormat && item.viewFormat.hasOwnProperty('zeroFormat')) {
    const zeroFormat = item.viewFormat.zeroFormat
    if (!zeroFormat || zeroFormat === 'none') {
      delete item.viewFormat.zeroFormat
    } else {
      item.viewFormat.zeroFormat = (typeof zeroFormat === 'boolean' && zeroFormat) || zeroFormat === 'open'
    }
  }
}

function handleZeroFormatParam(ret) {
  const paramsList = ['metrics', 'gaugeTarget', 'bandwidthReport']
  paramsList.forEach(key => {
    const params = ret.layers[0]?.[key]
    if (!params) return
    Array.isArray(params) ? params.forEach(item => transfromZeroFormat(item)) : transfromZeroFormat(params)
  })
}

function datasetFieldHandler(ret, el) {
  const _this = el.vm || this || {}
  if (ret.dataSetJoinsColumns && Array.isArray(ret.dataSetJoinsColumns)) {
    ret.dataSetJoinsColumns = ret.dataSetJoinsColumns.map(m => this.getCustomDatasetFieldDetail(m, TYPE_ELEMENT.CHART))
    Object.keys(ret.layers[0]).forEach(k => {
      if (ret.layers[0][k] && Array.isArray(ret.layers[0][k])) {
        ret.layers[0][k].forEach(m => {
          delete m.form
          delete m.to
          delete m.interval
        })
      }
    })
  } else {
    if (_this.element?.id !== ret.id || !_this.element?.content) return
    Object.keys(ret.layers[0]).forEach(k => {
      if (ret.layers[0][k] && Array.isArray(ret.layers[0][k])) {
        const layersKey = ret.layers[0][k].map(m => this.getCustomDatasetFieldDetail(m, TYPE_ELEMENT.CHART) || m)
        ret.layers[0][k] = layersKey
        _this.element.content.drillSettings.layers[0][k] = layersKey
      } else if (ret.layers[0][k]?.keyName || ret.layers[0][k]?.metricGroupInfo) {
        const layersKey = this.getCustomDatasetFieldDetail(ret.layers[0][k], TYPE_ELEMENT.CHART) || ret.layers[0][k]
        ret.layers[0][k] = layersKey
        _this.element.content.drillSettings.layers[0][k] = layersKey
      }
    })
  }
}

function auxiliaryLineParam(ret, el, chartUserConfig) {
  const { xAuxiliaryLineData = [], yAuxiliaryLineData = [], metricsContainer = {}, chartAlias } = chartUserConfig
  let metricList = metricsContainer?.default || []
  const auxiliaryLineList = [...deepClone(xAuxiliaryLineData)]
  const isMoreAuxiliaryLine = ['ve-composite', 've-scatter-normal'].includes(chartAlias) || (SPECIAL_SUPPORT_DEPUTY_AXIS.includes(chartAlias) && metricsContainer.hasOwnProperty('line'))
  if (isMoreAuxiliaryLine) {
    auxiliaryLineList.push(...deepClone(yAuxiliaryLineData))
  }
  if (auxiliaryLineList.length) {
    const auxLineMetrics = []
    const customAuxLineMetrics = []
    const formatList = {
      'normal': 'convention',
      'number': 'numerical',
      'percent': 'percentage',
      'currencyUnit': 'currencyUnit',
    }
    // 不需要作为参数的key
    const ignoreKey = ['color', 'currencyFlag', 'decimalNum', 'fieldCalcType', 'format', 'lineType', 'lineWidth',
      'metricValue', 'modelType', 'name', 'valueType', 'fieldType', 'otherMetricValue', 'filed1', 'filed2', 'customCalType', 'auxiliaryLineType']

    let size = metricsContainer?.size?.[0]
    // 散点图需要过滤size轴
    if (size) {
      metricList = metricList.filter(item => item.alias !== size.alias)
    }

    auxiliaryLineList.forEach(item => {
      // 辅助线选择度量字段时，需要将度量字段的度量函数值作为辅助线传参
      if (item.metricValue !== 'otherField') {
        metricList.forEach(metricItem => {
          if (item.metricValue === (metricItem.alias || metricItem.labeName)) {
            const metricCascaderKey = ['alias', 'labeName', 'columnTpe', 'aggType', 'type', 'bFxType', 'lgeType', 'expression', 'exp', 'compreInfo']
            Object.keys(metricItem).forEach(key => {
              if (metricCascaderKey.includes(key)) {
                item[key] = key === 'aggType' ? getAggType(metricItem) : metricItem[key]
              }
              // 对比数据传参没有保存在metricsContainer中
              if (metricItem.aggType === 'CONTRAST') {
                Object.assign(item, {
                  compareInfo: metricItem.selectedConttast ? { compSubType: metricItem.selectedConttast } : {},
                  compareRule: metricItem.selectedConttastMode ? metricItem.selectedConttastMode : '',
                })
              }
            })
          }
        })
      }
      const { columnTpe, decimalNum = 2, format, currencyFlag, valueType, fieldType, labeName, auxiliaryLineType, customCalType } = item
      const formatKey = formatList[format] || 'currencyUnit'
      let line = {
        viewFormat: {
          [formatKey]: { decimalNum },
        },
      }
      if (format === 'normal') {
        line.viewFormat[formatKey] = format
      }
      if (auxiliaryLineType === 'custom') {
        Object.assign(line, {
          id: item.id,
          calculateType: customCalType,
          columns: [item.filed1, item.filed2],
          parentId: item.parentId,
        })
        customAuxLineMetrics.push(line)
      } else {
        Object.assign(line, {
          columnType: columnTpe,
          columnName: labeName,
          auxLineCalcType: valueType === 'average' ? 'Avg' : valueType === 'max' ? 'Max' : 'Min',
          fieldType: fieldType === 'fixedValue' ? 'fixed' : 'calculate',
          parentId: item.parentId,
        })
        Object.keys(item).forEach(key => {
          if (!ignoreKey.includes(key)) {
            line[key] = item[key]
          }
        })
        auxLineMetrics.push(line)
      }
    })
    ret.layers[0].auxLineMetrics = auxLineMetrics
    ret.layers[0].customAuxLineMetrics = customAuxLineMetrics
  }
}

function handelDateDimensionFormatter(ret, chartUserConfig) {
  const { dateFormatterSetting, dimensionList = [], needYearsMerge } = chartUserConfig
  const dateDimensionIndex = dimensionList.findIndex(d => d.columnTpe === 'date' && d.timeDimensionSplittingRule && d.timeDimensionSplittingRule !== '_vs_none')
  if (dateDimensionIndex !== -1 && dateFormatterSetting) {
    const dateDimension = ret.layers[0].dimension[dateDimensionIndex]
    if (!dateDimension.viewFormat) { dateDimension.viewFormat = {} }
    const dateMap = {
      'customize_day_vs_day': 'dateCustomFormat',
      'week_vs_week': 'weekCustomFormat',
      'month_vs_month': 'monthCustomFormat',
      'quarter_vs_quarter': 'quarterCustomFormat',
      'half_year_vs_half_year': 'halfYearCustomFormat',
      'year_vs_year': 'yearCustomFormat',
    }
    const key = dateMap[dateDimension.timeDimensionSplittingRule]

    const viewFormat = dateDimension.viewFormat

    // 删除原先的传参
    delete viewFormat.dateCustomFormat
    delete viewFormat.timeCustomFormat

    dateFormatterSetting[key] && Object.assign(viewFormat, {
      [key]: dateFormatterSetting[key]
    })
    dateDimension.needYearsMerge = needYearsMerge
    // 按天时可以设置时间格式
    if (dateDimension.timeDimensionSplittingRule === 'customize_day_vs_day') {
      dateFormatterSetting['timeCustomFormat'] && (viewFormat.timeCustomFormat = dateFormatterSetting['timeCustomFormat'])
    }
  }
}

function associationDatasetParam(ret, el, chartUserConfig) {
  const { associatedData, dataset } = el.content
  if (!chartUserConfig.datasetAssociation || !associatedData || !Object.keys(associatedData).length) return
  const { sqlJoins, dataSetJoinsColumns, dataSetJoins, associatedDataset = {}, dataSetJoinsType = '2' } = deepClone(associatedData)
  // 当结果数据集字段未设置别名时，需要给一个默认值
  dataSetJoinsColumns.forEach(item => {
    if (!item.alias) {
      item.alias = item.labeName || item.columnName
    }
    if (item.customExpressionSet) {
      item.columnName = item.customExpressionSet.expression
      item.exp = item.customExpressionSet.expression
      item.aggType = 'expression'
    }

  })
  let param = {
    joinDataSetId: associatedDataset.id,
    dataSetJoinsColumns,
    dataSetJoinsType,
  }
  if (dataSetJoinsType === '2') {
    Object.assign(param, {
      sqlJoins,
      dataSetJoins: [dataSetJoins],
    })
  }
  Object.assign(ret, param)
}

export function warningToAlterLogic(ret, chartUserConfig) {
  const { warnLineSettingList = [], dimensionValueIndicatorWarningList = [], chartAlias, warningMethod = {}, dimensionWarningList = [] } = chartUserConfig
  let warningLineArray = []
  if (WARNING_LINE_CHART.includes(chartAlias) && warnLineSettingList.length) {
    warningLineArray.push(...warnLineSettingList)
  }
  if (DIMENSION_VALUE_INDICATOR_WARNING_CHART.includes(chartAlias) && warningMethod.dimensionMethod === 4) {
    dimensionValueIndicatorWarningList.length && warningLineArray.push(...dimensionValueIndicatorWarningList)
  }
  // ret.layers[0].auxLineMetrics = auxLineMetrics
  const hasWarningFlag = chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION && dimensionWarningList.length
  if (warningLineArray.length > 0 || hasWarningFlag) {
    // 过滤不要的预警线的数据
    const filterKeyArr = ['fieldCalcTypeList', 'warnLineColor', 'chartColor', 'matchField', 'matchFieldCascader']
    let _alterLogic = warningLineArray.map(warningItem => {
      let result:any = {}
      const webWarningId = warningItem.id

      Object.keys(warningItem).forEach((key) => {
        if (filterKeyArr.includes(key)) return
        if (key === 'dimensionSetting') {
          result.alertDimensions = warningItem[key].map(ds => {
            const targetDimension = chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION ? ret.layers[0].dimension.find(e => e.keyName === ds.fieldKeyName) : null
            return {
              dimensionAlias: ds.fieldAlias || ds.fieldColumnName,
              values: ds.selectValues.map(v => v.code),
              joinType: ds.joinType,
              columnType: targetDimension ? targetDimension.columnType : undefined
            }
          })
        } else {
          result[key] = warningItem[key]
        }
      })
      if (Number(warningItem.compareMethod) === 4) {
        Object.assign(result, warningItem.matchField)
      }
      result.id = webWarningId
      result.parentId = warningItem.parentId
      result.dataSetId = warningItem.parentId
      return result
    })

    if (chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION) {
      _alterLogic.forEach(item => {
        if (item.columnTpe) {
          item.columnType = item.columnTpe
        }
        if (item.calcType === '=') {
          item.calcType = '=='
        }
      })
      const calcDIC = {
        'exactMatch': 'in',
        '=': 'in',
        '≠': 'not_in',
        '!=': 'not_in',
        [`like '%string%'`]: 'like',
        [`not like '%string%'`]: 'not_like'
      }
      dimensionWarningList.forEach(w => {
        _alterLogic.push(...w.warningList.map(item => {
          const targetDim = ret.layers[0].dimension.find(e => e.keyName === item.fieldKeyName)
          return {
            ...item,
            "alertType":"dimension",
            id: item.id,
            calcType: calcDIC[item.calcType] || item.calcType,
            fieldCalcType: '',
            metric: w.metricAlias,
            dimension: targetDim?.alias || targetDim?.columnName || item.fieldAlias,
            columnType: item.fieldColumnType,
            percent: 100,
            values: item.matchArray.length ? item.matchArray.map(e => e.code) : item.matchValue.split(';'),
            compareMethod: 3,
            filedType: 'fixed',
            parentId: item.parentId,
          }
        }))
      })

    }
    ret.layers[0].alterLogic = _alterLogic
  } else {
    delete ret.layers[0].alterLogic
  }
}

// 处理度量总占比
export function handleMetric(ret, metricLabelDisplay, layers) {
  if(!layers.metrics || !layers.metrics.length || !metricLabelDisplay.length) return false
  let _metrics: Array<String> = []
  layers.metrics.map(m => {
    let _arr = metricLabelDisplay.filter(l => l.keyName === m.keyName && l.origin?.showMetricTotalPercent)
    if(_arr?.length) {
      _metrics.push(m.alias)
    }
  })
  ret.metricListAllDimPercent = _metrics
}

export function decompositionRequestSet(ret, chartAlias, chartUserConfig) {
  if (chartAlias !== CHART_ALIAS_TYPE.VE_DECOMPOSITION) return
  if (ret.hasOwnProperty('maxCountNum')) {
    delete ret.maxCountNum
    // ret.maxCountNum = 2000
  }
  if (ret.hasOwnProperty('pageInfo')) {
    delete ret.pageInfo
  }
  if (ret.hasOwnProperty('maxCountDimension')) {
    delete ret.maxCountDimension
  }
  if (chartUserConfig?.decompositionSetting?.proportionBasisType === PROPORTION_BASIS_TYPE.MAX) {
    ret.metricMaxPercent = true
  }

  const dimensionList = ret.layers[0].dimension

  const decomposition = this.$refs.decomposition
  if (!decomposition) {
    const decompositionSetting = chartUserConfig.decompositionSetting
    if (decompositionSetting) {
      const { defaultSelectList, runtimeConfig } = decompositionSetting
      const dimensionListSort = []

      const previewType = this.commonData?.previewType?.() || this.previewType
      const isRuntime = !this.isChartSet && this.levelData.userLevel === '2' && previewType === PREVIEW_STATUS.BROWSE
      // const isRuntime = !this.isChartSet && this.levelData.userLevel === '2'
      if (isRuntime && runtimeConfig) {
        const content = runtimeConfig.content
        const listTmp = JSON.parse(content)
        listTmp.forEach(item => {
          const target = dimensionList.find(e => e.keyName === item.dimensionKeyName)
          if (target) {
            dimensionListSort.push({
              ...target,
              order: 'asc',
            })
          }
        })
      } else {
      defaultSelectList.forEach(item => {
        const target = dimensionList.find(e => e.keyName === item.dimensionKeyName)
        if (target) {
          dimensionListSort.push({
            ...target,
            order: 'asc',
            // order: item.order
          })
        }
      })
      }

      if (dimensionListSort.length) {
        ret.layers[0].dimension = dimensionListSort
      }
    }
    return
  }

  const data = decomposition.getAllConfig()

  const { dimensionSetList = [] } = data

  // 根据已选择的数据进行数据取值
  const dimensionListSort = []
  // dimensionKeyNameList.forEach(keyName => {
  //   const target = dimensionList.find(e => e.keyName === keyName)
  //   target && dimensionListSort.push(target)
  // })
  dimensionSetList.forEach(item => {
    const target = dimensionList.find(e => e.keyName === item.dimensionKeyName)
    if (target) {
      dimensionListSort.push({
        ...target,
        order: 'asc',
        // order: item.order
      })
    }
  })

  // 如果没选择任何数据，则走全部，原因：空维度时没有汇总值
  if (dimensionListSort.length) {
    ret.layers[0].dimension = dimensionListSort
  }
  // console.log('decompositionRequestSet', data, dimensionListSort)
}

export default {
  element,
  adapter,
  ElContent,
  ElStyle,
  ElLayout,
  ElStoreLayout,
  ElStyleConfig,
}
