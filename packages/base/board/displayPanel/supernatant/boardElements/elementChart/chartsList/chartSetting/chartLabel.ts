import { getProp, getFormatterFunc } from 'packages/assets/utils/globalTools'
import { getThemeConfig } from '../theme'
import { getTooltipLabelData, setCORSImg } from './handleChartSetting'
import Imgs from '../imgs'
import { getWarningMetrics, setWarnColor, getWarningArray } from './chartWarn'
import mapConstant, { findLangProvinces } from '../mapConstant'
import {
  CHART_ALIAS_TYPE,
  ANIMATION_CHART,
  DISPLAY_LABEL_PERCENT,
  METRIC_POSITION_CHART,
  CUSTOM_METRIC,
  LABEL_SHOW_LINES,
  CONTROL_MEASURES_CHART,
  ALLOW_LABEL_OVERLAP_CHART,
  FOLLOWS_DIMENSION_COLOR,
  HOLIDAY_STYLE_CHART,
  ALLOW_METRIC_SPLIT_DISPLAY_CHART,
  ALLOW_WARNING_ICON_CONFIG_CHART
} from '../../constant'
import * as chartApi from '../../api'
import { getNumber, getDataPercentContent } from '../charts'

const STR = 'VIEWFORMAT'
const parentStrKey = 'METRIC_PERCENT_DIM_'
const childStrKey = 'METRIC_PERCENT_'

interface LabelThemeConfigParam {
  vm: any,
  chartUserConfig: any,
  isCalendar?: boolean,
}

interface LabelPositonParam {
  vm: any,
  serieItem: any,
  chartUserConfig: any,
  labelThemeConfig: any,
  currentMetricLabel?: any,
  isCalendar?: boolean,
  isLabelCenter?: boolean,
  funnelThemeConfig?: any,
  treeThemeConfig?: any,
}

interface LabelValueParam {
  serieItem: any,
  chartUserConfig: any,
  rows: any,
  columns: any,
  metrics: any,
  isLabelLogogram: boolean,
  labelData: any,
  value: string | number,
  data: any,
  formatFunc: Function,
  currentMetricLabel: any,
  name: string,
  isMap?: boolean,
  isFunnel?: boolean,
  isCalendar?: boolean,
  isShowLabelChart?: boolean,
}

export function getBarPercent(serieItem, currentRow) {
  // 百分比堆叠柱状图的占比由后台计算
  // 解决currentRow为undefind时报错的问题
  const percent =  currentRow ? currentRow[`VIEWFORMAT_ABSMETRIC_PERCENT_DIM_${serieItem.originalName}`] : undefined 
  if (percent) return percent
  if (!serieItem.metricTotal) return ''
  // 总占比由后台计算
  if(serieItem.metricTotal?.showMetricTotalPercent && currentRow) return currentRow[`VIEWFORMAT_METRIC_ALLDIM_PERCENT_${serieItem.originalName}`] || ''
  const total = serieItem.metricTotal.dimension?.length > 1 && currentRow ? serieItem.metricTotal.dimensionTotal?.[currentRow[serieItem.metricTotal.dimension?.[0]]] : serieItem.metricTotal.total
  const cValue = (!currentRow || isNaN(currentRow[serieItem.metricTotal.alias]) ? 0 : Number(currentRow[serieItem.metricTotal.alias]))
  return (total && cValue !== 0) ? (Math.round(cValue * 10000 / total) / 100 + '%') : '0.00%'
}

const labelFormatterConfig = {
  bar({ _value, currentRow, serieItem, labelData }) {
    let strArr = []
    if (serieItem.metricTotal?.show) {
      strArr.push(_value)
    }
    let percentStr = getDataPercentContent(serieItem, currentRow, labelData)

    if (serieItem.metricTotal?.showPercentLabel && percentStr) {
      strArr.push(percentStr)
      return strArr.join('\n')
    }
    return _value
  },
  treemap({ name, isDoubleDimension, labelData, dimension, metrics, labelLineShow, _value }) {
    let treemapStr = isDoubleDimension ? `${labelData.data[`${STR}_${dimension[0]}`]}-${name}` : `${name}`
    if (labelLineShow && (this.labelLineButton || !this.commonData.isPreview)) {
      const percent = labelData.data[`${STR}_${childStrKey}${metrics[0]}`] || ''
      treemapStr = `${treemapStr}\n${_value}\n${percent}`
    }
    if (isDoubleDimension && !labelData.data[`${STR}_${dimension[0]}`]) {
      treemapStr = ''
    }
    return treemapStr
  },
  sunburst({ name, labelLineShow, labelData, metrics, _value, chartUserConfig }) {
    if (name) {
      // 是否父级
      const isParent = labelData.data.hasOwnProperty('children')
      // 旭日图是否显示数值
      const showData = labelLineShow && (this.labelLineButton || !this.commonData.isPreview)

      let configArray = {
        labelLineShow: { show: true, value: name, },
        dataShow: { show: showData && !isParent, value: _value, },
        percentageShow: { show: showData, value: labelData.data[`${STR}_${childStrKey}${metrics[0]}`], },
      }

      return getShowLinesLabel(chartUserConfig, configArray)
    }
    return ''
  },
  pie({ name, labelLineShow, dataShow, percentageShow, percent, _value, chartUserConfig, seriesIndex, options, value, serieItem, currentRow, labelData, }) {

    let percentVal = getDataPercentContent(serieItem, currentRow, labelData)

    let configArray = {
      labelLineShow: { show: labelLineShow, value: name, },
      dataShow: { show: dataShow, value: _value, },
      percentageShow: { show: percentageShow, value: percentVal, },
    }
    return getShowLinesLabel(chartUserConfig, configArray)
  },
  scatter({ isMap, _value, chartUserConfig, labelData, dataValueShowResult, }) {
    const { mapMode = 'administrativeRegional', longitudeAndLatitudeNameShow, latitudeList = [], longitudeList = [], mapSetting = {}, childChartAlias } = chartUserConfig
    const isLongitudeAndLatitude = mapMode === 'longitudeAndLatitude'
    // 只有地图会走进来（散点图已经抽出去了）
    let _labelResult: any = []
    if (isMap) {
      const showAnthorName = isLongitudeAndLatitude && longitudeAndLatitudeNameShow && latitudeList.length && longitudeList.length
      if (mapSetting.showDimensionValue && ['ve-map-china', 've-map-britain'].includes(childChartAlias)) {
        _labelResult.push(`{regionStyle|${ labelData.name }}`)
      }
      if (showAnthorName) {
        _labelResult.push(labelData.data.value[0])
        _labelResult.push(labelData.data.value[1])
      }
      if (dataValueShowResult) {
        _labelResult.push(_value)
      }
    }
    return _labelResult.join('\n')
  },
  map() {
    return ''
  },
  mapScatter({ labelData, _value, chartUserConfig, options, vm, serieItem }) {
    const { mapType, mapSetting = {}, childChartAlias } = chartUserConfig
    if (mapType !== 'HEATMAP' || serieItem.type === 'map') return _value
    const { onlyShowHasValue, labelLineShow, regionName } = options.mapLabelShow
    const hasValue = serieItem.type === 'map'
                ? serieItem.data.find(dataItem => dataItem.name.indexOf(labelData.name) > -1)
                : (labelData.data !== undefined)
    const showMapRegionName = serieItem.type === 'map' && ((onlyShowHasValue && hasValue) || (!onlyShowHasValue && regionName.show))
    let result: string[] = []

    if (mapSetting.showDimensionValue && hasValue && childChartAlias !== 've-map-world') {
      result.push(`{regionStyle|${ labelData.name }}`)
    } else if (showMapRegionName) {
      result.push(`{regionStyle|${chartApi.mapSet.getGeoName(labelData, vm, childChartAlias)}}`)
    } else if (serieItem.type === 'map') {
      result.push(chartApi.mapSet.getGeoName(labelData, vm, childChartAlias))
    }
    if (hasValue && labelLineShow && serieItem.type !== 'map') {
      result.push(_value)
    }
    return result.join('\n')
  },
  funnel({ labelLineShow, labelData, _value, serieItem, funnelSettings, vm }) {
    const { funnelLabelShow, converesionRateShow, conversionRateAlgorithm } = funnelSettings
    const { dimensionList, metricAllList } = vm.UserConfig
    const metricOriginalName = metricAllList.map(m => m.alias || m.labeName)
    // 外部标签特殊处理
    if (!serieItem.isClone) {
      const key = conversionRateAlgorithm === 'previousPercent' ? 'VIEWFORMAT_METRIC_PERCENT_DIM' : 'VIEWFORMAT_METRIC_PERCENT'
      const { metricIndex = 0, name } = labelData.data
      const conversionRate = `${labelData.data.row[`${key}_${metricOriginalName[metricIndex]}`]}`
      // 有维度取维度值，无维度取度量名称 由于度量名称可以设置多语言，所以从chartSettings里取值
      const _name = dimensionList.length ? name : metricAllList[metricIndex].lang_alias
      _value = funnelLabelShow ? converesionRateShow ? `${_name}\n${conversionRate}` : _name : converesionRateShow ? conversionRate : ''
    } else if (!labelLineShow || !(this.labelLineButton || !this.commonData.isPreview)) {
      // 内部数值显示关闭
      _value = ''
    }
    return _value
  },
  tree({ name, dataShow, labelData, metrics, _value, _dimension, _extendDimensionList, percentageShow, chartUserConfig }) {
    const isTreeParent = labelData.data.hasOwnProperty('children')
    const {
      treeChildPercent,
      treeParentPercent,
      treeChildName,
      treeParentName,
      treeOneName,
      treeTwoName,
    } = getTooltipLabelData({ type: 'tree', value: labelData, dimension: _dimension, metrics, extendDimension: _extendDimensionList })
    let _valueShow = ''
    if (_value) {
      _valueShow = `：${ _value }`
      if (_extendDimensionList?.length && labelData.data.level === 1 && chartUserConfig.metricCalculation) {
        _valueShow = `: ${labelData.data[`${ STR }_METRIC_TOTAL_${metrics[0]}`]}`
      }
    }
    let treeData = (this.labelLineButton || !this.commonData.isPreview) && dataShow && _value ? `${_valueShow}` : ''
    let treePercentage = (this.labelLineButton || !this.commonData.isPreview) && percentageShow ? _extendDimensionList.length ? `(${treeParentPercent})` : `(${treeChildPercent})` : ''
    let treeName = !isTreeParent && treeOneName ? `${treeTwoName}${treeOneName}` : `${treeParentName}${treeChildName}`
    _value = labelData.dataIndex === 1 ? name : `${treeName}${treeData}${treePercentage === '()' ? '' : treePercentage}`
    return _value
  },
}
// 获取事件日历标签格式化内容， formatter和rich 对象
export function getHolidayFormatter(vm): Object {
  const { chartResponse = {}, chartData, chartUserConfig } = vm.content
  // const { chartResponse = {}, chartData, chartUserConfig } = this.element.content
  const { dimension: originalDimension } = chartResponse

  if (HOLIDAY_STYLE_CHART.includes(chartUserConfig.chartAlias)) {
    console.log(`事件日历标签生效图像 => %c${chartUserConfig.chartAlias}`, 'color: #fff;background: red')
    // 双维度场景，最小层级维度数据为类目轴数据
    // dimensionKey、dimensionKeyEmphasis没有用到，先注释掉
    // const dimensionKey = originalDimension[originalDimension.length - 1]
    // const dimensionKeyEmphasis = originalDimension[0]
    const holidayDescList = {}
    const isMobile = vm.isMobile
    // 直接遍历所有维度，将所有事件日历时间记录下来
    if (Array.isArray(originalDimension) && originalDimension.length > 0) {
      originalDimension.forEach(dimensionKey => {
        chartData.rows.forEach(item => {
          holidayDescList[item[`VIEWFORMAT_${dimensionKey}`]] = item[`VFMT_HOLI_${dimensionKey}`]
        })
      })
    }

    // 获取字符串括号中的值
    const extractContent = (str = '') => {
      // 匹配中文括号或英文括号中的内容
      const regex = /[(（](.*?)[)）]/
      const match = str.match(regex)

      if (match && match.length > 1) {
        // 提取内容并去除括号
        const content = match[1]
        return content
      } else {
        return ''
      }
    }
    const checkArrayContains = (array1, array2) => {
      for (let i = 0; i < array1.length; i++) {
        if (array2.includes(array1[i])) {
          return true
        }
      }
      return false
    }
    const EXCLUDE_TEXT_LISTS = ['今日', 'Today', '周六', 'Saturday', '周日', 'Sunday']
    const holidayFormatter = (val, index = 0, options = { type: '' }) => {
      // options 可直接传 type 或传对象带入更多数据进行处理
      let type = (typeof (options || '')) === 'string' ? options : (options.type)

      // TODO: 因为时间格式能够修改，预计这功能会出现问题，比如数据是 2022-09-21和2023-09-21时间格式为 09-21 那两个都将匹配到，如果事件设置不一样，可能出现 Bug
      // console.log(val, holidayDescList, index, type)

      // 事件日历日期
      let holidayDesc = holidayDescList[val]

      // 判断是否为用户设置的事件日
      let styleName = 'holiday'
      const extractContentText = extractContent(holidayDesc)
      const extractContentArr = extractContentText.split(',')

      // 括号中的内容不是排除列表中的内容才展示用户设置的样式
      if (!EXCLUDE_TEXT_LISTS.includes(extractContentText)) {
        // 两个事件，并且两个事件都在排除列表中时，不展示为事件日历，避免 （今天,周六） 这种被解析为事件日历的情况
          if (!(
              extractContentArr.length === 2
              && EXCLUDE_TEXT_LISTS.includes(extractContentArr[0])
              && EXCLUDE_TEXT_LISTS.includes(extractContentArr[1])
          )) {
              styleName = 'holidayUserSet'
          }
      }

      // ====================================================== 特殊逻辑处理 start
      /**
       *  type 为需要特殊处理的方法
       * slice2 返回值处理为 slice(0, 2) + '...'
       * treeData 树型数据，可能是时间拼接，需要判断数据中是否有时间，然后替换时间为事件日期
       * text 直接返回对应的事件日历日期
       *  */
      if (type === 'text') {
        return holidayDesc ? (isMobile ? val : holidayDesc) : val
      }
      if (type === 'treeData') {
        // TODO: 树型数据多的时候这里遍历次数会比较多，如果卡顿，需要优化这部分
        let newVal = val
        holidayDesc = void 0
        Object.keys(holidayDescList).forEach(key => {
          // TODO: 这里从字符串中取时间，可能导致匹配出错的问题，如数据非时间字段，但也出现了时间格式，并且与当前匹配时间格式一致
          // 判断是否存在事件日历标签
          // 替换日期为事件日历日期
          if ((val.startsWith(key)) && holidayDescList[key]) {
            const reg = new RegExp('^' + key)
            val = val.replace(/[(（][^)）]*[)）]/g, '') // TODO: 这里可以应该更精准一点匹配
            newVal = val.replace(reg, holidayDescList[key])
            holidayDesc = newVal
          } else if ((val.indexOf(key) >= 0) && holidayDescList[key]) {
            const reg = new RegExp(key)
            newVal = val.replace(reg, holidayDescList[key])
            val = val.replace(/[(（][^)）]*[)）]/g, '')
            holidayDesc = newVal
          }
        })
        // 因为树形传入数据不一样，所以这里需要使用处理之后的数据做判断
        // 判断是否为用户设置的事件日
        let styleName = 'holiday'
        const extractContentText = extractContent(holidayDesc)
        const extractContentArr = extractContentText.split(',')

        // 括号中的内容不是排除列表中的内容才展示用户设置的样式
        if (!EXCLUDE_TEXT_LISTS.includes(extractContentText)) {
            // 两个事件，并且两个事件都在排除列表中时，不展示为事件日历，避免 （今天,周六） 这种被解析为事件日历的情况
            if (!(
              extractContentArr.length === 2
              && EXCLUDE_TEXT_LISTS.includes(extractContentArr[0])
              && EXCLUDE_TEXT_LISTS.includes(extractContentArr[1])
            )) {
                styleName = 'holidayUserSet'
            }
        }

        console.log('val: ', val, newVal)
        return holidayDesc ? (isMobile ? `{${styleName}|${val}}` : `{${styleName}|${newVal}}`) : val
      }
      // 不能通过index获取对应rowItem，datazoom开启时，类目轴数据索引与rows匹配不上
      if (type === 'slice2') {
        return holidayDesc ? (isMobile ? `{${styleName}|${val.slice(0, 2)}...}` : `{${styleName}|${holidayDesc.slice(0, 2)}...}`) : val.slice(0, 2) + '...'
      }
      // ====================================================== 特殊逻辑处理 end
      return holidayDesc ? (isMobile ? `{${styleName}|${val}}` : `{${styleName}|${holidayDesc}}`) : val
    }
    const getHolidayUserSetStyle = () => {
      // 返回设置的样式
      return chartUserConfig?.holidayStyle || null
    }

    let formatter: any = { formatter: (e) => e, rich: {} }
    const holidayColor = ['sdp-dark-blue', 'sdp-deep-blue'].includes(vm.themeType) ? '#9E9AE5' : '#553CCE'
    if (Object.keys(holidayDescList).some(key => holidayDescList[key])) {
      formatter = {
        formatter: holidayFormatter,
        rich: {
          holiday: {
            color: holidayColor,
          },
          // 获取用户设置的颜色
          holidayUserSet: getHolidayUserSetStyle() || {
            color: holidayColor // 没有获取到 事件日历标签默认样式，说明时老数据，设置为老颜色
          }
        },
      }
    }
    return formatter
  }
  return { formatter: (e) => e, rich: {} }
}
export function getShowLinesLabel(chartUserConfig, configObject = {}) {
  const { labelConfig, chartAlias } = chartUserConfig
    let labelInside: boolean = false // 标签是否在图形内
    let labelShowLines = 1 // 标签会有多少行
    const boolLength = Object.values(configObject).filter(b => !!b.show).length // 标签、数值、占比开了几个
    if (['ve-pie-normal', 've-ring-normal', 've-liquidfill'].includes(chartAlias)) {
      if (['inside', 'outside'].includes(labelConfig.position)) {
        labelInside = labelConfig.position === 'inside'
        labelShowLines = boolLength
      }
    }
    if (!labelInside && LABEL_SHOW_LINES.includes(chartAlias)) {
      labelShowLines = (labelConfig.labelShowLines && labelConfig.labelShowLines <= boolLength) ? labelConfig.labelShowLines : 1
      if (['ve-roundCascades', 've-sunburst'].includes(chartAlias) && labelShowLines === 3) {
        labelShowLines = 1
      }
    }
    labelShowLines = Math.min(labelShowLines, boolLength)

    if (configObject.labelLineShow.show) {
      configObject.dataShow.prefix = labelShowLines > 1 ? '\n' : '：'
    }
    configObject.percentageShow.prefix = labelShowLines >= boolLength ? (boolLength === 1 ? '' : '\n') : '('

    const result = ['labelLineShow', 'dataShow', 'percentageShow'].filter(key => configObject[key].show).map(key => `${ configObject[key].prefix || '' }${ configObject[key].value }${ key === 'percentageShow' && configObject[key].prefix === '(' ? ')' : '' }`).join('')
    return result
}
export function getCurrentMetrciLabel({ serieItem, chartUserConfig, hasShowPercentSwitch }) {
  const { metricLabelDisplay = [], chartAlias } = chartUserConfig
  let currentMetricLabel = null
  let labelShow = true
  // 判断当前度量值是否显示
  if (serieItem.type === 'radar') {
    // 雷达图度量值特殊处理，雷达图多个度量只存在一个series
    serieItem.data.forEach(dataItem => {
      currentMetricLabel = metricLabelDisplay.find(metricItem => {
        return metricItem.keyName === dataItem.metricKeyName
      })
      if (currentMetricLabel && !currentMetricLabel?.origin?.showMetricValue) {
        dataItem.label = { show: false, }
        labelShow = false
      }
    })
  } else {
    currentMetricLabel = CONTROL_MEASURES_CHART.includes(chartUserConfig.chartAlias) && metricLabelDisplay.find(metricItem => {
      return metricItem.keyName === serieItem.metricKeyName
    })
    // 显示占比或者显示度量都未勾选，则隐藏标签
    const isShowCustomMetricValue = (CUSTOM_METRIC.includes(chartAlias) && currentMetricLabel?.labelType === 'custom' && currentMetricLabel?.customMetricList?.find(v => v.showMetricValue)) || currentMetricLabel?.origin?.showMetricValue
    if (currentMetricLabel && !(isShowCustomMetricValue || (hasShowPercentSwitch && currentMetricLabel.origin.showMetricPercentLabel))) {
      serieItem.label = { show: false, }
      labelShow = false
    }
  }
  return { currentMetricLabel, labelShow }
}

// 图形标签设置
export function labelSettings(vm, options) {
  const { chartUserConfig, chartData, content } = vm
  const { metrics = [], dimension = [] } = content.chartResponse || {}
  const { calendarSettings, labelLineShow, isLabelLogogram, dataShow = false, percentageShow = false,
    funnelSettings = {}, pieSetting = {}, metricLabelDisplay = [], childChartAlias, treeSetting = {}, chartAlias, compositeChart = {} } = chartUserConfig
  const { dimensionList, dimensionExtendList, metricAllList } = vm.UserConfig
  const { alertResult, chartResponse = {} } = content
  const { metrics: metricResponse, titleCalendarCacheMap = {} } = chartResponse

  const { funnelLabelShow, converesionRateShow } = funnelSettings
  const { rows, columns } = chartData
  // if (!labelLineShow) return
  const { series = [] } = options
  const { plateSpacing = true, spacingWidth = 1 } = pieSetting
  const isDoubleDimension = dimensionList.length === 2
  const _dimension = dimensionList.map(e => e.alias || e.labeName)
  const _extendDimensionList = dimensionExtendList.map(e => e.alias || e.labeName)
  // 单维度单度量、无维度漏斗图内部标签居中显示
  const isLabelCenter = !dimension.length || metrics.length === 1
  const isCalendar = chartAlias === 've-calendar'
  const isMap = chartAlias === 've-map-parent'
  const isNotWorld = ['ve-map-china', 've-map-britain'].includes(childChartAlias)
  const regionName = isNotWorld ? chartUserConfig.mapSetting.regionName : {}

  // 组合堆叠柱状图
  const isStackCompositeChart = chartAlias === 've-composite' && compositeChart.stack

  const {
    labelThemeConfig,
    funnelThemeConfig,
    treeThemeConfig,
    dayConfig
  } = getLabelThemeConfig({ vm, chartUserConfig, isCalendar })
  let features: any = []
  if (childChartAlias === 've-map-britain') {
    features = chartApi.mapSet.getData(`country/britain`)
    if (!features) return
  }
  // 在这里判断一下就不用再formatter里面循环判断了
  const hasShowPercentSwitch = DISPLAY_LABEL_PERCENT.includes(chartAlias)

  // 预警列表
  let warningArray = getWarningArray(vm) || []
  const isWaterfall = chartAlias === 've-waterfall'
  // const isWaterfall = ALLOW_WARNING_ICON_CONFIG_CHART.includes(chartAlias)
  // 瀑布图组装原始数据
  let waterfallData = []
  if (isWaterfall) {
    if (metricAllList.length > 1) {
      metricResponse.map(metricItem => {
        waterfallData.push({
          metric: metricItem,
          value: rows[0][`VFMT_DIFF_${metricItem}`]
        })
      })
    } else {
      rows.map(rowItem => {
        waterfallData.push({
          metric: metricResponse[0],
          value: rowItem[`VFMT_DIFF_${metricResponse[0]}`],
        })
      })
    }
  }
  series && Array.isArray(series) && series.forEach((serieItem, si) => {
    let hasWarnIcon = false // 满足预警条件且有 预警图标
    try {
      let { name, data, _seriesIndex, metricKeyName, originalName } = serieItem

      let waterfallName = name
      if (isWaterfall) {
         data.forEach((dataItem, dataIndex) => {
          const { waterfallSetting } = chartUserConfig
          // 需要给图例名称设置多语言
          const legendName = waterfallSetting?.legendName || { positive: '增长', negative: '下降' }
          const lendgenText = Object.keys(legendName).map(key => legendName[key]).map(nameItem => '(' + nameItem + ')').join('|')
          const reg = new RegExp(lendgenText + '|(growth)|(decline)', 'g')
          if (name.match(reg)) {
            waterfallName = waterfallData[dataIndex].metric
          }
        })
      }
      const warnings = warningArray.filter(warnlineSettingItem => {
        if (warnlineSettingItem.compareMethod === 3) return
        if (metricKeyName && warnlineSettingItem.metricKeyName) {
          return warnlineSettingItem.metricKeyName === metricKeyName
        }
        return warnlineSettingItem.metric === waterfallName
      })
      hasWarnIcon = ALLOW_WARNING_ICON_CONFIG_CHART.includes(chartAlias) && (Array.isArray(warnings) && warnings.length > 0) && (warnings.some(i => {
        return i.warningIconOption && i.warningIconOption?.imgOption?.pictureUrl !== ''
      }))
    } catch (e) {
    }
    if (serieItem.type === 'liquidFill' || serieItem.type === 'map' || (isMap && serieItem.type === 'lines')) return
    if (serieItem.isBackground || serieItem.isCustomSeries) return
    if (serieItem.type === 'bar' && chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH) return
    if (chartAlias === 've-waterfall' && serieItem.total) return
    let rowsData = rows
    if (chartAlias === 've-pie-normal' && pieSetting.showCombinePie) {
      if (si === 1) {
        // 其他项数据的index
        const othersPieDataIndex = chartData.rows?.findIndex(r => r.hasOwnProperty('others') && Array.isArray(r.others) && r.others.length)
        if (othersPieDataIndex > -1) {
          rowsData = chartData.rows[othersPieDataIndex].others
        }
      }
    }

    const { currentMetricLabel, labelShow } = getCurrentMetrciLabel({ serieItem, chartUserConfig, hasShowPercentSwitch })
    if (!labelShow) return

    const _fontStyle = getLabelPosition({
      vm,
      serieItem,
      chartUserConfig,
      isCalendar,
      isLabelCenter,
      labelThemeConfig,
      funnelThemeConfig,
      treeThemeConfig,
      currentMetricLabel,
    })
    const showLabelChart = ['ve-sunburst', 've-treemap', 've-tree', 've-calendar']
    const isSunburst = serieItem.type === 'sunburst'
    const isFunnel = serieItem.type === 'funnel'
    // tv屏蔽动画效果
    const needHideLabel = isAnimationLabelHide(chartUserConfig) && (!vm.utils.isTvScreen || vm.utils.getPlayAnimation())
    // 旭日图、矩形树图默认显示标签名称
    const isShowLabelChart = showLabelChart.includes(chartAlias)
    // 图形中标签、数字、占比可独立配置
    const isShowChartLabel = FOLLOWS_DIMENSION_COLOR.includes(chartAlias) ? labelLineShow || dataShow || percentageShow : labelLineShow
    const dataValueShowResult = isShowChartLabel && (vm.labelLineButton || !vm.commonData.isPreview)

    // 度量值、数值、占比其中一项打开时，label为true
    // let seriesLableShow = isShowLabelChart ? true : isFunnel ? isFunnlLabelShow : (isNotWorld && chartUserConfig.mapType === 'HEATMAP' ? isShowChartLabel && (vm.labelLineButton || !vm.commonData.isPreview) || regionName.show : (isShowChartLabel && !needHideLabel) && (vm.labelLineButton || !vm.commonData.isPreview))
    let seriesLableShow = false
    if (isCalendar && serieItem.calendarSeriesType === 'zebraCross') {
      seriesLableShow = false
    } else if (isShowLabelChart) {
      seriesLableShow = true
    } else if (isFunnel) {
      // 漏斗图标签显示
      // const isFunnlLabelShow = serieItem.isClone ? labelLineShow : funnelLabelShow || converesionRateShow
      seriesLableShow = serieItem.isClone ? labelLineShow : funnelLabelShow || converesionRateShow
    } else if (isNotWorld) {
      // 显示维度值
      if (chartUserConfig.mapSetting.showDimensionValue) {
        seriesLableShow = true
      } else if (chartUserConfig.mapType === 'HEATMAP') {
        seriesLableShow = dataValueShowResult || regionName.show
      } else {
        seriesLableShow = dataValueShowResult
      }
    } else {
      seriesLableShow = !needHideLabel && dataValueShowResult
    }

    let seriesLabel = {
      show: seriesLableShow, /* (['ve-themeRiver'].includes(chartAlias) ? Boolean(chartUserConfig.labelConfig.position) : labelLineShow) */
      // align: 'center', // 设置默认居中，加了rich:{}默认居左了
      ...((hasWarnIcon && chartAlias !== CHART_ALIAS_TYPE.VE_BAR) ? { align: 'center' } : {}),
      // rich: {},
      formatter(labelData) {
        const { value, name, seriesName, percent, data } = labelData
        // if (!labelData.hasOwnProperty('dataIndex')) return
        const formatFunc = getFormatterFunc('unitShorthand', 100, true)
        if (chartAlias === 've-bar-percent') {
          let value = Number(rows[labelData.dataIndex][metrics[0]])
          if (!isNaN(value) && isLabelLogogram && getIsNeedLogram(value)) {
            return formatFunc(value)
          } else {
            return rows[labelData.dataIndex]['VIEWFORMAT_' + metrics[0]]
          }
        } else if (chartAlias === 've-themeRiver') {
          return labelData.name
        } else if (chartAlias === 've-radar') {
          let value = Number(labelData.value)
          if (!isNaN(value) && isLabelLogogram && getIsNeedLogram(value)) {
            return formatFunc(value)
          } else {
            return labelData.data.metricValue[labelData.dimensionIndex]
          }
        } else if (chartAlias === 've-waterfall') {
          let value = getWaterfallLabel({ labelData, rows, metrics, chartUserConfig })
          if (!isNaN(value) && isLabelLogogram && getIsNeedLogram(value)) {
            let valueMid = formatFunc(value)
            if (hasWarnIcon) {
                const labelLineShow = chartUserConfig.labelLineShow
                let hasWordBreak = serieItem.label?.rich[`warn${labelData.dataIndex}`] ? `\n` : '' // 如果没有匹配上预警图标，则不额外加上换行
                return (labelLineShow ? valueMid + hasWordBreak : '') + `{warn${labelData.dataIndex}|}`
            }
            return valueMid
          } else {
            let valueMid = getWaterfallLabel({ labelData, rows, metrics, isFormat: true, chartUserConfig })
            if (hasWarnIcon) {
                const labelLineShow = chartUserConfig.labelLineShow
                let hasWordBreak = serieItem.label?.rich[`warn${labelData.dataIndex}`] ? `\n` : '' // 如果没有匹配上预警图标，则不额外加上换行
                return (labelLineShow ? valueMid + hasWordBreak : '') + `{warn${labelData.dataIndex}|}`
            }
            return valueMid
          }
        } else if((ALLOW_METRIC_SPLIT_DISPLAY_CHART.includes(chartAlias) || isStackCompositeChart) && serieItem._isSpecialMetricAliasLabel) {
          // series中按度量拆分的度量别名label显示度量别名的名称
          return formatFunc(name)
        }

        let { _value, currentRow } = getLabelValue({
          serieItem,
          isMap,
          chartUserConfig,
          isFunnel,
          rows: rowsData,
          columns,
          metrics,
          isCalendar,
          isShowLabelChart,
          isLabelLogogram,
          labelData,
          value,
          data,
          formatFunc,
          currentMetricLabel,
          name
        })

        const barFormatterSeriesType = ['bar', 'line', 'pictorialBar']
        let seriesType = barFormatterSeriesType.includes(serieItem.type) ? 'bar' : (serieItem.selfSeriesType || serieItem.type)
        if (labelFormatterConfig[seriesType] && !isCalendar) {
          _value = labelFormatterConfig[seriesType].call(vm, {
            isDoubleDimension,
            labelData,
            dimension,
            metrics,
            labelLineShow,
            dataValueShowResult,
            _value,
            currentRow,
            dataShow,
            percentageShow,
            seriesIndex: si,
            percent,
            isMap,
            rows: rowsData,
            seriesName,
            columns,
            value,
            isLabelLogogram,
            formatFunc,
            funnelSettings,
            serieItem,
            _dimension,
            _extendDimensionList,
            name,
            chartUserConfig,
            features,
            options,
            vm,
          })
        }

        if (isCalendar) {
          const day = calendarSettings.isShowDay ? value[0].split('-')[2] : ''
          const _labelInside = '\n'
          const calendarWarnColorArr = setWarnColor(vm, options, false, getWarningMetrics(content), 'null')
          const normalVal = _value
          const isData = labelData.data?.row && labelLineShow && vm.labelLineButton
          // 日历图日期、数值样式配置
          let dayRich = 'a'
          let dataRich = 'b'
          if (calendarSettings.weekColorSetting) {
            // 获取当前日期对应的星期
            const currentData = Array.isArray(labelData.data) ? labelData.data : labelData.data.value
            const weekDay = new Date(currentData[0]).getDay()
            calendarSettings.weekColorSetting.some((item, index) => {
              if (item.day.includes(weekDay)) {
                dayRich = `weekColor${index}A`
                dataRich = `weekColor${index}B`
                return true
              }
              return false
            })
          }

          _value = `{${dayRich}|${day}}${_labelInside}{${dataRich}|${isData ? normalVal : ''}}`
          // 日历图的预警样式
          if (Array.isArray(calendarWarnColorArr) && calendarWarnColorArr.length) {
            calendarWarnColorArr.forEach(warnItem => {
              if (warnItem.value.value[0] === value[0]) {
                _value = `{${dayRich}|${day}}${_labelInside}{${dataRich}|${isData ? normalVal : ''}}${isData ? '\n' : ''}{warn|}`
              }
            })
          }
          // 日历图交互样式
          if (value[0] === chartUserConfig.interactionOptionsValues) {
            _value = `{active|●}${_value}`
          }
        }
        if (chartAlias === CHART_ALIAS_TYPE.VE_LINE_AREA || chartAlias === CHART_ALIAS_TYPE.VE_BAR) {
            if (hasWarnIcon) {
              let valueMid = `${_value}`
              const labelLineShow = chartUserConfig.labelLineShow
              let hasWordBreak = serieItem.label.rich[`warn${labelData.dataIndex}`] ? `\n` : '' // 如果没有匹配上预警图标，则不额外加上换行
              return (labelLineShow ? valueMid + hasWordBreak : '') + `{warn${labelData.dataIndex}|}`
            }
        }
        return String(_value) === 'NaN' ? '' : childChartAlias === 've-map-world' ? `${_value}\n${name}` : _value
      },
    }
    if (chartAlias === 've-tree') {
      const oldFormatter = seriesLabel.formatter
      const formatterAndRich: any = getHolidayFormatter(vm)
      seriesLabel.formatter = (...data) => formatterAndRich.formatter(oldFormatter(...data), data.index, 'treeData')
      seriesLabel.rich = formatterAndRich.rich
    }
    // 日历图标签颜色设置
    if (isCalendar) {
      let baseA = {
        fontFamily: dayConfig.fontFamily,
        fontSize: vm.getPxOfHighResolution(dayConfig.fontSize),
        align: 'center',
      }
      let baseB = {
        fontFamily: _fontStyle.fontFamily,
        fontSize: vm.getPxOfHighResolution(_fontStyle.fontSize),
        align: 'center',
      }
      let rich = {
        a: {
          color: dayConfig.color,
          ...baseA,
        },
        b: {
          color: _fontStyle.color,
          ...baseB,
        },
        active: {
          color: 'red',
          align: 'left',
        },
        warn: {
          backgroundColor: {
            image: setCORSImg(Imgs.chartWarn)
          },
          align: 'center'
        },
      }
      if (calendarSettings.weekColorSetting) {
        calendarSettings.weekColorSetting.map((item, index) => {
          rich[`weekColor${index}A`] = { color: item.color, ...baseA }
          rich[`weekColor${index}B`] = { color: item.color, ...baseB }
        })
      }
      Object.assign(seriesLabel, { rich })
    }

    const combinedGraphics = ['ve-histogram-normal', 've-histogram-stack', 've-bar-normal', 've-composite', 've-pictorialbar', 've-bar-stack', 've-waterfall']
    if (combinedGraphics.includes(chartAlias)) {
      // 柱状图旋转90度
      if ((serieItem.type === 'bar' || serieItem.type === 'pictorialBar') && currentMetricLabel) {
        if ((['90', '270'].includes(currentMetricLabel.metricLabelRotationAngle)) && currentMetricLabel.metricLabelPosition === 'top') {
          Object.assign(_fontStyle, { distance: '30' })
          Object.assign(options['grid'], { top: '6%' })
        }
        if (['bottom', 'right', 'left'].includes(currentMetricLabel.metricLabelPosition)) {
          delete _fontStyle.distance
        }
      }
    }
    let needShowOptionChart = [
      CHART_ALIAS_TYPE.VE_BAR,
      CHART_ALIAS_TYPE.VE_BAR_PERCENT,
      CHART_ALIAS_TYPE.VE_BAR_STACK,
      CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT
    ]
    let isAlignmentMethod = needShowOptionChart.includes(chartUserConfig.chartAlias) && !!chartUserConfig?.alignmentMethod && chartUserConfig.alignmentMethod === 'right'
    if (hasWarnIcon) {
      // const warnings = warningArray.filter((item: { metricKeyName: string }) => item.metricKeyName === serieItem.metricKeyName)
      // 是否展示预警图标
      seriesLabel.show = warnings.some(i => {
        return i.warningIconOption && i.warningIconOption?.imgOption?.pictureUrl !== ''
      })

      const { alertResult, chartResponse = {} } = content
      const isHeatmap = chartAlias === 've-bar-Heatmap'
      let warnRich = {}
      serieItem.data.map((dataitem, dataIndex) => {
        const getMapVal = () => {
          if (Array.isArray(dataitem)) return dataitem[1]
          if (!dataitem || typeof dataitem !== 'object') return dataitem
          if (isHeatmap) return dataitem.row?.[`VFMT_RAW_${originalName}`]
          return dataitem.value
        }
        let dataValue = getMapVal()
        let isActiveDimensionWarn = false
        if (isWaterfall) {
          const { waterfallSetting } = chartUserConfig
          // 需要给图例名称设置多语言
          const legendName = waterfallSetting?.legendName || { positive: '增长', negative: '下降' }
          const lendgenText = Object.keys(legendName).map(key => legendName[key]).map(nameItem => '(' + nameItem + ')').join('|')
          const reg = new RegExp(lendgenText + '|(growth)|(decline)', 'g')
          let { name } = serieItem
          if (name.match(reg)) {
            dataValue = waterfallData[dataIndex].value
          }
        }
       
        warnings.forEach(activeWarnItem => {
        // 同维度指标比较
        const isDimensionWarn = activeWarnItem.compareMethod !== 2
          // 已经命中一条预警规则，不匹配余下的
        if (isActiveDimensionWarn) return
          const { value, id: warnName, calcType, rangeMin, rangeMax, fieldType, chartColor, metric, percent } = activeWarnItem
          // 维度预警优先取值，匹配中一个即放弃匹配余下的
          // if (activeDimensionWarn.some(item => item[metric] && item[metric] !== name)) return
          let _value = fieldType === 'other' ? +alertResult[warnName] : value * percent / 100
          if (isDimensionWarn && fieldType === 'other') {
            if (!rows.length) return
            let currentRow = null
            if (dimensionExtendList.length) {
              // 维度扩展特殊取值
              const _seriesItem = rows[dataIndex].extendData[_seriesIndex]
              _seriesItem && (currentRow = _seriesItem)
            } else {
              // 日历图寻找对应的那条数据不能根据索引，因为有凑数的数据
              if (dataitem.row) {
                currentRow = dataitem.row
              } else {
                const isWaterfall = chartAlias === 've-waterfall'
                currentRow = rows[isWaterfall && metricAllList.length > 1 ? 0 : dataIndex]
              }
            }
            if (currentRow?.['VIEWFORMAT_' + warnName] === chartResponse.measurementNullValue) return
            _value = currentRow?.[warnName] || 0
          } else if (fieldType === 'other') {
            if (alertResult['VIEWFORMAT_' + warnName] === chartResponse.measurementNullValue) return
          }
          
          _value = isNaN(_value) ? 0 : _value
          let _offset = +dataValue === 0 ? 0 : _value / dataValue
          let warningMetrics = getWarningMetrics(content)
          const isInRangeResult = isInRange.call(vm, calcType, { chartData: chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH && dataitem.hasOwnProperty('realValue') ? dataitem.realValue : +dataValue, lineData: +_value }, { min: +rangeMin, max: +rangeMax, measurementNullValue: chartResponse.measurementNullValue }, { warningMetrics, metric: metric, warningItem: activeWarnItem, dimensionList, dataItem: dataitem, extendDimensionList: dimensionExtendList })
          if (!isInRangeResult.inRange || isNaN(_offset)) return
          isDimensionWarn && (isActiveDimensionWarn = true)
          if (activeWarnItem.isSubscribedAndAlert) vm.isWarning = true
          if (activeWarnItem?.warningIconOption?.imgOption?.pictureUrl) {
            warnRich[`warn${dataIndex}`] = {
              backgroundColor: {
                image: activeWarnItem?.warningIconOption?.imgOption?.pictureUrl
              },
              width: 25,
              height: 25,
              align: isAlignmentMethod ? 'right' : chartAlias === CHART_ALIAS_TYPE.VE_BAR ? 'left' : 'center'
            }
          }
        })
      })

      Object.assign(seriesLabel, { rich: warnRich })
    }

    // 热力图
    if(chartAlias === 've-bar-Heatmap') {
      Object.assign(_fontStyle, { position: 'insideTopLeft', padding: plateSpacing ? spacingWidth + 2 : 2 })
    }

    Object.assign(seriesLabel, _fontStyle)
    // 旭日图标签特殊处理，鼠标悬浮时标签保持一致
    if (chartAlias === 've-tree' && treeSetting.layout === 'orthogonal') {
      Object.assign(seriesLabel, {
        rotate: 0,
      })
    } else if (isSunburst) {
      Object.assign(serieItem, {
        emphasis: { focus: 'none', label: { formatter: seriesLabel.formatter }, itemStyle: { opacity: 0.5 } },
        highLight: { label: { formatter: seriesLabel.formatter } },
        downplay: { label: { formatter: seriesLabel.formatter } },
      })
    } else if (isFunnel) {
      Object.assign(serieItem, {
        emphasis: {
          label: {
            show: seriesLabel.show,
          }
        },
      })
    } else if (isNotWorld) {
      Object.assign(seriesLabel, {
        rich: {
          regionStyle: {
            color: regionName.textStyle.color,
            fontSize: vm.getPxOfHighResolution(regionName.textStyle.fontSize || 12),
            fontFamily: regionName.textStyle.fontFamily,
          }
        }
      })
    }
    serieItem.label = seriesLabel
    // if (serieItem.type === 'map') {
    //   // options.geo.label.formatter = seriesLabel.formatter
    // } else if (serieItem.type === 'map' && regionName.show) {
    //   if (childChartAlias === 've-map-world') {
    //     const originShow = seriesLabel.show
    //     seriesLabel.show = false
    //     if (originShow) {
    //       let emphasisStyle = options.geo.emphasis || {}
    //       let emphasisLabelStyle = emphasisStyle.label || {}
    //       emphasisLabelStyle.color = 'transparent'
    //       Object.assign(emphasisStyle, { label: emphasisLabelStyle })
    //       options.geo.emphasis = emphasisStyle
    //     }
    //   }
    //   options.geo.label = seriesLabel
    // } else {
    //   serieItem.label = seriesLabel
    // }
  })
}

function isInRange(calcType, value, range, metrics) {
  const { warningMetrics, metric, warningItem, dimensionList, dataItem, extendDimensionList } = metrics
  const result = {
    calcType,
    inRange: false,
    value,
    range,
    metrics,
  }

  if (Number(warningItem.compareMethod) === 4) {
    let isContainDimensionVal = false
    const _dimensionList = [...dimensionList, ...extendDimensionList]
    if (dataItem?.row && _dimensionList?.length) {
      warningItem.dimensionSetting.forEach((ds, dsIndex) => {
        const { joinType, selectValues, fieldKeyName } = ds
        const fieldItem = _dimensionList.find(d => d.keyName === fieldKeyName)
        const langVal = selectValues.map(v => this.replaceColumnValuesLan(v))
        const currentDimensionValue = dataItem.row[fieldItem.alias || fieldItem.labeName]
        const _innerRes = langVal.includes(currentDimensionValue)
        if (dsIndex === 0) {
          isContainDimensionVal = _innerRes
        } else if (joinType === 'and') {
          isContainDimensionVal = isContainDimensionVal && _innerRes
        } else if (joinType === 'or') {
          isContainDimensionVal = isContainDimensionVal || _innerRes
        }
      })
    }
    if (!isContainDimensionVal) return result
  }

  if (['range'].includes(calcType)) {
    if (value.chartData >= range.min && value.chartData <= range.max) result.inRange = true
  } else if (['>', '>='].includes(calcType)) {
    if (!warningMetrics.linearGradientMetrics.includes(metric) && calcType === '>') {
      result.inRange = value.chartData > value.lineData
    } else {
      result.inRange = value.chartData >= value.lineData
    }
  } else if (['<', '<='].includes(calcType)) {
    if (!warningMetrics.linearGradientMetrics.includes(metric) && calcType === '<') {
      result.inRange = value.chartData < value.lineData
    } else {
      result.inRange = value.chartData <= value.lineData
    }
  } else if (['!='].includes(calcType)) {
    if (value.chartData !== value.lineData) result.inRange = true
  } else if (['isNotNull'].includes(calcType)) {
    let chartFormatData = value.chartData
    if (dataItem.row) chartFormatData = dataItem.row['VIEWFORMAT_' + metric]
    if (chartFormatData !== range.measurementNullValue) result.inRange = true
  } else if (['='].includes(calcType)) {
    if (value.chartData === value.lineData) result.inRange = true
  }
  return result
}

function getWaterfallLabel({ labelData, rows, metrics, chartUserConfig, isFormat }) {
  let value
  const isAccumulative = rows[labelData.dataIndex]?.isAccumulative
  const isContrast = rows[labelData.dataIndex]?.isContrast
  const key = isFormat ? 'VIEWFORMAT_' : 'VFMT_'
  if (isContrast) {
    // 对比值取值
    const contrastAlias = chartUserConfig.contrastList?.[0]?.alias || chartUserConfig.contrastList?.[0]?.labeName
    value = rows[labelData.dataIndex][`${key}TOTAL_${contrastAlias}`]
  } else if ((metrics.length <= 1 && isAccumulative) || (labelData.dataIndex >= metrics.length && metrics.length > 1)) {
    // 累计值取值 无维度多度量情况取最后一个度量的累计值；单维度单度量情况取最后一条数据的累计值
    value = metrics.length > 1 ? rows[0][`${key}TOTAL_${metrics[metrics.length - 1]}`] : rows[labelData.dataIndex][`${key}TOTAL_${metrics[0]}`]
  } else {
    value = metrics.length > 1 ? rows[0][`${key}DIFF_${metrics[labelData.dataIndex]}`] : rows[labelData.dataIndex]?.[`${key}DIFF_${metrics[0]}`]
  }
  return value
}

// 获取label的颜色
export function getLabelThemeConfig(param: LabelThemeConfigParam) {
  const { vm, chartUserConfig, isCalendar } = param
  const { calendarSettings, labelConfig = {}, funnelLabelConfig = {}, treeSetting = {}, chartAlias } = chartUserConfig
  const themeConfig = getThemeConfig(
    vm.themeType,
    {
      attributes: ['labelConfig', 'funnelLabelConfig', 'treeConfig', 'calendarConfig'],
      chartAlias: chartAlias,
    })
  const labelThemeConfig = Object.assign(themeConfig.labelConfig, labelConfig)
  const funnelThemeConfig = Object.assign(themeConfig.funnelLabelConfig, funnelLabelConfig)
  const treeThemeConfig = Object.assign(themeConfig.treeConfig.baseNameStyle, treeSetting.baseNameStyle)
  let dayConfig = {}
  if (isCalendar) {
    dayConfig = Object.assign(themeConfig.calendarConfig.daySetting, calendarSettings.daySetting)
  }
  return {
    labelThemeConfig,
    funnelThemeConfig,
    treeThemeConfig,
    dayConfig
  }
}
// 标签是否允许重叠
export function getLabelLayout(chartUserConfig) {
  const { chartAlias, isAllowLabelOverlap, animationSetting = {} } = chartUserConfig
  let _isAllowLabelOverlap = isAllowLabelOverlap
  if (!ALLOW_LABEL_OVERLAP_CHART.includes(chartAlias) || (animationSetting.enable && ((ANIMATION_CHART.includes(chartAlias) && chartAlias !== 've-map-parent') || animationSetting.animationType === 'rotation-normal'))) {
    _isAllowLabelOverlap = true
  }
  return {
    labelLayout: {
      hideOverlap: !_isAllowLabelOverlap,
      moveOverlap: true,
    },
    avoidLabelOverlap: _isAllowLabelOverlap,
  }
}
// 图形的动画
export function getSeriesAnimation(isFromResize = false) {
  if (isFromResize) {
    return {
      animation: {
        duration: 800,
      }
    }
  }
  return {
    animation: true,
    animationDuration: 800,
    animationDelay: 100,
    // animationEasing: 'cubicInOut',
    animationDurationUpdate: 800,
    animationDelayUpdate: 100,
    layoutAnimation: true,
    // animationEasingUpdate: 'cubicInOut',
  }
}
// 获取label的位置
export function getLabelPosition(param: LabelPositonParam) {
  const {
    vm,
    serieItem,
    chartUserConfig,
    isLabelCenter,
    labelThemeConfig,
    funnelThemeConfig,
    treeThemeConfig,
    currentMetricLabel,
  } = param
  const { treeSetting = {}, labelConfig } = chartUserConfig
  let padding = 0
  let result: any = {
    position: 'top',
  }
  let { color, fontFamily, fontSize } = labelThemeConfig
  switch (chartUserConfig.chartAlias) {
    case 've-bar-normal':
      result.position = 'right'
      break
    case 've-pictorialbar':
      // 象形柱状图是否开启反转Y轴
      const flipAxis = getProp(chartUserConfig, 'pictorialBarSettings.flipAxis', false)
      if (flipAxis) {
        result.position = 'right'
      }
      break
    case 've-histogram-stack':
      result.position = 'inside'
      break
    case 've-treemap':
      result.position = 'insideTopLeft'
      padding = [2, 0]
      break
    case 've-tree':
      result.position = treeSetting.layout === 'orthogonal' ? treeSetting.orient === 'LR' ? 'left' : 'right' : ''
      color = treeThemeConfig.color
      fontFamily = treeThemeConfig.fontFamily
      fontSize = treeThemeConfig.fontSize
      break
    case 've-funnel':
      if (serieItem.isClone) {
        // 设置内部标签位置
        result.position = isLabelCenter ? 'center' : serieItem.funnelAlign === 'right' ? 'insideRight' : 'insideLeft'
        padding = 5
      } else {
        // 设置外部标签位置
        result.position = serieItem.funnelAlign && serieItem.funnelAlign === 'right' ? 'left' : 'right'
        color = funnelThemeConfig.color
        fontFamily = funnelThemeConfig.fontFamily
        fontSize = funnelThemeConfig.fontSize
      }
      break
    case 've-bar-percent':
      result.position = 'insideLeft'
      break
    case 've-themeRiver':
      result.position = chartUserConfig.labelConfig.position
      break
    case 've-calendar':
    case 've-bar-stack':
      result.position = 'inside'
      break
    case 've-waterfall':
      result.position = 'top'
      break
    case 've-pie-rose':
      result.position = 'outside'
      break
    case 've-pie-normal':
    case 've-ring-normal':
    case 've-liquidfill':
      result.position = ['inside', 'outside'].includes(labelConfig.position) ? labelConfig.position : 'outside'
  }
  if (serieItem.type === 'bar' && serieItem.stack && chartUserConfig.chartAlias !== 've-waterfall') {
    result.position = 'inside'
  }
  // 根据度量显示按钮开关控制柱状等图形度量数值的显示位置
  if (METRIC_POSITION_CHART.includes(chartUserConfig.chartAlias) && currentMetricLabel) {
    if (chartUserConfig.labelLineShow && currentMetricLabel.metricLabelPosition) {
      result.position = serieItem.type === 'line' ? 'top' : currentMetricLabel.metricLabelPosition
      result.rotate = serieItem.type === 'line' ? 0 : currentMetricLabel.metricLabelRotationAngle
    }
  }
  // 位置根据设置的来设置
  // let needShowOptionChart = [
  //   CHART_ALIAS_TYPE.VE_BAR,
  //   CHART_ALIAS_TYPE.VE_BAR_PERCENT,
  //   CHART_ALIAS_TYPE.VE_BAR_STACK,
  //   CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT
  // ]
  // if (needShowOptionChart.includes(chartUserConfig.chartAlias) && !!chartUserConfig?.alignmentMethod && chartUserConfig.alignmentMethod === 'right') {
  //   if (chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT) {
  //       result.position = 'inside'
  //   } else if (chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BAR_PERCENT) {
  //       result.position = 'insideRight'
  //   } else {
  //       result.position = 'left'
  //   }
  // }
  return {
    ...result,
    color,
    fontFamily,
    fontSize: vm.getPxOfHighResolution(fontSize),
    padding,
    distance: 0,
  }
}
export function isAnimationLabelHide(chartUserConfig) {
  const { animationSetting = {}, chartAlias } = chartUserConfig
  if (!animationSetting.enable) return false
  if (!['ve-pie-normal', 've-pie-rose', 've-ring-normal'].includes(chartAlias)) return false
  if (animationSetting.animationType === 'rotation-rotate') return true
  if (animationSetting.animationType === 'rotation-normal' && animationSetting.labelAmount === 'single') return true
  return false
}
// 获取label的value
export function getLabelValue(param: LabelValueParam) {
  const {
    serieItem,
    isMap,
    chartUserConfig,
    isFunnel,
    rows,
    columns,
    metrics,
    isCalendar,
    isShowLabelChart,
    isLabelLogogram,
    labelData,
    value,
    data,
    formatFunc,
    currentMetricLabel,
    name
  } = param
  const { extendDimensionList = [], chartAlias } = chartUserConfig

  const metricName = serieItem.originalName || serieItem.name

  let currentMetricLabelAlias = ''
  if (CUSTOM_METRIC.includes(chartAlias) && currentMetricLabel && currentMetricLabel.labelType === 'custom') {
    const find = currentMetricLabel.customMetricList?.find(v => v.showMetricValue)
    currentMetricLabelAlias = find ? find.customMetricAlias : ''
  }
  const baseName = currentMetricLabelAlias || (serieItem.type === 'pie' ? columns[1] : isFunnel ? metrics[data.metricIndex] : extendDimensionList.length ? serieItem.metric : metricName)

  let currentRow: any = null
  let valueKey = `${STR}_${baseName}`

  // 度量值
  let baseValue = labelData.data?.row ? labelData.data.row[metricName] : (isMap && Array.isArray(value) ? value[2] : isFunnel ? (data.realValue || data.value) : isCalendar ? value[1] : value)
  if (isFunnel && !chartUserConfig.dimensionList.length) {
    // 无维度漏斗图取rows第一个元素
    currentRow = rows[0]
  } else if (isCalendar) {
    if (labelData.data?.row) {
      currentRow = labelData.data.row
    }
  } else if (isShowLabelChart) {
    currentRow = labelData.data
    valueKey = `${STR}_${metrics[0]}`
  } else if (labelData.data?.row) {
    currentRow = labelData.data.row
  } else if (Array.isArray(rows)) {
    currentRow = rows.find(item => (Object.values(item).includes(baseValue) && Object.values(item).includes(name)) || (name === 'others' && item.hasOwnProperty('others') && Array.isArray(item.others) && item.others.length))
  }

  let resultValue = currentRow?.[valueKey]
  if (resultValue !== '') {
    resultValue = resultValue || baseValue
    const currentMetricValue = currentMetricLabelAlias ? getNumber(currentRow[currentMetricLabelAlias]) : +baseValue
    const isNeedLogram = getIsNeedLogram(currentMetricValue)
    if (isLabelLogogram && isNeedLogram) {
      resultValue = formatFunc(currentMetricValue || 0)
    }
  }
  // if (!isNeedLogram && currentMetricLabelAlias) {
  //   const val = currentRow[`${STR}_${currentMetricLabelAlias}`]
  //   _value = isNaN(val) ? val : getNumber(val)
  // }

  return {
    _value: resultValue,
    currentRow
  }
}

export function getIsNeedLogram(value, data = 1000) {
  return value > data || value < (data * -1)
}
