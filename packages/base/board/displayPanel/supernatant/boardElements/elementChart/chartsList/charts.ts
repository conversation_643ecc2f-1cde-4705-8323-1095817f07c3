import { isDimensionLegend } from './chartConfig'
import Vue from 'vue'
import { FIELD_CALC_TYPE_LIST, RUN_TYPE, STATIC_BASE_PATH, THEME_TYPE } from 'packages/assets/constant'
import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import { getHolidayFormatter } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/chartSetting/chartLabel'
import {
  accSub,
  deepClone,
  flatten,
  getElementSize,
  getFormatterFunc,
  getProp,
  getRect,
  getStrSize,
  JSONClone,
  numToThouands,
  updateSizeByScale,
} from 'packages/assets/utils/globalTools'
import { getThemeConfig } from './theme'
import mapConstant, { findLangProvinces } from './mapConstant'
import * as gaugeConfigs from './config-gauge'
import {
  checkNoDataPermissions,
  getCalendardata,
  getChartLegendWidth,
  getEchartsMapTreeData,
  getEchartsTreeData,
  getNowMonth,
  hasDimensionColor,
  onlyDimensionColor,
  isReverseAxisChart,
  largeScreenStyle,
  setLegendAndTooltipWithHighResolution,
  ViturCalendarData,
  weekData
} from './chartSetting/handleChartSetting'
import { settingLine, warnLineSetting } from './chartSetting/chartWarn'
import { getMapTooltip } from '../chartDesign/getChartTooltip'
import { labelSettings, getIsNeedLogram, getBarPercent, getShowLinesLabel, getLabelLayout, getSeriesAnimation } from './chartSetting/chartLabel'
import { doubleDimensionSetting } from './chartSetting/doubleDimension'
import * as chartApi from '../api'
import iconPath from './iconPath'
import {
  AXIS_COORDINATES_CHART,
  BASE_RADIUS,
  BASE_RADIUS_CombinePie,
  CHART_ALIAS_TYPE,
  chartsList,
  CUSTOM_METRIC,
  DISPLAY_LABEL_PERCENT,
  EXTEND_DIMENSION_CHART,
  FOLLOWS_DIMENSION_COLOR,
  CIRCULAR_CHART,
  LOGARITHMARR,
  MEARSURE_DIS_CENTER,
  MOBILE_GRIDRIGHT_CHART,
  MOBILE_PAGE_CHART,
  MOBILE_PAGESIZE_CHART,
  NEED_TIMEDIMENSION_CHART,
  NOT_AUXILIATYLINE_CHART,
  NOTSHOWLEGENDSELEVTION,
  REVERSE_AXIS_CHART,
  RING_WIDTH_RATIO,
  SET_GRADIENT_CHART,
  ANIMATION_CHART,
  SUPPORT_DEPUTY_AXIS,
  VE_BANDWIDTH_TYPE,
  X_AXIS_LABEL_FORMATTER,
  Y_AXIS_LABEL_FORMATTER,
  mapTypeSvgPath,
  SPECIAL_PROP_TYPE,
  VE_LIQUIDFILL_MODE,
  ALLOW_METRIC_SPLIT_DISPLAY_CHART,
  BACKGROUND_TREND_LINE_CHART
} from '../constant'
import { TYPE_PARAM_ELEMENT } from 'packages/base/board/displayPanel/params/utils/constants'
import { handleBarSetting } from '../chartDesign/chartHandleFn/index'
import { lineNormal } from '../chartDesign/line/lineRenderFunc'

import { setBarItemStyleOption, setLegendDimension } from '../chartDesign/histogram/barRenderFunc'
import { setPieAreaPadding } from '../chartDesign/pie/pieRenderFunc'
import { TYPE_LIST } from '../../../../params/paramElement/bussinessCalendar/components/constants'
import { setExtremeValueSetting } from './chartSetting/extremeValue'
import { heatmapNormal } from '../chartDesign/heatmap/heatmapFunc'
function tempStore(vm, name) {
  name = name + ''
  if (!vm['ChartTempStore']) {
    vm['ChartTempStore'] = {}
  }
  // 临时存储
  return {
    setItem(key, value) {
      if (!vm['ChartTempStore'][key]) {
        vm['ChartTempStore'][key] = {}
      }
      vm['ChartTempStore'][key] = value
      return true
    },
    getItem(key) {
      return vm['ChartTempStore'][key] || null
    }
  }
}
// 图例排序
function legendSort(vm, options) {
  // 是否开启了排序功能，没开启不做处理
  if (!vm?.chartUserConfig?.topSelectedLegend) return
  console.log('legendSort!!!!!!!!!!', vm)
  const PARTITION_NUM = 10000000 // 图例排序分割，一个范围可能出现的最多图例，这里写 10000000 是预测显示或隐藏或其他图例不会超过这个范围
  // 根据 legend selected 为 series 中所有项添加排序标识
  const selected = options.legend.selected || []
  if (options?.series && options.series.length > 1) {
    options.series.forEach((item, index) => {
      item._color = options.color[index]
      if (vm.chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_RADAR) {
        if (item.data[0].name && selected[item.data[0].name]) {
          // 存在，添加标识，10000000 之前的为显示的
          if (selected[item.data[0].name]) {
            // 显示
            item._sort = index
          } else {
            // 隐藏
            item._sort = PARTITION_NUM + index
          }
        } else {
          // 其他图例默认排最后
          item._sort = PARTITION_NUM + PARTITION_NUM + index
        }
      } else {
        if (item?.name && selected[item.name] !== void 0) {
          // 存在，添加标识，10000000 之前的为显示的
          if (selected[item.name]) {
            // 显示
            item._sort = index
          } else {
            // 隐藏
            item._sort = PARTITION_NUM + index
          }
        } else {
          // （主纬度排序功能，修改名称，新名称选中状态不会出现在 selected 中，这里不能将字段拍到最后，按原顺序排序，71150）
          item._sort = index
        }
      }

    })
    // 通过标识重新排序 series
    options.series.sort((a, b) => a._sort - b._sort)
    // color 也需要排序
    options.series.forEach((item, index) => {
      options.color[index] = item._color
    })
  }

  // 其他情况
  /**
   * 饼图...
    环形图
    散点图
   */
  if (options?.series && options.series.length) {
    options.series.forEach(serie => {
      if (serie?.data && serie.data.length > 1) {
        serie.data.forEach((item, index) => {
          if (item?.name && selected[item.name] !== void 0) {
            if (selected[item.name]) {
              // 显示
              item._sort = index
            } else {
              // 隐藏
              item._sort = PARTITION_NUM + index
            }
          }
        })
        serie.data.sort((a, b) => a._sort - b._sort)
      }
    })
  }

  // 特殊图
  /**
   * 漏斗图 问题：不会触发更新 (触发更新会使图例重置)  解决：legendselectchanged 中保存下当前显示隐藏的图例，并刷新图形
    河流图 问题：不会触发更新 (触发更新会使图例重置) 解决：legendselectchanged 中保存下当前显示隐藏的图例，并刷新图形
    层叠圆形图  问题：根据大小排序 (需要生成图例来实现排序) 解决：创建 legend.data 数据实现图例排序
    带宽图  问题：排序不生效 (隐藏时报错导致后续流程没有执行)
    蝴蝶图 问题：不会触发更新 (只要触发更新即可)  解决：legendselectchanged 刷新图形
   */
  // TODO: 如果需要生成图例进行排序，需要先判断当前是否有图例，如果有图例则需要为当前图例排序
  switch (vm.chartUserConfig.chartAlias) {
    // 层叠圆形图 需要添加自定义图例实现排序，否则默认的图例排序无效
    case CHART_ALIAS_TYPE.VE_ROUNDCASCADES:
    case CHART_ALIAS_TYPE.VE_BANDWIDTH:
    case CHART_ALIAS_TYPE.VE_FUNNEL:
    case CHART_ALIAS_TYPE.VE_THEMERIVER:
      if (options.legend.data && options.legend.data.length) {
        // 有图例项配置时直接排序图例

        // 需要判断当前是否为数组类型，如果时数组类型图列需要转换成对象类型
        if (typeof options.legend.data[0] !== 'object') {
          options.legend.data = options.legend.data.map(item => {
            return {
              name: item
            }
          })
        }
        options.legend.data.forEach((item, index) => {
          if (item?.name && selected[item.name] !== void 0) {
            // 存在，添加标识，10000000 之前的为显示的
            if (selected[item.name]) {
              // 显示
              item._sort = index
            } else {
              // 隐藏
              item._sort = PARTITION_NUM + index
            }
          } else {
            // 其他图例默认排最后
            item._sort = PARTITION_NUM + PARTITION_NUM + index
          }
        })
        // 通过标识重新排序 legend.data
        options.legend.data.sort((a, b) => a._sort - b._sort)
      } else {
        // 没有图例项配置时生成图例
        options.legend.data = []
        options.series.forEach(serie => {
          options.legend.data.push({
            name: serie.name || ''
          })
        })
      }

      break
  }
}
// 处理四象限参数
export function handleFourQuadrantLine(chartConfig, echartsFn, markLineColor) {
  let echarts
  if (echartsFn && (echarts = echartsFn()) && echarts.getOption) {
    const xMax = echarts.getOption().xAxis[0].max
    const yMax = echarts.getOption().yAxis[0].max
    const xMin = echarts.getOption().xAxis[0].min
    const yMin = echarts.getOption().yAxis[0].min

    const xAxis = (xMax + xMin) / 2
    const yAxis = (yMax + yMin) / 2
    const markLine = {
      silent: true,
      symbol: 'none', // 将四象限箭头改为直线
      lineStyle: {
        type: 'solid',
        color: markLineColor || '#AB9680',
      },
      data: [
        { xAxis },
        { yAxis },
      ],
      precision: 10,
    }
    const oldMartLine = JSON.stringify(chartConfig.markLine)
    const newMartLine = JSON.stringify(markLine)
    if (oldMartLine !== newMartLine) {
      Vue.set(chartConfig, 'markLine', markLine)
    }
  }
}

// 传入字符串或渐变颜色选择生成的对象返回可使用的 LiquidFill 颜色配置
function getLiquidFillStyle(color) {
  if (typeof color === 'string') {
    return {
      color
    }
  }
  if (color?.type === 'pure') {
    // 纯色
    return {
      color: color?.pureColor
    }
  } else {
    // 渐变色
    const angle = color?.angle
    const gradientColor = color?.gradientColor
    const [ x, y, x2, y2 ] = Color.getColorDirection(angle)
    console.log('{ x, y, x2, y2 }: ', { x, y, x2, y2 })
    return {
      color: {
        type: 'linear',
        x,
        y,
        x2,
        y2,
        colorStops: [
            { offset: 0, color: gradientColor[0] },
            { offset: 1, color: gradientColor[1] }
        ]
      }
    }
  }
}

// ----------------分割线---------------------------------------
export function getChartsList(elName = '') {
  if (!elName) {
    return chartsList
  }
  return chartsList.filter(chart => chart.elName === elName)
}

export function getNumber(value) {
  return isNaN(value) ? 0 : Number(value)
}
// 设置Y轴最大值最小值
function handleNumber(num, splitNumber = 4) {
  if (!num) return 0
  // 12345 ，科学计数法数值部分*10为 12.345 ，则向上取整（12.345/4）*4 = 16 ，此用例N为3，则再乘以1000，计算最大值为  16000，则刻度值为 0  4000   8000   12000   16000
  // 当度量最大值为1<y<10时，最大值最接近的4的倍数；
  // 当度量最大值为0<y<1时，最大值最接近的0.4的倍数；
  function getMaxNum(num, regNum) {
    const reg = new RegExp(`(^\\d{${regNum}})`)
    return Math.ceil(Math.ceil(num).toString().replace(reg, '$1.') / splitNumber) * splitNumber
  }

  if (num > 0.1 && num < 1) {
    num = getMaxNum(num * 100, 4) / 100
  } else if (num >= 1 && num < 10) {
    num = getMaxNum(num * 10, 3) / 10
  } else if (num >= 10) {
    // 计算需要补几个0
    const N = Math.ceil(num).toString().length - 2
    num = getMaxNum(num, 2)
    if (N) {
      num *= (10 ** N)
    }
  } else {
    num = 0.1
  }
  return num
}

const afterSettingChart = {
  [CHART_ALIAS_TYPE.VE_BANDWIDTH](vm, options) {
    const { chartConfig: { yAxis } } = vm
    if (!yAxis) return
    const setBandwidthFun = {
      // 带宽图兼容辅助线和预警线和度量值
      a_setWarnAndAuxiliary(options) {
        let _yAxis = JSONClone(Array.isArray(yAxis) ? yAxis[0] : yAxis)
        if (!_yAxis) {
          // TODO: 临时记录下来，防止隐藏掉后数据没了
          _yAxis = tempStore(vm, vm.UserConfig.element.id).getItem(`bandwidth_yaxis`)
        } else {
          tempStore(vm, vm.UserConfig.element.id).setItem(`bandwidth_yaxis`, _yAxis)
        }
        console.log('_yAxis: ', _yAxis);
        axisInterval(_yAxis)
        const absMin = Math.abs(_yAxis.min)
        options.series.forEach(item => {
          if (!item) return
          if (item.type === 'custom') {
            const { markLine, markPoint } = item
            markLine.data.forEach((e, i) => {
              if (e.realValue > _yAxis.max) {
                e.yAxis = _yAxis.max + absMin
              } else if (e.realValue < _yAxis.min) {
                e.yAxis = _yAxis.min + absMin
              } else {
                e.yAxis = e.realValue + absMin
              }
              // 一条线对应两个点
              markPoint.data[i * 2].yAxis = e.yAxis
              markPoint.data[i * 2 + 1].yAxis = e.yAxis
            })
          } else if (item.stack === VE_BANDWIDTH_TYPE.STACK) {
            item.label = { show: false }
          }
          if (item.type === 'line' && item.label && absMin) {
            const formatter = item.label.formatter
            item.label.formatter = (labelData) => {
              const value = labelData.value
              if (typeof value === 'number' || !Object.is(+value, NaN)) {
                labelData.value = accSub(labelData.value, absMin)
              }
              return formatter(labelData)
            }
          }
        })

        // 带宽图、带宽图折线预警、带宽图折线预警、带宽图特殊处理、带宽图有贷款时，开始位置有变化，需要在这里处理、8682
        if (options.visualMap && options.visualMap[0]) {
          // 带宽图只会有一个度量
          (options?.visualMap?.[0]?.pieces || []).forEach(item => {
            if (item._type !== 'point') {
              if (item.gte !== void '') {
                console.log('折线预警大于等于小于等于')
                if (item.gte !== Number.MIN_SAFE_INTEGER) {
                  console.log('折线预警处理大于等于')
                  item.gte = item.gte + absMin
                }
                if (item.lte !== Number.MAX_SAFE_INTEGER) {
                  console.log('折线预警处理小于等于')
                  item.lte = item.lte + absMin
                }
              } else {
                console.log('折线预警大于小于')
                if (item.gt !== Number.MIN_SAFE_INTEGER) {
                  console.log('折线预警处理大于')
                  item.gt = item.gt + absMin
                }
                if (item.lt !== Number.MAX_SAFE_INTEGER) {
                  console.log('折线预警处理小于')
                  item.lt = item.lt + absMin
                }
              }
            }
          })
        }
      },
      // 设置dataZoon
      b_bandwidthDataZoom(options) {
        const { rank } = getBandwidthData(vm.chartUserConfig)
        if (!rank?.isOpen) return

        const { dataZoom = [], xAxis } = options
        if (xAxis.length > 1) {
          dataZoom.forEach(item => {
            item.xAxisIndex = [0, 1]
          })
        }
      },
      // 处理移动图形带宽颜色对不上, 追加一个重复的颜色
      c_bandwidthColor(options) {
        if (!vm.isMobile) return
        options.color.push(options.color[1])
      }
    }
    Object.keys(setBandwidthFun).sort().forEach(key => {
      setBandwidthFun[key](options)
    })
  }
}
// todo kyz
export const afterConfigHandler = {
  setSeriesKeyName(vm, options) {
    const { metricLabelDisplay = [] } = vm.chartUserConfig
    const metricList = vm.UserConfig.metricAllList
    let currentMetricLabel: any = {}
    let optionsSeries = options.series
    if (!Array.isArray(optionsSeries)) optionsSeries = [optionsSeries]
    optionsSeries.forEach(seriesItem => {
      if (!seriesItem || !seriesItem.type) return
      if (seriesItem.type === 'radar') {
        // 雷达图度量值特殊处理，雷达图多个度量只存在一个series
        seriesItem.data.forEach(dataItem => {
          const metricItem = metricList.find(item => item.lang_alias === dataItem.name)
          currentMetricLabel = metricLabelDisplay.find(m => m.keyName === metricItem?.keyName)
          currentMetricLabel && (dataItem.metricKeyName = metricItem.keyName)
        })
      } else {
        const metricItem = metricList.find(m => m.lang_alias === seriesItem.name || m.lang_alias === seriesItem.metric)
        currentMetricLabel = metricLabelDisplay.find(m => m.keyName === metricItem?.keyName)
        currentMetricLabel && (seriesItem.metricKeyName = metricItem.keyName)
      }
    })
  },
  // 图形最后处理一些特殊场景
  afterSettings(vm, options) {
    const chartAlias = vm?.chartUserConfig?.chartAlias
    afterSettingChart[chartAlias] && afterSettingChart[chartAlias](vm, options)
  },
  // 设置视觉指示线
  // labelLine: (vm, options) => {
  //   const { series } = options
  //   const labelLine = series[0].labelLine || {}
  //   const label = series[0].label || {}
  //   const labelLineShow = vm.chartUserConfig.labelLineShow || false
  //   series[0].labelLine = Object.assign({}, labelLine, { show: labelLineShow })
  //   series[0].label = Object.assign({}, label, { show: labelLineShow })
  // },
  setBoundaryGap(vm, options) {
    const { chartUserConfig: { boundaryGap = false } } = vm
    let { xAxis } = options
    if (Array.isArray(xAxis)) {
      xAxis.forEach((item) => {
        item.boundaryGap = boundaryGap
      })
    } else {
      xAxis.boundaryGap = boundaryGap
      options.xAxis = [xAxis]
    }
  },

  bandwidthRank(vm, options) {
    const { chartData, chartUserConfig: { chartAlias }, themeType } = vm
    if (!Array.isArray(chartData?.rows) || !chartData.rows.length) return

    const { rank } = getBandwidthData(vm.chartUserConfig)
    if (!rank?.isOpen) return

    const { rankSplitLineConfig } = getThemeConfig(themeType, { attributes: ['rankSplitLineConfig'], chartAlias })
    const { subtextStyle } = rank
    const fontSize = vm.getPxOfHighResolution(subtextStyle.fontSize)
    const rankingName = vm.$t('sdp.views.Ranking')
    const textStyle = {
      ...subtextStyle,
      fontSize
    }
    const rankingFontSize = vm.getPxOfHighResolution(12)
    const { yAxis, xAxis } = options
    let _yAxis = Array.isArray(yAxis) ? yAxis[0] : yAxis
    let _xAxis = Array.isArray(xAxis) ? xAxis[0] : xAxis
    // 使用abc来处理执行顺序
    const setBandwidthRankFun = {
      a_setGrid({ grid }) {
        let rankHeight = vm.getPxOfHighResolution(32)
        const rankInterval = vm.getPxOfHighResolution(15)
        const cloneGrid = JSONClone(grid)
        delete cloneGrid.top
        delete cloneGrid.x
        delete cloneGrid.y
        const { subtextStyle } = rank
        // 排名框适应字体高度
        const { height } = getStrSize('1234567890', { fontSize: fontSize + 'px', fontFamily: subtextStyle.fontFamily })
        // 计数排名不会被截取
        const { fontSize: fs, fontFamily, formatter } = _yAxis.axisLabel
        const base = fs / 12 > 1 ? 1 : fs / 12
        const maxW = getStrSize(formatter(_yAxis.max), { fontSize: fs + 'px', fontFamily: fontFamily }).width * base
        const minW = getStrSize(formatter(_yAxis.min), { fontSize: fs + 'px', fontFamily: fontFamily }).width * base
        let { width } = getStrSize(rankingName, { fontSize: rankingFontSize + 'px', fontFamily: subtextStyle.fontFamily })
        width = (width + vm.getPxOfHighResolution(12)) - (maxW > minW ? maxW : minW)
        const left = grid.left > width ? grid.left : width

        rankHeight = height > rankHeight ? height : rankHeight
        options.grid = [
          {
            ...grid,
            bottom: rankHeight + grid.bottom + rankInterval,
            left,
          },
          {
            ...cloneGrid,
            height: rankHeight,
            bottom: grid.bottom,
            left,
          }
        ]
      },
      b_setXAxisAndYAxis() {
        options.xAxis = [
          _xAxis,
          {
            type: 'category',
            gridIndex: 1,
            data: _xAxis.data,
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false
            },
            axisPointer: {
              type: 'none',
            },
            splitLine: {
              show: true,
              interval: 0,
              lineStyle: {
                color: rankSplitLineConfig.splitLine.lineStyle.color
              }
            }
          }
        ]
        options.yAxis = [
          _yAxis,
          {
            type: 'value',
            gridIndex: 1,
            max: _yAxis.max,
            min: _yAxis.min,
            name: rankingName,
            nameTextStyle: {
              color: textStyle.color,
              verticalAlign: 'middle',
              fontSize: rankingFontSize
            },
            nameLocation: 'center',
            axisLabel: {
              ..._yAxis.axisLabel,
              show: true,
              color: 'transparent',
            },
            nameRotate: 0,
            axisLine: {
              show: false
            },
            minInterval: _yAxis.max,
            splitNumber: 1,
            splitLine: {
              show: true,
              lineStyle: {
                color: rankSplitLineConfig.splitLine.lineStyle.color
              }
            },
            axisTick: {
              show: false,
              length: 0,
            }
          }
        ]
      },
      c_setSeries({ series }) {
        const { bw, rank } = getBandwidthData(vm.chartUserConfig)

        options.series = [
            ...series,
            {
              data: chartData.rows.map(item => ({
                name: bw ? `${item[`RANK_${bw.alias || bw.labeName}`]} ${rank.format} ${item[`CNT_${bw.alias || bw.labeName}`]}` : `${item[rank.field.labeName] || ''}`,
                value: _yAxis.max
              })),
              label: {
                show: true,
                formatter({ data }) {
                  return data.name
                },
                textStyle,
              },
              type: 'bar',
              barGap: 0,
              itemStyle: {
                normal: {
                  color: 'transparent'
                }
              },
              animation: false,
              xAxisIndex: 1,
              yAxisIndex: 1,
              id: 'rank'
            }
          ]
      },
    }
    Object.keys(setBandwidthRankFun).sort().forEach(key => {
      setBandwidthRankFun[key](options)
    })
  },
  bandwidthArea(vm, options) {
    const { chartData, chartConfig: { yAxis: cyAxis } } = vm
    if (!Array.isArray(chartData?.rows) || !chartData.rows.length) return

    let { series, yAxis } = options
    let _yAxis = Array.isArray(yAxis) ? yAxis[0] : yAxis
    const { yAxisSetting = [] } = vm.chartUserConfig
    const _yAxisSetting = Array.isArray(yAxisSetting) ? yAxisSetting : [yAxisSetting]
    const type = _yAxisSetting[0] && _yAxisSetting[0].formatType
    const formatter = getFormatterFunc(type)

    const { min, max, bw } = getBandwidthData(vm.chartUserConfig)
    if (!min && !bw) {
      if (formatter) {
        _yAxis.axisLabel.formatter = formatter
      }
      return
    }

    // 带宽国际化
    const commonData = {
      lineStyle: { width: 0 },
      smooth: 0,
      stack: VE_BANDWIDTH_TYPE.STACK,
      type: 'line',
      name: vm.$t('sdp.views.bandwidth'),
      emphasis: {
        scale: false,
      },
    }

    let keys = ['min', 'max']
    let data = {
      [keys[0]]: [],
      [keys[1]]: []
    }
    let minAliasKey = ''
    let maxAliasKey = ''
    if (min && max) {
      minAliasKey = min.alias || min.labeName
      maxAliasKey = `DIFF_${max.alias || max.labeName}`
    } else if (bw) {
      minAliasKey = `MIN_${bw.alias || bw.labeName}`
      maxAliasKey = `DIFF_${bw.alias || bw.labeName}`
    }

    let _cyAxis = JSONClone(Array.isArray(cyAxis) ? cyAxis[0] : cyAxis)
    console.log('_cyAxis: ', _cyAxis, vm);
    if (!_cyAxis) {
      // TODO: 临时记录下来，防止隐藏掉后数据没了
      _cyAxis = tempStore(vm, vm.UserConfig.element.id).getItem(`bandwidth_cyaxis`)
    } else {
      tempStore(vm, vm.UserConfig.element.id).setItem(`bandwidth_cyaxis`, _cyAxis)
    }
    axisInterval(_cyAxis)
    _yAxis = Object.assign(_yAxis, {
      max: _cyAxis.max,
      interval: _cyAxis.interval,
    })
    const measurementNullValue = vm?.content?.chartResponse?.measurementNullValue
    const absMin = Math.abs(_cyAxis.min)
    _yAxis.min = 0
    _yAxis.max += absMin
    _yAxis.axisLabel = {
      ..._yAxis.axisLabel,
      formatter(val) {
        const value = accSub(val, absMin)
        if (formatter) {
          return formatter(value)
        }
        return numToThouands(value)
      }
    }
    data = chartData.rows.reduce((pre, item) => {
      let minValue = +item[minAliasKey]
      let maxValue = item[maxAliasKey]
      if (item[minAliasKey] === measurementNullValue || !item[minAliasKey]) {
        minValue = 0
      }
      if (item[maxAliasKey] === measurementNullValue || !item[maxAliasKey]) {
        maxValue = '0'
      }
      pre[keys[0]].push({ value: String(minValue + absMin), symbolSize: 0 })
      pre[keys[1]].push({ value: maxValue, symbolSize: 0 })
      return pre
    }, data)
    options.series = [
      ...series.map(item => {
        if (item.type === 'line' && item.data) {
          item.data.forEach(e => {
              if (typeof e.value === 'number') {
                e.realValue = e.value
                e.value += absMin
              }
          })
        }
        return item
      }),
      Object.assign({}, commonData, {
        data: data[keys[0]],
        areaStyle: { opacity: 0 },
        id: keys[0],
      }),
      Object.assign({}, commonData, {
        data: data[keys[1]],
        areaStyle: { opacity: 1 },
        id: keys[1],
      }),
    ]
    options.legend = Object.assign({}, options.legend, {
      lineStyle: {
        width: 1,
      },
    })
  },
  compositeChartArea: (vm, options) => {
    const { series } = options
    const showLine = vm.chartSettings.showLine
    series.forEach(item => {
      if (showLine.includes(item.name)) {
        item.stack = 'line'
        item.areaStyle = {}
      }
    })
  },
  // 条形图 y 轴设置
  yAxisSettingForBar(vm, options) {
    // const { legend, grid, dataZoom } = options
    // if (!options.yAxis) return
    // const _yAxis = options.yAxis[0]
    // const { dimensionList = [], yAxisSetting = [], chartAlias, yAxis = [] } = vm.chartUserConfig
    // const yAxisSettingOption = yAxisSetting[0]
    // const format = (name) => {
    //   return name.length > 30 ? name.substr(0, 29) + '...' : name
    // }
    // const defaultRotate = chartAlias === 've-bar-normal' && dimensionList.length === 1 ? 45 : 0
    // const barConfig = {
    //   axisLabel: {
    //     rotate: defaultRotate,
    //     fontSize: 10,
    //     formatter: format,
    //   },
    // }
    // const settingConfig = Object.assign(barConfig, deepClone(vm.chartUserConfig.yAxis[0]))
    // Object.assign(_yAxis, settingConfig)
    // if (!yAxisSettingOption || (!yAxis.rotate && yAxis.rotate !== 0)) {
    //   _yAxis.axisLabel.rotate = defaultRotate
    // }
    // _yAxis.axisLabel.formatter = format
    // Object.assign(grid, {
    //   right: 60,
    //   left: 20,
    // })
    // _yAxis.name = ''
    // legend.bottom = 0
    // if (dataZoom && dataZoom.length) {
    //   dataZoom[0].right = 30
    //   grid.right = 100
    // }
    // // 移动端条形图调整grid
    // vm.isMobile && (grid.right = '10%')
  },
  // y轴通用设置
  yAxisGeneralSetting(vm, options) {
    if (!options.yAxis) return
    const yAxisOptionArr = Array.isArray(options.yAxis) ? options.yAxis : [options.yAxis]
    const { yAxisSetting = [], yAxis = [], chartAlias } = vm.chartUserConfig
    // const _yAxisSetting = Array.isArray(yAxisSetting) ? yAxisSetting : [yAxisSetting]
    // const _yAxis = Array.isArray(yAxis) ? yAxis : [yAxis]
    yAxisOptionArr.map((yAxisOption, index) => {
      // y轴标签不进行旋转,清空强制显示所有标签设置
      Object.assign(yAxisOption.axisLabel, {
        rotate: 0,
        interval: 'auto',
      })
    })
    if (yAxisOptionArr.length === 2 && yAxisOptionArr[1] && !yAxisOptionArr[1].axisLabel.show) {
      options.series.map((item, index) => {
        Object.assign(item, {
          yAxisIndex: 0
        })
      })
    }
  },
  // x轴通用设置
  xAxisGeneralSetting(vm, options) {
    if (!options.xAxis) return
    const xAxisOption = Array.isArray(options.xAxis) ? options.xAxis : [options.xAxis]
    const { xAxisSetting, xAxis, chartAlias, yAxisSetting = [], yAxis = [] } = vm.chartUserConfig
    // const { showLine = [] } = vm.chartSettings
    // const reverseAxisFlag = isReverseAxisChart(vm)
    // xAxisOption.forEach((item, index) => {
    //   const baseAxis = (reverseAxisFlag && index === 1) ? yAxis[index] : xAxis
    //   const baseAxisSetting = (reverseAxisFlag && index === 1) ? yAxisSetting[index] : xAxisSetting
    //   const defaultSetting = {
    //     axisLabel: {
    //       fontFamily: 'NotoSansHans-Regular',
    //       formatter: '{value}',
    //     },
    //     nameTextStyle: {
    //       color: '#333',
    //       fontFamily: 'NotoSansHans-Regular',
    //       fontSize: '12',
    //     },
    //   }
    //   let settingConfig = Object.assign({}, defaultSetting, deepClone(baseAxis))
    //   // 反转y轴图形x轴标签不进行旋转
    //   if (reverseAxisFlag) {
    //     Object.assign(settingConfig.axisLabel, {
    //       rotate: 0,
    //       interval: 'auto',
    //     })
    //   }
    //   Object.assign(item, settingConfig)
    // })
    // 组合图yAxisIndex设置
    // if (SUPPORT_DEPUTY_AXIS.includes(chartAlias)) {
    //   let axisKeyArr = isReverseAxisChart(vm) ? ['xAxisIndex', 'yAxisIndex'] : ['yAxisIndex', 'xAxisIndex']
    //   options.series.map((item) => {
    //     Object.assign(item, {
    //       [axisKeyArr[0]]: showLine.includes(item.name) && yAxis[1]?.axisLabel?.show ? 1 : 0,
    //       [axisKeyArr[1]]: 0,
    //     })
    //     if (chartAlias === 've-composite') return
    //     let type
    //     switch (chartAlias) {
    //       case 've-histogram-normal':
    //         type = 'bar'
    //         break
    //       case 've-pictorialbar':
    //         type = 'pictorialBar'
    //         break
    //       default:
    //         type = 'line'
    //         break
    //     }
    //     Object.assign(item, {
    //       type,
    //     })
    //   })
    // }
  },
  randarSetting(vm, options) {
    const { chartSettings, chartData, chartUserConfig } = vm
    if (!chartData.rows || !Array.isArray(chartData.rows) || !chartData.rows.length || chartUserConfig.chartAlias !== 've-radar') return
    let { radarSetting, metricLabelDisplay = [], labelLineShow, xAxis = {}, userLineStyle = [], colors = [], labelConfig = {} } = chartUserConfig
    const { metricAllList, dimensionList } = vm.UserConfig
    const defaultThemeConfig: any = getThemeConfig(vm.themeType, { attributes: ['radarConfig'] })
    let _radarSetting = deepClone(radarSetting)
    _radarSetting.axisLabel.fontSize = vm.getPxOfHighResolution(_radarSetting.axisLabel.fontSize)
    let tickMetricName = radarSetting.tickSetting?.metricKeyName

    const metricAliasMap: any[] = []
    metricAllList.forEach((metricItem, mi) => {
      if (metricItem.isDesensibilisation || !metricItem) return
      const metricAliasName = metricItem.alias || metricItem.labeName
      metricAliasMap.push({
        keyName: metricItem.keyName,
        originName: metricAliasName,
        aliasName: metricItem.lang_alias,
        legendShow: !radarSetting.tickSetting?.sameTick || !vm.legendSelected || !Object.keys(vm.legendSelected).length || vm.legendSelected[metricItem.lang_alias],
      })
    })
    if (!tickMetricName || !metricAliasMap.find(m => (m.keyName === tickMetricName))) {
      tickMetricName = metricAliasMap[0]?.keyName || ''
    }
    const metricMap = {
      tickMetricName: tickMetricName,
      indicatorRange: {},
    }
    const showMetricMap = metricAliasMap.filter(m => m.legendShow)

    const indicatorArray: any[] = []
    const radarIndexMap: any = {}
    if (!radarSetting.tickSetting?.sameTick) {
      // 分度量计算最大最小值
      showMetricMap.forEach((m, i) => {
        radarIndexMap[m.aliasName] = i
        const metricValue: number[] = chartData.rows.map(r => +r[m.originName])
        const indicatorItem = getIndicatorItem(metricValue, chartData.rows)
        indicatorArray.push(indicatorItem)
        if (m.keyName === metricMap.tickMetricName) {
          metricMap.indicatorRange = { min: indicatorItem[0].min, max: indicatorItem[0].max }
        }
      })
    } else {
      const _showMetricMap = showMetricMap.length ? showMetricMap : metricAliasMap
      // 所有度量值一起计算最大最小值
      let metricValue: number[] = []
      chartData.rows.forEach(r => {
        metricValue = metricValue.concat(_showMetricMap.map(m => +r[m.originName]))
      })
      const indicatorItem = getIndicatorItem(metricValue, chartData.rows)
      indicatorArray.push(indicatorItem)
      metricMap.indicatorRange = { min: indicatorItem[0].min, max: indicatorItem[0].max }
    }
    const unitFormater = getFormatterFunc('unitShorthand')
    options.legend.icon = 'roundRect'
    // Bug: 66273解决标签和数值重复问题
    const labelRect = labelLineShow ? getStrSize('JUSTFORCALC', { fontSize: `${labelConfig?.fontSize || 0}px`, fontFamily: labelConfig?.fontFamily }) : { height: 0 }
    const radar = {
      shape: _radarSetting.radarShape || 'circle',
      name: {
        show: xAxis.axisLabel.show,
        formatter: '{value}',
        textStyle: {
          color: xAxis.axisLabel.color,
          fontFamily: xAxis.axisLabel.fontFamily,
          fontSize: vm.getPxOfHighResolution(xAxis.axisLabel.fontSize),
        }
      },
      scale: true,
      nameGap: labelRect.height || 5,
      splitArea: {
        show: _radarSetting.splitArea.show,
        areaStyle: _radarSetting.splitArea.areaStyle || defaultThemeConfig.radarConfig.splitArea.areaStyle,
      },
      splitLine: {
        show: _radarSetting.splitLine.show,
        lineStyle: _radarSetting.splitLine.lineStyle || defaultThemeConfig.radarConfig.splitLine.lineStyle,
      },
      axisLine: _radarSetting.axisLine,
      axisLabel: {
        ..._radarSetting.axisLabel,
        formatter: (value, index) => {
          // 最中间的刻度不显示,不然太挤了
          let result = index === 0 ? '' : (chartUserConfig.isLabelLogogram ? unitFormater(value) : numToThouands(value))
          return result
        },
      },
      ...(vm.isMobile && !vm.isChartSet ? {
        radius: chartSettings.radius || '75%',
        center: ['50%', (chartSettings.offsetY || '50%')],
      } : {}),
    }
    options.radar = indicatorArray.map((indicatorItem, indicatorIndex) => {
      if ((indicatorArray.length === 1 && indicatorIndex === 0) || (showMetricMap[indicatorIndex].keyName === metricMap.tickMetricName)) {
        return Object.assign(radar, { indicator: indicatorItem })
      }
      const _radar = {}
      Object.keys(radar).forEach(k => {
        if (radar[k].show) _radar[k] = { show: false }
        else _radar[k] = radar[k]
      })
      return {
        ..._radar,
        indicator: indicatorItem,
      }
    })
    // 需要替换矢量路径的symbol
    const iconMap = {
      fivePointedStar: iconPath.fivePointedStar,
      emptyFivePointedStar: iconPath.emptyFivePointedStar,
    }
    const shadowMetrics = radarSetting?.shadowMetrics || ['ALL']
    options.series = metricAliasMap.map((m, mi) => {
      const currentMetricLabel = metricLabelDisplay.find(metricItem => {
        return metricItem.keyName === m.keyName
      })
      const lineStyleItem = userLineStyle.find(lineStyle => lineStyle.keyName === m.keyName) || { showSymbol: true }
      const configItem = labelLineShow && currentMetricLabel ? {
        label: { show: currentMetricLabel.origin.showMetricValue, }
      } : {}
      const dataValue: any[] = []
      const metricValue: any[] = []
      chartData.rows.forEach(r => {
        dataValue.push(r[m.originName])
        metricValue.push(r[`VIEWFORMAT_${m.originName}`])
      })
      let o: any = {
        ...(radarSetting?.areaStyle.show && (shadowMetrics.includes('ALL') || shadowMetrics.includes(m.keyName)) ? { areaStyle: { opacity: 0.3 } } : {}),
        ...getLabelLayout(chartUserConfig),
        ...getSeriesAnimation(),
        type: 'radar',
        symbol: lineStyleItem.showSymbol ? (iconMap[lineStyleItem.symbol] || lineStyleItem.symbol || 'emptyCircle') : 'path://',
        symbolSize: 10,
        radarIndex: radarIndexMap[m.aliasName] || 0,
        lineStyle: { type: lineStyleItem.type || 'solid', width: lineStyleItem.width || 2 },
        data: [{
          value: dataValue,
          metricValue: metricValue,
          name: m.aliasName,
          metricKeyName: m.keyName,
          originName: m.originName,
          ...configItem,
        }],
      }
      if (lineStyleItem.symbolColor) {
        if (colors[mi]) {
          o.lineStyle = {
            ...o.lineStyle,
            color: Color.getEchartsColorItem(colors[mi])
          }
        }
        o.itemStyle = {
          ...(o.itemStyle || {}),
          color: Color.getEchartsColorItem(lineStyleItem.symbolColor)
        }
      }
      return o
    })
    function getIndicatorItem(_dArray, _rows) {
      let min = Math.min(..._dArray)
      let max = Math.max(..._dArray)
      if (min === max) {
        if (min === 0) {
          max = 1
        } else {
          min = max - 1
        }
      }
      const result = splitAxis({ min, max }, 5)
      return _rows.map(r => {
        return {
          text: r[`VIEWFORMAT_${dimensionList[0].alias || dimensionList[0].labeName}`],
          min: result.min,
          max: result.max,
        }
      })
    }
  },
  // 线状图样式设计
  lineNormal(vm, options) {
    lineNormal(vm, options)
  },
  // 热力图样式设计
  heatmapNormal(vm, options){
    heatmapNormal(vm, options)
  },
  // 条形图,柱状图阴影指示器等设置
  handleAxisPointer(vm, options) {
    let axisKey = isReverseAxisChart(vm) ? 'yAxis' : 'xAxis'
    const axis = Array.isArray(options[axisKey]) ? options[axisKey] : (options[axisKey] && [options[axisKey]])
    axis && axis.forEach(item => {
      item.axisPointer = {
        type: 'shadow',
        // show: true,
        shadowStyle: {
          width: 'auto',
          color: 'rgba(30,136,229,0.10)',
        },
      }
      item.triggerEvent = true
    })
  },
  // 组合图形去除右侧坐标轴的背景线,次轴刻度线朝内
  hideYaxias(vm, options) {
  },
  commonHideYaxias(vm, options) {
    const { xAxis, yAxis } = options
    const splitLineStyle = getThemeConfig(
      vm.themeType,
      {
        attributes: ['gridSplitLineStyle'],
      }
    )
    const splitLine = {
      splitLine: {
        show: !vm.chartUserConfig.splitLine,
        lineStyle: splitLineStyle.gridSplitLineStyle
      },
      axisTick: {
        show: !vm.chartUserConfig.splitLine,
        length: 0,
      }
    }
    const axis = isReverseAxisChart(vm) ? xAxis : yAxis
    const axisArr = Array.isArray(axis) ? axis : [axis]
    axisArr.forEach((item, index) => {
      if (!item) return
      if (vm.chartUserConfig.chartAlias === 've-bar-percent') {
        Object.assign(item, {
          splitLine: { show: false },
          axisLine: { ...item.axisLine, show: true },
        })
      } else {
        !index && Object.assign(item, splitLine)
      }
      // 次轴基本设置
      if (index) {
        Object.assign(item, {
          splitLine: {
            show: false
          },
          axisTick: {
            show: false,
            length: 0,
          }
        })
      }
    })
  },
  pieOffset(vm, options) {
    // 饼类图在根据图例位置调整偏移
    // const { left, right } = options.legend
    const { chartUserConfig, isMobile, isChartSet, chartData, legendSelected = {}, othersPieDataIndex = -1 } = vm
    const { pieSetting = {}, chartAlias, legend, liquidFillSetting = {} } = chartUserConfig
    const { metricAllList, dimensionList } = vm.UserConfig
    if ((chartAlias === 've-liquidfill' && liquidFillSetting.mode === VE_LIQUIDFILL_MODE.NORMAL) || !dimensionList.length) return
    const isSide = ['right', 'left'].includes(legend) && (!isMobile || isChartSet)
    if (isSide) {
      // options.legend.height = '70%'
      // options.legend.top = '15%'
    }
    const scaleType = pieSetting?.radius?.type
    const scaleValue = pieSetting?.radius?.[scaleType] || 100
    const unselectLegends = options?.legend?.selected || {}
    let chartDataRows = chartData.rows
    const dimensionName = `VIEWFORMAT_${dimensionList[0].alias || dimensionList[0].labeName}`
    let actualShowCombinePie = false
    if (othersPieDataIndex > -1 && unselectLegends[chartData.rows[othersPieDataIndex][dimensionName]] !== false) {
      chartDataRows = chartDataRows.concat(chartData.rows[othersPieDataIndex].others)
      chartAlias === 've-pie-normal' && (actualShowCombinePie = true)
    }

    let total = 0
    let totalResult: number[] = []
    let othersTotal = 0
    let othersCount = 0
    const metricName = metricAllList[0]?.alias || metricAllList[0]?.labeName
    if (!Array.isArray(chartDataRows)) return
    chartDataRows.forEach((e, i) => {
      let flag = Object.keys(legendSelected).length && !legendSelected.hasOwnProperty(e[dimensionName])
      if (unselectLegends[e[dimensionName]] !== false || flag) {
        if (i <= othersPieDataIndex) {
          totalResult.push(i === 0 ? getNumber(e[metricName]) : totalResult[i - 1] + getNumber(e[metricName]))
        } else {
          othersTotal = othersTotal + getNumber(e[metricName])
          othersCount = othersCount + 1
        }
        if (i !== othersPieDataIndex) total += getNumber(e[metricName])
      } else {
        if (i <= othersPieDataIndex) {
          totalResult.push(i === 0 ? 0 : totalResult[i - 1])
        }
      }
    })
    options.totalData = {
      total,
      totalArray: totalResult,
      othersTotal: othersTotal,
      othersCount,
      actualShowCombinePie,
    }

    const base_radius = actualShowCombinePie && ['left', 'right'].includes(legend) ? BASE_RADIUS_CombinePie : BASE_RADIUS
    if (chartAlias === 've-ring-multiple') {
      if (!options.isRingMultiple || !Array.isArray(options.polar.center)) return
      const x = isSide ? (legend === 'left' ? '63%' : '37%') : options.polar.center[0]
      options.polar.center[0] = x
      options.polar.center[1] = '50%'
      if (scaleType === 'fixedRadius') {
        options.polar.radius = scaleValue
      } else {
        options.polar.radius = changeRadius(base_radius, scaleValue)
      }
      if (options.setOffsetRingMultiple) options.setOffsetRingMultiple(options, vm)
    } else {
      options.series.forEach((item, _seriesIndex) => {
        if (!Array.isArray(item.center)) return
        const x = isSide ? (legend === 'left' ? '63%' : '37%') : item.center[0]
        item.center[0] = x
        item.center[1] = '50%'
        if (Array.isArray(item.radius)) {
          let radiusArr: any = [parseInt(base_radius) * 0.8 + '%', base_radius]
          if (chartAlias === 've-ring-normal') {
            radiusArr = [parseInt(base_radius) * 0.5 + '%', base_radius]
          }
          if (scaleType === 'fixedRadius') {
            radiusArr = radiusArr.map(e => parseInt(e))
            item.radius = [scaleValue * radiusArr[0] / radiusArr[1], scaleValue]
          } else {
            item.radius = radiusArr.map(e => changeRadius(e, scaleValue))
          }
        } else {
          if (scaleType === 'fixedRadius') {
            item.radius = scaleValue
          } else {
            item.radius = changeRadius(base_radius, scaleValue)
          }
        }
        item.metricTotal = {
          total: total,
          showPercent: true,
          show: true,
          alias: metricName,
          dimension: [dimensionName],
          showPercentLabel: true, }
      })
      const liquidFillSeries = options.series.find(item => item.type === 'liquidFill')
      if (liquidFillSeries && scaleType === 'fixedRadius') {
        const outerPie = options.series.find(item => item.type === 'pie')
        if (!outerPie) return
        let liquidFillRadius = outerPie.radius[0]

        const dom = document.querySelector(`.${vm.chartClassName} .widget-chart`)
        const canvasWidth = dom.offsetWidth
        const canvasHeight = dom.offsetHeight
        const minSize = Math.min(canvasWidth, canvasHeight)
        liquidFillSeries.radius = liquidFillRadius / minSize * 200 + '%'
      }
    }

    // 饼图图例显示所占百分比,保留两位小数
    const notNeedHandleChart = ['ve-ring-multiple', 've-roundCascades']
    if (options.legend && !notNeedHandleChart.includes(chartAlias)) {
      options.legend.tooltip.formatter = (params) => {
        if (unselectLegends[params.name] === false) {
          return `${params.name}：0%`
        }
        const _value = params.name === chartDataRows[othersPieDataIndex]?.[dimensionName] ? othersTotal : getNumber(chartDataRows.filter(item => item[dimensionName] === params.name)[0]?.[metricName])
        const percent = total ? Math.round(_value / total * 10000) / 100 + '%' : '0.00%'
        return `${params.name}：${percent}`
      }
    }
    function changeRadius(val, percent) {
      if (String(val).includes('%')) {
        return val.replace('%', '') * percent / 100 + '%'
      }
      return val * percent / 100
    }
  },
  // 饼图的线宽
  pieSparation(vm, options) {
    const { chartUserConfig } = vm
    const { chartAlias, liquidFillSetting } = chartUserConfig
    if (chartAlias === 've-liquidfill' && liquidFillSetting.mode === VE_LIQUIDFILL_MODE.NORMAL) return
    options.series.forEach(item => {
      if (item.type !== 'pie') return

      const { echartInstance = {} } = vm
      if (echartInstance.dispatchAction) {
        vm.chartData.rows.forEach((item, index) => {
          vm.echartInstance.dispatchAction({
            type: 'downplay',
            // 可选，系列 index，可以是一个数组指定多个系列
            dataIndex: index,
          })
        })
      }

      if (vm.chartUserConfig.chartAlias === 've-liquidfill') return

      // 单个数据或者单个非零数据不需要饼图线宽
      const notZeroLength = item.data.filter(e => e.value).length
      const flag = item.data.length === 1 || notZeroLength === 1 || (vm.utils.isScreen && ['ve-pie-normal', 've-ring-normal', 've-pie-rose'].includes(vm.chartUserConfig.chartAlias))
      const borderWidth = flag ? 0 : 2
      item.itemStyle = {
        borderColor: getThemeConfig(
          vm.themeType,
          { attributes: ['pieConfig'] }
        ).pieConfig.borderColor,
        borderWidth,
      }
    })
  },
  ringMultipleSetting(vm, options) {
    const { chartData, chartUserConfig, themeType, } = vm
    const { gaugeTarget = {}, chartAlias, labelLineShow, dataShow, percentageShow, complationRate, pieSetting = {} } = chartUserConfig
    const { metricAllList, dimensionList } = vm.UserConfig
    if (!metricAllList.length || !chartData.rows.length || chartAlias !== 've-ring-multiple' || !(gaugeTarget.defaults?.alias || gaugeTarget.defaults?.labeName)) return
    const dimensionOriginalName = dimensionList.map(m => m.alias || m.labeName)
    const ringMultipleConfig = getThemeConfig(themeType, { attributes: ['ringMultipleConfig'], chartAlias })
    const dimensionColors = Color.getDimensionColor(vm).noRepeat
    const MAX_AXIS_TICK = 100
    const scaleType = pieSetting?.radius?.type
    const scaleValue = pieSetting?.radius?.[scaleType] || 100

    let seriesData: any = {
      negative: [],
      positive: [],
      backgroundColor: ringMultipleConfig.ringMultipleConfig.ringBackgroundColor,
      emphasis: {
        itemStyle: {
          color: ringMultipleConfig.ringMultipleConfig.ringBackgroundColor,
        }
      },
      yAxisNames: []
    }
    const originMatricName = metricAllList[0].alias || metricAllList[0].labeName
    chartData.rows.forEach((row, rowIndex) => {
      // if (!row['VIEWFORMAT_COMPLETION_RATE']) return
      // 占据整个圆环的比例
      const isPositive = row['COMPLETION_RATE'] >= 0
      const dimensionColor = dimensionColors.find(dc => dc.name === row[`VIEWFORMAT_${dimensionOriginalName[0]}`])?.color
      // 实际的完成率（根据带格式的完成率转化而来）
      let realComplateRate = Number(('' + row['COMPLETION_RATE'] || '0').replace(/[^0-9.]/g, '')) * 100
      const rangeColor = (complationRate && row['COMPLETION_RATE'] !== '') ? gaugeConfigs.getCompletionColor(Number(realComplateRate), complationRate.rateArray) : { index: -1 }
      const barValue = Math.min(Math.abs(isNaN(realComplateRate) ? 0 : realComplateRate), MAX_AXIS_TICK)

      // 此details用来设置tooltip内容
      const details = {
        dimensionName: row[`VIEWFORMAT_${dimensionOriginalName[0]}`],
        complationRateName: vm.$t('sdp.views.complateRate'),
        metricValue: row[`VIEWFORMAT_${originMatricName}`],
        metricRealValue: row[`${originMatricName}`],
        targetValue: row[`VIEWFORMAT_${gaugeTarget.defaults.alias || gaugeTarget.defaults.labeName}`],
        complationRateValue: row[`VIEWFORMAT_COMPLETION_RATE`],
        dimensionColor: rangeColor.index > -1 ? rangeColor.color : dimensionColor,
        isInRange: rangeColor.index > -1,
      }
      let positiveItem = {
        value: isPositive ? barValue : (MAX_AXIS_TICK - barValue),
        name: row[`VIEWFORMAT_${dimensionOriginalName[0]}`],
        itemStyle: { color: isPositive ? details.dimensionColor : seriesData.backgroundColor, },
      }
      let negativeItem: any = {
        value: isPositive ? (MAX_AXIS_TICK - barValue) : barValue,
        name: row[`VIEWFORMAT_${dimensionOriginalName[0]}`],
        itemStyle: { color: isPositive ? seriesData.backgroundColor : details.dimensionColor, },
      }
      isPositive ? (negativeItem.emphasis = seriesData.emphasis) : (positiveItem.emphasis = seriesData.emphasis)
      seriesData.positive.push(positiveItem)
      seriesData.negative.push(negativeItem)
      seriesData.yAxisNames.push(details, details)
    })
    seriesData.yAxisNames.unshift(seriesData.yAxisNames[0])

    seriesData.yAxisNames.reverse()
    options.angleAxis = {
      splitLine: { show: false },
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: { show: false },
      max: MAX_AXIS_TICK,
      min: 0,
    }
    options.radiusAxis = { type: 'category', inverse: true, axisTick: { show: false }, axisLine: { show: false }, axisLabel: { show: false } }
    options.polar = { center: ['50%', '50%'], radius: BASE_RADIUS }
    const publicConfig = {
      type: 'bar',
      coordinateSystem: 'polar',
      stack: 'a',
      name: metricAllList[0].aliasName,
      showBackground: true,
      barWidth: '50%',
      backgroundStyle: { color: ringMultipleConfig.ringMultipleConfig.ringBackgroundColor },
      originalName: originMatricName,
    }
    options.legend.data = [metricAllList[0].aliasName]
    options.series = [
      { ...publicConfig, data: seriesData.negative },
      { ...publicConfig, data: seriesData.positive },
    ]
    options.isRingMultiple = true
    // 显示标签
    if ((labelLineShow || dataShow || percentageShow) && (vm.labelLineButton || !vm.commonData.isPreview)) {
      createLabelSetting(options)
      delete options.axisPointer
    } else {
      delete options.yAxis
      delete options.xAxis
      delete options.setOffsetRingMultiple
    }
    function createLabelSetting(options) {
      const { labelConfig } = chartUserConfig
      const labelContentFunc = getLabelContent(chartUserConfig)
      const yAxisNames = seriesData.yAxisNames.map(yAxisName => labelContentFunc(yAxisName))
      options.grid = { z: 20, containLabel: false, show: false, }
      options.yAxis = {
        show: true,
        type: 'category',
        z: 20,
        data: yAxisNames,
        boundaryGap: false,
        splitLine: {
          show: true,
          lineStyle: {
            color: seriesData.yAxisNames.map((yName, index) => index % 2 ? yName.dimensionColor : 'transparent'),
          },
          interval: 0,
        },
        axisTick: { show: false, },
        axisLabel: {
          interval: 0,
          align: 'left',
          color: (val, index) => index % 2 ? (seriesData.yAxisNames[index].isInRange || chartUserConfig.followDimensionColor ? seriesData.yAxisNames[index].dimensionColor : labelConfig.color) : 'transparent',
          fontSize: vm.getPxOfHighResolution(labelConfig.fontSize),
          fontFamily: labelConfig.fontFamily,
        },
        axisLine: { show: false },
      }
      options.xAxis = [{ type: 'value', show: false }]
      options.setOffsetRingMultiple = setOffsetRingMultiple
      function setOffsetRingMultiple(options, vm) {
        let { polar } = options
        const { center, radius } = polar

        let ringRadius
        if (scaleType === 'fixedRadius') {
          ringRadius = scaleValue
        } else {
          const dom = document.querySelector(`.${vm.chartClassName} .widget-chart`)
          const canvasWidth = dom.offsetWidth
          const canvasHeight = dom.offsetHeight
          const minSize = Math.min(canvasWidth, canvasHeight)
          let noPercentRadius = Number(radius.replace('%', ''))
          ringRadius = minSize * noPercentRadius / 100 / 2
        }
        options.grid.left = center[0]
        options.grid.bottom = center[1]
        options.grid.width = ringRadius + 10
        options.grid.height = ringRadius
        options.yAxis.axisLabel.margin = -ringRadius - 20
      }
      function getLabelContent(chartUserConfig) {
        const { dataShow, percentageShow, isLabelLogogram } = chartUserConfig
        const formatFunc = getFormatterFunc('unitShorthand', 100, true)
        return (_details) => {
          let configArray = {
            labelLineShow: { show: labelLineShow, value: _details.dimensionName, },
            dataShow: {
              show: dataShow,
              value: (isLabelLogogram && !isNaN(_details.metricRealValue)) ? formatFunc(Number(_details.metricRealValue)) : _details.metricValue,
            },
            percentageShow: { show: percentageShow, value: _details.complationRateValue, },
          }

          return getShowLinesLabel(chartUserConfig, configArray)
        }
      }
    }
  },
  handRingConfig(vm, options) {
    const { chartUserConfig } = vm
    const { chartAlias, liquidFillSetting } = chartUserConfig
    if (chartAlias === 've-liquidfill' && liquidFillSetting.mode === VE_LIQUIDFILL_MODE.NORMAL) return
    // 圆环度量汇总位置调整
    measureSumchange(vm, options)

    // 圆环比例调整 圆环水滴图
    setRingWidthRatio(vm, options)
  },

  // 所有图形的处理
  allImgSettings(vm, options) {
    const { chartUserConfig, element, isMobile, isChartSet, chioceTab = [], screenType, configs } = vm
    const { colors, title, histogramDisplayMode, chartAlias, animationSetting = {}, } = chartUserConfig
    if (chartAlias === 've-grid-normal') return
    const isGaugeAlias = (chartAlias === 've-gauge-normal')
    let { color, title: optionsTitle, dataZoom, xAxis } = options
    let colorSet = getThemeConfig(
      vm.themeType,
      {
        attributes: ['chartBackground', 'chartTitleConfig', 'tooltipConfig', 'datazoomConfig'],
      },
    )
    // 图表背景颜色、图例文本颜色
    // options.grid.backgroundColor = (vm.isMobile && !isChartSet) ? BOARD_ELEMENT_THEME[vm.themeType].mobileBgc : colorSet.chartBackground

    // 移动端图形，隐藏图例、tooltip
    // if (!vm.isChartSet && (isMobile && (!configs || configs.type !== 'template'))) {
    //   options.tooltip.show = false
    // }

    // tooltip计算始终限制在框内
    options.tooltip.confine = true
    if (animationSetting.enable && ANIMATION_CHART.includes(chartAlias)) {
      options.animation = chartAlias === 've-map-parent'
    }

    Object.assign(options, getSeriesAnimation())

    // 设置图形的colors
    chartAlias === 've-themeRiver' && (options.color = [])

    if (!isGaugeAlias && !['ve-ring-multiple', 've-roundCascades'].includes(chartAlias)) {
      // 设置标签显示格式
      labelDisplayFormatter(vm, options)
    }

    // 度量柱设置
    handleBarSetting({ seriesOption: options.series, chartUserConfig })

    // 双度量设置
    doubleMetricSetting(vm, options)

    // 设置移动端内置datazoom
    mobileInsideDatazoom(vm, options)

    // 处理极值高亮颜色
    setExtremeValueSetting(vm, options)

    // 预警线设置(柱状图，条形图，仪表盘)
    warnLineSetting(vm, options)

    // 交互设置样式
    interactionStyle(vm, options)

    // 标签跟随维度颜色
    followDimensionColor(vm, options)

    // 嵌套柱状图设置dataZoom控制的x轴
    const isDatazoomExist = dataZoom || isMobile
    if (['ve-histogram-normal', 've-composite'].includes(chartAlias) && histogramDisplayMode === 'cascade' && isDatazoomExist) {
      const _dataZoom = deepClone(options.dataZoom)
      const xAxisIndex = xAxis.map((item, index) => index)
      Array.isArray(_dataZoom) && _dataZoom.forEach(item => { item.xAxisIndex = xAxisIndex })
      options.dataZoom = _dataZoom
    }

    // 高分辨率图例和tooltip调整
    setLegendAndTooltipWithHighResolution(vm, options)

    // 图形大屏模式调整
    largeScreenStyle(vm, options)
    // 图形渐变色
    handChartColorSet(vm, options)

    // 图例排序
    legendSort(vm, options)

    // 图表对齐方式
    handAlignmentMethod(vm, options)

    console.log(options, 'opts')

  },
  scatterOffset(vm, options) {
    // 调整散点图大小
    const values = []
    const { chartData, } = vm
    const { metricSizeList, metricSecondList, dimensionList } = vm.UserConfig
    const { rows, columns } = chartData
    const _columns = deepClone(columns)
    if (dimensionList.length === 2) {
      _columns.splice(0, 1)
    }
    // 不存在size字段时，取Y轴字段
    const size = metricSizeList[0] || metricSecondList[0] || {}
    const sizeAlias = size.alias || size.labeName
    Object.keys(rows).forEach((key, i) => {
      const obj = rows[key][0]
      if (obj) {
        const value = parseFloat(obj[sizeAlias]) || 0
        values.push(value)
      }
    })
    let symbolSizeMax = vm.chartSettings.symbolSizeMax
    const mobileSymbolSizeMax = [20, 40]
    if (vm.isMobile && !mobileSymbolSizeMax.includes(symbolSizeMax)) {
      symbolSizeMax = 20
    }
    const max = Math.max.apply(null, values)
    const min = Math.min.apply(null, values)
    const singleSize = symbolSizeMax / (max - min)
    Object.keys(rows).forEach((key, i) => {
      const obj = rows[key]
      if (obj.length && options.series[i]) {
        const { data } = options.series[i]
        if (singleSize !== Infinity && typeof singleSize === 'number') {
          data.forEach((item, index) => {
            const value = parseFloat(obj[index][sizeAlias]) || 0
            item.symbolSize = Math.max(singleSize * (value - min) / 2 + symbolSizeMax / 2, symbolSizeMax / 2)
            // item.value[0] = item.value[4] || thouandsToNumber(item.value[0])
          })
        } else {
          data.forEach((item, index) => {
            item.symbolSize = symbolSizeMax / 2
            // item.value[0] = item.value[4] || thouandsToNumber(item.value[0])
          })
        }
      }
    })

    if (options.dataZoom && options.dataZoom.length) {
      // 特殊情况Y轴的datazoom保留至小数点后第一个不为0的数后两位, labelPrecision为整数时也有精度
      const _datazoom = deepClone(options.dataZoom)
      _datazoom.forEach(item => {
        item.labelFormatter = (value) => {
          const formatVal = getFormatterFunc('thousands')(Number(value))
          return `${formatVal}`.match(/([0-9]|,)+\.?0*[1-9]{0,2}/)[0]
        }
      })
      options.dataZoom = _datazoom
    }
    // 散点图y轴标签隐藏，散点元素过大会显示不完全，将散点图网格右移
    if (getProp(options, 'yAxis.axisLabel') && !options.yAxis.axisLabel.show) {
      options.grid.left = 30
    }
    // 散点图的面积色带按钮左移一些
    if (options.toolbox?.show) {
      options.toolbox.right = 20
      options.grid.top += 20
    }
    const gridSplitLineStyle = getThemeConfig(
      vm.themeType,
      {
        attributes: ['gridSplitLineStyle'],
      }
    )
    options.xAxis = (Array.isArray(options.xAxis) ? options.xAxis : [options.xAxis]).map((xAxis) => {
      return Object.assign({}, xAxis, {
        splitLine: {
          lineStyle: gridSplitLineStyle.gridSplitLineStyle,
        }
      })
    })
    options.yAxis = (Array.isArray(options.yAxis) ? options.yAxis : [options.yAxis]).map((yAxis) => {
      return Object.assign({}, yAxis, {
        splitLine: {
          lineStyle: gridSplitLineStyle.gridSplitLineStyle,
        }
      })
    })
    options.series.forEach((item, i) => {
      i && (item.markLine = {})
    })
  },
  // 设置dataZoom 图例、网格需上移
  legendOffset(vm, options) {
  },

  // 设置图例、网格bottom默认值
  legendAndGridDefaultOffset(vm, options) {
    const alias = vm.chartUserConfig.chartAlias
    const { grid, legend } = options
    // 手机端隐藏图例
    if (!vm.isChartSet && (vm.isMobile && alias !== 've-gauge-normal')) legend.show = false

    legend.bottom = 0
    alias !== 've-ring-multiple' && (grid.bottom = vm.isMobile ? vm.isChartSet ? 28 : 0 : vm.getPxOfHighResolution(45))
  },
  notCricleSetting(vm, options) {
    if (!AXIS_COORDINATES_CHART.includes(vm.content.alias)) return
    function setAxisFontSize(axis) {
      const _axis = Array.isArray(axis) ? axis : [axis]
      _axis.forEach(item => {
        if (!item.axisLabel) return
        const fontSize = item.axisLabel.fontSize || 12
        Object.assign(item.axisLabel, {
          fontSize: vm.getPxOfHighResolution(fontSize),
        })
      })
    }
    const { chartAlias } = vm.chartUserConfig
    if (!['ve-themeRiver', 've-bar-percent'].includes(chartAlias)) {
      const { xAxis, yAxis } = options
      // 轴线不需要保持在0刻度
      let _xAxis = Array.isArray(xAxis) ? xAxis[0] : xAxis
      let _yAxis = Array.isArray(yAxis) ? yAxis[0] : yAxis
      _xAxis.axisLine = Object.assign({}, _xAxis.axisLine, {
        onZero: false,
      })
      _yAxis.axisLine = Object.assign({}, _yAxis.axisLine, {
        onZero: false,
      })
      if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) {
        yAxis.forEach(_y => {
          _y.axisLine.onZero = true
        })
      }
      setAxisFontSize(xAxis)
      setAxisFontSize(yAxis)
      if (!['ve-tree'].includes(chartAlias)) {
        // 强行设置刻度间距，保证刻度轴等分
        if (!_xAxis) {
          // TODO: 临时记录下来，防止隐藏掉后数据没了
          _xAxis = tempStore(vm, vm.UserConfig.element.id).getItem(`bandwidth_xaxis`)
        } else {
          tempStore(vm, vm.UserConfig.element.id).setItem(`bandwidth_xaxis`, _xAxis)
        }
        axisInterval(_xAxis)
        // const yAxisArrForSetInterval = Array.isArray(yAxis) ? yAxis : [yAxis]
        const axis = isReverseAxisChart(vm) ? xAxis : yAxis
        const axisArr = Array.isArray(axis) ? axis : [axis]

        // 自动计算最大最小值的轴线的index
        let axisIntervalIndex: number
        axisArr.forEach((axisItem, index) => {
          if (axisItem.hasOwnProperty('max') && axisItem.hasOwnProperty('min') && axisItem.isCustomSet) {
            let axisIntervalCount = 4
            if (axisIntervalIndex !== undefined) {
              const axisIntervaled = axisArr[axisIntervalIndex]
              if (axisIntervaled.hasOwnProperty('max') && axisIntervaled.hasOwnProperty('min') && axisIntervaled.interval) {
                axisIntervalCount = (axisIntervaled.max - axisIntervaled.min) / axisIntervaled.interval
              }
            }
            axisItem.interval = (axisItem.max - axisItem.min) / axisIntervalCount
            return
          }
          if (!axisItem.show) return
          axisIntervalIndex = index
          if (!axisItem) {
            // TODO: 临时记录下来，防止隐藏掉后数据没了
            axisItem = tempStore(vm, vm.UserConfig.element.id).getItem(`bandwidth_axisitem`)
          } else {
            tempStore(vm, vm.UserConfig.element.id).setItem(`bandwidth_axisitem`, axisItem)
          }
          axisInterval(axisItem)
        })

        // 不均等分布
        inequalityHistogram(vm, options)
        // 双轴图形规则，0刻度对齐
        settingZeroIntevalAlign(vm, options)
        // 设置图形辅助线
        settingAuxiliaryLine(vm, options)
        // 处理维度扩展
        // handleExtendDimension(vm, options)
      }
    }
    // 双维度设置
    chartAlias !== 've-themeRiver' && doubleDimensionSetting(vm, options)
    // 维度颜色设置
    setDimensionColor(vm, options)
  },
  // 设置仪表盘
  settingGauge(vm, options) {
    const { chartUserConfig, chartData } = vm
    const { rows } = chartData
    const { metricAllList, targetList } = vm.UserConfig
    let legend = { show: false }
    if (!rows || !Array.isArray(rows) || !rows.length || !metricAllList.length || !targetList.length) {
      options.series = []
      options.legend = legend
      return
    }
    // 数值显示
    const defaultGaugeConfig = getThemeConfig(vm.themeType, { attributes: ['gaugeConfig'] }).gaugeConfig
    if (chartUserConfig.gaugeTarget?.settingGauge?.axisLine?.lineStyle?.color) {
      defaultGaugeConfig.backgroundColor = chartUserConfig.gaugeTarget.settingGauge.axisLine.lineStyle.color
    }

    // 获取画布的大小
    const elementEl = vm.$el.querySelector('.sdp-chart-content')
    if (!elementEl) return
    const wrapperSize = getRect(elementEl)

    const tabletConfig = vm.tabletConfig()
    const _scale = tabletConfig.tabletScale || 1
    updateSizeByScale(wrapperSize, _scale)

    // 大屏看板预览时，还是根据原始尺寸计算仪表盘位置，最后通过transform缩放
    if (vm.themeFullScreen) {
      const scale = vm.element.scale || 1
      Object.assign(wrapperSize, {
        width: wrapperSize.width / scale,
        height: wrapperSize.height / scale,
      })
    }

    let gaugeConfig = gaugeConfigs.getGaugeConfig(
      chartUserConfig,
      {
        wrapperSize,
        vm,
        themeConfig: defaultGaugeConfig,
      }
    )
    if (!gaugeConfig) {
      gaugeConfig = gaugeConfigs.getGaugeConfig(
        chartUserConfig,
        {
          wrapperSize,
          vm,
          themeConfig: defaultGaugeConfig,
        }
      )
    }
    options.series = gaugeConfig.series
    options.legend = gaugeConfig.legend
  },
  // 设置 仪表盘辅助线
  settingGaugeLine(vm, options) {
    const { series } = options
    const { chartUserConfig, content } = vm
    if (!series || !Array.isArray(series) || !series.length || chartUserConfig.chartAlias !== 've-gauge-normal') return
    const markLineData = []
    const { alertResult } = content
    let { warnLineSettingList, xAuxiliaryLineData = [], gaugeTarget = {}, childChartAlias } = chartUserConfig
    const defaultLine = {
      tooltip: { show: true },
      name: '',
      value: '',
      pointer: { width: 0, length: '81%' },
      emphasis: { itemStyle: { borderWidth: 3 } },
      itemStyle: { borderWidth: 1, borderColor: '#7EC3FB', borderType: [4, 6] },
      title: { show: false },
    }
    const minTick = series[0].hasOwnProperty('min') ? series[0].min : 0
    xAuxiliaryLineData = deepClone(xAuxiliaryLineData.filter(data => !vm.auxiliaryLineHidden[data.id]))
    if ((Array.isArray(xAuxiliaryLineData) && xAuxiliaryLineData.length)) {
      xAuxiliaryLineData.forEach(item => {
        const defaultLineCopy = JSON.parse(JSON.stringify(defaultLine))
        let value = ''
        if (item.fieldType === 'calculatedValue') {
          if (item.valueType === 'min') item.value = minTick
          if (item.valueType === 'max') item.value = minTick + 100
          if (item.valueType === 'average') item.value = minTick + 50
        } else {
          let dataValue = getNumber(item.value)
          if (gaugeTarget.settingGauge.tickValueSetting?.tickType === 'originValue') {
            item.value = dataValue
          }
          if (dataValue >= 100) item.value = minTick + 100
          if (dataValue <= 0) item.value = dataValue
        }
        const lineType = item.lineType || 'dashed'
        defaultLineCopy.itemStyle.borderColor = item.color || '#7EC3FB'
        defaultLineCopy.itemStyle.borderWidth = item.lineWidth || 1
        defaultLineCopy.itemStyle.borderType = lineType === 'dashed' ? [4, 6] : lineType

        const _obj = Object.assign({}, defaultLineCopy, { name: item.name, value: parseInt(item.value) })
        markLineData.push(_obj)
      })
    }
    if (!(gaugeConfigs.HAS_TICK_GAUGE.includes(childChartAlias) && gaugeTarget.settingGauge.tickValueSetting?.tickValueCustom) && warnLineSettingList && warnLineSettingList.length) {
      warnLineSettingList.forEach(item => {
        const defaultLineCopy = JSON.parse(JSON.stringify(defaultLine))
        defaultLineCopy.itemStyle.borderColor = item.warnLineColor
        if (item.calcType === 'range') {
          const defaultLineCopy1 = Object.assign({}, defaultLineCopy, { name: item.name, value: parseInt(item.rangeMin) })
          const defaultLineCopy2 = Object.assign({}, defaultLineCopy, { name: item.name, value: parseInt(item.rangeMax) })
          markLineData.push(defaultLineCopy1, defaultLineCopy2)
        } else {
          const _obj = Object.assign({}, defaultLineCopy, { name: item.name, value: (gaugeTarget.settingGauge.tickValueSetting?.tickType !== 'originValue' && alertResult && Object.keys(alertResult).length && alertResult[item.id]) || getNumber(item.value) || 0 })
          markLineData.push(_obj)
        }
      })
    }
    return markLineData
    // function getRealValue(series, _val) {
    //   let dataValue = _val
    //   if (gaugeTarget.settingGauge.tickValueSetting?.tickType === 'originValue') {
    //     const rangValue = series[0].maxTickValueCustom - series[0].minTickValueCustom
    //     dataValue = Math.abs((dataValue - series[0].minTickValueCustom) / rangValue) * 100
    //   }
    //   return dataValue
    // }
  },
  // 地图配置
  mapSetting(vm, options) {
    const { chartUserConfig, chartSettings, isChartSet, langCode, commonData, content, themeType, utils, mapData, mapFlyLineData, mapOptions, maxData, minData, geoRoamInfo, drillSettings, isRealScreen } = vm
    if (chartUserConfig.chartAlias !== 've-map-parent') return
    const { mapMode = 'administrativeRegional', mapSetting = {}, childChartAlias, mapBoundaryAttribute = {}, mapRangeVal, animationSetting = {}, colors } = chartUserConfig
    const { metricAllList, dimensionList } = vm.UserConfig
    if (!metricAllList.length) return
    if (!dimensionList.length || (childChartAlias === 've-map-britain' && !chartApi.mapSet.data[`country/britain`])) return
    const isRippleAnimation = animationSetting.animationType === 'animation-ripple' && animationSetting.enable
    const seriesMetricItem = {
      metricKeyName: metricAllList[0].keyName,
      originalName: metricAllList[0].alias || metricAllList[0].labeName,
      name: metricAllList[0].lang_alias,
    }
    let series: any = []
    const aliasMap = {
      've-map-china': chartSettings.position || 'china',
      've-map-world': 'world',
      've-map-britain': chartSettings.position || 'britain',
    }
    let position = aliasMap[childChartAlias]
    let isWorldMap = childChartAlias === 've-map-world'
    // let needNameMap = commonData.boardSlectLang() === 'zh' || (langCode === 'zh' && commonData.boardSlectLang() === '' && commonData.isPreview) || (langCode === 'zh' && !commonData.isPreview)
    if (isWorldMap) chartSettings.position = 'world'
    const isLongitudeAndLatitude = mapMode === 'longitudeAndLatitude'
    options.legend = {
      show: false
    }
    const themeConfig = getThemeConfig(themeType, { attributes: ['mapConfig', 'boundaryAttributeConfig'] })
    let defaultThemeConfig = themeConfig.mapConfig
    defaultThemeConfig.itemStyle.normal.areaColor = mapSetting.style.areaColor
    // 世界地图的区域名称显示保存在hasValueRegionName中
    const regionName = isWorldMap ? mapSetting.hasValueRegionName : mapSetting.regionName
    // 仅显示有值
    const onlyShowHasValue = isWorldMap ? regionName.show : (regionName.show && regionName.showOnlyHasValue)
    const isDrill = Array.isArray(drillSettings.drillDimensions) && drillSettings.drillDimensions.length
    const mapType = chartUserConfig.mapType || dimensionList[0].MAPTYPE

    const { res: mapResData, nameMap = {}, dataChangeResult, coordinateData, flyLineData } = (mapType === 'HEATMAP' || !isLongitudeAndLatitude) ? convertData.call(vm, mapData, mapFlyLineData, { isHEATMAP: mapType === 'HEATMAP', }) : convertSpecialData(vm)

    console.log('SSSSS', dataChangeResult, flyLineData)
    // debugger
    if (mapType === 'HEATMAP' && !mapResData) return

    Object.assign(defaultThemeConfig.itemStyle.normal, {
      borderWidth: mapBoundaryAttribute.borderWidth || 0.8,
      borderColor: mapBoundaryAttribute.color || themeConfig.boundaryAttributeConfig.color,
      ...(mapSetting.boundaryStyle.hasShadow ? {
        shadowOffsetX: Number(mapSetting.boundaryStyle.shadowOffsetX),
        shadowOffsetY: Number(mapSetting.boundaryStyle.shadowOffsetY),
        shadowBlur: Number(mapSetting.boundaryStyle.shadowBlur),
        shadowColor: mapSetting.boundaryStyle.shadowColor,
      } : {}),
    })

    // tooltip背景色
    const tooltipBackGround = chartUserConfig.mapTooltipSetting?.color || 'rgba(50,50,50,0.7)'
    let dataChangeSeries: any = null
    if (utils.isScreen && mapSetting.showDataChange && commonData.isPreview) {
        // 地图增量数据
        const { positiveValueStyle, negativeValueStyle, tipsBackgroundColor, tipsPreIcon = {} } = mapSetting.dataChangeStyle
        const commonStyle = {
          padding: [0, 0, 0, tipsPreIcon.pictureId ? 10 : 0],
          textShadowBlur: 0,
        }
        const positiveFontSize = parseInt(positiveValueStyle.fontSize)
        const negativeFontSize = parseInt(negativeValueStyle.fontSize)
        dataChangeSeries = {
          type: 'scatter',
          name: seriesMetricItem.name + '_Variation_',
          originalName: seriesMetricItem.originalName + '_Variation_',
          metricKeyName: seriesMetricItem.metricKeyName,
          coordinateSystem: 'geo',
          // ...getLabelLayout(chartUserConfig),
          ...getSeriesAnimation(),
          symbol: 'circle',
          map: position,
          data: [],
          symbolSize: 1,
          itemStyle: {
            color: 'transparent',
          },
          zlevel: 6,
          label: {
            show: true,
            padding: [6, 10, 6, 10],
            formatter: (val) => {
              const numVal = val.data?.valueData || 0
              if (numVal !== 0) {
                const strPrefix = numVal > 0 ? 'positive' : 'negative'
                return (tipsPreIcon.pictureUrl ? `{${ strPrefix }Icon|}` : '') + `{${ strPrefix }Style|${ numVal > 0 ? '+' : '' }${ val.data.VIEWFORMAT }}`
              }
              return ''
            },
            backgroundColor: tipsBackgroundColor,
            borderRadius: 2,
            rich: {
              negativeStyle: {
                ...commonStyle,
                ...negativeValueStyle,
              },
              positiveStyle: {
                ...commonStyle,
                ...positiveValueStyle,
              },
              positiveIcon: {
                width: positiveFontSize,
                height: positiveFontSize,
                backgroundColor: {
                  image: Vue.prototype.$_getAssetsUrl(tipsPreIcon.pictureUrl),
                  // image: 'https://sdptest.shijicloud.com/fe/img/login-bg.25b38d2e.jpg',
                }
              },
              negativeIcon: {
                width: negativeFontSize,
                height: negativeFontSize,
                backgroundColor: {
                  image: Vue.prototype.$_getAssetsUrl(tipsPreIcon.pictureUrl),
                  // image: 'https://sdptest.shijicloud.com/fe/img/login-bg.25b38d2e.jpg',
                }
              },
            },
          },
          isCustomSeries: true,
        }
    }

    if (mapType === 'HEATMAP') {
      const heatSeries = {
        ...seriesMetricItem,
        ...getLabelLayout(chartUserConfig),
        ...getSeriesAnimation(),
        type: 'map',
        map: position,
        select: { disabled: true, },
        data: mapResData,
        selfSeriesType: 'mapScatter',
        geoIndex: 0,
      }

      let scatterSeries = {
        type: 'scatter',
        selfSeriesType: 'mapScatter',
        ...seriesMetricItem,
        coordinateSystem: 'geo',
        geoIndex: 0,
        symbol: 'circle',
        map: position,
        data: coordinateData,
        symbolSize: chartUserConfig.showSymbolSize ? 0 : 5,
        zlevel: 1,
        ...getLabelLayout(chartUserConfig),
        ...getSeriesAnimation()
      }

      series.push(heatSeries)
      series.push(scatterSeries)
      // 分段图例
      if(!chartUserConfig.isShowLegend && chartUserConfig?.legendType === 'SEGMENTED' && mapSetting?.segmentedLegendType === 'custom') {
        let _arr = []
        let _legendArr = mapSetting?.segmentedLegendType === 'custom' ? mapSetting?.customLegendArr : mapSetting?.defaultLegendArr
        if(_legendArr?.length) {
          _arr = _legendArr.map(item => {
            let _item = {}
            switch(item.type) {
              case '<':
                _item = {lt : Number(item.min), color: item.color || '', label: `<${Number(item.min)}`}
                break;
              case '<=':
                _item = {lte : Number(item.min), color: item.color || '', label: `<=${Number(item.min)}`}
                break;
              case '>':
                _item = {gt :  Number(item.min), color: item.color || '', label: `>${Number(item.min)}`}
                break;
              case '>=':
                _item = {gte :  Number(item.min), color: item.color || '', label: `>=${Number(item.min)}`}
                break;
              case 'range':
                _item = {min :  Number(item.min), max:  Number(item.max), color: item.color || ''}
                break;
            }
            return _item
          })
        }
        options.visualMap = {
          type: 'piecewise',
          bottom: 6,
          precision: 2,
          textStyle: defaultThemeConfig.heatMap.visualMap.textStyle,
          show: !chartUserConfig.isShowLegend,
          pieces: _arr?.length? deepClone(_arr) : [],
          outOfRange: {
            color: defaultThemeConfig.itemStyle.normal.areaColor || deepClone(mapOptions.visualMap.inRange?.color) || ''
          }
        }
        scatterSeries.data = coordinateData.map((item)=> {
          return {...item, itemStyle: {color: Color.getEchartsColorItem(chartUserConfig.colors[0])} }
        })
        series.push(scatterSeries)
      } else {
        let _minData = minData
        let newMaxData = maxData
        if (minData === newMaxData && minData === 0) {
          newMaxData = 1
        } else if (minData === newMaxData) {
          let str = String(minData).split('.')[1]
          const length = str ? str.length : 0
          const baseNum = (minData > 0 ? 9 : 11)
          _minData = _minData * (baseNum * 10 ** length) / 10 ** (length + 1)
        }
        const showMin = isNaN(_minData) ? 0 : _minData
        const showMax = isNaN(newMaxData) ? 0 : newMaxData
        options.visualMap = {
          textStyle: defaultThemeConfig.heatMap.visualMap.textStyle,
          min: showMin,
          max: showMax,
          show: !chartUserConfig.isShowLegend,
          inRange: {
            color: deepClone(mapOptions.visualMap.inRange?.color)
          }
        }
        if(!chartUserConfig.isShowLegend && chartUserConfig?.legendType === 'SEGMENTED' && mapSetting?.segmentedLegendType !== 'custom') {
          // 分段图例-默认
          options.visualMap = {
            ...options.visualMap,
            type:'piecewise',
            precision: 2,
            formatter: (value, value2) => {
              let _value = String(value)==='0' ? 0 : getFormatterFunc('number', Math.pow(10, 2), true)(Number(value))
              let _value2 =  String(value2)==='0' ? 0 : getFormatterFunc('number', Math.pow(10, 2), true)(Number(value2))
              return _value + ' - ' + _value2
            }
          }
          scatterSeries.data = coordinateData.map((item)=> {
            return {...item, itemStyle: {color: Color.getEchartsColorItem(chartUserConfig.colors[0])} }
          })
          series.push(scatterSeries)
        } else {
          options.visualMap = {
            type:'continuous',
            ...deepClone(mapOptions.visualMap),
            ...options.visualMap,
            calculable: isWorldMap,
            precision: 3,
            seriesIndex: 0,
            text: [showMax, showMin].map(value => {
              return (value + '').replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,')
            }),
            show: !chartUserConfig.isShowLegend
          }
        }
        if (chartUserConfig.isMapGradients) {
          options.visualMap.inRange.color = [Color.getEchartsColorItem(chartUserConfig.colors[1]), Color.getEchartsColorItem(chartUserConfig.colors[0])]
        }
      }
    } else {
      options.visualMap = []
      series.push({
        ...seriesMetricItem,
        ...getLabelLayout(chartUserConfig),
        ...getSeriesAnimation(),
        type: isRippleAnimation ? 'effectScatter' : 'scatter',
        coordinateSystem: 'geo',
        zlevel: 1,
        data: mapResData,
        symbol: mapType === 'ICON' ? `path://${ mapTypeSvgPath[chartUserConfig.mapImg] }` : 'circle',
        symbolSize: mapType === 'ICON' ? 15 : function (val) {
          let symbolSizeMax = isRippleAnimation ? 20 : 30
          const singleSize = symbolSizeMax / (maxData - minData)
          let size = Math.max(singleSize * (val[2] - minData) / 2 + symbolSizeMax / 2, symbolSizeMax / 2)
          if (!size) size = 15
          return size
        },
        tooltip: {
          backgroundColor: tooltipBackGround,
          extraCssText: 'max-height: 100%; overflow-y: auto;',
          textStyle: {
            color: '#fff',
            fontFamily: 'NotoSansHans-Regular',
            fontSize: '12',
          },
          formatter(params) {
            return getMapTooltip(vm, params)
          }
        },
        symbolOffset: [0, 0],
        // showEffectOn: 'render',
        // rippleEffect: {
        //   brushType: 'stroke',
        // },
      })

      if (chartUserConfig.isMapGradients && mapType !== 'HEATMAP') {
        options.visualMap = { ...deepClone(mapOptions.visualMap) }
        options.visualMap.calculable = isWorldMap
        options.visualMap.precision = 3
        let _minData = minData
        let newMaxData = maxData
        if (minData === newMaxData && minData === 0) {
          newMaxData = 1
        } else if (minData === newMaxData) {
          let str = String(minData).split('.')[1]
          const length = str ? str.length : 0
          const baseNum = (minData > 0 ? 9 : 11)
          _minData = _minData * (baseNum * 10 ** length) / 10 ** (length + 1)
        }
        const showMin = isNaN(_minData) ? 0 : _minData
        const showMax = isNaN(newMaxData) ? 0 : newMaxData
        options.visualMap.min = showMin
        options.visualMap.max = showMax
        options.visualMap.show = false
        options.visualMap.text = [showMax, showMin]
        options.visualMap.inRange = {
          color: [Color.getEchartsColorItem(chartUserConfig.colors[1]), Color.getEchartsColorItem(chartUserConfig.colors[0])]
        }
      }
    }
    if (dataChangeSeries) {
      dataChangeSeries.data = dataChangeResult
      series.push(dataChangeSeries)
    }
    options.mapLabelShow = { onlyShowHasValue, labelLineShow: chartUserConfig.labelLineShow, regionName, mapType: chartUserConfig.mapType }

    let worldNameMap = {}
    if (isWorldMap) {
      mapResData.forEach(m => {
        const enName = mapConstant.namemapZhToEn.world[m.name]
        if (!enName) return
        worldNameMap[enName] = m.name
      })
    }

    const labelEmphasisStyle = {
      color: mapSetting.emphasisStyle.textStyle.color,
      fontFamily: mapSetting.emphasisStyle.textStyle.fontFamily,
      fontSize: Number(vm.getPxOfHighResolution(mapSetting.emphasisStyle.textStyle.fontSize)),
    }
    const geoStyle = {
      label: {
        show: regionName.show,
        ...(regionName.show ? {
          color: onlyShowHasValue ? 'transparent' : mapSetting.regionName.textStyle.color,
          fontFamily: mapSetting.regionName.textStyle.fontFamily,
          fontSize: Number(vm.getPxOfHighResolution((regionName.show ? mapSetting.regionName.textStyle.fontSize : mapSetting.emphasisStyle.textStyle.fontSize) || 12)),
        } : labelEmphasisStyle),
        formatter(labelData) {
          let hasValue = false
          if (onlyShowHasValue && childChartAlias === 've-map-britain') {
            hasValue = mapResData.find(dataItem => dataItem.name === labelData.name)
            console.log()
          } else if (childChartAlias === 've-map-china') {
            hasValue = mapResData.find(dataItem => dataItem.name.indexOf(labelData.name) > -1)
          }
          const regionName = chartApi.mapSet.getGeoName(labelData, vm, childChartAlias)
          if (!isWorldMap && ((onlyShowHasValue && hasValue) || (!onlyShowHasValue && regionName.show))) {
            return `{regionStyle|${ regionName }}`
          }
          return `{emphasisRegionStyle|${ regionName }}`
        },
        rich: {
          regionStyle: {
            color: mapSetting.regionName.textStyle.color,
            fontSize: Number(vm.getPxOfHighResolution(mapSetting.regionName.textStyle.fontSize || 12)),
            fontFamily: mapSetting.regionName.textStyle.fontFamily,
          },
          emphasisRegionStyle: {
            fontSize: labelEmphasisStyle.fontSize,
            fontFamily: labelEmphasisStyle.fontFamily,
          },
        },
      },
      tooltip: {
        backgroundColor: tooltipBackGround,
        extraCssText: 'max-height: 100%; overflow-y: auto;',
        textStyle: {
          color: '#fff',
          fontFamily: 'NotoSansHans-Regular',
          fontSize: '14',
        },
        formatter: function(params) {
          let value = params.data && params.data.VIEWFORMAT
          if (value) {
            return getMapTooltip(vm, params, true)
          } else {
            return chartApi.mapSet.getGeoName(params, vm, childChartAlias)
          }
        }
      },
      itemStyle: {
        areaColor: mapSetting.style.areaColor,
        borderWidth: mapBoundaryAttribute.borderWidth || 0.8,
        borderColor: mapBoundaryAttribute.color || themeConfig.boundaryAttributeConfig.color,
        ...(mapSetting.boundaryStyle.hasShadow ? {
          shadowOffsetX: Number(mapSetting.boundaryStyle.shadowOffsetX),
          shadowOffsetY: Number(mapSetting.boundaryStyle.shadowOffsetY),
          shadowBlur: Number(mapSetting.boundaryStyle.shadowBlur),
          shadowColor: mapSetting.boundaryStyle.shadowColor,
        } : {}),
      },
      emphasis: {
        label: {
          show: !mapSetting.showDimensionValue || mapRangeVal !== 'provice',
          ...labelEmphasisStyle,
          rich: {
            regionStyle: { ...labelEmphasisStyle },
          },
        },
        itemStyle: {
          areaColor: mapSetting.emphasisStyle.areaColor,
          borderColor: mapSetting.emphasisStyle.borderColor,
          shadowColor: mapSetting.emphasisStyle.shadowColor,
          shadowOffsetX: 2,
          shadowOffsetY: 3,
          shadowBlur: 2,
        },
      },
    }

    options.geo = Object.assign(
      {
        map: position,
        zoom: (mapSetting.saveZoomAndPosition && !isDrill && (isChartSet ? geoRoamInfo.zoom : mapSetting.geoRoamInfo.zoom)) || 1.01,
        roam: (commonData.isPreview || vm.previewType === 'preview-template') ? !mapSetting.roamDisabled : true,
        center: (mapSetting.saveZoomAndPosition && !isDrill && (isChartSet ? geoRoamInfo.center : mapSetting.geoRoamInfo.center)) || (childChartAlias === 've-map-britain' && position === 'britain' ? [-3.5246, 55.3524] : undefined),
        ...geoStyle,
        nameMap: isWorldMap ? worldNameMap : (mapType === 'HEATMAP' ? nameMap : undefined),
      },
      mapType === 'HEATMAP' ? {
        regions: [],
        show: true,
      } : {},
    )
    function showMapLabel(options) {
      if (!['ve-map-china', 've-map-britain'].includes(childChartAlias) || chartUserConfig.mapType === 'HEATMAP') return
      const { onlyShowHasValue, regionName } = options.mapLabelShow
        // options.geo.label.color = onlyShowHasValue ? 'transparent' : regionName.textStyle.color
        // options.geo.label.fontFamily = regionName.textStyle.fontFamily
        // options.geo.label.fontSize = vm.getPxOfHighResolution(regionName.textStyle.fontSize || 12)
        // options.geo.label.show = regionName.show
        let features: any = []
        if (childChartAlias === 've-map-britain') {
          features = chartApi.mapSet.getData(`country/britain`)
          if (!features) return
        }
        options.geo.label.formatter = (params) => {
          let _name = ''
          let paramsName = params.name
          let regionStyleName = ''
          if (chartUserConfig.childChartAlias === 've-map-china') {
            _name = findLangProvinces.call(this, paramsName)
            if (onlyShowHasValue && mapResData.find(dataItem => dataItem.name.indexOf(paramsName) > -1)) {
              regionStyleName = `{regionStyle|${_name}}`
            }
          } else {
            const regionInfo = features?.features?.find(region => region.properties.name === paramsName)
            _name = regionInfo?.properties[`name_${vm.sdpLangcode}`] || paramsName
            if (onlyShowHasValue && chartUserConfig.mapMode !== 'longitudeAndLatitude' && mapResData.find(dataItem => dataItem.name === paramsName)) {
              regionStyleName = `{regionStyle|${_name}}`
            } else if (onlyShowHasValue && chartUserConfig.mapMode === 'longitudeAndLatitude') {
              const latitude = regionInfo?.properties?.latitude
              const longitude = regionInfo?.properties?.longitude

              if (latitude !== undefined && longitude !== undefined) {
                const hasThisData = mapResData.find(dataItem => {
                  const [lon, lat] = dataItem.value
                  return lon === longitude && lat === latitude
                })
                hasThisData && (regionStyleName = `{regionStyle|${_name}}`)
              }
            }
          }
          return regionStyleName || _name
        }
        // let labelEmphasis = options.geo.emphasis?.label || defaultThemeConfig.emphasis.label
        // if (mapSetting.showDimensionValue && mapRangeVal === 'provice') {
        //   labelEmphasis.show = false
        // } else {
        //   delete labelEmphasis.show
        // }
        // options.geo.emphasis = Object.assign({}, options.geo.emphasis || {}, { label: labelEmphasis })
        // options.geo.label.rich = {
        //   regionStyle: {
        //     color: regionName.textStyle.color,
        //     fontSize: vm.getPxOfHighResolution(regionName.textStyle.fontSize || 12),
        //     fontFamily: regionName.textStyle.fontFamily,
        //   }
        // }
    }
    // showMapLabel.call(vm, options)
    options.series = series

    // 地图飞线
    const flyLineList = flyLineData?.filter(item => {
      return item.name && item.destName && item.value?.[0] && item.value?.[1] && item.destGeoCoord?.[0] && item.destGeoCoord?.[1]
    })
    // 1. 开启地图飞线
    // 2. 有符合飞线的数据
    // 3. 非中国地图或者中国地图轮廓为非省份
    if (isRealScreen && mapSetting.flyLine?.enable && flyLineList.length) {
      console.log('飞线：', flyLineList, coordinateData, mapResData, nameMap, childChartAlias, mapRangeVal)
      const { color, radian = 'middle', size = 1, speed = 2, style } = mapSetting.flyLine
      // 线的弧度
      const radianNum = radian === 'small' ? -0.1 : radian === 'large' ? -1 : -0.5
      // 图标
      const styleSymbol = style === 'arrow' ? 'arrow' : style === 'icon' ? 'circle' : 'pin'
      // 图标大小
      const symbolSize = styleSymbol === 'pin' ? size + 4 : size + 8
      const flyLineSerie = {
        ...seriesMetricItem,
        type: 'lines',
        zlevel: 0,
        coordinateSystem: 'geo',
        geoIndex: 0,
        label: {
          show: false,
        },
        effect: {
          show: true,
          period: speed, // 特效动画的时间
          trailLength: 0.01, // 特效尾迹的长度。取从 0 到 1 的值，默认为 0.2，数值越大尾迹越长
          symbol: styleSymbol, // 箭头图标
          symbolSize, // 图标大小
        },
        tooltip: {
          backgroundColor: tooltipBackGround,
          extraCssText: 'max-height: 100%; overflow-y: auto;',
          textStyle: {
            color: '#fff',
            fontFamily: 'NotoSansHans-Regular',
            fontSize: '12',
          },
          formatter(params) {
            return getMapTooltip(vm, params)
          }
        },
        data: flyLineList.map(item => {
          return {
            coords: [
              [item.value[0], item.value[1]],
              item.destGeoCoord
            ],
            lineStyle: {
              color: Color.getEchartsColorItem(colors[0]),
              width: size, // 尾迹线条宽度
              opacity: 0.7, // 尾迹线条透明度
              curveness: radianNum // 尾迹线条曲直度
            },
            row: item.row,
            value: item.valueData
          }
        })
      }
      options.series.push(flyLineSerie)
    }

    console.log('地图OPTIONS: ', options)
  },
  labelPercentSetting(vm, options) {
    const { chartData, chartUserConfig, } = vm
    // 在这里判断一下就不用再formatter里面循环判断了
    const hasShowPercentSwitch = DISPLAY_LABEL_PERCENT.includes(chartUserConfig.chartAlias)
    if (!hasShowPercentSwitch) return
    if (!chartData.rows || !Array.isArray(chartData.rows) || !chartData.rows.length || !options.series?.length) return
    const { metricLabelDisplay, butterflySetting = {} } = chartUserConfig
    const { metricAllList, dimensionExtendList, dimensionList } = vm.UserConfig
    if (!metricLabelDisplay || !Array.isArray(metricLabelDisplay) || !metricLabelDisplay.length) return
    const dimensionOriginalName = dimensionList.map(d => `VIEWFORMAT_${d.alias || d.labeName}`)

    options.series.forEach(serieItem => {
      if (serieItem.type === 'liquidFill') return
      if (serieItem.type === 'bar' && serieItem.isBackground) return
      if (serieItem.type === 'bar' && chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH) return
      if (chartUserConfig.chartAlias === 've-waterfall' && serieItem.total) return

      // 这里把扩展维度认为是维度
      let metricTotal = { total: 0, showPercent: false, show: false, alias: '', dimensionTotal: {}, dimension: [...dimensionOriginalName, ...dimensionExtendList], showPercentLabel: false, showMetricTotalPercent: false }
      const currentMetric = metricAllList.find(m => m.keyName === serieItem.metricKeyName || m.lang_alias === serieItem.name)
      const currentMetricLabel = metricLabelDisplay.find(m => currentMetric.keyName === m.keyName)
      if (currentMetricLabel?.origin.showMetricPercent && !butterflySetting.dataPercent) {
        chartData.rows.forEach((current) => {
          const cValue = getNumber(current[currentMetric.alias || currentMetric.labeName])
          metricTotal.total = metricTotal.total + cValue
          metricTotal.dimensionTotal[current[dimensionOriginalName[0]]] = (metricTotal.dimensionTotal[current[dimensionOriginalName[0]]] || 0) + cValue
        }, 0)
        metricTotal.showPercent = true
        metricTotal.showPercentLabel = currentMetricLabel.origin.showMetricPercentLabel
        metricTotal.show = currentMetricLabel?.origin.showMetricValue || (CUSTOM_METRIC.includes(chartUserConfig.chartAlias) && currentMetricLabel?.labelType === 'custom' && !!currentMetricLabel?.customMetricList?.find(v => v.showMetricValue))
        metricTotal.alias = currentMetric.alias || currentMetric.labeName
        metricTotal.showMetricTotalPercent =  currentMetricLabel.origin.showMetricTotalPercent
      }
      serieItem.metricTotal = metricTotal
    })
  },
  // 柱状图嵌套展示
  nestedHistogram(vm, options) {
    const { chartUserConfig } = vm
    const { chartAlias, histogramDisplayMode, compositeChart } = chartUserConfig
    const histogramNormalChart = chartAlias === 've-histogram-normal' || (chartAlias === 've-composite' && !compositeChart.stack)
    if (!histogramNormalChart || histogramDisplayMode !== 'cascade') return
    let { series, xAxis } = options
    if (!Array.isArray(options.xAxis)) {
      xAxis = new Array(xAxis)
    }
    // 根据series长度组装xAxis数据
    let barWidth = 80
    const xAxisData = xAxis[0].data
    for (let i = 0, len = series.length; i < len; i++) {
      const isBar = series[i].type === 'bar'
      if (i !== 0 && isBar) {
        const axisOption = {
          type: 'category',
          data: xAxisData,
          axisTick: { show: false },
          position: 'bottom',
          axisLabel: { show: false },
          axisLine: { show: false },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'transparent'
            }
          },
        }
        options.xAxis.push(axisOption)
      }
      // 设置层级、x轴数据宽度
      Object.assign(series[i], {
        xAxisIndex: isBar ? i : 0,
        z: i,
        barWidth: barWidth + '%'
      })
      barWidth = barWidth / 2
    }
  },
  // 水球图配置
  liquidFillSetting(vm, options) {
    const { chartUserConfig, content, chartData, themeType } = vm
    const { gaugeTarget, liquidFillSetting = {} } = chartUserConfig
    if (liquidFillSetting.mode === VE_LIQUIDFILL_MODE.NORMAL) return

    const { rows = [] } = chartData
    const { title, titleStyle = { fontSize: 14 }, color, bgColor, waveAnimation = false, titleShow = true } = liquidFillSetting
    const { targetList, metricAllList } = vm.UserConfig
    const _titleStyle = Object.assign({ ...titleStyle }, { fontSize: vm.getPxOfHighResolution(titleStyle.fontSize) })
    const target = gaugeTarget.defaults?.alias || gaugeTarget.defaults?.labeName
    // 水球图的半径与位置
    let radius
    options.series.map(item => {
      if (item.type === 'pie') {
        const dimensionColor = Color.getDimensionColor(vm)
        radius = item.radius[0] || BASE_RADIUS
        item.center = ['50%', '50%']
        item.data = item.data.map((e) => {
          e.itemStyle = {
            ...(e.itemStyle || {}),
            color: dimensionColor.getColor(e.name),
          }
          return e
        })
      }
    })
    if (!targetList.length || !metricAllList.length || !rows.length) return
    if (checkNoDataPermissions({ metric: target, chartResponse: content.chartResponse })) return
    const completionRate = content.chartResponse.measureSummary?.MEASURE_SUMMARY_Sum_COMPLETION_RATE || {}
    const completeRate = completionRate['COMPLETION_RATE'] || 0
    const labelText = `{a|${title}}\n${completionRate['VIEWFORMAT_COMPLETION_RATE']}`
    const liquidFillSeries = {
      type: 'liquidFill',
      id: 'liquidFill',
      itemStyle: {
        normal: {
          // opacity: 0.4,
          // shadowBlur: 0,
          // shadowColor: color,
          // color: color,
        }
      },
      backgroundStyle: getLiquidFillStyle(bgColor),
      waveAnimation: waveAnimation,
      amplitude: 10,
      data: [1, 2, 3].map(i => ({
        value: completeRate > 10 ? 10 : completeRate,
        itemStyle: {
          ...getLiquidFillStyle(color),
          opacity: 0.6
        }
      })),
      name: '',
      color: [color],
      radius: radius,
      center: ['50%', '50%'],
      outline: {
        itemStyle: {
          borderColor: '#86c5ff',
          borderWidth: 0
        },
        borderDistance: 0
      },
      label: {
        show: titleShow,
        insideColor: _titleStyle.color || '#333', // 被波浪覆盖部分的文本的颜色
        formatter: labelText,
        rich: {
          a: {
            ..._titleStyle,
            padding: [0, 0, 15, 0],
          },
        },
        ..._titleStyle,
      },
    }
    options.series.push(liquidFillSeries)
  },
  // 水滴图普通模式
  liquidFillNormalSetting(vm, options) {
    const { chartUserConfig, content, chartData } = vm
    const { rows = [] } = chartData
    const { gaugeTarget, liquidFillSetting = {} } = chartUserConfig
    if (liquidFillSetting.mode !== VE_LIQUIDFILL_MODE.NORMAL) return
    const { title, titleStyle = { fontSize: 14 }, color, bgColor, borderColor, waveAnimation = false, titleShow = true, borderShow = true } = liquidFillSetting
    const _titleStyle = Object.assign({ ...titleStyle }, { fontSize: vm.getPxOfHighResolution(titleStyle.fontSize) })
    const target = gaugeTarget.defaults?.alias || gaugeTarget.defaults?.labeName
    if (!target || !rows.length) return
    if (checkNoDataPermissions({ metric: target, chartResponse: content.chartResponse })) return

    // 获取画布的大小
    const elementEl = vm.$el.querySelector('.sdp-chart-content')
    if (!elementEl) return
    const wrapperSize = getRect(elementEl)

    const tabletConfig = vm.tabletConfig()
    const _scale = tabletConfig.tabletScale || 1
    updateSizeByScale(wrapperSize, _scale)

    // 大屏看板预览时，还是根据原始尺寸计算仪表盘位置，最后通过transform缩放
    if (vm.themeFullScreen) {
      const scale = vm.element.scale || 1
      Object.assign(wrapperSize, {
        width: wrapperSize.width / scale,
        height: wrapperSize.height / scale,
      })
    }

    const gaugeConfig = gaugeConfigs.getGaugeConfig(
      {
        ...chartUserConfig,
        childChartAlias: 'LIQUIDFILL_NORMAL'
      },
      {
        wrapperSize,
        vm,
        themeConfig: {},
      }
    )

    const gaugeSerie = gaugeConfig?.series[0]
    const liquidFillSeries = {
      type: 'liquidFill',
      id: 'liquidFill',
      itemStyle: {},
      backgroundStyle: getLiquidFillStyle(bgColor),
      waveAnimation: waveAnimation,
      amplitude: 10,
      data: [1, 2, 3].map(i => ({
        value: gaugeSerie.data[0].value / 100,
        row: rows[0],
        itemStyle: {
          ...getLiquidFillStyle(color),
          opacity: 0.6
        }
      })),
      name: gaugeSerie.name,
      color: [color],
      radius: `${gaugeSerie.radius * 2}px`,
      center: gaugeSerie.center,
      outline: {
        itemStyle: {
          borderColor: borderColor,
          borderWidth: borderShow ? 1 : 0
        },
        borderDistance: 0
      },
      label: {
        show: titleShow,
        insideColor: _titleStyle.color || '#333', // 被波浪覆盖部分的文本的颜色
        formatter: (p) => { return `{a|${title}}\n${p.data.row['VIEWFORMAT_COMPLETION_RATE']}` },
        rich: {
          a: {
            ..._titleStyle,
            padding: [0, 0, 15, 0],
          },
        },
        ..._titleStyle,
      },
    }
    options.series = [gaugeSerie, liquidFillSeries]
    options.legend = gaugeConfig?.legend
  },
  // 旭日图配置
  sunburstSetting(vm, options) {
    const { chartData = {}, chartUserConfig, isChartSet, themeType, isMobile } = vm
    const { chartAlias } = chartUserConfig
    const data = getEchartsTreeData.call(vm, chartData)
    const isNegativeShowChart = SPECIAL_PROP_TYPE.negativeConfigChartTypes.includes(chartAlias)

    if (isNegativeShowChart) {
      data.forEach(element => {
        element.value = Math.abs(element.value)
        element.children.forEach(item => {
          item.value = Math.abs(item.value)
        })
      })
    }

    // 旭日图主题配色，由于不是chartUserConfig的配置数据，所以与替换主题色的逻辑分开处理
    const themeColor = themeType === THEME_TYPE.darkBlue ? {
      borderColor: '#212D3B',
      circleCenterColor: '#fff',
    } : {
      borderColor: '#fff',
      circleCenterColor: '#ccc',
    }
    options.series = [{
      radius: [0, '48%'], // 用于控制下钻点的大小，控制下钻点大小与旭日图空白圈相等，避免内圈标签显示在下钻点范围内
      type: 'sunburst',
      ...getLabelLayout(chartUserConfig),
      ...getSeriesAnimation(),
      sort: null,
      emphasis: { focus: 'none', },
      data: data,
      nodeClick: 'false',
      // 内外圈比例为6:4
      levels: [
        // 留给数据下钻点的空白配置
        {
          itemStyle: {
            color: themeColor.circleCenterColor
          },
        },
        // 最内层
        {
          r0: '15%',
          r: '48%',
          nodeClick: isChartSet || isMobile ? 'false' : 'rootToNode', // 编辑界面内层钻取失效
          label: {
            align: 'left',
            padding: 5,
          },
          itemStyle: {
            borderColor: themeColor.borderColor,
            borderWidth: 0.5,
            // opacity: 0.85,
          }
        },
        // 最外层
        {
          r0: '48%',
          r: '70%',
          label: {
            position: 'outside',
            rotate: '0',
            distance: isMobile ? 6 : 10,
            // padding: 6,
          },
          itemStyle: {
            borderColor: themeColor.borderColor,
            borderWidth: 0.5,
          }
        }
      ]
    }]
    // 旭日图配色
    setDimensionColor(vm, options)
  },
  settingTreemap(vm, options) {
    const { chartData = {}, chartUserConfig = {}, content } = vm
    const { series = [] } = options
    const { treemapSetting = {}, pieSetting = {} } = chartUserConfig
    const { plateSpacing = true, spacingWidth = 1 } = pieSetting
    const { metricAllList, dimensionList } = vm.UserConfig
    const { upperLabel: _upperLabel, upperBackgroundColor } = Object.assign(getThemeConfig(
      vm.themeType,
      {
        attributes: ['treemapConfig'],
      }).treemapConfig, treemapSetting)
    const metric = metricAllList[0].alias || metricAllList[0].labeName
    const parentStrKey = `VIEWFORMAT_METRIC_PERCENT_${metric}`
    let fontSize = treemapSetting.upperLabel ? treemapSetting.upperLabel.fontSize : 14
    fontSize = vm.getPxOfHighResolution(fontSize)
    const fontFamily = treemapSetting.upperLabel ? treemapSetting.upperLabel.fontFamily : 'NotoSansHans-Regular'
    // const color = treemapSetting.upperLabel ? treemapSetting.upperLabel.color : '#fff'
    const height = (getStrSize('a', { fontSize: fontSize + 'px', fontFamily }).height + 2) * 2
    series.forEach(seriesItem => {
      Object.assign(seriesItem, {
        type: 'treemap',
        data: getEchartsMapTreeData.call(vm, chartData),
        itemStyle: {
          borderColor: upperBackgroundColor,
          borderWidth: plateSpacing ? spacingWidth : 0,
        },
        breadcrumb: {
          show: false
        },
        roam: false,
        nodeClick: false,
        // animationDuration: 500,
        width: '90%',
        height: '100%',
        upperLabel: {
          show: dimensionList.length === 2 && treemapSetting.upperLabelShow,
          height: treemapSetting.upperLabelShow ? height : 0,
          color: _upperLabel.color,
          emphasis: {
            color: _upperLabel.color,
          },
          fontFamily,
          fontSize,
          formatter: (e) => {
            let str = ''
            const data = e.data
            const metricKey = `VIEWFORMAT_${metric}`
            if (dimensionList.length && data[metricKey]) {
              const _end = data[parentStrKey] ? `(${data[parentStrKey]})` : ''
              str = `${e.name}\n${data[metricKey] || data.value || ''}${_end}`
            }
            return str
          }
        }
      })
    })
    Object.assign(options, {
      legend: { show: false },
      grid: { show: false },
      xAxis: { show: false },
      yAxis: { show: false },
    })
    setDimensionColor(vm, options)
  },
  themeRiverSetting(vm, options) {
    let { dataZoom, grid } = options
    let { content, chartData, chartUserConfig } = vm
    if (!content.chartResponse) return
    const { dimensionList } = vm.UserConfig
    const { dimension, metrics, dimensionNullValue } = content.chartResponse
    options.legend.data = []
    if (!chartData.rows.length || !dimension.length || !metrics.length) return

    let _rows: any[] = []
    let timeSet: any = new Set()
    let dateDimensionIndex = dimensionList.findIndex(d => d.columnTpe === 'date')
    let xAxisFormatter = getXaxisLabel()
    let axisConfig = deepClone(chartUserConfig.xAxis)
    axisConfig.axisLabel.fontSize = vm.getPxOfHighResolution(axisConfig.axisLabel.fontSize)
    let xAxisLabelSizeArray: any[] = []
    let seriesData: any[] = []
    let legendData = new Set()
    chartData.rows.forEach(row => {
      if (row[dimension[0]] === dimensionNullValue) return
      _rows.push(row)
      const label = xAxisFormatter(row)
      const labelSize = getStrSize(label, { fontSize: axisConfig.axisLabel.fontSize + 'px' })
      xAxisLabelSizeArray.push(labelSize)
      legendData.add(row[dimension[1]])
      seriesData.push([label, row[metrics[0]], row[dimension[1]], row[`VIEWFORMAT_${metrics[0]}`]])
      timeSet.add(xAxisFormatter(row))
    })
    timeSet = Array.from(timeSet)

    if (!_rows.length) return

    // 以第一个数据作为主干河流
    let firstDimensionData = _rows.filter(row => row[dimension[1]] === _rows[0][dimension[1]])
    timeSet.forEach(time => {
      if (!firstDimensionData.find(row => xAxisFormatter(row) === time)) {
        seriesData.push([time, 0, _rows[0][dimension[1]]])
      }
    })
    const themeConfig = getThemeConfig(vm.themeType, { attributes: ['themeRiverConfig'] })
    let seriesItem = {
      type: 'themeRiver',
      data: seriesData,
      color: [],
    }
    let timeDimensionObj = {}
    if (dateDimensionIndex === 0) {
      let timeOrder = timeSet.sort((a, b) => a > b ? 1 : -1)
      let minTime = new Date(...getTime(timeOrder[0])).getTime()
      let maxTime = new Date(...getTime(timeOrder[timeOrder.length - 1])).getTime()
      timeDimensionObj = {
        type: 'time',
        interval: (maxTime - minTime) / 4,
        min: minTime,
        max: maxTime,
      }

      // 8703 支持事件日历标签显示，这里再使用事件日历方法来处理一次
      const oldFormatter = options?.singleAxis?.axisLabel?.formatter
      const oldRich = options?.singleAxis?.axisLabel?.rich || {}
      let formatter = (value, index) => timeFormatter(value)
      let rich = {}
      if (oldFormatter) {
        formatter = (value, index) => {
          const formatterValue = timeFormatter(value)
          return oldFormatter(formatterValue, index)
        }
        rich = oldRich
      }
      axisConfig.axisLabel.formatter = formatter
      axisConfig.axisLabel.rich = rich
    }
    let singleAxis = {
      ...axisConfig,
      type: dateDimensionIndex === 0 ? 'time' : 'value',
      axisPointer: {
        animation: true,
        label: {
          show: vm.isChartSet || !vm.isMobile,
          formatter: (params) => timeFormatter(params.value),
          fontSize: vm.getPxOfHighResolution(12)
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: themeConfig.themeRiverConfig.splitLine.lineStyle.color,
          type: 'solid',
        }
      },
      axisTick: { show: false },
      ...timeDimensionObj,
    }
    if (axisConfig.axisLabel.show) {
      if (axisConfig.axisLabel.rotate === 90) {
        singleAxis.bottom = Math.max(...xAxisLabelSizeArray.map(size => size.width))
      } else if (axisConfig.axisLabel.rotate === -45 || axisConfig.axisLabel.rotate === 45) {
        singleAxis.bottom = Math.max(...xAxisLabelSizeArray.map(size => size.width)) / Math.sqrt(2)
      } else {
        singleAxis.bottom = xAxisLabelSizeArray[0].height
      }
    } else {
      singleAxis.bottom = 0
    }
    const formatterAndRich = getHolidayFormatter(vm)
    options.name = dimension[0]
    options.series = [seriesItem]
    options.singleAxis = singleAxis
    options.xAxis = []
    options.yAxis = []
    options.legend.data = Array.from(legendData)
    options.tooltip = {
      show: !!chartUserConfig.isHover,
      trigger: 'axis',
      confine: true,
      axisPointer: {
        type: 'line',
        lineStyle: {
          // color: 'rgba(0,0,0,0.2)',
          width: 1,
          type: 'solid'
        }
      },
      formatter: (params) => {
        let result = [params[0].data[0]]
        let markerArray = []
        params.forEach(srs => {
          if (srs.value.length <= 3) return
          const marker = srs.marker.replace(/10px/g, vm.getPxOfHighResolution('10px'))
          markerArray.push({
            label: `${marker} ${srs.name}: ${srs.data[3]}`,
            dimension1: srs.name,
          })
        })
        _rows.filter(row => {
          return xAxisFormatter(row) === result[0]
        }).forEach(rowItem => {
          let markItem = markerArray.find(m => m.dimension1 === rowItem[dimension[1]])
          markItem && result.push(markItem.label)
        })
        result[0] = formatterAndRich.formatter(result[0], 0, 'text')
        return result.join('</br>')
      }
    }
    setSingleAxisPosition(vm, options)
    if (dataZoom.length) {
      options.dataZoom = [{ ...dataZoom[0], xAxisIndex: undefined }]
    }
    function getXaxisLabel() {
      if (dateDimensionIndex === 0) {
        return (row) => {
          return row['VFMT_RAW_' + dimension[0]]
        }
      } else {
        return (row) => {
          return row['VIEWFORMAT_' + dimension[0]]
        }
      }
    }
    function getTime(timeStr) {
      return timeStr.split(/\D/).map((val, index) => {
        if (index === 1) {
          return Number(val) - 1
        }
        return Number(val)
      })
    }
    function timeFormatter(params) {
      let time = new Date(params)
      let [yyyy, MM, dd, HH, mm, ss] = [time.getFullYear(), time.getMonth() + 1, time.getDate(), time.getHours(), time.getMinutes(), time.getSeconds()]
      return dimensionList[0].dateFormat.replace('yyyy', yyyy).replace('MM', `00${MM}`.substr(-2)).replace('dd', `00${dd}`.substr(-2)).replace('HH', `00${HH}`.substr(-2)).replace('mm', `00${mm}`.substr(-2)).replace('ss', `00${ss}`.substr(-2))
    }
  },
  treeSetting(vm, options) {
    const { chartUserConfig = {} } = vm
    const { series } = options
    const { treeSetting = {}, labelConfig = {}, colors = [] } = chartUserConfig
    const { baseName, baseNameStyle, orient, layout, treeExpandFold } = treeSetting

    const themeConfig = getThemeConfig(
      vm.themeType,
      {
        attributes: ['labelConfig', 'funnelLabelConfig', 'treeConfig'],
        chartAlias: chartUserConfig.chartAlias,
      })
    const labelThemeConfig = Object.assign(themeConfig.labelConfig, labelConfig)

    const isLeft = orient === 'LR'
    const _position = isLeft ? 'right' : 'left'
    const isOrthogonal = layout === 'orthogonal'
    if (!series[0]) return
    const _left = '20%'
    const _right = '25%'
    options.series = [Object.assign(series[0], {
      name: baseName,
      // 树的方向
      orient,
      // 树的布局
      layout,
      // width: '80%',
      // height: '70%',
      // top: isOrthogonal ? '10%' : '18%',
      left: !isOrthogonal ? _left : isLeft ? _left : _right,
      // bottom: isOrthogonal ? '5%' : '18%',
      right: !isOrthogonal ? _left : isLeft ? _right : _left,
      symbolSize: 7,
      // symbolSize: treeExpandFold ? 7 : 0.001,
      itemStyle: {
        color: Color.getEchartsColorItem(colors[0]),
        borderColor: Color.getEchartsColorItem(colors[0]),
      },
      lineStyle: {
        width: vm.getPxOfHighResolution(1),
        color: vm.themeType === 'sdp-classic-white' ? '#DDDDDD' : '#434A60'
      },
      leaves: {
        label: {
          position: isOrthogonal ? _position : '',
          color: labelThemeConfig.color,
          fontFamily: labelThemeConfig.fontFamily,
          fontSize: vm.getPxOfHighResolution(labelThemeConfig.fontSize),
        }
      },
    })]
    Object.assign(options, {
      legend: { show: false }
    })
  },
  calendarSetting(vm, options) {
    const { chartData, chartUserConfig, content, isMobile } = vm
    const { series, grid } = options
    const { chartAlias, calendarSettings = {}, colors } = chartUserConfig
    if (!content.chartResponse || chartAlias !== 've-calendar') return
    const { factor: factorResponse } = content.chartResponse
    const { date, arrangement, startDate, calendarType, isShowYear, yearSetting, isShowMonth, monthSetting,
      isShowWeek, weekSetting, visualMapping = false, splitLineShow = false, splitLineSetting, zebraCrossing,
      zebraCrossingColor1, zebraCrossingColor2, openCalendarPage = false, monthsOfOnePage = 1, weekColorSetting,
      isShowMeasurementCalculation, factorList = [], measurementSetting } = calendarSettings
    const isWhiteTheme = vm.themeType === 'sdp-classic-white'
    const _ViturCalendarData = Math.abs(ViturCalendarData)
    const themeConfig = getThemeConfig(
      vm.themeType,
      {
        attributes: ['calendarConfig'],
        chartAlias: chartUserConfig.chartAlias,
      })
    const { yearSetting: yearThemeConfig, monthSetting: monthThemeConfig, weekSetting: weekThemeConfig, measurementSetting: factorThemeConfig } = themeConfig.calendarConfig
    const yearConfig = Object.assign(yearThemeConfig, yearSetting)
    const monthConfig = Object.assign(monthThemeConfig, monthSetting)
    const weekConfig = Object.assign(weekThemeConfig, weekSetting)
    const factorConfig = Object.assign(factorThemeConfig, measurementSetting)

    const isHeatmap = calendarType === 'heatmap'
    // 热力日历图不支持斑马线颜色
    const supportZebraCross = zebraCrossing && !isHeatmap
    const _langCode = vm.sdpLangcode === 'zh' ? 'cn' : 'en'

    const param = { ...chartData, calendarFirstDate: vm.calendarFirstDate, monthsOfOnePage, openCalendarPage, calendarDateRange: vm.calendarDateRange }
    const acrossYearArr = getCalendardata(param, content.chartResponse)
    const isVertical = arrangement === 'vertical'
    // 存储实际展示的日历数据
    vm.calendarRealData = deepClone(acrossYearArr.map(e => e.data))
    const seriesItem = series[0]
    let maxDataArr = []
    let minDataArr = []
    let calendar = []
    // 用于设置斑马线颜色
    let visualMapSeries = []
    const acrossYearNum = acrossYearArr.length
    // 获取画布的大小
    const elementEl = vm.$el.querySelector('.sdp-chart-content')
    if (!elementEl) return
    const wrapperSize = getElementSize(elementEl)

    const tabletConfig = vm.tabletConfig()
    const _scale = tabletConfig.tabletScale || 1
    updateSizeByScale(wrapperSize, _scale)

    const padding_content = (vm.isMobile && !vm.isChartSet) ? 10 : 24
    const padding_top = (vm.isMobile && !vm.isChartSet) ? 10 : 24
    const padding_left = (vm.isMobile && !vm.isChartSet) ? 10 : 32

    Object.assign(Array.isArray(grid) ? grid[0] : grid, { top: padding_top, right: padding_left, bottom: padding_left, left: padding_left })
    function getSize() {
      const yearMargin = isShowYear ? vm.getPxOfHighResolution((vm.isMobile && !vm.isChartSet) ? 4 : 12) : 0
      let _yearLabelSize = vm.getPxOfHighResolution(isShowYear ? getStrSize('yyyy', { fontSize: yearConfig.fontSize + 'px' }).height : 0)
      let _monthLabelSize = vm.getPxOfHighResolution(isShowMonth ? getStrSize(_langCode === 'cn' ? '十二月' : 'Jan', { fontSize: monthConfig.fontSize + 'px' })[isVertical ? 'width' : 'height'] : 0)
      let _dayLabelSize = vm.getPxOfHighResolution(isShowWeek ? getStrSize(_langCode === 'cn' ? '五' : 'Mon', { fontSize: weekConfig.fontSize + 'px' })[isVertical ? 'height' : 'width'] : 0) + yearMargin
      let _factorLabelSize = 0
      // 区域指标汇总
      if (isShowMeasurementCalculation && factorResponse && isShowMonth) {
        const alias = factorList[0].alias || factorList[0].labeName
        const fieldCalcTypeLabel = FIELD_CALC_TYPE_LIST(vm).find(v => v.value === factorList[0].fieldCalcType)?.label
        // 计算区域指标汇总标签长度
        if (isVertical) {
          // 计算区域指标标签长度
          const factorLabelArray = Object.keys(factorResponse).map(date => {
            const labelItem = factorResponse[date][`VIEWFORMAT_${alias}`] + (fieldCalcTypeLabel ? `（${fieldCalcTypeLabel}）` : '')
            return getStrSize(labelItem, { fontSize: vm.getPxOfHighResolution(factorConfig.fontSize) + 'px' }).width
          })
          _monthLabelSize = Math.max(...factorLabelArray, _monthLabelSize)
        } else {
          _factorLabelSize = getStrSize('TEXT', { fontSize: vm.getPxOfHighResolution(factorConfig.fontSize) + 'px' }).height
          _monthLabelSize = _factorLabelSize + _monthLabelSize
        }
      }
      const _res = {
        yearMargin,
        yearLabelSize: isShowYear ? parseFloat(vm.getPxOfHighResolution(yearConfig.fontSize)) : 0,
        monthLabelSize: isShowMonth ? parseFloat(vm.getPxOfHighResolution(monthConfig.fontSize)) : 0,
        dayLabelSize: isShowWeek ? parseFloat(vm.getPxOfHighResolution(weekConfig.fontSize)) : 0,
        visualMapSize: isHeatmap && visualMapping ? 40 : 0,
        calendarTop: (i) => padding_top,
        calendarLeft: (i) => padding_left,
        perCalendarWidth: 0,
        perCalendarHeight: 0,
        _monthLabelSize,
        _yearLabelSize,
        _dayLabelSize,
      }
      let horizontalLabelSize = 0; let verticalLabelSize = 0
      if (isVertical) {
        horizontalLabelSize = (_monthLabelSize + padding_content) * acrossYearNum - padding_content
        verticalLabelSize = _yearLabelSize + _dayLabelSize
      } else {
        horizontalLabelSize = _yearLabelSize + _dayLabelSize
        verticalLabelSize = (_monthLabelSize + padding_content) * acrossYearNum - padding_content
      }
      const perCalendarWidth = (wrapperSize.width - (padding_left + padding_left + horizontalLabelSize)) / (isVertical ? acrossYearNum : 1)
      const perCalendarHeight = (wrapperSize.height - (padding_left + padding_top + _res.visualMapSize + verticalLabelSize)) / (isVertical ? 1 : acrossYearNum)
      Object.assign(_res, {
        perCalendarWidth,
        perCalendarHeight,
        calendarTop: (i) => {
          let top = 0
          const base = ((isVertical && !isShowWeek && !isShowYear) || (!isVertical && !isShowMonth)) ? padding_top : (padding_top - 4)
          if (isVertical) {
            top = verticalLabelSize
          } else {
            top = i === 0 ? _monthLabelSize : ((_monthLabelSize + perCalendarHeight + padding_content) * i + _monthLabelSize)
          }
          return base + top
        },
        calendarLeft: (i) => {
          let left = 0
          const base = ((isVertical && !isShowMonth) || (!isVertical && !isShowWeek && !isShowYear)) ? padding_left : (padding_left - 4)
          if (isVertical) {
            left = i === 0 ? _monthLabelSize : ((_monthLabelSize + perCalendarWidth + padding_content) * i + _monthLabelSize)
          } else {
            left = _yearLabelSize + _dayLabelSize
          }
          return base + left
        },
      })
      return _res
    }
    const sizeMap = getSize()

    acrossYearArr.forEach((item, index) => {
      const { data = [], _range, zebraCrossData = [] } = item
      const _serieItem = deepClone(seriesItem)
      const realValueArr = data.filter(e => !Array.isArray(e) && e.row).map(e => e.value[1])
      const max = data.length && Math.max(...realValueArr)
      maxDataArr.push(max)
      const min = data.length && Math.min(...realValueArr)
      minDataArr.push(min)

      const calendarItem = {
        // range: data.length ? _range : isChartSet ? range : nowDate,
        // range: nowDate || (data.length ? _range : getNowMonth()),
        range: data.length ? _range : getNowMonth(),
        cellSize: 'auto',
        top: sizeMap.calendarTop(index),
        left: sizeMap.calendarLeft(index),
        width: sizeMap.perCalendarWidth,
        height: sizeMap.perCalendarHeight,
        orient: arrangement,
        itemStyle: {
          color: 'rgba(0,0,0,0)',
        },
        splitLine: {
          show: splitLineShow,
          lineStyle: splitLineSetting,
        },
        dayLabel: {
          show: isShowWeek,
          firstDay: startDate,
          color: weekConfig.color,
          fontSize: sizeMap.dayLabelSize,
          fontFamily: weekConfig.fontFamily,
          nameMap: weekData[_langCode],
          margin: 4,
        },
        monthLabel: {
          show: isShowMonth,
          color: monthConfig.color,
          fontSize: sizeMap.monthLabelSize,
          fontFamily: monthConfig.fontFamily,
          nameMap: _langCode,
        },
        yearLabel: {
          show: isShowYear,
          color: yearConfig.color,
          fontSize: sizeMap.yearLabelSize,
          fontFamily: yearConfig.fontFamily,
          margin: isVertical ? sizeMap._dayLabelSize : sizeMap._dayLabelSize,
        },
      }
      // 区域指标汇总
      if (isShowMeasurementCalculation && factorResponse) {
        const alias = factorList[0].alias || factorList[0].labeName
        const fieldCalcTypeLabel = FIELD_CALC_TYPE_LIST(vm).find(v => v.value === factorList[0].fieldCalcType)?.label
        Object.assign(calendarItem.monthLabel, {
          formatter: (param) => {
            const date = `${param.yyyy}-${param.MM}`
            let base = `{a|${param.nameMap}}`
            if (factorResponse[date]) {
              const factorValue = factorResponse[date][`VIEWFORMAT_${alias}`] + (fieldCalcTypeLabel ? `（${fieldCalcTypeLabel}）` : '')
              base = (base ? `${ base }\n` : '') + `{b|${factorValue}}`
            }
            return base
          },
          rich: {
            a: {
              color: monthConfig.color,
              fontSize: sizeMap.monthLabelSize,
              fontFamily: monthConfig.fontFamily,
              align: 'center',
            },
            b: {
              color: factorConfig.color,
              fontSize: vm.getPxOfHighResolution(factorConfig.fontSize),
              fontFamily: factorConfig.fontFamily,
              align: 'center',
            },
          },
        })
      }
      calendar.push(calendarItem)
      Reflect.deleteProperty(_serieItem, 'yAxisIndex')
      Object.assign(_serieItem, {
        // scatter,heatmap
        type: isHeatmap ? 'heatmap' : 'scatter',
        coordinateSystem: 'calendar',
        calendarIndex: index,
        data,
        symbolSize: (e) => {
          const size = e[1] / max * 30
          const noSize = Math.abs(e[1]) === _ViturCalendarData || calendarType === 'none'
          return noSize ? 0.0001 : size < 8 ? 8 : size
        }
      })
      series[index] = _serieItem
      if (supportZebraCross) {
        visualMapSeries.push({
          type: 'heatmap',
          coordinateSystem: 'calendar',
          calendarIndex: index,
          data: zebraCrossData,
          calendarSeriesType: 'zebraCross', // 用于区分是斑马线数据还是正常日历数据
        })
      }
    })
    visualMapSeries.length && options.series.push(...deepClone(visualMapSeries))

    const visualMapColor = isWhiteTheme
      ? ['#fff', Color.getEchartsColorItem(colors[0])]
      : ['#212d3b', Color.getEchartsColorItem(colors[0])]
    let maxData = Math.max(...maxDataArr)
    let minData = Math.min(...minDataArr)
    if (maxData === minData) {
      minData -= 1
    }
    const visualMap = {
      show: false,
      calculable: true,
      orient: 'horizontal',
      inRange: {
        color: calendarType !== 'none' ? visualMapColor : 'transparent',
      },
      textStyle: {
        color: isWhiteTheme ? '#333' : '#fff'
      },
      bottom: padding_top - 8,
      left: padding_left,
    }
    if (Math.abs(minData) !== Infinity || Math.abs(maxData) !== Infinity) {
      Object.assign(visualMap, {
        show: calendarType === 'heatmap' && visualMapping,
        min: minData,
        max: maxData,
      })
    }
    if (supportZebraCross) {
      const visualMapSeriesIndex = visualMapSeries.map((item, i) => acrossYearNum + i)
      Object.assign(visualMap, {
        type: 'piecewise',
        pieces: [
          { value: _ViturCalendarData, color: zebraCrossingColor1 },
          { value: ViturCalendarData, color: zebraCrossingColor2 },
        ],
        seriesIndex: visualMapSeriesIndex,
      })
    }
    Object.assign(options, {
      calendar,
      xAxis: [],
      yAxis: [],
      legend: {
        show: false
      },
      visualMap: isHeatmap || zebraCrossing ? visualMap : undefined
    })
  },
  barPercentSetting(vm, options) {
    const { chartData, chartUserConfig, content, themeType, isChartSet } = vm
    const { series, xAxis, yAxis } = options
    let { dimensionColors = [], chartAlias, complationRate = {}, alignmentMethod = '' } = chartUserConfig
    const { targetList } = vm.UserConfig
    if (chartAlias !== 've-bar-percent' || !Array.isArray(chartData.rows) || !chartData.rows.length || !Array.isArray(series) || !series.length || !series[0].data || !series[0].data.length) {
      options.xAxis && options.xAxis.length && options.xAxis.forEach(xais => { xais.show = false })
      return
    }
    let _complationRate = deepClone(complationRate)
    _complationRate.textStyle.fontSize = vm.getPxOfHighResolution(complationRate.textStyle.fontSize)
    let newDimensionColors = Color.getDimensionColor(vm)
    const { dimension = [] } = content.chartResponse
    const { rowsData, ...xAxisRange } = handlePercentAxis({ chartData, chartUserConfig })
    const barPercentConfig = getThemeConfig(themeType, { attributes: ['barPercentConfig', 'axisConfig'], chartAlias: chartAlias })
    let richObj = {}
    let barPercentColors = chartData.rows.map((r, i) => {
      let hasDimensionColor = newDimensionColors.noRepeat.find(dc => dc.name === chartData.rows[i][`VIEWFORMAT_${dimension[0]}`])
      const rangeColor = gaugeConfigs.getCompletionColor(rowsData[i], _complationRate.rateArray)
      richObj[`${i}`] = {
        color: rangeColor.color || _complationRate.textStyle.color,
        fontSize: _complationRate.textStyle.fontSize,
        fontFamily: _complationRate.textStyle.fontFamily,
      }
      return { barColor: rangeColor.color || Color.getEchartsColorItem(hasDimensionColor) }
    })
    let needShowOptionChart = [
      // CHART_ALIAS_TYPE.VE_BAR,
      CHART_ALIAS_TYPE.VE_BAR_PERCENT,
      // CHART_ALIAS_TYPE.VE_BAR_STACK,
      // CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT
    ]
    // 如果设置了图表对齐方式为right，且是百分比条形图
    let isAlignmentMethod = needShowOptionChart.includes(chartAlias) && !!alignmentMethod && alignmentMethod === 'right'
    let seriesItem = {
      type: 'bar',
      itemStyle: {
        color: barPercentConfig.barPercentConfig.barBackgroundColor,
      },
      ...getLabelLayout(chartUserConfig),
      ...getSeriesAnimation(),
      barGap: '-100%',
      animation: false,
      data: series[0].data.map(data => xAxisRange.max),
      label: {
        show: !!targetList.length,
        position: isAlignmentMethod ? 'left' : 'right',
        formatter: (param) => {
          return `{${param.dataIndex}|${chartData.rows[param.dataIndex]['VIEWFORMAT_COMPLETION_RATE']}}`
        },
        ..._complationRate.textStyle,
        rich: richObj
      },
      xAxisIndex: '0',
      isBackground: true,
      silent: true,
    }
    let backgroundSeries = [seriesItem]
    if (xAxisRange.min < 0) {
      backgroundSeries.push(Object.assign({},
        seriesItem,
        { data: series[0].data.map(data => xAxisRange.min), label: { show: false } }
      ))
    }

    let serieItem0 = {
      ...series[0],
      data: rowsData.map((r, i) => {
        return {
          value: r,
          name: chartData.rows[i][`VIEWFORMAT_${dimension[dimension.length - 1]}`],
          itemStyle: {
            barBorderRadius: isAlignmentMethod ? r > 0 ? [500, 0, 0, 500] : [0, 500, 500, 0]  : r < 0 ? [500, 0, 0, 500] : [0, 500, 500, 0],
            color: barPercentColors[i].barColor,
          },
          row: chartData.rows[i]
        }
      }),
      z: 10,
    }
    if (series.length > 1) {
      let [s, ...otherSeries] = series
      backgroundSeries.unshift(...otherSeries)
    }
    Object.assign(xAxis[0], xAxisRange)

    yAxis[0].z = 11
    let complationRateArray = chartData.rows.map(r => getStrSize(r['VIEWFORMAT_COMPLETION_RATE'], { fontSize: `${_complationRate.textStyle.fontSize}px` }).width)
    if (!vm.isMobile) {
      options.grid.right = Math.max(...complationRateArray) + options.grid.right
    } else {
      options.grid.right = Math.max(...complationRateArray) + (vm.isChartSet ? 16 : 5)
    }
    options.series = [serieItem0, ...backgroundSeries]
    options.xAxis = [xAxis[0]]
  },
  // 象形柱图配置
  async pictorialBarSetting(vm, options) {
    const { chartUserConfig, utils, chartData, isMobile, isChartSet } = vm
    if (!chartUserConfig.pictorialBarSettings) return
    const { xAxis, yAxis, series } = options
    const { flipAxis = false, symbolRepeat = false, symbolType = 'basicShape', symbol = [], svgPath = '', customIconSetting = [] } = chartUserConfig.pictorialBarSettings
    options.series.forEach((item, index) => {
      let chartSymbol = symbol[index]
      if (symbolType === 'icon' && customIconSetting[index] && customIconSetting[index].svgPath) {
        chartSymbol = `path://${customIconSetting[index].svgPath}`
      }
      Object.assign(item, {
        type: 'pictorialBar',
        barGap: '10%',
        symbolRepeat: symbolRepeat,
        symbol: chartSymbol,
        symbolSize: ['100%', '100%'],
        symbolClip: true,
        itemStyle: {
          borderWidth: 0,
        },
      })
      if (item.yAxisIndex && yAxis[item.yAxisIndex]) {
        const axis = yAxis[item.yAxisIndex]
        if ((axis.hasOwnProperty('max') && axis.hasOwnProperty('min')) && axis.isCustomSet) {
          const range = Math.max(Math.abs(axis.max), Math.abs(axis.min))
          Object.assign(item, {
            symbolBoundingData: range,
            symbolOffset: [axis.min * 100 / range + '%', 0],
          })
        }
      }
      // 复用图片特殊处理，当数据量过小时,基准柱宽度过大，需要重新定图标大小
      if (symbolRepeat && !isChartSet) {
        if (chartData.rows.length < 20 && !isMobile) {
          if (chartData.rows.length <= 3) {
            item.symbolSize = ['9%', '9%']
          } else if (chartData.rows.length <= 5) {
            item.symbolSize = ['15%', '15%']
          } else {
            // 数据量每5条为一个档位，一个档位增加10%的比例。如5-10条为20% 30%
            const baseValue = Math.ceil(chartData.rows.length / 5) * 10 + '%'
            item.symbolSize = [baseValue, baseValue]
          }
        }
        if (chartData.rows.length < 10 && isMobile) {
          // 数据量每2条为一个档位，一个档位增加20%的比例
          const baseValue = Math.ceil(chartData.rows.length / 2) * 20 + '%'
          item.symbolSize = [baseValue, baseValue]
        }
      }
    })
    // yAxis.length > 1 && yAxis.pop()
    // 反转y轴
    if (flipAxis) {
      // 维度数据
      // const xAxisCopy = deepClone(xAxis)
      // options.xAxis = deepClone(yAxis)
      // options.yAxis = xAxisCopy
    }
  },
  // 堆叠条形图配置
  stackBarSetting(vm, options) {
    const { chartUserConfig } = vm
    const { barMinHeight, } = chartUserConfig
    const { dimensionExtendList } = vm.UserConfig
    // 无扩展维度时度量堆叠
    options.series.forEach(item => {
      if (barMinHeight) {
        item.barMinHeight = barMinHeight
      }
      if (!dimensionExtendList.length) {
        item.stack = 'stackBar'
      }
    })
  },
  // 瀑布图配置
  waterfallSetting(vm, options) {
    const { chartUserConfig, chartData, content, themeType } = vm
    const { chartResponse } = content
    if (!chartData.rows.length) return
    const { waterfallSetting, contrastList, } = chartUserConfig
    const { metricAllList } = vm.UserConfig
    // 需要给图例名称设置多语言
    const legendName = waterfallSetting?.legendName || { positive: '增长', negative: '下降' }
    const contrastAlias = contrastList?.[0]?.alias || contrastList?.[0]?.labeName
    let increaseSeries = {
      type: 'bar',
      stack: 'waterfall',
      name: legendName.positive,
      data: [],
    }
    let decreaseSeries = {
      type: 'bar',
      stack: 'waterfall',
      name: legendName.negative,
      data: [],
    }
    let totalSeries = {
      type: 'bar',
      stack: 'waterfall',
      total: true,
      itemStyle: {
        barBorderColor: 'rgba(0,0,0,0)',
        color: 'rgba(0,0,0,0)'
      },
    }
    let initialVal = 0
    let endVal = 0
    let growthTotalData = []
    let declineTotalData = []
    if (metricAllList.length > 1) {
      chartResponse.metrics.forEach((metricItem, metricIndex) => {
        // 当前柱子数值
        const originalRowValue = chartData.rows[0][`VFMT_DIFF_${metricItem}`]
        const rowValue = Number(originalRowValue)
        // 计算起始值、终点值，瀑布图下一条数据得起始值就是上一条数据的终点值
        if (rowValue && !isNaN(rowValue)) {
          initialVal = endVal
          endVal = initialVal + rowValue
        }
        // 默认值与度量空值区分开
        let { growthTotal = '//', declineTotal = '//', aseVal = '//', descVal = '//' } = getWaterfallSeriesData({ initialVal, endVal, rowValue, chartUserConfig })
        // 空值判断
        if (chartResponse.measurementNullValue && chartResponse.measurementNullValue === originalRowValue) {
          aseVal = 0
        }
        decreaseSeries.data.push(descVal)
        increaseSeries.data.push(aseVal)
        growthTotalData.push(growthTotal)
        declineTotalData.push(declineTotal)
      })
      // 无维度需要特殊处理x轴数据
      Object.assign(options.xAxis[0], {
        data: chartData.columns.map(item => item),
      })
    } else {
      const metircsAlias = metricAllList[0].alias || metricAllList[0].labeName
      chartData.rows.forEach((rowItem, rowIndex) => {
        if (rowItem.isAccumulative) return
        // 当前柱子数值
        const originalRowValue = rowItem.isContrast ? rowItem[`VFMT_TOTAL_${contrastAlias}`] : rowItem[`VFMT_DIFF_${metircsAlias}`]
        const rowValue = Number(originalRowValue)
        // 计算起始值、终点值，瀑布图下一条数据得起始值就是上一条数据的终点值
        if (rowValue && !isNaN(rowValue)) {
          initialVal = endVal
          endVal = initialVal + rowValue
        }
        // 默认值与度量空值区分开
        let { growthTotal = '//', declineTotal = '//', aseVal = '//', descVal = '//' } = rowItem.isContrast ? {} : getWaterfallSeriesData({ initialVal, endVal, rowValue, chartUserConfig })
        // 空值判断
        if (chartResponse.measurementNullValue && chartResponse.measurementNullValue === originalRowValue) {
          aseVal = 0
        }
        let rowSeriesData = { aseVal, descVal }
        Object.keys(rowSeriesData).forEach(rdk => {
          if (typeof rowSeriesData[rdk] === 'object') {
            rowSeriesData[rdk].row = rowItem
          } else {
            rowSeriesData[rdk] = {
              value: rowSeriesData[rdk],
              row: rowItem
            }
          }
        })
        decreaseSeries.data.push(rowSeriesData.descVal)
        increaseSeries.data.push(rowSeriesData.aseVal)
        growthTotalData.push(growthTotal)
        declineTotalData.push(declineTotal)
      })
    }
    const growthTotalSeries = deepClone(Object.assign(totalSeries, { data: growthTotalData, totalType: 'growth', name: 'growthTotal' }))
    const declineTotalSeries = deepClone(Object.assign(totalSeries, { data: declineTotalData, totalType: 'decline', name: 'declineTotal' }))
    const series = [growthTotalSeries, declineTotalSeries, increaseSeries, decreaseSeries]
    // TODO 代码优化
    // 设置对比值、累计值
    const keys = metricAllList.length > 1 ? ['accumulative'] : ['contrast', 'accumulative']
    keys.forEach(type => {
      const specialSeries = addSpecialValue({ vm, options, themeType, chartUserConfig, increaseSeries, chartResponse, chartData, contrastAlias, type })
      specialSeries && series.push(specialSeries)
    })
    const labelLayout = getLabelLayout(chartUserConfig)
    options.series = series.map(s => Object.assign(s, labelLayout, getSeriesAnimation()))
    options.legend.data = Object.keys(legendName).map(key => legendName[key])
    // 累计值和对比值开启时，禁止图例点击
    if (waterfallSetting?.contrast?.show || waterfallSetting?.accumulative?.show) {
      Object.assign(options.legend, { selectedMode: false })
    }
  },
  // 层叠圆形图配置
  roundCascadesSetting(vm, options) {
    const { chartData, chartUserConfig } = vm.content
    const rows = chartData.rows
    if (!rows || !rows.length) {
      options.series = []
      return
    }
    const { isLabelLogogram, chartAlias, labelConfig = {}, roundCascadesSetting = {}, labelLineShow, dataShow, } = chartUserConfig
    const { dimensionList, metricAllList } = vm.UserConfig
    const metricAlias = metricAllList[0].alias || metricAllList[0].labeName
    const dimensionAlias = dimensionList[0].alias || dimensionList[0].labeName
    const { openMinDistance, minDistance } = roundCascadesSetting
    // 图形宽高
    const chartDom = document.querySelector(`.${vm.chartContentClass}`)
    if (!chartDom) return
    const { offsetWidth, offsetHeight } = chartDom
    // 最外层圆形位置与大小
    let { radius: firstCircularRadius, center: firstCircularPosition } = options.series?.[0]
    // 如果raduis、center为百分比，则转换为数值
    if (firstCircularRadius.toString().includes('%')) {
      const baseRaduis = firstCircularRadius.replace('%', '') / 100
      firstCircularRadius = offsetWidth > offsetHeight ? baseRaduis * offsetHeight / 2 : baseRaduis * offsetWidth / 2
    }
    firstCircularPosition.map((pos, index) => {
      if (pos.toString().includes('%')) {
        let _pos = pos.replace('%', '') / 100
        firstCircularPosition[index] = index === 0 ? _pos * offsetWidth : _pos * offsetHeight
      }
    })
    // 维度颜色
    const dimensionColors = Color.getDimensionColor(vm).repeat
    const series: any[] = []
    let generalSeries = {
      type: 'pie',
      emphasis: {
        scale: false
      },
      // 层叠圆形图的label通过markLine配置
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
    }
    const scaleStr = `VFMT_RAW_METRIC_PERCENT_MAX_${ metricAlias }`
    const legendName = []
    // 标签指示线基数
    const baseLineLength = vm.isMobile ? 10 : 50
    rows.forEach((rowItem, index) => {
      if (!rowItem.hasOwnProperty(scaleStr)) return
      const dimensionVal = rowItem[`VIEWFORMAT_${ dimensionAlias }`]
      legendName.push(dimensionVal)
      let radius, center
      // 计算每层圆形大小和位置
      if (index === 0) {
        radius = Math.round(firstCircularRadius * 100) / 100
        center = firstCircularPosition
      } else {
        radius = openMinDistance && minDistance ? Math.ceil(firstCircularRadius - minDistance * index) : Math.ceil(firstCircularRadius * rowItem[scaleStr])
        if (radius < 0) radius = 0
        // 所有圆形的圆心X轴坐标相同，Y轴坐标 = 第一层圆形Y轴坐标 + 第一层圆形半径 - 当前圆形半径
        let Y = firstCircularPosition[1] + firstCircularRadius - radius
        center = [firstCircularPosition[0], Y]
      }
      let seriesItem = Object.assign(JSONClone(generalSeries), {
        name: dimensionVal,
        center,
        radius,
        z: index * 0.01 + 2, // echarts默认层级为2
        data: [{
          value: rowItem[metricAlias],
          name: dimensionVal,
          row: rowItem,
        }],
        itemStyle: {
          color: dimensionColors[index]
        },
        ...getSeriesAnimation(),
      })
      // 绘制指示线
      if ((labelLineShow || dataShow) && (vm.labelLineButton || !vm.commonData.isPreview)) {
        const metricVal = isLabelLogogram && getIsNeedLogram(rowItem[metricAlias]) ? getFormatterFunc('unitShorthand', 100, true)(rowItem[metricAlias]) : rowItem[`VIEWFORMAT_${ metricAlias }`]
        let configArray = {
          labelLineShow: { show: labelLineShow, value: dimensionVal, },
          dataShow: { show: dataShow, value: metricVal, },
          percentageShow: { show: false, value: '', },
        }

        let name = getShowLinesLabel(chartUserConfig, configArray)
        const labelThemeConfig = Object.assign(getThemeConfig(vm.themeType, { attributes: ['labelConfig'], chartAlias }).labelConfig, labelConfig)
        let { color, fontFamily, fontSize } = labelThemeConfig
        Object.assign(seriesItem.label, {
          show: true,
          color: chartUserConfig.followDimensionColor ? dimensionColors[index] : color,
          fontFamily,
          fontSize: vm.getPxOfHighResolution(fontSize),
          formatter: () => name,
        })
        const { labelLayout } = getLabelLayout(chartUserConfig)
        const lineLength = firstCircularRadius + baseLineLength
        seriesItem.labelLayout = (params) => {
          const { rect, labelRect } = params
          const labelEndXposition = center[0] + (index % 2 ? -1 * lineLength : lineLength)
          const yPosition = radius === 0 ? center[1] : (rect.y + 1)
          return {
            ...labelLayout,
            x: center[0] + (index % 2 ? -1 * lineLength : lineLength),
            dx: index % 2 ? -5 : 5,
            y: center[1] - (radius === 0 ? 0 : (radius - 1)),
            align: index % 2 ? 'right' : 'left',
            verticalAlign: 'middle',
            labelLinePoints: [[center[0], yPosition], [labelEndXposition, yPosition], [labelEndXposition, yPosition]],
          }
        }
        seriesItem.labelLine = { show: true, lineStyle: { width: 1, }, }
      }
      series.push(seriesItem)
    })
    options.series = series
    options.legend.data = legendName
  },
  setBarItemStyleOption,
  setLegendDimension,
  setPieAreaPadding,
}

// 瀑布图增加累计值、对比值数据
function addSpecialValue({ vm, options, themeType, chartUserConfig, increaseSeries, chartResponse, chartData, contrastAlias, type }) {
  const { waterfallSetting, colors, contrastList } = chartUserConfig
  const specicalConfig = waterfallSetting?.[type]
  const seriesFlag = type === 'accumulative' ? 'isAccumulative' : 'isContrast'
  // 取chartResponse中度量名不会出现多语言预览问题
  const { metrics, rows: chartResponseRows } = chartResponse
  const { rows: chartDataRows } = chartData
  // 判断是否存在X轴标签数据
  const isXAxisData = chartDataRows.find(rowItem => rowItem[seriesFlag])
  if (specicalConfig && specicalConfig.show && (type === 'accumulative' || (type === 'contrast' && contrastList?.length))) {
    const defaultStyle = getThemeConfig(themeType, { attributes: ['waterfallConfig'] }).waterfallConfig
    const labelStyle = Object.assign(defaultStyle[type].style, deepClone(specicalConfig.style))
    let { color, fontFamily, fontSize } = labelStyle
    let series = {
      type: 'bar',
      stack: 'waterfall',
      name: type === 'accumulative' ? 'aggregate' : 'contrast',
      [seriesFlag]: true,
      data: [],
    }
    let alias = contrastAlias
    if (type === 'accumulative') {
      for (let i = 0; i < increaseSeries.data.length; i++) { series.data.push('//') }
      alias = metrics.length > 1 ? metrics[metrics.length - 1] : metrics[0]
    }
    series.data.push({
      name: specicalConfig.name,
      value: metrics.length > 1 ? chartResponseRows[0][`VFMT_TOTAL_${alias}`] : chartResponseRows[chartResponseRows.length - 1][`VFMT_TOTAL_${alias}`],
      itemStyle: {
        color: type === 'accumulative' ? Color.getEchartsColorItem(colors[2]) : waterfallSetting?.accumulative?.show ? Color.getEchartsColorItem(colors[3]) : Color.getEchartsColorItem(colors[2]),
      },
    })
    // 判断是否存在当前值的X轴标签
    // 无维度瀑布图根据长度判断，当xAxis.data.lenght大于increaseSeries.data.length时存在累计值；
    // 有维度瀑布图根据chartDataRows内是否存在对当前值标识
    options.xAxis.map(xAxisItem => {
      const data = {
        value: specicalConfig.name,
        textStyle: {
          color,
          fontFamily,
          fontSize: vm.getPxOfHighResolution(fontSize),
        },
      }
      if (type === 'contrast') {
        if (isXAxisData) {
          xAxisItem.data[0] = data
        } else {
          xAxisItem.data.unshift(data)
        }
      }
      if (type === 'accumulative') {
        if (isXAxisData || (metrics.length > 1 && xAxisItem.data.length > increaseSeries.data.length)) {
          xAxisItem.data[xAxisItem.data.length - 1] = data
        } else {
          xAxisItem.data.push(data)
        }
      }
    })
    return series
  } else {
  }
}

function getWaterfallSeriesData({ initialVal, endVal, rowValue, chartUserConfig }) {
  let total, growthTotal, declineTotal, aseVal, descVal
  if (isNaN(rowValue)) return {}
  if (rowValue === 0) return { aseVal: 0 }
  if (initialVal === 0 || endVal === 0) {
    total = 0
    if (rowValue > 0) {
      aseVal = initialVal === 0 ? rowValue : rowValue * -1
    } else {
      descVal = initialVal === 0 ? rowValue : Math.abs(rowValue)
    }
  } else if ((initialVal < 0 && endVal > 0) || (initialVal > 0 && endVal < 0)) {
    // 如果起始值跟终点值之间包含0，则横跨0刻度线，横框0刻度线时，统一下部分为辅助值，上部分为柱子数值
    const _total = initialVal < 0 ? initialVal : endVal
    const normalData = initialVal > 0 ? initialVal : endVal
    const color = rowValue > 0 ? chartUserConfig.colors[0] : chartUserConfig.colors[1]
    total = {
      value: _total,
      itemStyle: {
        color: Color.getEchartsColorItem(color),
      },
    }
    if (rowValue > 0) {
      aseVal = normalData
    } else {
      descVal = normalData
    }
  } else {
    // 整个柱体都在正半轴或负半轴
    const flag = initialVal < 0 ? -1 : 1
    // 辅助值 = 起始值绝对值和终点值绝对值的最小值 距离0刻度线大小
    total = (Math.min(Math.abs(initialVal), Math.abs(endVal)) - 0) * flag
    if (rowValue > 0) {
      aseVal = initialVal < 0 ? flag * rowValue : rowValue
    } else {
      descVal = initialVal > 0 ? Math.abs(rowValue) : rowValue
    }
  }
  if (rowValue > 0) {
    growthTotal = total
  } else {
    declineTotal = total
  }
  return { growthTotal, declineTotal, aseVal, descVal }
}
export function getTitleStyle(vm) {
  const { chartUserConfig } = vm
  const { title } = chartUserConfig
  let colorSet = getThemeConfig(
    vm.themeType,
    {
      attributes: ['chartTitleConfig'],
    },
  )
  let textStyle = {}
  Object.keys(colorSet.chartTitleConfig.textStyle).forEach(item => {
    textStyle = (title && title.textStyle) || {}
    textStyle[item] = textStyle[item] || colorSet.chartTitleConfig.textStyle[item]
  })
  let _textStyle = {}
  Object.keys(textStyle).map(key => {
    let _key = key.replace(/([A-Z])/g, '-$1').toLowerCase()
    // _key !== 'font_family'
    _textStyle[_key] = title.textStyle[key]
    if (_key === 'font-size') {
      _textStyle[_key] = `${parseFloat(title.textStyle[key])}px`
    }
  })
  return _textStyle
}
export function setTitleRow(vm, titleContainerWidth) {
  const { chartUserConfig, mobileFullScreen } = vm
  const { title } = chartUserConfig
  let strLen = 0
  let rowNum = 1
  let newText = ''
  let _textStyle = getTitleStyle(vm)
  // 移动端标题最多显示行数 横屏显示1行 竖屏显示两行
  const maxRow = mobileFullScreen ? 1 : 2
  for (let i = 0, len = title.text.length; i < len; i++) {
    let textWidth = getStrSize(title.text[i], _textStyle).width
    strLen += textWidth
    if (strLen >= titleContainerWidth) {
      rowNum++
      if (rowNum > maxRow) break
      newText += '\n'
      strLen = 0
    }
    newText += title.text[i]
  }
  // 当标题占满两行时，超出截取
  if (rowNum > maxRow) {
    rowNum = maxRow
    newText = newText.slice(0, newText.length - 3) + '...'
  }
  return rowNum
}
function splitAxis(params, splitNumber = 5) {
  const { min, max } = params
  let result = { min: 0, max: 1 }
  if (min === undefined || max === undefined || isNaN(min) || isNaN(max)) return result
  const isMinPositive = min > 0
  const isMaxPositive = max > 0
  if (isMinPositive || !isMaxPositive || min === 0 || max === 0) {
    // 如果最大值和最小值是同号的,（如果最小值大于0或者最大值小于0）
    let minKey = isMaxPositive ? 'min' : 'max'
    let maxKey = isMaxPositive ? 'max' : 'min'
    result[minKey] = 0
    result[maxKey] = getRadarAliasRange(Math.abs(params[maxKey]), splitNumber)
    if (!isMaxPositive) result[maxKey] = -1 * result[maxKey]
  } else {
    let dividObj = getDividObj(splitNumber)
    let quotient = Math.abs(min) / Math.abs(max)
    let divid = dividObj.find(d => d.negative / d.positive - quotient <= 0)
    if (!divid) divid = dividObj[dividObj.length - 1]

    result[divid.maxKey] = getRadarAliasRange(Math.abs(params[divid.maxKey]), divid[divid.baseKey])
    result[divid.minKey] = result[divid.maxKey] / divid[divid.baseKey] * (splitNumber - divid[divid.baseKey])
    if (divid.baseKey === 'negative') {
      result[divid.maxKey] = -1 * result[divid.maxKey]
    } else {
      result[divid.minKey] = -1 * result[divid.minKey]
    }
    if (divid.minKey === 'min' && result[divid.minKey] > min) {
      const minAxisAbs = getRadarAliasRange(Math.abs(params[divid.minKey]), splitNumber - divid[divid.baseKey])
      result[divid.minKey] = -1 * minAxisAbs
      result[divid.maxKey] = minAxisAbs / (splitNumber - divid[divid.baseKey]) * divid[divid.baseKey]
    }
  }
  function getDividObj(sn) {
    let dividObjArr = []
    for (let i = 1; i < sn; i++) {
      let biggerAxis = sn - 2 * i > 0
      let dividObj = {
        negative: sn - i,
        positive: i,
        minKey: biggerAxis ? 'max' : 'min',
        maxKey: biggerAxis ? 'min' : 'max',
        baseKey: biggerAxis ? 'negative' : 'positive',
      }
      dividObjArr.push(dividObj)
    }
    return dividObjArr
  }
  return result
}
function getRadarAliasRange(num, splitNumber) {
  let result = handleNumber(num, splitNumber)
  const N = Math.ceil(num).toString().length - 2
  if (num > 10 && !N && result % 10) {
    result = result - result % 10 + 10
  }
  return result
}
function handlePercentAxis({ chartData, chartUserConfig }) {
  const { rows } = chartData
  let rowsData = rows.map(r => {
    return r['COMPLETION_RATE'] * 100
  })
  let result = {
    min: 0, max: 100, interval: 25
  }
  let max = Math.max(...rowsData)
  let min = Math.min(...rowsData)
  if (max > 100) max = 100
  if (min < -100) min = -100
  if (min >= 0 || min === max) {
    result = { min: 0, max: 100, interval: 25 }
  } else if (max < 0) {
    result = { min: -100, max: 25, interval: 25 }
  } else {
    let min25 = (min % 25) ? (min - (min % 25) - 25) : min
    if (min25 < -75) {
      result = { min: -100, max: 100, interval: 50 }
    } else {
      result = { min: min25, max: 100, interval: 25 }
    }
  }
  return {
    ...result,
    rowsData,
  }
}
// 是否显示指标选择器
export function showChartIndicator(vm, place, params = {}) {
  // 为方便使用，使用解构出vm中需要用到的参数
  const { isMobile, themeFullScreen, chioceTabList, isChartSet, hasChartData = true, element, utils = {} } = vm
  const { content = {}, cardInteractionState } = element
  const { chioceTab = [] } = content
  if (place && ((isMobile && place !== 'mobile') || (!isMobile && place !== 'pc'))) return false
  !place && (place = isMobile ? 'mobile' : 'pc')
  const isRelated = vm.metricChioceTab && vm.metricChioceTab.isRelated
  if (place === 'pc') {
    return (!utils.isScreen || utils.isDataReport) && !themeFullScreen && ((!isRelated && chioceTab.length > 1) || (isRelated && chioceTabList && chioceTabList.length > 1)) && !isChartSet && !isMobile && (hasChartData || params.hasChartData)
  }
  if (place === 'mobile') {
    return ((!isRelated && chioceTab.length > 1 && !cardInteractionState?.isActive) || (isRelated && chioceTabList && chioceTabList.length > 1) || (cardInteractionState?.isActive && chioceTabList && chioceTabList.length > 1)) && !isChartSet && isMobile
  }
}
// 是否显示图形日期切换控件，选择日期维度且在看板设计界面或预览界面时显示，设置时间维度拆分时不显示
export function showDateComponent({ vm, place, dateParams = false }) {
  const { chartUserConfig, content, isMobile, isChartSet, commonData, themeFullScreen, switchDateDimension } = vm
  const { dimensionList, chartAlias, datasetAssociation } = chartUserConfig
  if (!dimensionList || chartAlias === 've-gauge-normal') return false
  if (chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION) return false
  if (place && ((isMobile && place !== 'mobile') || (!isMobile && place !== 'pc'))) return false
  !place && (place = isMobile ? 'mobile' : 'pc')
  const { timeDimensionSplitting } = content.drillSettings
  let isDateDimension = dimensionList.filter(item => item.columnTpe === 'date')
  isDateDimension = isDateDimension.length === 1
  // 如果是智能搜索
  const hideDateComponent = !!vm.hideDateComponent
  // 简单表格、主题流图不需要时间维度
  const isShowDateComponent = !['ve-themeRiver'].includes(chartAlias) &&
    (timeDimensionSplitting !== '1' || !NEED_TIMEDIMENSION_CHART.includes(chartAlias)) &&
    isDateDimension &&
    (!isChartSet || commonData.isPreview)
  if (place === 'pc') {
    return (dateParams || !themeFullScreen) && !isMobile && isShowDateComponent && switchDateDimension && !hideDateComponent
  }
  if (place === 'mobile') {
    return isMobile && isShowDateComponent && switchDateDimension
  }
}

// 转换地图经纬度
function convertData (data, flyData, HEATMAPParam: any = {}) {
  const { childChartAlias, } = this.content.chartUserConfig || {}
  const { metricAllList, } = this.UserConfig
  const metrcName = metricAllList[0]?.alias || metricAllList[0]?.labeName
  let isWorldMap = childChartAlias === 've-map-world'
  let features: any = {}
  const isForeignCountry = childChartAlias === 've-map-britain'
  const country = childChartAlias?.replace('ve-map-', '')
  const languageCode = this.boardInfo?.languageCode || '1'
  const languageMap = this.boardInfo?.languageAndCurrency?.languageMap || {}
  const languageStatus = this.boardInfo?.languageAndCurrency?.languageStatus
  let lang = 'En'
  if (languageStatus === '0') {
    // 全局语言
    const langList = this.utils?.languageList || []
    const _lan = langList.find(item => item.languageCode === languageCode)
    if (_lan) {
      lang = _lan.isoCode === 'zh' ? 'Zh' : 'En'
    }
  } else {
    // 物业语言
    lang = ['zh', 'zh-cn'].includes(languageMap[languageCode]) ? 'Zh' : 'En'
  }
  // 世界地图经纬度对应关系
  const worldCoordinates = lang === 'Zh' ? mapConstant.coordinates.world : mapConstant.coordinates.worldEn
  const result: any = {
    res: [],
    nameMap: {},
    dataChangeResult: [],
    coordinateData: [],
    flyLineData: []
  }

  if (isForeignCountry) {
    features = chartApi.mapSet.getData(`country/${country}`) || {}
  } else if (country === 'china') {
    features = chartApi.mapSet.getMapLanguageProvice()
    if (!features) return result
  }
  for (let i = 0; i < data.length; i++) {
    let geoCoord = country === 'world' ? worldCoordinates[data[i].name] : null
    let _value = data[i].value
    if (isForeignCountry) {
      let regionInfo = chartApi.mapSet.britainFeatures[data[i].name]
      if (regionInfo) {
        geoCoord = [regionInfo.longitude, regionInfo.latitude]
      }
    } else if (!isWorldMap) {
      const mapLanguageWithCoord = chartApi.mapSet.getMapLanguageWithCoord(HEATMAPParam.isHEATMAP ? 'heatMap' : '')
      let _regionName = mapLanguageWithCoord[data[i].name] ? data[i].name : Object.keys(mapLanguageWithCoord).find(k => mapLanguageWithCoord[k][`regionName${ lang }`] === data[i].name)
      if (_regionName) geoCoord = mapLanguageWithCoord[_regionName].coordinates
    }
    if (geoCoord) {
      let name = data[i].name
      const needAddStr = country === 'china' && HEATMAPParam.isHEATMAP
      if (needAddStr) {
        name = findMapEchatsName.call(this, name)
        result.nameMap[name] = data[i].name
      }
      let resItem: any = {
        row: data[i].rowItem,
        name: data[i].name,
        value: HEATMAPParam.isHEATMAP ? _value : geoCoord.concat(_value),
        VIEWFORMAT: data[i].VIEWFORMAT,
        valueData: data[i].value,
      }
      result.res.push(resItem)
      result.coordinateData.push({
        ...resItem,
        value: geoCoord.concat(_value)
      })
      const variationKey = `${ metrcName }_Variation_`
      const dataVariation = data[i].rowItem[variationKey]
      if (dataVariation !== 0) {
        result.dataChangeResult.push({
          ...resItem,
          value: geoCoord.concat(dataVariation),
          VIEWFORMAT: data[i].rowItem[`VIEWFORMAT_${ variationKey }`],
          valueData: dataVariation,
        })
      }
    }
  }
  // 飞线相关
  for (let i = 0; i < flyData.length; i++) {
    let item = flyData[i]
    let geoCoord = country === 'world' ? worldCoordinates[item.name] : null
    let { destName, destGeoCoord } = getDestinationInfo(item, this)
    let _value = item.value
    if (isForeignCountry) {
      let regionInfo = chartApi.mapSet.britainFeatures[item.name]
      if (regionInfo) {
        geoCoord = [regionInfo.longitude, regionInfo.latitude]
      }
    } else if (!isWorldMap) {
      const mapLanguageWithCoord = chartApi.mapSet.getMapLanguageWithCoord(HEATMAPParam.isHEATMAP ? 'heatMap' : '')
      let _regionName = mapLanguageWithCoord[item.name] ? item.name : Object.keys(mapLanguageWithCoord).find(k => mapLanguageWithCoord[k][`regionName${ lang }`] === item.name)
      if (_regionName) geoCoord = mapLanguageWithCoord[_regionName].coordinates
    }
    if (geoCoord) {
      let name = item.name
      const needAddStr = country === 'china' && HEATMAPParam.isHEATMAP
      if (needAddStr) {
        name = findMapEchatsName.call(this, name)
        result.nameMap[name] = item.name
        if (destName) {
          destName = findMapEchatsName.call(this, destName)
          result.nameMap[destName] = item.nameDestination
        }
      }
      let resItem: any = {
        row: item.rowItem,
        name: item.name,
        value: geoCoord.concat(_value),
        VIEWFORMAT: item.VIEWFORMAT,
        valueData: item.value,
      }
      if (destName) {
        resItem = {
          ...resItem,
          destName,
          destGeoCoord,
        }
      }
      result.flyLineData.push(resItem)
    }
  }
  return result
}

// 通过目的地获取其经纬度信息
function getDestinationInfo(data, vm) {
  let destGeoCoord: any = null
  if (!data.nameDestination) return {}
  const { childChartAlias, } = vm.chartUserConfig
  let destName = data.nameDestination
  let isWorldMap = childChartAlias === 've-map-world'
  let features: any = {}
  const isForeignCountry = childChartAlias === 've-map-britain'
  const country = childChartAlias?.replace('ve-map-', '')
  const languageCode = vm.boardInfo?.languageCode || '1'
  const languageMap = vm.boardInfo?.languageAndCurrency?.languageMap || {}
  const languageStatus = vm.boardInfo?.languageAndCurrency?.languageStatus
  let lang = 'En'
  if (languageStatus === '0') {
    // 全局语言
    const langList = vm.utils?.languageList || []
    const _lan = langList.find(item => item.languageCode === languageCode)
    if (_lan) {
      lang = _lan.isoCode === 'zh' ? 'Zh' : 'En'
    }
  } else {
    // 物业语言
    lang = ['zh', 'zh-cn'].includes(languageMap[languageCode]) ? 'Zh' : 'En'
  }
  // 世界地图经纬度对应关系
  const worldCoordinates = lang === 'Zh' ? mapConstant.coordinates.world : mapConstant.coordinates.worldEn
  if (isForeignCountry) {
    features = chartApi.mapSet.getData(`country/${country}`) || {}
  } else if (country === 'china') {
    features = chartApi.mapSet.getMapLanguageProvice()
    if (!features) return {}
  }
  destGeoCoord = country === 'world' ? worldCoordinates[destName] : null
  if (isForeignCountry) {
    let destInfo = chartApi.mapSet.britainFeatures[destName]
    if (destInfo) destGeoCoord = [destInfo.longitude, destInfo.latitude]
  } else if (!isWorldMap) {
    const mapLanguageWithCoord = chartApi.mapSet.getMapLanguageWithCoord('')
    let _destName = mapLanguageWithCoord[destName] ? destName : Object.keys(mapLanguageWithCoord).find(k => mapLanguageWithCoord[k][`regionName${ lang }`] === destName)
    if (_destName) destGeoCoord = mapLanguageWithCoord[_destName].coordinates
  }
  return {
    destName,
    destGeoCoord,
  }
}

// 转换经纬度模式地图的经纬度
function convertSpecialData(vm) {
  const { chartUserConfig, chartData, mapFlyLineData } = vm
  const { latitudeList = [], longitudeList = [], } = chartUserConfig
  const { metricAllList, dimensionList } = vm.UserConfig
  if (!latitudeList.length || !longitudeList.length) return { res: [], dataChangeResult: [] }
  let res: any = []
  let dataChangeResult: any = []
  let flyLineData: any = []
  const dimensionOriginName = dimensionList[0].alias || dimensionList[0].labeName
  const metricOriginName = metricAllList[0]?.alias || metricAllList[0]?.labeName
  const latitude = latitudeList[0].alias || latitudeList[0].labeName
  const longitude = longitudeList[0].alias || longitudeList[0].labeName
  const variationKey = `${ metricOriginName }_Variation_`
  chartData.rows.map((item, index) => {
    let o: any = {
      name: item[dimensionOriginName],
      value: [+item[longitude], +item[latitude], item[metricOriginName]],
      row: item,
    }

    res.push(o)

    const dataVariation = item[variationKey]
    dataChangeResult.push({
      name: item[dimensionOriginName],
      value: [+item[longitude], +item[latitude], dataVariation],
      VIEWFORMAT: item[`VIEWFORMAT_${ variationKey }`],
      valueData: dataVariation,
      row: item,
    })
  })
  mapFlyLineData.map((item, index) => {
    const { destName, destGeoCoord } = getDestinationInfo(item, vm)
    let o: any = {
      name: item.rowItem[dimensionOriginName],
      value: [+item.rowItem[longitude], +item.rowItem[latitude], item.rowItem[metricOriginName]],
      row: item.rowItem,
    }
    if (destName) {
      o = {
        ...o,
        destName,
        destGeoCoord,
      }
    }
    flyLineData.push(o)
  })
  return { res, dataChangeResult, flyLineData }
}

function findMapEchatsName(name) {
  let __name = name
  const { res, dataUrl } = this.chartUserConfig.mapJson
  let features = []
  if (dataUrl && chartApi.mapSet.getData(dataUrl)) {
    features = chartApi.mapSet.getData(dataUrl).features
  } else {
    features = res ? res.features ? res.features : res.data?.features : []
  }
  const regionInfo = features.find(item => {
    const _name = item.properties.name
    return _name === name || name.includes(_name)
  })
  return regionInfo?.properties?.name || __name
}

function isBoardCurrencyAbbr(vm) {
  const { boardInfo, commonData, isMobile } = vm
  const currencyUnitBtn = isMobile ? boardInfo?.tabBarComponentConfig?.isCurrencyUnitAbbrBtn : boardInfo?.isCurrencyUnitAbbrBtn
  const currencyUnit = currencyUnitBtn && commonData.isCurrencyAbbr
  return currencyUnitBtn && currencyUnit
}

function labelDisplayFormatter(vm, options) {
  // 支持设置Y轴标签格式
  const yAxisLabel = Y_AXIS_LABEL_FORMATTER
  // 支持设置X轴标签格式
  const xAxisLabel = X_AXIS_LABEL_FORMATTER
  // 象形柱状图是否开启反转Y轴
  const flipAxis = getProp(vm.chartUserConfig, 'pictorialBarSettings.flipAxis', false)
  const { chartUserConfig } = vm
  const { chartAlias, yAxisSetting = [], xAxisSetting } = chartUserConfig
  const yAxisOptionArr = Array.isArray(options.yAxis) ? options.yAxis : [options.yAxis]
  const _yAxisSetting = Array.isArray(yAxisSetting) ? yAxisSetting : [yAxisSetting]

  yAxisOptionArr.map((yAxisOption, index) => {
    if (!yAxisOption) return
    if (yAxisLabel.includes(chartAlias) || (chartAlias === 've-pictorialbar' && !flipAxis)) {
      let type = (_yAxisSetting[index] && _yAxisSetting[index].formatType)
      if (isBoardCurrencyAbbr(vm)) {
        type = 'currencyUnit'
      }
      // 当系统语言不为中文、或币种不为CNY时，取单位简写逻辑
      if (type === 'currencyUnit' && !vm.isCNY) {
        type = 'unitShorthand'
      }
      const axisSet = _yAxisSetting[index] || {}
      const alwaysShorthand = yAxisOption.isCustomSet && (axisSet.maxTickValueCustom || axisSet.minTickValueCustom)
      const point = alwaysShorthand && _yAxisSetting[index].decimalPlace || 0
      const formatFunc = alwaysShorthand ? getFormatterFunc(type, Math.pow(10, point), alwaysShorthand) : getFormatterFunc(type, 10, yAxisOption.isCustomSet)
      if (formatFunc) {
        !yAxisOption.axisLabel && (yAxisOption.axisLabel = {})
        yAxisOption.axisLabel.formatter = formatFunc
      }
    }
  })
  if (xAxisLabel.includes(chartAlias) || chartAlias === 've-scatter-normal' || (chartAlias === 've-pictorialbar' && flipAxis)) {
    options.xAxis.forEach((xAxisOption, index) => {
      let type = xAxisSetting?.formatType
      let axisSetting = xAxisSetting
      if (chartAlias === 've-pictorialbar' && flipAxis && index === 1) {
        type = yAxisSetting?.[1]?.formatType
      } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) {
        type = yAxisSetting?.[index]?.formatType
        axisSetting = yAxisSetting?.[index]
      }
      if (isBoardCurrencyAbbr(vm)) {
        type = 'currencyUnit'
      }
      // 当系统语言不为中文、或币种不为CNY时，取单位简写逻辑
      if (type === 'currencyUnit' && !vm.isCNY) {
        type = 'unitShorthand'
      }
      const alwaysShorthand = xAxisOption.isCustomSet && axisSetting && (axisSetting.maxTickValueCustom || axisSetting.minTickValueCustom)
      const point = alwaysShorthand && axisSetting.decimalPlace || 0
      const formatFunc = alwaysShorthand ? getFormatterFunc(type, Math.pow(10, point), alwaysShorthand) : getFormatterFunc(type, 10, xAxisOption.isCustomSet)
      if (!formatFunc) return
      xAxisOption.axisLabel.formatter = formatFunc
    })
  }
  if (['ve-bar-percent'].includes(chartAlias)) {
    options.xAxis[0] && (options.xAxis[0].axisLabel.formatter = '{value}%')
  }
  // 组合图次轴设置
  if (chartAlias === 've-composite') {
    // const compositeAxis = options.yAxis[1]
    // const { axisLabel, axisLine = {} } = deepClone(yAxisOption)
    // // axisLabel && (axisLabel.show = show)
    // axisLine && (axisLine.show = show && axisLine.show)
    // Object.assign(compositeAxis, { axisLabel, axisLine })
  }
}

export function setLegendIconPosition(vm, params = {}) {
  const { echartInstance = {}, chartUserConfig, chartConfig } = vm
  // echarts实例不存在
  if (!Object.keys(echartInstance).length) return
  if ((!vm.isChartSet && vm.isMobile)) {
    echartInstance.setOption({ legend: { show: false } })
  }
  const options = echartInstance.getOption()
  const { graphic, legend } = options
  const { position, type } = params
  // 图例位置
  const legendPosition = position || chartUserConfig.legend
  // 图例类型
  const legendType = type || legend[0].type
  // 图例总页数
  const { total } = chartConfig.lengendPageInfo

  const graphicIds = ['legendIcon1', 'legendIcon2']

  // 图例总页数小于2 或 图例类型为换行显示 或 图例不存在 时，删除图例控制按钮
  if (total < 2 || legendType !== 'scroll' || legendPosition !== 'bottom') {
    const lengendArr = []
    const graphicLength = Array.isArray(graphic) && graphic.length
    if (!graphicLength) return

    const elements = graphic[0].elements
    graphicIds.map((id) => {
      const _graphicElment = elements.find(e => e.id === id)
      _graphicElment && lengendArr.push({
        id: id,
        $action: 'remove'
      })
    })
    lengendArr.length > 0 && echartInstance.setOption({
      graphic: lengendArr,
      legend: {
        left: 'center',
      }
    })
    return
  }

  const element = {
    type: 'polygon',
    bottom: 5,
    z: 100,
    shape: {
      points: [[0, 0], [0, 12], [6, 6]]
    },
    style: {
      fill: 'rgba(47, 69, 84, 0.1)'
    }
  }

  // 根据图例位置计算icon位置
  let _graphic = []
  graphicIds.map(item => {
    let _element = deepClone(element)
    let polygonGraphic = {
      rotation: Math.PI,
      left: 0,
      // onclick: function() {
      //   lengendIconClick(vm, 'prev')
      // },
    }
    if (item === 'legendIcon2') {
      polygonGraphic = {
        right: 0,
        // onclick: function() {
        //   lengendIconClick(vm, 'next')
        // },
      }
    }
    Object.assign(_element, polygonGraphic, {
      id: item,
    })
    _graphic.push(_element)
  })

  echartInstance && echartInstance.setOption({
    graphic: _graphic,
    legend: {
      left: 10,
    },
  })
}

function settingAuxiliaryLineItem({ vm, options, auxiliaryLineData, isYaxis = false, axisType = '' }) {
  const { chartUserConfig, content } = vm
  const { alias, chartResponse } = content
  const auxLineResult = chartResponse?.auxLineResult || {}
  const { chartAlias, pictorialBarSettings } = chartUserConfig
  const { metricSecondList, } = vm.UserConfig
  let secondAxisMetrics = []

  if ((SUPPORT_DEPUTY_AXIS.includes(chartAlias) || chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) && metricSecondList.length) {
    secondAxisMetrics = metricSecondList.map(m => m.alias || m.labeName)
  }
  const isBar = REVERSE_AXIS_CHART.includes(chartAlias)
  const axiaskey = isBar ? 'xAxis' : 'yAxis'
  // 象形柱图无论反转Y轴是否开启，最大值最小值都保存在yAxis中
  const flipAxis = chartAlias === 've-pictorialbar' && pictorialBarSettings.flipAxis
  let maxAxias = getProp(options, flipAxis ? 'xAxis' : axiaskey, [])
  const isScatter = chartAlias === 've-scatter-normal'
  if (isScatter && isYaxis) {
    // 这种情况为散点图的X轴
    maxAxias = getProp(options, 'xAxis', [])
  }
  if (!isScatter && !maxAxias[axisType === 'copyY' ? 1 : 0]) return
  const strSizeArr = auxiliaryLineData.map(item => {
    const resultItem = auxLineResult[item.id]
    if (!resultItem || !Object.keys(resultItem).length) return {}
    const alias = item.auxiliaryLineType === 'custom' ? 'customizeGuides'
      : item.fieldType === 'calculatedValue' ? item.metricValue === 'otherField' ? item.alias || item.labeName : item.metricValue : 'fixed'
    const calcVal = Number(resultItem[`VFMT_RAW_${alias}`])
    const unitCalcVal = resultItem[`VIEWFORMAT_${alias}`]
    // let calcVal
    // if (item.fieldType === 'calculatedValue') {
    //   const yKey = item.metricValue
    //   const aliasItem = vm.chartUserConfig.metricsContainer.default.find(eve => eve.labeName === yKey || eve.alias === yKey)
    //   const yAliasKey = aliasItem ? aliasItem.alias : ''
    //   const rows = vm.chartData.rows
    //   const { extendDimensionList = [] } = vm.chartUserConfig
    //   let _arr = Array.isArray(rows) ? rows.map(eve => eve[yKey] || eve[yAliasKey]) : Object.values(rows).map(eve => eve[0][yKey] || eve[0][yAliasKey])
    //   if (extendDimensionList.length) {
    //     _arr = rows.reduce((prev, cur) => {
    //       prev = prev.concat(cur.extendData || [])
    //       return prev
    //     }, []).map(eve => {
    //       const evedata = +eve[yKey] || +eve[yAliasKey]
    //       return isNaN(evedata) ? 0 : evedata
    //     })
    //   }
    //   switch (item.valueType) {
    //     case 'max':
    //       calcVal = Math.max(..._arr)
    //       break
    //     case 'min':
    //       calcVal = Math.min(..._arr)
    //       break
    //     case 'average':
    //       calcVal = _arr.reduce((total, cur) => total + cur, 0) / _arr.length
    //       break
    //   }

    // } else {
    //   calcVal = Number(item.value)
    // }
    // const formatType = item.format === 'percent' ? 'percentage' : item.format === 'number' ? 'number' : 'unitShorthand'
    // const decimalNum = item.decimalNum ? Math.pow(10, item.decimalNum) : item.decimalNum === 0 ? 0 : 100
    // const unitCalcVal = getFormatterFunc(formatType, decimalNum, true)(calcVal)
    return {
      valueWidth: getStrSize(unitCalcVal.toString()).width + 10,
      unitCalcVal,
      calcVal,
    }
  })
  const maxStrSize = Math.max(...strSizeArr.map(s => s.valueWidth || 0))
  const isReverseChart = isReverseAxisChart(vm)
  const reversalFlag = isReverseChart || isYaxis
  const xDistanceKey = reversalFlag ? 'y' : 'x'
  if (xDistanceKey === 'x' && maxStrSize > 70) {
    // 数值宽度超出70，超出的宽度加到左边距上，如果需要更改distance的基数30，也需要同步更改基数70
    if (!options.grid.left || options.grid.left < maxStrSize - 70) {
      options.grid.left = maxStrSize - 70
    }
  }
  auxiliaryLineData.forEach((item, i) => {
    if (!auxLineResult[item.id]) return
    // 是否为次轴的度量
    let isSecondMetric = false
    if (secondAxisMetrics.includes(item.metricValue) || axisType) {
      isSecondMetric = true
      if (!getProp(chartUserConfig, 'yAxis.1.axisLabel.show')) {
        return
      }
    }
    const gridRightDistance = Array.isArray(options.grid) ? options.grid[options.grid.length - 1].right : options.grid.right
    const totalDistance = vm.$el.clientWidth - (gridRightDistance || 0)
    const maxVal = isScatter ? (maxAxias[0]?.max || '') : maxAxias[(axisType === 'copyY' || isSecondMetric) ? 1 : 0].max || ''
    const minVal = isScatter ? (maxAxias[0]?.min || '') : maxAxias[(axisType === 'copyY' || isSecondMetric) ? 1 : 0].min || ''
    let distance = 0
    if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) {
      distance = 10
    } else if (flipAxis) {
      // 象形柱图反转Y轴的情况
      if (isSecondMetric) {
        distance = 10
      } else {
        const grid = Array.isArray(options.grid) ? options.grid[options.grid.length - 1] : options.grid
        const _totalDistance = vm.$el.clientHeight - (grid.top || 0) - (grid.bottom || 0)
        distance = chartUserConfig.legend === 'top'
          ? _totalDistance - 30
          : _totalDistance - 15
      }
    } else {
      if (isSecondMetric) {
        distance = options.legend.hasOwnProperty('right')
          ? totalDistance - 60
          : totalDistance - getStrSize(maxVal).width - 20
      } else {
        distance = reversalFlag ? 10 : (maxStrSize - strSizeArr[i].valueWidth / 2 + 10)
      }
    }

    if (!reversalFlag) {
      switch (chartUserConfig.legend) {
        case 'left':
          if (!isSecondMetric) {
            distance += getChartLegendWidth(vm, options).width
          }
          break
        case 'right':
          if (isSecondMetric) {
            distance -= getChartLegendWidth(vm, options).width
          }
          break
      }
    } else if (chartUserConfig.legend === 'top') {
      distance += 20
    }
    // markpoint hover上去的展示的位置
    let position = ''
    if (isSecondMetric && chartAlias !== CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) {
      position = flipAxis ? 'bottom' : 'left'
    } else {
      position = reversalFlag ? (flipAxis ? 'top' : 'bottom') : 'right'
    }
    const emphasisStr = `${item.name}\n${strSizeArr[i].unitCalcVal}`
    let axisIndexKey = isReverseAxisChart(vm) ? 'xAxisIndex' : 'yAxisIndex'
    let compositeSeries = isSecondMetric ? { type: 'custom', [axisIndexKey]: 1 } : { type: 'custom' }
    const lineParams = {
      maxVal: typeof maxVal === 'number' ? maxVal : Infinity,
      minVal: typeof minVal === 'number' ? minVal : -Infinity,
      e: compositeSeries,
      strWidth: strSizeArr[i].valueWidth,
      key: reversalFlag ? 'xAxis' : 'yAxis',
      color: item.color,
      calcVal: strSizeArr[i].calcVal,
      unitCalcVal: strSizeArr[i].unitCalcVal,
      distance,
      emphasisStr,
      xDistanceKey,
      position,
      options,
      name: item.metricValue,
      id: item.id,
      lineType: item.lineType,
      lineWidth: item.lineWidth
    }

    settingLine(lineParams)
    compositeSeries.animation = true
    if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) {
      if ((item.fieldType !== 'calculatedValue' || item.metricValue === 'otherField') || item.auxiliaryLineType === 'custom') {
        const rightSeries = { type: 'custom', xAxisIndex: 1, yAxisIndex: 1, animation: true, }
        settingLine({ ...lineParams, e: rightSeries })
        options.series.push(rightSeries)
      } else if (isSecondMetric) {
        compositeSeries.yAxisIndex = 1
      }
    }
    options.series.push(compositeSeries)
  })
  // options.series[0].markPoint.symbolSize[0] = Math.max(...strWidthArr)
}

// 标签跟随维度颜色
function followDimensionColor(vm, options) {
  const isFollowDimensionColor = FOLLOWS_DIMENSION_COLOR.includes(vm.chartUserConfig.chartAlias) && vm.chartUserConfig.followDimensionColor
  if (isFollowDimensionColor) {
    options.series.forEach(val => {
      val.data.forEach(item => {
        const fontColor = item.itemStyle?.color
        item.label = { color: fontColor }
      })
    })
  }
}

// 移动看板添加内置datazoom
function mobileInsideDatazoom(vm, options) {
  const { mobilePagesize: userMobilePagesize, chartAlias, } = vm.content.chartUserConfig
  if (!MOBILE_PAGE_CHART.includes(chartAlias) || !vm.utils.isMobile) return
  const mobilePagesize = (MOBILE_PAGESIZE_CHART.includes(chartAlias) && userMobilePagesize || 11) - 1
  let minValueSpan = vm.chartData.rows ? Math.min(vm.chartData.rows.length, mobilePagesize) : mobilePagesize

  // if (extendDimensionList.length && EXTEND_DIMENSION_CHART.includes(chartAlias) && Array.isArray(vm.chartData.rows)) {
  //   let spanValue = 0
  //   let extendDataCount = 0
  //   while(extendDataCount < minValueSpan) {
  //     extendDataCount += vm.chartData.rows[spanValue].extendData?.length || 0
  //     spanValue ++
  //   }
  //   minValueSpan = spanValue
  // }
  // 移动端预览状态下聚焦后datazoom才生效
  // const disabled = vm.isMobile && vm.commonData.isPreview && (!vm.element.isFocus && !vm.element.opable) && !vm.fullscreenData.enlargeVisible
  const disabled = vm.isMobile && vm.commonData.isPreview && !vm.element.isFocus && !vm.fullscreenData.enlargeVisible
  console.log('disabled', chartAlias, disabled, vm.isMobile, vm.commonData.isPreview, !vm.element.isFocus, !vm.fullscreenData.enlargeVisible)
  // const disabled = false
  const isVertical = isReverseAxisChart(vm)
  const dataZoomOption = {
    type: 'inside',
    disabled: disabled,
    startValue: 0,
    endValue: Math.max(minValueSpan - 1, 0),
    minValueSpan,
    orient: 'horizontal',
  }
  if (isVertical) {
    Object.assign(dataZoomOption, {
      orient: 'vertical',
      startValue: vm.chartData.rows.length - minValueSpan,
      endValue: vm.chartData.rows.length,
      yAxisIndex: options.yAxis.map(x => x.axisIndex || 0),
    })
  }
  let dataZoom: any = [dataZoomOption]
  if (chartAlias === 've-scatter-normal') {
    dataZoom = [{
      type: 'inside',
      orient: 'horizontal',
      disabled: disabled,
    }, {
      type: 'inside',
      orient: 'vertical',
      disabled: disabled,
    }]
  }
  options.dataZoom = dataZoom
}

// 双度量设置
function doubleMetricSetting(vm, options) {
  const { chartAlias, histogramDisplayMode, compositeChart, yAxis = [], } = vm.chartUserConfig
  const { metricSecondList, } = vm.UserConfig

  if (SUPPORT_DEPUTY_AXIS.includes(chartAlias)) {
    // 需要特殊处理重叠展示柱状图
    const isHistogramCascade = (chartAlias === 've-histogram-normal' || (chartAlias === 've-composite' && !compositeChart.stack)) && histogramDisplayMode === 'cascade'
    let axisKeyArr = isReverseAxisChart(vm) ? ['xAxisIndex', 'yAxisIndex'] : ['yAxisIndex', 'xAxisIndex']
    let hasSecondAxis = yAxis[1]?.axisLabel.show
    options.series.map((item) => {
      if (!item || ['custom'].includes(item.type)) return
      const isSecondAxis = hasSecondAxis && metricSecondList.find(m => m.keyName === item.metricKeyName)
      Object.assign(item, {
        [axisKeyArr[0]]: isSecondAxis && yAxis[1]?.axisLabel?.show ? 1 : 0,
        [axisKeyArr[1]]: isHistogramCascade ? item.xAxisIndex : 0,
      })
    })
  }
}

// 交互设置样式
function interactionStyle(vm, options) {
  const { isChartSet, chartUserConfig, content } = vm
  const { dimensionColors = [], chartAlias, pieSetting = {} } = chartUserConfig
  const { dimensionList } = vm.UserConfig
  // 编辑界面内不显示交互样式
  // if (vm.isChartSet) return
  // 仪表盘处理报错，日历图交互自己单独处理
  if (!Array.isArray(options.series) || chartUserConfig.chartAlias === 've-calendar') return
  // const options = this.echartInstance.getOption()
  if (!dimensionList.length && chartAlias !== CHART_ALIAS_TYPE.VE_GAUGE) return
  const dimensionOriginalName = dimensionList.map(d => d.alias || d.labeName)
  const isDoubleDimension = dimensionList.length === 2
  const value = vm.interactionOptionsValues
  let rows = vm.chartData.rows
  if (pieSetting.showCombinePie && chartAlias === 've-pie-normal' && Array.isArray(rows) && rows.length) {
      const othersRows = rows?.find(r => r.hasOwnProperty('others') && Array.isArray(r.others) && r.others.length)?.others || []
      rows = rows.concat(othersRows)
  }
  const dimension = dimensionOriginalName[1] || dimensionOriginalName[0]
  const hoverDimensionName = `${ vm.specialTimeSplitOffset ? 'HOVER_' : ''}${dimension}`
  const selectIndex = Array.isArray(rows)
    ? rows.findIndex(e => {
      if (isDoubleDimension && Array.isArray(value)) {
        return (e[hoverDimensionName] || e[dimension]) === value[0][0] && e[`${dimensionOriginalName[0]}`] === value[1][0]
      } else {
        return (e[hoverDimensionName] || e[dimension]) === value
      }
    })
    : Object.keys(rows).findIndex(e => {
      const rowItem = rows[e][0]
      if (isDoubleDimension && Array.isArray(value)) {
        return (rowItem[hoverDimensionName] || rowItem[dimension]) === value[0][0] && e[`${dimensionOriginalName[0]}`] === value[1][0]
      } else {
        return (rowItem[hoverDimensionName] || rowItem[dimension]) === value
      }
    })
  const isNotChoose = isChartSet || selectIndex === -1 || (Array.isArray(vm.interactionOptions) && !vm.interactionOptions.length)
  const themeConfig = getThemeConfig(
    vm.themeType,
    {
      attributes: ['labelConfig'],
      chartAlias: chartUserConfig.chartAlias,
    }
  )
  const labelThemeConfig = Object.assign(themeConfig.labelConfig, chartUserConfig.labelConfig)
  // 旭日图、矩形树图存在子级结构，selectIndex为父级索引，通过value匹配父级结构数据可能失败
  const specialChartNotChoose = isChartSet || !value || (Array.isArray(vm.interactionOptions) && !vm.interactionOptions.length)
  // 存在分割线的双维度图形，series最后一个对象为分割线数据
  const specialTypeArr = ['treemap', 'sunburst', 'tree']
  function specialChartStyleSetting(param, isTree) {
    param.forEach((e, index) => {
      if (!isTree) {
        e.itemStyle = Object.assign({}, e.itemStyle, {
          color: e.itemStyle?.color,
          opacity: !specialChartNotChoose ? 0.5 : (e.itemStyle?.opacity || 1)
        })
      } else {
        let _opacity = 1
        if (dimensionColors.length) {
          const color = dimensionColors[index]?.color || ''
          _opacity = color && color.includes('rgba') ? Color.getColorOpacity(color) : 1
        }
        e.itemStyle = Object.assign({}, e.itemStyle, {
          colorAlpha: !specialChartNotChoose ? 0.5 : _opacity
        })
      }
      if (e.hasOwnProperty('children')) {
        e.children.map(par => {
          // 当前子级块是否被选中
          const isChildSelected = par[`${dimension}`] === value
          // 存在交互状态且当前子级块不被选中时，透明度设置为0.5
          if (!isTree) {
            par.itemStyle = Object.assign({}, par.itemStyle, {
              color: e.itemStyle.color,
              opacity: !specialChartNotChoose && !isChildSelected ? 0.5 : (par.itemStyle?.opacity || 1)
            })
          } else {
            let _opacity = 1
            if (dimensionColors.length && index < dimensionColors.length) {
              const color = dimensionColors[index].color
              _opacity = color && color.includes('rgba') ? Color.getColorOpacity(color) : 1
            }
            par.itemStyle = Object.assign({}, par.itemStyle, {
              colorAlpha: !specialChartNotChoose && !isChildSelected ? 0.5 : _opacity
            })
          }
        })
      }
    })
  }

  function setTreeColor(treeData, level = 0) {
    const key = 'label'
    if (!Array.isArray(treeData)) return
    treeData.forEach(treeItem => {
      if (level) {
        treeItem[key] = Object.assign({}, treeItem[key], {
          color: labelThemeConfig.color,
          fontFamily: labelThemeConfig.fontFamily,
          fontSize: vm.getPxOfHighResolution(labelThemeConfig.fontSize)
        })
        treeItem.level = level
      }
      if (treeItem.hasOwnProperty('children')) {
        setTreeColor(treeItem.children, level + 1)
      }
    })

  }
  const dimensionColor = Color.getDimensionColor(vm)
  options.series.forEach((item, i) => {
    if (!item) return
    if (isDoubleDimension && i === (options.series.length - 1) && !specialTypeArr.includes(item.type)) return
    if (item.type === 'bar' && item.isBackground) return
    if (item.isCustomSeries) return
    switch (item.type) {
      case 'line':
        // 折线图放大选中的点
        if (item.data) {
          const symbolSize = item.symbolSize ? Number(item.symbolSize) : 6
          item.data = item.data.map((e, index) => {
            let _value = Array.isArray(e) ? e[1] : e && typeof e === 'object' ? e.value : e
            _value = {
              value: _value,
              symbolSize: isChartSet ? symbolSize : selectIndex === index ? symbolSize + 4 : symbolSize,
            }
            if (typeof e === 'object') {
              Object.assign(_value, e)
            }
            // // 处理折线图精度
            // if (_value.value) {
            //   const data = _value.value.toString()
            //   _value.value = data.includes('.') && data.split('.')[1].length > 4 ? Number(Number(data).toFixed(4)) : Number(_value.value)
            // }
            return _value
          })
        }
        break
      case 'scatter':
        // 散点图透明其他点
        const _opacity = Color.getColorOpacity(chartUserConfig.colors[0])
        if (chartUserConfig.chartAlias === 've-scatter-normal') {
          item.itemStyle = Object.assign({}, item.itemStyle, {
            color: dimensionColor.getColor(item.name)
          })
        }
        let key = selectIndex === i
        item.data.forEach(e => {
          if (!e) return
          // 地图散点图需要额外处理
          if (chartUserConfig.chartType === 've-map') {
            const selectItem = rows[selectIndex]
            key = selectItem && selectItem[`VIEWFORMAT_${dimension}`] === e.name
          }
          const itemOpacity = isNotChoose ? 1 : key ? 0.8 : 0.2
          if (e.itemStyle) {
            Object.assign(e.itemStyle, {
              opacity: itemOpacity,
            })
          } else {
            e.itemStyle = {
              opacity: itemOpacity,
            }
          }

        })

        break
      case 'gauge':
        // 仪表盘单独分离出来
        const arr = [...item.data]
        // 设置仪表盘辅助线
        const data = afterConfigHandler['settingGaugeLine'](vm, options)
        if (data?.length) arr.push(...data)
        item.data = arr.map(e => {
          const hasTooltip = e.tooltip ? { tooltip: e.tooltip } : {}
          const hasTitle = e.title ? { title: e.title } : {}
          return {
            value: e.value,
            name: e.name ? (e.name || e.metricValue) : vm.chartUserConfig.gaugeTarget.settingGauge.title,
            pointer: e.pointer,
            itemStyle: e.itemStyle,
            row: e.row,
            emphasis: e.emphasis ? e.emphasis : {},
            ...hasTooltip,
            ...hasTitle,
          }
        })
        break
      case 'pie':
        // 层叠圆形图交互圆形透明度不同
        if (chartAlias === 've-roundCascades') {
          Object.assign(item.itemStyle, { opacity: selectIndex !== i ? isNotChoose ? _opacity : 0.2 : _opacity })
        } else {
          let seriesIndex = 0
          let currentSelectIndex = selectIndex
          let isPieNormal = false
          if (chartAlias === 've-pie-normal') {
            isPieNormal = true
            if (currentSelectIndex >= vm.chartData.rows.length) {
              seriesIndex = 1
              currentSelectIndex = selectIndex - vm.chartData.rows.length
            }
          }
          // 饼图选中区域分离出来
          item.data = item.data.map((e, index) => {
            let _value = {
              ...e,
              name: e.name,
              value: e.value,
              selected: isChartSet ? false : (currentSelectIndex === index && (!isPieNormal || seriesIndex === i)),
              itemStyle: {
                ...e.itemStyle,
                opacity: (isNotChoose || (currentSelectIndex === index && (!isPieNormal || seriesIndex === i))) ? _opacity : 0.5,
                // color: dimensionColor.getColor(e.name)
              }
            }
            return _value
          })
        }
        break
      case 'sunburst':
        // 旭日图除选中块外，其余块透明度设置为0.85
        specialChartStyleSetting(item.data)
        break
      case 'radar':
        break
      case 'treemap':
        if (isDoubleDimension) {
          specialChartStyleSetting(item.data, 'treemap')
        } else {
          item.data = item.data.map((e, index) => {
            if (selectIndex !== index && Array.isArray(chartUserConfig.dimensionColors)) {
              // const color = chartUserConfig.dimensionColors[index].color
              const _color = chartUserConfig.dimensionColors.find(colorItem => colorItem.name === e.name)
              if (!_color) return e
              const color = _color.color
              const _opacity = color && color.includes('rgba') ? Color.getColorOpacity(color) : 1
              if (e.itemStyle) {
                Object.assign(e.itemStyle, {
                  colorAlpha: isNotChoose ? _opacity : 0.5
                })
              } else {
                e.itemStyle = {
                  colorAlpha: isNotChoose ? _opacity : 0.5
                }
              }
            }
            return e
          })
        }
        break
      case 'map':
        item.data = item.data.map((e, index) => {
          if (selectIndex !== index) {
            let selectItem = rows[selectIndex]
            let key = selectItem && selectItem[`VIEWFORMAT_${dimension}`] === e.name
            if (e.itemStyle) {
              Object.assign(e.itemStyle, {
                opacity: isNotChoose ? 1 : key ? 1 : 0.5
              })
            } else {
              e.itemStyle = {
                opacity: isNotChoose ? 1 : key ? 1 : 0.5
              }
            }
          }
          return e
        })
        break
      case 'wordCloud':
        if (Array.isArray(item.data)) {
          item.data = item.data.map((e, index) => {
            const color = e.textStyle?.color
            if (selectIndex !== index) {
              const _opacity = color && color.includes('rgba') ? Color.getColorOpacity(color) : 1
              const wordOpacity = isNotChoose ? _opacity : 0.5
              if (e.textStyle) {
                e.textStyle.color = color.replace(/(rgba\(\d+,\d+,\d+),\d+(\))/, `$1,${wordOpacity}$2`)
              }
            }
            return e
          })
        }
        break
      case 'tree':
        if (Array.isArray(item.data)) {
          setTreeColor(item.data)
        }
        break
      default:
        // 柱状图之类的透明其他柱子
        if (Array.isArray(item.data)) {
          item.data = item.data.map((e, index) => {
            if (chartUserConfig.chartAlias === 've-bar-percent' && typeof e !== 'object') return e
            const dataKey = 'itemStyle'
            let _value = e && typeof e === 'object' ? e.value : e
            if (selectIndex !== index) {
              if (e && typeof e === 'object') {
                if (e[dataKey]) {
                  e[dataKey].opacity = isNotChoose ? _opacity : 0.5
                } else {
                  e[dataKey] = {
                    opacity: isNotChoose ? _opacity : 0.5
                  }
                }
              } else {
                e = {
                  value: _value,
                  [dataKey]: {
                    opacity: isNotChoose ? _opacity : 0.5
                  }
                }
              }
            }
            return e
          })
        }
        break
    }
  })
}

function setSingleAxisPosition(vm, options) {
  const { grid, dataZoom } = options
  if (vm.chartUserConfig.chartAlias === 've-themeRiver' && options.singleAxis) {
    const { top, right, bottom, left } = grid
    const singleAxisBottom = options.singleAxis.bottom
    top !== undefined && (options.singleAxis.top = top)
    right !== undefined && (options.singleAxis.right = right)
    bottom !== undefined && (options.singleAxis.bottom += bottom + (singleAxisBottom ? 8 : 0) + (!vm.isMobile && dataZoom ? 30 : 0) + (vm.isMobile && vm.isChartSet ? 20 : 0))
    left !== undefined && (options.singleAxis.left = left)
  }
}

export function getValidAuxiliaryLine(auxiliaryLineData, vm) {
  const chartResponse = vm.content.chartResponse || {}
  const auxLineResult = chartResponse.auxLineResult
  const validAuxiliaryLine = auxiliaryLineData.filter(item => {
    if (!Object.keys(auxLineResult?.[item.id] || {}).length) return false
    const { metricValue, id, auxiliaryLineType } = item
    const noPermission = checkNoDataPermissions({ metric: id, chartResponse, auxLineResult, type: 'auxLineResult' })
    return !vm.auxiliaryLineHidden[item.id] && !noPermission
  })
  return validAuxiliaryLine
}

// 设置图形辅助线
function settingAuxiliaryLine(vm, options) {
  let { chartAlias, xAuxiliaryLineData = [], yAuxiliaryLineData = [], } = vm.chartUserConfig
  const { metricSecondList, } = vm.UserConfig
  if (NOT_AUXILIATYLINE_CHART.includes(chartAlias)) return
  xAuxiliaryLineData = getValidAuxiliaryLine(xAuxiliaryLineData, vm)
  yAuxiliaryLineData = getValidAuxiliaryLine(yAuxiliaryLineData, vm)

  if (Array.isArray(xAuxiliaryLineData) && xAuxiliaryLineData.length) {
    settingAuxiliaryLineItem({ vm, options, auxiliaryLineData: xAuxiliaryLineData, isYaxis: chartAlias === 've-scatter-normal' })
  }
  if (Array.isArray(yAuxiliaryLineData) && yAuxiliaryLineData.length && [...SUPPORT_DEPUTY_AXIS, 've-scatter-normal'].includes(chartAlias)) {
    if (chartAlias === 've-composite' && (!vm.chartSettings.axisSite || !metricSecondList.length)) return
    settingAuxiliaryLineItem({ vm, options, auxiliaryLineData: yAuxiliaryLineData, isYaxis: false, axisType: SUPPORT_DEPUTY_AXIS.includes(chartAlias) ? 'copyY' : '' })
  }
}

// 不均等分布柱状图
function inequalityHistogram(vm, options) {
  // echarts 对数轴只能显示正数，显示负数会有问题
  const { chartUserConfig, chartSettings, legendSelected = {} } = vm
  const { rows } = vm.chartData
  // const optionsCopy = JSON.parse(JSON.stringify(options))
  const { chartAlias, yAxisSetting = [], xAxisSetting = [], isShowLog } = chartUserConfig
  const { dimensionExtendList, metricAllList, metricSecondList } = vm.UserConfig
  if (!isShowLog || !LOGARITHMARR.includes(chartAlias)) return
  const { stack, } = chartSettings
  if (Array.isArray(stack) && stack[0] && stack[0].length) return
  const key = [CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, 've-bar-normal'].includes(chartAlias) ? 'xAxis' : 'yAxis'
  const axisSetting = [CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, 've-bar-normal'].includes(chartAlias) ? xAxisSetting : yAxisSetting
  function getMinData(_metrics, isAnotherAxias = false) {
    // 对数轴计算最小最大的数值
    let metricsDataArr: number[] = []
    _metrics.forEach(metricsItem => {
      const isShowMetricsItem = !Object.keys(legendSelected).length || legendSelected[metricsItem.lang_alias]
      if (dimensionExtendList.length) {
        isShowMetricsItem && metricsDataArr.push(...rows.map(rowsItem => rowsItem.extendData.map(extendItem => extendItem[metricsItem.alias || metricsItem.labeName])).flat())
      } else {
        isShowMetricsItem && metricsDataArr.push(...rows.map(rowsItem => rowsItem[metricsItem.alias || metricsItem.labeName]))
      }
    })
    const minData = metricsDataArr.length ? Math.min(...metricsDataArr) : isAnotherAxias ? 0 : 1
    const specialArr = [Infinity, -Infinity]
    return specialArr.includes(Math.log10(minData)) ? 1 : 10 ** Math.floor(Math.log10(minData))
  }
  let min = getMinData(metricAllList) || 1
  options[key].forEach((axisItem, i) => {
    if (!axisItem.hasOwnProperty('min') || axisItem.min < 0) return
    const max = 10 ** Math.ceil(Math.log10(axisItem.max)) || 1

    let isHide = false
    if (metricSecondList.length && i === 1) {
      // 次轴最小值
      min = getMinData(metricSecondList, true)
      isHide = i === 1 && !!Object.keys(legendSelected).length && !metricSecondList.some(e => legendSelected[e.lang_alias])
    }

    Object.assign(axisItem, {
      show: !isHide,
      type: 'log',
      min,
      max,
      interval: 'auto'
    })
    if (!axisSetting[i] || !axisSetting[i].formatType || axisSetting[i].formatType === 'normal') {
      const formatFunc = getFormatterFunc('thousands')
      Object.assign(axisItem.axisLabel, {
        formatter: (value) => {
          const data = value.toString().split('.')[1]?.length || 0
          return data > 2 ? `${formatFunc(max)}` : `${formatFunc(value)}`
        }
      })
    }
  })
  Array.isArray(options.series) && options.series.forEach(seriesItem => {
    if (Array.isArray(seriesItem.data)) {
      seriesItem.data = seriesItem.data.map(dataItem => {
        return dataItem && dataItem !== '0' ? dataItem : ''
      })
    }
  })
}

function settingZeroIntevalAlign(vm, options) {
  const { chartAlias, zeroIntevalAlign, } = vm.chartUserConfig
  const { xAxis, yAxis } = options
  const axis = isReverseAxisChart(vm) ? xAxis : yAxis
  if (!SUPPORT_DEPUTY_AXIS.includes(chartAlias) || !zeroIntevalAlign || axis?.length !== 2 || !axis.every(item => item.hasOwnProperty('interval'))) return
  let positiveNumber = 4
  let negativeNumber = 4
  const zeroYAxis = axis.map(item => {
    const { max, min, interval } = item
    return {
      _positiveNumber: Math.ceil(Math.abs(max / interval)),
      _negativeNumber: Math.ceil(Math.abs(min / interval)),
    }
  })
  positiveNumber = Math.max(...zeroYAxis.map(item => item._positiveNumber))
  negativeNumber = Math.max(...zeroYAxis.map(item => item._negativeNumber))
  axis.forEach(item => {
    const { interval } = item
    const N = String(interval).split('.')[1]?.length || 0
    item.max = (interval * (10 ** N) * positiveNumber) / (10 ** N)
    item.min = (interval * (10 ** N) * negativeNumber) / (10 ** N) * -1
  })
}

// 强行设置刻度间距，保证刻度轴等分
function axisInterval(axis) {
  if (axis.type === 'log') return
  let num = axis.max || axis.min
  // const num = axis.max || axis.min
  if (isNaN(num)) return
  if (axis.hasOwnProperty('max') && axis.hasOwnProperty('min') && axis.isCustomSet) return
  if (axis.max && axis.min) {
    num = Math.max(Math.abs(axis.max), Math.abs(axis.min))
  }
  let interval = Math.abs(num / 4)
  if (axis.max && axis.min && axis.max !== axis.min) {
    const positive = Math.abs(axis.max) > Math.abs(axis.min)
    const miniAxis = positive ? 'min' : 'max'
    const mathKey = positive ? 'floor' : 'ceil'
    const a = Math[mathKey](axis[miniAxis] / interval)
    const N = String(interval).split('.')[1]?.length || 0
    axis[miniAxis] = axis.max > 0 && axis.min < 0 ? (interval * (10 ** N) * a) / (10 ** N) : 0
    axis.interval = (axis.max - axis.min) / (4 + a)
  } else if (!axis.max && !axis.min && axis.max === axis.min) {
    // 当不存在数据时，max、min的值都为0，echarts会默认将坐标轴分割间隔处理成0.2
    axis.max = 1
    interval = 0.2
  }
  interval && (axis.interval = interval)
  axis.type = 'value'
}

// label或者tooltip，找到rows中的那条数据
// function getDataFromRows(rows, value, name) {
//   if (!Array.isArray(rows)) return
//   let activeRow = ''
//   rows.some(item => {
//     const flag = Object.values(item).includes(value) && Object.values(item).includes(name)
//     if (flag) {
//       activeRow = item
//     } else {
//       const { extendData = [] } = item
//       if (extendData.length) {
//         activeRow = getDataFromRows(extendData, value, name)
//       }
//     }
//     return flag || activeRow
//   })
//   return activeRow
// }

// 设置维度颜色
function setDimensionColor(vm, options) {
  const { chartUserConfig, chartData, themeType } = vm
  const { colorType, chartAlias, dimensionColors = [], negativeConfig = {}, dimensionLangColor = false } = chartUserConfig
  const { dimensionList, metricAllList } = vm.UserConfig
  const metrics = metricAllList.map(m => m.alias || m.labeName) || []
  if (!colorType || !hasDimensionColor(chartAlias, dimensionList.length) || (colorType !== 'dimension' && chartAlias !== 've-themeRiver')) return
  // 获取默认颜色
  let optionsCopy = deepClone(options)
  const { series } = optionsCopy
  // 负值样式设置
  const isShowNegativeStyleBorder = negativeConfig?.negativeValue === 'border'
  const isShowNegativeStyleColor = negativeConfig?.negativeValue === 'color'
  const negativeBackground = negativeConfig?.negativeStyle?.color
  const negativeLineWidth = negativeConfig?.negativeStyle?.lineWidth
  const isTreeMap = chartAlias === 've-treemap'
  const isSunburst = chartAlias === CHART_ALIAS_TYPE.VE_SUNBURST
  const seriesCopy = series.map(item => {
    if (['themeRiver'].includes(item.type)) {
      let chartColors = Color.getDimensionColor(vm).noRepeat
      let colorResult: any[] = []
      let dataResult = []
      item.data.forEach(data => {
        if (colorResult.length && colorResult.find(cr => cr.name === data[2])) return
        colorResult.push({
          name: data[2],
          color: chartColors.find(dc => dc.name === data[2] || (dimensionLangColor && dc.langName && (dc.langName?.split('$mls$') || []).includes(data[2]))).color
        })
        dataResult = dataResult.concat(item.data.filter(d => d[2] === data[2]))
      })
      item.color = colorResult.map(cr => cr.color)
      options.legend.data = colorResult.map(cr => cr.name)
      item.data = dataResult
    } else if (['sunburst', 'treemap'].includes(item.type)) {
      // 旭日图、树图
      let defaultIndex = 0
      const defaultColors = Color.getDefaultChartColor(chartUserConfig, { colorThemeType: themeType, vm: vm })
      const chartColors = Color.getDimensionColor(vm).noRepeat
      item.data.forEach(dataItem => {
        const curIndex = defaultIndex % defaultColors.length
        // let hasColor = dimensionColors[curIndex]
        const isNegative = dataItem[metrics[0]] < 0
        // 处理维度多语言
        let hasColor = chartColors.find(colorItem => (colorItem.name === dataItem.name || (dimensionLangColor && colorItem.langName && (colorItem.langName?.split('$mls$') || []).includes(dataItem.name))))
        const isSetNegativeColor = (isTreeMap || isSunburst) && isShowNegativeStyleColor && isNegative
        // 优先取用户配置颜色
        let itemStyle = { color: isSetNegativeColor ? chartUserConfig.negativeConfig.negativeColor : (hasColor ? hasColor.color : defaultColors[curIndex]) }
        const setNegativeStyle = function (_itemStyle) {
          const style = { ..._itemStyle }
          // 设置矩形颜色
          if (isSetNegativeColor) {
            style.color = chartUserConfig.negativeConfig.negativeColor
          }
          // 设置矩形树图边框样式
          if ((isTreeMap || isSunburst) && isShowNegativeStyleBorder && isNegative) {
            style.borderColor = negativeBackground
            style.borderWidth = negativeLineWidth
          }
          return style
        }
        defaultIndex++
        // 给父级配置颜色
        dataItem.itemStyle = isSunburst ? setNegativeStyle(itemStyle) : (dataItem.children ? itemStyle : setNegativeStyle(itemStyle))
        // 给子级配置颜色
        if (dataItem.children) {
          const childStyle = setNegativeStyle(itemStyle)
          dataItem.children.forEach((child, childIndex) => {
            const opacity = {}
            if (item.type === 'sunburst') {
              opacity.opacity = 1 - (childIndex % 9 + 1) * 0.1
            }
            child.itemStyle = { ...childStyle, ...opacity }
          })
        }
      })
    } else if (!['ve-ring-multiple', 've-bar-percent'].includes(chartAlias) && ['bar', 'pictorialBar'].includes(item.type)) {
      // 百分比条形图的维度颜色在barPercentSetting里设置
      const { repeat: dimensionColorsResult } = Color.getDimensionColor(vm)
      item.data.forEach((dataItem, index) => {
        // 瀑布图不处理透明色柱子
        const currentColor = dimensionColorsResult[index]
        if (typeof dataItem !== 'object' || (chartAlias === 've-waterfall' && item.total)) return

        dataItem.itemStyle = Object.assign({}, dataItem.itemStyle || {}, { color: currentColor })
      })
    }
    return item
  })
  options.series = seriesCopy
}

// 是否显示流图
export function isThemeRiver(chartUserConfig) {
  const { dimensionList, metricsContainer } = chartUserConfig
  if (!dimensionList || !metricsContainer?.default?.length) return false
  return dimensionList.length === 2 && metricsContainer.default.length === 1 && dimensionList[0].columnTpe === 'date'
}
export function isSurportMetricSplitDisplay(chartUserConfig) {
  const { extendDimensionList = [], chartAlias, compositeChart = {} } = chartUserConfig
  if (!EXTEND_DIMENSION_CHART.includes(chartAlias) || !extendDimensionList.length) return false
  return ALLOW_METRIC_SPLIT_DISPLAY_CHART.includes(chartAlias) || (chartAlias === 've-composite' && compositeChart.stack)
  // return chartAlias === 've-histogram-stack' || (chartAlias === 've-composite' && compositeChart.stack)
}

// 进一法保留小数
function keepDecimalNumByAddMethod(data, decimalNum) {
  return Math.ceil(data * Math.pow(10, decimalNum)) / Math.pow(10, decimalNum)
}
// 调整圆环比例
function setRingWidthRatio(vm, options) {
  const { chartUserConfig: { ringWidthRatio, chartAlias, pieSetting = {} } } = vm
  const { series } = options
  if (!ringWidthRatio || !RING_WIDTH_RATIO.includes(chartAlias)) return

  const scaleType = pieSetting?.radius?.type

  options.series = series.map(item => {
    const { radius, type } = item
    if (type !== 'pie') return item

    if (scaleType === 'fixedRadius') {
      radius[0] = radius[1] - radius[1] * ringWidthRatio / 100
    } else {
      const baseRatio = Number(radius[1].replace('%', ''))
      radius[0] = `${(100 - ringWidthRatio) * baseRatio / 100}%`
    }

    return { ...item, radius }
  })
}

// 度量汇总位置切换
function measureSumchange(vm, options) {
  const { chartUserConfig: { measureConfig, chartAlias }, isMobile } = vm
  if (MEARSURE_DIS_CENTER.includes(chartAlias) && measureConfig?.hide && measureConfig?.displayPosotion === 'disCenter') {
    const { subtext, subtextStyle, measureSummaryValue, valueDirection } = measureConfig
    const regularText = [subtext, vm.$t(`sdp.views.measure${measureSummaryValue}Show`)]
    const [textNo1, textNo2] = valueDirection === 'top' ? regularText : regularText.reverse()
    const pieSeries = options.series.filter(item => {
      const { type } = item
      return type === 'pie'
    })
    if (pieSeries.length <= 0) return
    const { center } = pieSeries[0]
    const font = subtextStyle.fontSize + 'px ' + subtextStyle.fontFamily
    const fill = subtextStyle.color
    const measureCenterTxt = {
        type: 'group',
        left: center[0],
        top: center[1],
        bounding: 'raw', // Notice that this prop is necessary.
        children: [{
            type: 'text',
            cursor: 'auto',
            silent: true,
            style: {
                text: `${textNo1}\n${textNo2}`,
                textVerticalAlign: 'middle',
                textAlign: 'center',
                font,
                fill,
            }
        }]
    }
    const elements = [measureCenterTxt]
    subtext !== '' && (options.graphic = { elements })
  }
}

// 获取最大最小带宽数据
export function getBandwidthData(chartUserConfig) {
  if (chartUserConfig.chartAlias !== CHART_ALIAS_TYPE.VE_BANDWIDTH || !chartUserConfig.bandwidthData) return {}

  const { bandwidthData: { maxBandwidthList, minBandwidthList, bandwidthList, rankSetConfig }, bandwidthMode } = chartUserConfig
  if (bandwidthMode === VE_BANDWIDTH_TYPE.CUSTOM_BANDWIDTH && maxBandwidthList?.length && minBandwidthList?.length) {
    return {
      max: maxBandwidthList[0],
      min: minBandwidthList[0],
      rank: rankSetConfig,
    }
  } else if (bandwidthMode === VE_BANDWIDTH_TYPE.AUTOMATIC_BANDWIDTH && bandwidthList?.length) {
    return {
      bw: bandwidthList[0],
      rank: rankSetConfig,
    }
  }
  return {}
}

// 获得初始化带宽数据
export function getBandwidthInitData(themeType) {
  return {
    maxBandwidthList: [],
    minBandwidthList: [],
    bandwidthList: [],
    rankSetConfig: {
      isOpen: false,
      apposition: true,
      field: { labeName: '' },
      format: '/',
      ...JSONClone(getThemeConfig(themeType,
        {
          attributes: ['rankConfig'],
        }).rankConfig.rankSetConfig
      )
    }
  }
}
// 兼容后台需要的数据
export function setBandwidthRequestData(ret, chartUserConfig) {
  if (chartUserConfig.chartAlias !== CHART_ALIAS_TYPE.VE_BANDWIDTH) return
  const { max, rank, bw } = getBandwidthData(chartUserConfig)
  ret.layers && (Array.isArray(ret.layers)) && ret.layers.forEach(l => {
    if (max) {
      if (rank?.isOpen) {
        // 是否需要排名
        l['displayRanking'] = rank.isOpen
        if (rank?.field?.labeName) {
          l['bandwidthRank'] = {
            alias: rank.field.labeName,
            columnName: rank.field.labeName,
          }
        }
      }
    } else if (bw) {
      if (rank?.isOpen) {
        // 是否需要排名
        l['displayRanking'] = rank.isOpen
        // 默认rank，连续密集dense_rank
        l['windowFn'] = rank?.apposition ? 'rank' : 'dense_rank'
      }
    }
  })
}

function handChartColorSet(vm, options) {
  const { chartUserConfig: { barchartWarnType, chartAlias }, isMobile } = vm
  if (SET_GRADIENT_CHART.includes(chartAlias)) {
    options.color = options.color.map(c => {
      return Color.getEchartsColorItem(c)
    })
  }
}

function handAlignmentMethod(vm, options) {
  const { chartUserConfig: { alignmentMethod, chartAlias } } = vm
  // 需要图表对齐方式的图形
  let needShowOptionChart = [
    CHART_ALIAS_TYPE.VE_BAR,
    CHART_ALIAS_TYPE.VE_BAR_PERCENT,
    CHART_ALIAS_TYPE.VE_BAR_STACK,
    CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT
  ]
  if (needShowOptionChart.includes(chartAlias) && !!alignmentMethod && alignmentMethod === 'right') {
    options.xAxis = options.xAxis.map(x => {
      return {
          ...x,
          inverse: true
        }
    })
    options.yAxis = options.yAxis.map(x => {
      return {
          ...x,
          position: 'right'
        }
    })
    let leftTemp = options['grid'].left
    let rightTemp = options['grid'].right
    if(options?.dataZoom.length > 0){ //开启Datazoom 情况
      Object.assign(options['grid'], { left: rightTemp + 14 - 16, right: leftTemp + 16 })
    }else{
      Object.assign(options['grid'], { left: rightTemp - 16, right: leftTemp + 16 })
    }
  }
}

export const getCalenderComponentMonths = (data = [], elementId, paramsType, dateFormat) => {
  const { BUSSINESS_CALENDAR, FINANCIAL_CALENDAR, CALENDAR_QUICK, DATE_QUICK_OPERATION } = TYPE_PARAM_ELEMENT
  const isIncludesArr = paramsType === RUN_TYPE.handRun ? [BUSSINESS_CALENDAR, FINANCIAL_CALENDAR] : [CALENDAR_QUICK, DATE_QUICK_OPERATION]
  if (!data || !data.some(el => isIncludesArr.includes(el.type) && el.content.bindElement.includes(elementId))) return []

  const callback = (time) => {
    if (/([yY]+)/.test(dateFormat) && RegExp.$1.length < 3) {
      time = +time.slice(0, 2) <= 99 && +time.slice(0, 2) >= 90 ? `19${time}` : `20${time}`
    }
    return time
  }
  const getMonth = (item) => {
    const num = Number(item)
    return num > 9 ? num : `0${num}`
  }
  const isValidDate = (date) => {
    return date instanceof Date && !isNaN(date.getTime())
  }

  const dateFormatArr = dateFormat.split(/[-./]/)
  const monthIndex = dateFormatArr.findIndex(item => /([mM]+)/.test(item))
  const yearIndex = dateFormatArr.findIndex(item => /([yY]+)/.test(item))

  const yearLen = 4
  const mothNum = 12

  const getYearDate = (year) => {
    // 年的操作
    let arr = new Array(mothNum)
    for (let i = 0; i < arr.length; i++) {
      const month = i + 1
      arr[i] = `${year}-${getMonth(month)}`
    }
    return arr
  }

  const times = data.filter(el => isIncludesArr.includes(el.type) && el.content.bindElement.includes(elementId)).map(param => {
    const { activeTabs, Event } = param.content.typeSaveData

    let list = param.content.thisPeriodKey.this_period_key

    if (activeTabs === TYPE_LIST.Event) {
      const { currentList = [] } = Event
      list = currentList
    }

    return list.map(time => {
      if (time.includes(' ~ ')) {
        const [startTime, endTime] = time.split(' ~ ')

        const startFormat = callback(startTime.split(' ')[0]).split(/[-./]/)
        const endFormat = callback(endTime.split(' ')[0]).split(/[-./]/)

        let startMonth = startFormat[monthIndex]
        let startYear = startFormat[yearIndex]
        let endMonth = endFormat[monthIndex]
        let endYear = endFormat[yearIndex]

        startMonth = Math.min(startMonth, endMonth)
        startYear = Math.min(startYear, endYear)
        endMonth = Math.max(startMonth, endMonth)
        endYear = Math.max(startYear, endYear)

        let diffY = endYear - startYear
        let arr = []
        const transformEnd = `${endYear}-${getMonth(endMonth)}`
        const transformStart = `${startYear}-${getMonth(startMonth)}`
        for (let year = diffY; year >= 0; year--) {
          arr.push(...getYearDate(startYear + year))
        }
        arr = arr.filter(date => date <= transformEnd && date >= transformStart)
        return arr

        // let transformEnd = `${endYear}-${getMonth(endMonth)}`
        //
        // const arr = [transformStart]
        //
        // if (transformStart === transformEnd) return arr
        //
        // while (transformStart !== transformEnd) {
        //   if (startMonth === 12) {
        //     startMonth = 1
        //     ++startYear
        //   } else {
        //     ++startMonth
        //   }
        //   transformStart = `${startYear}-${getMonth(startMonth)}`
        //   arr.push(transformStart)
        // }
        // return arr
      } else if (callback(time).length === yearLen) {
        const year = callback(time)

        return getYearDate(year)
      }

      const timeArr = callback(time).split(/[-./]/)
      let month = timeArr[monthIndex]
      let year = timeArr[yearIndex]
      return `${year}-${getMonth(month)}`
    })
  })
  const flattenTimes = flatten(times)
  return [...new Set(flattenTimes)].sort((a, b) => new Date(a) - new Date(b))
}

export function getDataPercentContent(serieItem, currentRow, itemData) {
  let str = ''
  if (itemData?.data?.percentValue) return itemData.data.percentValue
  if (serieItem.metricTotal?.showPercent) {
    str = getBarPercent(serieItem, currentRow)
    // 主维度度量不展示百分比
    if (currentRow?.isMainDimensionMeasure) {
      str = ''
    }
    if (itemData && typeof itemData.data === 'object') {
      itemData.data.percentValue = str
    } else if (itemData) {
      let value = itemData.data
      itemData.data = {
        value,
        percentValue: str,
      }
    }
  }
  return str
}

// 背景趋势配置
export const setBackgroundTrend = (vm, cb) => {
  const { bgTrendSetting, chartAlias, colorType } = vm.chartUserConfig
  if (!BACKGROUND_TREND_LINE_CHART.includes(chartAlias) || !bgTrendSetting || !bgTrendSetting.enable || colorType === 'dimension') return
  console.log('注意: 开始绘制背景趋势图', bgTrendSetting, vm)
  const echartInstance = vm.echartInstance
  const echartModel = echartInstance.getModel()
  const seriesModels = echartModel.getSeries()
  if (!seriesModels || !seriesModels.length) return
  const elements: any[] = []
  const options = echartInstance.getOption()
  seriesModels.forEach((seriesModel, index) => {
    console.log(seriesModel, echartInstance)
    const seriesData = seriesModel.getData()
    const seriesDataCount = seriesData.count()
    // 根据每个box生成polygon
    for (let i = 0; i < seriesDataCount; i++) {
      console.log(i, seriesDataCount)
      const el = seriesData.getItemGraphicEl(i)
      const nextEl = seriesData.getItemGraphicEl(i + 1)
      if (!nextEl) break
      const box = el.getBoundingRect()
      const boxNext = nextEl.getBoundingRect()
      // 从第一个的右上角开始，再到第二个的左上角，再到第二个的左下角，最后第一个的右下角,生成四个点
      let p1,p2,p3,p4
      if ([CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT].includes(chartAlias)) {
        p1 = [box.x, box.y + box.height]
        p2 = [box.x + box.width, box.y + box.height]
        p3 = [boxNext.x + boxNext.width, boxNext.y]
        p4 = [boxNext.x, boxNext.y]
      } else {
        p1 = [box.x + box.width, box.y + box.height]
        p2 = [boxNext.x, boxNext.y + boxNext.height]
        p3 = [boxNext.x, boxNext.y]
        p4 = [box.x + box.width, box.y]
      }

      const points = [p1, p2, p3, p4]
      let _color = options.color[index]
      if (typeof _color === 'object' && _color.colorStops) {
        _color = _color.colorStops[1].color
      } else if (typeof _color === 'object' && _color.length) {
        _color = _color[0]
      }
      const polygon = {
        type: 'polygon',
        shape: {
          points,
        },
        style: {
          fill: _color,
          opacity: bgTrendSetting.style.opacity,
        }
      }
      elements.push(polygon)
    }
  })
  setTimeout(() => {
    const oldGraphic = options.graphic || []
    echartInstance.setOption({
      ...options,
      graphic: { elements }
    }, { replaceMerge: ['graphic'] })
    cb && cb()
  }, 0)
}
