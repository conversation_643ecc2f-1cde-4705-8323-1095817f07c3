// @ts-nocheck
import i18n from 'packages/assets/locale'
import Imgs from './chartsList/imgs'

export const CHART_ALIAS_TYPE = {
  VE_HISTOGRAM: 've-histogram-normal', // 柱状图
  VE_HISTOGRAM_STACK: 've-histogram-stack', // 堆叠柱状图
  VE_LINE: 've-line-normal', // 折线图
  VE_LINE_AREA: 've-line-area', // 折线面积图
  VE_BAR: 've-bar-normal', // 条形图
  VE_PIE: 've-pie-normal', // 饼图
  VE_ROSE: 've-pie-rose', // 玫瑰图
  VE_RING: 've-ring-normal', // 圆环图
  VE_RING_MULTIPLE: 've-ring-multiple', // 多圆环图
  VE_SCATTER: 've-scatter-normal', // 散点图
  VE_COMPOSITE: 've-composite', // 组合图
  VE_RADAR: 've-radar', // 雷达图
  VE_LIQUIDFILL: 've-liquidfill', // 水滴图
  VE_SUNBURST: 've-sunburst', // 旭日图
  VE_TREEMAP: 've-treemap', // 矩形树图
  VE_FUNNEL: 've-funnel', // 漏斗图
  VE_GRID: 've-grid-normal', // 简单表格
  VE_WORDCLOUD: 've-wordcloud', // 词云图
  VE_BAR_PERCENT: 've-bar-percent', // 百分比条形图
  VE_TREE: 've-tree', // 树图
  VE_THEMERIVER: 've-themeRiver', // 河流图
  VE_CALENDAR: 've-calendar', // 日历图
  VE_PICTORIALBAR: 've-pictorialbar', // 象形柱图
  VE_BAR_STACK: 've-bar-stack', // 堆叠条形图
  VE_WATERFALL: 've-waterfall', // 瀑布图
  VE_ROUNDCASCADES: 've-roundCascades', // 层叠圆形图
  VE_GAUGE: 've-gauge-normal', // 仪表盘
  VE_BANDWIDTH: 've-bandwidth', // 带宽图
  VE_MAP: 've-map-parent', // 地图
  VE_STACK_PERCCENT: 've-stack-percent', // 百分比堆叠柱状图
  VE_BAR_STACK_PERCENT: 've-bar-stack-percent', // 百分比堆叠条形图
  VE_BAR_BUTTERFLY: 've-bar-butterfly', // 蝴蝶图
  VE_BAR_STACK_PERCENT: 've-bar-stack-percent', // 百分比堆叠条形图
  VE_BAR_HEATMAP: 've-bar-Heatmap',  // 热力图
  VE_DECOMPOSITION: 've-decomposition', // 分解树
}

export const hideChartOfMobile = [CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_DECOMPOSITION]

export const SPECIAL_PROP_TYPE = {
  negativeConfigChartTypes: [CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_SUNBURST]
}

export function getChartFieldLengthRequired(chartUserConfig, params: any = {}) {
  const { chartAlias } = chartUserConfig
  function getLen(key) {
    if (params.hasOwnProperty(key)) return params[key]
    return false
  }
  if ([CHART_ALIAS_TYPE.VE_SCATTER].includes(chartAlias)) {
    // 散点图，x轴或者y轴长度不为1，验证不通过
    if (getLen('metricFirst', 1) !== 1) return true
    if (getLen('metricSecond') !== 1) return true
    if (!getLen('dimension')) return true
  } else if ([CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT].includes(chartAlias)) {
    // 百分比堆叠柱状图，度量长度为[1, 20]，否则验证不通过
    if (getLen('metricFirst') < 1 || getLen('metricFirst') > 20) return true
    if (getLen('dimension') < 1 || getLen('dimension') > 2) return true
  } else if ([CHART_ALIAS_TYPE.VE_GRID].includes(chartAlias)) {
    // 简单表格
    if (getLen('metricAll') > 50) return true
    if (!getLen('dimension') && !getLen('metricAll')) return true
  } else if ([CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY].includes(chartAlias)) {
    if (getLen('metricAll') !== 2) return true
    if (getLen('dimension') !== 1) return true
  } else if ([CHART_ALIAS_TYPE.VE_GAUGE].includes(chartAlias)) {
    if (getLen('metricAll') !== 1) return true
    if (getLen('target') !== 1) return true
    if (getLen('dimension')) return true
  } else if (SINGLE_METRIC_CHART.includes(chartAlias)) {
    // 单度量图形，度量长度不为1，验证不通过
    if (getLen('metricAll') !== 1) return true
  } else {
    if (!getLen('metricAll')) return true
  }
}

export const STR = 'VIEWFORMAT'

export const VE_BANDWIDTH_TYPE = {
  CUSTOM_BANDWIDTH: 'customBandwidth',
  AUTOMATIC_BANDWIDTH: 'automaticBandwidth',
  STACK: 'bandwidth'
}

// 折现预警，折线颜色，这里是有折线的图的列表，组合图中也有这线图，但组合图需要单独判断，所以这里面不放置
export const HAS_LINE_CHART_LIST = [CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE_AREA]

// 置顶已选图例，列表
export const TOP_SELECTED_LEGEND_LIST = [
  CHART_ALIAS_TYPE.VE_HISTOGRAM, // 柱状图
  CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, // 堆叠柱状图
  CHART_ALIAS_TYPE.VE_LINE, // 折线图
  CHART_ALIAS_TYPE.VE_LINE_AREA, // 折线面积图
  CHART_ALIAS_TYPE.VE_BAR, // 条形图
  CHART_ALIAS_TYPE.VE_PIE, // 饼图
  CHART_ALIAS_TYPE.VE_ROSE, // 玫瑰图
  CHART_ALIAS_TYPE.VE_RING, // 圆环图
  CHART_ALIAS_TYPE.VE_SCATTER, // 散点图
  CHART_ALIAS_TYPE.VE_COMPOSITE, // 组合图
  CHART_ALIAS_TYPE.VE_RADAR, // 雷达图
  CHART_ALIAS_TYPE.VE_LIQUIDFILL, // 水滴图
  CHART_ALIAS_TYPE.VE_SUNBURST, // 旭日图
  CHART_ALIAS_TYPE.VE_FUNNEL, // 漏斗图
  CHART_ALIAS_TYPE.VE_THEMERIVER, // 河流图
  CHART_ALIAS_TYPE.VE_PICTORIALBAR, // 象形柱图
  CHART_ALIAS_TYPE.VE_BAR_STACK, // 堆叠条形图
  CHART_ALIAS_TYPE.VE_ROUNDCASCADES, // 层叠圆形图
  CHART_ALIAS_TYPE.VE_BANDWIDTH, // 带宽图
  CHART_ALIAS_TYPE.VE_STACK_PERCCENT, // 百分比堆叠柱状图
  CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, // 百分比堆叠条形图
  CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, // 蝴蝶图
]

// 支持排序设置的图形
export const SORT_SETTING_CHART = [
  CHART_ALIAS_TYPE.VE_COMPOSITE,
  CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
  CHART_ALIAS_TYPE.VE_BAR_STACK,
  CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
  CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
  // CHART_ALIAS_TYPE.VE_BAR_HEATMAP,
]

// 支持极值高亮设置的图形
export const EXTREME_VALUE_HIGHLIGHT = [CHART_ALIAS_TYPE.VE_DECOMPOSITION, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_COMPOSITE,CHART_ALIAS_TYPE.VE_BAR_HEATMAP]
// 水滴图模式
export const VE_LIQUIDFILL_MODE = {
  DIMENSION: 'dimension',
  NORMAL: 'normal',
}

// 扩展维度设置->忽略未选维度度量值，图形
export const IGNORING_UNSELECTED_METRICS_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK]

// 点击图例需要重新计算xy轴最大最小值的图形
export const LEGEND_SELECT_CHANGED_DATA_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_RADAR, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 不支持图例的图形
export const NOT_NEED_LEGEND = [CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

// 自定义度量
export const CUSTOM_METRIC = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 图例数据与度量数据长度相同的图形
export const SAME_LENDGEN_AND_METRIC_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持度量汇总的图形
export const GRAPHARR = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING]

// 支持多度量汇总的图形
export const MULTI_GRAPHARR = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK,]

// 支持对数轴的图形
export const LOGARITHMARR = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 支持显示维度图例的图形（柱状图，条形图，象形柱图，瀑布图）
export const DIMENSION_LEGEND_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 支持指标维度预警的图形
export const DIMENSION_WARNING_CHART = [CHART_ALIAS_TYPE.VE_DECOMPOSITION, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 支持指定维度值指标预警
export const DIMENSION_VALUE_INDICATOR_WARNING_CHART = [CHART_ALIAS_TYPE.VE_DECOMPOSITION, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

// 不需要全选反选的图形
export const NOTSHOWLEGENDSELEVTION = [CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

// 需要设置目标值的图形
export const HAS_TARGET_CHART = [CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_RING_MULTIPLE]

// 支持设置滚动条的图形
export const SCROLL_BAR_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 支持纵向滚动条图形
export const VERTICAL_SCROLL_BAR_CHART = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持维度扩展图形
export const EXTEND_DIMENSION_CHART = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持主维度度量的图形
export const MAIN_DIMENSION_MEASURE_CHART = [CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR]

// 支持时间维度的图形
export const NEED_TIMEDIMENSION_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 除散点图外，需要计算xy轴最大值最小值图形
export const HANDLE_MAX_MIN_VALUE_CHART = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 可以设置预警线的图形
export const WARNING_LINE_CHART = [CHART_ALIAS_TYPE.VE_DECOMPOSITION, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]
export const LINEAR_GRADIENT_WARNING_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_GAUGE]
export const WARNING_NO_LINE_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

// 可以设置纯色预警的图形
export const PURE_COLOR_WARNING_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_GAUGE]

// 支持背景线设置图形
export const SPLIT_LINE_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]
// 支持背景趋势线的图形
export const BACKGROUND_TREND_LINE_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 单度量图形
export const SINGLE_METRIC_CHART = [CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_RING_MULTIPLE, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_BAR_HEATMAP, CHART_ALIAS_TYPE.VE_DECOMPOSITION]

// 翻转Y轴图形
export const REVERSE_AXIS_CHART = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 堆叠图形
export const STACK_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持设置dataZoom图形
export const DATAZOOM_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 除散点图外, 竖向dataZoom图形
export const VERTIVAL_DATAZOOM_CHART = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持设置X轴标签格式图形
export const X_AXIS_LABEL_FORMATTER = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持设置Y轴标签格式图形
export const Y_AXIS_LABEL_FORMATTER = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT]

// 支持保存图例设置的图形
export const LEGEND_SETTING_SAVE_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_RADAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 特殊数据结构的图形，切换需要请求数据
export const SPEICAL_DATA_STRUCTURECHART = [CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]
// 移动端-每页显示条数
export const MOBILE_PAGESIZE_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 移动端支持分页图形
export const MOBILE_PAGE_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 因为vcharts或者echarts的bug，需要重新渲染的时候，重新生成dom的图形
export const RESET_UPDATE_CHART = [CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_RING_MULTIPLE, CHART_ALIAS_TYPE.VE_MAP]

// 支持控制度量值显示图形
export const CONTROL_MEASURES_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_RADAR, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]
// 显示度量弹框中可以设置显示占比的图
export const DISPLAY_LABEL_PERCENT = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]
// 支持无维度图形
export const NO_DIMENSION_CHART = [CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_WATERFALL]

// 不支持辅助线图形
export const NOT_AUXILIATYLINE_CHART = [CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

export const MOBILE_GRIDRIGHT_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_WATERFALL]

// 支持设置数值的显示位置以及旋转角度的图形
export const METRIC_POSITION_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 存在扩展维度颜色开关图形
export const EXTEND_DIMENSION_COLOR_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持双维度图形
export const DOUBLE_DIMENSION_CHART = [CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_HISTOGRAM,
  CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

// 不支持钻取的图形
export const NOT_SUPPORT_DIMENSION_CHART = ['ve-map-world', CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_DECOMPOSITION]

// 饼类图
export const CIRCULAR_CHART = [CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_RADAR, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_RING_MULTIPLE, CHART_ALIAS_TYPE.VE_ROUNDCASCADES]

// 支持设置两行、三行显示的图形
export const LABEL_SHOW_LINES = [CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_RING_MULTIPLE, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_SUNBURST]

// 支持设置动画效果的图形
export const ANIMATION_CHART = [CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_MAP]

// 支持设置-播放动画时进行图表联动-的图形
export const ANIMATION_WITH_INTERACTION_CHART = ['ve-pie-normal', 've-pie-rose', 've-ring-normal', 've-grid-normal']

// 支持悬浮窗轮播的图形
export const ANIMATION_TOOLTIP_CHART = [
  CHART_ALIAS_TYPE.VE_HISTOGRAM,
  CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
  CHART_ALIAS_TYPE.VE_LINE,
  CHART_ALIAS_TYPE.VE_LINE_AREA,
  CHART_ALIAS_TYPE.VE_BAR,
  CHART_ALIAS_TYPE.VE_COMPOSITE,
  CHART_ALIAS_TYPE.VE_BAR_PERCENT,
  CHART_ALIAS_TYPE.VE_THEMERIVER, // 河流图
  CHART_ALIAS_TYPE.VE_PICTORIALBAR, // 象形柱图
  CHART_ALIAS_TYPE.VE_BAR_STACK, // 堆叠条形图
  CHART_ALIAS_TYPE.VE_WATERFALL, // 瀑布图
  CHART_ALIAS_TYPE.VE_STACK_PERCCENT, // 百分比堆叠柱状图
  CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, // 百分比堆叠条形图
]

// 支持柱状图轮播的图形
export const ANIMATION_HISTOGRAM_CHART = [
  CHART_ALIAS_TYPE.VE_HISTOGRAM,
  CHART_ALIAS_TYPE.VE_LINE_AREA,
  CHART_ALIAS_TYPE.VE_COMPOSITE,
  CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
  CHART_ALIAS_TYPE.VE_BAR,
  CHART_ALIAS_TYPE.VE_PICTORIALBAR,
  CHART_ALIAS_TYPE.VE_BAR_PERCENT,
  CHART_ALIAS_TYPE.VE_BAR_STACK,
  CHART_ALIAS_TYPE.VE_LINE,
  CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
  CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
  CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY
]

// 需要设置事件日历标签样式的图形
export const HOLIDAY_STYLE_CHART = [ CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY ]

// 需要正向轮播的图形
export const ANIMATION_POSITIVE_CHART = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_BAR_STACK]

// 不支持空值显示的图形
export const NO_EMPTY_VALUE_SETTING_CHART = [CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_GAUGE]

// 不需要显示设置的图形
export const NO_DISPLAY_SETTING_CHART = [CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_RADAR, CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
  CHART_ALIAS_TYPE.VE_ROUNDCASCADES]

// 支持坐标次轴的图形
export const SPECIAL_SUPPORT_DEPUTY_AXIS = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_PICTORIALBAR]
export const SUPPORT_DEPUTY_AXIS = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_COMPOSITE]

// 支持设置曲线设置的图形
export const LINE_STYLE_SETTING_CHART = [CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_RADAR]
// 支持设置圆环比例的图形
export const RING_WIDTH_RATIO = [CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_LIQUIDFILL]
// 支持度量汇总显示在图形中间的图形
export const MEARSURE_DIS_CENTER = [CHART_ALIAS_TYPE.VE_RING]
// 度量汇总默认隐藏的图形
export const HIDE_MEARSURE_CHART = []

// 支持设置堆叠累加的图形
export const STACK_ACC_CHART = [CHART_ALIAS_TYPE.VE_DECOMPOSITION, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 支持设置渐变色的图形
export const SET_GRADIENT_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_GAUGE]

// 支持关联数据集的图形
export const DATASET_ASSOCIATION_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_GRID, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_COMPOSITE]

// 支持自定义日期维度图形
export const CUSTOM_DATE_DIMENSION_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_PICTORIALBAR,
  CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 不支持日期维度格式设置图形
export const HIDE_DATE_DIMENSION_SETTING_CHART = [CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_THEMERIVER]

// 根据维度值来显示数据的图形
export const NEED_DIMENSION_NAME_CHART = [CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_WORDCLOUD, CHART_ALIAS_TYPE.VE_THEMERIVER]
// 标签跟随维度颜色
export const FOLLOWS_DIMENSION_COLOR = [CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_RING_MULTIPLE, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_LIQUIDFILL]

// 展示数值显示的图形
export const LABEL_LINE_SHOW_CHART = [CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_RING_MULTIPLE, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_TREE]

export const BASE_RADIUS = '70%'
export const BASE_RADIUS_CombinePie = '55%'

// 支持度量柱设置图形
export const BAR_SETTING_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 支持设置-柱形圆角
export const BAR_RADIUS_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]
// 负数时根据 X 轴翻转的图形
export const BAR_RADIUS_CHART_OVERTURN_X = [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]
// 负数时根据 Y 轴翻转的图形
export const BAR_RADIUS_CHART_OVERTURN_Y = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_COMPOSITE]

// 无类目轴图形，tooltip的触发类型为item
export const TOOLTIP_TRIGGER_ITEM = [CHART_ALIAS_TYPE.VE_TREEMAP, CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_CALENDAR, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_GAUGE, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_WORDCLOUD, ...CIRCULAR_CHART, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

// 轴坐标图形
export const AXIS_COORDINATES_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_HEATMAP]

// 支持设置允许重叠图形
export const ALLOW_LABEL_OVERLAP_CHART = [CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_PIE, CHART_ALIAS_TYPE.VE_ROSE, CHART_ALIAS_TYPE.VE_RING, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_RADAR, CHART_ALIAS_TYPE.VE_LIQUIDFILL, CHART_ALIAS_TYPE.VE_SUNBURST, CHART_ALIAS_TYPE.VE_FUNNEL, CHART_ALIAS_TYPE.VE_BAR_PERCENT, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_MAP, CHART_ALIAS_TYPE.VE_ROUNDCASCADES, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// 支持设置辅助线参与刻度计算的图形
export const ALLOW_AUXILIARY_AXIS_TICK_CALC = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_BANDWIDTH]

// 支持设置预警线参与刻度计算的图形
export const ALLOW_ALERT_AXIS_TICK_CALC = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_PICTORIALBAR, CHART_ALIAS_TYPE.VE_BANDWIDTH]

// 支持按度量拆分显示的图形、支持同扩展维度值同颜色的图形
export const ALLOW_METRIC_SPLIT_DISPLAY_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_BAR_STACK,CHART_ALIAS_TYPE.VE_STACK_PERCCENT,CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT]

// 支持支持配置预警图标的图形
export const ALLOW_WARNING_ICON_CONFIG_CHART = [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY]

// echarts每种图形独特的配置函数
export const AFTERCONFIGFUNC = {
  bandwidthArr: ['bandwidthArea', 'bandwidthRank'],
  lineArr: ['labelPercentSetting', 'hideYaxias', 'lineNormal', 'commonHideYaxias', 'legendOffset', 'xAxisGeneralSetting', 'yAxisGeneralSetting', 'notCricleSetting'],
  histogramArr: ['labelPercentSetting', 'hideYaxias', 'handleAxisPointer', 'commonHideYaxias', 'legendOffset', 'xAxisGeneralSetting', 'yAxisGeneralSetting', 'notCricleSetting', 'setBarItemStyleOption', 'nestedHistogram'],
  barArr: ['labelPercentSetting', 'yAxisSettingForBar', 'handleAxisPointer', 'commonHideYaxias', 'xAxisGeneralSetting', 'notCricleSetting', 'setBarItemStyleOption', 'setLegendDimension'],
  pieArr: ['pieOffset', 'pieSparation', 'handRingConfig', 'combinePieSetting'],
  ringMultipleArr: ['ringMultipleSetting', 'pieOffset'],
  scatterArr: ['legendOffset', 'xAxisGeneralSetting', 'yAxisGeneralSetting', 'scatterOffset', 'notCricleSetting'],
  gaugeArr: ['settingGauge'],
  mapArr: ['mapSetting'],
  // setPieAreaPadding 只能放最后面，因为把水滴和雷达的半径，中心点都转成了固定px格式
  radar: ['randarSetting', 'setPieAreaPadding'],
  liquidFillArr: ['liquidFillSetting', 'pieOffset', 'pieSparation', 'handRingConfig', 'liquidFillNormalSetting', 'setPieAreaPadding'],
  sunburstArr: ['sunburstSetting'],
  treemapArr: ['settingTreemap'],
  funnelArr: ['funnelSetting'],
  wordcloudArr: ['wordCloudSetting'],
  themeRiverArr: ['themeRiverSetting', 'notCricleSetting'],
  barPercentArr: ['yAxisSettingForBar', 'handleAxisPointer', 'commonHideYaxias', 'xAxisGeneralSetting', 'barPercentSetting', 'notCricleSetting'],
  treeArr: ['treeSetting'],
  calendarArr: ['calendarSetting'],
  pictorialBarArr: ['labelPercentSetting', 'hideYaxias', 'pictorialBarSetting', 'handleAxisPointer', 'commonHideYaxias', 'xAxisGeneralSetting', 'yAxisGeneralSetting', 'notCricleSetting', 'setLegendDimension'],
  pictorialBarReverseAxisArr: ['pictorialBarSetting', 'handleAxisPointer', 'commonHideYaxias', 'xAxisGeneralSetting', 'yAxisSettingForBar', 'notCricleSetting'],
  satckBarArr: ['labelPercentSetting', 'yAxisSettingForBar', 'handleAxisPointer', 'commonHideYaxias', 'xAxisGeneralSetting', 'stackBarSetting', 'notCricleSetting', 'setBarItemStyleOption'],
  waterfallArr: ['waterfallSetting', 'handleAxisPointer', 'commonHideYaxias', 'legendOffset', 'xAxisGeneralSetting', 'yAxisGeneralSetting', 'notCricleSetting', 'setLegendDimension'],
  roundCascadesArr: ['pieOffset', 'roundCascadesSetting'],
  heatmapArr: []
}

export const createDimension = () => Object.assign({}, {
  // 字段名
  columnName: '',
  // 字段类型(string,number,date)
  columnType: '',
  // 别名
  alias: '',
  // 排序规则
  order: '',
})
export const createMeasure = () => Object.assign({}, {
  // 度量值
  columnName: '',
  // 字段类型(string,number,date)
  columnType: '',
  // 别名
  alias: '',
  // 函数类型
  aggType: '',
  compareInfo: {
    // dateField: "",//
    // dateType: "",//ChartCalendarTypeEnum   营运日历(...)，财务日历(...)
    // compSubType: ""//枚举类型AdvancedSubTypeEnum 增长率，增长值，对比值
  },
})
// 图表可配置项
export const CHART_DEFAULT_ALIAS = CHART_ALIAS_TYPE.VE_HISTOGRAM
export const chartUserConfig = {
  // 公共属性设置
  // 1. 标题名称 标题字体配置
  title: {
    text: '',
    textStyle: '',
  },
  // 2. 图表切换
  chartType: 've-histogram',
  // 子类型名称
  childChartAlias: '',
  chartAlias: CHART_DEFAULT_ALIAS,
  // 3. 图例位置设置
  legend: 'bottom',
  // 4. 备注
  chartDescription: '',
  // 5. 显示条目设置
  pagination: {
    pageSize: 20,
    rankOf: false,
    rankType: 'firstDimension',
  },
  // 6.图形上下左右各边的边距
  grid: {
    // x: 10,
    // y: 10,
    // x2: 10,
    // y2: 10,
  },
  // 图表排序
  ChartVeBarNormal: false,
  // 特有属性设置
  // 1. 各图表颜色设置 getChartColors()
  colors: [],
  // 2. 指标设置 (饼图)
  quota: false,
  // 3. 四象限配置 (散点图)
  quadrant: false,
  // 4. 色带 (散点图)
  colorBand: {
    x: {
      start: 0,
      end: 0,
    },
    y: {
      start: 0,
      end: 0
    }
  },
  showColorBand: false,
  // 5. 指标筛选器
  dimensionFilters: [],
  // 6. 堆叠图 + 折线图
  maxStack: 2,
  compositeChart: {
    stack: false,
    area: false,
  },
  // 7. 指标选择器
  choiceDimensions: [],
  // 8. 图表的度量轴
  metricsContainer: {},
  // 散点图气泡最大值
  symbolSizeMax: 50,
  // 圆形图 增长率
  growthRate: false,
  isMapGradients: false,
  // splitLine网线
  splitLine: false,
  dataZoomChecked: false,
  dataViewer: false,
  // 主题相关配置
  themeConfig: {
    'sdp-classic-white': {},
    'sdp-dark-blue': {},
    'sdp-deep-blue': {},
  }
}

const PIEDATA = {
  columns: ['日期', '访问用户'],
  rows: [
    { '日期': '1/1', '访问用户': 1393 },
    { '日期': '1/2', '访问用户': 3530 },
    { '日期': '1/3', '访问用户': 2923 },
    { '日期': '1/4', '访问用户': 1723 },
    { '日期': '1/5', '访问用户': 3792 },
    { '日期': '1/6', '访问用户': 4593 },
  ],
}

const CHARDATA = {
  // columns: ['日期', '访问用户', '下单用户', '下单率'],
  columns: [],
  rows: [
    // { '日期': '1/1', '访问用户': 1393, '下单用户': 1093, '下单率': 0.32 },
    // { '日期': '1/2', '访问用户': 3530, '下单用户': 3230, '下单率': 0.26 },
    // { '日期': '1/3', '访问用户': 2923, '下单用户': 2623, '下单率': 0.76 },
    // { '日期': '1/4', '访问用户': 1723, '下单用户': 1423, '下单率': 0.49 },
    // { '日期': '1/5', '访问用户': 3792, '下单用户': 3492, '下单率': 0.323 },
    // { '日期': '1/6', '访问用户': 4593, '下单用户': 4293, '下单率': 0.78 },
  ],
}

export const chartsList = [
  {
    elName: 've-histogram',
    alias: CHART_ALIAS_TYPE.VE_HISTOGRAM,
    cnName: i18n.t('sdp.views.histogram'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      // metrics: ['访问用户', '下单用户'],
      // dimension: ['日期'],
      metrics: [],
      dimension: [],
      showLine: [],
      stack: {},
      // xAxisType: 'value', //无法切换为value类型，对x轴数据排序
    },
    img: Imgs.VHistogram,
    // 分类
    category: 'histogram'
  },
  {
    elName: 've-histogram',
    alias: CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK,
    cnName: i18n.t('sdp.views.stackedHistogram'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      metrics: ['访问用户', '下单用户'],
      dimension: ['日期'],
      stack: { '用户': ['访问用户', '下单用户'] },
      // xAxisType: 'value',
    },
    img: Imgs.VHistogram,
    // 所属分类
    belong: 'histogram'
  },
  {
    elName: 've-line',
    alias: CHART_ALIAS_TYPE.VE_LINE,
    cnName: i18n.t('sdp.views.lineChart'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      metrics: ['访问用户', '下单用户'],
      dimension: ['日期'],
      // xAxisType: 'value',
      showLine: [],
      stack: {},
    },
    img: Imgs.vLine,
    // 分类
    category: 'line'
  },
  {
    elName: 've-line',
    alias: CHART_ALIAS_TYPE.VE_LINE_AREA,
    cnName: i18n.t('sdp.views.lineAreaChart'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      metrics: ['访问用户', '下单用户'],
      dimension: ['日期'],
      stack: { '用户': ['访问用户', '下单用户'] },
      area: true,
      showLine: [],
    },
    img: Imgs.vLineStack,
    belong: 'line',
  },
  {
    elName: 've-bar',
    alias: CHART_ALIAS_TYPE.VE_BAR,
    cnName: i18n.t('sdp.views.barGraph'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: ['日期'],
      metrics: ['访问用户'],
    },
    img: Imgs.VBar,
    category: 'bar'
  },
  {
    elName: 've-pie',
    alias: CHART_ALIAS_TYPE.VE_PIE,
    cnName: i18n.t('sdp.views.pieChart'),
    chartConfig: {},
    chartData: PIEDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
      dataOrder: {},
    },
    img: Imgs.VPie,
    category: 'pie',
  },
  {
    elName: 've-pie',
    alias: CHART_ALIAS_TYPE.VE_ROSE,
    cnName: i18n.t('sdp.views.roseChart'),
    chartData: PIEDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
      roseType: 'radius',
    },
    img: Imgs.VPieRoseType,
    belong: 'pie',
  },
  {
    elName: 've-ring',
    alias: CHART_ALIAS_TYPE.VE_RING,
    cnName: i18n.t('sdp.views.ringGraph'),
    chartData: PIEDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: Imgs.VRingRoseType,
    belong: 'pie',
  },
  {
    // 多圆环图的elName与百分比条形图的不能一样，否则百分比条形图切换到多圆环图会有问题
    elName: 've-ring',
    alias: CHART_ALIAS_TYPE.VE_RING_MULTIPLE,
    cnName: i18n.t('sdp.views.ringMultiple'),
    chartData: PIEDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: Imgs.VRingRoseType,
    belong: 'pie',
  },
  {
    elName: 've-scatter',
    alias: CHART_ALIAS_TYPE.VE_SCATTER,
    cnName: i18n.t('sdp.views.scatterDiagram'),
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
      xAxisType: 'value',
      symbolSizeMax: 50,
    },
    img: Imgs.VScatter,
  },
  {
    elName: 've-histogram',
    alias: CHART_ALIAS_TYPE.VE_COMPOSITE,
    cnName: i18n.t('sdp.views.combineGraphics'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      // axisSite: { right: ['下单用户'] },
      metrics: ['访问用户', '下单用户'],
      dimension: ['日期'],
      showLine: ['下单用户'],
      stack: { '用户': ['访问用户', '下单用户'] },
    },
    img: Imgs.VHistogramLine,
    belong: 'histogram'
  },
  {
    elName: 've-gauge',
    alias: CHART_ALIAS_TYPE.VE_GAUGE,
    cnName: i18n.t('sdp.views.dial'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      metrics: [],
      dimension: [],
    },
    children: [
      {
        typeName: 've-gauge-normal-type1',
      },
      {
        typeName: 've-gauge-normal-type2',
      },
      {
        typeName: 've-gauge-normal-type3',
      },
      {
        typeName: 've-gauge-normal-type4',
        isDefault: true,
      }
    ]
    // img: Imgs.VGAUGE,
  },
  {
    elName: 've-map',
    alias: CHART_ALIAS_TYPE.VE_MAP,
    cnName: i18n.t('sdp.views.map'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: [],
      metrics: [],
    },
    children: [
      {
        i18n: 'sdp.views.mapChina',
        typeName: 've-map-china',
        isDefault: true,
      },
      {
        i18n: 'sdp.views.mapWorld',
        typeName: 've-map-world',
      },
      {
        i18n: 'sdp.views.mapBritain',
        typeName: 've-map-britain',
      }
    ]
  },
  {
    elName: CHART_ALIAS_TYPE.VE_RADAR,
    alias: CHART_ALIAS_TYPE.VE_RADAR,
    cnName: i18n.t('sdp.views.radar'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: ['日期'],
      metrics: ['访问用户', '下单用户', '下单率'],
      dataType: { '下单率': 'percent' }
    },
    img: Imgs.Vradar,
  },
  {
    elName: 've-ring',
    alias: CHART_ALIAS_TYPE.VE_LIQUIDFILL,
    cnName: i18n.t('sdp.views.liquidfill'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: Imgs.VRingRoseType,
  },
  {
    elName: 've-pie',
    alias: CHART_ALIAS_TYPE.VE_SUNBURST,
    cnName: i18n.t('sdp.views.sunburst'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: CHART_ALIAS_TYPE.VE_TREEMAP,
    alias: CHART_ALIAS_TYPE.VE_TREEMAP,
    cnName: i18n.t('sdp.views.treemap'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: CHART_ALIAS_TYPE.VE_FUNNEL,
    alias: CHART_ALIAS_TYPE.VE_FUNNEL,
    cnName: i18n.t('sdp.views.funnel'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: 've-grid',
    alias: CHART_ALIAS_TYPE.VE_GRID,
    cnName: i18n.t('sdp.views.simpleGrid'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: [],
      metrics: [],
    },
    img: '',
  },
  {
    elName: CHART_ALIAS_TYPE.VE_WORDCLOUD,
    alias: CHART_ALIAS_TYPE.VE_WORDCLOUD,
    cnName: i18n.t('sdp.views.wordcloud'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: 've-bar',
    alias: CHART_ALIAS_TYPE.VE_BAR_PERCENT,
    cnName: i18n.t('sdp.views.barPercent'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
    belong: 'bar',
  },
  {
    elName: CHART_ALIAS_TYPE.VE_TREE,
    alias: CHART_ALIAS_TYPE.VE_TREE,
    cnName: i18n.t('sdp.views.tree'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  // 热力图
  {
    elName: CHART_ALIAS_TYPE.VE_BAR_HEATMAP,
    alias: CHART_ALIAS_TYPE.VE_BAR_HEATMAP,
    cnName: i18n.t('sdp.views.barHeatmap'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: CHART_ALIAS_TYPE.VE_THEMERIVER,
    alias: CHART_ALIAS_TYPE.VE_THEMERIVER,
    cnName: i18n.t('sdp.views.themeRiver'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: CHART_ALIAS_TYPE.VE_CALENDAR,
    alias: CHART_ALIAS_TYPE.VE_CALENDAR,
    cnName: i18n.t('sdp.views.calendar'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: 've-histogram',
    alias: CHART_ALIAS_TYPE.VE_PICTORIALBAR,
    cnName: i18n.t('sdp.views.pictorialbar'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
      showLine: [],
      stack: {},
    },
    img: '',
    belong: 'histogram'
  },
  {
    elName: 've-bar',
    alias: CHART_ALIAS_TYPE.VE_BAR_STACK,
    cnName: i18n.t('sdp.views.barStack'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
    belong: 'bar',
  },
  {
    elName: 've-histogram',
    alias: CHART_ALIAS_TYPE.VE_WATERFALL,
    cnName: i18n.t('sdp.views.waterfall'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
    belong: 'histogram'
  },
  {
    elName: 've-pie',
    alias: CHART_ALIAS_TYPE.VE_ROUNDCASCADES,
    cnName: i18n.t('sdp.views.roundCascades'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: 've-line',
    alias: CHART_ALIAS_TYPE.VE_BANDWIDTH,
    cnName: i18n.t('sdp.views.bandwidthReport'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
  {
    elName: 've-stack-percent',
    alias: CHART_ALIAS_TYPE.VE_STACK_PERCCENT,
    cnName: i18n.t('sdp.views.stackPercent'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
    belong: 'histogram'
  },
  {
    elName: 've-bar-stack-percent',
    alias: CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT,
    cnName: i18n.t('sdp.views.barStackPercent'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
    belong: 'bar',
  },
  {
    elName: CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
    alias: CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY,
    cnName: i18n.t('sdp.views.barButterfly'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
    belong: 'bar'
  },
  {
    elName: CHART_ALIAS_TYPE.VE_DECOMPOSITION,
    alias: CHART_ALIAS_TYPE.VE_DECOMPOSITION,
    cnName: i18n.t('sdp.views.decomposition'),
    chartConfig: {},
    chartData: CHARDATA,
    chartSettings: {
      dimension: '日期',
      metrics: '访问用户',
    },
    img: '',
  },
]

export const BARCHART_WARN_TYPES = { // 柱状图预警效果
  PURE: 'pure',
  LINEAR: 'linear'
}

export const STACK_ACC_TYPES = {
 STACK_ACC_MAP: new Map([['addup', 'ADDUP'], ['addup_rate', 'ADDUP_RATE']]), // 堆叠累加
 ADDUP: 'addup',
 ADDUP_RATE: 'addup_rate'
}

export const ANGLE_MAP = new Map([[90, [0, 0, 1, 0]], [180, [0, 0, 0, 1]], [270, [1, 0, 0, 0]], [0, [0, 1, 0, 0]]])
export const getComponentShowAxisTickValue = (element, axisType) => {
  const showComponentChart = {
    isXAxisValue: [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT],
    isYAxisValue0: [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.CHART_ALIAS_TYPE, CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY],
    // isYAxisValue1: [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_PICTORIALBAR],
    isYAxisValue1: [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY],
  }
  const chartAlias = element.content.alias
  // if (chartAlias === CHART_ALIAS_TYPE.VE_PICTORIALBAR && element.content.chartUserConfig?.pictorialBarSettings?.flipAxis) {
  //   showComponentChart.isXAxisValue.push(CHART_ALIAS_TYPE.VE_PICTORIALBAR)
  // } else {
  //   showComponentChart.isYAxisValue0.push(CHART_ALIAS_TYPE.VE_PICTORIALBAR)
  // }
  if (axisType === 'xAxis') {
    return showComponentChart.isXAxisValue.includes(chartAlias)
  } else if (axisType === 'yAxis-0') {
    return showComponentChart.isYAxisValue0.includes(chartAlias)
  } else if (axisType === 'yAxis-1') {
    return showComponentChart.isYAxisValue1.includes(chartAlias)
  }
  return false
}
export const getValueAxis = (element, axisType) => {
  // 暂时只有象形柱图 设置反转Y轴时 支持双X轴配置
  const axisMap = {
    isXAxisValue0: [CHART_ALIAS_TYPE.VE_BAR, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_BAR_STACK, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT],
    isXAxisValue1: [CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY],
    isYAxisValue0: [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_HISTOGRAM_STACK, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_COMPOSITE, CHART_ALIAS_TYPE.VE_WATERFALL, CHART_ALIAS_TYPE.VE_BANDWIDTH, CHART_ALIAS_TYPE.VE_STACK_PERCCENT],
    isYAxisValue1: [CHART_ALIAS_TYPE.VE_HISTOGRAM, CHART_ALIAS_TYPE.VE_LINE, CHART_ALIAS_TYPE.VE_LINE_AREA, CHART_ALIAS_TYPE.VE_COMPOSITE],
  }
  const chartAlias = element.content.alias
  if (chartAlias === CHART_ALIAS_TYPE.VE_PICTORIALBAR && element.content.chartUserConfig?.pictorialBarSettings?.flipAxis) {
    axisMap.isXAxisValue0.push(CHART_ALIAS_TYPE.VE_PICTORIALBAR)
    axisMap.isXAxisValue1.push(CHART_ALIAS_TYPE.VE_PICTORIALBAR)
  } else {
    axisMap.isYAxisValue0.push(CHART_ALIAS_TYPE.VE_PICTORIALBAR)
    axisMap.isYAxisValue1.push(CHART_ALIAS_TYPE.VE_PICTORIALBAR)
  }

  if (axisType === 'xAxis') {
    return axisMap.isXAxisValue0.includes(chartAlias)
  } else if (axisType === 'xAxis-1') {
    return axisMap.isXAxisValue1.includes(chartAlias)
  } else if (axisType === 'yAxis-0') {
    return axisMap.isYAxisValue0.includes(chartAlias)
  } else if (axisType === 'yAxis-1') {
    return axisMap.isYAxisValue1.includes(chartAlias)
  }
  return false
}

// 获得轴线对应的度量
export function getAxisValueMetricArray(element, params: any = {}) {
  let result = { metricShow: [], allMetric: [], dimensionShow: [], noSecondAxis: false }
  const { axisType, legendSelected, chartData, seriesData } = params
  if (!getValueAxis(element, axisType)) return result
  const { chartAlias, yAxis = [], showSaveLegendSelected, saveLegendSelected } = element.content.chartUserConfig
  const { dimensionExtendList, metricAllList, metricFirstList, metricSecondList, dimensionList } = this.vm.UserConfig
  const noYaxis2Label = yAxis.length === 2 && !yAxis[1].axisLabel.show
  let noSecond = (!metricSecondList.length || (!SUPPORT_DEPUTY_AXIS.includes(chartAlias)) || noYaxis2Label) && ![CHART_ALIAS_TYPE.VE_SCATTER, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY].includes(chartAlias)

  // 需要参与计算的度量列表
  let allMetric = []
  let dimensionShow = []
  let firstAllMetric = []
  let firstAxis = []
  let seconAllMetric = []
  let secondAxis = []

  let _legendSelected = legendSelected && Object.keys(legendSelected).length ? legendSelected : (LEGEND_SETTING_SAVE_CHART.includes(chartAlias) && showSaveLegendSelected && saveLegendSelected && Object.keys(saveLegendSelected).length ? saveLegendSelected : {})
  if (Object.keys(_legendSelected).length) {
    if (Object.keys(_legendSelected).every(k => !_legendSelected[k])) {
      _legendSelected = {}
    }
  }
  _legendSelected = this.vm.setLangLegendSelect(_legendSelected)

  if (dimensionExtendList.length) {
    if (!seriesData) return result

    let _seriesData = seriesData.map((s, i) => ({ ...s, index: i }))
    if (_legendSelected && Object.keys(_legendSelected).length) {
      allMetric = _seriesData.filter(s => !_legendSelected.hasOwnProperty(s.name) || _legendSelected[s.name])
    } else {
      allMetric = _seriesData
    }
    seconAllMetric = seriesData.filter(s => s.type === 'line').map((s, i) => ({ ...s, index: i })).filter(s => allMetric.find(a => a.name === s.name))
    secondAxis = seconAllMetric
    !secondAxis.length && (noSecond = true)
    firstAllMetric = noSecond ? allMetric : allMetric.filter(s => s.type === 'bar')
    firstAxis = firstAllMetric
  } else {
    metricAllList.forEach((metric, mi) => {
      if (metric.isDesensibilisation) return
      const metricItem = getMetricItem(metric, mi)
      if (chartAlias === CHART_ALIAS_TYPE.VE_SCATTER) {
        if ([...metricFirstList, ...metricSecondList].find(m => m.keyName === metricItem.keyName)) {
          allMetric.push(metricItem)
        }
      } else if (_legendSelected && Object.keys(_legendSelected).length) {
        if (!_legendSelected.hasOwnProperty(metric.lang_alias) || _legendSelected[metric.lang_alias]) {
          allMetric.push(metricItem)
        }
      } else {
        allMetric.push(metricItem)
      }
    })
    if (chartAlias === CHART_ALIAS_TYPE.VE_SCATTER) {
      result.dimensionName = `VIEWFORMAT_${dimensionList[0].alias || dimensionList[0].labeName}`
      if (_legendSelected && Object.keys(_legendSelected).length) {
        dimensionShow = Object.keys(_legendSelected).filter(key => _legendSelected[key])
      } else {
        if (Array.isArray(chartData.rows)) {
          dimensionShow = chartData.rows.map(rowData => rowData[result.dimensionName])
        } else {
          dimensionShow = Object.keys(chartData.rows)
        }
      }
    }
    seconAllMetric = [...metricSecondList]
    secondAxis = noSecond ? [] : allMetric.map(m => {
      const index = seconAllMetric.findIndex(s => s.keyName === m.keyName)
      return {
        ...m,
        originIndex: index !== -1 ? index : m.originIndex,
      }
    }).filter(metricItem => {
      return seconAllMetric.find(metric => metric.keyName === metricItem.keyName)
    })
    !secondAxis.length && (noSecond = true)
    firstAllMetric = noSecond ? allMetric : [...metricFirstList]
    firstAxis = noSecond ? allMetric : allMetric.filter(metricItem => {
      return firstAllMetric.find(metric => metric.keyName === metricItem.keyName)
    })
  }
  if (axisType === 'xAxis') {
    result.metricShow = firstAxis
    result.allMetric = firstAllMetric
  } else if (axisType === 'xAxis-1') {
    result.metricShow = secondAxis
    result.allMetric = seconAllMetric
  } else if (axisType === 'yAxis-0') {
    if (chartAlias === CHART_ALIAS_TYPE.VE_SCATTER) {
      result.metricShow = secondAxis
      result.allMetric = seconAllMetric
    } else {
      result.metricShow = firstAxis
      result.allMetric = firstAllMetric
    }
  } else if (axisType === 'yAxis-1') {
    result.metricShow = secondAxis
    result.allMetric = seconAllMetric
  }
  if (chartAlias === CHART_ALIAS_TYPE.VE_SCATTER) {
    result.dimensionShow = dimensionShow
  }
  if (!metricSecondList.length || noSecond) {
    result.noSecondAxis = true
  }
  if (metricSecondList.length && (SUPPORT_DEPUTY_AXIS.includes(chartAlias))) {
    result.secondMetric = dimensionExtendList.length
      ? allMetric.filter(m => metricSecondList.find(mi => mi.keyName === m.metricKeyName))
      : allMetric.filter(m => metricSecondList.find(mi => mi.keyName === m.keyName))
  }
  return result
  function getMetricItem(metricObj, mi) {
    return {
      ...metricObj,
      originIndex: mi,
    }
  }
}

export function getNumber(value) {
  return isNaN(value) ? 0 : Number(value)
}

// 获取自定义表达式中使用的全局参数
export function getGlobalParamId(metric, globalParamList) {
  let paramId = []
  if (!Array.isArray(metric)) return paramId
  metric.forEach((item) => {
    if (!item.exp) return
    globalParamList.forEach(id => {
      if (item.exp.toString().indexOf(id) !== -1 && !paramId.includes(id)) {
        paramId.push(id)
      }
    })
  })
  return paramId
}

// 自定义图标地图的svg中的path(echarts.symbol要用)
export const mapTypeSvgPath = {
  've-map-flag1': 'M14,1.5c-0.10000038146972656,-0.10000002384185791,-0.3000001907348633,-0.10000002384185791,-0.39999961853027344,-0.10000002384185791c-1,0.20000004768371582,-3.90000057220459,0.6999999284744263,-5.200000762939453,0C6.300000190734863,0.4000000059604645,4,0.8999999761581421,3,1.2000000476837158L3,0.5C3,0.20000000298023224,2.799999952316284,0,2.5,0S2,0.20000000298023224,2,0.5l0,15C2,15.800000190734863,2.200000047683716,16,2.5,16S3,15.800000190734863,3,15.5L3,9.800000190734863c0.7999999523162842,-0.3000001907348633,3.5999999046325684,-1,5.300000190734863,0c0.8000001907348633,0.5,1.6999998092651367,0.6999998092651367,2.59999942779541,0.6999998092651367c1.6000003814697266,0,3,-0.6000003814697266,3,-0.6999998092651367c0.20000076293945312,-0.10000038146972656,0.3000001907348633,-0.3000001907348633,0.3000001907348633,-0.40000057220458984L14.199999809265137,1.899999976158142C14.199999809265137,1.7999999523162842,14.100000381469727,1.600000023841858,14,1.5Z',
  've-map-flag2': 'M15.300000190734863,10.199999809265137L14,5.5l1.1999998092651367,-3.200000047683716c0,-0.09999990463256836,0.10000038146972656,-0.20000004768371582,0.10000038146972656,-0.2999999523162842c0,-0.6000000238418579,-0.40000057220458984,-1,-1,-1L2,1L2,0.5C2,0.20000000298023224,1.7999999523162842,0,1.5,0S1,0.20000000298023224,1,0.5L1,1l0,10.5L1,15c0,0.3000001907348633,0.20000004768371582,0.5,0.5,0.5S2,15.300000190734863,2,15l0,-3.5l12.300000190734863,0c0.09999942779541016,0,0.19999980926513672,0,0.3000001907348633,0c0.2999992370605469,-0.10000038146972656,0.5,-0.19999980926513672,0.5999994277954102,-0.5C15.300000190734863,10.800000190734863,15.399999618530273,10.5,15.300000190734863,10.199999809265137ZM2,10.5L2,2l12.300000190734863,0l-1.1999998092651367,3.1999998092651367c-0.10000038146972656,0.20000028610229492,-0.10000038146972656,0.40000009536743164,0,0.6000003814697266l1.2999992370605469,4.699999809265137L2,10.5Z',
  've-map-needle1': 'M13,5c0,-2.799999952316284,-2.1999998092651367,-5,-5,-5S3,2.200000047683716,3,5c0,2.5999999046325684,2,4.699999809265137,4.5,4.899999618530273L7.5,16l1,0L8.5,9.899999618530273C11,9.699999809265137,13,7.599999904632568,13,5ZM8,7C6.900000095367432,7,6,6.099999904632568,6,5s0.9000000953674316,-2,2,-2c1.1000003814697266,0,2,0.9000000953674316,2,2S9.100000381469727,7,8,7Z',
  've-map-needle2': 'M13,4.5C13,2,11,0,8.5,0C8.399999618530273,0,8.300000190734863,0,8.300000190734863,0C8.199999809265137,0,8.100000381469727,0,8,0C5.199999809265137,0,3,2.200000047683716,3,5c0,2.5999999046325684,2,4.699999809265137,4.5,4.899999618530273L7.5,16l1,0L8.5,9.899999618530273C11,9.699999809265137,13,7.599999904632568,13,5c0,-0.09999990463256836,0,-0.19999980926513672,0,-0.3000001907348633C13,4.699999809265137,13,4.599999904632568,13,4.5Z',
  've-map-locations': 'M12.199999809265137,1.899999976158142C11.100000381469727,0.800000011920929,9.600000381469727,0.10000000149011612,8,0.10000000149011612S4.900000095367432,0.800000011920929,3.799999952316284,1.899999976158142S2,4.5,2,6.099999904632568c0,2.200000286102295,1.7999999523162842,5.299999713897705,5.599999904632568,9.400000095367432L8,16l0.39999961853027344,-0.39999961853027344c3.8000001907348633,-4.200000762939453,5.600000381469727,-7.300000190734863,5.600000381469727,-9.40000057220459C14,4.5,13.399999618530273,3,12.199999809265137,1.899999976158142ZM8,14.5C4.699999809265137,10.800000190734863,3,8,3,6.099999904632568c0,-1.299999713897705,0.5,-2.5999999046325684,1.5,-3.5S6.699999809265137,1.100000023841858,8,1.100000023841858c1.3000001907348633,0,2.6000003814697266,0.5,3.5,1.4999998807907104C12.5,3.5999999046325684,13,4.800000190734863,13,6.099999904632568C13,8,11.300000190734863,10.800000190734863,8,14.5Z M5,6C5,4.342541217803955,6.342541217803955,3,8,3C9.657458305358887,3,11,4.342541217803955,11,6C11,7.657458782196045,9.657458305358887,9,8,9C6.342541217803955,9,5,7.657458782196045,5,6Z',
  've-map-pin': 'M12.800000190734863,3L14,2L14,0L2,0L2,2L3.200000047683716,3L1,5.5L1,7L7.5,7L7.5,16L8.5,16L8.5,7L15,7L15,5.5Z',
  've-map-taper': 'M8,0L1,2L8,16L15,2Z',
  've-map-star': 'M15.5,6.300000190734863c0,-0.3000001907348633,-0.10000038146972656,-0.5,-0.39999961853027344,-0.6000003814697266L10.5,5l-2,-4.199999988079071c0,-0.10000002384185791,-0.10000038146972656,-0.19999998807907104,-0.19999980926513672,-0.19999998807907104C8,0.6000000238418579,7.699999809265137,0.699999988079071,7.599999904632568,0.8999999761581421l-2,4.199999928474426L0.8999999761581421,5.699999809265137c-0.09999996423721313,0,-0.19999998807907104,0.10000038146972656,-0.2999999523162842,0.10000038146972656C0.4000000059604645,6,0.4000000059604645,6.300000190734863,0.6000000238418579,6.5L4,9.800000190734863l-0.7999999523162842,4.59999942779541c0,0.10000038146972656,0,0.20000076293945312,0.09999990463256836,0.3000001907348633C3.4000000953674316,14.899999618530273,3.700000047683716,15,4,14.899999618530273l4,-2.09999942779541l4.100000381469727,2.1999998092651367c0.09999942779541016,0.10000038146972656,0.19999980926513672,0.10000038146972656,0.2999992370605469,0.10000038146972656c0.3000001907348633,0,0.5,-0.3000001907348633,0.40000057220458984,-0.6000003814697266L12,9.800000190734863l3.3000001907348633,-3.200000286102295C15.399999618530273,6.5,15.5,6.400000095367432,15.5,6.300000190734863Z',
}

export enum DATE_DIMENSION_TYPE {
  Day_vs_day = 'day_vs_day',
  Day_vs_day_d = 'day_vs_day_dayofmonth'
}

// 全局自定义色系类型
export const GLOBAL_CHART_CUSTOM_SCHEME = 'custom'

// 图形刷新来源
export const CHART_REFRESH_SOURCES = {
  // 不触发动画效果的刷新图形动作
  NO_ANIMATION: 'no-animation'
}

// 修改OPTION避免出现动画效果
export const removeChartRefreshAnimation = (source: string, options: any) => {
  // 只有不需要动画的时候才移除
  if (![CHART_REFRESH_SOURCES.NO_ANIMATION].includes(source)) return
  if (options.animation) options.animation = false
  if (options.layoutAnimation) options.layoutAnimation = false
  ;(options?.series || []).forEach(serie => {
    if (serie.animation) serie.animation = false
    if (serie.layoutAnimation) serie.layoutAnimation = false
  })
}

export const isNormalLiquidfill = (content) => {
  if (content.alias === 've-liquidfill' && content.chartUserConfig?.liquidFillSetting?.mode === 'normal') {
    return true
  }
  return false
}

export enum PROPORTION_BASIS_TYPE {
  ROOT,
  UP,
  MAX,
}

export enum SOURCE_DATA_TYPE {
  dataset = 1,
  indexFlag
}
