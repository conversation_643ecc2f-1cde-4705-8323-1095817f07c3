import { settingSlide, removeSlide } from 'packages/assets/utils/methodParams'
import { SCROLL_BAR_CHART, DATAZOOM_CHART } from './constant'
import CHART_LIST from './chartDesign/chartDesignClass/ChartClassList'
import * as echarts from 'echarts'
import 'echarts-liquidfill'
import 'echarts-wordcloud'

export default {
  inject: {
    commonData: { default: {} },
    utils: { default: {} },
    getActiveElement: { default: () => () => {} },
    configs: { default: () => false },
    isApp: { default: false },
    themeData: { default: {} },
  },
  props: {
    elName: {
      type: String,
      default: 've-line',
    },
    className: {
      type: Array,
      // popupClickOutsideClassName一定要有
      default: () => (['widget-chart', 'chartClickOutsideClass']),
    },
    chartProps: {
      type: Object,
      default: () => ({}),
    },
    parent: {
      type: Object,
    },
    themeType: {
      type: String,
    },
    element: {
      type: Object,
      default: () => ({}),
    },
    canvasClassName: {
      type: String,
    },
    chartClassName: {
      type: String,
    },
    isChartSet: {
      type: Boolean,
      default: false,
    },
    chartUserConfig: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      displayArr: ['ve-treemap', 've-themeRiver', 've-calendar'],
      echartsEventList: ['legendinverseselect', 'click', 'brush', 'legendScroll', 'legendselectchanged', 'brushselected', 'georoam', 'legendselectall', 'finished', 'datazoom'],
      isSetDevicePixelRatio: false, // 是否提升图形清晰度
      canvasStyle: {
        position: 'relative',
        cursor: 'pointer',
      },
      bindEventTimer: null,
    }
  },
  render(h) {
    // const dom = h('div', {
    //   class: this.className,
    //   ref: this.elName,
    //   props: this.chartProps,
    // })
    // if (this.themeData.themeFullScreen && !this.isSetDevicePixelRatio) {
    //   const initOptions = { devicePixelRatio: Math.ceil(this.element.scale), }
    //   this.drawChart(initOptions)
    //   this.isSetDevicePixelRatio = true
    // }
    return h('div', {
      class: this.className,
      ref: this.elName,
      props: this.chartProps,
      style: {
        ...this.canvasStyle,
        ...(this.element?.content?.chartUserConfig?.chartAlias === 've-map-parent' ? { width: '100%', height: '100%' }: {})
      },
    })
  },
  watch: {
    'themeData.themeFullScreen': {
      handler(val) {
        if (!val) {
          this.isSetDevicePixelRatio = false
        }
      },
      immediate: true,
    },
    // 'canvasSize.height': {
    //   handler() {
    //     if (this.isMobile) {
    //       this.$emit('refreshChart')
    //     }
    //   },
    //   immediate: true,
    // },
  },
  computed: {
    isMobile() {
      return this.utils.isMobile
    },
    chartDom() {
      return document.getElementsByClassName(this.chartClassName)[0]
    },
    canvasDom() {
      return document.getElementsByClassName(this.canvasClassName)[0]
    },
    canvasSize() {
      return this.canvasDom ? { width: this.canvasDom.offsetWidth, height: this.canvasDom.offsetHeight } : {}
    },
    needClick() {
      return !this.isChartSet && this.isMobile && this.elName === 've-themeRiver'
    },
    needDrag() {
      const { dataZoomChecked, chartAlias } = this.chartUserConfig
      const hasDataZoom = dataZoomChecked && !this.isMobile && DATAZOOM_CHART.includes(chartAlias)
      return !this.isChartSet && !this.isMobile &&
      (hasDataZoom || this.chartProps.toolbox.show || this.elName === 've-map' || this.chartUserConfig.scrollBarShow)
    },
    needScroll() {
      // 需要滚动条的图形类型
      const { chartAlias, scrollBarShow = false } = this.chartUserConfig
      return !this.isMobile && SCROLL_BAR_CHART.includes(chartAlias) && scrollBarShow
    },
    scrollEvent() {
      // firfox支持的是另外一种事件 DOMMouseScroll
      return navigator.userAgent.indexOf('Firefox') !== -1 ? 'DOMMouseScroll' : 'mousewheel'
    }
  },
  mounted() {
    // 组件更新的时候，如果是移动看板，绑定手势
    if (this.isMobile) {
      settingSlide.call(this, this.chartClassName, this.chartDom, this.chartSlide)
    }
    this.drawChart({ devicePixelRatio: this.element.scale })
    this.bindDragEvent()
    this.drawChart = this.$_debounce(this.drawChart, 100)
    this.bindEventTimer = setInterval(() => {
      this.bindDragEvent()
    }, 500)
    this.$once('hook:destroyed', () => {
      clearInterval(this.bindEventTimer)
    })
  },
  methods: {
    drawChart(initOptions) {
      const { devicePixelRatio = 2 } = initOptions
      // TODO 需要根据initOptions设置devicePixelRatio
      const myChart = echarts.init(this.$el, undefined, { devicePixelRatio: Math.max(devicePixelRatio, 2) })
      const injectData = {
        themeType: this.themeType,
        isChartSet: this.isChartSet,
        isMobile: this.isMobile,
      }
      const params = {
        element: this.element,
        injectData,
        echartsInstance: myChart,
        vm: this.parent,
      }
      const chartInstance = CHART_LIST[this.chartUserConfig.chartAlias] ? new CHART_LIST[this.chartUserConfig.chartAlias](params) : new CHART_LIST.default(params)
      this.parent.chartInstance = chartInstance
      this.parent.echartInstance = myChart
      myChart.clear()
      this.$emit('refreshChart', { source: 'drawChart' })
      // 图形交互
      this.echartsEventList.forEach(eventName => {
        // 地图比例和位置只在编辑的时候获取
        if (eventName === 'georoam' && !this.isChartSet) return
        myChart.on(eventName, this.chartEventListeners.bind(this, eventName))
      })
    },
    chartEventListeners(eventName, e) {
      this.$emit('trigger-chart-events', eventName, e)
    },
    // 图形滑动的事件
    chartSlide(gesture) {
      switch (gesture) {
        case 'left':
          this.$emit('slide-left')
          break
        case 'right':
          this.$emit('slide-right')
          break
        case 'top':
          this.$emit('slide-top')
          break
        case 'down':
          this.$emit('slide-down')
          break
        // 暂不处理的手势，包括未滑动，上滑和下滑
        default:
          break
      }
    },
    listenerTouchend(event) {
      event.preventDefault()
      if (this.isChartSet || this.configs) return
      let { offsetX, offsetY } = event
      if (!this.commonData.isPreview) {
        let hightLightChart = this.getActiveElement()
        if (hightLightChart.id !== this.element.id) return
      }
      const react = this.$_getRect(this.$el)
      const grid = this.chartProps.grid
      if (event.type === 'touchend') {
        offsetX = event.changedTouches[0].pageX - react.left
        offsetY = event.changedTouches[0].pageY - react.top
      }
      if (offsetX > 0 && offsetX < react.width && offsetY > grid.top && offsetY < react.height - grid.bottom) {
        const eventData = {
          type: 'click',
          componentType: this.elName
        }
        this.$emit('chart-click', eventData)
      }
    },
    startDrag() {
      this.$el.firstElementChild.firstElementChild.addEventListener('mousemove', this.$_debounce(this.getDrag))
    },
    getDrag() {
      const _cursor = this.$el.firstElementChild.firstElementChild?.style?.cursor
      const arr = ['ns-resize', 'ew-resize', 'crosshair', 'move', 'pointer']
      const key = arr.includes(_cursor) ? 'add' : 'remove'
      this.$el.classList[key]('sdp-grid-item-drag-ignore')
    },
    removeDrag() {
      this.$el.classList.remove('sdp-grid-item-drag-ignore')
      this.$el && this.$el.removeEventListener('mousemove', this.getDrag)
    },
    addScrollEvent() {
      const canvas = this.$el.firstElementChild.firstElementChild
      canvas.addEventListener(this.scrollEvent, this.chartScroll, false)
    },
    removeScrollEvent() {
      const canvas = this.$el.firstElementChild.firstElementChild
      canvas.addEventListener(this.scrollEvent, this.chartScroll, false)
    },
    // 图形滚动事件
    chartScroll(e) {
      e.preventDefault()
      //
      var wheel = e.wheelDelta || -e.detail
      var delta = Math.max(-1, Math.min(1, wheel))
      if (delta < 0) {
        // 向下滚动
        this.$emit('scroll-down')
      } else {
        // 向上滚动
        this.$emit('scroll-up')
      }
    },
    bindDragEvent() {
      setTimeout(() => {
        // 判断canvas是否需要绑定事件，消除编辑看板时，元素拖拽对datazoom,以及散点图的面积色带的影响
        const dom = this.$el?.firstElementChild?.firstElementChild
        if (!dom) return
        clearInterval(this.bindEventTimer)
        if (this.needDrag) {
          dom.addEventListener('mouseenter', this.startDrag)
          dom.addEventListener('mouseleave', this.removeDrag)
          this.$once('hook:destroyed', () => {
            dom.removeEventListener('mouseenter', this.getDrag)
            dom.removeEventListener('mouseleave', this.removeDrag)
          })
        }
        if (this.needScroll) {
          // 绑定滚动事件
          dom.addEventListener('mouseenter', this.addScrollEvent)
          dom.addEventListener('mouseleave', this.removeScrollEvent)
          this.$once('hook:destroyed', () => {
            dom.removeEventListener('mouseenter', this.addScrollEvent)
            dom.removeEventListener('mouseleave', this.removeScrollEvent)
          })
        }
        if (this.needClick) {
          // 绑定点击事件
          const isTouchSupported = 'ontouchstart' in window
          !isTouchSupported && dom.addEventListener('click', this.listenerTouchend, true)
          this.$once('hook:destroyed', () => {
            !isTouchSupported && dom.removeEventListener('click', this.listenerTouchend, true)
          })
        }
      }, 200)
    },
  },
  updated() {
    this.bindDragEvent()
  },
  destroyed() {
    // 组件销毁的时候, 移除手势的事件
    if (this.isMobile) {
      removeSlide(this.chartClassName)
    }
    // const dom = this.$el
    // if (this.needDrag) {
    //   dom.removeEventListener('mouseenter', this.getDrag)
    //   dom.removeEventListener('mouseleave', this.removeDrag)
    // }
    // if (this.needScroll) {
    //   dom.removeEventListener('mouseenter', this.addScrollEvent)
    //   dom.removeEventListener('mouseleave', this.removeScrollEvent)
    // }
    // if (this.needClick) {
    //   // 绑定点击事件
    //   dom.removeEventListener('click', this.listenerTouchend)
    // }
  },
}
