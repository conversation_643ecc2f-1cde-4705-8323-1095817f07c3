<template>
  <div v-if="showChartIndicatorPC && selectShow && chioceTabList.length > 1" class="choice-tab">
    <template v-if="isIndicatorDropDown">
      <ChartSelect
        @focus="popoverClose"
        ref="choiceSelect"
        :popper-append-to-body="notAppendToBody && (!$getSystemConfig || !$getSystemConfig('onlyIndex'))"
        v-model="checkedDimension"
        :disabled="!isBoardRequestFinish"
        @change="$emit('choose-dimension', checkedDimension, 'outerClick')"
        :popper-class="'preview-select ' + selectClassName"
        :style="changeFontStyle()"
        :class="['dimension-selector', 'sdp-params-theme-element-top-background', 'pc-selector']">
        <el-option
          v-for="(dimension) of chioceTabList"
          :key="dimension.index"
          :label="dimension.name || dimension.id"
          :value="dimension.index"
        >
        </el-option>
      </ChartSelect>
    </template>

    <div v-else class="choice-tabs">
      <div
        v-for="(dimension) of chioceTabList"
        class="chioce-tab-item"
        :title="dimension.name || dimension.id"
        @click="$emit('handle-choice-tab', dimension.index)"
        :class="{ 'active': checkedDimension === dimension.index }"
        :style="changeFontStyle(checkedDimension === dimension.index)"
        :key="dimension.index">
        {{dimension.name || dimension.id}}
      </div>
    </div>
  </div>
</template>

<script>
import { ChartSelect } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
export default {
  name: 'ChoiceTab',
  inject: ['themeData'],
  components: { ChartSelect },
  props: {
    notAppendToBody: {
      type: Boolean,
    },
    chioceTabList: {
      type: Array,
      default: () => []
    },
    element: {
      type: Object,
      default: () => ({}),
    },
    isBoardRequestFinish: {
      default: true,
    },
    showChartIndicatorPC: {
      default: false,
    },
    selectShow: {
      default: false,
    },
    selectClassName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
    }
  },
  computed: {
    checkedDimension: {
      get() {
        return this.element.content.saveIndex || 0
      },
      set(val) {
        this.element.content.saveIndex = val
      }
    },
    isIndicatorDropDown() {
      return !this.content.indicatorSelectorShowType || this.content.indicatorSelectorShowType === 'dropDown'
    },
    content() {
      return this.element.content
    },
  },
  methods: {
    popoverClose() {
      this.$emit('popoverClose')
    },
    // 设置指标选择器文字样式
    changeFontStyle(isActive) {
      if (typeof this.$parent?.getIndicatorStyle === 'function') {
        return this.$parent.getIndicatorStyle('indicatorSelectorTextStyle', isActive)
      }
    },
  }
}
</script>
<style lang="scss" scoped>
$FontFamily: var(--chioce-font-family);
$FontWeight: var(--chioce-font-weight);
$FontColor: var(--chioce-color);
$TextDecoration: var(--chioce-text-decoration);
.choice-tab {
  .sdp-params-theme-element-top-background {
    background: transparent !important;
    /deep/ .el-input input, {
      background: transparent !important;
      font-family: $FontFamily !important;
      font-weight: $FontWeight !important;
      color: $FontColor !important;
      text-decoration: $TextDecoration !important;
    }
  }
}
</style>
