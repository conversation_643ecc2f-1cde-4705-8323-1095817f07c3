import {
  HAS_TARGET_CHART,
  STACK_ACC_CHART,
  EXTEND_DIMENSION_CHART,
  CHART_ALIAS_TYPE,
  VE_BANDWIDTH_TYPE,
} from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { OnlyField } from 'packages/base/board/displayPanel/datasetReplace'
import { getAggType, setLgeTypeValue } from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'

function formatField(fieldItem, params = {}) {
  return {
    ...fieldItem,
    columnType: fieldItem.columnTpe,
    columnName: fieldItem.labeName,
    alias: fieldItem.alias || fieldItem.labeName,
    order: !fieldItem.order || fieldItem.order === 'none' ? '' : fieldItem.order,
    orderList: fieldItem.orderList,
    keyName: fieldItem.keyName,
    dataSetId: fieldItem.dataSetId || fieldItem.parentId,
    webFieldType: fieldItem.webFieldType,
    id: fieldItem.id,
    ...params,
    metricGroupInfo: {
      parentId: fieldItem.parentId,
      id: fieldItem.id,
      customFieldName: fieldItem.alias,
      webFieldFrom: fieldItem.webFieldFrom,
    },
  }
}
// 扩展维度
function layersExtendDimension(elContent) {
  const { chartUserConfig = {}, drillSettings = {} } = elContent
  if (chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_WORDCLOUD) {
    // 词云图的色系是用扩展维度实现的
    if (!chartUserConfig.wordCloudSetting.colorFieldKey) return []
    const field = chartUserConfig.wordCloudSetting.colorSeriesField || {
      columnName: chartUserConfig.wordCloudSetting.colorSeriesProp,
      columnType: 'string',
      dataSetId: drillSettings.dataSetId,
    }
    return [field]
  }
  if (!EXTEND_DIMENSION_CHART.includes(chartUserConfig.chartAlias) || !chartUserConfig.extendDimensionList?.length) return []
  return chartUserConfig.extendDimensionList.map(e => formatField(e))
}
// 带宽
function layersBandwidthReport(elContent) {
  const { chartUserConfig = {}, } = elContent
  if (chartUserConfig.chartAlias !== CHART_ALIAS_TYPE.VE_BANDWIDTH ||
      chartUserConfig.bandwidthMode !== VE_BANDWIDTH_TYPE.AUTOMATIC_BANDWIDTH ||
      !chartUserConfig.bandwidthData?.bandwidthList?.length
    ) return {}
  return formatField(chartUserConfig.bandwidthData.bandwidthList[0], { rankOrder: 'desc' })
}
// 度量
function layersMetrics(elContent) {
  const { chartAlias, metricsContainer = {}, mergeOthers = {} } = elContent.chartUserConfig
  let layersMetric = elContent.drillSettings.layers[0].metrics.filter(m => !m.isGridCustom && !m.isBandWidth).map(m => ({ ...m, ...fieldHelper(m), }))
  const basicMetrics = (metricsContainer.default || []).filter(m => !m.isGridCustom).map(m => formatField(m, fieldHelper(m)))

  function bandWidth() {
    // 带宽图
    if (chartAlias !== CHART_ALIAS_TYPE.VE_BANDWIDTH) return
    const { bandwidthData: { maxBandwidthList, minBandwidthList, }, bandwidthMode } = elContent.chartUserConfig
    if (bandwidthMode === VE_BANDWIDTH_TYPE.CUSTOM_BANDWIDTH && maxBandwidthList?.length && minBandwidthList?.length) {
      let minAndMaxBandWidth = [
        formatField(minBandwidthList[0], { ...fieldHelper(minBandwidthList[0]), bandwidthReport: 'min', isBandWidth: true }),
        formatField(maxBandwidthList[0], { ...fieldHelper(maxBandwidthList[0]), bandwidthReport: 'max', isBandWidth: true }),
      ]
      layersMetric = [basicMetrics[0]].concat(minAndMaxBandWidth)
    }
  }
  function waterfall() {
    // 瀑布图
    if (chartAlias !== CHART_ALIAS_TYPE.VE_WATERFALL) return
    const { waterfallSetting: { contrast, }, contrastList, dimensionList } = elContent.chartUserConfig
    if (contrast?.show && dimensionList?.length && contrastList?.length) {
      let contrast = [
        formatField(contrastList[0], { ...fieldHelper(contrastList[0]), contrastValue: true }),
      ]
      layersMetric = [basicMetrics[0]].concat(contrast)
    }
  }
  function mapParent() {
    // 地图  todo: 之后单度量图形的metrics都在这个方法里面处理
    if (chartAlias !== CHART_ALIAS_TYPE.VE_MAP) return
    layersMetric = [basicMetrics[0]]
  }
  function fieldHelper(field: any = {}) {
    let result: any = {}
    // 堆叠累加
    if (!STACK_ACC_CHART.includes(chartAlias)) result.addup = ''
    result.aggType = getAggType(field)
    if (field.aggType === 'CONTRAST') {
      field.selectedConttast && (result.compareInfo = { compSubType: field.selectedConttast })
      field.selectedConttastMode && (result.compareRule = field.selectedConttastMode)
    }
    if (mergeOthers?.isMerge) {
      result.percentValue = true
    }
    if (chartAlias === CHART_ALIAS_TYPE.VE_ROUNDCASCADES) {
      // 层叠圆形图默认降序
      result.order = 'desc'
    }
    setLgeTypeValue(result, field)
    return result
  }
  bandWidth()
  waterfall()
  mapParent() // 之后单度量图形的metrics都在这个方法里面处理
  return layersMetric.map(item => {
    return {
      ...item,
      dataSetId: item.parentId, // 指标字段后端需要用到 dataSetId
    }
  })
}

// 维度
function layersDimension(elContent) {
  let layersDimensions = elContent.drillSettings.layers[0].dimension.map(d => {
    d.dataSetId = elContent.drillSettings.dataSetId
    return d
  })
  return layersDimensions
  // let layersDimensions = elContent.drillSettings.layers[0].dimension.map(d => {
  //   d.parentId = elContent.drillSettings.dataSetId
  //   return d
  // })
  // return layersDimensions
}

function layersLatiAndLongitude(elContent) {
  const { chartAlias, longitudeList = [], latitudeList = [], mapMode, } = elContent.chartUserConfig
  if (chartAlias !== 've-map-parent') return []
  if (longitudeList.length && latitudeList.length && mapMode === 'longitudeAndLatitude') {
    return [
      Object.assign({}, longitudeList[0], {
        columnName: longitudeList[0].labeName,
        metricGroupInfo: this.getMetricGroupInfo(longitudeList[0]),
      }),
      Object.assign({}, latitudeList[0], {
        columnName: latitudeList[0].labeName,
        metricGroupInfo: this.getMetricGroupInfo(latitudeList[0]),
      })
    ]
  }
}

const factors = (elContent) => {
  const { drillSettings = {} } = elContent
  return drillSettings?.layers?.[0]?.factors?.map(f => {
    return {
      ...f,
      dataSetId: f.parentId,
    }
  })
}

const customMetrics = (elContent) => {
  const { drillSettings = {} } = elContent
  return drillSettings?.layers?.[0]?.customMetrics?.map(m => {
    return {
      ...m,
      dataSetId: m.parentId,
    }
  })
}

const auxLineMetrics = (elContent) => {
  const { drillSettings = {} } = elContent
  return drillSettings?.layers?.[0]?.auxLineMetrics?.map(m => {
    return {
      ...m,
      dataSetId: m.parentId,
    }
  })
}

const customAuxLineMetrics = (elContent) => {
  const { drillSettings = {} } = elContent
  return drillSettings?.layers?.[0]?.customAuxLineMetrics?.map(m => {
    return {
      ...m,
      dataSetId: m.parentId,
    }
  })
}

const fieldFuncMap = {
  extendDimension: layersExtendDimension,
  bandwidthReport: layersBandwidthReport,
  metrics: layersMetrics,
  latiAndLongitude: layersLatiAndLongitude,
  dimension: layersDimension,

  factors,
  customMetrics,
  auxLineMetrics,
  customAuxLineMetrics,
  // gaugeTarget: layersGaugeTarget,
}

export default function(layers, element) {
  Object.keys(fieldFuncMap).forEach(k => {
    layers[k] = fieldFuncMap[k].call(this, element.content)
  })
}
