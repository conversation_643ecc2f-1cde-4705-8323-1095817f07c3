<template>
  <div
    class="element-chart"
    :class="{
      'sdp-grid-item-drag-ignore': isMobile,
      'element-chart-mobile-preview': isMobile && !isChartSet,
    }"
    :key="commonData.isPreview ? aliasDict.dictKey || 1 : 1"
    :style="{...containerPadding.chart, ...overflowStyle}"
    @mouseenter="mouseenterHandler"
    @mouseleave="mouseleaveHandler"
    @mousemove="mouseenterHandler"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    v-mousemove="{
        disabled: utils.isScreen ? !isChartSet : true,
      }"
  >
    <!-- <ElementSlot :visible="utils.isMobile" top="0px" right="0px" >
      <slot></slot>
    </ElementSlot> -->
    <!-- 占位文本 -->
    <div v-if="!hasChartData" :class="chartClassName" class="text">
      <div class="placeholder-text">{{ commonData.isPreview ? '' : $t('sdp.views.specChart') }}</div>
      <waiting v-if="!loading" :isLoading="commonData.isPreview && isRenderInteraction"/>
    </div>
    <template v-if="!showNone">
      <!-- 设置数据权限图表 -->

      <div v-if="hasChartData && noDataPermissions" class="no-data-permissions widget">
        <!-- 标题 -->
        <div
          v-clamp="chartClamp"
          :style="titleStyle"
          :class="[isMobile ? '' : 'title-nowrap', superLinkTitleClick ? 'chart-pointer' : '', `export-title-${element.id}`]"
          :title="chartUserConfig.title.text"
          @click="titleClick"
        >
          <font
            :class="{
              'gradient' : checkIsGradient(titleStyle)
            }"
            :style="gradientColorStyle"
          >{{chartUserConfig.title.text}}</font>
        </div>

         <!-- 纯数据导出 -->
         <i v-if="isTableDataExport" class="icon-sdp-xiazaiexcel title-right" style="font-size: 20px;cursor: pointer; color: #777777" @click="handleExportTableData"></i>
        <!-- 指标选择器 -->
        <div style="float: right">
          <div style="display: flex;justify-content: flex-end;gap:10px">
            <!-- 这里放一个插槽 isNoData-->
            <template v-if="isMobile">
               <slot></slot>
            </template>
            <WarningBell @eventBus="eventBus" v-if="selectShow && (!(!isIndicatorDropDown && (fixAddress || warnAddress)) || isIndicatorDropDownTitltShow)" v-bind="{isChartSet, isIndicatorDropDown: !isIndicatorDropDown, themeFullScreen, fullscreen, isShowChartWarning, chartUserConfig, selectShow, isMobile, warnAppendToBody, element, contentItem: chioceTab.length ? chioceTab[checkedDimension] : null, isWarning}"></WarningBell>
            <template v-if="showChartIndicatorPC && selectShow">
              <ChoiceTab
              v-bind="{notAppendToBody, chioceTabList, element, isBoardRequestFinish, selectClassName, showChartIndicatorPC, selectShow}"
              @choose-dimension="chooseDimension"
              @handle-choice-tab="handleChoiceTab"
              @popoverClose="popoverClose"
              ></ChoiceTab>
            </template>
            <el-select
              @change="changeDateDimensionVal"
              ref="dateSelect"
              :popper-append-to-body="notAppendToBody"
              v-model="selectedDateVal"
              v-if="showDateComponent({vm: this, place: 'pc'}) && selectShow && isIndicatorDropDown"
              :disabled="!isBoardRequestFinish"
              :class="['date-component-left', 'sdp-params-theme-element-top-background']"
              :style="changeFontStyle('dateDimensionStyle', 'dateDimensionColor')"
              :title="selectedDateLabel"
              :popper-class="selectClassName">
              <el-option
                v-for="item in dateDimensionValList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>

          </div>
        </div>

        <div class="text">
          <i class="icon-sdp-quanxian"></i>
          <span v-if="isDesensibilisationChart">{{ $t('sdp.views.sensitiveFieldTips', { prop: isDesensibilisationChart }) }}</span>
          <span v-else>{{ $t('sdp.views.noDataPermissions') }}</span>
        </div>
      </div>
      <div
        v-if="!noDataPermissions && (hasChartData || hasGrid)"
        ref="chart"
        :class="[
          'widget',
          'loading-settings',
          chartClassName,
          {
            'mobile': isMobile && !isChartSet,
          }
        ]"
        :style="overflowStyle"
      >
        <div v-if="!showNone" class="sdp-chart-title">
          <div class="backdrill-title" :class="{'mobile-chart-title': isMobile}" :style="mobileChartPartMargin('title')">
            <!-- 钻取返回 -->
            <div
              v-if="(!hasGrid && isShowUpDrillButton) || (hasGrid && isGridDrillArrowShow)"
              @click="onUpDrill()"
              :class="{
                'up-drill-button': true,
              }">
              <i class="el-icon-arrow-left"></i>
            </div>
            <!-- 标题 -->
            <div
              v-clamp="chartClamp"
              v-if="hasChartData"
              :style="titleStyle"
              :class="[isMobile ? '' : 'title-nowrap', superLinkTitleClick ? 'chart-pointer' : '', `export-title-${element.id}`]"
              :title="chartUserConfig.title.text"
              @click="titleClick"
            >
              <font
                :class="{
                  'gradient' : checkIsGradient(titleStyle)
                }"
                :style="gradientColorStyle"
              >{{chartUserConfig.title.text}}</font>
            </div>
            <div v-if="showMeasureSummary && showMeasureConfig === 'SINGLE' && !chartUserConfig.title.text && !isMobile">
              <!-- 度量汇总（pc端不存在标题时，度量汇总与指标选择器显示在同一行） -->
              <pre
                :style="measureStyle"
                :title="measureText"
              >{{ measureText }}</pre>
            </div>
            <MultipulMeasureSummaryPc
              v-if="showMeasureSummary && showMeasureConfig === 'MULTIPUL' && multiMeasureList.length && !chartUserConfig.title.text && !isMobile"
              :list="multiMeasureList"
              :containerWidth="multiMeasureWidth"
              ></MultipulMeasureSummaryPc>
            <el-tooltip
              v-if="isShowDescriptions && selectShow && !isMobile"
              effect="dark"
              :append-to-body="tooltipAppendToBody"
              class="chart-desc"
              v-sdp-el-insert="'el-tooltip$#$elementChart'"
              placement="bottom"
            >
              <div v-html="$_xss($_transformNtoBreak(chartUserConfig.chartDescription))" slot="content"></div>
              <i class="icon-sdp-tishi"></i>
            </el-tooltip>
            <!-- 写v-else-if 比v-else 保险点 -->
            <div class="element-chart-desc" v-else-if="isShowDescriptions && selectShow && isMobile && !fullscreenData.enlargeVisible" @click="showDescriptionInfoPopup(chartUserConfig.chartDescription)">
              <i class="icon-sdp-miaoshu"></i>
            </div>
            <!--提示-->
            <el-popover
              v-if="tipRenderShowType === 'hover' && checkTipShow({ isTipList: true, tipSetIndex: currentTipSetId, element, commonData, utils, langCode })"
              effect="dark"
              class="chart-desc chart-tip-popover"
              :class="{ 'is-mobile': isMobile }"
              :popper-class="`${getCurrentThemeClass()} sdp-tip-popover`"
              :trigger="isMobile ? 'click' : 'hover'"
              placement="bottom"
            >
              <tip-render :element="element" :isTipList="true" :tipSetIndex="currentTipSetId" @refresh="handleTipRefresh(0)"/>
              <i class="icon-sdp-gengxinshijian1" style="font-size: 12px" slot="reference"></i>
            </el-popover>
            <div class="show-state-icon" :style="computedStateIconStyle" v-if="borderInfo.isShowStateIcon && (isInteractive || isSuperLink || isDrillList)" >
              <!-- 设置了交互 -->
              <i class="icon-sdp-liandong" :title="$t('sdp.boardAttr.AttrLinkage')" v-if="isInteractive "></i>
              <i class="icon-sdp-tiaozhuan" :title="$t('sdp.boardAttr.AttrJump')" v-if="isSuperLink"></i>
              <i class="icon-sdp-xiazuan" :title="$t('sdp.boardAttr.AttrDrillDown')" v-if="isDrillList"></i>
            </div>
          </div>

          <!-- 纯数据导出 -->
          <i v-if="isTableDataExport" class="icon-sdp-xiazaiexcel title-right" style="font-size: 20px;cursor: pointer; color: #777777" @click="handleExportTableData"></i>

          <!-- 指标选择器 -->
          <div class="title-right" ref="titleRightRef">
            <div style="display: flex;justify-content: flex-end;gap:10px;align-items: center;">
              <!-- 这里放一个插槽 isNoData-->
              <template v-if="isMobile">
               <slot></slot>
              </template>
              <WarningBell @eventBus="eventBus" v-if="selectShow && (!(!isIndicatorDropDown && (fixAddress || warnAddress)) || isIndicatorDropDownTitltShow)" v-bind="{isChartSet, isIndicatorDropDown: !isIndicatorDropDown, themeFullScreen, fullscreen, isShowChartWarning, chartUserConfig, selectShow, isMobile, warnAppendToBody, element, contentItem: chioceTab.length ? chioceTab[checkedDimension] : null, isWarning}"></WarningBell>

              <template v-if="showChartIndicatorPC && selectShow">
                <ChoiceTab
                v-bind="{notAppendToBody, chioceTabList, element, isBoardRequestFinish, selectClassName, showChartIndicatorPC, selectShow}"
                @choose-dimension="chooseDimension"
                @handle-choice-tab="handleChoiceTab"
                @popoverClose="popoverClose"
                ></ChoiceTab>
              </template>
              <ChartSelect
                @change="changeDateDimensionVal"
                ref="dateSelect"
                :popper-append-to-body="notAppendToBody"
                v-model="selectedDateVal"
                v-if="showDateComponent({vm: this, place: 'pc'}) && selectShow && isIndicatorDropDown"
                :disabled="!isBoardRequestFinish"
                :class="['date-component-left', 'sdp-params-theme-element-top-background']"
                :style="changeFontStyle('dateDimensionStyle', 'dateDimensionColor')"
                :title="selectedDateLabel"
                :popper-class="'preview-select ' + selectClassName">
                <el-option
                  v-for="item in dateDimensionValList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </ChartSelect>

            </div>
          </div>
        </div>
        <!--提示-->
        <div v-if="!showNone && tipRenderShowType === 'tile'">
          <tip-render style="margin-bottom: 4px;" :element="element" :isTipList="true" :tipSetIndex="currentTipSetId" @refresh="handleTipRefresh"/>
        </div>
        <div v-if="isFilterSortTile">
          <filterSortTile
            style="margin: 0 18px 0 10px;"
            :element="element"
            :elList="elList"
            :boardInfo="boardInfo"
            :fullscreen="false"
            :is-loading="loading"
            :boxHeadRange.sync="filterSortTileHeight"
            @eventBus="eventBus"
          />
        </div>
        <!-- 移动端指标选择器 -->
        <template v-if="indicatorSelectorShow && showChartIndicatorMobile('mobile')">
          <div
            @click="indicatorPopup"
            class="dimension-selector mobile-indicator"
            :style="mobileChartPartMargin('choiceTab')"
            v-if="isIndicatorDropDown"
            >
            <span :style="getIndicatorStyle('indicatorSelectorTextStyle', true)">{{chioceTab[checkedDimension].name}}</span>
            <i class="el-icon-arrow-down"></i>
          </div>
          <div v-else :class="['chart-choice-tabbar', indicatorSelectorShowType === 'tile-underLine' ? 'indicator-underline' : '', chartTabWidthType1 || chartTabWidthType2 ? 'chart-choice-tabbar-custom' : 'chartChoiceTabbarWidth100', chartTabWidthType1 ? 'chart-choice-tabbar-custom-type1' : '']">
            <ScrollNavBar
              ref="scrollNavBarRef"
              :current="checkedDimension"
              :labels="chioceTab.map((_, index) => index)"
              :txts="chioceTab.map(item => item.name)"
              :style="getIndicatorStyle('indicatorSelectorTextStyle')"
              @change="handleChoiceTab"
              class="sdp-grid-item-drag-ignore"
            >
            </ScrollNavBar>
          </div>
        </template>

        <!-- 移动端日期切换控件 -->
        <div
          class="mobile-date-component"
          v-if="showDateComponent({vm: this, place: 'mobile'})"
          >
          <div class="tab">
            <div
              v-for="item in dateDimensionValList"
              :key="item.value"
              class="date-component-option"
              :class="selectedDateVal === item.value ? 'select-date-option' : ''"
              @click="mobileDateComponentClick(item.value)">
              <span :style="changeFontStyle('dateDimensionStyle', 'dateDimensionColor', selectedDateVal === item.value)">{{ item.label }}</span>
            </div>
          </div>
        </div>
        <div class="sdp-drill-bread" v-if="isMobile && !isChartSet && drillDimensionsLabelList.length">
          <ul class="drill-bread-ul">
            <li
              class="drill-bread-item"
              v-for="(item, index) in drillDimensionsLabelList"
              @click="onUpDrillByIndex(index)"
            >
              {{item}}
            </li>
          </ul>
        </div>
        <div class="sdp-chart-title sdp-chart-measure" :class="{'mobile-chart-measure': isMobile, 'simple-grid-margin-top': !isMobile && showGrid && refreshDomVisible }">
          <div
            v-if="showMeasureSummary && showMeasureConfig === 'SINGLE' && (chartUserConfig.title.text || isMobile)">
            <!-- 度量汇总 -->
            <pre
              class="has-margin"
              v-if="showMeasureSummary && (chartUserConfig.title.text || isMobile)"
              :style="measureStyle"
              :title="measureText"
            >{{ measureText }}</pre>
          </div>
          <div v-if="!showMeasureSummary || (!chartUserConfig.title.text && !isMobile)"></div>
          <template v-if="isMobile">
            <cube-scroll
              v-if="showMeasureSummary && showMeasureConfig === 'MULTIPUL' && multiMeasureList.length"
              ref="measureScroll"
              :data="multiMeasureList"
              :scrollEvents="['scroll']"
              :options="{
                click: true,
                tap: false,
                bounceTime: 300,
                stopPropagation: true
              }"
              direction="horizontal"
              class="horizontal-scroll-list-wrap has-margin">
              <div class="list-wrapper">
                <pre
                  v-for="(item, index) in multiMeasureList"
                  :style="item.showStyle"
                  :title="item.showLabel"
                  :key="index"
                ><i class="dot" :style="{background: item.iconColor}"></i>{{ item.showLabel }}</pre>
              </div>
            </cube-scroll>
          </template>
          <template v-if="chartUserConfig.title.text && !isMobile">
            <MultipulMeasureSummaryPc
              v-if="showMeasureSummary && showMeasureConfig === 'MULTIPUL' && multiMeasureList.length"
              :list="multiMeasureList"
              :containerWidth="multiMeasureWidth"
              class="has-margin"
              ></MultipulMeasureSummaryPc>
          </template>
          <!-- 日期切换控件 -->
          <div style="display: flex;justify-content: flex-end;">
            <WarningBell class="has-margin" @eventBus="eventBus" v-if="selectShow && !isIndicatorDropDownTitltShow && !isIndicatorDropDown && (fixAddress || warnAddress)" v-bind="{isChartSet, isIndicatorDropDown: isIndicatorDropDown, themeFullScreen, fullscreen, isShowChartWarning, chartUserConfig, selectShow, isMobile, warnAppendToBody, element, contentItem: chioceTab.length ? chioceTab[checkedDimension] : null, isWarning}"></WarningBell>

            <ChartSelect
              @change="changeDateDimensionVal"
              ref="dateSelect"
              :popper-append-to-body="notAppendToBody"
              v-model="selectedDateVal"
              v-if="showDateComponent({vm: this, place: 'pc'}) && selectShow && !isIndicatorDropDown"
              :disabled="!isBoardRequestFinish"
              :class="['date-component', 'sdp-params-theme-element-top-background', 'has-margin']"
              :style="changeFontStyle('dateDimensionStyle', 'dateDimensionColor')"
              :title="selectedDateLabel"
              :popper-class="'preview-select ' + selectClassName">
              <el-option
                v-for="item in dateDimensionValList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </ChartSelect>
          </div>
        </div>
        <!--提示-->
        <!--<div v-if="isMobile && !chartUserConfig.title.text && !showNone && tipRenderShowType === 'tile'">-->
        <!--  <tip-render style="margin-bottom: 4px;" :element="element" :isTipList="true" :tipSetIndex="currentTipSetId" @refresh="handleTipRefresh"/>-->
        <!--</div>-->
        <RenderLayout v-if="!showGrid && !showDecomposition && showChart">
          <!-- 辅助线显示切换 -->
          <VirtrualScroll
            :slot="'auxiliaryLine-' + mobilePosition('auxiliaryLine')"
            ref="virtrualScroll"
            :themeType="themeType"
            :listData="auxiliaryLine"
            :terminal="isMobile ? 'mobile': 'pc'"
            :isChartSet="isChartSet"
          >
            <div :class="['auxiliaryLine-item', { 'gray-item': !!auxiliaryLineHidden[data.id] }]" slot-scope="{ data }" @click="toggleAuxiliaryLine(data)">
              <span class="auxiliaryLine-line" :style="{ 'border-color': data.color }"></span>
              <span :class="['auxiliaryLine-name', { 'ellipsis': !isMobile || isChartSet }]" :title="data.name">{{ data.name }}</span>
            </div>
          </VirtrualScroll>
          <!-- 移动端图例 -->
          <add-mobile-legend
            :slot="'legend-mobile-' + mobilePosition('legend')"
            v-bind="{ content, isMobileLegend, echartInstance, themeType, screenType, legendSelected, element }"
            :class="chartUserConfig.legend === 'bottom' ? 'bottom-legend' : ''"
            ref="addMobileLegend"
            @mobile-legend-init-complete="(params) => resizeEcharts(params, 'mobile-legend-init-complete')"
            @eventBus="eventBus"
          ></add-mobile-legend>
          <z-element
            slot="echarts-element"
            v-if="!isNoData && refreshDomVisible && !loadingFlag"
            :key="chartKey"
            :class="chartContentClass"
            class="sdp-chart-content"
            :ref="elName"
            :el-name="elName"
            :chart-props="chartProps"
            :element="element"
            :parent="this"
            :element-id="element.id"
            :canvas-class-name="chartContentClass"
            :chart-class-name="chartClassName"
            :is-chart-set="isChartSet"
            :themeType="themeType"
            :chartUserConfig="chartUserConfig"
            @refreshChart="refreshChart"
            @trigger-chart-events="triggerEchartsEvents"
            @chart-click="mobileClick"
            @slide-left="slideLeft"
            @slide-right="slideRight"
            @slide-top="slideTop"
            @slide-down="slideDown"
            @scroll-down="chartScroll('down')"
            @scroll-up="chartScroll('up')"
          >
          </z-element>
          <no-data slot="data-empty" v-if="!loadingFlag && isNoData" class="sdp-chart-no-data-mask" />
        </RenderLayout>

        <div v-if="showGrid && refreshDomVisible && !showDecomposition" class="sdp-chart-grid-content" :class="`${element.id}_container_header`" :style="overflowStyle">
          <table-preview
            ref="grid"
            :element="tableElement"
            :boardInfo="boardInfo"
            :api="utils.api"
            :tenant-id="utils.tenantId"
            :application="application"
            :styles="style"
            :board-mode="boardMode"
            :previewCode="langCode"
            :original-table-config="originalTable"
            :table-default-config="tableDefaultConfig"
            :options="tableOptions"
            :is-chart-set="isChartSet"
            :isChart="true"
            :elList="elList"
            v-bind="$attrs"
            @drill-event="onGridDrill"
            @eventBus="eventBus"
            @gird-render-finish="gridMounted"
            @handColResize="handColResize"
          />
        </div>

        <div v-if="showDecomposition && refreshDomVisible" class="sdp-chart-decomposition">
          <Decomposition
            ref="decomposition"
            :element="element"
            :UserConfig="UserConfig"
            :warningArray="warningArray"
            :api="utils.api"
            :themeType="themeType"
            :tenant-id="utils.tenantId"
            :application="application"
            :elCanvasSize="elCanvasSize"
            :styles="style"
            :board-mode="boardMode"
            :previewCode="langCode"
            :is-chart-set="isChartSet"
            :isChart="true"
            :elList="elList"
            v-bind="$attrs"
            @eventBus="eventBus"
            @dimensionSuperLink="simpleDimensionSuperLinkClick"
            @dimensionInterAction="simpleDimensionInterActionClick"
            @refreshSelf="refreshSelf"
          />
        </div>

        <!-- 日历图翻页 -->
        <div v-if="isShowCalendarButton" class="calendar-btn">
          <el-button icon="el-icon-arrow-left" circle @click="changeCalendarRange('pre')"></el-button>
          <el-button icon="el-icon-arrow-right" circle @click="changeCalendarRange('next')"></el-button>
        </div>

        <!-- 占位图 -->
        <div v-if="!isChartShow || (!chartSettings.metrics.length && elName !== 've-tree' && !hasGrid)" class="placeholder-image">
          <span>NONE</span>
        </div>
      </div>

      <div v-if="disableDrill" class="placeholder-image" style="position: absolute!important;top:0px;left:0px;">
        <Loading :isLoading="true" v-if="!isMobile"></Loading>
        <div v-if="isMobile" style="font-size: 14px;color:rgb(85,85,85);">加载中...</div>
      </div>
    </template>
    <div v-else class="placeholder-image"><span>NONE</span></div>
  </div>
</template>
<script>
import ZElement from './Element'
import RenderLayout from './renderLayout'
import filterSortTile from 'packages/base/common/filterSortTile'
import { ChartSelect } from 'packages/base/board/displayPanel/supernatant/chartSet/common'
// import Elements from 'components/Elements'
// import elRenderHelperMixin from 'components/Supernatant/elRenderHelperMixin'
import NoData from 'packages/base/common/loadingMask/noData'
import Color from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/color'
import { getSeriesAnimation } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/chartsList/chartSetting/chartLabel'
import { OnlyField } from 'packages/base/board/displayPanel/datasetReplace'
import {
  afterConfigHandler,
  handleFourQuadrantLine,
  showChartIndicator,
  showDateComponent,
  isThemeRiver,
  getBandwidthData,
  getCalenderComponentMonths,
  setBackgroundTrend
} from './chartsList/charts'
import * as gaugeConfigs from './chartsList/config-gauge'
import animationClass from './animation'
import * as chartApi from './api'
import {
  HOLIDAY_STYLE_CHART,
  LEGEND_SELECT_CHANGED_DATA_CHART,
  AFTERCONFIGFUNC, CIRCULAR_CHART,
  HANDLE_MAX_MIN_VALUE_CHART,
  NEED_TIMEDIMENSION_CHART,
  LEGEND_SETTING_SAVE_CHART,
  VERTICAL_SCROLL_BAR_CHART,
  MOBILE_PAGE_CHART,
  GRAPHARR,
  ANIMATION_CHART,
  NOT_AUXILIATYLINE_CHART,
  MEARSURE_DIS_CENTER,
  WARNING_LINE_CHART,
  NOT_SUPPORT_DIMENSION_CHART,
  DATAZOOM_CHART,
  VERTIVAL_DATAZOOM_CHART,
  CHART_ALIAS_TYPE,
  ANIMATION_HISTOGRAM_CHART,
  ANIMATION_TOOLTIP_CHART,
  CUSTOM_DATE_DIMENSION_CHART,
  ANIMATION_POSITIVE_CHART,
  CUSTOM_METRIC,
  CONTROL_MEASURES_CHART,
  BARCHART_WARN_TYPES,
  getGlobalParamId,
  DIMENSION_VALUE_INDICATOR_WARNING_CHART,
  DATE_DIMENSION_TYPE,
  EXTEND_DIMENSION_CHART,
  VE_LIQUIDFILL_MODE,
  HAS_TARGET_CHART,
  MULTI_GRAPHARR,
  MAIN_DIMENSION_MEASURE_CHART, IGNORING_UNSELECTED_METRICS_CHART,
} from './constant'
import CHART_LIST from './chartDesign/chartDesignClass/ChartClassList'
import { isDimensionLegend } from './chartsList/chartConfig'

import { isReverseAxisChart, getAfterPagingCalendarDate, hasDimensionColor, isScrollAccordingToDimension, checkNoDataPermissions, onlyDimensionColor } from './chartsList/chartSetting/handleChartSetting'
// import { handleFourQuadrantLine } from 'components/Supernatant/elRenderAdapters'
// import { dateJudge } from 'packages/base/gridPreview/common/js/utils'
import Loading from 'packages/base/common/loading/loading'
import EventData from 'packages/assets/EventData'
import eventBus from 'packages/assets/eventBus'
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin'
import {
  EVENT_DATA_PARAMS,
  THEME_TYPE,
  RUN_TYPE,
  STATIC_BASE_PATH,
  CREATE_TOAST_TIME,
  SUPERLINK_CONST_TYPE
} from 'packages/assets/constant'
import bridge, { initNegativeConfig, isSetMeasureConfig } from './bridge'
import { TYPE_INTERACTION, TRENDS_DIMENSION_EXCHANGE } from 'packages/base/board/displayPanel/constants'
import { getformatValue } from 'packages/base/board/displayPanel/params/paramElement/bussinessCalendar/api'
import fonts from 'packages/assets/fonts'
import { VirtrualScroll, ChoiceTab, LegendMobile } from './components'
import mapConstant from './chartsList/mapConstant'
import compatibilitiChartData from './compatibilitiChart'
import * as echarts from 'echarts'
import { setThemeChartUserConfig, setThemeConfig, getThemeConfig, setThemeContent } from './chartsList/theme'
import waiting from 'packages/base/common/waiting'
import chartImgs from '../../chartSet/components/smallChartImg'
import {getGradientStyle, isCanInteraction} from 'packages/base/board/displayPanel/utils'
import chartToTableMixin from './chartToTableMixin'
import {checkIsGradient, filterMapVerify} from '../../../utils'
import { TYPE_ELEMENT_GROUP } from '../../../params/utils/constants'
import { ScrollNavBar } from 'cube-ui'
// import html2canvas from 'html2canvas'
import html2canvas from 'packages/base/board/mixins/html2canvas.esm.js'
import { initFilterSorterHandler, clearToolParams, FILTER_DISPLAY_MODE } from '../../filterDialog/utils'
import { compatibilityDatasetField } from './helper/dataset'
import { displayMeasureInLegend, getExtendDimensionData } from './chartsList/chartSetting/extendDimension'
import { cloneHandler } from '../../../../mixins/exportMixin'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import SdpElInsert from 'packages/directives/v-el-insert'
import WarningBell from 'packages/base/common/warningBell/index'
import { EVENT_BUS } from '../../../constants'
import mousemove from 'packages/assets/directives/mousemove'
import elementResizeDetectorMaker from 'element-resize-detector'
import { USER_PROP } from 'packages/base/board/displayPanel/supernatant/chartSet/functions/assets/index'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import UserConfig from './userConfig'
import elementMixin from 'packages/base/board/displayPanel/supernatant/mixins/elementMixin'
import { SBI_ATTR } from 'packages/base/board/dailyConcernMetrics/utils'
import { insetDom } from 'packages/base/common/loading/components/loadingIcon'
import MultipulMeasureSummaryPc from './components/multipul-measure-summary-pc.vue'
import refreshMixin from 'packages/base/board/displayPanel/datasetReplace/refreshMixin'
import { ElementWarningData } from '../../mixins/warningSubscribe'
import ElementSlot from '../components/elementSlot.vue'
import descriptionInfoMixin from 'packages/base/board/mixins/descriptionInfoMixin'
import chartDecompositionMixin from './Decomposition/mixins/chartDecompositionMixin'
import { isRealScreenFn } from 'packages/helpers'
import {getFilterCount, setFormatterValue} from "./chartDesign/getChartTooltip";
import {previewMergeApi} from "../../../utils/helpers/api";
import { setScrollNavBarItemsWidth } from '../../../utils'
import { ELEMENT_EXPORT_TYPE } from 'packages/base/board/settingPanel/components/exportComponents/constants'
import TipRender
  from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/tipRender.vue";
import {checkTipShow} from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/utils";

// ----------------分割线---------------------------------------
const erd = elementResizeDetectorMaker()

export default {
  name: 'elementChart',
  mixins: [chartToTableMixin, chartDecompositionMixin, datasetMixin, elementMixin, refreshMixin, descriptionInfoMixin],
  inject: {
    sdpBus: { type: Object },
    kanbanId: { type: String, default: '' },
    commonData: { default: {} },
    themeData: { default: {} },
    utils: { default: {} },
    isEdit: { default: false }, // 这个注入不能删
    tabletConfig: {
      default: () => () => ({
        tabletScale: 1,
        isMobileTablet: false
      })
    }, // 是否平板
    requestAppEvent: { default: () => () => {} },
    // 环境多语言，外界传入
    langCode: { default: 'en' },
    // 环境多语言，外界传入（不支持的变成en）
    sdpLangcode: { default: 'en' },
    getCurrentThemeClass: { default: () => () => '' },
    fullscreenData: { default: {} },
    tenantData: { default: {} },
    boardData: {
      default: {}
    },
    getBoardData: {
      default: () => () => ({})
    },
    setLoadingStatus: {
      default: () => () => false
    },
    setErrorStatus: {
      default: () => () => false
    },
    shareData: { default: {} },
    getBoardStatus: {
      default: () => () => ''
    },
    getBoardCurrency: {
      default: () => () => {}
    },
    getBoardUnderIdElement: {
      default: () => () => {}
    },
    levelData: {
      type: Object,
      default: () => ({})
    },
    elementRef: { default: () => [] },
    configs: { default: () => false },
    authority: { default: {} },
    boardWarningSubscribeData: { default: {} },
  },
  directives: {
    mousemove,
    SdpElScrollbar,
    SdpElInsert
  },
  components: {
    TipRender,
    ElementSlot,
    ZElement,
    RenderLayout,
    ChartSelect,
    // ...Elements,
    Loading,
    addMobileLegend: LegendMobile,
    waiting,
    ScrollNavBar,
    VirtrualScroll,
    ChoiceTab,
    WarningBell,
    filterSortTile,
    NoData,
    MultipulMeasureSummaryPc
  },

  props: {
    terminal: {
      type: String,
      default: '',
    },
    previewType: {
      // 不可删！展示类型
      type: String,
      default: 'preview-edit',
    },
    element: {
      type: Object,
      default: () => ({}),
    },
    // 是否是编辑状态
    isChartSet: {
      type: Boolean,
      default: false,
    },
    // 是否隐藏日期切换
    hideDateComponent: {
      type: Boolean,
      default: false,
    },
    // 该图表是否是全屏状态
    fullscreen: {
      type: Boolean,
      default: false,
    },
    isContainer: {
      type: Boolean,
      default: false,
    },
    elList: {
      type: Array,
      default: () => []
    },
    // 移动端屏幕展示方式 竖屏-vertical 横屏-landscape
    screenType: {
      type: String,
    },
    // 是否高级容器
    isAdvanceContainer: {
      type: Boolean
    },
    boardInfo: {
      type: Object,
      default: () => {}
    },
    boardScale: {
      type: Number,
      default: 100
    },
  },

  data() {
    return {
      // 9335 临时存储置灰的图例
      tempLegendsSelected: null,
      tempDrillLegendsSelected: null,
      isLegendsHiddenCall: false,
      refreshWithResizeCharts: ['ve-ring-multiple', 've-roundCascades', 've-gauge-normal', 've-grid-normal', 've-liquidfill', 've-radar', 've-pie-normal'],
      UserConfig: new UserConfig(this.element, this),
      dimensionSensibilisationMessage: null,
      isWarning: false,
      labelLineButtonList: [],
      visible: false,
      refreshDomVisible: true,
      auxiliaryLineHidden: {},
      showNone: false,
      metricChioceTab: {},
      chartKey: 1,
      calendarRealData: '',
      loadingInstance: '',
      loadingFlag: false, // 大屏看板地图动画暂时新增loading标识，this.loading永远是false
      // location选择维度标签还是报表层级树
      locationSelectType: '',
      drillValue: [],
      tipDOM: null,
      legendSelected: {},
      legendScrollDataIndex: 0,
      legendSelectedChanged: 0,
      chartClamp: 'NO',
      interactionOptionsValues: null,
      interactionOptionsTrigger: '',
      triggerMousemove: false,
      isMouseEnter: false,
      calendarFirstDate: '', // 记录日历图当前页的第一条日期数据
      calendarDateRange: '',
      // 筛选器平铺高度
      filterSortTileHeight: 0,
      geoRoamInfo: {},
      elCanvasSize: {
        canvasOffsetWidth: 0,
        canvasOffsetHeight: 0,
      },
      dateDimensionVal_OLD: [
        // {
        //   value: '_vs_none',
        //   label: this.$t('sdp.views.none'),
        // },
        {
          value: DATE_DIMENSION_TYPE.Day_vs_day_d,
          label: this.$t('sdp.views.Day'),
        },
        {
          value: DATE_DIMENSION_TYPE.Day_vs_day,
          label: this.$t('sdp.views.Day'),
        },
        {
          value: 'week_vs_week',
          label: this.$t('sdp.views.Week'),
        },
        {
          value: 'month_vs_month',
          label: this.$t('sdp.views.Month'),
        },
        {
          value: 'quarter_vs_quarter',
          label: this.$t('sdp.views.Quarter'),
        },
        {
          value: 'half_year_vs_half_year',
          label: this.$t('sdp.views.HalfYear'),
        },
        {
          value: 'year_vs_year',
          label: this.$t('sdp.views.Year'),
        }
      ],
      loading: false,
      error: false,
      selectShow: true,
      hasChartData: false,
      renderChart: false,
      compareKey: '',
      formatterPrevData: {},
      formatterNowData: {},
      echartInstance: {}, // echarts实例
      chartInstance: null, // 图形实例
      // interactionSetting: {
      //   clickAble: true,
      //   filedName: '',
      // },
      colorBand: {
        x: [],
        y: [],
      },
      // savePrintData: () => {
      // },
      mapOptions: {
        position: 'china',
        series: [],
        visualMap: {
          left: 'left',
          show: true,
          min: 0,
          max: 1000,
          inRange: {
            color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          },
          text: [], // 文本，默认为数值文本
          calculable: false,
          realtime: false,
          orient: 'horizontal',
        }
      },
      // 禁止设置背景趋势效果的标志，为true则在finished的时候不执行
      disabledBgTrendFlag: false,
      defaultChartConfig: {
        extend: {
          series: {
            symbolSize: 10,
            itemStyle: {
              borderWidth: 2,
            },
            lineStyle: {
              width: 3,
            }
          }
        },
        axisPointer: {
          lineStyle: {
            color: '#E1E7F4',
          },
        },
        events: {
          // legendinverseselect: () => {
            // 解决带宽图标不起作用问题
            // if (this.chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH) {
              // this.echartInstance.dispatchAction({
              //   type: 'legendToggleSelect',
              //   name: this.$t('sdp.views.bandwidth')
              // })
            // }
          // },
          click: (e) => {
            // 此处改动要同步改handleChartsMockClick
            console.log(e)
            if (this.shareData.isFilterHold) return void '正在筛选时不支持点击图形'
            if (e.seriesId === 'rank' && this.isBandwidth) {
              return void '排名不交互钻取'
            }
            // 双维度的分隔线不需要点击事件,树图跟节点不需要（pc端支持点击主维度标签进行交互、超链接）
            const allowClickParentDimensionLabel = e.data?.parentDimensionName && !this.isMobile
            if ((e.componentType === 'markPoint' && !allowClickParentDimensionLabel) || e.selfType === 'breadcrumb' || (e.seriesType === 'tree' && e.dataIndex === 1)) {
              return
            }
            // 地图跳转需要记录上次点击的地理信息
            if (this.content.chartUserConfig.childChartAlias === 've-map-world' && e.componentType !== 'title') {
              if (this.mapSuperlinkData && this.mapSuperlinkData.name === e.name) {
                this.mapSuperlinkData = undefined
              } else {
                this.mapSuperlinkData = {
                  name: e.name,
                  seriesName: e.seriesName
                }
              }
            }
            // 旭日图点击父级不需要钻取、交互
            if (!this.isMobile && this.chartUserConfig.chartAlias === 've-sunburst' && e.data.hasOwnProperty('children')) return
            // 水滴图内部不需要点击事件
            if (e.componentSubType === 'liquidFill') return
            if (['ve-map-china'].includes(this.content.chartUserConfig.childChartAlias) && (e.componentType === 'geo' || e.data === undefined)) return
            // this.mobileClick(e)
            this.$set(this, 'currentClickData', e)
            this.handleChart(e)
            this.handleSingleOperateClick(e)
          },
          brush: e => {
            console.log(e)
          },
          legendScroll: e => {
            this.legendScrollDataIndex = e.scrollDataIndex
          },
          legendselectchanged: e => {
            this.legendChange()
            if (!this.echartInstance || this.$_isEmptyObject(this.echartInstance)) return

            if (isDimensionLegend(this.chartUserConfig)) {
              return this.disableLegendSelect(e)
            }
            const { chartAlias, labelLineShow } = this.chartUserConfig
            if (this.chartUserConfig.chartAlias === 've-ring-multiple' && this.chartUserConfig.labelLineShow && (this.labelLineButton || !this.commonData.isPreview)) {
              this.echartInstance.setOption({
                yAxis: { show: e.selected[e.name] }
              })
            } else if (this.chartUserConfig.chartAlias === 've-waterfall') {
              this.waterfallLegendSelected(e.selected)
            } else if ([...LEGEND_SELECT_CHANGED_DATA_CHART, ...CIRCULAR_CHART, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_FUNNEL].includes(chartAlias)) {
              if (chartAlias === 've-roundCascades') {
                this.handleRoundCascadesLabel(e.selected)
              }
              if (this.isLiquidfill) {
                this.calRateCompletion(e.selected)
              }
              this.legendSelectedChanged = 1

              // 河流图，漏斗图通过下面这个方法将图例显示隐藏信息先保留
              this.resetAxisRange(e.selected)

              // 蝴蝶图、河流图，漏斗图需要刷新实现图例的排序
              if ([CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY, CHART_ALIAS_TYPE.VE_THEMERIVER, CHART_ALIAS_TYPE.VE_FUNNEL].includes(chartAlias)) {
                this.refreshChart({ source: 'legendselectchanged' })
              }
            }
          },
          legendselectall: e => {
            this.legendChange()
          },
          legendinverseselect: e => {
            this.legendChange()
          },
          brushselected: e => {
            if (!this.echartInstance || this.$_isEmptyObject(this.echartInstance)) return
            const options = this.echartInstance.getOption()
            const selected = options.legend[0]?.selected || {}
            const { chartAlias, labelLineShow, liquidFillSetting } = this.chartUserConfig
            const { dimensionList } = this.UserConfig
            // 图例翻页时也会触发brushselected事件，翻页时selected为空对象
            if (chartAlias === 've-ring-multiple' && labelLineShow && (this.labelLineButton || !this.commonData.isPreview)) {
              let legendKey = Object.keys(selected)[0]
              legendKey && this.echartInstance.setOption({
                yAxis: { show: selected[legendKey] }
              })
            } else if (chartAlias === 've-waterfall') {
              if (Object.keys(selected).every(item => !!item && !['growthTotal', 'declineTotal'].includes(item))) {
                this.waterfallLegendSelected(selected)
              }
            } else if ([...LEGEND_SELECT_CHANGED_DATA_CHART, ...CIRCULAR_CHART, CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY].includes(chartAlias)) {
              if (chartAlias === 've-roundCascades') {
                this.handleRoundCascadesLabel(selected)
              }
              if (this.isLiquidfill && liquidFillSetting.mode === VE_LIQUIDFILL_MODE.NORMAL) return
              if (this.isLiquidfill && !this.$_isEmptyObject(selected)) {
                this.calRateCompletion(selected)
              }

              if (this.legendSelectedChanged === 1) {
                this.legendSelectedChanged++
              } else {
                const dimensionName = dimensionList[0].alias || dimensionList[0].labeName
                if (this.legendSelected && Object.keys(this.legendSelected).length) {
                  for (let k in selected) {
                    // 用来区分是否改变了legend的选中状态
                    if (selected[k] !== this.legendSelected[k]) {
                      if (this.othersPieDataIndex > -1) {
                        this.chartData.rows[this.othersPieDataIndex].others.forEach(r => {
                          selected[r[dimensionName]] = selected.others
                        })
                      }
                      this.resetAxisRange(selected)
                      break
                    }
                  }
                } else if (Object.keys(selected).length) {
                  // 如果是直接点击的全选反选
                  if (this.othersPieDataIndex > -1 && new Set(Object.values(selected)).size === 1) {
                    this.chartData.rows[this.othersPieDataIndex].others.forEach(r => {
                      selected[r[dimensionName]] = selected.others
                    })
                    this.resetAxisRange(selected)
                  }
                }
              }
            }
          },
          georoam: e => {
            if (this.chartUserConfig.chartAlias !== 've-map-parent' || !this.isChartSet) return

            const geo = this.echartInstance.getOption().geo[0]
            this.geoRoamInfo = Object.assign({}, this.geoRoamInfo || {}, {
              center: geo.center,
              zoom: geo.zoom,
            })
          },
          finished: () => {
            if (this.disabledBgTrendFlag) return
            setBackgroundTrend(this, () => {
              this.disabledBgTrendFlag = true
              setTimeout(() => {
                this.disabledBgTrendFlag = false
              }, 500)
            })
          },
          datazoom: e => {
            if (!this.isMobile || !this.commonData?.isPreview) return
            // 是否触发了datazoom事件
            this.isDataZoomEmitted = true
            let start = e.batch?.[0]?.start
            let end = e.batch?.[0]?.end
            if ((VERTIVAL_DATAZOOM_CHART.concat(CHART_ALIAS_TYPE.VE_BAR_PERCENT)).includes(this.chartUserConfig.chartAlias) && (start === 0 || end === 100)) {
              console.log('移动图形dataZoom滚动到头或尾，清除聚焦标识', this.chartUserConfig.chartAlias, start, end, e)
              const eventData = new EventData({
                type: 'clearForce',
                target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanelMobile'],
                targetFn: 'clearForce',
                data: { ids: [this.element.id] },
              })
              this.$emit('eventBus', eventData)
            }
          }
        },
        colors: Color.getDefaultChartColor(this.chartUserConfig, { colorThemeType: this.themeType, vm: this }),
        grid: {
          top: 24,
          bottom: 45,
          // right: 20,
          show: true,
          borderWidth: 0,
        },
        // 在tableInfo组件中 有个相同的 Legend 配置对象
        legend: {
          type: 'scroll',
          // bottom: 10,
          bottom: 0,
          tooltip: {
            show: true,
          },
          formatter: name => {
            let label = name
            if (this.showGrowthRate && this.chartData.legendData) {
              // 显示同比百分比
              const prefix = name.length  > 20 && !this.chartUserConfig.isLegendAdaptive ? name.substr(0, 19) + '...' : name
              try {
                label = `${prefix} ${this.getCompareValue(name)}`
              } catch (e) {
                label = prefix
              }
            } else {
              label = name.length > 20 && !this.chartUserConfig.isLegendAdaptive ? name.substr(0, 19) + '...' : name
            }
            // const ret = label.length > 10 ? label.substr(0, 9) + '...' : label
            return label
          },
        },
        toolbox: {
          show: false,
          feature: {
            brush: {
              show: true,
              title: {
                lineX: this.$t('sdp.views.lineX'),
                lineY: this.$t('sdp.views.lineY'),
                keep: this.$t('sdp.views.keep'),
                clear: this.$t('sdp.views.clear'),
              },
            },
          },
          top: -5,
          right: 20,
        },
        brush: {
          toolbox: ['lineX', 'lineY', 'keep', 'clear'],
          // brushMode: 'multiple',
          xAxisIndex: 0,
        },
      },
      // checkedDimension: 0,
      defaultEventData: {
        source: 'elementChart'
      },
      locationTreeDatae: [],
      isUpdateChart: true, // 重新渲染一遍图形，解决水滴图波纹颜色、地图缩放问题
      speciaDrillandInterActionlArr: ['ve-sunburst', 've-treemap'],
      treeDataChartArr: ['ve-tree'],
      disableDrill: false, // 是否禁止钻取，当钻取返回数据为空时，禁止下一次钻取
      chartImgs: chartImgs,
      mapSuperlinkData: undefined,
      showChartIndicatorMobile: (place) => {
        return showChartIndicator(this, place)
      },
      showDateComponent: ({ place, dateParams }) => {
        return showDateComponent({ vm: this, place, dateParams })
      },
      scrollBarPosition: '',
      // 修改该值时需考虑简单表格，计算简单表格的大小时有用到下面的值，padding值的格式必须为'上 右 下 左'
      chartPadding: {
        isChartSet: '16px 0 0 16px',
        gridInChartSet: '16px 16px 16px 16px',
        // 看板元素界面
        noPadding: '0 0 0 0',
        isBoardElement: '16px 0 10px 16px',
        gridInBoardElement: '16px 16px 10px 16px',
        gridInTemplatePreview: '16px 16px 16px 16px',
        isInAdvanceContainer: '8px 0 0 8px',
        gridInAdvanceContainer: '8px 8px 0 8px',
        isThemeFullScreen: '6px 0px 10px 10px',
        gridInThemeFullScreen: '6px 10px 10px 10px',
      },
      animationInstance: {},
      staticChartData: { columns: [], rows: [] },
      isChartLargeLast: true,
      calendarDate: [],
      firstRender: true,
      requestData: null,
      firstAnimated: false,
      globalChartColorScheme: null,
      tempSaveIndex: -1,
      currentClickData: null,
      // _前缀使其不走vue双向绑定
      _chartResponseTmp: null,
      // tab宽度
      mobileCursorPadding: 2,
      tipRenderHeight: 0,
      // 手势信息
      swipeInfo: {
        touchStartX: 0, // 触摸开始X坐标
        touchStartY: 0, // 触摸开始Y坐标
        touchEndX: 0, // 触摸结束X坐标
        touchEndY: 0, // 触摸结束Y坐标
        minSwipeDistance: 5, // 最小滑动距离
      },
      isDataZoomEmitted: false,
      // 临时变量：用于存储交互参数children的临时变量
      tempOldChildren: {},
    }
  },

  computed: {
    chartTabWidthType1() {
      const {content = {}} = this.element
      const chioceTabLength = content?.chioceTab?.length || 0
      const tabWidthValue = content?.tabWidthValue || 0
      const tabWidthType = content?.tabWidthType || '2'
      return tabWidthType === '2' && chioceTabLength !== tabWidthValue
    },
    chartTabWidthType2() {
      const {content = {}} = this.element
      const tabWidthType = content?.tabWidthType || '2'
      return tabWidthType === '1'
    },
    isTableDataExport() {
      return this.hasGrid && this.commonData?.isTableDataExport && this.commonData?.isPreview && !this.isMobile && this.boardInfo?.elementExport?.exportType?.includes(ELEMENT_EXPORT_TYPE.unFormatExport)
    },
    currentTipSetId() {
      const { chioceTab, saveIndex } = this.element.content
      if (!chioceTab || chioceTab.length <= 1) return this.element.id
      return chioceTab[saveIndex].id
    },
    isTemplate() {
      return this.configs?.type === 'template'
    },
    tipRenderShowType() {
      if (this.isTemplate || !this.boardInfo) return true
      if (!this.boardInfo.showElementTip || !this.element.tipSetList) return false
      const target = this.element.tipSetList.find(e => e.id === this.currentTipSetId)
      if (target) {
        return target?.displayType || false
      }
      return false
    },
    computedStateIconStyle() {
      let style = ''
      if ((!this.chartUserConfig.title.text && !this.showMeasureSummary && !this.isMobile && !this.isShowDescriptions) || (!this.chartUserConfig.title.text && this.isMobile && !this.isShowDescriptions)) {
        style = 'margin-left:0px'
      }
      return style
    },
    hasDrillData() {
      return !!(this.drillSettings?.drillDimensions || []).length
    },
    isIgnoringUnselectedMetrics() {
      return this.content.chartUserConfig?.metricCalculation
      && this.content.chartUserConfig?.ignoringUnselectedMetrics
      && IGNORING_UNSELECTED_METRICS_CHART.includes(this.chartUserConfig.chartAlias)
      && this.chartSettings?.extendDimension?.length
    },
    isPlayAnimation() {
      return this.utils.getPlayAnimation && this.utils.getPlayAnimation()
    },
    isDimensionSensibilisation() {
      const { dimensionList, } = this.UserConfig
      return dimensionList.some(item => this.content.chartResponse.dataMaskRuleParamMap?.[item.alias || item.labeName])
    },
    dateFormat() {
      return this?.tenantData?.tenant?.dateFormat || 'yyyy-MM-dd'
    },
    othersPieDataIndex() {
      if (this.chartUserConfig.chartAlias === 've-pie-normal' && this.chartUserConfig.pieSetting?.showCombinePie) {
        const othersPieDataIndex = this.chartData.rows?.findIndex(r => r.hasOwnProperty('others') && Array.isArray(r.others) && r.others.length)
        return othersPieDataIndex
      }
      return -1
    },
    // 是否使用关联数据集
    isAssociationDataset() {
      return this.chartUserConfig.datasetAssociation && this.element.content?.associatedData?.associateDatasetName
    },
    isChildMapScheme() {
      const mapSchemeSetting = this.element.content.mapSchemeSetting
      if (!mapSchemeSetting?.schemeList?.length || this.element.content.alias !== 've-map-parent') return false
      if (mapSchemeSetting.schemeIndex && this.chartUserConfig.childChartAlias !== 've-map-world') return true
      return false
    },
    warnAddress() {
      return !this.chartUserConfig.title.text && this.measureText
    },
    globalParameterIdList() {
      const globalParameterList = this.tenantData.globalParameterList || []
      return globalParameterList.map(e => e.id)
    },
    isIndicatorDropDownTitltShow() {
      return (this.warnAddress && !((this.showChartIndicatorPC) || (this.showDateComponent({ place: 'pc' }) && this.isIndicatorDropDown)))
    },
    // (!(!isIndicatorDropDown && (fixAddress || warnAddress)))
    fixAddress() {
      return (this.showChartIndicatorPC && this.selectShow && this.chioceTabList.length > 1) || (this.selectShow && this.isIndicatorDropDown) || (this.showDateComponent({ place: 'pc' }) && this.selectShow && this.isIndicatorDropDown)
    },
    isShowChartWarning() {
      return WARNING_LINE_CHART.includes(this.chartUserConfig.chartAlias)
    },
    auxiliaryLine() {
      const xAuxiliaryLineData = this.chartUserConfig.xAuxiliaryLineData || []
      const yAuxiliaryLineData = this.chartUserConfig.yAuxiliaryLineData || []
      return xAuxiliaryLineData.concat(yAuxiliaryLineData)
    },
    isChinaMap() {
      return this.chartUserConfig.childChartAlias === 've-map-china'
    },
    isMap() {
      return this.chartUserConfig.chartType === 've-map'
    },
    isBritainMap() {
      return this.chartUserConfig.childChartAlias === 've-map-britain'
    },
    isBandwidth() {
      return this.chartUserConfig.chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH
    },
    showChartIndicatorPC: {
      get() {
        return this.indicatorSelectorShow && showChartIndicator(this, 'pc')
      }
    },
    containerPadding() {
      let chartKey = ''
      let gridKey = ''
      if (this.isChartSet) {
        chartKey = 'isChartSet'
        gridKey = this.hasGrid ? 'gridInChartSet' : 'isChartSet'
      } else if (this.configs) {
        chartKey = 'isChartSet'
        gridKey = this.hasGrid ? 'gridInTemplatePreview' : 'isChartSet'
      } else if (this.themeFullScreen) {
        chartKey = 'isThemeFullScreen'
        gridKey = 'gridInThemeFullScreen'
      } else if (this.isInAdvanceContainer) {
        chartKey = this.isMobile ? 'noPadding' : 'isInAdvanceContainer'
        gridKey = this.hasGrid ? (
          this.isMobile ? 'noPadding' : 'gridInAdvanceContainer'
        ) : 'isInAdvanceContainer'
      } else if (!this.isMobile && !this.element._containerId) {
        chartKey = 'isBoardElement'
        gridKey = this.hasGrid ? 'gridInBoardElement' : 'isBoardElement'
      }
      // 该值在config-gauge中有用到
      return {
        chart: chartKey ? { padding: this.chartPadding[chartKey] } : {},
        grid: gridKey ? { padding: this.chartPadding[gridKey] } : {},
      }
    },
    isIndicatorDropDown() {
      return !this.content.indicatorSelectorShowType || this.content.indicatorSelectorShowType === 'dropDown'
    },
    indicatorSelectorShowType() {
      return this.content.indicatorSelectorShowType || 'dropDown'
    },
    indicatorSelectorShow() {
      if (this.content.indicatorSelectorShow === false) return false
      return true
    },
    noDataPermissions() {
      if (this.isDesensibilisationChart) return true
      if (this.utils.env?.projectName !== ALL_PROJECT_NAME.SUBSYSTEM) return false
      const { metricsContainer = {}, chartAlias, gaugeTarget = {} } = this.chartUserConfig
      // chartResponse内的metrics包括了目标值、散点图的size，所以从metricsContainer.default内取度量
      let metrics = metricsContainer.default ? metricsContainer.default.map(item => item.alias || item.labeName) : []
      // 隐藏图形特殊场景：散点图当X轴、Y轴、size轴其中一个字段为列权限字段；仪表盘当度量、目标值其中一个字段为列权限字段
      if (['ve-scatter-normal', 've-gauge-normal'].includes(chartAlias)) {
        if (chartAlias === 've-gauge-normal' && gaugeTarget.defaults) {
          metrics.push(gaugeTarget.defaults.alias || gaugeTarget.defaults.labeName)
        }
        return metrics.length && metrics.some(item => checkNoDataPermissions({ metric: item, chartResponse: this.content.chartResponse }))
      }
      return metrics.length && metrics.every(item => checkNoDataPermissions({ metric: item, chartResponse: this.content.chartResponse }))
    },
    isDesensibilisationChart() {
      const { chartAlias, liquidFillSetting = {}, gaugeTarget = {}, metricsContainer = {} } = this.chartUserConfig
      if (!Object.keys(this.content.chartResponse?.dataMaskRuleParamMap || {}).length) return false
      if ([CHART_ALIAS_TYPE.VE_TREE, CHART_ALIAS_TYPE.VE_GRID].includes(chartAlias)) return false
      const result = []

      const metricAll = metricsContainer.default || []
      const showMetric = filterFunc.call(this, metricAll, false)

      if (!showMetric.length) {
        result.push(...(filterFunc.call(this, metricAll, true)).map(m => this.getDatasetLabel(m)))
      } else if (showMetric.length !== 2 && chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) {
        result.push(...(filterFunc.call(this, metricAll, true)).map(m => this.getDatasetLabel(m)))
      } else if (showMetric.length !== metricAll.length && [CHART_ALIAS_TYPE.VE_STACK_PERCCENT, CHART_ALIAS_TYPE.VE_BAR_STACK_PERCENT].includes(chartAlias)) {
        result.push(...(filterFunc.call(this, metricAll, true)).map(m => this.getDatasetLabel(m)))
      } else if (chartAlias === CHART_ALIAS_TYPE.VE_SCATTER) {
        const metricFirst = metricsContainer.x || metricsContainer.histogram || []
        const metricSecond = metricsContainer.y || metricsContainer.line || []
        const metricList = metricFirst.concat(metricSecond)
        if (filterFunc.call(this, metricList, false).length !== 2) {
          result.push(...(filterFunc.call(this, metricList, true)).map(m => this.getDatasetLabel(m)))
        }
      }
      if (HAS_TARGET_CHART.includes(chartAlias)) {
        const gaugeAlias = gaugeTarget.type === 'expression' ? 'GAUGE_TARGET' : this.getDatasetLabel(gaugeTarget.defaults, true)
        if (this.content.chartResponse.dataMaskRuleParamMap[gaugeAlias] && (chartAlias !== CHART_ALIAS_TYPE.VE_LIQUIDFILL || liquidFillSetting.mode === 'normal')) {
          result.push(gaugeAlias)
        }
      }
      return result.join('，')
      function filterFunc(arr, hidden = true) {
        return arr.filter(m => {
          const alias = m.webFieldType ? this.getDatasetLabel(m, true) : (m.alias || m.labeName)
          return !!this.content.chartResponse?.dataMaskRuleParamMap?.[alias] === hidden
        })
      }
    },
    dimensionSelectorStyle() {
      let result = {}
      this.chartProps.toolbox.show && (result.transform = 'translateX(-110px)')
      if (this.element._containerId || !this.hasGrid) {
        (result.right = '25px')
      }
      return result
    },
    hasGrid() {
      return this.chartUserConfig.chartAlias === 've-grid-normal'
    },
    showGrid() {
      return this.isChartShow && this.hasGrid && this.isUpdateChart && !this.disableDrill
    },
    showChart() {
      return this.isChartShow && this.chartUserConfig.chartAlias !== 've-grid-normal' && (!this.isDecomposition) && (this.chartSettings.metrics.length || this.elName === 've-tree') && this.isUpdateChart && !this.disableDrill
    },
    switchDateDimension() {
      return this.content.alias !== 've-calendar' && this.chartUserConfig.switchDateDimension !== false
    },
    isRenderInteraction() {
      return this.$_getProp(this, 'element.elAttr.isRenderInteraction', false)
    },
    dateSwitchDIM() {
      const langCode = this.langCode
      const i18nCode = this.$i18n.locale
      const DIM = {
        Day: this.$t('sdp.views.Day'),
        Wk: this.$t('sdp.views.Week'),
        Mth: this.$t('sdp.views.Month'),
        Q: this.$t('sdp.views.Quarter'),
        HYr: this.$t('sdp.views.HalfYear'),
        Yr: this.$t('sdp.views.Year'),
      }
      if (this.utils?.intelligentData?.isIntelligentComponent && i18nCode !== langCode) {
        if (langCode === 'zh') {
          return {
            Day: '日',
            Wk: '周',
            Mth: '月',
            Q: '季度',
            HYr: '半年',
            Yr: '年'
          }
        } else if (langCode === 'en') {
          return {
            Day: 'Day',
            Wk: 'Wk',
            Mth: 'Mth',
            Q: 'Q',
            HYr: 'HYr',
            Yr: 'Yr'
          }
        } else {
          return DIM
        }
      } else {
        return DIM
      }
    },
    dateDimensionVal() {
      const DIM = this.dateSwitchDIM
      return [
          {
            value: DATE_DIMENSION_TYPE.Day_vs_day_d,
            label: DIM.Day,
          },
          {
            value: DATE_DIMENSION_TYPE.Day_vs_day,
            label: DIM.Day,
          },
          {
            value: 'week_vs_week',
            label: DIM.Wk,
          },
          {
            value: 'month_vs_month',
            label: DIM.Mth,
          },
          {
            value: 'quarter_vs_quarter',
            label: DIM.Q,
          },
          {
            value: 'half_year_vs_half_year',
            label: DIM.HYr,
          },
          {
            value: 'year_vs_year',
            label: DIM.Yr,
          }
        ]
    },
    selectedDateLabel() {
      const selectedDate = this.dateDimensionVal.find(item => item.value === this.selectedDateVal) || {}
      return selectedDate.label
    },
    timeDimensionSplittingRule() {
      let dateDimension = this.chartUserConfig.dimensionList.find(item => item.columnTpe === 'date')
      if (!dateDimension) {
        return ''
      }
      dateDimension = dateDimension.timeDimensionSplittingRule
      const timeDimensionSplittingRule = typeof dateDimension === 'string' ? dateDimension : dateDimension.timeDimensionSplittingRule
      return timeDimensionSplittingRule
    },
    // 图形日期控件选中值
    selectedDateVal: {
      get() {
        return this.chartUserConfig.selectedDateVal || this.timeDimensionSplittingRule
      },
      set(val) {
        this.$set(this.chartUserConfig, 'selectedDateVal', val)
      },
    },
    dimensionDateFormat() {
      const arr = ['yyyy-MM', 'yyyy']
      return Array.isArray(this.chartUserConfig.dimensionList) && this.chartUserConfig.dimensionList.length && arr.includes(this.chartUserConfig.dimensionList[0].dateFormat)
    },
    isShowTimeDimension() {
      return NEED_TIMEDIMENSION_CHART.includes(this.chartUserConfig.chartAlias) && this.chartUserConfig.dimensionList.length !== 2 && !this.dimensionDateFormat
    },
    // 是否显示钻取返回键
    isShowUpDrillButton() {
      if (this.isChildMapScheme) return !this.isMobile && !this.isChartSet
      return !this.isMobile && !this.isChartSet && Array.isArray(this.drillSettings.drillDimensions) && this.drillSettings.drillDimensions.length
    },
    drillDimensionsLabelList() {
      if (!this.drillSettings?.drillDimensions) return []
      const labelList =  this.drillSettings.drillDimensions.map(e => e.values.join(','))
      return labelList.length ? [this.$t('sdp.views.All'), ...labelList] : []
    },
    // 图表标签名和ref名字
    labelLineButtonShow() {
      // this.chartUserConfig.labelLineShow
      let check = false
      let listA = ['ve-pie', 've-ring', 've-line', 've-histogram', 've-bar', 've-scatter', 've-radar', 've-treemap', 've-funnel', 've-tree', 've-calendar', 've-map']
      let listB = ['ve-gauge']
      if (listA.includes(this.chartUserConfig.chartType)) {
        check = this.chartUserConfig.labelLineShow
      }
      if (listB.includes(this.chartUserConfig.chartType)) {
        check = this.$_getProp(this.chartUserConfig.gaugeTarget.settingGauge.dataDisplay, 'showData', false)
      }
      return check
    },
    elName() {
      return this.content.elName
    },
    // 图表的所有配置跟数据
    content() {
      return this.element.content
    },
    isInAdvanceContainer() {
      return this.isAdvanceContainer && this.element._containerId
    },
    //
    interactionSetting() {
      let setting = {}
      let interactionOptions = this.getFilteredInterActionOptions()
      if (interactionOptions && interactionOptions?.length) {
        setting = {
          clickAble: true,
          filedName: interactionOptions.map(e => e.columnName),
        }
      }
      return Object.assign({
        clickAble: false,
        filedName: '',
      }, setting)
    },
    isChart() {
      return this.elName.indexOf('element') === -1
    },
    // 判断是否需要展示仪表盘,是否设置目标值
    isGauge() {
      const { gaugeTarget = {} } = this.chartUserConfig
      return Array.isArray(this.drillSettings.layers) ? this.drillSettings.layers[0].gaugeTarget && ((gaugeTarget.type === 'expression' && gaugeTarget.exp) || (gaugeTarget.type === 'aggType' && gaugeTarget.defaults)) : false
    },
    isChartShow() {
      const chartAlias = this.chartUserConfig.chartAlias
      const { dimension, metrics } = this.chartSettings
      const { dimensionList = [] } = this.chartUserConfig
      // 需要设置目标值图形
      if (chartAlias === 've-gauge-normal') {
        return this.isGauge
      } else if (chartAlias === 've-liquidfill') {
        return this.isGauge && metrics.length && (this.chartUserConfig.liquidFillSetting.mode === VE_LIQUIDFILL_MODE.DIMENSION ? dimension.length : true)
      } else if (['ve-bar-percent', 've-ring-multiple'].includes(chartAlias)) {
        return this.isGauge && dimension.length && metrics.length
      } else if (['ve-sunburst'].includes(chartAlias)) {
        return dimension.length === 2 && metrics.length === 1
      } else if (['ve-themeRiver'].includes(chartAlias)) {
        return isThemeRiver(this.chartUserConfig)
      } else if (chartAlias === 've-funnel') {
        return (dimension.length === 1 && metrics.length > 0 && metrics.length <= 2) || (!dimension.length && metrics.length > 1)
      } else if (chartAlias === 've-grid-normal') {
        return dimension.length > 0 || metrics.length > 0
      } else if (chartAlias === 've-waterfall') {
        return (!dimension.length && metrics.length > 1) || (dimension.length && metrics.length === 1)
      } else if (chartAlias === CHART_ALIAS_TYPE.VE_BAR_BUTTERFLY) {
        return dimension.length === 1 && metrics.length === 2
      } else if (chartAlias === 've-calendar') {
        return dimensionList.length && dimensionList.every(d => d.columnTpe === 'date')
      } else if (chartAlias === 've-bar-Heatmap') {
        return dimensionList.length &&  dimensionList.length === 2 && metrics.length === 1
      } else {
        return (this.isGauge || dimension ? dimension.length : '' || metrics ? metrics.length : '' ||
      this.chartSettings.dataType ? this.chartSettings.dataType : '' ||
      this.chartSettings.position ? this.chartSettings.position : '')
      }
    },
    isRealScreen() {
      return isRealScreenFn(this.utils)
    },
    isMapFlyLineEnable() {
      const { dimensionDestinationList, mapSetting = {} } = this.chartUserConfig
      return !!(this.isMap && this.isRealScreen && mapSetting?.flyLine?.enable && dimensionDestinationList?.[0])
    },
    mapData() {
      let res = []
      const { dimensionList, metricAllList } = this.UserConfig
      Array.isArray(this.chartData.rows) && this.chartData.rows.forEach((item) => {
        const dimensionAlias = dimensionList[0]?.alias || dimensionList[0]?.labeName || ''
        const metricAlias = metricAllList[0]?.alias || metricAllList[0]?.labeName || ''
        let obj = {
          'name': item[dimensionAlias],
          'value': item[metricAlias],
          'VIEWFORMAT': item[`VIEWFORMAT_${ metricAlias }`],
          rowItem: item,
        }
        res.push(obj)
      })
      return res
    },
    mapFlyLineData() {
      if (!this.isMapFlyLineEnable) return []
      let res = []
      const { dimensionList, metricAllList } = this.UserConfig
      const { dimensionDestinationList } = this.chartUserConfig
      Array.isArray(this.chartData.rows) && this.chartData.rows.forEach((item) => {
        const dimensionAlias = dimensionList[0]?.alias || dimensionList[0]?.labeName || ''
        const metricAlias = metricAllList[0]?.alias || metricAllList[0]?.labeName || ''
        if (item.extendData?.length) {
          const destinationAlias = dimensionDestinationList[0]?.alias || dimensionDestinationList[0]?.labeName || ''
          item.extendData.forEach(extend => {
            let obj = {
              'name': extend[dimensionAlias],
              'value': extend[metricAlias],
              'VIEWFORMAT': extend[`VIEWFORMAT_${ metricAlias }`],
              rowItem: extend,
              nameDestination: extend[destinationAlias]
            }
            res.push(obj)
          })
        }
      })
      return res
    },
    minData() {
      let res = 0
      if (this.mapData.length) {
        res = Math.min(...this.mapData.map(e => e.value))
      }
      return res
    },
    maxData() {
      let res = 0
      if (this.mapData.length) {
        res = Math.max(...this.mapData.map(e => e.value))
      }
      return res
    },
    // 图表的数据
    chartData() {
      return this.content.chartData
    },
    // 图表的坐标轴字段
    chartSettings() {
      return this.content.chartSettings
    },
    // 图表配置
    chartConfig() {
      return this.content.chartConfig
    },
    // 钻取配置
    drillSettings() {
      return this.content.drillSettings
    },
    // 图表各种设定
    chartUserConfig() {
      return this.content.chartUserConfig
    },
    // 图表渲染的所有依赖项汇总(实时更新)
    chartProps() {
      const chartResponse = this.content.chartResponse
      // this.savePrintData()
      const settings = this.$_JSONClone(this._getSettingProp())

      // 散点图双维度处理，只需要传一个维度到Echarts，其他的到afterConfig的钩子中处理散点图
      const _data = this.$_JSONClone(this.chartData)
      if (this.content.alias === 've-scatter-normal' && chartResponse?.dimension?.length === 2) {
        settings.dimension.splice(1, 1)
        this.$set(settings, 'metrics', chartResponse.metrics.slice(1))
        _data.columns = [...chartResponse.dimension, ...chartResponse.metrics]
        // _data.columns.splice(0, 1)
      }

      this.updateRenderChart(true)
      const mergeConfig = this._mergeConfig(this.chartConfig)
      // 新建散点图设置数据过滤后报错
      if (this.chartData.rows && !Array.isArray(this.chartData.rows) && Object.keys(this.chartData.rows).length <= 0) {
        Object.assign(this.chartData, {
          rows: []
        })
      }
      const obj = {
        data: _data, // 图表显示数据
        settings, // 维度 和 度量 设置数据
        ...this.defaultChartConfig, // 图表默认配置数据
        // ...specChartConfig,
        ...this.$_deepClone(this.chartConfig), // 覆盖默认配置
        ...mergeConfig,
        dataSetId: this.drillSettings.dataSetId,
      }
      if (this.elName === 've-map') {
        obj.visualMap = this.mapOptions.visualMap
      }

      if ((this.chartUserConfig.chartAlias !== 've-themeRiver' && Object.keys(this.legendSelected).length)) {
        let legend = obj.legend
        Object.assign(legend, { selected: this.setLangLegendSelect(this.legendSelected) })
      }
      // 订阅界面不开启动画
      Object.assign(obj, {
        animation: {
          animation: !this.commonData.isSubscribe && !this.isChartLarge
        },
        title: {}
      })
      obj.grid.top = this.getPxOfHighResolution(24)
      obj.events.showTip = this.showTip
      obj.events.hideTip = this.hideTip
      return obj
    },
    showGrowthRate() {
      const chartType = CIRCULAR_CHART
      return chartType.includes(this.chartUserConfig.chartAlias)
    },
    // 图表的tab切换内容
    chioceTab: {
      get() {
        return this.element.content.chioceTab || []
      },
      set(val) {
        this.element.content.chioceTab = val
      },
    },
    chioceTabList() {
      // 卡片指标交互
      if (this.cardInteractionChioceTab && this.cardInteractionChioceTab.isActive && this.cardInteractionChioceTab.indicators?.length) {
        const tabIds = this.cardInteractionChioceTab.indicators
        return this.chioceTab.map((tab, index) => {
          const languageList = this.chioceTabLang.filter(e => e.key === `${this.element.id}_${tab.id}_indicatrixAlias`)
          const names = languageList.map(e => e.value)
          if (languageList.length <= 0) {
            if (tabIds.includes(tab.id)) {
              return { ...tab, index }
            }
          } else {
            for (let i in names) {
              if (tabIds.includes(names[i]) || tabIds.includes(tab.id)) {
                return { ...tab, index }
              }
            }
          }
        }).filter(e => e)
      }
      // metric参数组件
      if (this.metricChioceTab && this.metricChioceTab.isRelated) {
        const metricChioceTabName = this.metricChioceTab.metricChioceTab.map(mct => mct.name)
        return this.chioceTab.map((tab, index) => {
          const languageList = this.chioceTabLang.filter(e => e.key === `${this.element.id}_${tab.id}_indicatrixAlias`)
          const names = languageList.map(e => e.value)
          if (languageList.length <= 0) {
            if (metricChioceTabName.includes(tab.name)) {
              return { ...tab, index }
            }
          } else {
            for (let i in names) {
              if (metricChioceTabName.includes(names[i]) || metricChioceTabName.includes(tab.name)) {
                return { ...tab, index }
              }
            }
          }
        }).filter(e => e)
      }
      return this.chioceTab.map((ct, index) => ({ ...ct, index }))
      // if (this.metricChioceTab && this.metricChioceTab.isRelated) {
      //   const metricChioceTabName = this.metricChioceTab.metricChioceTab.map(mct => mct.name)
      //   return this.chioceTab.map((ct, index) => ({ ...ct, index })).filter(ct => metricChioceTabName.includes(ct.name))
      // }
      // return this.chioceTab.map((ct, index) => ({ ...ct, index }))
    },
    chioceTabmetaDashboardElementLanList() {
      return this.boardInfo?.metaDashboardElementLanList || []
    },
    chioceTabLang() {
      return this.chioceTabmetaDashboardElementLanList.filter(e => {
        // bug: 30007 【需求4775】跳转后，通过面包屑返回，图形的数据丢失，item.key有时候会为true，判断一下类型
        return e.key && [Array, String].includes(e.key) && e.key.indexOf(this.element.id) !== -1 && e.key.indexOf('_indicatrixAlias') !== -1
      }) || []
    },
    checkedDimension: {
      get() {
        return this.element.content.saveIndex || 0
      },
      set(val) {
        this.element.content.saveIndex = val
      },
    },
    // 包含图形标题区域
    chartClassName() {
      return this.isChartSet ? `_${this.element.id}-chartset` : `_${this.element.id}`
    },
    // canvas区域，不包含标题区域
    chartContentClass() {
      return this.isChartSet ? `chart_content_${this.element.id}_chartset` : `chart_content_${this.element.id}`
    },
    isMobile() {
      return this.terminal ? this.terminal === 'APP' : this.utils.isMobile
    },
    // 移动端需要分页展示的图形
    isMobilePageImg() {
      return this.isMobile && MOBILE_PAGE_CHART.includes(this.element.content.alias)
    },
    isMobileLegend() {
      const { chartType, legend } = this.element.content.chartUserConfig
      const noLegend = ['ve-map', 've-gauge', 've-grid-normal', 've-wordcloud'].includes(chartType)
      return this.isMobile && !this.isChartSet && legend !== '' && !noLegend
    },
    interactionOptions() {
      return this.content.interactionOptions || []
    },
    superLinkOptions() {
      return this.content.superLinkOptions || []
    },
    // 是否支持超链接跳转
    superLinkNeedClick() {
      return !!(!this.isChartSet && (this.screenType === 'vertical' || this.isMobile) && this.superLinkOptions?.length)
    },
    // 标题超链接跳转
    superLinkTitleClick() {
      let superLinkOptions = this.superLinkOptions
      if (this.chioceTab.length && this.chioceTab[this.content.saveIndex]) {
        const chioceId = this.chioceTab[this.content.saveIndex]['id']
        superLinkOptions = superLinkOptions.filter(item => `${item.labelBoard.site}`.indexOf(chioceId) > -1 && item.labelBoard.type !== SUPERLINK_CONST_TYPE.variavle)
      } else {
        superLinkOptions = superLinkOptions.filter(item => item.labelBoard.type !== SUPERLINK_CONST_TYPE.variavle)
      }
      return this.superLinkNeedClick && superLinkOptions
    },
    // 维度超链接跳转
    superLinkDimensionClick() {
      // 判断是否有关联数据集
      // const isAssociationDataset = this.$_getProp(this, 'chartUserConfig.datasetAssociation', false)
      let superLinkOptions = this.superLinkOptions
      if (this.chioceTab.length && this.chioceTab[this.content.saveIndex]) {
        const chioceId = this.chioceTab[this.content.saveIndex]['id']
        superLinkOptions = superLinkOptions.find(item => `${item.labelBoard.site}`.indexOf(chioceId) > -1 && item.labelBoard.type === SUPERLINK_CONST_TYPE.variavle)
      } else {
        superLinkOptions = superLinkOptions.find(item => item.labelBoard.type === SUPERLINK_CONST_TYPE.variavle)
      }
      return this.superLinkNeedClick && superLinkOptions
    },
    // 是否被交互
    isByTheInteraction() {
      return this.elList.some(item => {
        const interactionOptions = item.content.interactionOptions
        const flag = interactionOptions?.length && interactionOptions[0].associElements.some(eve => eve.id === this.element.id)
        return flag
      })
    },
    containerEl() {
      const { _containerId } = this.element
      return _containerId ? this.elList.find(elItem => elItem.id === _containerId) : false
    },
    isContainerAdaptation() {
      return this.containerEl ? this.containerEl.content.settings.adaptation : false
    },
    // 特殊日期格式的字段，日期函数列表限制点击
    dateDimensionValList() {
      const arr = ['yyyy-MM', 'yyyy']
      // 这里不需要判断是否符合出现日期维度切换下拉框了
      const dateDimensionList = this.chartUserConfig.dimensionList?.filter(d => d.columnTpe === 'date')

      const flag = dateDimensionList.length && arr.includes(dateDimensionList[0].dateFormat)
      let _dateDimensionVal = []
      if (flag) {
        switch (dateDimensionList[0].dateFormat) {
          case 'yyyy-MM':
            // todo kyz 7816
            const Marr = [DATE_DIMENSION_TYPE.Day_vs_day, DATE_DIMENSION_TYPE.Day_vs_day_d, 'week_vs_week']
            _dateDimensionVal = this.dateDimensionVal.filter(item => !Marr.includes(item.value))
            this.setSelectedDateVal(Marr.includes(this.selectedDateVal) ? _dateDimensionVal[0].value : this.selectedDateVal)
            break
          case 'yyyy':
            _dateDimensionVal = this.dateDimensionVal.filter(item => ['year_vs_year'].includes(item.value))
            this.setSelectedDateVal('year_vs_year')
            break
        }
      } else {
        _dateDimensionVal = this.dateDimensionVal
      }
      let dateDimensionItems = this.$_JSONClone(this.chartUserConfig.dateDimensionSetting?.dateDimensionItems)
      if (Array.isArray(dateDimensionItems) && dateDimensionItems.length) {
        // 替换DATE_DIMENSION_TYPE.Day_vs_day为DATE_DIMENSION_TYPE.Day_vs_day_d
        if (this.timeDimensionSplittingRule === DATE_DIMENSION_TYPE.Day_vs_day_d) {
          const index = dateDimensionItems.findIndex((val) => val === DATE_DIMENSION_TYPE.Day_vs_day)

          index !== -1 && dateDimensionItems.splice(index, 1, this.timeDimensionSplittingRule)
        }
        let result = []
        dateDimensionItems.forEach(di => {
          const item = _dateDimensionVal.find(d => di === d.value)
          if (item) result.push(item)
        })
        _dateDimensionVal = result
      } else {
        _dateDimensionVal = _dateDimensionVal.filter(item => item.value !== DATE_DIMENSION_TYPE.Day_vs_day_d)
      }
      return _dateDimensionVal
    },
    // 是否开启大屏
    themeFullScreen() {
      return this.themeData.themeFullScreen
    },
    // 主题类型
    themeType() {
      return this.themeData.themeType || THEME_TYPE.default
      // return (this.themeData.enableTheme || (this.isChartSet && this.themeData.screenMode)) ? this.themeData.themeType : THEME_TYPE.default
    },
    // 看板请求是否完成标识
    isBoardRequestFinish() {
      return this.configs ? !this.loading : this.commonData.getIsFinish()
    },
    selectClassName() {
      return (this.isContainer && this.fullscreen) ? 'container-select' : 'sdp-params-theme-element-background'
    },
    isLiquidfill() {
      return this.chartUserConfig.chartAlias === 've-liquidfill'
    },
    // 是否设置预设维度 双维度图形不存在预置维度
    presetDimensionLabel() {
      const dimensionList = this.chartUserConfig.dimensionList || []
      return dimensionList.length !== 2 && dimensionList.some(item => item.presetDimensionLabel)
    },
    notAppendToBody() {
      return this.themeFullScreen || !this.fullscreen
    },
    warnAppendToBody() {
      return this.fullscreen || this.fullscreenData.enlargeVisible
    },
    // 是否需要禁用钻取
    disabledDrill() {
      return this.isChartSet || (this.commonData.paramsType === RUN_TYPE.locationRun || NOT_SUPPORT_DIMENSION_CHART.includes(this.content.alias) || NOT_SUPPORT_DIMENSION_CHART.includes(this.content.chartUserConfig.childChartAlias))
    },
    titleStyle() {
      const textStyle = { ...this.chartUserConfig.title.textStyle }
      const themeTextStyle = getThemeConfig(
        this.themeType,
        {
          attributes: ['chartTitleConfig'],
        },
      )
      Object.keys(themeTextStyle.chartTitleConfig.textStyle).forEach(item => {
        let val = textStyle[item] || themeTextStyle.chartTitleConfig.textStyle[item]
        if (item === 'fontSize') {
          const _val = this.getPxOfHighResolution(val)
          val = String(_val).includes('px') ? _val : `${_val}px`
        }
        textStyle[item] = val
      })
      // const { type, angle, gradientColor} = textStyle
      // if (type === 'gradient') {
      //   return getGradientStyle(angle, gradientColor, textStyle)
      // }

      return textStyle
    },
    gradientColorStyle() {
      const textStyle = { ...this.chartUserConfig.title.textStyle }
      const { type, angle, gradientColor} = textStyle
      if (type === 'gradient') {
        return getGradientStyle(angle, gradientColor, textStyle)
      }
      return {}
    },
    showMeasureSummary() {
      if (!this.UserConfig.metricAllList.length || !this.chartUserConfig.metricsContainer.default.length) return false
      return this.hasChartData && this.showMeasureConfig && this.mesureDisPositon && !this.isNoData
    },
    showMeasureConfig() {
      const { extendDimensionList = [], chartAlias, measureConfig = {} } = this.chartUserConfig
      const { metrics = [], dimension = [] } = this.chartSettings
      // 单度量单维度场景取度量汇总配置，多度量取summaryList配置
      const hasMeasureSummarySetting = dimension.length === 1 && !extendDimensionList.length
      if (!hasMeasureSummarySetting) return false
      if (metrics.length === 1) {
        return GRAPHARR.includes(chartAlias) && measureConfig.hide ? 'SINGLE' : false
      } else if (measureConfig?.summaryList?.length) {
        const list = measureConfig?.summaryList.filter(item => item.visible)
        return MULTI_GRAPHARR.includes(chartAlias) && list.length && measureConfig.hide ? 'MULTIPUL' : false
      }
      return false
    },
    measureText() {
      const chartUserConfig = this.chartUserConfig
      const subtext = chartUserConfig?.measureConfig?.subtext
      if (!this.showMeasureConfig || !subtext) return ''
      const { measureSummaryAlias, measureSummaryValue } = chartUserConfig.measureConfig
      let lable = ''
      if (subtext.includes(';')) {
        // lable = subtext.split(';')[0] + `(${this.$t(`sdp.views.measure${chartUserConfig.measureConfig.measureSummaryValue}Show`) + ': ' + subtext.split(';')[1]})`
        lable = subtext.split(';')[0] + `(${measureSummaryAlias + ': ' + subtext.split(';')[1]})`
      } else {
        // const str = this.$t(`sdp.views.measure${measureSummaryValue}Show`)
        const str = measureSummaryAlias
        lable = subtext + (str ? `(${str})` : '')
      }
      return lable
    },
    measureStyle() {
      const subtextStyle = this.chartUserConfig.measureConfig.subtextStyle
      const fontSize = this.getPxOfHighResolution(subtextStyle.fontSize)
      return Object.assign({}, subtextStyle, { fontSize: String(fontSize).includes('px') ? fontSize : `${fontSize}px` })
    },
    mesureDisPositon() {
      const { displayPosotion } = this.chartUserConfig?.measureConfig
      return displayPosotion === 'disLeft' || !MEARSURE_DIS_CENTER.includes(this.chartUserConfig.chartAlias)
    },
    multiMeasureList() {
      let list = []
      const { measureConfig = {}, colorType, colors } = this.chartUserConfig
      const { summaryList = [] } = measureConfig
      if (this.showMeasureConfig !== 'MULTIPUL' || !summaryList.length) return list
      const measureSummaryConfig = getThemeConfig(this.themeType, { attributes: ['measureSummaryConfig'] }).measureSummaryConfig
      let metricAll = this.UserConfig.metricAllList || []
      metricAll = filterFunc.call(this, metricAll, false)
      list = summaryList.filter(item => item.visible && metricAll.some(m => m.keyName === item.keyName)).map(item => {
        const { subtext = '', subtextStyle, summaryValue, summaryAlias, keyName } = item
        const field = metricAll.find(me => me.keyName === keyName)
        const fieldIndex = metricAll.findIndex(me => me.keyName === keyName)
        const fontSize = this.getPxOfHighResolution(subtextStyle.fontSize)
        let showStyle = Object.assign({}, subtextStyle, { fontSize: String(fontSize).includes('px') ? fontSize : `${fontSize}px` })
        let showLabel = ''
        let colorList = colors // 都用度量颜色 colorType === 'metric' ? colors : []
        let iconColor = typeof colorList[fieldIndex] === 'object' ? (colorList[fieldIndex].type === 'gradient' ? `linear-gradient(${ colorList[fieldIndex].angle }deg, ${ colorList[fieldIndex].gradientColor[0] }, ${ colorList[fieldIndex].gradientColor[1] })` : colorList[fieldIndex].pureColor) : colorList[fieldIndex]

        const summaryLabel = summaryAlias ? `(${summaryAlias})` : ''
        if (subtext.includes(';')) {
          // showLabel = subtext.split(';')[0] + `(${this.$t(`sdp.views.measure${summaryValue}Show`) + ': ' + subtext.split(';')[1]})`
          showLabel = subtext.split(';')[0] + `(${summaryAlias + ': ' + subtext.split(';')[1]})`
        } else {
          // showLabel = `${field.lang_alias || this.getDatasetLabel(field)}: ${subtext}(${this.$t(`sdp.views.measure${summaryValue}Show`)})`
          showLabel = `${field.lang_alias || this.getDatasetLabel(field)}: ${subtext}${summaryLabel}`
        }
        if (!showStyle.color) {
          showStyle.color = measureSummaryConfig.subtextStyle.color
        }
        return {
          ...item,
          showLabel,
          showStyle,
          iconColor
        }
      })
      // 过滤敏感字段
      function filterFunc(arr, hidden = true) {
        return arr.filter(m => {
          const alias = m.webFieldType ? this.getDatasetLabel(m, true) : (m.alias || m.labeName)
          return !!this.content.chartResponse?.dataMaskRuleParamMap?.[alias] === hidden
        })
      }

      return list
    },
    // 放大 || 全屏
    isChartLarge() {
      return this.fullscreenData.enlargeVisible
    },
    // 大屏、全屏、放大时tooltip的DOM直接增加到父级内
    tooltipAppendToBody() {
      return this.themeFullScreen || !this.isChartLarge
    },
    mobileFullScreen() {
      return this.screenType === 'landscape'
    },
    watchChartSize() {
      return {
        // 监听图表所占的宽度格数自适应尺寸
        width: this.element.style.width,
        // 监听图表所占的高度格数自适应尺寸
        height: this.element.style.height,
        // 容器内图形宽度变化
        chartWidth: this.chartSettings.chartWidth,
        // 容器内图形高度变化
        chartHeight: this.chartSettings.chartHeight,
        // 页面窗口大小变化
        ...this.elCanvasSize,
      }
    },
    isPcHorizontalView() { // 是否为pc横屏
      return this.fullscreenData.isPcHorizontalView
    },
    isShowDescriptions() {
      return this.chartUserConfig.chartDescription && this.commonData.showElementDescription
    },
    labelLineButton() {
      return this.labelLineButtonList.length && this.labelLineButtonList[this.checkedDimension]?.labelLineButton
    },
    isCNY() {
      if (this.commonData.isPreview && this.commonData.boardSlectLang() === '') {
        return this.getBoardCurrency() === 'CNY'
      }

      return this.getBoardCurrency() === 'CNY' && (this.commonData.isPreview ? this.commonData.boardSlectLang() : this.langCode) === 'zh'
    },
    isShowCalendarButton() {
      const { chartAlias, calendarSettings = {} } = this.chartUserConfig
      return chartAlias === 've-calendar' && calendarSettings.openCalendarPage && calendarSettings.pagingMode !== 'slide'
    },
    // 是否设置自定义日期维度
    isTimeSplitingOffsetChart() {
      const { dimensionList = [], chartAlias } = this.chartUserConfig
      const hasTimeSplitingOffset = dimensionList.find(item => item.columnTpe === 'date' && item.timeDimensionSplittingOffset)
      return hasTimeSplitingOffset && CUSTOM_DATE_DIMENSION_CHART.includes(chartAlias)
    },
    // 设置日期偏移的图形，按天展示时，不存在时间维度拆分，交互和钻取时需要取真实日期
    specialTimeSplitOffset() {
      const { switchDateDimension } = this.chartUserConfig
      const { timeDimensionSplitting } = this.drillSettings
      // todo kyz 7816
      return this.isTimeSplitingOffsetChart && [DATE_DIMENSION_TYPE.Day_vs_day, DATE_DIMENSION_TYPE.Day_vs_day_d].includes(this.selectedDateVal) && switchDateDimension && timeDimensionSplitting !== '1'
    },
    isShowFullFilterSorter() {
      const { enlargeVisible, enlargeEl } = this.fullscreenData
      return enlargeVisible
    },
    // 筛选器是否平铺
    isFilterSortTile() {
      return !this.isContainer && !this.isChartSet && (this.isShowFullFilterSorter || this.themeData.themeFullScreen || (!!this.filterDisplayMode && this.filterDisplayMode === FILTER_DISPLAY_MODE.Tile))
      // return (!this.isShowFullFilterSorter && !this.themeData.themeFullScreen) && !this.utils.isScreen && !!this.filterDisplayMode && this.filterDisplayMode === FILTER_DISPLAY_MODE.Tile && !this.isContainer
    },
    filterDisplayMode() {
      return this.element.filterSortDisplayMode
    },
    isNoData() {
      return this.hasChartData && this.content?.chartResponse?.rows?.length === 0
    },
    // PC多度量汇总容器宽度
    multiMeasureWidth() {
      const _ref = this.$refs.titleRightRef
      let w = this.watchChartSize.canvasOffsetWidth - (this.isMobile ? 22 : (16 + 20))
      if (_ref && !this.chartUserConfig.title.text && !this.isMobile) {
        let refW = _ref.clientWidth
        w += 20
        w = w - (refW + 24)
      }

      if (!this.chartUserConfig.title.text && this.borderInfo.isShowStateIcon && (this.isInteractive || this.isSuperLink || this.isDrillList)) {
        w -= 5
        if (this.isInteractive) {
          w -= 18
        }
        if (this.isSuperLink) {
          w -= 18
        }
        if (this.isDrillList) {
          w -= 18
        }
      }
      return w
    },
    // 被卡片指标形式交互的状态
    cardInteractionChioceTab() {
      return this.element.cardInteractionState || { mainId: '', columnName: '', indicators: [], isActive: false }
    },
    isInteractive() { // 是否是交互状态（不用混入里面的 因为考虑有指标卡片）
      try {
        // const setting = this.interactionSetting
        // const hasInteraction = setting.clickAble
        // return hasInteraction
        let interactionOptions = this.getFilteredInterActionOptions()
        return interactionOptions[0]?.associElements.length
      } catch (e) {
        return false
      }
    },
    isSuperLink() {
      try {
        return (this.superLinkTitleClick?.length > 0 || !!this.superLinkDimensionClick)
      } catch (e) {
        return false
      }
    }
  },

  watch: {
    refershComponentIds(ids) {
      setTimeout(() => {
        if (ids.includes(this.element.id)) {
          if (this.showGrid) {
            this.createTable({ firstRender: !!this.showGrid, from: 'set-save', getCanvasSize: false })
            this.updateChartSettings(true)
          }

          this.refreshEl({
            ids: [this.element.id],
          })
        }
      }, 1000)
    },
    themeType: {
      handler(val, oldVal) {
        // 看板设计界面走另外的初始化逻辑，
        // oldval=undefined表示是第一次打开图形，如果当前主题是白的，则不需修改chartuserconfig
        if ((!this.configs || (this.configs && this.configs.type !== 'template')) && this.isChartSet) return
        if (this.chioceTab && this.chioceTab.length) {
          this.chioceTab.forEach(ct => {
            setTheme.call(this, ct.saveObj)
          })
          this.$_filterDeepClone(this.content.chartUserConfig, this.content.chioceTab[this.content.saveIndex].saveObj.chartUserConfig, [])
          this.$_filterDeepClone(this.content.chartConfig, this.content.chioceTab[this.content.saveIndex].saveObj.chartConfig, [])
        } else {
          setTheme.call(this, this.content)
        }
        setThemeContent.call(this, this.content, val, this)
        function setTheme(content) {
          if (oldVal === undefined && (!content.chartUserConfig.themeConfig || this.$_isEmptyObject(content.chartUserConfig.themeConfig[THEME_TYPE.default]))) {
            setThemeConfig(content.chartUserConfig, THEME_TYPE.default)
            // 将所有主题色的默认色系存入themeConfig， bug：74952
            // 优先取themeConfig[key]
            // 新增的图形，如果有全局色系则使用全局色系
            // 如果有业务设置的默认色系，则使用业务默认色系
            // 否则用默认
            let busiChartColorScheme = this?.sdpBus?.busiChartColorScheme
            let globalColorScheme = this?.globalChartColorScheme || this?.boardInfo?.globalChartColorScheme
            let themeConfigs = content.chartUserConfig.themeConfig
            const _setColorScheme = (themeType) => {
              let defaultBusi = ''
              let themeConfig = themeConfigs[themeType] || {}
              if (busiChartColorScheme && busiChartColorScheme[themeType]) {
                defaultBusi = busiChartColorScheme[themeType].colorScheme
              }
              let defaultScheme = themeConfig.colorScheme || defaultBusi || 'default'
              if (globalColorScheme && globalColorScheme[themeType]) {
                defaultScheme = globalColorScheme[themeType].colorScheme
              }
              this.$set(themeConfig, 'colorScheme', defaultScheme)
            }
            _setColorScheme(THEME_TYPE.default)
            _setColorScheme(THEME_TYPE.darkBlue)
            _setColorScheme(THEME_TYPE.deepBlue)
          }
          setThemeChartUserConfig.call(this, content.chartUserConfig, val, content)
        }
      },
      immediate: true,
    },
    isFilterSortTile() {
      this.refreshChart({ source: 'reset chart' })
    },
    interactionOptionsValues(val) {
      if (!this.chartUserConfig?.animationSetting?.enable) {
        if (this.isMobile && !this.isChartSet) return
        this.resetAfterConfig({ isMapInit: true })
      }
    },
    'fullscreenData.enlargeVisible'() {
      this.selectShow = false
      this.$nextTick(() => {
        this.selectShow = true
      })
    },
    fullscreen() {
      // 全屏切换的时候要让element的select重新生成一下，不然会导致select无法选择
      this.selectShow = false
      this.$nextTick(() => {
        this.selectShow = true
      })
    },
    loading: {
      handler(_loading) {
        if (!this.hasChartData && _loading && !this.isRenderInteraction) {
          this.setHasChartData(true)
        }
      },
      immediate: true,
    },
    renderChart: {
      handler(val) {
        // 当图形配置完成后，无法继续调用 charset 组件中的方法
        // 需要对色带和四象线重新渲染
        if (val) {
          this.clearMark()
          setTimeout(() => {
            this.renderChart = false
            this.chartUserConfig && this.chartUserConfig.quadrant && handleFourQuadrantLine(this.chartConfig, this.getEchartsInstance, this.chartConfig.markLineColor)
          }, 300)
        }
      },
      immediate: true,
    },
    'labelLineButton': {
      handler(showArea) {
        this.resetAfterConfig()
      },
    },
    // // 监听组合图形---折线面积图
    'chartUserConfig.compositeChart.area': {
      handler(showArea) {
        this.resetAfterConfig()
      },
      immediate: true,
    },
    // disableDrill
    'isBoardRequestFinish': {
      handler(val) {
        if (this.disableDrill) {
          this.onUpDrill('', false, true)
        }
      },
    },
    watchChartSize: {
      handler(newSize, oldSize) {
        let isSizeChange = Object.keys(newSize).some(key => oldSize[key] && newSize[key] && (newSize[key] !== oldSize[key]))
        if (isSizeChange) {
          this.resizeEcharts()
          this.disabledBgTrendFlag = false
          this.updateScrollNavBarItemsWidth()
        }
        if (!this.refreshWithResizeCharts.includes(this.chartUserConfig.chartAlias)) return
        if (isSizeChange) {
          this.resetAfterConfig({ source: 'resizeChart', isMapInit: true, isSizeChange })
        }
      },
      deep: true
    },
    'chartUserConfig.liquidFillSetting.color'() {
      if (this.chartUserConfig.chartAlias !== 've-liquidfill') return
      this.setUpdateChart()
    },
    'chartUserConfig.isLabelLogogram'() {
      if (this.chartUserConfig.chartAlias !== 've-pictorialbar') return
      this.setUpdateChart()
    },
    // 容器内图形开启自适应或非自适应
    isContainerAdaptation() {
      this.resetAfterConfig({ isMapInit: true })
    },
    chartData() {
      const { columns } = this.chartData
      if (this.isPie && columns.length > 1) {
        const primaryKey = columns[0]
        this.compareKey = columns[1]
        this.formatterPrevData = {}
        this.formatterNowData = {}
        // 将数据改为对象形式,避免查找时多次循环
        // TODO: 将此处的 this.chartData.rows 改为同比数据
        this.chartData.rows.forEach(data => {
          this.formatterPrevData[data[primaryKey]] = data
        })

        this.chartData.rows.forEach(data => {
          this.formatterNowData[data[primaryKey]] = this.$_JSONClone(data)
        })
      }
    },
    interactionOptions: {
      handler(val, oldVal) {
        // 如果有指标选择器，需要筛选当前指标选择器的值
        val = this.getFilteredInterActionOptions(val)
        const _val = val[0]
        if (!_val || (_val && (!Array.isArray(_val.values) || (!_val.values[0] && _val.values[0] !== '')))) {
          this.interactionOptionsValues = null
        }
        // 监听交互设置简单表格
        // (!this.isChartSet && oldVal !== undefined && this.chartUserConfig.chartAlias === 've-grid-normal') && this.createTable()
      },
      immediate: true,
      deep: true,
    },
    'commonData.isPreview'() {
      // 预览时关闭移动端指标选择器弹窗
      this.picker && this.picker.$el.remove()
      if (this.hasGrid) {
        this.justCellText({ from: 'commonData.isPreview', preventRequest: false })
      } else if (!this.utils.isScreen || this.utils.isDataReport) {
        if (this.utils?.isDailyConcerned) return

        this.refreshEl({
          ids: [this.element.id],
        })
      }
      // Object.keys(this.echartInstance).length && this.echartInstance.setOption({
      //   legend: {
      //     selected: {}
      //   }
      // })
      setTimeout(() => {
        this.metricUpdateAfterPreview()
      })
    },
    screenType: {
      handler() {
        // 移动端横竖屏切换关闭移动端指标选择器弹窗
        this.picker && this.picker.$el.remove()
        this.resetAfterConfig({ retainScroll: false, })
      },
      immediate: true
    },
    'element.isFocus'(val) {
      this.$set(this.tableElement, 'isFocus', val)
      // if (this.isMobile && !this.isChartSet && this.element.opable) return
      // 移动端预览状态下聚焦后datazoom才生效
      if (!this.isMobile || !this.commonData.isPreview || !DATAZOOM_CHART.includes(this.chartUserConfig.chartAlias)) return
      Object.keys(this.echartInstance).length && this.echartInstance.setOption({
        dataZoom: {
          type: 'inside',
          disabled: !val,
        }
      })
    },
    'chioceTab.length'(val) {
      this.setLabelLineButtonList()
      this.updateScrollNavBarItemsWidth()
    },
    themeFullScreen() {
      this.$nextTick(() => {
        if (!this.hasGrid) return
        this.$set(this.tableElement, 'scale', this.element.scale)
      })
    },
    // 监听大屏TV是否开启动画
    isPlayAnimation: {
      handler(val) {
        this.screenTvPlayAnimation(val)
      }
    },

    'element.content.tabWidthType': {
      handler(val) {
        this.updateScrollNavBarItemsWidth()
      },
      deep: true
    },
    'element.content.tabWidthValue': {
      handler(val) {
        this.updateScrollNavBarItemsWidth()
      },
      deep: true
    },
    isIndicatorDropDown(val) {
      if(!val) {
        this.updateScrollNavBarItemsWidth()
      }
    }
  },

  created() {
    // 将this挂载在element里面
    const vm = this
    Object.defineProperty(this.element, 'vm', {
      enumerable: false,
      configurable: true,
      get() {
        return vm
      },
    })
    if (this.isMobile) {
      this.chartClamp = this.mobileFullScreen ? 1 : 2
    }
    // 兼容代码
    this.compatibilityChart()
    this.hiddenSelect = this.$_throttle(this.hiddenSelect)
    this.calRateCompletion = this.$_debounce(this.calRateCompletion)
    this.setUpdateChart = this.$_debounce(this.setUpdateChart)
    this.animationPlay = this.$_debounce(this.animationPlay, 300)
    this.changeCalendarRange = this.$_debounce(this.changeCalendarRange)
    this.refreshChart = this.$_debounce(this.refreshChart, 300)

    initFilterSorterHandler(this.element, this.getBoardStatus()) // roc todo 3300: enter edit mode
  },
  mounted() {
    if (this.isMobile && !this.isChartSet) {
      window.sdpTooltipDispatchAction = this.sdpTooltipDispatchAction
      try {
        // 乾坤框架通信，同步子应用和主应用的信息，主要是方法、属性
        this.$bus.$emit('setGlobalState', {
          type: 'syncChildAppAndMainApp',
          data: {
            key: 'sdpTooltipDispatchAction',
            value: this.sdpTooltipDispatchAction
          }
        })
      } catch (e) {
        console.error(e)
      }
    }
    this.UserConfig.init()
    if (!this.isMobile) {
      this.setHiddenSelect(document.querySelector('#data-screen-table'))
    }

    // if (this.isMobile && !this.isChartSet) {
    //   this.$set(this.element, 'opable', true)
    // }
    const chioceTab = this.element.content.chioceTab || []
    if (!this.labelLineButtonList.length || (chioceTab.length && !this.labelLineButtonList[0].hasOwnProperty(id))) {
      // 设置显示数据按钮初始值
      this.setLabelLineButtonList()
    }
    // 不论是否是简单表格，都需要监听元素变化，防止有其他图形切换成简单表格
    this.$nextTick(() => {
      // erd.listenTo(this.$el, (element) => {
      //   // 小程序IOS端不会进入改回调
      //   this.$nextTick(() => {
      //     if (this.hasGrid) {
      //       this.getCanvasSize({ from: 'mounted' })
      //     } else {
      //       this.resizeWindow()
      //     }
      //   })
      // })
      if (this.element._containerId) {
        // this.resizeWindow()
      }
    })
    this.resetAfterConfig = this.$_debounce(this.resetAfterConfig, 100)

    this.tempSaveIndex = this.element.content.saveIndex || 0
    // 指标标签展示方式
    let isCallback = false
    ;(function listenScrollWrapperSize() {
      if (this.isPC) return

      const scrollWrapperDOM = this.getScrollWrapperDOM()

      if (!scrollWrapperDOM) return

      erd.listenTo(scrollWrapperDOM, elem => {
        this.updateScrollNavBarItemsWidth()
        isCallback = true
      })
    }.bind(this))()
    setTimeout(() => {
      if (!isCallback) {
        this.updateScrollNavBarItemsWidth()
      }
    }, 3000)
  },
  beforeDestroy() {
    const scrollWrapperDOM = this.getScrollWrapperDOM()
    erd && scrollWrapperDOM && erd.uninstall(scrollWrapperDOM)
  },
  destroyed() {
    this.picker && this.picker.$el.remove()
    this.clearAnimation()
    erd.uninstall(this.$el)
  },
  methods: {
    checkTipShow,
    handleExportTableData() {
      this.sdpBus?.$emit(EVENT_BUS.EXPORT_TABLE_DATA, this.element.id)
    },
    handleTipRefresh(height) {
      this.tipRenderHeight = height
      this.resizeEcharts()
    },
    handleCloseTipPopover() {
      const ref = this.$refs.tipPopover
      if (ref) {
        if (Array.isArray(ref)) {
          ref.forEach(e => e.doClose())
        } else {
          ref.doClose()
        }
      }
    },
    getScrollWrapperDOM() {
      try {
        return this.$refs.scrollNavBarRef?.$el.querySelector('.cube-scroll-wrapper')
      } catch (e) {
        console.error(e)
        return null
      }
    },
    getLegendsHiddenData() {
      // 这里等待是为了 echarts 第一次渲染完成，需要取渲染后数据
      setTimeout(() => {
        if (!this.echartInstance.getOption) {
          this.closeLoading()
          return void 0
        }
        this.legendChange()
      }, 2000)
    },
    // 获取置灰图例过滤数据
    getLegendsHidden() {
      if (!this.echartInstance.getOption) {
        return void 0
      }
      // 维度和扩展维度列表
      // 扩展维度名称列表
      const UserConfig = this.$_deepClone(this.UserConfig)
      const content = this.$_deepClone(this.content)
      const chartUserConfig = this.$_deepClone(this.chartUserConfig)
      const dimensionList = UserConfig.dimensionList.map(item => {
        return item.alias || item.labeName
      })
      const dimensionExtendList = UserConfig.dimensionExtendList.map(item => {
        return item.alias || item.labeName
      })
      const dimensionListData = []
      const filters = [
        ...UserConfig.dimensionList.map((item, index) => {
          const drillData = this.$_deepClone((this.drillSettings?.drillDimensions || []).find(d => d.keyName === (item.alias || item.labeName))) || null

          return {
            "groupFilterType": "and",
            "filterType": "in",
            "filterValueType": "value",
            "dataSetId": drillData?.dataSetId || item.parentId,
            "columnName": drillData?.columnName || item.alias || item.labeName,
            "columnType": drillData?.columnType || item.columnTpe,
            "values": content.chartResponse.rows.map((item1, index1) => {
              if (!dimensionListData[index1]) {
                dimensionListData[index1] = []
              }
              dimensionListData[index1][index] = item1[`VFMT_RAW_${item.alias || item.labeName}`]
              return item1[`VFMT_RAW_${item.alias || item.labeName}`]
            })
          }
        }),
        ...UserConfig.dimensionExtendList.map((item, index) => {
          return {
            "groupFilterType": "and",
            "filterType": "in",
            "filterValueType": "value",
            "dataSetId": item.parentId,
            "columnName": item.alias || item.labeName,
            "columnType": item.columnTpe,
            "values": (content?.chartResponse?.rows || []).map((item1, index1) => {
              return (item1.extendData || []).filter(item2 => !item2.tag_others).map(item2 => item2[`VFMT_RAW_${item.alias || item.labeName}`])
            }).flat()
          }
        })
      ]
      // 获取置灰图例
      const options = this.echartInstance.getOption()
      // 销毁上一个图
      if (this.isChartSet) {
        this.loadingFlag = true
        this.$nextTick(() => {
          this.loadingFlag = false
        })
      }

      // 获取保存的置灰图例
      let saveLegendSelected = null
      const { chartAlias, extendDimensionList = [], extendDimensionColor , sameExtendedDimensionSameColor} = chartUserConfig
      if (LEGEND_SETTING_SAVE_CHART.includes(chartAlias) && chartUserConfig.saveLegendSelected) {
        if (extendDimensionList.length && !extendDimensionColor && !sameExtendedDimensionSameColor) {
          saveLegendSelected = null
        } else {
          saveLegendSelected = chartUserConfig.saveLegendSelected
        }
      }
      // 优先获取当前用户操作的图例状态，如果没有则获取保存图例配置功能的
      const selected = options?.legend?.[0]?.selected || saveLegendSelected || {}
      // 下钻后处理下钻数据
      if (this.hasDrillData) {
        this.tempDrillLegendsSelected = this.$_deepClone(selected)
      } else {
        this.tempLegendsSelected = this.$_deepClone(selected)
      }
      // 获取所有置灰图例名称
      const disabledNames = Object.entries(selected).filter(([key, value]) => {
        return value === false
      }).map(item => item[0])


      const disabled = this.UserConfig.metricAllList.map(item => {
        const disabledSeries = options.series.filter(item1 => {
          return ((item1.originalMetricAlias || item1.originalName) === (item.alias || item.labeName)) && disabledNames.includes(item1.name)
        })
        const values = []
        disabledSeries.forEach(disabledSerie => {
          return disabledSerie?.data?.forEach((item, index) => {
            const data = dimensionExtendList.map((item2, index2) => {
              return (item.row?.[`VFMT_RAW_${item2}`] || null)
            })
            values.push([...dimensionListData[index], ...data])
          })
        })
        return {
          "metric": item.alias || item.labeName,
          "extendDimension": [...dimensionList, ...dimensionExtendList],
          "values": values
        }
      })
      const legendsHidden = {
        filters: filters,
        disabled: disabled
      }
      console.log('legendsHidden', legendsHidden);
      return this.$_deepClone(legendsHidden)
    },
    legendChange() {
      // 并不是只有图例切换才会调用，请先看调用位置
      if (this.isIgnoringUnselectedMetrics) {
        this.isLegendsHiddenCall = true
        this.isChartSet ? this.$emit('preview-chart', { legendsHidden: this.getLegendsHidden() }) : this.refreshEl({
          ids: [this.element.id],
        })
      }
    },
    // 此方法内一律不允许使用this,否则会出现操作错元素的情况
    sdpTooltipDispatchAction(type, elId, _this) {
      if (!this.isMobile || this.isChartSet) return
      // console.log('YL>', type, elId, _this, _this.parent)
      // console.log('YL> parent', _this.parentNode, _this.parentNode.parentNode)

      const el = this.elList.find(e => e.id === elId)
      if (!el) return
      if (type === 'close') {
        _this.parentNode.parentNode.style.display = 'none'
        el.vm.echartInstance.dispatchAction({
          type: 'hideTip'
        })
        return
      }
      if (type === 'interaction') {
        el.vm.handleChartsMockClick(type)
        return
      }
      if (type === 'drill') {
        el.vm.handleChartsMockClick(type)
        return
      }
      if (type === 'superLink') {
        el.vm.handleChartsMockClick(type)
        return
      }
    },
    handleChartsMockClick(type) {
      if (!this.isMobile || this.isChartSet) return
      if (!this.currentClickData) return
      const e = this.currentClickData
      if (this.shareData.isFilterHold) return void '正在筛选时不支持点击图形'
      if (e.seriesId === 'rank' && this.isBandwidth) {
        return void '排名不交互钻取'
      }
      // 双维度的分隔线不需要点击事件,树图跟节点不需要（pc端支持点击主维度标签进行交互、超链接）
      const allowClickParentDimensionLabel = e.data?.parentDimensionName && !this.isMobile
      if ((e.componentType === 'markPoint' && !allowClickParentDimensionLabel) || e.selfType === 'breadcrumb' || (e.seriesType === 'tree' && e.dataIndex === 1)) {
        return
      }
      // 地图跳转需要记录上次点击的地理信息
      if (this.content.chartUserConfig.childChartAlias === 've-map-world' && e.componentType !== 'title') {
        if (this.mapSuperlinkData && this.mapSuperlinkData.name === e.name) {
          this.mapSuperlinkData = undefined
        } else {
          this.mapSuperlinkData = {
            name: e.name,
            seriesName: e.seriesName
          }
        }
      }
      // 旭日图点击父级不需要钻取、交互
      if (!this.isMobile && this.chartUserConfig.chartAlias === 've-sunburst' && e.data.hasOwnProperty('children')) return
      // 水滴图内部不需要点击事件
      if (e.componentSubType === 'liquidFill') return
      if (['ve-map-china'].includes(this.content.chartUserConfig.childChartAlias) && (e.componentType === 'geo' || e.data === undefined)) return
      this.handleChart(e)

      // 以下无需同步点击事件
      const { activeDimension, result } = this.getMobileClickActive(e)

      const curDimension = result.find(dimensionItem => dimensionItem.dimension.join('-') === activeDimension.join('-'))
      if (curDimension && curDimension.disableClick) return

      let clickDimension = [...activeDimension].reverse()

      // 设置自定义日期维度时，弹窗展示数据和交互钻取数据不同，需要特殊处理
      if (curDimension?.labelDimension) {
        clickDimension = curDimension.labelDimension
      }
      if (type === 'interaction') {
        this.handleChart(clickDimension, true)
      } else if (type === 'superLink') {
        this.handleChart(clickDimension, true)
      } else if (type === 'drill') {
        this.handleChart(clickDimension, true)
      }
    },
    isSetDrill() {
      if (this.currentClickData && this.chartUserConfig?.childChartAlias === 've-map-world' && this.content?.mapSchemeSetting?.schemeList?.length) {
        const countryMap = {
          // 弄成数组是方便查找，如果世界地图做了多语言，那就往数组里面加对应的字符串就行
          china: ['中国'],
          britain: ['英国'],
        }
        const { activeDimension, result } = this.getMobileClickActive(this.currentClickData)
        const thisCountry = Object.keys(countryMap).find(k => countryMap[k].includes(activeDimension[0]))
        if (thisCountry) {
          const schemeIndex = this.content.mapSchemeSetting.schemeList.findIndex(s => s.saveObj.chartUserConfig.childChartAlias === `ve-map-${thisCountry}`)
          if (schemeIndex > -1) {
            return true
          }
        }
      }
      const drillList = this.$_getProp(this.content, 'drillList', [])
      const drilledLength = this.$_getProp(this.content, 'drillSettings.drillDimensions', []).length
      const dimensionList = this.$_getProp(this.content, 'chartUserConfig.dimensionList', [])
      const flag = drillList.length !== drilledLength / (dimensionList.length === 2 ? 2 : 1)
      // 设置预置维度标签，禁止钻取
      return dimensionList.length && drillList.length && flag
    },
    // 大屏TV主动调用播放动画的方法
    screenTvPlayAnimation(isPlayAnimation) {
      if (!this.utils.isTvScreen) return
      if (isPlayAnimation) {
        if (ANIMATION_CHART.includes(this.element.content.alias)) {
          this.animationInstance.continue ? this.animationInstance.continue() : this.animationPlay({ source: 'screenTvPlayAnimation' })
        } else if ([...ANIMATION_HISTOGRAM_CHART, ...ANIMATION_TOOLTIP_CHART].includes(this.element.content.alias)) {
          this.histogramAnimationInstance?.continue ? this.histogramAnimationInstance.continue() : this.histogramAnimation({ source: 'response', chartData: this.element.content.chartData })
        }
      } else {
        this.animationInstance.clear && this.animationInstance.clear()
        this.histogramAnimationInstance?.stop && this.histogramAnimationInstance.stop()
      }
    },
    checkIsGradient,
    // 图表元素预警参数
    getElementWarningData() {
      const elementWarningDataInstance = new ElementWarningData(
        this.boardWarningSubscribeData,
        this.boardInfo,
        this.themeData,
        this
      )
      const elementObj = elementWarningDataInstance.getChartWarningData(this.element, this)
      return elementObj
      // const { id, elName } = this.element
      // const elementInfoLanguageConfigList = [
      //   {
      //     // 元件名称
      //     key: 'elName',
      //     value: this.element.elName,
      //     languageId: id,
      //   },
      // ]
      // const warningList = []
      // const originWarningObj = this.boardWarningSubscribeData.originWarningList?.find(v => v.webElementId === id) || {}

      // const chioceTab = this.chioceTab?.length > 1 ? this.chioceTab : [{ saveObj: this.element.content, isOrigin: true, }]
      // chioceTab.forEach((contentItem, cIndex) => {
      //   const indicatorConfig = contentItem.isOrigin ? {} : { id: contentItem.id, name: contentItem.name, index: cIndex, }
      //   if (contentItem.saveObj.mapSchemeSetting?.schemeList?.length) {
      //     contentItem.saveObj.mapSchemeSetting.schemeList.forEach(val => {
      //       const childChartAlias = val.saveObj.chartUserConfig.childChartAlias
      //       const languageSpecialKey = childChartAlias === 've-map-world' ? '' : ('$ms$' + childChartAlias)
      //       warningList.push(...this.getWarningList(val.saveObj, originWarningObj, indicatorConfig, languageSpecialKey))
      //     })
      //   } else {
      //     warningList.push(...this.getWarningList(contentItem.saveObj, originWarningObj, indicatorConfig))
      //   }
      //   elementInfoLanguageConfigList.push({
      //     // 图表标题
      //     key: contentItem.isOrigin ? 'elTitle' : `elTitle_${contentItem.id}`,
      //     value: contentItem.saveObj.chartUserConfig.title?.text || '',
      //     languageId: `${ id }_${ contentItem.isOrigin ? 'x' : indicatorConfig.index }_chartTitle`,
      //   })
      //   // 添加指标选择器多语言
      //   if (!contentItem.isOrigin) {
      //     elementInfoLanguageConfigList.push({
      //       // 指标选择器里面的名称
      //       key: `indicator_${contentItem.id}`,
      //       value: contentItem.name,
      //       languageId: `${ id }_${ contentItem.id }_indicatrixAlias`,
      //     })
      //   }
      // })

      // // 有标题就用标题，没标题就用元件名称，指标选择器用元件名称
      // const titleLanguageObj = elementInfoLanguageConfigList.find(e => e.key === 'elTitle')
      // if (titleLanguageObj?.value) {
      //   elementInfoLanguageConfigList.push({
      //     ...titleLanguageObj,
      //     key: 'elementName',
      //   })
      // } else {
      //   const elNameLanguageObj = elementInfoLanguageConfigList.find(e => e.key === 'elName')
      //   elementInfoLanguageConfigList.push({
      //     ...elNameLanguageObj,
      //     key: 'elementName',
      //   })
      // }

      // const elementObj = {
      //   webElementId: id,
      //   elementName: titleLanguageObj?.value || elName,
      //   elementType: '1',
      //   languageConfig: elementInfoLanguageConfigList,
      //   elementId: originWarningObj.elementId,
      //   warningList,
      // }
      // if (!this.configs || this.configs.type !== 'template') {
      //   this.boardWarningSubscribeData.updataWarningLanguageConfig(elementObj, this.boardInfo?.moreLanguageList, this.element)
      // }
      // return elementObj
    },
    getWarningList(content, originWarningObj, indicatorConfig = {}, languageSpecialKey = '') {
      const CALC_TYPE = {
        fixed: 'fixed',
        other: 'otherField',
        cell: 'cell',
      }
      const { metricsContainer, warnLineSettingList = [], dimensionValueIndicatorWarningList = [], dimensionList = [], barchartWarnType, linearDirectionType, chartAlias, warningMethod = {}, extendDimensionList = [] } = content.chartUserConfig

      const subscribeWarningArray = [...warnLineSettingList]
      if (DIMENSION_VALUE_INDICATOR_WARNING_CHART.includes(chartAlias) && warningMethod.dimensionMethod === 4) {
        subscribeWarningArray.push(...dimensionValueIndicatorWarningList)
      }
      const shadeDirectionMap = {
        toTop: '1',
        toBottom: '2',
        toLeft: '3',
        toRight: '4',
      }
      const warningList = subscribeWarningArray.filter(v => v.allowSubscribe).map(v => {
        const isComputedFixedVal = v.fieldType === 'fixed' && (Number(v.compareMethod) === 2 || Number(v.compareMethod) === 4) && !['isNull', 'isNotNull', 'range'].includes(v.calcType)
        // 被比较字段是固定字符串
        const isBeComparedFixed = chartAlias === 've-gauge-normal'

        let metricAlias = isBeComparedFixed ? 'complateRate' : v.metric
        const metricIndex = metricsContainer.default.findIndex(item => (item.alias || item.labeName) === v.metric)

        let beComparedObject = {}
        if (!isBeComparedFixed) {
          beComparedObject = metricsContainer.default[metricIndex]
        } else {
          Object.assign(beComparedObject, {
            alias: metricAlias,
            isCustomField: true,
          })
        }
        const comparedObject = Number(v.compareMethod) === 4 ? v.matchField : v
        let dimensionSetting
        if (Number(v.compareMethod) === 4 && v.dimensionSetting) {
          dimensionSetting = v.dimensionSetting.map(ds => {
            const { selectValues, dimensionAlias, ...dimensionSettingItem } = ds
            return {
              ...dimensionSettingItem,
              dimensionAlias: (chartAlias === 've-grid-normal' && dimensionAlias) || dimensionSettingItem.fieldAlias || dimensionSettingItem.fieldColumnName,
              values: selectValues.map(v => v.code),
              selectValues,
            }
          })
        }
        const ruleParameterObj = {
          comparedText: comparedObject.alias || comparedObject.labeName,
          beComparedText: metricAlias,
          beComparedObject: beComparedObject,
          comparedObject: comparedObject,
          lessValue: v.rangeMin,
          highValue: v.rangeMax,
          fixedValue: isComputedFixedVal ? (v.value) * (v.percent) / 100 : v.value,
          compareMethod: Number(v.compareMethod),
          dimensionSetting: dimensionSetting,
          calcMethod: v.calcType,
          calcType: CALC_TYPE[v.fieldType],
          percent: v.percent,
        }
        const dimensionSettingLang = Number(v.compareMethod) === 4 ? v.dimensionSetting.map(ds => {
          let dimensionIndex = [...dimensionList, ...extendDimensionList].findIndex(item => item.keyName === ds.fieldKeyName)
          let dimensionLangFix = 'dimensionAlias'
          if (dimensionIndex >= dimensionList.length) {
            dimensionIndex = dimensionIndex - dimensionList.length
            dimensionLangFix = 'extendDimensionAlias'
          }
          return {
            key: ds.fieldKeyName,
            value: ds.fieldAlias || ds.fieldColumnName,
            languageId: `${ this.element.id }_${ dimensionIndex }_${ indicatorConfig.id ? indicatorConfig.index : 'x' }${ languageSpecialKey }_${ dimensionLangFix }`,
          }
        }) : []

        return {
          ...(originWarningObj.warningList?.find(w => w.webWarningId === v.id) || {}),
          ruleParameter: JSON.stringify({ ...ruleParameterObj, indicatorConfig }),
          warningColor: v.chartColor,
          warningName: v.name,
          webWarningId: v.id,
          elementType: '1',
          warningEffect: barchartWarnType === BARCHART_WARN_TYPES.PURE ? '1' : '2',
          shadeDirection: shadeDirectionMap[linearDirectionType],
          warningLineColor: v.warnLineColor,
          languageConfig: [
            {
              key: 'warningName',
              value: v.name,
              languageId: `${ this.element.id }_${ v.id }`,
            },
            {
              key: 'beComparedText',
              value: metricAlias,
              languageId: `${ this.element.id }_${ metricIndex }_${ indicatorConfig.id ? indicatorConfig.index : 'x' }${ languageSpecialKey }_metricsAlias`,
            },
            {
              key: 'comparedText',
              value: v.alias,
              languageId: `${ this.element.id }_${ indicatorConfig.id ? indicatorConfig.index : 'x' }${ languageSpecialKey }_${ v.id }_${ comparedObject.alias }`,
            },
            ...dimensionSettingLang,
          ]
        }
      })
      return warningList
    },
    getElementTitle() {
      if (this.chioceTab.length > 1) {
        return {
          text: '',
          textStyle: {},
        }
      }
      return {
        text: this.chartUserConfig.title.text,
        textStyle: this.titleStyle,
      }
    },
    replaceColumnValuesLan(val) {
      const lang = this.isChartSet ? '1' : (this.boardInfo?.languageCode || '1')
      return this.$_replaceColumnValuesLan(val, lang)
    },
    labelLineButtonListChange(bool) {
      let list = Object.assign(this.labelLineButtonList[this.checkedDimension], { labelLineButton: bool })
      this.$set(this.labelLineButtonList, [this.checkedDimension], this.$_JSONClone(list))
      // this.element.content.labelLineButtonListTmp = this.labelLineButtonList
      this.isChartLargeLast = bool
    },
    labelLineButtonListChangeAll(bool) {
      for (let i = 0; i < this.labelLineButtonList.length; i++) {
        let list = Object.assign(this.labelLineButtonList[i], { labelLineButton: bool })
        this.$set(this.labelLineButtonList, [i], this.$_JSONClone(list))
      }
      // this.element.content.labelLineButtonListTmp = this.labelLineButtonList
      this.isChartLargeLast = bool
    },
    mouseenterHandler() {
      this.animationInstance.mouseenter && this.animationInstance.mouseenter()
      this?.histogramAnimationInstance?.stop && this.histogramAnimationInstance.stop()
      this.isMouseEnter = true
    },
    mouseleaveHandler() {
      this.animationInstance.mouseenter && this.animationInstance.mouseleave()
      this?.histogramAnimationInstance?.continue && this.histogramAnimationInstance.continue()
      this.isMouseEnter = false
    },
    updateUserConfig() {
      if (this.UserConfig.otherData.updateTimestamp !== this.chartUserConfig.updateTimestamp) {
        this.UserConfig.init()
      }
    },
    metricUpdateAfterPreview() {
      const eventData = new EventData({
        ...this.defaultEventData,
        data: {
          list: [],
          check: false
        },
        target: 'paramsPanel',
        targetFn: 'metricRun',
      })
      this.$emit('eventBus', eventData)
    },
    popoverClose() {
      if (this.shareData.isFilterHold) {
        this.$set(this.shareData, 'isFilterHold', false)
      }
      if (this.shareData.isSortHold) {
        this.$set(this.shareData, 'isSortHold', false)
      }
    },
    // 设置指标选择器文字样式
    getIndicatorStyle(styleKey, isActive) {
      const { indicatorSelectorTextStyle = {}, indicatorSelectedTextStyle = {}, backgroundColor, buttonColor, underLineColor } = this.element.content?.contentStyle || {}
      const result = this.$_deepClone((isActive) ? indicatorSelectedTextStyle : indicatorSelectorTextStyle)
      const dom = this.$el
      const defaultStyle = {
        'font-style': 'normal',
        'font-weight': 'normal',
        'text-decoration': 'none',
      }
      Object.keys(result).forEach(k => {
        if (dom?.style) {
          if (this.isIndicatorDropDown && !this.isMobile) {
            // pc端下拉选择框特殊处理
            dom.style.setProperty(`--chioce-${ k }`, indicatorSelectedTextStyle[k] || defaultStyle[k])
          } else {
            dom.style.setProperty(`--chioce-${ k }`, indicatorSelectorTextStyle[k] || defaultStyle[k])
          }
          dom.style.setProperty(`--chioce-active-${ k }`, indicatorSelectedTextStyle[k] || defaultStyle[k])
        }
       })

      if (!!this.isMobile && dom?.style) {
        if (this.indicatorSelectorShowType === 'Tile') {
          dom.style.setProperty(`--chioce-active-background-color`, backgroundColor || 'var(--sdp-color-KBXXKDS)')
          dom.style.setProperty(`--chioce-active-button-color`, buttonColor || 'var(--sdp-color-KBXXK)')
        } else {
          // 设置成以前的默认色
          dom.style.setProperty(`--chioce-active-background-color`, 'var(--sdp-color-KBXXKDS)')
          dom.style.setProperty(`--chioce-active-button-color`, 'var(--sdp-color-KBXXK)')
        }
        if (this.indicatorSelectorShowType === 'tile-underLine') {
          dom.style.setProperty(`--chioce-active-under-line-color`, underLineColor || 'currentColor')
        } else {
          // 设置成以前的默认色
          dom.style.setProperty(`--chioce-active-under-line-color`, 'currentColor')
        }
      }

      if (!this.isMobile) {
          // pc端下划线模式适配老数据
          // #222 #E9E9EF
          // && indicatorSelectedTextStyle[k] ===CONTAINER_TITLE_TYPLE[this.themeType].color
          if (isActive && !indicatorSelectedTextStyle.isEditIndicatorSelectedTextStyle) {
            // 以前只有未选中的颜色值， 为了适配老数据 给选中颜色设置成跟未选中的一样的颜色值
            this.$set(this.element.content.contentStyle.indicatorSelectedTextStyle, 'color', indicatorSelectorTextStyle.color)
            result.color = indicatorSelectorTextStyle.color
          }
      }


      if (!this.isMobile && !this.isIndicatorDropDown) {
        result.opacity = isActive ? 1 : 0.25
      }
      return result
    },
    changeFontStyle(fontStyle, colorField, isActive) {
      let curFontStyle = this.element.content?.[fontStyle]
      let themeConfig = this.element.content.chartUserConfig?.themeConfig
      if (!curFontStyle || !Object.keys(curFontStyle)?.length) {
        curFontStyle = {
          'font-family': 'NotoSansHans-Regular', // 'Roboto-Regular',
          'font-size': '13px',
          'font-weight': '', // '',
          'text-decoration': '',
          'color': ''
        }
      }
      const themeType = this.themeData.themeType || 'sdp-classic-white'
      const curFontColor = themeConfig ? this.$_getProp(themeConfig[themeType], colorField, undefined) : undefined
      let dimensionFontColor = this.themeData?.appThemeType === THEME_TYPE.deepBlue && this.isMobile ? '#428AFF' : themeType === THEME_TYPE.classicWhite ? '#222' : '#E9E9EF'
      let fontColor = curFontColor?.color ? curFontColor.color : dimensionFontColor
      let styleObj = {}
      if (!this.isMobile) {
        this.$nextTick(() => {
          for (let key in curFontStyle) {
            let dom = this.$el
            let propName = `--date-dimension-${key}`
            dom?.style && dom.style.setProperty(propName, key === 'color' ? fontColor : curFontStyle[key])
          }
        })
      } else {
        for (let key in curFontStyle) {
          styleObj[key] = (key === 'color' ? fontColor : curFontStyle[key]) + '!important'
          styleObj.borderColor = fontColor
          styleObj.opacity = isActive ? 1 : 0.25
        }
      }
      return styleObj
    },
    toggleAuxiliaryLine(data) {
      if (this.auxiliaryLineHidden[data.id]) {
        this.$set(this.auxiliaryLineHidden, data.id, undefined)
      } else {
        this.$set(this.auxiliaryLineHidden, data.id, data)
      }
      this.resetAfterConfig({ isMapInit: false })
    },
    // 移动端图形标题、指标选择器、日期切换、度量汇总边距
    mobileChartPartMargin(part) {
      function mobieTitleMargin({ hasChoiceTab, hasDateComponent, hasTipRender }) {
        let marginBottom = 8
        if (!hasDateComponent && !hasChoiceTab) {
          marginBottom = 0
        } else if (hasDateComponent) {
          marginBottom = 5
        } else if (hasChoiceTab && this.isMobile && this.indicatorSelectorShowType === 'tile-underLine') {
          marginBottom = 0
        }
        if (hasTipRender) {
          marginBottom = 4
        }
        return { 'margin-bottom': marginBottom + 'px' }
      }

      function mobileIndicatorMargin({ hasDateComponent, hasTipRender }) {
        let marginBottom = 12
        if (!hasDateComponent) {
          marginBottom = 6
        }
        if (hasTipRender) {
          marginBottom = 4
        }
        return { 'margin-bottom': marginBottom + 'px' }
      }

      const hasChoiceTab = this.showChartIndicatorMobile('mobile')
      const hasDateComponent = this.showDateComponent({ vm: this, place: 'mobile' })
      const hasTipRender = !!this.tipRenderHeight

      if (part === 'title') return mobieTitleMargin.call(this, { hasChoiceTab, hasDateComponent, hasTipRender })
      if (part === 'choiceTab') return mobileIndicatorMargin({ hasDateComponent, hasTipRender })
      return {}
    },
    mobilePosition(type = 'legend') {
      if (type === 'legend') return (this.isMobileLegend && !this.disableDrill && this.chartUserConfig.legend) || 'none'
      if (type === 'auxiliaryLine') {
        const { auxiliaryLine, xAuxiliaryLineData, yAuxiliaryLineData } = this.chartUserConfig
        const hasAuxiliaryLine = (xAuxiliaryLineData && xAuxiliaryLineData.length) || (yAuxiliaryLineData && yAuxiliaryLineData.length)
        const showLineConfig = !CIRCULAR_CHART.includes(this.chartUserConfig.chartAlias) && !NOT_AUXILIATYLINE_CHART.includes(this.chartUserConfig.chartAlias)
        return (showLineConfig && hasAuxiliaryLine && auxiliaryLine.showDisplaySwitch && auxiliaryLine.displaySwitchPosition) || 'none'
      }
    },
    // 这个方法尽量不要用this
    mapSchemeSwitch(index = 0, content = this.element.content) {
      if (content.alias !== 've-map-parent') return
      if (!content.mapSchemeSetting?.schemeList?.length) return
      if (content.chartUserConfig.childChartAlias === content.mapSchemeSetting.schemeList[index].saveObj.chartUserConfig.childChartAlias) return
      const newContent = content.mapSchemeSetting.schemeList[index].saveObj
      const titleChartUserConfig = this.$_deepClone(content.chartUserConfig.title)
      const titleChartConfig = this.$_deepClone(content.chartConfig.title)

      setThemeChartUserConfig.call(this, newContent.chartUserConfig, this.themeType, newContent)
      newContent.chartUserConfig.title = titleChartUserConfig
      newContent.chartConfig.title = titleChartConfig
      this.$_filterDeepClone(content, newContent)
      this.geoRoamInfo = this.$_deepClone(newContent.chartUserConfig.mapSetting?.geoRoamInfo || {})
      content.mapSchemeSetting.schemeIndex = index
      this.redrawEchartsDom()
    },
    metricSwitch(indicatorList, isRelated) {
      this.metricChioceTab = { isRelated, metricChioceTab: indicatorList, showNone: false, }
      if (this.cardInteractionChioceTab.isActive) return

      const metricChioceTabName = indicatorList.map(mct => mct.name)
      const metricEventBus = () => {
        const eventData = new EventData({
          type: 'metricSwitch',
          target: ['paramsPanel'],
          targetFn: 'metricSwitchFinish',
          data: { id: this.element.id },
        })
        this.$emit('eventBus', eventData)
      }
      if (!isRelated || this.cardInteractionChioceTab.isActive) {
        metricEventBus()
        return
      } else if (this.chioceTab.length > 1) {
        const isContainCurrent = metricChioceTabName.includes(this.chioceTab[this.checkedDimension].name)
        if (!isContainCurrent) {
          const checkedDimension = this.chioceTab.findIndex(ct => metricChioceTabName.includes(ct.name))
          if (checkedDimension > -1) {
            this.checkedDimension = checkedDimension
            this.chooseDimension(checkedDimension, 'metricSwitch')
          } else {
            // 被关联了，有指标选择器，没有勾选
            this.metricChioceTab.showNone = true
          }
        }
      } else {
        // 被关联了 && 没有指标选择器
        this.metricChioceTab.showNone = true
      }
      metricEventBus()
    },
    dimensionSuperLink(e, closeFullScreen = false) {
      if (this.isChartLarge && closeFullScreen) {
        const eventData = new EventData({
          type: 'closeChartFullScreen',
          target: ['supernatant'],
          targetFn: 'closeChartFullScreen',
          data: { id: this.element.id, callback: this.dimensionSuperLink, e },
        })
        this.$emit('eventBus', eventData)
        return
      }
      // 大屏模式下禁止交互、钻取、跳转 8056 this.themeFullScreen ||【昆仑】大屏跳转【PC】
      const { chartAlias, childChartAlias } = this.chartUserConfig
      const { dimensionList, } = this.UserConfig
      if (!this.superLinkDimensionClick || this.isChartLarge) return

      // 树图点击扩展维度不跳转
      if (chartAlias === 've-tree' && e.data.level === 2 && !e.name) return

      const superLinkOptions = this.superLinkDimensionClick

      if (superLinkOptions?.openMethod === '2' && (this.utils.isPcMobile || (this.commonData.isMobileDataReport && this.commonData.isMobileDataReport()))) {
        return void 'PC转移动端后不能打开弹窗超链接'
      }

      const dimensionAliasList = dimensionList.map(item => item.alias || item.labeName)
      if (this.commonData.isPreview) {
        if (this.isDimensionSensibilisation) {
          if (!this.dimensionSensibilisationMessage || this.dimensionSensibilisationMessage.closed) {
            this.chartMessageBox(this.$t('sdp.views.sensibilisationTips'))
          }
          return
        }
        const { parameterField = [], dataSets = [] } = superLinkOptions

        let values = ''
        if (this.isMobile) {
          values = [e[0]]
        } else if (['xAxis', 'yAxis'].includes(e.componentType)) {
          values = e.value ? [e.value] : [e.name]
        } else if (e.data?.parentDimensionName) {
          // 点击双维度图形的主维度标签
          values = [e.data.parentDimensionName]
        } else if (this.elName === 've-scatter') {
          values = [e.seriesName]
        } else if (this.elName === 've-calendar') {
          values = [e.value[0]]
        } else if (this.elName === 've-radar') {
          values = Array.isArray(e) ? e : [this.chartData.rows[e.event.target.__dimIdx][`VIEWFORMAT_${this.chartSettings.dimension[0]}`]]
        } else if (chartAlias === 've-roundCascades' && e.componentType === 'markLine') {
          // 层叠圆形图点击标签钻取时，需要特殊处理
          values = [this.chartData.rows[e.seriesIndex]?.[dimensionAliasList[0]]]
        } else {
          values = [e.name]
        }

        // if (alias === 've-calendar') {
        //   const res = await getformatValue(this.utils.api, {
        //     tenantId: this.utils.tenantId,
        //     values: [e.value[0]],
        //   })
        //   calendarVal = e.value[0]
        //   values[0] = [res[0]]
        // }
        let titleCalendarPeroid = []

        // 双维度特殊处理values
        if (dimensionList.length === 2 && parameterField.length === 2 && !e.data?.parentDimensionName) {
          if (!this.isMobile) {
            const _rowIndex = e.dataIndex === undefined ? e.event.target.style.textShadowOffsetX || 0 : e.dataIndex
            let rows = [...this.speciaDrillandInterActionlArr, ...this.treeDataChartArr].includes(this.content.alias) ? e.data : this.chartData.rows[_rowIndex]
            values.unshift(rows[`VIEWFORMAT_${dimensionAliasList[0]}`])
          } else {
            values.unshift(e[1])
          }
        } else if (dimensionList.length === 1 && dimensionList[0].columnTpe === 'date' && CUSTOM_DATE_DIMENSION_CHART.includes(chartAlias)) {
          let chartRows = this.chartData.rows
          // 需要取日期真实值的场景
          const dimensionAlias = dimensionAliasList[0]
          const hoverDimensionName = `${ this.specialTimeSplitOffset ? 'HOVER_' : '' }${dimensionAlias}`
          if (!this.isMobile) {
            const _rowIndex = e.dataIndex === undefined ? e.event.target.style.textShadowOffsetX || 0 : e.dataIndex
            let rowItem = this.speciaDrillandInterActionlArr.includes(chartAlias) ? e.data : chartAlias === 've-scatter-normal'
              ? chartRows[e.seriesName][_rowIndex] : chartRows[_rowIndex]
            rowItem = this.treeDataChartArr.includes(chartAlias) ? e.data : rowItem
            values[0] = rowItem[hoverDimensionName] || rowItem[dimensionAlias]
          } else {
            const rowItem = chartRows.find(c => c[`VIEWFORMAT_${ dimensionAlias }`] === values[0])
            if (rowItem) {
              values[0] = rowItem[hoverDimensionName] || rowItem[dimensionAlias]
            }
          }
          const key = `${dimensionList[0].alias || dimensionList[0].labeName}${values[0]}`
          titleCalendarPeroid = this.content?.chartResponse?.titleCalendarCacheMap?.[key] || []
        }
        let dataSetsParams = []
        dataSets.forEach(item => {
          item.columnName.forEach((columnItem, columnIndex) => {
            values[columnIndex] && dataSetsParams.push({
              columnName: columnItem,
              dataSetId: item.dataSetId,
              values: [values[columnIndex]],
              titleCalendarPeroid
            })
          })
        })
        const eventData = new EventData({
          type: 'setSuperLink',
          target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
          targetFn: 'setSuperLink',
          data: {
            options: Object.assign({}, superLinkOptions, {
              clickVal: Array.isArray(values) ? (values[values.length - 1] || '') : '',
            }),
            superLink: {
              dataSets: dataSetsParams,
              dataSetId: dimensionList[0].parentId,
            },
          },
        })
        this.$emit('eventBus', eventData)
      } else {
        this.$message.warning(this.$t('sdp.views.bplsClick'))
      }

    },
    titleClick() {
      // 标题超链接，非图形配置，竖屏，没有指标选择器，已配置超链接
      // 大屏模式下禁止交互、钻取、跳转 8056 this.themeFullScreen ||【昆仑】大屏跳转【PC】
      if (!this.superLinkTitleClick || this.isChartLarge) return
      const superLinkOptions = this.superLinkTitleClick.find(el => el.labelBoard.id === this.element.id)
      console.log(this.superLinkTitleClick, superLinkOptions, this.element.id)

      if (superLinkOptions?.openMethod === '2' && (this.utils.isPcMobile || (this.commonData.isMobileDataReport && this.commonData.isMobileDataReport()))) {
        return void 'PC转移动端后不能打开弹窗超链接'
      }

      // 地图的超链接是上次的点击信息
      if (this.content.chartUserConfig.childChartAlias === 've-map-world') {
        if (this.mapSuperlinkData) {
          superLinkOptions.clickVal = this.mapSuperlinkData.name
        } else {
          if (superLinkOptions.clickVal) delete superLinkOptions.clickVal
        }
      }

      if (superLinkOptions) {
        if (this.commonData.isPreview) {
          const eventData = new EventData({
            type: 'setSuperLink',
            target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
            targetFn: 'setSuperLink',
            data: { options: superLinkOptions },
          })
          this.$emit('eventBus', eventData)
        } else {
          this.$message.warning(this.$t('sdp.views.bplsClick'))
        }
      }
    },

    setHiddenSelect(dom) {
      if (!dom) return
      dom.addEventListener('scroll', this.hiddenSelect)
      this.$once('hook:destroyed', () => {
        dom.removeEventListener('scroll', this.hiddenSelect)
      })
    },
    // 高分辨率下，放大或全屏的时候，字体放大的方法
    getPxOfHighResolution(val) {
      return this.isChartLarge ? this.$_getPxOfHighResolution(val) : val
    },
    // 获取简单表格
    getGridRef() {
      if (this.hasGrid) {
        return this.$refs.grid
      }
      return null
    },
    // 浏览器窗口大小变化之后执行的方法
    resizeWindow() {
      if (!this.hasGrid) {
        this.resetAfterConfig({ source: 'resizeChart', isMapInit: true })
      }
    },
    // 获取location选择维度标签还是报表层级树
    // LOCATION_SELECT_TYPE.DIMENSION 层级 LOCATION_SELECT_TYPE.LABEL 维度标签
    setLocationSelectType(type, treeData) {
      this.locationSelectType = type
      this.locationTreeDatae = treeData
    },
    hiddenSelect() {
      const { dateSelect = {}, choiceSelect = {} } = this.$refs
      if (dateSelect.visible) {
        dateSelect.blur()
      }
      if (choiceSelect.visible) {
        choiceSelect.blur()
      }
    },
    setSelectedDateVal(val) {
      this.selectedDateVal = val
    },
    showTip() {
      const tipDOM = this.getTipDOM()
      const { clientWidth: wrapperWidth, clientHeight: wrapperHeight } = this.getTipWrapper()
      const { clientWidth: tipWidth, clientHeight: tipHeight } = tipDOM
      const isEnough = tipWidth < wrapperWidth && tipHeight < wrapperHeight

      if (isEnough) {
        this.hideTip()
        return
      }

      this.tipDOM = this.getTipDOM()
      this.resetTipPosition()
      this.appendTipIntoBody()
    },

    hideTip() {
      this.resetTipDOM()
    },

    getTipDOM() {
      return this.tipDOM ? this.tipDOM : this.getTipWrapper().children[1]
    },

    getTipWrapper() {
      // roc todo: 有没有更好的方式获取此元素
      return this.$refs[this.elName].$el.children[0]
    },

    appendTipIntoBody() {
      if (this.tipDOM.dataset.elId) return
      this.tipDOM.dataset.elId = this.element.id
      let parentDom = document.body
      // 修改大屏模式
      // if (this.themeFullScreen) {
      //   parentDom = document.querySelector('.full-screen-class')
      // }
      parentDom.append(this.tipDOM)
    },

    resetTipDOM() {
      if (!this.tipDOM) return

      delete this.tipDOM.dataset.elId
      this.getTipWrapper().append(this.tipDOM)
      this.tipDOM = null
    },

    resetTipPosition() {
      const elName = this.$refs[this.elName]
      const { left, top } = elName.$el.getBoundingClientRect()
      const style = this.tipDOM.style

      this.tipDOM.style.left = parseFloat(style.left) + left + 'px'
      this.tipDOM.style.top = parseFloat(style.top) + top + 'px'
    },

    triggerEchartsEvents(eventName, e) {
      this.defaultChartConfig.events[eventName](e)
    },

    // 点击交互
    async interActionClick(e, drillDimensionsFlag) {
      // 大屏模式下禁止交互、钻取、跳转,this.themeFullScreen(预览时大屏)，this.fullscreenData.enlargeVisible(预览时元素放大或全屏)
      const isMobileShow = !this.isMobile && !this.isChartSet
      if ((this.isChartLarge && isMobileShow) || e.data?.parentDimensionName) return
      const { dimension, metrics } = this.chartSettings
      const { chartAlias } = this.chartUserConfig
      const { dimensionList, } = this.UserConfig

      // 如果有指标选择器，则取指标选择器中的交互设置信息
      let interactionOptions = this.getFilteredInterActionOptions()
      console.log('当前交互设置：', interactionOptions)

      const associElements = (interactionOptions && interactionOptions[0] && interactionOptions[0].associElements) || []

      // 判断是否可以进行交互
      if (!isCanInteraction(this.element._containerId, associElements || [], this.elList)) return
      // 移动看板元素交互
      const mobileInterAction = Object.prototype.toString.call(e) === '[object Array]'
      console.log('mobileInterAction', e, mobileInterAction)
      // 移动看板点击元素区域 不进行交互
      if (this.isMobile && !mobileInterAction && this.isByTheInteraction) return

      const setting = this.interactionSetting
      const alias = this.content.alias
      if (!this.isChartSet && setting.clickAble) {
        const values = [null]
        let calendarVal
        if (mobileInterAction) {
          values[0] = [e[0]]
          if (this.content.chartUserConfig.childChartAlias === 've-map-world' && this.commonData.boardSlectLang() !== 'zh' && (this.langCode !== 'zh' || this.commonData.isPreview)) {
            values[0] = [mapConstant.namemapEnToZh.world[values[0]]]
          }
        } else if (setting.filedName.some(item => metrics.includes(item))) {
          values[0] = [e.value]
        } else if (alias === 've-scatter-normal') {
          values[0] = [e.seriesName]
        } else if (this.treeDataChartArr.includes(alias) && dimension.length === 2) {
          values[0] = [e.data[`VIEWFORMAT_${dimension[1]}`]]
        } else if (chartAlias === 've-roundCascades' && e.componentType === 'markLine') {
          // 层叠圆形图点击标签钻取时，需要特殊处理
          const dimensionAlias = dimensionList[0].alias || dimensionList[0].labeName
          values[0] = [this.chartData.rows[e.seriesIndex]?.[dimensionAlias]]
        } else if (setting.filedName.some(item => dimensionList.find(_dimension => _dimension.labeName === item))) {
          const axis = ['xAxis', 'yAxis']
          values[0] = [axis.includes(e.componentType) ? e.value : e.name]
          if (this.content.chartUserConfig.childChartAlias === 've-map-world' && this.commonData.boardSlectLang() !== 'zh' && (this.langCode !== 'zh' || this.commonData.isPreview)) {
            mapConstant.namemapEnToZh.world[values[0]] && (values[0] = [mapConstant.namemapEnToZh.world[values[0]]])
          }
          if (alias === 've-calendar') {
            const res = await getformatValue(this.utils.api, {
              tenantId: this.utils.tenantId,
              values: [e.value[0]],
            })
            calendarVal = e.value[0]
            values[0] = [res[0]]
          }
        }
        let chartRows = this.chartData.rows
        // 双维度特殊处理values
        if (dimension.length === 2) {
          if (!this.isMobile) {
            const _rowIndex = e.dataIndex === undefined ? e.event.target.style.textShadowOffsetX : e.dataIndex
            let rows = this.speciaDrillandInterActionlArr.includes(alias) ? e.data : chartRows[_rowIndex]
            rows = this.treeDataChartArr.includes(alias) ? e.data : rows
            values.push([rows[`VIEWFORMAT_${dimension[0]}`]])
          } else {
            values.push([e[1]])
          }
        } else if (dimension.length === 1) {
          if (this.othersPieDataIndex > -1) {
            if (!this.isMobile && e.seriesIndex === 1) {
              chartRows = this.chartData.rows[this.othersPieDataIndex].others
            } else if (this.isMobile) {
              chartRows = chartRows.concat(chartRows[this.othersPieDataIndex].others)
            }
          }
        }
        let dateDimensionList = dimensionList.map((d, i) => {
          d._index = i
          return d
        }).filter(d => d.columnTpe === 'date')
        if (dateDimensionList.length === 1) {
          // 需要取日期真实值的场景
          // if (this.specialTimeSplitOffset || dimensionList[0].viewFormat?.dateCustomFormat || dimensionList[0].viewFormat?.timeCustomFormat) {
            const dimensionAlias = dateDimensionList[0].alias || dateDimensionList[0].labeName
            const hoverDimensionName = `${ this.specialTimeSplitOffset ? 'HOVER_' : '' }${dimensionAlias}`
            if (!this.isMobile) {
              const _rowIndex = e.dataIndex === undefined ? e.event.target.style.textShadowOffsetX : e.dataIndex
              let rowItem = this.speciaDrillandInterActionlArr.includes(chartAlias) ? e.data : chartAlias === 've-scatter-normal'
                ? chartRows[e.seriesName][_rowIndex] : chartRows[_rowIndex]
              rowItem = this.treeDataChartArr.includes(chartAlias) ? e.data : rowItem
              values[dateDimensionList[0]._index] = [rowItem[hoverDimensionName] || rowItem[dimensionAlias]]
            } else {
              const rowItem = chartRows.find(c => c[`VIEWFORMAT_${ dimensionAlias }`] === values[dateDimensionList[0]._index][0])
              if (rowItem) {
                values[dateDimensionList[0]._index] = [rowItem[hoverDimensionName] || rowItem[dimensionAlias]]
              }
            }
          // }
        }

        // 是否点击相同图块标识
        let theSameInteraction = true
        interactionOptions.map(item => item.values ? item.values[0] : null).reverse().forEach((curVal, index) => {
          (values[index] === null || (values[index] && curVal !== values[index][0])) && (theSameInteraction = false)
        })
        // 带钻取的英国地图不存在取消交互
        if (this.isBritainMap && this.content.drillList.length) {
          theSameInteraction = false
        }

        const data = {
          ids: [
            this.element.id,
            ...associElements.map(
              elSetting => elSetting.id
            )
          ],
          element: { type: TYPE_INTERACTION.INTERACTION_SETTING }
        }
        data.ids.forEach((id, i) => {
          if (i === 0) return
          const associElement = this.elList.find(el => el.id === id)
          if (!associElement?.content.drillSettings?.drillDimensions) return
          delete associElement.content.drillSettings.drillDimensions
        })
        const reserveIndex = [1, 0]
        interactionOptions.forEach((item, index) => {
          const _length = dimension.length
          // 判断是否存在钻取 交互参数数组长度 = 钻取参数数组长度 + 1
          const children = item.children
          const drillDimensions = this.drillSettings.drillDimensions
          // 钻取到最后一层
          const lastInteraction = children && children.length === drillDimensions.length / _length + 1
          // this.$set(item, 'values', values[index])
          const _index = _length === 2 ? reserveIndex[index] : index
          // 判断过滤条件是否修改
          if (item.openFilter && filterMapVerify({ type: TYPE_ELEMENT_GROUP.CHART, filterMap: item.filterMap, filterData: this.$_getProp(this, 'element.content.drillSettings.filters', []) })) {
            this.$delete(item, 'filterMap')
            this.$delete(item, 'openFilter')
          }

          if (theSameInteraction) {
            // 带交互的钻取，没有钻取到底层，不具备取消交互的功能
            if (lastInteraction || !drillDimensionsFlag) {
              this.$delete(item, 'values')
            }
          } else {
            this.$set(item, 'values', values[_index])
          }

          // 全部钻取完成后点击交互
          // 是否是钻取最后一层返回
          const specialUpDrill = drillDimensions?.length && children?.length === drillDimensions.length
          if (drillDimensionsFlag || specialUpDrill) {
            data.dimensionDrill = !!drillDimensionsFlag
            // 双维度第一个维度的columnName不需要变化
            const doubleNotNeed = (!index && _length === 2)
            this.getInteractionOptionsByDimensions(item, drillDimensions.length / _length, specialUpDrill, doubleNotNeed)
          }

          // 交互参数数组长度达到最大值后，点击交互，只改变values
          if (lastInteraction) {
            const len = children.length
            if (!theSameInteraction) {
              children[len - (index + 1)].values = this.$_JSONClone(values[_index])
            } else {
              Reflect.deleteProperty(children[len - (index + 1)], 'values')
            }
          }
          const thisDimension = dimensionList.find(d => d.keyName === item.site) || {}
          item.needChangeTrendsDimension = this.getNeedChangeTrendsDimension(thisDimension)
          // 获取日期维度拆分数据
          const titleCalendarCacheMap = this.content?.chartResponse?.titleCalendarCacheMap || {}
          if (this.element.content.needTitleCalendarCacheMap && titleCalendarCacheMap) {
            const key = `${dimensionList[0].alias || dimensionList[0].labeName}${values[0]}`
            item.titleCalendarPeroid = titleCalendarCacheMap[key]
            if (item.children?.length) {
              const interactionItem = item.children[item.children.length - 1]
              interactionItem.titleCalendarPeroid = titleCalendarCacheMap[key]
            }
          } else {
            delete item.titleCalendarPeroid
          }
        })

        this.refreshEl(data)
        if (this.isMobile && !this.isChartSet) return
        const _interactionOptionsValues = alias === 've-calendar' ? calendarVal : dimension.length === 2 ? values : values[0][0]
        this.interactionOptionsValues = (theSameInteraction || drillDimensionsFlag) ? null : _interactionOptionsValues
        this.interactionOptionsTrigger = e.source === 'PieAnimation' ? 'auto' : 'click'
        this.animationInstance.interaction && this.animationInstance.interaction(this.interactionOptionsValues, this.interactionOptionsTrigger)
      }
    },
    getNeedChangeTrendsDimension(item) {
      let trendsDimensionType
      if (item.timeDimensionSwitch) {
        trendsDimensionType = TRENDS_DIMENSION_EXCHANGE.TIME_DIMENSION
      } else if (item.trendsDimensionSwitch) {
        trendsDimensionType = TRENDS_DIMENSION_EXCHANGE.TRENDS_DIMENSION
      } else if (item.eventDimensionSwitch) {
        trendsDimensionType = TRENDS_DIMENSION_EXCHANGE.EVENT_DIMENSION
      }
      return trendsDimensionType
    },
    // 点击钻取
    drillDimensionsClick(e) {
      // 判断是否存在交互设置
      const setting = this.interactionSetting
      const hasInteraction = setting.clickAble
      const { chartAlias, childChartAlias } = this.chartUserConfig
      const { dimensionList, } = this.UserConfig
      const isMobileShow = !this.isMobile && !this.isChartSet

      // 大屏模式下禁止交互、钻取、跳转，全屏时候禁止带交互的钻取
      if (
        (!this.isMobile && this.elName === 've-radar' && e.event.target.__dimIdx === undefined) ||
        this.disabledDrill ||
        (this.isChinaMap && this.chartUserConfig?.mapRangeVal === 'provice') ||
        this.isMapFlyLineEnable ||
        ((this.isChartLarge && isMobileShow) && hasInteraction) ||
        e.data?.parentDimensionName) return
      // 设置预置维度标签，location选择维度标签时，禁止钻取; location选择报表层级树且钻取返回数据为空，禁止下一次钻取
      if (this.presetDimensionLabel && (this.locationSelectType === 'label' || (this.locationSelectType === 'dimension' && this.disableDrill))) return
      const mobileDrillFlag = Object.prototype.toString.call(e) === '[object Array]'
      if (this.isMobile && !mobileDrillFlag && this.isByTheInteraction) return
      const drillList = this.content.drillList?.filter(item => item.keyName === dimensionList[dimensionList.length - 1].labeName) || []
      // 在编辑界面外 且 存在钻取设置时 进行钻取
      if (this.isChartSet || drillList.length === 0) return
      let drillDimensions = this.content.drillSettings.drillDimensions || []
      const drillDimensionsLength = drillDimensions.filter(d => d.isDrillField).length
      // 设置钻取次数 大于 当前钻取层级时进行钻取
      if (drillList.length <= drillDimensionsLength) {
        return
      }
      if (this.isDimensionSensibilisation) {
        if (!this.dimensionSensibilisationMessage || this.dimensionSensibilisationMessage.closed) {
          this.chartMessageBox(this.$t('sdp.views.sensibilisationTips'))
        }
        return
      }

      // 获取要钻取的维度数据
      let currentDrillData = drillList.slice(drillDimensionsLength, drillDimensionsLength + 1)[0]
      // 因为双维度的影响，添加钻取字段区分标识
      currentDrillData.isDrillField = true
      const thisDimension = dimensionList.find(d => d.keyName === currentDrillData.drillDimensionKey) || {}
      currentDrillData.needChangeTrendsDimension = this.getNeedChangeTrendsDimension(thisDimension)

      let values = ''
      if (this.isMobile) {
        values = [e[0]]
      } else {
        values = [e.name]
        if (this.elName === 've-scatter') {
          values = [e.seriesName]
        } else if (this.elName === 've-bar') {
          values = [e.name]
        } else if (this.elName === 've-radar') {
          values = Array.isArray(e) ? e : [this.chartData.rows[e.event.target.__dimIdx][`VIEWFORMAT_${this.chartSettings.dimension[0]}`]]
        } else if (chartAlias === 've-roundCascades' && e.componentType === 'markLine') {
          // 层叠圆形图点击标签钻取时，需要特殊处理
          const dimensionAlias = dimensionList[0].alias || dimensionList[0].labeName
          values = [this.chartData.rows[e.seriesIndex]?.[dimensionAlias]]
        }
      }

      // 点击标签
      if (['xAxis', 'yAxis'].includes(e.componentType) && !this.isMobile) {
        values = e.value ? [e.value] : [e.name]
      }
      // await dateJudge.call(this, values)
      currentDrillData.values = values
      if (childChartAlias === 've-map-china') {
        currentDrillData.mapType = this.chartSettings.position
      }

      // if (this.treeDataChartArr.includes(chartAlias) && dimension.length === 2) {
      //   // 这段逻辑貌似没有用到
      //   values[0] = [e.data[`VIEWFORMAT_${dimension[1]}`]]
      // }
      if (this.content.drillSettings.currentDrillData && this.content.drillSettings.currentDrillData.length) {
        const length = values.length
        let exist = values.filter(item => this.content.drillSettings.currentDrillData.includes(item))
        if (exist.length === length) return
      }
      // 双维度钻取是成对的，需要在二级维度后面跟上一级维度
      const parentCurrentDrillData = []
      let chartRows = this.chartData.rows
      if (dimensionList.length === 2) {
        const baseData = {
          keyName: dimensionList[0].labeName,
          needChangeTrendsDimension: this.getNeedChangeTrendsDimension(dimensionList[0]),
          isDrillField: false,
        }
        if (!this.isMobile) {
          const _rowIndex = e.dataIndex === undefined ? e.event.target.style.textShadowOffsetX : e.dataIndex
          let rows = this.speciaDrillandInterActionlArr.includes(chartAlias) ? e.data : chartAlias === 've-scatter-normal'
            ? chartRows[e.seriesName][_rowIndex] : chartRows[_rowIndex]

          rows = this.treeDataChartArr.includes(chartAlias) ? e.data : rows
          parentCurrentDrillData.push(Object.assign({}, currentDrillData, {
            ...baseData,
            values: [rows[`VIEWFORMAT_${dimensionList[0].alias || dimensionList[0].labeName}`]],
          }))
        } else {
          parentCurrentDrillData.push(Object.assign({}, currentDrillData, {
            ...baseData,
            values: [e[1]],
          }))
        }
      } else if (dimensionList.length === 1) {
        if (this.othersPieDataIndex > -1) {
          if (!this.isMobile && e.seriesIndex === 1) {
            chartRows = this.chartData.rows[this.othersPieDataIndex].others
          } else if (this.isMobile) {
            chartRows = chartRows.concat(chartRows[this.othersPieDataIndex].others)
          }
        }
      }
      let dateDimensionList = dimensionList.map((d, i) => {
        d._index = i
        return d
      }).filter(d => d.columnTpe === 'date')
      if (dateDimensionList.length === 1) {
        if (this.specialTimeSplitOffset || dateDimensionList[0].viewFormat?.dateCustomFormat || dateDimensionList[0].viewFormat?.timeCustomFormat) {
          const dimensionAlias = dateDimensionList[0].alias || dateDimensionList[0].labeName
          const hoverDimensionName = `${ this.specialTimeSplitOffset ? 'HOVER_' : '' }${dimensionAlias}`
          if (!this.isMobile) {
            const _rowIndex = e.dataIndex === undefined ? e.event.target.style.textShadowOffsetX : e.dataIndex

            let rowItem = this.speciaDrillandInterActionlArr.includes(chartAlias) ? e.data : chartAlias === 've-scatter-normal'
              ? chartRows[e.seriesName][_rowIndex] : chartRows[_rowIndex]

            rowItem = this.treeDataChartArr.includes(chartAlias) ? e.data : rowItem
            values[dateDimensionList[0]._index] = rowItem[hoverDimensionName] || rowItem[dimensionAlias]
          } else {
            const rowItem = chartRows.find(c => c[`VIEWFORMAT_${ dimensionAlias }`] === values[dateDimensionList[0]._index])
            if (rowItem) {
              values[dateDimensionList[0]._index] = [rowItem[hoverDimensionName] || rowItem[dimensionAlias]]
            }
          }
        }
      }
      // if (!this.content.drillSettings.currentDrillData || this.content.drillSettings.currentDrillData.index < drillDimensionsLength) {
      //   currentDrillData.index = drillDimensionsLength
      //   this.$set(this.content.drillSettings, 'currentDrillData', currentDrillData)
      // }
      const drillDataList = [currentDrillData, ...parentCurrentDrillData]
      // 获取日期维度拆分数据
      const titleCalendarCacheMap = this.content?.chartResponse?.titleCalendarCacheMap || {}
      if (this.element.content.needTitleCalendarCacheMap && titleCalendarCacheMap) {
        const key = `${dimensionList[0].alias || dimensionList[0].labeName}${values[0]}`
        drillDataList.forEach(item => {
          item.titleCalendarPeroid = titleCalendarCacheMap[key]
        })
      }
      drillDimensions.push(...drillDataList)
      this.$set(this.content.drillSettings, 'drillDimensions', drillDimensions)

      if (hasInteraction) return true
      this.refreshEl({
        ids: [this.element.id],
      })
    },
    getMapRang() {
      let minData = this.minData
      if (this.minData === this.maxData) {
        minData *= 0.9
      }
      this.mapOptions.visualMap.min = minData
      this.mapOptions.visualMap.max = this.maxData
      this.mapOptions.visualMap.text = [this.maxData, this.minData]
    },
    async getLanguageProvice(val) {
      if (!this.isChinaMap) return
      let name = mapConstant.provinceList.find(item => item.spell === val)
      if (chartApi.mapSet.getMapLanguageProvice()) return

      await chartApi.getMapLanguageProvice(this.utils.api, name?.name)
    },
    doRegisterMap(data = {}) {
      if (this.elName !== 've-map') return
      this.clearMapAnimation({ from: 'register map' })
      const { name, params } = data
      if (['ve-map-china', 've-map-world'].includes(this.chartUserConfig.childChartAlias)) {
        this.ChinaAndWorldMapHandler(name)
      } else if (['ve-map-britain'].includes(this.chartUserConfig.childChartAlias)) {
        this.foreignCountryHandler({ name, params })
      }
      this.getGeoJson()
    },
    foreignCountryHandler({ name = 'britain', params }) {
      if (['ve-map-china', 've-map-world'].includes(this.chartUserConfig.childChartAlias)) return
      const dataUrl = `country/${name}`
      if (this.$_getProp(this.chartUserConfig, 'mapJson.name') === name && chartApi.mapSet.getData(dataUrl)) {
        this.chartSettings.position = name
        if (!this.commonData.isPreview) {
          this.redrawEchartsDom()
        }
        return
      }
      chartApi.getRegionData({ dataUrl }).then(res => {
        if (!res) return
        this.chartSettings.position = name
        !echarts.getMap(name) && echarts.registerMap(name, res)
        this.$set(this.chartUserConfig, 'mapJson', { name, dataUrl: dataUrl })
        this.$set(this.mapOptions, 'position', name)
        this.resetAfterConfig()
      })
    },
    async ChinaAndWorldMapHandler(name = 'china') {
      if (!['ve-map-china', 've-map-world'].includes(this.chartUserConfig.childChartAlias)) return
      const res = this.setMapPosition(name || 'china')
      if (!res) return
      const { _name, curProvince, provice } = res
      const _url = ['china', 'world'].includes(curProvince) ? curProvince : `province/${curProvince}`
      const chinaMapDrill = provice && this.$_getProp(this.chartUserConfig, 'mapJson.name') === provice.name
      if (chartApi.mapSet.getData(_url) && (this.chartUserConfig.childChartAlias !== 've-map-china' || chinaMapDrill)) {
        // 解决地图切换地图类型时，地图显示问题
        if (!this.commonData.isPreview) {
          this.redrawEchartsDom()
        }
        return
      }

      const { mapProviceVal = 'beijing', mapRangeVal = 'china' } = this.chartUserConfig
      const isProvice = mapRangeVal === 'provice'
      const spell = isProvice ? mapProviceVal : ''
      await this.getLanguageProvice(spell)

      // 解决钻取时进入下级地图气泡/图标位置偏移问题
      chartApi.getRegionData({ dataUrl: _url }).then(res => {
        !echarts.getMap(_name) && echarts.registerMap(_name, res)
        this.$set(this.chartUserConfig, 'mapJson', { name: _name, dataUrl: _url })
        this.$set(this.mapOptions, 'position', _name)
        this.resetAfterConfig()
      })
    },
    getGeoJson() {
      const countryList = {
        've-map-china': 'china',
        've-map-world': 'world',
        've-map-britain': `country/britain`,
      }

      Object.keys(countryList).forEach(childChartAlias => {
        if (chartApi.mapSet.getData(countryList[childChartAlias])) return
        const geoName = childChartAlias.replace('ve-map-', '')
        chartApi.getRegionData({ dataUrl: countryList[childChartAlias] }).then(res => {
          if (!res) return
          !echarts.getMap(geoName) && echarts.registerMap(geoName, res)
        })
      })
    },
    setMapPosition(name = '') {
      const { mapProviceVal = 'beijing', mapRangeVal = 'china', childChartAlias } = this.chartUserConfig
      const isProvice = mapRangeVal === 'provice'
      const spell = isProvice ? mapProviceVal : ''
      let _name = name.includes('province/') ? name.replace('province/', '') : name
      _name = childChartAlias === 've-map-china' && _name === 'world' ? 'china' : _name
      _name = childChartAlias === 've-map-world' && _name === 'china' ? 'world' : _name
      let curProvince = _name
      let _position = _name
      let provice = { name: 'china' }
      if ((_name !== 'china' || isProvice) && this.chartUserConfig.childChartAlias === 've-map-china') {
        const key = (spell && isProvice) ? 'spell' : 'name'
        const val = (spell && isProvice) ? spell : _name
        provice = mapConstant.provinceList.find((v) => val.includes(v[key]))
        if (!provice) return false
        _name = provice.name
        curProvince = provice.spell
        _position = `province/${curProvince}`
      }
      this.$set(this.chartSettings, 'position', _position)
      return { _name: _position, curProvince, provice }
    },
    mapDrill(e, isPopMobileClick) {
      // 判断是否存在交互设置
      const setting = this.interactionSetting
      const hasInteraction = setting.clickAble
      // 大屏模式下禁止交互、钻取、跳转
      // if (this.themeFullScreen || (this.fullscreenData.enlargeVisible && hasInteraction)) return
      if (this.elName !== 've-map' || (this.isChinaMap && this.chartUserConfig?.mapRangeVal === 'provice') || this.isMapFlyLineEnable) return
      if (this.content.drillList && this.content.drillList.length === 0) return
      if (this.isChartSet) return
      if (isPopMobileClick || ['map', 'scatter', 'effectScatter'].includes(e.componentSubType) || e.componentType === 'geo') {
        let provinceName = mapConstant.provinceList.map((v) => v.name)
        let curProvince = isPopMobileClick ? e[0] : e.name
        if (provinceName.some(item => curProvince.includes(item))) {
          this.doRegisterMap({ name: curProvince })
        }
      }
    },
    britainMapFilterData() {
      if (!this.isBritainMap || !this.content.drillList.length || this.isChartSet || !this.drillSettings?.drillDimensions?.length) return
      const { dimensionList, } = this.UserConfig
      const value = this.drillSettings.drillDimensions[0]?.values?.[0]
      if (!value) return
      const dimensionAlias = dimensionList[0]?.alias || dimensionList[0]?.labeName
      const mapData = this.chartData.rows.filter(item => item[dimensionAlias] === value)
      this.$set(this.chartData, 'rows', mapData)
    },
    britainMapDrill(e) {
      // 英国地图做的假钻取：只能钻取一次，过滤出点击的数据
      if (!this.content.drillList.length || this.isChartSet || this.drillSettings?.drillDimensions?.length || this.isMapFlyLineEnable) return
      let value = this.isMobile ? e[0] : e.name
      this.drillDimensionsClick(e)
      this.britainMapFilterData()
      const position = `britainMapCountry/${value}`
      this.doRegisterMap({ name: position, params: e })
    },
    onBritainUpDrill() {
      let { chartResponse, interactionOptions } = this.element.content
      // this.drillSettings.drillDimensions = []
      this.$set(this.drillSettings, 'drillDimensions', [])

      let data = {
        ids: [this.element.id],
      }
      let list = []
      interactionOptions && interactionOptions.forEach(item => {
        this.$delete(item, 'values')
        this.$delete(item, 'children')
        list.push([...item.associElements.map(elSetting => elSetting.id)])
      })
      // 清除对其他交互元素的影响
      list.forEach(arr => {
        if (arr.length) {
          arr.forEach(id => {
            const el = this.elList.find(e => e.id === id)
            if (el && el.content.interactionState && el.content.interactionState[this.element.id]) {
              delete el.content.interactionState[this.element.id]
              this.refreshEl({ ids: [id] })
            }
          })
        }
      })
      data = {
        ids: [
          this.element.id,
          ...list
        ],
        element: { type: TYPE_INTERACTION.INTERACTION_SETTING }
      }
      this.doRegisterMap({ name: 'britain' })
      // this.redrawEchartsDom()
      this.refreshEl(data)
    },
    mapBack(position = 'china') {
      this.doRegisterMap({ name: position })
      // this.mapOptions.position = position
      this.resetAfterConfig()
    },
    // 此处修改也要修改getMobileClickActive
    mobileClick(e) {
      // 移动端点击图形任意区域, 弹出hover层, 显示图例和指标信息
      if (!this.isMobile || this.isChartSet) return
      const { chartAlias, colorType, dimensionColors = [], funnelSettings = {}, waterfallSetting, contrastList, labelLineShow, metricLabelDisplay = [], extendDimensionColor, wordCloudSetting = {}, mainDimensionMeasureConfig = {}, metricsContainer } = this.chartUserConfig
      const { dimensionList, dimensionExtendList, metricAllList } = this.UserConfig
      // 仪表盘特殊处理（辅助线和预警线无需弹框、目前根据borderType区分辅助线和预警线）
      if (chartAlias === 've-gauge-normal' && e.data?.itemStyle?.borderType === 'dashed') return
      // 移动端的折线图和折线面积图使用markLine补充点击事件
      if (e.componentType === 'graphic' || e.targetType === 'axisName' || (e.componentType === 'markLine' && e.name !== 'mobileClickArea' && chartAlias !== 've-roundCascades') || (this.elName === 've-radar' && e.event.target.__dimIdx === undefined)) return
      const { chartData, chartResponse, chartSettings } = this.element.content
      if (chartAlias === 've-scatter-normal') {
        this.getChartPopupData(e)
        return
      }
      const {
        dimension: dimensionSettings,
      } = chartSettings
      const metricMap = metricAllList.filter(m => !m.isDesensibilisation).map((m, i) => {
        return {
          originalName: m.alias || m.labeName,
          aliasName: m.aliasName,
          name: m.lang_alias,
          metricKeyName: m.keyName,
        }
      })

      let { rows, } = this.$_deepClone(chartData)
      const columns = chartResponse.dimension.concat(chartResponse.metrics)
      // 散点图特殊处理
      if (!Array.isArray(rows)) rows = Object.values(rows).map(r => r[0])
      // 散点图chartSettings的维度数据比较特殊，所以从chartUserConfig中取值
      const dimensionAliasList = dimensionList.map(item => item.alias || item.labeName)
      const extendDimension = dimensionExtendList.map(item => item.alias || item.labeName)

      // 点击区域对应维度值
      let clickVaule = [e.name]
      if (e.name === 'mobileClickArea') {
        // 点击折线图的扩充点击区域
        clickVaule = [rows[e.value][`VIEWFORMAT_${columns[dimensionList.length - 1]}`]]
      } else if (['xAxis', 'yAxis'].includes(e.componentType)) {
        // 点击标签
        clickVaule = e.value ? [e.value] : [e.name]
      } else if (this.elName === 've-scatter') {
        clickVaule = [e.seriesName]
      } else if (this.elName === 've-bar') {
        clickVaule = [e.name]
      } else if (this.elName === 've-radar') {
        clickVaule = [this.chartData.rows[e.event.target.__dimIdx][`VIEWFORMAT_${ dimensionSettings[0] }`]]
      } else if (this.elName === 've-themeRiver') {
        clickVaule = []
      } else if (this.elName === 've-calendar') {
        clickVaule = [e.value[0]]
      } else if (chartAlias === 've-roundCascades' && e.componentType === 'markLine') {
        // 层叠圆形图点击标签钻取时，需要特殊处理
        clickVaule = [this.chartData.rows[e.seriesIndex]?.[dimensionAliasList[0]]]
      }

      const options = this.echartInstance.getOption()
      let color = []
      const isDimension = colorType === 'dimension' && hasDimensionColor(chartAlias, dimensionList.length)
      if (isDimension) {
        let dimenColors = Color.getDimensionColor(this)
        if (['ve-themeRiver'].includes(chartAlias) || (chartAlias === 've-wordcloud' && wordCloudSetting.colorFieldKey)) {
          color = dimenColors.noRepeat
        } else {
          color = dimenColors.repeat
        }
      } else if (chartAlias === 've-gauge-normal') {
        color = [options.series[0].axisLine.lineStyle.color[0][1]]
      } else {
        const defaultColors = Color.getDefaultChartColor(this.chartUserConfig, { colorThemeType: this.themeType, vm: this })
        // 避免维度扩展数据过多，颜色显示不全
        color = [...options.color, ...defaultColors, ...defaultColors]
      }

      let activeDimension = clickVaule
      const STR = 'VIEWFORMAT_'

      function createDataItem(row, metricIndex, colorIndex) {
        let _resData = {
          currentRow: row,
        }
        if (typeof metricIndex === 'number') {
          _resData.metricValue = row[`${ STR }${ metricMap[metricIndex].originalName }`] || row[metricMap[metricIndex].originalName]
          _resData.metricKeyName = metricMap[metricIndex]?.keyName
          _resData.metric = metricMap[metricIndex].name
        } else if (metricIndex) {
          _resData.metricValue = row[`${ STR }${ metricIndex }`] || row[metricIndex]
          _resData.metric = metricIndex
        }
        _resData.legendName = _resData.metric
        _resData.color = Color.getColorDirection(typeof colorIndex === 'number' ? color[colorIndex] : colorIndex)
        return _resData
      }

      let result = []
      if (chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH) {
        const { max, min, bw } = getBandwidthData(this.chartUserConfig)
        const keyMax = max?.alias || max?.labeName || `MAX_${bw.alias || bw.labeName}`
        const keyMin = min?.alias || min?.labeName || `MIN_${bw.alias || bw.labeName}`
        result = rows.map(item => ({
          color: Color.getColorDirection(color[0]),
          dimension: dimensionSettings.map(key => item[`${STR}${key}`] || item[key]),
          metric: [...metricMap.map((key, index) => ({
            ...createDataItem(item, index, 0),
          })), {
            ...createDataItem(item, keyMax, 1),
            metric: this.$t(`sdp.views.maximumBandwidth`),
            legendName: this.$t('sdp.views.bandwidth'),
          }, {
            ...createDataItem(item, keyMin, 1),
            metric: this.$t(`sdp.views.minimumBandwidth`),
            legendName: this.$t('sdp.views.bandwidth'),
          }]
        }))
      } else if (chartAlias === 've-gauge-normal') {
        result = [{
          dimension: [],
          metric: [{ ...createDataItem(rows[0], 'COMPLETION_RATE', 0), }],
          color: Color.getColorDirection(color[0]),
        }]
      } else if (chartAlias === 've-themeRiver') {
        rows.forEach((currentRow, rowIndex) => {
          if (result.length && result.find(r => r.dimension[0] === currentRow[STR + dimensionAliasList[0]])) return
          const dimensionItem = {
            dimension: [currentRow[STR + dimensionAliasList[0]]],
            metric: [],
            color: '',
          }
          const sameDimension = rows.filter(row => row[STR + dimensionAliasList[0]] === currentRow[STR + dimensionAliasList[0]])
          dimensionItem.metric = sameDimension.map(row => {
            let itemColor = color.find(dc => dc.name === row[dimensionAliasList[1]]).color
            return {
              ...createDataItem(row, '' + row[dimensionAliasList[1]], itemColor),
              metricValue: row[STR + columns[2]],
            }
          })
          result.push(dimensionItem)
        })
      } else {
        // 存在层级关系图形
        const hierarchyChart = ['ve-sunburst', 've-treemap']
        // 扩展维度最小层级数据长度
        let seriesLen = Math.max(...rows.map(item => item.extendData || []).map(item => item.length))
        let baseSeriesLen = Math.max(...rows.map(item => item.extendData || []).map(item => item.length))
        let isComposite = chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE
        const { enable, compositeTypes = [] } = mainDimensionMeasureConfig

        // 获取扩展维度颜色列表
        const tempExtendDimensionData = getExtendDimensionData.call(this, { chartResponseData: rows, chartUserConfig: this.chartUserConfig, dimensionSortList: chartResponse.extendDims })
        let extendDimensionColorList = extendDimensionColor ? tempExtendDimensionData.extendDimensionColorList : []
        let complationRate = false
        if (['ve-ring-multiple', 've-bar-percent'].includes(chartAlias)) {
          complationRate = this.chartUserConfig.complationRate || false
        }
        let rowsData = rows
        if (this.othersPieDataIndex > -1) {
          rowsData = rowsData.concat(rows[this.othersPieDataIndex].others)
        }
        const showMeasureInLegend = displayMeasureInLegend(this.chartUserConfig, true)
        rowsData.map((currentRow, rowIndex) => {
          let dimensionKey = chartAlias === 've-calendar' ? 'VFMT_RAW_' : STR
          const dimensionLabel = dimensionAliasList.map(dl => currentRow[dimensionKey + dl] || currentRow[dl])
          const dimensionItem = {
            dimension: dimensionLabel, // 弹框展示数据(hover数据)
            metric: [],
            color: Color.getColorDirection(color[rowIndex]),
            seriesIndex: this.othersPieDataIndex > -1 && rowIndex >= chartData.rows.length ? 1 : 0,
            dataIndex: rowIndex >= chartData.rows.length ? rowIndex - chartData.rows.length : rowIndex,
          }
          // 当设置日期偏移时，弹框展示数据（hover数据）与交互数据（标签数据）不同，需要特殊处理
          if (this.isTimeSplitingOffsetChart && currentRow[`${ STR }HOVER_${dimensionAliasList[0]}`]) {
            const dimensionHoverKey = STR + 'HOVER_'
            if (currentRow[`${ STR }${dimensionAliasList[0]}`] === clickVaule[0]) {
              activeDimension = [currentRow[`${dimensionHoverKey}${dimensionAliasList[0]}`]]
            }
            const dimensionHover = dimensionAliasList.map(dl => currentRow[`${ STR }HOVER_${dl}`] || currentRow[`${ STR }${dl}`])
            Object.assign(dimensionItem, {
              dimension: dimensionHover, // 弹框展示数据
              labelDimension: dimensionLabel, // 交互数据
            })
          } else if (HOLIDAY_STYLE_CHART.includes(chartAlias) && dimensionAliasList.some(dl => currentRow[`VFMT_HOLI_${dl}`])) {
            // 节假日显示
            const dimensionHover = dimensionAliasList.map(dl => {
              let dimensionName = currentRow[`${ STR }HOVER_${dl}`] || currentRow[`${ STR }${dl}`]
              return currentRow[`VFMT_HOLI_${dl}`] || dimensionName
            })
            Object.assign(dimensionItem, {
              dimension: dimensionHover, // 弹框展示数据
              labelDimension: dimensionLabel, // 交互数据
              matchDimension: dimensionLabel, // 弹框展示数据与X轴标签数据不同，高亮无法匹配，此时取matchDimension匹配
            })
          }
          if (currentRow.isAccumulative || currentRow.isContrast) {
            const name = currentRow.isAccumulative ? waterfallSetting.accumulative.name : waterfallSetting.contrast.name
            Object.assign(dimensionItem, { dimension: [name], disableClick: true })
          }
          // 匹配当前子级数据
          let isChildSelect = false
          currentRow.hasOwnProperty('extendData') && currentRow.extendData.map(item => {
            Object.keys(item).map(par => {
              item[par] === clickVaule[0] && (isChildSelect = true)
            })
          })
          // 点击区域对应双维度值
          Object.keys(currentRow).map(key => {
            if ((currentRow[key] === clickVaule[0] || isChildSelect) && activeDimension.length !== 2 && dimensionList.length === 2) {
              // 存在层级关系图形即可点击父级也可点击子级 当点击父级时，把子级数据放在父级数据前面
              if (hierarchyChart.includes(chartAlias) && dimensionItem.dimension[0] === clickVaule[0]) {
                activeDimension.unshift(dimensionItem.dimension[1])
              } else if (e.dataIndex === undefined || rowIndex === e.dataIndex) {
                activeDimension.push(dimensionItem.dimension[0])
              }
            }
          })
          const metricData = []
          if (['ve-ring-multiple', 've-bar-percent'].includes(chartAlias)) {
            const isPositive = currentRow['COMPLETION_RATE'] >= 0
            let realComplateRate = `${isPositive ? '' : '-'}${currentRow[STR + 'COMPLETION_RATE'].replace(/[^0-9.]/g, '')}`
            const rangeColor = (complationRate && currentRow['COMPLETION_RATE'] !== '') ? gaugeConfigs.getCompletionColor(Number(realComplateRate), complationRate.rateArray) : { index: -1 }
            metricData.push({
              ...createDataItem(currentRow, 0, rangeColor.index > -1 ? rangeColor.color : rowIndex),
              metricValue: `${ currentRow[STR + metricMap[0].originalName] }(${ currentRow[STR + 'COMPLETION_RATE'] })`,
            })
          } else {
            // 对比值取值
            const contrastAlias = contrastList?.[0]?.alias || contrastList?.[0]?.labeName
            metricMap.map((metricItem, index) => {
              const currentColumn = metricItem.originalName
                const pieChart = ['ve-pie-normal', 've-pie-rose', 've-ring-normal', 've-liquidfill', 've-wordcloud', 've-ring-multiple']
                let metricKeyIndex = index
                let customMetricAliasAry = []

                if (labelLineShow && CONTROL_MEASURES_CHART.includes(chartAlias) && CUSTOM_METRIC.includes(chartAlias)) {
                  if (metricKeyIndex > -1 && metricLabelDisplay[metricKeyIndex].labelType === 'custom') {
                    const currentCustomMetric = this.chartSettings.customMetric?.[metricKeyIndex]
                    currentCustomMetric?.length && customMetricAliasAry.push(...currentCustomMetric)
                  }
                }
                // 矩形树图只拖拽一个维度字段时，返回数据无层级关系
                if (!dimensionExtendList.length && (!hierarchyChart.includes(chartAlias) || dimensionList.length === 1)) {
                  let percent = ''
                  if (chartAlias === 've-funnel') {
                    // 转换率计算方式
                    const key = funnelSettings.conversionRateAlgorithm === 'previousPercent' ? STR + 'METRIC_PERCENT_DIM' : STR + 'METRIC_PERCENT'
                    percent = '(' + currentRow[`${key}_${currentColumn}`] + ')'
                  }
                  let metricColor = color[(isDimension || pieChart.includes(chartAlias)) ? rowIndex : index]
                  if (chartAlias === 've-waterfall' && !isDimension) {
                    if (currentRow.isAccumulative || currentRow.isContrast) {
                      metricColor = currentRow.isAccumulative ? color[2] : color[3]
                    } else {
                      // 正常数据取色需要根据正负值
                      metricColor = Number(currentRow['VFMT_DIFF_' + currentColumn]) > 0 ? color[0] : color[1]
                    }
                  } else if (chartAlias === 've-wordcloud' && wordCloudSetting.colorFieldKey) {
                    metricColor = color.find(colorItem => colorItem.name === currentRow[wordCloudSetting.colorSeriesProp])
                  }
                  const key = currentRow.isAccumulative ? `${ STR }TOTAL_${currentColumn}` : currentRow.isContrast ? `${ STR }TOTAL_${contrastAlias}` : `${ STR }${currentColumn}`
                  // 饼类图取色
                  let metricName = metricKeyIndex === -1 || metricKeyIndex >= metricMap.length ? currentColumn : metricMap[metricKeyIndex].name
                  if (metricKeyIndex === -1 && chartAlias === 've-liquidfill') {
                    metricName = chartSettings.target[0]
                  }
                  const currentMetricResult = {
                    ...createDataItem(currentRow, metricKeyIndex === -1 ? currentColumn : metricKeyIndex, metricColor),
                    metric: metricName,
                    metricValue: currentRow[key] + percent,
                    legendName: pieChart.includes(chartAlias) ? dimensionItem.dimension[0] : metricName,
                  }
                  metricData.push(currentMetricResult)
                  const customMetricData = []
                  if (customMetricAliasAry.length) {
                    customMetricAliasAry.forEach(customMetricAlias => {
                      setCustomMetric(customMetricData, currentMetricResult, customMetricAlias, customMetricAlias)
                    })
                    metricData.push(...customMetricData)
                  }
                } else {
                  // 如果开了主维度度量，需要拓展extendData数据
                  let arr = currentRow.extendData
                  // 组合图当前主维度的配置
                  let currentType = null
                  if (MAIN_DIMENSION_MEASURE_CHART.includes(chartAlias) && enable && dimensionList.length === 1 && dimensionExtendList.length === 1) {
                    // 计算出的所有扩展维度数据
                    let _extendDimensionData = tempExtendDimensionData.extendDimensionData
                    let isAddMainDimension = false
                    // 组合图
                    if (isComposite) {
                      isAddMainDimension = !!compositeTypes.find(c => c.enable)
                      const metricKeyName = metricItem.metricKeyName
                      let flag = false
                      let needFilter = true
                      compositeTypes.forEach(o => {
                        const { type } = o
                        if (o.enable && metricsContainer[type]?.length && metricsContainer[type][0]['keyName'] === metricKeyName) {
                          flag = true
                          needFilter = false
                          currentType = o
                        }
                      })
                      isAddMainDimension = isAddMainDimension && flag

                      if (needFilter) {
                        _extendDimensionData = tempExtendDimensionData.extendDimensionData.map(item => {
                          return item.filter(o => !o.isMainDimensionMeasure)
                        })
                      } else {
                        if (currentType?.onlyMain) {
                          _extendDimensionData = tempExtendDimensionData.extendDimensionData.map(item => {
                            return item.filter(o => o.isMainDimensionMeasure)
                          })
                        }
                      }
                    } else {
                      // 非组合图
                      isAddMainDimension = metricAllList.length === 1
                    }
                    if (isAddMainDimension) {
                      const mainDimension = this.$_deepClone(currentRow)
                      delete mainDimension.extendData
                      const copyPrefix = ['', 'VIEWFORMAT_', 'VFMT_RAW_']
                      copyPrefix.forEach(key => {
                        mainDimension[`${key}${dimensionExtendList[0].alias || dimensionExtendList[0].labeName}`] = mainDimension[dimensionList[0].alias || dimensionList[0].labeName]
                      })
                      // 通过isMainDimensionMeasure区分是否为主维度还是拓展维度
                      mainDimension.isMainDimensionMeasure = true
                      arr = [ mainDimension, ...currentRow.extendData ]
                      if (currentType?.onlyMain) {
                        arr = [ mainDimension ]
                      }
                      // 组合图开启主维度显示时需要以第一个series来算颜色
                      if (index === 0) {
                        seriesLen = Math.max(..._extendDimensionData.map(item => item.length))
                      }
                    }
                  }
                  arr.map((extendItem, extendIndex) => {
                    let metricName = extendDimension.map(dimen => extendItem[dimen])
                    const dimensionName = dimensionAliasList[1] || dimensionAliasList[0]
                    if (extendItem.isMainDimensionMeasure) {
                      if (chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE) {
                        const types = mainDimensionMeasureConfig.compositeTypes
                        let o = types.find(item => {
                          return metricsContainer[item.type]?.length && metricsContainer[item.type][0]['keyName'] === metricMap[metricKeyIndex].metricKeyName
                        })
                        if (o?.legendName) metricName.push(o?.legendName)
                      } else {
                        let _name = (mainDimensionMeasureConfig.legendName || '').trim()
                        if (_name) metricName.push(_name)
                      }
                    }
                    const metric = showMeasureInLegend ? [...metricName, metricMap[metricKeyIndex].name].join(' - ') : metricName.join(' - ')
                    // 扩展维度取色规则：扩展维度数据长度小于最大长度时，没有数据也占颜色，一个扩展数据以最大长度为基准来取色
                    let extendColorIndex = isDimension ? rowIndex : index !== 0 ? extendIndex + index * seriesLen : extendIndex
                    if (extendDimensionColor) {
                      let curExtendColorIndex = extendDimensionColorList.findIndex(item => item.dimension === metricName.join('-'))
                      extendColorIndex = curExtendColorIndex !== -1 ? curExtendColorIndex : extendColorIndex
                    }
                    const _data = hierarchyChart.includes(chartAlias) ? {
                      ...createDataItem(extendItem, metricKeyIndex, rowIndex),
                      metric: extendItem[dimensionName],
                      metricValue: `${extendItem[STR + currentColumn]}(${extendItem[STR + 'METRIC_PERCENT_' + currentColumn]})`,
                      legendName: extendItem[dimensionName],
                    } : {
                      ...createDataItem(extendItem, metricKeyIndex, extendColorIndex),
                      metric,
                      legendName: metric,
                    }
                    metricData.push(_data)
                    const customMetricData = []
                    if (customMetricAliasAry.length) {
                      customMetricAliasAry.forEach(customMetricAlias => {
                        const customMetric = showMeasureInLegend ? [...metricName, customMetricAlias].join(' - ') : metricName.join(' - ')
                        setCustomMetric(customMetricData, _data, customMetricAlias, customMetric)
                      })
                    }
                    metricData.push(...customMetricData)
                  })
                }
              })
          }
          dimensionItem.metric = metricData
          result.push(dimensionItem)
        })
        // 无维度瀑布图特殊处理
        const metricsResponse = chartResponse.metrics
        if (chartAlias === 've-waterfall' && metricsResponse.length > 1 && waterfallSetting.accumulative && waterfallSetting.accumulative.show) {
          result.push({
            disableClick: true,
            color: Color.getColorDirection(color[rows.length]),
            dimension: [waterfallSetting.accumulative.name],
            metric: [{
              ...createDataItem(rows[0], `TOTAL_${metricsResponse[metricsResponse.length - 1]}`, this.chartUserConfig.colors[2]),
              metric: waterfallSetting.accumulative.name,
              legendName: waterfallSetting.accumulative.name,
            }],
          })
        }
      }
      // 设置预置维度标签，location选择维度标签时，禁止钻取; location选择报表层级树且钻取返回数据为空，禁止下一次钻取
      const disableDrill = this.presetDimensionLabel && (this.locationSelectType === 'label' || (this.locationSelectType === 'dimension' && this.disableDrill))

      if (isReverseAxisChart({ chartUserConfig: this.chartUserConfig })) {
        result.reverse()
      }
      const obj = {
        result,
        el: this.element,
        activeDimension,
        echartInstance: this.echartInstance,
        disableDrill: disableDrill,
        screenType: this.screenType,
        legendSelected: this.legendSelected,
      }
      if (chartAlias === 've-calendar') {
        obj.calendarRealData = this.calendarRealData
      }
      this.$emit('cube-popup', obj)
      function setCustomMetric(customMetricData = [], currentMetricResult, customMetricAlias, metricAlias) {
        customMetricData.push({
          metric: metricAlias,
          metricValue: currentMetricResult.currentRow[`${ STR }${customMetricAlias}`],
          currentRow: currentMetricResult.currentRow,
          metricKeyName: currentMetricResult.metricKeyName,
          labelType: 'customMetric',
          legendName: currentMetricResult.metric,
        })
      }
    },
    showDimensionPupup() {
      this.mobileClick({})
    },
    handleSingleOperateClick(e) {
      if (!this.isMobile || this.isChartSet) return
      // 是否有交互
      const interactionOptions = this.getFilteredInterActionOptions()
      const hasInteraction = !!(interactionOptions?.[0]?.associElements?.length)
      const hasDrill = !!(this.content.drillList?.length && this.isSetDrill())
      const hasSuperLink = !!this.superLinkDimensionClick

      const cnt = hasInteraction + hasDrill + hasSuperLink
      if (cnt === 1 || (hasInteraction && hasDrill)) {
        const { activeDimension, result } = this.getMobileClickActive(e)

        const curDimension = result.find(dimensionItem => dimensionItem.dimension.join('-') === activeDimension.join('-'))
        if (curDimension && curDimension.disableClick) return

        let clickDimension = [...activeDimension].reverse()

        // 设置自定义日期维度时，弹窗展示数据和交互钻取数据不同，需要特殊处理
        if (curDimension?.labelDimension) {
          clickDimension = curDimension.labelDimension
        }
        if (hasInteraction) {
          this.handleChart(clickDimension, true)
        } else if (hasSuperLink) {
          this.handleChart(clickDimension, true, true)
        } else if (hasDrill) {
          this.handleChart(clickDimension, true)
        }
      } else if (cnt === 2) {
      }
    },
    getMobileClickActive(e) {
      // 移动端点击图形任意区域, 弹出hover层, 显示图例和指标信息
      if (!this.isMobile || this.isChartSet) return
      const { chartAlias, colorType, dimensionColors = [], funnelSettings = {}, waterfallSetting, contrastList, labelLineShow, metricLabelDisplay = [], extendDimensionColor, wordCloudSetting = {}, mainDimensionMeasureConfig = {}, metricsContainer } = this.chartUserConfig
      const { dimensionList, dimensionExtendList, metricAllList } = this.UserConfig
      // 仪表盘特殊处理（辅助线和预警线无需弹框、目前根据borderType区分辅助线和预警线）
      if (chartAlias === 've-gauge-normal' && e.data?.itemStyle?.borderType === 'dashed') return
      // 移动端的折线图和折线面积图使用markLine补充点击事件
      if (e.componentType === 'graphic' || e.targetType === 'axisName' || (e.componentType === 'markLine' && e.name !== 'mobileClickArea' && chartAlias !== 've-roundCascades') || (this.elName === 've-radar' && e.event.target.__dimIdx === undefined)) return
      const { chartData, chartResponse, chartSettings } = this.element.content
      if (chartAlias === 've-scatter-normal') {
        return this.getChartPopupData(e, { isMock: true })
      }
      const {
        dimension: dimensionSettings,
      } = chartSettings
      const metricMap = metricAllList.filter(m => !m.isDesensibilisation).map((m, i) => {
        return {
          originalName: m.alias || m.labeName,
          aliasName: m.aliasName,
          name: m.lang_alias,
          metricKeyName: m.keyName,
        }
      })

      let { rows, } = this.$_deepClone(chartData)
      const columns = chartResponse.dimension.concat(chartResponse.metrics)
      // 散点图特殊处理
      if (!Array.isArray(rows)) rows = Object.values(rows).map(r => r[0])
      // 散点图chartSettings的维度数据比较特殊，所以从chartUserConfig中取值
      const dimensionAliasList = dimensionList.map(item => item.alias || item.labeName)
      const extendDimension = dimensionExtendList.map(item => item.alias || item.labeName)

      // 点击区域对应维度值
      let clickVaule = [e.name]
      if (e.name === 'mobileClickArea') {
        // 点击折线图的扩充点击区域
        clickVaule = [rows[e.value][`VIEWFORMAT_${columns[dimensionList.length - 1]}`]]
      } else if (['xAxis', 'yAxis'].includes(e.componentType)) {
        // 点击标签
        clickVaule = e.value ? [e.value] : [e.name]
      } else if (this.elName === 've-scatter') {
        clickVaule = [e.seriesName]
      } else if (this.elName === 've-bar') {
        clickVaule = [e.name]
      } else if (this.elName === 've-radar') {
        clickVaule = [this.chartData.rows[e.event.target.__dimIdx][`VIEWFORMAT_${ dimensionSettings[0] }`]]
      } else if (this.elName === 've-themeRiver') {
        clickVaule = []
      } else if (this.elName === 've-calendar') {
        clickVaule = [e.value[0]]
      } else if (chartAlias === 've-roundCascades' && e.componentType === 'markLine') {
        // 层叠圆形图点击标签钻取时，需要特殊处理
        clickVaule = [this.chartData.rows[e.seriesIndex]?.[dimensionAliasList[0]]]
      }

      const options = this.echartInstance.getOption()
      let color = []
      const isDimension = colorType === 'dimension' && hasDimensionColor(chartAlias, dimensionList.length)
      if (isDimension) {
        let dimenColors = Color.getDimensionColor(this)
        if (['ve-themeRiver'].includes(chartAlias) || (chartAlias === 've-wordcloud' && wordCloudSetting.colorFieldKey)) {
          color = dimenColors.noRepeat
        } else {
          color = dimenColors.repeat
        }
      } else if (chartAlias === 've-gauge-normal') {
        color = [options.series[0].axisLine.lineStyle.color[0][1]]
      } else {
        const defaultColors = Color.getDefaultChartColor(this.chartUserConfig, { colorThemeType: this.themeType, vm: this })
        // 避免维度扩展数据过多，颜色显示不全
        color = [...options.color, ...defaultColors, ...defaultColors]
      }

      let activeDimension = clickVaule
      const STR = 'VIEWFORMAT_'

      function createDataItem(row, metricIndex, colorIndex) {
        let _resData = {
          currentRow: row,
        }
        if (typeof metricIndex === 'number') {
          _resData.metricValue = row[`${ STR }${ metricMap[metricIndex].originalName }`] || row[metricMap[metricIndex].originalName]
          _resData.metricKeyName = metricMap[metricIndex]?.keyName
          _resData.metric = metricMap[metricIndex].name
        } else if (metricIndex) {
          _resData.metricValue = row[`${ STR }${ metricIndex }`] || row[metricIndex]
          _resData.metric = metricIndex
        }
        _resData.legendName = _resData.metric
        _resData.color = Color.getColorDirection(typeof colorIndex === 'number' ? color[colorIndex] : colorIndex)
        return _resData
      }

      let result = []
      if (chartAlias === CHART_ALIAS_TYPE.VE_BANDWIDTH) {
        const { max, min, bw } = getBandwidthData(this.chartUserConfig)
        const keyMax = max?.alias || max?.labeName || `MAX_${bw.alias || bw.labeName}`
        const keyMin = min?.alias || min?.labeName || `MIN_${bw.alias || bw.labeName}`
        result = rows.map(item => ({
          color: Color.getColorDirection(color[0]),
          dimension: dimensionSettings.map(key => item[`${STR}${key}`] || item[key]),
          metric: [...metricMap.map((key, index) => ({
            ...createDataItem(item, index, 0),
          })), {
            ...createDataItem(item, keyMax, 1),
            metric: this.$t(`sdp.views.maximumBandwidth`),
            legendName: this.$t('sdp.views.bandwidth'),
          }, {
            ...createDataItem(item, keyMin, 1),
            metric: this.$t(`sdp.views.minimumBandwidth`),
            legendName: this.$t('sdp.views.bandwidth'),
          }]
        }))
      } else if (chartAlias === 've-gauge-normal') {
        result = [{
          dimension: [],
          metric: [{ ...createDataItem(rows[0], 'COMPLETION_RATE', 0), }],
          color: Color.getColorDirection(color[0]),
        }]
      } else if (chartAlias === 've-themeRiver') {
        rows.forEach((currentRow, rowIndex) => {
          if (result.length && result.find(r => r.dimension[0] === currentRow[STR + dimensionAliasList[0]])) return
          const dimensionItem = {
            dimension: [currentRow[STR + dimensionAliasList[0]]],
            metric: [],
            color: '',
          }
          const sameDimension = rows.filter(row => row[STR + dimensionAliasList[0]] === currentRow[STR + dimensionAliasList[0]])
          dimensionItem.metric = sameDimension.map(row => {
            let itemColor = color.find(dc => dc.name === row[dimensionAliasList[1]]).color
            return {
              ...createDataItem(row, '' + row[dimensionAliasList[1]], itemColor),
              metricValue: row[STR + columns[2]],
            }
          })
          result.push(dimensionItem)
        })
      } else {
        // 存在层级关系图形
        const hierarchyChart = ['ve-sunburst', 've-treemap']
        // 扩展维度最小层级数据长度
        let seriesLen = Math.max(...rows.map(item => item.extendData || []).map(item => item.length))
        let baseSeriesLen = Math.max(...rows.map(item => item.extendData || []).map(item => item.length))
        let isComposite = chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE
        const { enable, compositeTypes = [] } = mainDimensionMeasureConfig

        // 获取扩展维度颜色列表
        const tempExtendDimensionData = getExtendDimensionData.call(this, { chartResponseData: rows, chartUserConfig: this.chartUserConfig, dimensionSortList: chartResponse.extendDims })
        let extendDimensionColorList = extendDimensionColor ? tempExtendDimensionData.extendDimensionColorList : []
        let complationRate = false
        if (['ve-ring-multiple', 've-bar-percent'].includes(chartAlias)) {
          complationRate = this.chartUserConfig.complationRate || false
        }
        let rowsData = rows
        if (this.othersPieDataIndex > -1) {
          rowsData = rowsData.concat(rows[this.othersPieDataIndex].others)
        }
        const showMeasureInLegend = displayMeasureInLegend(this.chartUserConfig, true)
        rowsData.map((currentRow, rowIndex) => {
          let dimensionKey = chartAlias === 've-calendar' ? 'VFMT_RAW_' : STR
          const dimensionLabel = dimensionAliasList.map(dl => currentRow[dimensionKey + dl] || currentRow[dl])
          const dimensionItem = {
            dimension: dimensionLabel, // 弹框展示数据(hover数据)
            metric: [],
            color: Color.getColorDirection(color[rowIndex]),
            seriesIndex: this.othersPieDataIndex > -1 && rowIndex >= chartData.rows.length ? 1 : 0,
            dataIndex: rowIndex >= chartData.rows.length ? rowIndex - chartData.rows.length : rowIndex,
          }
          // 当设置日期偏移时，弹框展示数据（hover数据）与交互数据（标签数据）不同，需要特殊处理
          if (this.isTimeSplitingOffsetChart && currentRow[`${ STR }HOVER_${dimensionAliasList[0]}`]) {
            const dimensionHoverKey = STR + 'HOVER_'
            if (currentRow[`${ STR }${dimensionAliasList[0]}`] === clickVaule[0]) {
              activeDimension = [currentRow[`${dimensionHoverKey}${dimensionAliasList[0]}`]]
            }
            const dimensionHover = dimensionAliasList.map(dl => currentRow[`${ STR }HOVER_${dl}`] || currentRow[`${ STR }${dl}`])
            Object.assign(dimensionItem, {
              dimension: dimensionHover, // 弹框展示数据
              labelDimension: dimensionLabel, // 交互数据
            })
          } else if (HOLIDAY_STYLE_CHART.includes(chartAlias) && dimensionAliasList.some(dl => currentRow[`VFMT_HOLI_${dl}`])) {
            // 节假日显示
            const dimensionHover = dimensionAliasList.map(dl => {
              let dimensionName = currentRow[`${ STR }HOVER_${dl}`] || currentRow[`${ STR }${dl}`]
              return currentRow[`VFMT_HOLI_${dl}`] || dimensionName
            })
            Object.assign(dimensionItem, {
              dimension: dimensionHover, // 弹框展示数据
              labelDimension: dimensionLabel, // 交互数据
              matchDimension: dimensionLabel, // 弹框展示数据与X轴标签数据不同，高亮无法匹配，此时取matchDimension匹配
            })
          }
          if (currentRow.isAccumulative || currentRow.isContrast) {
            const name = currentRow.isAccumulative ? waterfallSetting.accumulative.name : waterfallSetting.contrast.name
            Object.assign(dimensionItem, { dimension: [name], disableClick: true })
          }
          // 匹配当前子级数据
          let isChildSelect = false
          currentRow.hasOwnProperty('extendData') && currentRow.extendData.map(item => {
            Object.keys(item).map(par => {
              item[par] === clickVaule[0] && (isChildSelect = true)
            })
          })
          // 点击区域对应双维度值
          Object.keys(currentRow).map(key => {
            if ((currentRow[key] === clickVaule[0] || isChildSelect) && activeDimension.length !== 2 && dimensionList.length === 2) {
              // 存在层级关系图形即可点击父级也可点击子级 当点击父级时，把子级数据放在父级数据前面
              if (hierarchyChart.includes(chartAlias) && dimensionItem.dimension[0] === clickVaule[0]) {
                activeDimension.unshift(dimensionItem.dimension[1])
              } else if (e.dataIndex === undefined || rowIndex === e.dataIndex) {
                activeDimension.push(dimensionItem.dimension[0])
              }
            }
          })
          const metricData = []
          if (['ve-ring-multiple', 've-bar-percent'].includes(chartAlias)) {
            const isPositive = currentRow['COMPLETION_RATE'] >= 0
            let realComplateRate = `${isPositive ? '' : '-'}${currentRow[STR + 'COMPLETION_RATE'].replace(/[^0-9.]/g, '')}`
            const rangeColor = (complationRate && currentRow['COMPLETION_RATE'] !== '') ? gaugeConfigs.getCompletionColor(Number(realComplateRate), complationRate.rateArray) : { index: -1 }
            metricData.push({
              ...createDataItem(currentRow, 0, rangeColor.index > -1 ? rangeColor.color : rowIndex),
              metricValue: `${ currentRow[STR + metricMap[0].originalName] }(${ currentRow[STR + 'COMPLETION_RATE'] })`,
            })
          } else {
            // 对比值取值
            const contrastAlias = contrastList?.[0]?.alias || contrastList?.[0]?.labeName
            metricMap.map((metricItem, index) => {
              const currentColumn = metricItem.originalName
              const pieChart = ['ve-pie-normal', 've-pie-rose', 've-ring-normal', 've-liquidfill', 've-wordcloud', 've-ring-multiple']
              let metricKeyIndex = index
              let customMetricAliasAry = []

              if (labelLineShow && CONTROL_MEASURES_CHART.includes(chartAlias) && CUSTOM_METRIC.includes(chartAlias)) {
                if (metricKeyIndex > -1 && metricLabelDisplay[metricKeyIndex].labelType === 'custom') {
                  const currentCustomMetric = this.chartSettings.customMetric?.[metricKeyIndex]
                  currentCustomMetric?.length && customMetricAliasAry.push(...currentCustomMetric)
                }
              }
              // 矩形树图只拖拽一个维度字段时，返回数据无层级关系
              if (!dimensionExtendList.length && (!hierarchyChart.includes(chartAlias) || dimensionList.length === 1)) {
                let percent = ''
                if (chartAlias === 've-funnel') {
                  // 转换率计算方式
                  const key = funnelSettings.conversionRateAlgorithm === 'previousPercent' ? STR + 'METRIC_PERCENT_DIM' : STR + 'METRIC_PERCENT'
                  percent = '(' + currentRow[`${key}_${currentColumn}`] + ')'
                }
                let metricColor = color[(isDimension || pieChart.includes(chartAlias)) ? rowIndex : index]
                if (chartAlias === 've-waterfall' && !isDimension) {
                  if (currentRow.isAccumulative || currentRow.isContrast) {
                    metricColor = currentRow.isAccumulative ? color[2] : color[3]
                  } else {
                    // 正常数据取色需要根据正负值
                    metricColor = Number(currentRow['VFMT_DIFF_' + currentColumn]) > 0 ? color[0] : color[1]
                  }
                } else if (chartAlias === 've-wordcloud' && wordCloudSetting.colorFieldKey) {
                  metricColor = color.find(colorItem => colorItem.name === currentRow[wordCloudSetting.colorSeriesProp])
                }
                const key = currentRow.isAccumulative ? `${ STR }TOTAL_${currentColumn}` : currentRow.isContrast ? `${ STR }TOTAL_${contrastAlias}` : `${ STR }${currentColumn}`
                // 饼类图取色
                let metricName = metricKeyIndex === -1 || metricKeyIndex >= metricMap.length ? currentColumn : metricMap[metricKeyIndex].name
                if (metricKeyIndex === -1 && chartAlias === 've-liquidfill') {
                  metricName = chartSettings.target[0]
                }
                const currentMetricResult = {
                  ...createDataItem(currentRow, metricKeyIndex === -1 ? currentColumn : metricKeyIndex, metricColor),
                  metric: metricName,
                  metricValue: currentRow[key] + percent,
                  legendName: pieChart.includes(chartAlias) ? dimensionItem.dimension[0] : metricName,
                }
                metricData.push(currentMetricResult)
                const customMetricData = []
                if (customMetricAliasAry.length) {
                  customMetricAliasAry.forEach(customMetricAlias => {
                    setCustomMetric(customMetricData, currentMetricResult, customMetricAlias, customMetricAlias)
                  })
                  metricData.push(...customMetricData)
                }
              } else {
                // 如果开了主维度度量，需要拓展extendData数据
                let arr = currentRow.extendData
                // 组合图当前主维度的配置
                let currentType = null
                if (MAIN_DIMENSION_MEASURE_CHART.includes(chartAlias) && enable && dimensionList.length === 1 && dimensionExtendList.length === 1) {
                  // 计算出的所有扩展维度数据
                  let _extendDimensionData = tempExtendDimensionData.extendDimensionData
                  let isAddMainDimension = false
                  // 组合图
                  if (isComposite) {
                    isAddMainDimension = !!compositeTypes.find(c => c.enable)
                    const metricKeyName = metricItem.metricKeyName
                    let flag = false
                    let needFilter = true
                    compositeTypes.forEach(o => {
                      const { type } = o
                      if (o.enable && metricsContainer[type]?.length && metricsContainer[type][0]['keyName'] === metricKeyName) {
                        flag = true
                        needFilter = false
                        currentType = o
                      }
                    })
                    isAddMainDimension = isAddMainDimension && flag

                    if (needFilter) {
                      _extendDimensionData = tempExtendDimensionData.extendDimensionData.map(item => {
                        return item.filter(o => !o.isMainDimensionMeasure)
                      })
                    } else {
                      if (currentType?.onlyMain) {
                        _extendDimensionData = tempExtendDimensionData.extendDimensionData.map(item => {
                          return item.filter(o => o.isMainDimensionMeasure)
                        })
                      }
                    }
                  } else {
                    // 非组合图
                    isAddMainDimension = metricAllList.length === 1
                  }
                  if (isAddMainDimension) {
                    const mainDimension = this.$_deepClone(currentRow)
                    delete mainDimension.extendData
                    const copyPrefix = ['', 'VIEWFORMAT_', 'VFMT_RAW_']
                    copyPrefix.forEach(key => {
                      mainDimension[`${key}${dimensionExtendList[0].alias || dimensionExtendList[0].labeName}`] = mainDimension[dimensionList[0].alias || dimensionList[0].labeName]
                    })
                    // 通过isMainDimensionMeasure区分是否为主维度还是拓展维度
                    mainDimension.isMainDimensionMeasure = true
                    arr = [ mainDimension, ...currentRow.extendData ]
                    if (currentType?.onlyMain) {
                      arr = [ mainDimension ]
                    }
                    // 组合图开启主维度显示时需要以第一个series来算颜色
                    if (index === 0) {
                      seriesLen = Math.max(..._extendDimensionData.map(item => item.length))
                    }
                  }
                }
                arr.map((extendItem, extendIndex) => {
                  let metricName = extendDimension.map(dimen => extendItem[dimen])
                  const dimensionName = dimensionAliasList[1] || dimensionAliasList[0]
                  if (extendItem.isMainDimensionMeasure) {
                    if (chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE) {
                      const types = mainDimensionMeasureConfig.compositeTypes
                      let o = types.find(item => {
                        return metricsContainer[item.type]?.length && metricsContainer[item.type][0]['keyName'] === metricMap[metricKeyIndex].metricKeyName
                      })
                      if (o?.legendName) metricName.push(o?.legendName)
                    } else {
                      let _name = (mainDimensionMeasureConfig.legendName || '').trim()
                      if (_name) metricName.push(_name)
                    }
                  }
                  const metric = showMeasureInLegend ? [...metricName, metricMap[metricKeyIndex].name].join(' - ') : metricName.join(' - ')
                  // 扩展维度取色规则：扩展维度数据长度小于最大长度时，没有数据也占颜色，一个扩展数据以最大长度为基准来取色
                  let extendColorIndex = isDimension ? rowIndex : index !== 0 ? extendIndex + index * seriesLen : extendIndex
                  if (extendDimensionColor) {
                    let curExtendColorIndex = extendDimensionColorList.findIndex(item => item.dimension === metricName.join('-'))
                    extendColorIndex = curExtendColorIndex !== -1 ? curExtendColorIndex : extendColorIndex
                  }
                  const _data = hierarchyChart.includes(chartAlias) ? {
                    ...createDataItem(extendItem, metricKeyIndex, rowIndex),
                    metric: extendItem[dimensionName],
                    metricValue: `${extendItem[STR + currentColumn]}(${extendItem[STR + 'METRIC_PERCENT_' + currentColumn]})`,
                    legendName: extendItem[dimensionName],
                  } : {
                    ...createDataItem(extendItem, metricKeyIndex, extendColorIndex),
                    metric,
                    legendName: metric,
                  }
                  metricData.push(_data)
                  const customMetricData = []
                  if (customMetricAliasAry.length) {
                    customMetricAliasAry.forEach(customMetricAlias => {
                      const customMetric = showMeasureInLegend ? [...metricName, customMetricAlias].join(' - ') : metricName.join(' - ')
                      setCustomMetric(customMetricData, _data, customMetricAlias, customMetric)
                    })
                  }
                  metricData.push(...customMetricData)
                })
              }
            })
          }
          dimensionItem.metric = metricData
          result.push(dimensionItem)
        })
        // 无维度瀑布图特殊处理
        const metricsResponse = chartResponse.metrics
        if (chartAlias === 've-waterfall' && metricsResponse.length > 1 && waterfallSetting.accumulative && waterfallSetting.accumulative.show) {
          result.push({
            disableClick: true,
            color: Color.getColorDirection(color[rows.length]),
            dimension: [waterfallSetting.accumulative.name],
            metric: [{
              ...createDataItem(rows[0], `TOTAL_${metricsResponse[metricsResponse.length - 1]}`, this.chartUserConfig.colors[2]),
              metric: waterfallSetting.accumulative.name,
              legendName: waterfallSetting.accumulative.name,
            }],
          })
        }
      }
      // 设置预置维度标签，location选择维度标签时，禁止钻取; location选择报表层级树且钻取返回数据为空，禁止下一次钻取
      const disableDrill = this.presetDimensionLabel && (this.locationSelectType === 'label' || (this.locationSelectType === 'dimension' && this.disableDrill))

      if (isReverseAxisChart({ chartUserConfig: this.chartUserConfig })) {
        result.reverse()
      }
      const obj = {
        result,
        el: this.element,
        activeDimension,
        echartInstance: this.echartInstance,
        disableDrill: disableDrill,
        screenType: this.screenType,
        legendSelected: this.legendSelected,
      }
      if (chartAlias === 've-calendar') {
        obj.calendarRealData = this.calendarRealData
      }
      function setCustomMetric(customMetricData = [], currentMetricResult, customMetricAlias, metricAlias) {
        customMetricData.push({
          metric: metricAlias,
          metricValue: currentMetricResult.currentRow[`${ STR }${customMetricAlias}`],
          currentRow: currentMetricResult.currentRow,
          metricKeyName: currentMetricResult.metricKeyName,
          labelType: 'customMetric',
          legendName: currentMetricResult.metric,
        })
      }
      return obj
    },
    getChartPopupData(e, config = {}) {
      const { isMock = false } = config
      console.log(e)
      const { chartAlias, colorType, waterfallSetting = {}, labelLineShow, metricLabelDisplay = [], } = this.chartUserConfig
      const { dimensionList, metricAllList } = this.UserConfig
      const STR = 'VIEWFORMAT_'
      const dimensionKey = chartAlias === 've-calendar' ? 'VFMT_RAW_' : STR
      const dimensionHoverKey = STR + 'HOVER_'
      // 存在层级关系图形
      // const hierarchyChart = ['ve-sunburst', 've-treemap']

      const rowsChartData = this.chartData.rows // 原始数据
      const rowsData = this.$_deepClone(Array.isArray(rowsChartData) ? rowsChartData : Object.values(rowsChartData).map(r => r[0])) // 要遍历的数据
      const clickRow = e?.data?.row ? e.data.row || {} : {} // 点击的那一条数据
      let clickDataKey = STR + (chartAlias === 've-scatter-normal' ? dimensionList[dimensionList.length - 1].alias : '') // 数据高亮的标识字段

      const isDimension = colorType === 'dimension' && hasDimensionColor(chartAlias, dimensionList.length)
      let colorList = []
      if (isDimension) {
        let dimenColors = Color.getDimensionColor(this)
        if (chartAlias !== 've-themeRiver') {
          colorList = dimenColors.repeat
        } else {
          colorList = dimenColors.noRepeat
        }
      }
      let result = []
      let activeDimension = [clickRow[clickDataKey]]
      rowsData.map((currentRow, rowIndex) => {
        const dimensionItem = {
          dimension: [], // 弹框展示数据(hover数据)
          labelDimension: [],
          matchDimension: [],
          metric: [],
          color: Color.getColorDirection(colorList[rowIndex]),
          seriesIndex: this.othersPieDataIndex > -1 && rowIndex >= rowsChartData.length ? 1 : 0,
          dataIndex: rowIndex >= rowsChartData.length ? rowIndex - rowsChartData.length : rowIndex,
        }
        if (currentRow.isAccumulative || currentRow.isContrast) {
          const name = currentRow.isAccumulative ? waterfallSetting.accumulative.name : waterfallSetting.contrast.name
          Object.assign(dimensionItem, { dimension: [name], disableClick: true })
        } else {
          dimensionList.forEach(dem => {
            const dl = dem.alias
            const dimensionValue = currentRow[dimensionKey + dl] || currentRow[dl]
            let viewDimensionValue = ''
            // 当设置日期偏移时，弹框展示数据（hover数据）与交互数据（标签数据）不同，需要特殊处理
            if (this.isTimeSplitingOffsetChart) {
              viewDimensionValue = currentRow[`${ dimensionHoverKey }${ dl }`]
            } else if (HOLIDAY_STYLE_CHART.includes(chartAlias) && dimensionList.some(dd => currentRow[`VFMT_HOLI_${dd.alias}`])) {
              viewDimensionValue = currentRow[`VFMT_HOLI_${dl}`]
            }
            dimensionItem.dimension.push(viewDimensionValue || dimensionValue)
            dimensionItem.labelDimension.push(dimensionValue)
            dimensionItem.matchDimension.push(dimensionValue)
          })
        }

        // 点击区域对应双维度值
        if (activeDimension.length !== 2 && dimensionList.length === 2) {
          if (rowIndex === e.seriesIndex) {
            activeDimension.push(dimensionItem.dimension[0])
          }
        }
        const metricData = []
        metricAllList.map((metricItem, metricKeyIndex) => {
          if (metricItem.isDesensibilisation) return
          let customMetricAliasAry = []

          if (labelLineShow && CONTROL_MEASURES_CHART.includes(chartAlias) && CUSTOM_METRIC.includes(chartAlias)) {
            if (metricLabelDisplay[metricKeyIndex].labelType === 'custom') {
              const currentCustomMetric = this.chartSettings.customMetric?.[metricKeyIndex]
              currentCustomMetric?.length && customMetricAliasAry.push(...currentCustomMetric)
            }
          }

          let metricColor = colorList[isDimension ? rowIndex : metricKeyIndex]

          const key = `${ STR }${metricItem.alias}`
          // 饼类图取色
          const metricName = metricAllList[metricKeyIndex].lang_alias
          const currentMetricResult = {
            ...createDataItem(currentRow, metricKeyIndex, metricColor),
            metric: metricName,
            metricValue: currentRow[key],
            legendName: metricName,
          }
          metricData.push(currentMetricResult)
          const customMetricData = []
          if (customMetricAliasAry.length) {
            customMetricAliasAry.forEach(customMetricAlias => {
              setCustomMetric(customMetricData, currentMetricResult, customMetricAlias, customMetricAlias)
            })
            metricData.push(...customMetricData)
          }

        })

        dimensionItem.metric = metricData
        result.push(dimensionItem)
      })
      // 设置预置维度标签，location选择维度标签时，禁止钻取; location选择报表层级树且钻取返回数据为空，禁止下一次钻取
      const disableDrill = this.presetDimensionLabel && (this.locationSelectType === 'label' || (this.locationSelectType === 'dimension' && this.disableDrill))

      if (isReverseAxisChart({ chartUserConfig: this.chartUserConfig })) {
        result.reverse()
      }
      const obj = {
        result,
        el: this.element,
        activeDimension,
        echartInstance: this.echartInstance,
        disableDrill: disableDrill,
        screenType: this.screenType,
        legendSelected: this.legendSelected,
      }
      if (isMock) {
        return obj
      }
      this.$emit('cube-popup', obj)

      function createDataItem(row, metricIndex, colorIndex) {
        let _resData = {
          currentRow: row,
        }
        if (typeof metricIndex === 'number') {
          _resData.metricValue = row[`${ STR }${ metricAllList[metricIndex].alias }`] || row[metricAllList[metricIndex].alias]
          _resData.metricKeyName = metricAllList[metricIndex]?.keyName
          _resData.metric = metricAllList[metricIndex].lang_alias
        } else if (metricIndex) {
          _resData.metricValue = row[`${ STR }${ metricIndex }`] || row[metricIndex]
          _resData.metric = metricIndex
        }
        _resData.legendName = _resData.metric
        _resData.color = Color.getColorDirection(typeof colorIndex === 'number' ? colorList[colorIndex] : colorIndex)
        return _resData
      }
      function setCustomMetric(customMetricData = [], currentMetricResult, customMetricAlias, metricAlias) {
        customMetricData.push({
          metric: metricAlias,
          metricValue: currentMetricResult.currentRow[`${ STR }${customMetricAlias}`],
          currentRow: currentMetricResult.currentRow,
          metricKeyName: currentMetricResult.metricKeyName,
          labelType: 'customMetric',
          legendName: currentMetricResult.metric,
        })
      }
    },
    onUpDrillByIndex(index) {
      const indexSum = this.drillDimensionsLabelList.length - 1
      const dis = indexSum - index - 1
      if (dis > 0) {
        this.drillSettings.drillDimensions.splice(-dis, dis)
      }
      this.onUpDrill('', false, false)
    },
    // 钻取返回
    onUpDrill(value = '', isMobileDrillClick = false, isClear = false) {
      if (!this.isBoardRequestFinish) return
      if (this.hasGrid) {
        this.$refs.grid && this.$refs.grid.drillBack()
        this.onGridDrill()
        return
      }
      if (this.isBritainMap && (!this.isChildMapScheme || this.drillSettings.drillDimensions?.length)) {
        this.onBritainUpDrill()
        return
      }
      if (isClear) {
        // this.drillSettings.banDrill = true
        let values = this.drillSettings.drillDimensions[this.drillSettings.drillDimensions.length - 1].values
        let arr = this.drillSettings.currentDrillData || []
        values.forEach(item => {
          arr.push(item)
        })
        this.drillSettings.currentDrillData = arr
      }
      const drillDimensions = this.drillSettings.drillDimensions
      const interactionOptions = this.content.interactionOptions
      // const children = interactionOptions && interactionOptions.children
      // 散点图特殊的数据结构，所以维度长度是这个
      const dimensionLength = this.chartUserConfig.dimensionList.length
      // 获取返回后钻取层级
      const lastDrillDimension = drillDimensions.splice(drillDimensions.length - dimensionLength, dimensionLength)
      const len = drillDimensions.length
      let data = {
        ids: [this.element.id],
      }

      // 存在交互设置
      // 钻取完发现没有子层级后，返回上一层，交互保留 isClear为true
      interactionOptions && interactionOptions.forEach(item => {
        // Bug: 90106, 钻取+交互，到最底下一层之后，切换被交互图形的指标选择器，会丢失当前指标选择器
        // 的交互参数，因为目前主交互元素钻不下去了，进行了DrillUp返回到上一级了，所以存一份临时变量
        const oldChildren = this.$_deepClone(item.children)
        const children = item.children
        if (children && children.length > 0) {
          children.pop()
          // 最后一层钻取返回，交互参数为 返回后的上一层钻取维度
          if (children.length === drillDimensions.length + 1) {
            children.pop()
          }
          if (isClear) {
            this.interactionOptionsValues = item.values ? item.values[0] : item.values
            this.$set(this.tempOldChildren, item.id, oldChildren)
          } else {
            this.$delete(this.tempOldChildren, item.id)
          }
          let list = !isClear ? [...interactionOptions[0].associElements.map(
            elSetting => elSetting.id
          )] : []
          data = {
            ids: [
              this.element.id,
              ...list
            ],
            // 带钻取的交互需要刷新主元素
            dimensionDrill: true,
            element: { type: TYPE_INTERACTION.INTERACTION_SETTING }
          }
        }
      })
      let provice = 'china'
      if (len) {
        provice = drillDimensions[0].values[0]
      } else if (this.isChildMapScheme && !lastDrillDimension?.length) {
        provice = 'world'
        this.mapSchemeSwitch(0)
      }
      this.mapBack(provice)
      this.refreshEl(data)
    },
    // 返回看板跳转当前文本和变量内容
    getVariableAndText(chartUserConfig = this.chartUserConfig) {
      const variable = []
      const text = []
      const { dimensionList = [], title = {} } = chartUserConfig
      // 当前隐藏列
      const fun = function (rowData, rol) {
        const itemData = {
          val: rowData.webFieldType ? rowData.keyName : this.getDatasetLabel(rowData, true),
          alias: this.getDatasetLabel(rowData),
          dataSetId: rowData.parentId,
          site: rowData.keyName,
          keyName: rowData.keyName
        }
        variable.push(itemData)
      }
      dimensionList.forEach((rowData, rol) => {
        if (rowData.webFieldType === 'metricGroup') return
        if (!variable.find(el => el.site === rowData?.keyName)) {
          fun.call(this, rowData, rol)
        }
      })

      let titleText = title?.text || ''
      titleText = titleText.trim ? titleText.trim() : ''

      return {
        titleText,
        text,
        variable,
      }
    },
    // 根据钻取维度 获取交互参数
    getInteractionOptionsByDimensions(interactionItem, len, lastInteraction, doubleNotNeed) {
      const drillDimensions = this.drillSettings.drillDimensions
      const children = interactionItem.children || []
      // 获取上一次钻取维度 当钻取层级为1时，交互参数为初始维度; 当钻取全部完成后点击交互，交互参数为最后一次钻取维度
      const { columnName, columnType } = lastInteraction ? drillDimensions[len - 1] : len <= 1 ? interactionItem : drillDimensions[len - 2]
      // 根据钻取维度 获取交互参数
      const param = this.$_deepClone(interactionItem)
      param.children && this.$delete(param, 'children')
      // 清空日期维度拆分交互传参
      param.titleCalendarPeroid && this.$delete(param, 'titleCalendarPeroid')
      !doubleNotNeed && Object.assign(param, { columnName, columnType })
      param.associElements.map(elSetting => {
        !doubleNotNeed && Object.assign(elSetting, { columnName, columnType })
      })
      if (children.length && len) {
        param.needChangeTrendsDimension = ''
      }
      // if (children.length === drillDimensions.length) {
      //   children[children.length - 1] = param
      // } else {
        children.push(param)
      // }
      this.$set(interactionItem, 'children', children)
    },
    // 取消当前交互
    cancelCurrentInteraction(sonId = [], id = '') {
      const ref = this.getGridRef()
      if (ref) {
        ref.cancelCurrentInteraction(sonId = [], id = '')
        return
      }
      const interactionOptions = this.content.interactionOptions || []
      // 图形存在钻取功能且交互其他元素，钻取到最后一层后才可以进行交互
      this.interactionOptionsValues = null
      interactionOptions.forEach(item => {
        this.$delete(item, 'values')
        item.children && (item.children = [])
      })
      this.drillSettings.drillDimensions && this.$set(this.drillSettings, 'drillDimensions', [])
      // 取消掉对下级交互的影响
      const ids = this.elList.reduce((pre, next) => {
        if (next.content.interactionState && next.content.interactionState[this.element.id]) {
          this.$set(next.content.interactionState, this.element.id, [])
          sonId.includes(next.id) || pre.push(next.id)
        }
        return pre
      }, [])
      id && ids.push(id)
      if (ids.length) {
        const eventData = new EventData({
          type: 'refreshElByForce',
          target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
          targetFn: 'refreshElByForce',
          data: { ids },
        })
        this.$emit('eventBus', eventData)
      }
    },
    // ----------------分割线---------------------------------------
    eventBus,
    refreshEl(data = {}) {
      if (this.configs && this.configs.type === 'template' && !this.isChartSet) {
        this.$emit('refreshEl', { element: this.element, preventTemplateRequest: data.preventTemplateRequest })
      } else {
        if (this.chartUserConfig.chartAlias === 've-grid-normal') return
        const target = this.isMobile ? (this.utils.isPcMobile ? EVENT_DATA_PARAMS.displayPanelPcToMobile : EVENT_DATA_PARAMS.displayPanelMobile) : EVENT_DATA_PARAMS.displayPanel
        const eventData = new EventData({
          ...this.defaultEventData,
          target,
          targetFn: 'refreshElByForce',
          data: data
        })
        this.$emit('eventBus', eventData)
      }
    },
    // ----------------分割线---------------------------------------
    // 指标增长率
    getCompareValue(name) {
      const value = this.chartData.legendData[name] || 0
      let result = typeof value === 'number' ? value.toFixed(2) : value
      result = result === '-' ? 0 : result
      let sign
      if (result > 0) {
        sign = '↑'
      } else if (result < 0) {
        sign = '↓'
      } else {
        sign = '-'
      }
      return `  ${sign} ${result}`
    },
    getEchartsInstance() {
      return this.echartInstance
    },
    resizeEcharts(params = {}, trigger) {
      this.isChart && this.$nextTick(() => {
        if (this.chartSettings.metrics || this.chartSettings.dimension) {
          if (this.hasGrid) {
            this.getCanvasSize({ from: 'resizeEcharts', retainScroll: params.retainScroll })
          } else {
            const instance = this.getEchartsInstance()
            try {
              if (trigger === 'mobile-legend-init-complete' && this.othersPieDataIndex > -1) {
                this.refreshChart()
              } else {
                instance && instance.resize && instance.resize()
              }
            } catch (e) {
              console.warn(e)
            }
          }
        }
      })
    },
    handleChoiceTab(index, type = 'outerClick') {
      this.checkedDimension = index

      this.chooseDimension(this.checkedDimension, type)
    },
    // MARK:外面切换指标选择器
    chooseDimension(checkedDimension, trigger, forceChange) {
      this.visible = false
      if ((!this.isBoardRequestFinish && !forceChange) && trigger !== 'metricSwitch' && trigger !== 'init' && trigger !== 'cardInteractionIndicatorSwitch') {
        return
      }
      // 恢复图表配置
      // const _chartUserConfig = this.element.content.chioceTab[checkedDimension].saveObj.chartUserConfig
      // 兼容兼容没有主题配色的图表
      // if (!_chartUserConfig.themeConfig || this.$_isEmptyObject(_chartUserConfig.themeConfig) || this.$_isEmptyObject(_chartUserConfig.themeConfig[THEME_TYPE.default])) {
      //   setThemeConfig(_chartUserConfig, THEME_TYPE.default)
      // }
      this.$_filterDeepClone(this.element.content, this.element.content.chioceTab[checkedDimension].saveObj,
        ['chioceTab', 'contentStyle', `themeConfig`, 'saveIndex', 'interactionOptions', 'indicatorSelectorShowType', 'indicatorSelectorShow', 'superLinkOptions', 'metricChioceTab'])
      this.element.content.mapSchemeSetting = this.element.content.chioceTab[checkedDimension].saveObj.mapSchemeSetting ? this.$_deepClone(this.element.content.chioceTab[checkedDimension].saveObj.mapSchemeSetting) : undefined

      // 切换指标选择器，在看板编辑中时，重置pageSize
      const content = this.element.content
      if (!content.chartUserConfig.pagination.rankOf) {
        content.drillSettings.pageInfo.pageSize = (this.isMobilePageImg || this.isChartSet) ? 100 : 9999
      } else {
        content.drillSettings.pageInfo.pageSize = content.chartUserConfig.pagination.pageSize
      }

      // 看板设计界面切换指标选择器，重置日期维度选中值
      this.clearDateDimensionVal()

      // 1.当具备指标选择器的图形被设置为交互对象时，切换时不重置状态，按照被影响的条件更新数据，如有设置下钻，下钻功能也将失效；
      // 2.当具备指标选择器的图形没有被设置为交互对象时，切换时重置状态，下钻功能与交互可以正确使用；
      let holdInteractionStatus = false
      this.elList.forEach(item => {
        const interactionOptions = item.content.interactionOptions
        const flag = Array.isArray(interactionOptions) && interactionOptions[0] && interactionOptions[0].associElements.some(eve => eve.id === this.element.id)
        if (!flag || !Array.isArray(interactionOptions[0].values) || !interactionOptions[0].values[0]) return
        holdInteractionStatus = true
        this.refreshEl({
          ids: [item.id, this.element.id],
          element: { type: TYPE_INTERACTION.INTERACTION_SETTING },
          trigger
        })
        // this.$set(interactionOptions, 'values', [])
      })
      let { interactionOptions, chioceTab, saveIndex } = this.element.content
      if (chioceTab?.length && chioceTab[saveIndex] && interactionOptions?.length) {
        interactionOptions = interactionOptions.filter(item => item.chioceTabId !== chioceTab[saveIndex]['id'])
      }
      let _idsArr = []
      if (Array.isArray(interactionOptions) && interactionOptions.some(item => Array.isArray(item.values) && item.values[0])) {
        interactionOptions.forEach(item => {
          item.values && Reflect.deleteProperty(item, 'values')
          item.children && (item.children = [])
          _idsArr.push(item.associElements.map(el => el.id))
        })

        // 清除对应被交互元素的状态
        _idsArr.forEach(arr => {
          if (arr.length) {
            arr.forEach(id => {
              const el = this.elList.find(e => e.id === id)
              if (el && el.content.interactionState && el.content.interactionState[this.element.id]) {
                delete el.content.interactionState[this.element.id]
                this.refreshEl({ ids: [id] })
              }
            })
          }
        })
      }
      if (!this.isChartSet) {
        const ids = [this.element.id, ..._idsArr]
        if (holdInteractionStatus) {
          holdInteractionStatus = false
          ids.pop()
        }
        if (this.hasGrid && trigger === 'outerClick') {
          this.createTable({ firstRender: true, from: 'chooseDimension' })
          ids.shift()
        }

        // 切换指标选择器，外面组件处理的方法
        this.clearSomeSettings(trigger)

        trigger !== 'metricSwitch' && this.refreshEl({ ids, preventTemplateRequest: trigger !== 'outerClick' })

        this.sdpBus.$emit(EVENT_BUS.CHART_CHOOSE_HANDLER_CHANGE, { chartElement: this.element })
      }
      // 用于恢复卡片指标交互时，保存看板，图形的指标没恢复的问题 70593
      if (trigger !== 'cardInteractionIndicatorSwitch') {
        this.tempSaveIndex = checkedDimension
      }
    },
    clearSomeSettings(trigger) {
      if (trigger !== undefined) {
        this.legendSelected = {}
        // 清除工具栏请求参数
        clearToolParams(this.element, this.elList, this)
      }
    },
    async getPrintData(option = {}) {
      const { img = true, data = false } = option
      let dataURL
      try {
        if (img) {
        const themeBg = {
          [THEME_TYPE.classicWhite]: 'rgba(230, 231, 236, 1)',
          [THEME_TYPE.darkBlue]: 'rgba(44, 62, 80, 1)',
          [THEME_TYPE.deepBlue]: 'rgba(44, 62, 80, 1)',
        }
        const dom = this.getBoardUnderIdElement('#').querySelector(`.${this.kanbanId}.exportDOM${this.element.id}`)
        const imgBase64 = await html2canvas(dom, { onclone: cloneHandler, backgroundColor: themeBg[this.themeType] })
        dataURL = imgBase64 ? imgBase64.toDataURL('image/png') : ''
        // dataURL = this.echartInstance.getDataURL({ backgroundColor: chartBackground })
        } else {
          dataURL = ''
        }
      } catch (err) {
        console.log(err)
        dataURL = ''
      }
      // const dataURL = this.echartInstance.getDataURL()
      // const { rows } = this.chartData
      // const columns = [...this.chartSettings.dimension, ...this.chartSettings.metrics]

      // 导出数据的格式和hover格式一致
      const chartData = this.$_deepClone(this.chartData)
      if (Array.isArray(chartData.rows)) {
        chartData.rows.forEach(item => {
          Object.keys(item).forEach(eve => {
            item[eve] = item[`VIEWFORMAT_${eve}`] || item[eve]
          })
        })
      } else {
        Object.keys(chartData.rows).forEach(item => {
          chartData.rows[item] = chartData.rows[`VIEWFORMAT_${item}`] || chartData.rows[item]
        })
      }

      const val = {
        dataURL,
        chartData,
      }

      if (data) {
        val._chartResponseTmp = this._chartResponseTmp

        const measureConfig = this.chartUserConfig.measureConfig || {}
        if (measureConfig.hasOwnProperty('hide') && !measureConfig.hide) {
          const flag = !!(val._chartResponseTmp?.chartElements?.[0]?.chartResponse?.measureSummary)
          if (flag) {
            delete val._chartResponseTmp.chartElements[0].chartResponse.measureSummary
          }
        }
      }

      return val
    },
    resetLegendSelected(data, resetAllData = false) {
      if (resetAllData) {
        const trueValueSelected = Object.fromEntries(Object.entries(this.legendSelected).map(([key, value]) => [key, true]))
        this.legendSelected = Object.assign({}, trueValueSelected, data)
      } else {
        this.legendSelected = Object.assign({}, data)
      }
    },
    // 重置y轴最大值、最小值
    resetAxisRange(legendSelected = {}, isForce) {
      const { metricAllList, dimensionList } = this.UserConfig
      // 如果没有维度、度量
      if (this.hasGrid || (!dimensionList.length && (!metricAllList.length))) return
      const chartAlias = this.chartUserConfig.chartAlias
      if (!isForce && Object.keys(legendSelected).length === 0 && Object.keys(this.legendSelected).length === 0) return
      let _legendSelected = legendSelected
      if (LEGEND_SETTING_SAVE_CHART.includes(chartAlias) &&
        !Object.keys(legendSelected).length &&
        this.chartUserConfig.saveLegendSelected) {
        _legendSelected = this.chartUserConfig.saveLegendSelected
      }
      this.legendSelected = { ..._legendSelected }
      if (CIRCULAR_CHART.includes(chartAlias)) {
        this.refreshChart({ source: 'legend select', legendScrollDataIndex: this.legendScrollDataIndex })
      } else if ([...HANDLE_MAX_MIN_VALUE_CHART, 've-scatter-normal'].includes(chartAlias)) {
        this.setLangLegendSelect({ ..._legendSelected })
        this.refreshChart({ source: 'reset axis range', legendScrollDataIndex: this.legendScrollDataIndex })
      }
    },
    keepAxisRang() {
      this.resetAxisRange(this.legendSelected, true)
    },
    // todo kyz
    // 初始化度量汇总
    resetAfterConfig(param = { isMapInit: false, UserConfigSetted: false }) {
      const { chartAlias: _alias, pictorialBarSettings = {}, animationSetting = {} } = this.chartUserConfig
      const { flipAxis = false, symbolRepeat = false } = pictorialBarSettings
      if (_alias === 've-grid-normal') {
        this.getCanvasSize({ from: 'resetAfterConfig', retainScroll: param.retainScroll, })
        this.$nextTick(() => {
          erd.listenTo(this.$el, (element) => {
            this.elCanvasSize.canvasOffsetWidth = element.offsetWidth
            this.elCanvasSize.canvasOffsetHeight = element.offsetHeight
          })
        })
        return
      }
      this.$refs.decomposition && this.$refs.decomposition.decompositionEmit('refresh')
      this.isGridDrillArrowShow = false
      // if (!this.legendSelectedChanged) this.resetAxisRange({})

      param.isMapInit && this.doRegisterMap({ name: this.chartSettings.position })
      this.refreshChart({ ...param, source: param.source || param.from || 'resetAfterConfig' })
      this.$forceUpdate()
      // 89365 【图表】图形开过度量值后再关闭，鼠标hover到柱子上时仍然会显示度量值
      if ([USER_PROP.BAR_RADIUS_DIRECTION, USER_PROP.ANIMATION_SETTING].includes(param.source) || (param.source === USER_PROP.LABEL_SHOW)) {
        this.redrawEchartsDom(5)
      } else if ([USER_PROP.WORD_CLOUD_SHAPE, USER_PROP.WORD_CLOUD_ARRANGEMENT_MODE, USER_PROP.WATER_DROP_COLOR, USER_PROP.TREE_SETTING].includes(param.source)) {
        // 修改配置，渲染就有问题场景：词云图的横竖混排；水滴图修改波纹颜色配置；树图修改配置
        this.redrawEchartsDom()
      }
      // 新建象形柱图同时开启反转坐标轴+图片复用时，浏览器崩溃
      // if (!this.isChartSet && !this.commonData.isPreview && _alias === 've-pictorialbar' && flipAxis && symbolRepeat) {
      //   this.redrawEchartsDom()
      // }
      this.initMobileLegend()
      // 柱状图和组合图之间切换存在不能准确点击图例的bug，所以需要此代码
      try {
        this.$nextTick(() => {
          // 地图更新后会恢复默认比例 不需要再重置地图比例
          !param.isMapInit && this.resizeEcharts()

          this.timer && clearTimeout(this.timer)
          const { mapSetting = {} } = this.chartUserConfig
          if (_alias === 've-map-parent' && this.utils.isScreen && this.commonData.isPreview && mapSetting.showDataChange) {
            this.timer = setTimeout(() => {
              const options = this.echartInstance.getOption()
              options.series = options.series.filter(s => !s.isCustomSeries)
              this.echartInstance.setOption(options, true)
            }, mapSetting.dataChangeStyle.duration * 1000)
          }
        })
      } catch (e) {
        console.log(e)
      }
    },
    clearMark() {
      !this.chartUserConfig.quadrant && this.$delete(this.chartConfig, 'markLine')
    },
    _getSettingProp() {
      const { alias, chartSettings } = this.content
      const userConfig = this.chartUserConfig
      const setting = Object.create(null)
      const circularConfig = { }
      const specConfig = {}
      const circularAlias = ['ve-pie-normal', 've-pie-rose', 've-ring-normal', 've-radar', 've-ring-multiple', 've-roundCascades']
      if (alias === 've-radar') {
        specConfig.lineStyle = {
          normal: {
            type: 'dash'
          }
        }
        specConfig.areaStyle = {
          normal: {
            color: 'rgba(255, 255, 255, 0.5)'
          }
        }
      }
      if (alias === 've-line-area') {
        specConfig.area = true
      }
      const mapUrl = ['ve-map-china', 've-map-world'].includes(this.chartUserConfig.childChartAlias) ? '' : 'country/'
      Object.assign(
        setting,
        circularConfig,
        this.chartSettings,
        { dimension: chartSettings?.dimension?.map(d => `VIEWFORMAT_${ d }`) },
        specConfig,
        { mapURLProfix: STATIC_BASE_PATH.map + mapUrl }
      )
      return setting
    },
    _mergeConfig(chartConfig) {
      // 图例配置合并
      const legend = Object.assign({}, this.defaultChartConfig.legend, chartConfig.legend)
      const grid = Object.assign({}, this.defaultChartConfig.grid, chartConfig.grid || {})
      const toolbox = Object.assign({}, this.defaultChartConfig.toolbox, chartConfig.toolbox || {})
      const showToolbox = this.chartUserConfig.showColorBand || false
      this.$set(toolbox, 'show', showToolbox)
      return { legend, grid, toolbox }
    },
    updateRenderChart(value) {
      this.renderChart = value
      // 重新渲染图形时去除滚动条位置
      this.scrollBarPosition = ''
    },
    // 移动看板loading动画开启，可能会截断鼠标的mouseup事件，导致datazoom的拖拽状态一直持续
    dispatchMouseupEvent() {
      const chartDom = document.getElementsByClassName(this.chartClassName)[0]
      if (!chartDom) return
      const mouseEvent = document.createEvent('MouseEvents')
      mouseEvent.initEvent('mouseup', true, true)
      chartDom.dispatchEvent(mouseEvent)
      setTimeout(() => {
        this.triggerMousemove = false
      }, 200)
    },
    slideLeft(flag = false) {
      const { chartAlias, calendarSettings = {} } = this.chartUserConfig
      if (chartAlias === 've-calendar' && calendarSettings.pagingMode === 'slide') {
        this.changeCalendarRange('next')
        return
      }
      // 图形编辑设置了条数，不允许分页
      if ((!flag && isReverseAxisChart({ chartUserConfig: this.chartUserConfig })) || this.chartUserConfig.pagination.rankOf) return
      // 左滑请求下一页
      const responsePageInfo = this.chartConfig.responsePageInfo
      const pageInfo = this.drillSettings.pageInfo
      if (!this.isMobilePageImg || pageInfo.page === responsePageInfo.totalPage) return
      const options = this.echartInstance.getOption()
      if (options.dataZoom[0].end !== 100) return
      pageInfo.page++
      this.slideRequest()
    },
    slideRight(flag = false) {
       const { chartAlias, calendarSettings = {} } = this.chartUserConfig
      if (chartAlias === 've-calendar' && calendarSettings.pagingMode === 'slide') {
        this.changeCalendarRange('pre')
        return
      }
      // 图形编辑设置了条数，不允许分页
      if ((!flag && isReverseAxisChart({ chartUserConfig: this.chartUserConfig })) || this.chartUserConfig.pagination.rankOf) return
      // 右滑请求上一页
      const pageInfo = this.drillSettings.pageInfo
      if (!this.isMobilePageImg || pageInfo.page === 1) return
      const options = this.echartInstance.getOption()
      if (options.dataZoom[0].start !== 0) return
      pageInfo.page--
      this.slideRequest()
    },
    slideTop() {
      // 特殊处理条形图手势
      this.slideRight(isReverseAxisChart({ chartUserConfig: this.chartUserConfig }))
    },
    slideDown() {
      // 特殊处理条形图手势
      this.slideLeft(isReverseAxisChart({ chartUserConfig: this.chartUserConfig }))
    },
    slideRequest() {
      this.isChartSet ? this.$emit('preview-chart') : this.refreshEl({
        ids: [this.element.id],
      })
    },
    getCurrentParamsPanel(key) {
      const boardData = this.getBoardData()
      console.log('%c [ boardData ]-5429', 'font-size:13px; background:#16004b; color:#5a448f;', boardData)
      let paramsPanelList = boardData?.paramsPanelList || this.baordData?.paramsPanelList || []
      if (this.element[SBI_ATTR]) {
        paramsPanelList = Array.isArray(this.element[SBI_ATTR].paramsPanel) ? this.element[SBI_ATTR].paramsPanel : [this.element[SBI_ATTR].paramsPanel]
      }
      const paramsPanel = paramsPanelList.find(tab => tab.active)
      return key ? paramsPanel?.[key] : paramsPanel
    },
    // 判断是否为时间维度拆分图形
    isTimeSplitingChart(element) {
      // 时间维度拆分场景：1.日期维度切换; 2.时间维度拆分开关 + 日历组件配合使用; 3.日期维度选择按天外选项; 4.日期维度偏移
      const { drillDimensions = [], timeDimensionSplitting } = element.content.drillSettings
      const { dimensionList = [], switchDateDimension } = this.chartUserConfig
      let dateDimensionList = dimensionList.filter(d => d.columnTpe === 'date')

      if (dateDimensionList.length === 1) {
        // 场景1：日期维度切换
        if (switchDateDimension) return true

        // 场景3：日期维度选择按天外选项
        if (dateDimensionList[0]?.timeDimensionSplittingRule && !['_vs_none', DATE_DIMENSION_TYPE.Day_vs_day, DATE_DIMENSION_TYPE.Day_vs_day_d].includes(dateDimensionList[0].timeDimensionSplittingRule)) return true

        // 场景4: 日期维度偏移
        if (dateDimensionList[0]?.timeDimensionSplittingOffset && dateDimensionList[0].timeDimensionSplittingOffset !== 'NO_OFFSET') return true
      }

      // 获取日历组件数据
      const paramsPanel = this.getCurrentParamsPanel()
      let calendarBindingElement = []
      if (paramsPanel?.content) {
        paramsPanel.content.forEach(param => {
          if (param.type === 'bussinessCalendar' || param.type === 'financialCalendar') {
            const bindElement = param?.content?.bindElement || []
            calendarBindingElement.push(...bindElement)
          }
        })
      }
      // 场景2：时间维度拆分开关 + 日历组件配合使用
      const scenario1 = timeDimensionSplitting === '1' && calendarBindingElement.includes(element.id)
      if (scenario1) return true

      return false
    },
    // 需要传特定参数：1. 时间维度拆分，存在交互、钻取；2. 指标维度预警里面的维度是日期类型
    timeDimensionSplitParamRule(element) {
      if (this.isChartSet) return
      const { dimensionWarningList = [] } = this.chartUserConfig
      const needTitleCalendarCacheMap = this.isTimeSplitingChart(element)
      // 指标维度预警里面的维度是日期类型
      const scenario3 = dimensionWarningList.some(d => d.warningList.find(w => w.fieldColumnType === 'date'))

      // 交互主元素、钻取元素需要needTitleCalendarCacheMap(编辑下交互和跳转不会触发图形的请求获取不到，编辑下不判断。预览下不会编辑交互和跳转，用来判断请求是否需要)
      const isNeedTitleCalendarCacheMap = needTitleCalendarCacheMap && (!this.commonData.isPreview || element.content?.drillList?.length || this.interactionSetting.clickAble || this.superLinkDimensionClick)
      if (scenario3 || isNeedTitleCalendarCacheMap) {
        this.$set(this.element.content, 'needTitleCalendarCacheMap', true)
        element.needTitleCalendarCacheMap = true
      } else {
        this.$delete(this.element.content, 'needTitleCalendarCacheMap')
      }

    },
    clearMapAnimation(params = {}) {
      this.animationInstance?.clear && this.animationInstance.clear()
    },
    // ----------------分割线---------------------------------------
    requestAdapter({ type }) {
      console.log('requestAdapter');
      // 清除原有的动画效果
      this.clearMapAnimation({ from: 'request data' })
      this.histogramAnimationInstance?.clear && this.histogramAnimationInstance.clear()
      if (this.metricChioceTab.showNone && this.metricChioceTab.isRelated) {
        this.showNone = true
        this.setHasChartData(true)
        return false
      } else {
        this.showNone = false
      }
      // 没有维度度量，不进行数据请求
      const { dimension, metrics } = this.chartSettings
      if (!dimension.length && !metrics.length) {
        this.setHasChartData(true)
        return false
      }

      // run时清空钻取状态,交互状态
      let drillDimensions = this.drillSettings.drillDimensions || []
      // 自定义维度钻取场景，需要增加exp字段
      const { dimensionList = [] } = this.chartUserConfig
      drillDimensions.forEach(drill => {
        const dimension = dimensionList.find(item => item.keyName === drill.drillDimensionKey)
        if (dimension && dimension.customDimension?.expression) {
          drill.exp = dimension.customDimension?.expression
          drill.customerExprDim = true
        } else {
          delete drill.exp
          delete drill.customerExprDim
        }
      })
      let interactionOptions = this.content.interactionOptions
      // let children = (interactionOptions && interactionOptions.children) || []
      // if (this.elName === 've-map') {
      //   // 地图交互需要重置
      //   this.setUpdateChart()
      // }
      if (type === 'paramEl') {
        if (drillDimensions.length) {
          this.$set(this.drillSettings, 'drillDimensions', [])
          if (this.isBritainMap) {
            this.chartSettings.position = 'britain'
          }
        }
        // children.length && this.$set(this.content.interactionOptions, 'children', [])
        Array.isArray(interactionOptions) && interactionOptions.forEach(item => {
          item.values && Reflect.deleteProperty(item, 'values')
          item.children && (item.children = [])
        })
        this.doRegisterMap()
        // this.mapOptions.position = 'china'
        // 重置日期维度选中值
        // this.clearDateDimensionVal()
        // 图形渲染完成初始化图例
        this.initMobileLegend()
        this.isGridDrillArrowShow = false
      }
      if (isSetMeasureConfig.call(this, this.content)) {
        const { measureSummaryValue, ignoreDimension = false, summaryList = [] } = this.chartUserConfig.measureConfig || {}
        if (metrics.length === 1) {
          const rule = [{
            rule: measureSummaryValue || 'Sum',
            noDim: ignoreDimension,
          }]
          this.$set(this.drillSettings, 'measureSummary', rule)
        } else if (summaryList.length && MULTI_GRAPHARR.includes(this.content.chartUserConfig.chartAlias)) {
          const metricAll = this.content.chartUserConfig.metricsContainer.default || []
          const rules = summaryList.map(item => {
            const m = metricAll.find(me => me.keyName === item.keyName)
            return {
              rule: item.summaryValue,
              noDim: item.ignoreDimension,
              metricsAlias: item.summaryValue === 'Recently' ? this.chartSettings.dimension : [m.alias || m.labeName]
            }
          })
          this.$set(this.drillSettings, 'measureSummary', rules)
        } else {
          delete this.drillSettings.measureSummary
        }
      } else {
        delete this.drillSettings.measureSummary
      }
      if (this.chartUserConfig.chartAlias !== 've-grid-normal') {
        const { request, requestKeyFn } = bridge.adapter
        const element = this.$_deepClone(this.element)
        if (this.showDateComponent({ dateParams: true })) {
          const layers = this.$_getProp(element, 'content.drillSettings.layers', [])
          const dateDimension = layers.length && layers[0].dimension.find(item => item.columnTpe === 'date' || item.columnType === 'date')
          dateDimension && Object.assign(dateDimension, {
            timeDimensionSplittingRule: [DATE_DIMENSION_TYPE.Day_vs_day, DATE_DIMENSION_TYPE.Day_vs_day_d].includes(this.selectedDateVal) ? `customize_${this.selectedDateVal}` : this.selectedDateVal
          })
        }
        // 英国地图需要过滤钻取数据，英国地图的钻取因为数据问题暂时由前端实现
        if (this.isBritainMap) {
          this.$delete(element.content.drillSettings, 'drillDimensions')
        }
        // 时间维度拆分，存在交互、钻取时需要传特定参数
        this.timeDimensionSplitParamRule(element)

        const requestParams = request.call(this, element, { isChartSet: this.isChartSet })
        const requestKey = requestKeyFn()
        this.setElementLoadState(true)
        this.requestData = requestParams.layers[0]

        if (
          this.isIgnoringUnselectedMetrics
          && this.content.chartResponse
          && this.isLegendsHiddenCall
        ) {
          this.isLegendsHiddenCall = false
          requestParams.legendsHidden = this.getLegendsHidden()
        } else {
          // 下钻后处理下钻数据
          if (this.hasDrillData) {
            this.tempDrillLegendsSelected = null
          } else {
            this.tempLegendsSelected = null
          }
        }

        return {
          requestKey,
          requestParams
        }
      } else {
        const { filterData, sorterData } = this.element
        const params = this.mixin_table_requestAdapter({ type, elList: this.elList }, this.chartUserConfig.chartAlias)

        return {
          ...params,
          requestParams: Object.assign(
            {},
            params.requestParams,
            filterData ? { filterData } : {},
            sorterData ? { sorterData } : {},
          ),
        }
      }
    },
    updateElementCustomField() {
      if (this.chartUserConfig.chartAlias === 've-grid-normal') {
        this.originalTable = this.tableConstructor.tableCreator()
      }
    },
    // 替换交互设置dimension
    replaceInteractionDimension(realDimension) {
      const interactionOptions = this.$_getProp(this.element, 'content.interactionOptions', null)
      if (interactionOptions) {
        interactionOptions.forEach(item => {
          realDimension ? this.$set(item, 'realDimension', realDimension) : this.$delete(item, 'realDimension')
        })
      }
    },
    initMobileLegend() {
      if (!this.isMobile) return
      // Object.keys(this.echartInstance).length && this.echartInstance.setOption({
      //   legend: {
      //     selected: {}
      //   }
      // })
      this.$nextTick(() => {
        const legendDom = this.$refs['addMobileLegend']
        legendDom && legendDom.initDataList()
      })
    },
    changeChartType() {
      if (!this.echartInstance || !Object.keys(this.echartInstance).length) return
      // 切换图形需要清空echarts组件
      this.echartInstance.clear()
    },
    // 获取引用的自定义字段
    getCustomFieldsUsedInElement(webFieldTypes = ['metricGroup']) {
      if (webFieldTypes[0] === 'ALL') webFieldTypes = ['metricGroup', 'customComputed']
      const result = []
      Object.values(this.UserConfig.InstanceField._allKeys).forEach(k => {
        this.UserConfig[k].forEach(fieldItem => {
          if (webFieldTypes.includes(fieldItem.webFieldType) && !result.find(r => r.id === fieldItem.id)) {
            result.push(fieldItem)
          }
        })
      })
      return Array.from(result)
    },
    updateElementPaddingRender() {
      this.refreshChart()
    },
    // 重新渲染图形
    refreshChart(params = { source: 'init', UserConfigSetted: false }) {
      if (!this.echartInstance || !Object.keys(this.echartInstance).length) return
      if (this.chartUserConfig.chartAlias === 've-map-parent' && !echarts.getMap(this.mapOptions.position)) return
      if (!params.UserConfigSetted) this.UserConfig.init(this.element)

      this.disabledBgTrendFlag = false
      this.isWarning = false
      const instanceParams = {
        element: this.element,
        injectData: {
          themeType: this.themeType,
          isChartSet: this.isChartSet,
          isMobile: this.isMobile,
          configs: this.configs,
          legendScrollDataIndex: params.legendScrollDataIndex || 0,
        },
        echartsInstance: this.echartInstance,
        vm: this,
      }
      const newChart = CHART_LIST[this.chartUserConfig.chartAlias] ? new CHART_LIST[this.chartUserConfig.chartAlias](instanceParams) : new CHART_LIST.default(instanceParams)
      this.chartInstance = newChart
      this.chartInstance.refreshChart({ ...params, firstRender: this.firstRender })
      this.updateRenderChart(true)
      this.legendScrollDataIndex = params.legendScrollDataIndex || 0
      this.$nextTick(() => {
        this.animationPlay({ type: 'reset', from: params.source })
      })
      setTimeout(() => {
        this.firstRender = false
      }, 100)
    },
    responseAdapter(res, params = {}, originRes = {}) {
      if (params.source === 'websocketMessage') {
        console.log('kyz res', res, res.id)
      }
      const chioceDimension = this.chioceTabList?.[this.checkedDimension]
      this._chartResponseTmp = this.$_deepClone({
        ...originRes,
        exportChartTitle: `${this.chartUserConfig.title.text || this.element.elName}${chioceDimension ? `-${chioceDimension.name || chioceDimension.id}` : ''}`
      })

      let resData = null
      // this.inst
      if (this.hasGrid) {
        if (res.id === this.element.id) {
          this.commonData.isPreview && this.updateChartSettings(true)
          this.mixin_table_responseAdapter(res, this.chartUserConfig.chartAlias)
        }
      } else if (res.id === this.element.id) {
        const { dimensionNullValue, dimension, dimensionExtend = [] } = res.chartResponse
        // 存在：多次请求，最后的res不是最新的请求结果，这个判断不是很严谨，之后再补--todo
        if (this.chartUserConfig.chartAlias !== 've-map-parent' && (dimensionExtend?.length || 0) !== (this.requestData?.extendDimension?.length || 0)) return
        // 处理后的响应数据
        resData = bridge.adapter.response.call(this, res, this.element, this.elList, params)
        bridge.adapter.renderFns(this.element, resData)
        this.setStaticChartData(resData.chartData)
        this.getMapRang()
        this.replaceInteractionDimension(this.$_getProp(res, 'chartResponse.realDimension', false))
        // 设置预置维度，location设置为报表层级树,当钻取返回数据为空时，禁止下一次钻取
        const drillDimensionsLen = Array.isArray(this.drillSettings.drillDimensions) && this.drillSettings.drillDimensions.length
        // if (this.presetDimensionLabel && this.locationSelectType === 'dimension' && drillDimensionsLen) {
        if (drillDimensionsLen && this.presetDimensionLabel) {
          const rows = resData.chartData.rows
          let isDimensionNUll = true
          Array.isArray(rows) && rows.forEach(item => {
            // 检测维度为空值
            let empty = dimension.every(key => {
              if (item.extendData && item.extendData.length) {
                return item.extendData.every(extendData => {
                  return extendData[key] === dimensionNullValue
                })
              } else {
                return item[key] === dimensionNullValue
              }
            })
            !empty && (isDimensionNUll = false)
          })
          if (!rows || !rows.length || isDimensionNUll) {
            this.disableDrill = true
          } else {
            this.disableDrill = false
          }
        } else {
          this.disableDrill = false
        }
        // 英国地图钻取需要过滤数据
        this.britainMapFilterData()
        this.handleLegendSelected()
        if (this.tempLegendsSelected || this.tempDrillLegendsSelected) {
          // 下钻后处理下钻数据
          if (this.hasDrillData) {
            this.resetAxisRange(this.tempDrillLegendsSelected)
            this.tempDrillLegendsSelected = null
          } else {
            this.resetAxisRange(this.tempLegendsSelected)
            this.tempLegendsSelected = null
          }
        } else {
          // 下钻时，清空所有置灰图例
          if (this.hasDrillData && this.isIgnoringUnselectedMetrics) {
            this.resetAxisRange({})
          }
        }
        this.firstRender = true
        this.UserConfig.init(this.element)
        this.$emit('init-colors-config')
        // 请求结束后渲染图形
        this.resetAfterConfig({ source: 'request', isMapInit: true, UserConfigSetted: true, })
        // 重新请求数据后图例显示默认状态
        this.initMobileLegend()
        // this.resetAxisRange(this.legendSelected)
        this.histogramAnimation({ source: 'response', chartData: resData.chartData })
        // 日历图需要更新当前页最小的日期数据
        this.handleCalendarDate()

        this.checkDecompositionWarning()
      }
      // if (RESET_UPDATE_CHART.includes(this.chartUserConfig.chartAlias)) {
      //   this.setUpdateChart()
      // } else {
      //   this.setElementLoadState(false)
      // }
      this.setElementLoadState(false)
      this.setHasChartData(true,)

      if (
        !res?.chartResponse?.legendsDisabled
        && this?.chartUserConfig?.showSaveLegendSelected
        && this.isIgnoringUnselectedMetrics
      ) {
        this.setLoading()
        setTimeout(() => {
          this.getLegendsHiddenData()
        })
      }

      return resData
    },
    animationPlay(params = {}) {
      const {
        type = 'init',
        chartData = {},
        from = '',
        source = ''
      } = params
      // tv屏蔽动画效果
      if (this.isDestroyed || (this.utils.isTvScreen && !this.isPlayAnimation)) return
      this.$nextTick(() => {
        this.clearMapAnimation({ from: 'before start animation' })
        this.animationInstance = {}
        const { animationSetting = {}, chartAlias } = this.chartUserConfig
        if (ANIMATION_CHART.includes(this.element.content.alias) && animationSetting.enable && this.echartInstance && Object.keys(this.echartInstance).length) {
          if (this.element.content.alias === 've-map-parent' && animationSetting.animationType !== 'animation-ripple') {
            this.animationInstance = new animationClass.MapAnimation(this.element, this)
          } else if (this.element.content.alias !== 've-map-parent') {
            this.animationInstance = new animationClass.PieAnimation(this.element, this)
          }
          if (this.animationInstance.start) {
            this.animationInstance.start()
          }
        }
        if (animationSetting.enable && params.from !== 'HistogramAnimation.animation' && [...ANIMATION_HISTOGRAM_CHART, ...ANIMATION_TOOLTIP_CHART].includes(chartAlias) && this.histogramAnimationInstance?.showTip) {
          this.histogramAnimationInstance.showTip()
        }
      })
    },
    clearAnimation() {
      this.isDestroyed = true
      this.animationInstance.stop && this.animationInstance.stop()
      this.animationInstance = {}
      this?.histogramAnimationInstance?.clear && this.histogramAnimationInstance.clear()
    },
    clearHistogramAnimationTooltip() {
      this.histogramAnimationInstance?.hideTip && this.histogramAnimationInstance.hideTip()
    },
    histogramAnimation({ source = '', chartData = {}, delay = 0 }) {
      // tv屏蔽动画效果
      if (this.utils.isTvScreen && !this.isPlayAnimation) return
      if (source === 'changeConfig' && [...ANIMATION_HISTOGRAM_CHART, ...ANIMATION_TOOLTIP_CHART].includes(this.element.content.alias)) {
        this?.histogramAnimationInstance?.changeAnimateConfig && this.histogramAnimationInstance.changeAnimateConfig({ ...this.chartUserConfig.animationSetting })
      }
      if (source === 'response' && [...ANIMATION_HISTOGRAM_CHART, ...ANIMATION_TOOLTIP_CHART].includes(this.element.content.alias)) {
        // if (!this.chartUserConfig?.animationSetting?.enable) return
        this?.histogramAnimationInstance?.clear && this.histogramAnimationInstance.clear()
        this.histogramAnimationInstance = new animationClass.HistogramAnimation({
          element: this.element,
          propChartData: chartData,
          API: this.utils.api,
          isPositive: ANIMATION_POSITIVE_CHART.includes(this.element.content.alias),
          updateChartFn: this.refreshChart,
          vm: this,
        })
        setTimeout(() => {
          this.histogramAnimationInstance.start(this.isMouseEnter)
        }, delay)
      }
    },
    // 保存用户选择的图例状态
    handleLegendSelected() {
      // 不支持维度扩展
      const { chartAlias, extendDimensionList = [], extendDimensionColor } = this.chartUserConfig
      if (extendDimensionList.length && !extendDimensionColor) return
      if (LEGEND_SETTING_SAVE_CHART.includes(chartAlias) && this.chartUserConfig.saveLegendSelected) {
        this.resetAxisRange(this.chartUserConfig.saveLegendSelected)
      }
    },
    setLangLegendSelect(legendSelected) {
      const { extendDimensionList = [], extendDimensionColor } = this.chartUserConfig
      if ((extendDimensionList.length && !extendDimensionColor) || !legendSelected) return legendSelected
      const { dimension = [], metrics = [] } = this.content.chartResponse || {}
      const langDimension = this.chartSettings.dimension
      const langMetrics = this.chartSettings.metrics
      const obj = {}
      dimension.forEach((item, index) => {
        obj[item] = langDimension[index]
      })
      metrics.forEach((item, index) => {
        obj[item] = langMetrics[index]
      })
      const _langLegendSelected = {}
      Object.keys(legendSelected).forEach(item => {
        let key = obj[item] || item
        if (extendDimensionList.length) {
          const metricItem = Object.keys(obj).find(m => item.includes(m))
          key = metricItem ? item.replace(metricItem, obj[metricItem]) : item
        }
        _langLegendSelected[key] = legendSelected[item]
      })
      return _langLegendSelected
    },
    setHasChartData(val) {
      // 看板没有请求后台数据时，不渲染图形
      this.$nextTick(() => {
        if (this.hasGrid && val) {
          // 简单表格在点击运行之前获取的宽高是不对的，需要再次获取
          this.getCanvasSize({ from: 'setHasChartData', retainScroll: false })
        } else if (val) {
          erd.listenTo(this.$el, (element) => {
            this.elCanvasSize.canvasOffsetWidth = element.offsetWidth
            this.elCanvasSize.canvasOffsetHeight = element.offsetHeight
            if (!this.refreshWithResizeCharts.includes(this.chartUserConfig.chartAlias) || this.othersPieDataIndex > -1) {
              this.resizeEcharts({ source: 'setHasChartData' })
            }
          })
        }
      })
      if (val === this.hasChartData) return
      this.hasChartData = val
    },
    handleCalendarDate() {
      const { chartAlias, calendarSettings } = this.chartUserConfig
      if (chartAlias !== 've-calendar') return
      const { dimension = [], rows } = this.content.chartResponse || {}
      const dimensionStr = `VFMT_RAW_${dimension[0]}`
      if (this.isChartSet) {
        this.calendarDate = calendarSettings.date || []
      } else {
        this.calendarDate = getCalenderComponentMonths(this.getCurrentParamsPanel('content'), this.element.id, this.commonData.paramsType, this.dateFormat)
      }

      if (!this.calendarDate.length) {
        this.calendarFirstDate = ''
        this.calendarDateRange = ''
        return
      }
      const startDate = this.handleCalendarDay(this.calendarDate[0], 'start')
      const endDate = this.handleCalendarDay(this.calendarDate[this.calendarDate.length - 1], 'end')

      this.calendarFirstDate = startDate
      this.calendarDateRange = [startDate, endDate]
    },
    // 日历数据去除天
    handleCalendarDay(date, type) {
      const _date = date.replace(/\.|-/g, '/').split('/')
      const day = type === 'start' ? '01' : new Date(_date[0], _date[1], 0).getDate()
      return `${_date[0]}/${_date[1]}/${day}`
    },
    setLoading() {
      this.loadingInstance && this.loadingInstance.close()
      const loadingDom = (this.isChartSet || this.themeFullScreen || this.isChartLarge || this.mobileFullScreen)
        ? this.$el
        : (document.getElementById(this.element.id) || this.$el)
      if (this.setLoadingStatus(true, this.element.id)) return
      if (loadingDom) {
        if (this.isIgnoringUnselectedMetrics) {
          loadingDom.classList.add('none-opacity')
        }
        this.loading = true
        this.loadingInstance = this.$loading({
          target: loadingDom,
          spinner: 'sdp-loading-gif',
        })
        insetDom(this.loadingInstance.$el)
      }
    },
    closeLoading() {
      this.loadingInstance && this.loadingInstance.close()
      this.setLoadingStatus(false, this.element.id)
      this.loading = false
    },
    // 对外提供控制loading
    setElementLoadState(loading, type) {
      if (this.commonData.isOffApiLoading) return '关闭loading'
      if (this.loading && loading) return
      // loading开启时，mousemove事件标识置为true
      this.triggerMousemove = true
      this.clearHistogramAnimationTooltip()
      if (!loading) {
        this.closeLoading(loading)
        this.loadingFlag = false
        type === 'redrawEchartsDom' && this.animationPlay({ type: 'reset', from: 'redraw echarts dom' })
      } else {
        // this.loading = loading
        this.loadingFlag = true
        this.clearMapAnimation({ from: 'loading' })
        this.setLoading(loading)
      }
    },
    // 对外提供error 提示
    setElementErrorState(error) {
      this.setErrorStatus(error, this.element.id)
    },
    // 提供导出的方法
    async savePrintData(option = {}) {
      let data
      if (this.hasChartData) {
        data = await this.getPrintData(option)
      }
      return data
    },
    // 弹出指标选择器
    indicatorPopup() {
      if (!this.isBoardRequestFinish) return
      let postData = { type: 'bottom', data: {} }
      this.requestAppEvent(postData)

      // 清除上一次生成的popup
      if (this.picker) {
        // this.picker.$el.remove()
      }

      const selectChoiceTabIndex = this.chioceTabList.findIndex(c => c.index === this.checkedDimension)

      this.picker = this.$createPicker({
        alias: { value: 'name', text: 'name' },
        cancelTxt: ' ', // vue-pickers没有去除取消按钮属性,暂时先改成空格
        confirmTxt: this.$t('sdp.button.ensure'),
        data: [this.chioceTabList.map((e, index) => ({ name: e.name }))],
        selectedIndex: [selectChoiceTabIndex],
        onSelect: this.selectHandleIndicator,
        onCancel: this.cancelHandle,
      })
      this.picker.$el.style.zIndex = '10000'
      const parentDom = this.isPcHorizontalView && this.mobileFullScreen ? 'horizontal-view-box' : 'mobileElementPanel'
      this.$_insertElement(document.getElementById(parentDom), this.picker.$el)
      this.picker.$el.querySelector('.cube-popup-content').classList.add('chart-mobile-selector')
      if (this.mobileFullScreen && !this.isPcHorizontalView) {
        this.picker.$el.querySelector('.cube-popup-content').classList.add('chart-fullscreen-selector')
      }
      // cube-ui  picker的回弹
      const _ul = Array.from(this.picker.$el.querySelectorAll('.cube-picker-wheel-scroll'))
      const startKey = this.commonData.isMobileApp ? 'ontouchstart' : 'onmousedown'
      const endKey = this.commonData.isMobileApp ? 'ontouchend' : 'onmouseup'
      _ul.forEach(_ulItem => {
        const _liArr = Array.from(_ulItem.querySelectorAll('.cube-picker-wheel-item'))
        this.picker.$el[startKey] = () => {
          _ulItem.classList.remove('transition-duration-100')
          _liArr.forEach(_liItem => {
            _liItem.classList.remove('transition-duration-100')
          })
        }
        this.picker.$el[endKey] = () => {
          _ulItem.classList.add('transition-duration-100')
          _liArr.forEach(_liItem => {
            _liItem.classList.add('transition-duration-100')
          })
        }
      })
      this.picker.show()
    },
    cancelHandle() {
      let postData = { type: 'closeMask', data: {} }
      this.requestAppEvent(postData)
    },
    selectHandleIndicator(val, index, text) {
      let postData = { type: 'closeMask', data: {} }
      this.requestAppEvent(postData)
      this.checkedDimension = this.chioceTabList[index[0]].index
      this.chooseDimension(this.checkedDimension, 'outerClick')
    },
    async handleChart(e, isPopMobileClick = false, closeFullScreen = false) {
      // 瀑布图点击对比值、累计值不需要钻取、交互，移动端在dimensionPopup组件内处理
      if (!this.isMobile && this.chartUserConfig.chartAlias === 've-waterfall' && ['aggregate', 'contrast'].includes(e.seriesName)) return
      // 上一次交互完成才能进行下一次钻取和交互
      if ((this.isMobile && !isPopMobileClick) || !this.isBoardRequestFinish || this.isChartSet) return
      if (this.chartUserConfig.childChartAlias === 've-map-world' && this.element.content?.mapSchemeSetting?.schemeList?.length) {
        const countryMap = {
          // 弄成数组是方便查找，如果世界地图做了多语言，那就往数组里面加对应的字符串就行
          china: ['中国', 'China'],
          britain: ['英国', 'United Kingdom'],
        }
        const eName = this.isMobile ? e[0] : e.name
        const thisCountry = Object.keys(countryMap).find(k => countryMap[k].includes(eName))
        if (thisCountry) {
          const schemeIndex = this.element.content.mapSchemeSetting.schemeList.findIndex(s => s.saveObj.chartUserConfig.childChartAlias === `ve-map-${thisCountry}`)
          if (schemeIndex > -1) {
            this.mapSchemeSwitch(schemeIndex)
            this.refreshEl({
              ids: [this.element.id],
              preventTemplateRequest: false,
            })
            return
          }
        }
      }
      const { dimensionList, } = this.UserConfig
      // 当维度设置列权限时，不支持交互、钻取、维度超链接
      const isNotAuthorized = dimensionList.some(item => checkNoDataPermissions({ metric: item.alias || item.labeName, chartResponse: this.content.chartResponse }, false))
      if (isNotAuthorized) return this.chartMessageBox(this.$t('sdp.views.notAuthorized'))

      const sbiOptions = this.utils?.sbiOptions || {}
      const isSbiAddPreview = sbiOptions.isSbiDashPreview && sbiOptions.isAddDailyConcernElement
      // 钻取
      let drillDimensionsFlag = false
      if (this.isMap && this.isBritainMap && this.content.drillList.length && !isSbiAddPreview) {
        // 英国地图钻取
        this.britainMapDrill(e)
      } else if (!isSbiAddPreview) {
        drillDimensionsFlag = this.drillDimensionsClick(e)
        if (isPopMobileClick || !this.isMobile) this.mapDrill(e, isPopMobileClick)
      }

      // 记录点击了交互的临时变量
      if (!this?.tempData) {
        this.tempData = {}
      }
      if (!this?.tempData?.datazoomPositionInteractionClick) {
        this.tempData.datazoomPositionInteractionClick = true
      }

      // 交互
      this.interActionClick(e, drillDimensionsFlag)
      // 超链接
      this.dimensionSuperLink(e, closeFullScreen)
    },
    compatibilityChart() {
      if (!Object.keys(this.drillSettings).length) return
      // 元素模板预览时，走该兼容会导致数据多语言显示异常
      if (this.configs?.type === 'template' && this.commonData.isPreview) return
      const _this = this
      const content = this.element.content
      this.doRegisterMap()

      const themeConfig = getThemeConfig(this.themeType, { chartAlias: this.chartUserConfig.chartAlias, attributes: ['calendarConfig'] })
      compatibilitiChartData.call(this, { content, themeConfig })

      if (!this.isChartSet &&
        content.chartUserConfig.pagination.rankOf === '' &&
        this.$_getProp(content, 'drillSettings.pageInfo.pageSize')) {
        content.drillSettings.pageInfo.pageSize = this.isMobilePageImg ? 100 : 9999
      }
      if (this.chioceTab && this.chioceTab.length > 0) {
        this.chioceTab.forEach(item => {
          compatibilitiChartData.call(this, { content: item.saveObj, isSaveObj: 'saveObj', themeConfig })
        })
        // 初始化切换对应的指标
        this.$nextTick(() => {
          this.checkedDimension = this.element.content.saveIndex || 0
          this.chooseDimension(this.checkedDimension, 'init')
        })
      }
      // 兼容老看板数据的一些代码
      try {
        const _dimension = this.chartSettings.dimension || []
        const _dimensionList = this.chartUserConfig.dimensionList || []
        if (_dimensionList.length === 1) {
          _dimension[0] = _dimensionList[0].alias || _dimensionList[0].labeName
          this.chartSettings.metrics = this.chartUserConfig.metricsContainer.default.map(e => e.alias || e.labeName).slice(0, this.chartSettings.metrics.length)
          // 如果配置过交互，又修改了维度，清空交互设置
          if (!this.interactionSetting.filedName.includes(_dimensionList[0].labeName)) {
            if (this.chioceTab && this.chioceTab.length) {

            } else {
            Reflect.deleteProperty(this.element.content, 'interactionOptions')
            }
          }
        }
        // Y轴配置数据结构变更为数组
        // xy轴显示设置，替换掉下拉框不存在字体
        const { xAxis, yAxis } = this.chartUserConfig
        const xAisArr = [xAxis, ...yAxis]
        xAisArr.forEach(item => {
          if (item) {
            const { nameTextStyle, axisLabel } = item
            axisLabel && !fonts.includes(axisLabel.fontFamily) && (axisLabel.fontFamily = 'NotoSansHans-Regular')
            nameTextStyle && !fonts.includes(nameTextStyle.fontFamily) && (nameTextStyle.fontFamily = 'NotoSansHans-Regular')
          }
        })
        // 辅助线兼容问题数据，某些辅助线配置为null，尚不清楚数据来源
        const { xAuxiliaryLineData, yAuxiliaryLineData } = this.chartUserConfig
        filterAuxiliaryLine(xAuxiliaryLineData)
        filterAuxiliaryLine(yAuxiliaryLineData)

        // 字段设置兼容问题
        setFieldDecimalNum(this.drillSettings.layers?.[0]?.metrics)
        setFieldDecimalNum(this.chartUserConfig.metricsContainer.default)
      } catch (e) {
        console.log(e)
      }
      function filterAuxiliaryLine(data) {
        if (Array.isArray(data)) {
          data.splice(0, data.length, ...data.filter(item => item))
        }
      }
      function setFieldDecimalNum(metricArr) {
        if (!Array.isArray(metricArr)) return
        metricArr.forEach(item => {
          if (!item.viewFormat) return
          // 非货币字段小数点为空时，小数点改为0
          if (!item.labeName.includes('CURRENCY')) {
            Object.keys(item.viewFormat).forEach(key => {
              if (item.viewFormat[key] && !item.viewFormat[key].decimalNum && typeof item.viewFormat[key] === 'object') {
                item.viewFormat[key].decimalNum = 0
              }
            })
          }
          // 补充最小单位以下值简写数据，默认为false
          if (item.viewFormat.hasOwnProperty('currencyUnit') && !item.viewFormat.currencyUnit.hasOwnProperty('smallShort')) {
            item.viewFormat.currencyUnit.smallShort = false
          }
        })
      }
    },
    compatibility() {
      compatibilityDatasetField.call(this, this.element)
    },
    setLabelLineButtonList() {
      // 重置数值显示开关
      let chioceTab = this.element.content.chioceTab || []
      let list = []
      if (chioceTab.length) {
        chioceTab.forEach((item, index) => {
          let listItem = {
            id: item.id,
            labelLineButton: true
          }
          // 新增/删除指标选择器时，需要保留原始数据
          if (this.labelLineButtonList.length) {
            let oldList = this.labelLineButtonList.find(data => data.id === item.id)
            if (oldList) {
              listItem = this.$_JSONClone(oldList)
            }
          }
          list.push(listItem)
        })
      } else {
        list = [{
          labelLineButton: true
        }]
      }
      this.labelLineButtonList = list
    },
    // 移动端修改日期控件值
    mobileDateComponentClick(val) {
      if (!this.isBoardRequestFinish) return
      this.selectedDateVal = val
      // 重置page = 1
      this.drillSettings.pageInfo.page = 1
      this.changeDateDimensionVal()
    },
    // 修改图形日期控件选中值
    changeDateDimensionVal() {
      if (this.hasGrid) {
        this.createTable({ preventRequest: false, from: 'changeDateDimensionVal' })
        // todo---twm
        return
      }
      // 图形存在交互时，切换日期控件，清空被交互元素状态
      const data = {
        ids: [this.element.id]
      }
      const interactionOptions = this.getFilteredInterActionOptions()
      const associElements = Array.isArray(interactionOptions) && interactionOptions[0] && interactionOptions[0].associElements
      if (associElements?.length) {
        associElements.map(item => { !data.ids.includes(item.id) && data.ids.push(item.id) })
        Object.assign(data, {
          dimensionDrill: true, // dimensionDrill为刷新本身元素标识
          element: { type: TYPE_INTERACTION.INTERACTION_SETTING }
        })
      }
      // 清空交互数据
      this.elList.map(el => {
        if (!data.ids.includes(el.id)) return
        const interactionOptions = el.content.interactionOptions
        if (Array.isArray(interactionOptions)) {
          interactionOptions.forEach(item => {
            this.$delete(item, 'children')
            this.$delete(item, 'values')
          })
        } else if (interactionOptions) {
          this.$delete(interactionOptions, 'children')
          this.$delete(interactionOptions, 'values')
        }
      })
      this.refreshEl(data)
    },
    // 清空图形日期控件选中值，默认为图形编辑界面日期维度值
    clearDateDimensionVal() {
      if (this.showDateComponent({})) {
        this.selectedDateVal = this.chartUserConfig.dimensionList.find(item => item.columnTpe === 'date').timeDimensionSplittingRule
      }
    },
    // 重绘图形，解决水滴图波纹颜色、地图缩放问题、象形柱图标签开启单位简写在hover数据时才生效问题
    redrawEchartsDom(time = 100) {
      if (this.setUpdateChartTimerId) {
        clearTimeout(this.setUpdateChartTimerId)
      }
      this.hasChartData && this.setElementLoadState(true, 'redrawEchartsDom')
      this.chartKey++
      this.setUpdateChartTimerId = setTimeout(() => {
        this.setElementLoadState(false, 'redrawEchartsDom')
      }, time)
    },
    // 重新渲染一遍图形，解决水滴图波纹颜色、地图缩放问题、象形柱图标签开启单位简写在hover数据时才生效问题
    setUpdateChart(time = 100) {

    },
    deleteElementData() {
      const tempChartData = {
        columns: [],
        rows: []
      }
      Object.assign(this.content.chartData, tempChartData)
      Reflect.deleteProperty(this.content, 'img')
      Reflect.deleteProperty(this.content, 'printData')
      if (
        Array.isArray(this.content.chioceTab) &&
            this.content.chioceTab.length
      ) {
        this.content.chioceTab.forEach(eve => {
          Reflect.deleteProperty(eve.saveObj, 'img')
          Reflect.deleteProperty(eve.saveObj, 'printData')
        })
      }
      this.hasChartData = false
      this.isGridDrillArrowShow = false
      this.setElementLoadState(false)
    },
    // 收集使用的全局参数id return ['id1', 'id2']
    getElementGlobalParamIdList() {
      function getChartMetricList(content) {
        // 存在自定义计算的字段：度量字段、目标值字段、辅助线字段
        const { metricsContainer, gaugeTarget, xAuxiliaryLineData = [], yAuxiliaryLineData = [], dimensionList = [] } = content.chartUserConfig
        let metric = metricsContainer?.default ? [...metricsContainer.default] : []
        if (gaugeTarget?.defaults) {
          metric.push(gaugeTarget.defaults)
        }
        const auxiliaryLine = [...xAuxiliaryLineData, ...yAuxiliaryLineData]
        auxiliaryLine.forEach(item => {
          if (item.auxiliaryLineType === 'dimension' && item.metricValue === 'otherField') {
            metric.push(item)
          }
          if (item.auxiliaryLineType === 'custom') {
            metric.push(item.filed1, item.filed2)
          }
        })
        dimensionList.forEach(d => {
          if (d.customDimension?.expression) {
            metric.push({ exp: d.customDimension.expression })
          } else if (d.webFieldType) {
            metric.push(d)
          }
        })
        return metric.map(m => this.getCustomFieldPreview(m))
      }
      let metric = []
      if (this.chioceTab.length > 1) {
        this.chioceTab.forEach(tab => {
          metric.push(...getChartMetricList.call(this, tab.saveObj))
        })
      } else {
        metric = getChartMetricList.call(this, this.content)
      }
      const ids = getGlobalParamId(metric, this.globalParameterIdList)
      return Array.from(new Set(ids))
    },
    // 瀑布图操作图例时需要将对应辅助值一起隐藏
    waterfallLegendSelected(selected) {
      const { columns, rows } = this.chartData
      const legendEvents = {
        'legendUnSelect': [],
        'legendSelect': [],
      }
      const legendName = this.chartUserConfig.waterfallSetting?.legendName || { positive: '增长', negative: '下降' }
      const growthReg = new RegExp(legendName.positive, 'g')
      const declineReg = new RegExp(legendName.negative, 'g')
      Object.keys(selected).map(legendName => {
        if (!selected[legendName]) {
          if (legendName.match(growthReg)) {
            legendEvents['legendUnSelect'].push('growthTotal')
          } else if (legendName.match(declineReg)) {
            legendEvents['legendUnSelect'].push('declineTotal')
          }
        } else {
          if (legendName.match(growthReg)) {
            legendEvents['legendSelect'].push('growthTotal')
          } else if (legendName.match(declineReg)) {
            legendEvents['legendSelect'].push('declineTotal')
          }
        }
      })
      Object.keys(legendEvents).map(eventItem => {
        legendEvents[eventItem].map(seriesName => {
          this.echartInstance.dispatchAction({
            type: eventItem,
            name: seriesName,
          })
        })
      })
    },
    // 禁止图例点击事件
    disableLegendSelect(e) {
      this.echartInstance.setOption({ animation: false })
      this.echartInstance.dispatchAction({
        type: 'legendSelect',
        name: e.name
      })
      this.echartInstance.setOption({ animation: true })
    },
    // 操作图例时重新计算水滴图完成率
    calRateCompletion(selected) {
      let targetSum = 0 // 目标值
      let indicatorSum = 0 // 指标值
      const { rows } = this.chartData
      const { measurementNullValue, dimension, metrics } = this.content.chartResponse
      const [ metric, target ] = metrics
      // 是否全部是空值数据
      let isAllNullValue = true
      // 是否有图例被选中
      let hasLegendSelect = false
      rows.map(item => {
        const dimensionName = item[`VIEWFORMAT_${dimension[0]}`]
        if (selected.hasOwnProperty(dimensionName) && !selected[dimensionName]) return
        hasLegendSelect = true

        targetSum += isNaN(item[target]) ? 0 : parseFloat(item[target])
        indicatorSum += isNaN(item[metric]) ? 0 : parseFloat(item[metric])
        if (!isNaN(item['VFMT_RAW_' + target]) && !isNaN(item['VFMT_RAW_' + metric])) {
          isAllNullValue = false
        }
      })
      // 水滴图目标值设置数值时，targetSum为用户输入的数值
      const gaugeTarget = this.chartUserConfig.gaugeTarget
      if (gaugeTarget && gaugeTarget.type === 'expression') {
        targetSum = rows[0]?.['GAUGE_TARGET']
      }
      // targetSum为0时，rateCompletion为Infinity，isNaN(Infinity) = false
      let rateCompletion = targetSum === 0 ? 0 : parseFloat((indicatorSum / targetSum).toFixed(4))
      if (isNaN(rateCompletion)) {
        rateCompletion = 0
      }
      // 百分比值
      let percentText = (rateCompletion * 100).toFixed(2)
      const title = this.chartUserConfig.liquidFillSetting.title
      const negativeNumberType = this.tenantData.settingConfig?.negativeNumberType

      // 当图例数据全部为空值时，完成率显示为空值
      if (isAllNullValue && hasLegendSelect) {
        percentText = measurementNullValue
      } else if (negativeNumberType === '0' && percentText.includes('-')) {
        // 负数显示格式
        percentText = '(' + percentText.replace('-', '') + ')'
      }
      const labelText = `{a|${title}}\n${percentText}%`
      this.echartInstance.setOption({
        series: [{
          id: 'liquidFill',
          data: [{
            value: rateCompletion,
            tooltip: {
              formatter: () => `${title || this.$t('sdp.views.PercentageComplete')}:${percentText}%`,
            },
          }],
          label: {
            formatter: labelText,
          },
        }],
      })
    },
    handleRoundCascadesLabel(selected) {
      const { columns, rows } = this.chartData
      const dimenison = columns[0]
      const series = []
      rows.map((rowItem, index) => {
        series.push({
          type: 'pie',
          name: rowItem[dimenison],
          markLine: {
            show: selected[rowItem[dimenison]]
          },
          showEmptyCircle: false
        })
      })
      this.echartInstance.setOption({
        series: series,
      })
    },
    // 鼠标滚动
    chartScroll(direction) {
      if (!this.chartUserConfig.scrollBarShow || !this.echartInstance || !this.chartData.rows.length) return
      // 获取滚动条当前位置scrollBarPosition，滚动条默认比例scrollBarScale
      const { chartAlias } = this.chartUserConfig
      const scrollBarScale = Number(this.chartSettings.scrollBarScale)
      // 是否是纵向滚动条图形
      const isVertical = VERTICAL_SCROLL_BAR_CHART.includes(chartAlias)
      const isDimensionDataScroll = isScrollAccordingToDimension({ chartUserConfig: this.chartUserConfig, chartSettings: this.chartSettings })

      const dataZoomConfigKey = {
        metricScroll: {
          start: 'start',
          end: 'end',
          total: 100,
          scrollChangeScale: this.chartData.rows.length < 100 ? Math.ceil(100 / this.chartData.rows.length) : 1 // 鼠标滚动触发一次的滚动比例
        },
        dimensionScroll: {
          start: 'startValue',
          end: 'endValue',
          total: this.chartData.rows.length,
          scrollChangeScale: this.chartData.rows.length < 100 ? 1 : Math.round(this.chartData.rows.length * 0.01)
        },
      }
      const dataZoomConfig = isDimensionDataScroll ? dataZoomConfigKey.dimensionScroll : dataZoomConfigKey.metricScroll
      const { start: startKey, end: endKey, total, scrollChangeScale } = dataZoomConfig

      if (!this.scrollBarPosition) {
        this.scrollBarPosition = isVertical ? { [startKey]: [total] - scrollBarScale, [endKey]: [total] } : { [startKey]: 0, [endKey]: scrollBarScale }
      }
      // 鼠标滚动触发后滚动条位置
      let start
      let end
      if ((isVertical && direction === 'down') || (!isVertical && direction === 'up')) {
        start = this.scrollBarPosition[startKey] - scrollChangeScale
        end = this.scrollBarPosition[endKey] - scrollChangeScale
        start < 0 && (start = 0)
        end < scrollBarScale && (end = scrollBarScale)
      } else {
        start = this.scrollBarPosition[startKey] + scrollChangeScale
        end = this.scrollBarPosition[endKey] + scrollChangeScale
        start > total - scrollBarScale && (start = total - scrollBarScale)
        end > total && (end = total)
      }
      this.scrollBarPosition = {
        [startKey]: start,
        [endKey]: end,
      }
      this.echartInstance.dispatchAction({
        type: 'dataZoom',
        // dataZoom 组件的 index
        dataZoomIndex: 0,
        // 开始位置的百分比，0 - 100
        [startKey]: start,
        // 结束位置的百分比，0 - 100
        [endKey]: end,
      })
    },
    // 图形设计-保存时会调用该方法
    saveElementChart() {
      this.saveLegendSelect()
      this.saveGridColumnWidth()
      this.saveGeoZoomAndPosition()
    },
    // 保存图例选择状态
    saveLegendSelect() {
      if (!Object.keys(this.echartInstance).length) return
      const { showSaveLegendSelected, saveLegendSelected, extendDimensionColor, extendDimensionList = [] } = this.chartUserConfig
      const option = this.echartInstance.getOption()
      let legend = showSaveLegendSelected ? option.legend?.[0]?.selected : undefined
      // 存在扩展维度且关闭扩展维度颜色开关时，不支持保存图例状态
      if (extendDimensionList.length && !extendDimensionColor && showSaveLegendSelected) {
        legend = undefined
      }
      if (legend !== saveLegendSelected) {
        this.$set(this.chartUserConfig, 'saveLegendSelected', legend)
      }
    },
    // 只在最后点击保存的时候设置chartUserConfig.gridSetting.columnWidth
    saveGridColumnWidth() {
      if (!this.hasGrid) return
      this.$set(this.chartUserConfig.gridSetting, 'columnWidth', this.gridColumnWidth || {})
    },
    saveGeoZoomAndPosition() {
      if (this.chartUserConfig.chartAlias !== 've-map-parent') return
      this.$set(this.chartUserConfig.mapSetting, 'geoRoamInfo', this.geoRoamInfo)
    },
    setStaticChartData(chartData) {
      this.$set(this, 'staticChartData', this.$_deepClone(chartData))
    },
    chartMessageBox(message) {
      if (this.utils.isMobile) {
        this.dimensionSensibilisationMessage = this.$createToast({
          type: 'warn',
          time: CREATE_TOAST_TIME,
          txt: message
        })
        this.$_insertElement(document.getElementById('mobileElementPanel'), this.dimensionSensibilisationMessage.$el)
        this.dimensionSensibilisationMessage.show()
      } else {
        this.dimensionSensibilisationMessage = this.$message(message)
      }
    },
    changeCalendarRange(type) {
      const { chartAlias, calendarSettings = {} } = this.chartUserConfig
      if (chartAlias !== 've-calendar' || !calendarSettings.openCalendarPage) return
      if (!this.calendarDateRange || !this.calendarFirstDate) {
        return this.chartMessageBox(this.$t('sdp.views.calendarPageMsg'))
      }
      const { flipMonths = 1, monthsOfOnePage = 1 } = calendarSettings
      let newCalendarFirstDate = ''
      if (monthsOfOnePage === 1 && !this.isChartSet) {
        // 若monthsOfOnePage = 1并且在非编辑界面 则执行零散日历非连续翻月逻辑，执行常规连续翻月逻辑
        if (this.calendarDate.length) {
          const calendarFirstDate = this.calendarFirstDate.replace(/\//g, '-').slice(0, 7)
          const findIndex = this.calendarDate.findIndex(o => o === calendarFirstDate)
          const bool = (findIndex === 0 && type === 'pre') || (findIndex === this.calendarDate.length - 1 && type === 'next')
          if (bool) {
            return this.chartMessageBox(this.$t('sdp.views.calendarPageMsg'))
          } else {
            newCalendarFirstDate = getAfterPagingCalendarDate(this.calendarDate[type === 'pre' ? findIndex - 1 : findIndex + 1], 0)
          }
        }
      } else {
        // 计算翻页后的初始日期
        newCalendarFirstDate = getAfterPagingCalendarDate(this.calendarFirstDate, type === 'pre' ? flipMonths * -1 : flipMonths)
      }
      // 翻页后日期大于最大日期，不能向后翻页；小于最小日期，不能向前翻页
      const notPageUp = this.$_compareCalendarDate(newCalendarFirstDate, this.calendarDateRange[0]) === -1
      const notPageDown = this.$_compareCalendarDate(newCalendarFirstDate, this.calendarDateRange[1]) === 1
      if ((type === 'pre' && notPageUp) || (type === 'next' && notPageDown)) {
        return this.chartMessageBox(this.$t('sdp.views.calendarPageMsg'))
      }
      this.calendarFirstDate = newCalendarFirstDate
      this.resetAfterConfig()
    },
    // 获取筛选指标后的交互设置项
    getFilteredInterActionOptions(options) {
      let { interactionOptions, chioceTab, saveIndex } = this.element.content
      let _options = options || interactionOptions
      if (chioceTab?.length && chioceTab[saveIndex] && _options?.length) {
        _options = _options.filter(item => item.chioceTabId === chioceTab[saveIndex]['id'])
      }
      return _options || []
    },
    // 8577 图形外部调用，替换图表色系
    replaceColorScheme(data) {
      const { colorScheme: newColorScheme, colors, opacity } = data
      const doReplace = (source) => {
        if (!source) return
        let content = source.saveObj ? source.saveObj : source
        const chartUserConfig = content.chartUserConfig
        const { chartAlias } = chartUserConfig
        console.log(`开始替换图形色系: ${this.element.id}, ${source.saveObj ? `指标选择器: ${source.id}, ` : ', '}类型：${chartAlias}`)
        // 判断是否有指标选择器，如果有则要处理里面的saveObj，否则直接处理this.chartUserConfig
        this.globalChartColorScheme = {
          [this.themeType]: data
        }
        this.$set(chartUserConfig, 'dimensionColors', [])
        this.$set(chartUserConfig, 'colorScheme', newColorScheme)
        this.$set(chartUserConfig, 'opacityList', { ...(chartUserConfig.opacityList || {}), [newColorScheme]: opacity })
        // 度量颜色需要一个兜底的正确色，来保证有些图形默认情况下没有color从而出现灰色的情况
        let defaultMetricColors = newColorScheme === 'default' ? Color.getDefaultChartColor(chartUserConfig, { vm: this }) : colors
        if (Color.isSystemScheme(newColorScheme)) {
          defaultMetricColors = defaultMetricColors.map(color => {
            const index = color.lastIndexOf(',') + 1
            return color.replace(color.substr(index), `${opacity})`)
          })
        }
        this.$set(chartUserConfig, 'colors', defaultMetricColors)

        const { colorType: oldColorType, dimensionColors, colorScheme, opacityList = {}, dimensionList = [] } = chartUserConfig
        let oldDimensionColors = dimensionColors || []
        if (oldColorType === 'dimension') {
          let needRefeash = false
          oldDimensionColors = oldDimensionColors.map((c, i) => {
            let color = c.color
            let name = c.name
            if (typeof c === 'string') {
              if (i >= content.chartData?.rows.length) return
              color = c
              name = content.chartData?.rows[i][dimensionList[0].alias || dimensionList[0].labeName]
            }
            if (!c.type) {
              needRefeash = true
              const colorFormatter = Color.generateColorItem(color, { name: name, colorScheme, opacity: opacityList[colorScheme], })
              return Object.assign({}, colorFormatter, c)
            }
            return c
          }).filter(c => !!c)
          const _dimensionColors = Color.getDimensionColor(this, !oldColorType ? 'metric' : 'dimension')

          if (_dimensionColors.needRefeash.length || needRefeash) {
            this.$set(chartUserConfig, 'dimensionColors', [...oldDimensionColors, ..._dimensionColors.needRefeash])
          }
        } else {
          const colorNames = Color.getMetricColorNames(this, chartUserConfig)
          if (!colorNames.length) return
          const configableColors = Color.getMetricColors(this, chartUserConfig, colorNames, data)
          const newColors = this.$_deepClone(chartUserConfig.colors)
          newColors.splice(0, configableColors.length, ...configableColors)
          this.$set(chartUserConfig, 'colors', newColors)
        }
        setThemeConfig(chartUserConfig, this.themeType)
      }
      doReplace(this.content)
      if (this.chioceTab.length) {
        this.chioceTab.forEach(tab => {
          doReplace(tab)
        })
      }

      this.resetAfterConfig({ source: 'colorScheme', isMapInit: true })
    },
    // 8697 图形外部调用，卡片以指标选择器方式交互图形
    // 如果当前选择的已在当前展示的指标中，则不进行切换
    cardInteractionIndicatorSwitch(mainId, indicators, columnName) {
      let _forceChange = this.isBoardRequestFinish
      const _timeoutRefresh = (cb) => setTimeout(() => {
        this.refreshChart({ source: 'cardInteractionIndicatorSwitch' })
        cb && cb()
      })
      if (this.cardInteractionChioceTab.isActive && this.cardInteractionChioceTab.mainId === mainId && this.cardInteractionChioceTab.columnName === columnName) {
        const isActive = this.element.cardInteractionState?.isActive
        this.$delete(this.element, 'cardInteractionState')
        _timeoutRefresh(() => {
          if (this.metricChioceTab.isRelated) {
            this.refreshEl({ ids: [this.element.id] })
          } else {
            if (isActive) {
              const tempIndex = this.tempSaveIndex > -1 ? this.tempSaveIndex : 0
              if (tempIndex !== this.content.saveIndex) {
                this.checkedDimension = tempIndex
                this.chooseDimension(this.checkedDimension, 'outerClick', _forceChange)
              }
            }
          }
        })
        return
      }
      this.$set(this.element, 'cardInteractionState', { mainId, indicators, columnName, isActive: true })
      if (this.chioceTab.length > 1) {
        let currentId = this.chioceTab[this.checkedDimension].id
        const isContainCurrent = indicators.includes(currentId)
        if (!isContainCurrent && indicators.length) {
          const checkedDimension = this.chioceTab.findIndex(ct => ct.id === indicators[0])
          if (checkedDimension > -1) {
            this.checkedDimension = checkedDimension
            this.chooseDimension(checkedDimension, 'cardInteractionIndicatorSwitch')
          } else {
            this.$delete(this.element, 'cardInteractionState')
            _timeoutRefresh()
          }
        } else {
          if (!this.indicatorSelectorShow && indicators.length > 1) {
            const index = this.chioceTab.findIndex(ct => ct.id === indicators[0])
            this.checkedDimension = index
            this.chooseDimension(index, 'cardInteractionIndicatorSwitch')
          } else {
            _timeoutRefresh()
          }
        }
      } else {
        _timeoutRefresh()
      }
    },
    // 移动端tab显示
    updateScrollNavBarItemsWidth() {
      if (!this.isMobile || this.isIndicatorDropDown) return
      setTimeout(() => {
        const scrollNavBarRef = this.$refs.scrollNavBarRef
        if(!scrollNavBarRef) return
        setScrollNavBarItemsWidth({
          scrollNavBarDOM: scrollNavBarRef.$el,
          maxLengthToShow:  70,
          padding: this.mobileCursorPadding,
          settings: this.content
        }).then(() => {
          setTimeout(() => {
            scrollNavBarRef.refresh()
            this.isCubeScrollAnimated || (this.isCubeScrollAnimated = true)
          }, 0)
        })
      }, 300)
    },
    // 手势信息
    handleTouchStart(event) {
      if (this.isMobile) {
        console.log('手势，开始：', event)
        const touch = event.touches?.[0]
        if (touch) {
          this.swipeInfo.touchStartX = touch.clientX
          this.swipeInfo.touchStartY = touch.clientY
        }
      }
    },

    handleTouchMove(event) {
      // 可选：阻止默认滚动行为
      // event.preventDefault()
    },

    handleTouchEnd(event) {
      if (this.isMobile) {
        console.log('手势，结束：', event)
        const touch = event.changedTouches?.[0]
        if (touch) {
          this.swipeInfo.touchEndX = touch.clientX
          this.swipeInfo.touchEndY = touch.clientY
          this.detectSwipeDirection()
        }
      }
    },

    detectSwipeDirection() {
      const deltaX = this.swipeInfo.touchEndX - this.swipeInfo.touchStartX
      const deltaY = this.swipeInfo.touchEndY - this.swipeInfo.touchStartY
      const absDeltaX = Math.abs(deltaX)
      const absDeltaY = Math.abs(deltaY)
      console.log('absDeltaX', absDeltaX, absDeltaY)
      // 判断是否达到最小滑动距离
      if (Math.max(absDeltaX, absDeltaY) < this.swipeInfo.minSwipeDistance) {
        return // 滑动距离太小，忽略
      }
      // 判断滑动方向
      if (absDeltaX > absDeltaY) {
        // 水平滑动
        if (deltaX > 0) {
          this.onSwipe('right')
        } else {
          this.onSwipe('left')
        }
      } else {
        // 垂直滑动
        if (deltaY > 0) {
          this.onSwipe('down')
        } else {
          this.onSwipe('up')
        }
      }
    },
    onSwipe(direction) {
      const opt = this.echartInstance.getOption()
      const d = opt?.dataZoom?.[0]?.orient
      console.log('手势，向：', direction, d)
      const isSwipeH = ['left', 'right'].includes(direction)
      const isSwipeV = ['up', 'down'].includes(direction)
      if ((d === 'vertical' && isSwipeH) || (d === 'horizontal' && isSwipeV)) {
        const eventData = new EventData({
          type: 'clearForce',
          target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanelMobile'],
          targetFn: 'clearForce',
          data: { ids: [this.element.id] },
        })
        this.$emit('eventBus', eventData)
      } else {
        // 没有datazoom或者是竖向的datazoom，则清除聚焦
        if (isSwipeV && (d === 'vertical' || !d)) {
          setTimeout(() => {
            if (!this.isDataZoomEmitted && this.element.isFocus) {
              const eventData = new EventData({
                type: 'clearForce',
                target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanelMobile'],
                targetFn: 'clearForce',
                data: { ids: [this.element.id] },
              })
              this.$emit('eventBus', eventData)
            }
          }, 300)
        }
      }
    }
  },
}
</script>
<style lang="scss">
  @import "packages/base/grid/theme/skin.scss";
  @import './style/tooltipStyle.scss';
  @import './variable.scss';
  .element-chart-mobile-preview{
    .calendar-btn {
      margin-bottom: 10px;
    }
  }
  .widget .widget-chart .sdp-tooltip-wrap,.sdp-tooltip-wrap {
    display: flex;
    align-items: center;
    justify-content: center;

    .sdp-tooltip-btn {
      width: auto !important;
      flex: 1;
      min-width: 50px;
      height: 28px !important;
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.30);
      cursor: pointer;
      text-align: center;
      padding: 4px 0;

      color: #FFF;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;

      &:nth-child(n+2) {
        margin-left: 8px;
      }
    }

    .sdp-tooltip-close {
      position: absolute;
      right: 3px;
      top: 3px;
      background: #333333;
      color: #FFFFFF;
      width: 16px !important;
      height: 16px !important;
      line-height: 16px;
      border-radius: 50%;
      text-align: center;

      i {
        font-size: 14px;
      }
    }
  }
.auxiliaryLine-name {
  font-size: 12px;
  line-height: 15px;
  color: var(--sdp-xxbt2);
}
  $widgetW:100% !important;
  .chart-mobile-selector {
    border-top: none!important;
  }
  .auxiliaryLine-item{
    display: flex;
    align-items: center;
    cursor: pointer;
    .ellipsis{
      max-width: 80px;
      @include ellipsis;
    }
    &.gray-item{
      color: var(--sdp-jys);
      .auxiliaryLine-name {
        color: var(--sdp-jys);
      }
      .auxiliaryLine-line{
        border-color: var(--sdp-jys) !important;
      }
    }
  }
  .auxiliaryLine-line{
    width: 24px;
    border-bottom-width: 2px;
    border-bottom-style: dashed;
    height: 2px;
    margin-right: 7px;
  }
  .chart-fullscreen-selector {
    height: 60vh!important;
    .cube-picker-panel {
      height: 60vh!important;
    }
  }
  .element-chart.none-opacity {
    .el-loading-mask {
      opacity: 1!important;
    }
  }
  .element-chart {
    position: relative;
    // color: deepskyblue;
    width: 100%;
    height: 100%;
    overflow: hidden;
    .placeholder-image {
      width: 100%;
      height: 100%;
      color: var(--sdp-srk-bxwzs);
      font-size: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    .sdp-chart-no-data-mask {
      width: 100%;
      height: 100%;
      position: static;
    }
    .sdp-chart-content {
      height: unset!important;
    }
    .mobile-chart-legend {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    .sdp-chart-grid-content{
      flex: 1;
      overflow: hidden;
      width: 100%;
    }
    .sdp-chart-decomposition{
      flex: 1;
      overflow: hidden;
      width: 100%;
    }
    .bottom-legend {
      margin-top: 8px;
    }
    .sdp-chart-title {
      display: flex;
      justify-content: space-between;
      max-width: 100%;
      // overflow: hidden;
      .title-nowrap {
        white-space: nowrap;
      }
      .chart-pointer {
        cursor: pointer;
      }
    }
    .sdp-chart-measure {
      .has-margin{
        margin-top: 12px;
      }
      &.mobile-chart-measure {
        .has-margin{
          margin: 5px 0 2px 0;
        }
      }
      .horizontal-scroll-list-wrap {
        width: 100%;
        .cube-scroll-content {
          display: inline-block;
        }
      }
      .list-wrapper {
        white-space: nowrap;
        pre {
          display: inline-block;
          margin-right: 10px;
          cursor: pointer;
          &:last-child {
            margin-right: 0px;
          }
        }
      }
    }
    .backdrill-title {
      display: flex;
      align-items: center;
      // width: 40%;
      flex: 1;
    }
    .title-right {
      display: flex;
      margin-left: 24px;
    }
    .mobile-chart-title {
      // display: block;
      display: flex;
      align-items: center;
      width: 100%;
    }
  }
  .widget {
    position: relative;
    // color: deepskyblue;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    pre {
      margin: 0;
    }
    .widget-chart {
      // width: $widgetW;
      // height: $widgetW;
      flex: 1;
      div:first-child {
        width: $widgetW;
        height: $widgetW;
        div:first-child {
          width: $widgetW;
          height: $widgetW;
          canvas:first-child {
            width: $widgetW;
            height: $widgetW;
          }
        }
      }
    }
    .dimension-selector {
      // position: absolute;
      transition: all 0.5s linear;
      width: 170px;
      margin-right: 25px;
      // top: -1px;
      // .el-input.is-focus .el-input__inner {
      //   border-color: $color-main-preview!important;
      // }
      // .el-input__inner:focus {
      //   border-color: $color-main-preview!important;
      // }
      // &.inContainer{
      //   top: 0px;
      // }
    }
    .pc-selector {
      float: right;
    }
    .date-component-left {
      margin-right: 25px;
      .el-input {
        width: 70px;
        .el-input__inner {
          padding-right: 20px;
        }
      }
    }
    .date-component {
      // position: absolute;
      float: right;
      margin-right: 25px;
      // top: -1px;
      // &.inContainer{
      //   top: 0px;
      // }
      .el-input {
        width: 70px;
        .el-input__inner {
          padding-right: 20px;
        //   text-overflow: ellipsis;
        //   overflow: hidden;
        //   white-space: nowrap;
        }
      }
    }
    .mobile-date-component {
      // position: absolute;
      // left: 0;
      // right: 0;
      margin-bottom: 6px;
      padding: 0;
      color: $color-XHXTABWXZ;
      //background: transparent !important;
      .tab {
        height: 24px;
        width: 100%;
        border-radius: 8px;
        border-radius: 8px;
        display: flex;
        .date-component-option {
          cursor: pointer;
          width: 20%;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          text-align: center;
          > span {
            line-height: 24px;
            font-family: PingFang-SC-Medium;
            font-size: 12px;
            color: $color-XHXTABWXZ;
            letter-spacing: 0;
            max-width: 80%;
          }
        }
        .select-date-option {
          margin: 2px;
          height: 24px;
          color: $color-XHXTABXZ;
          > span {
            color: $color-XHXTABXZ;
            line-height: 20px;
            width: 32px;
            font-size: 14px;
            display: inline-block;
            border-bottom: 1px solid $color-XHXTABXZ;
          }
        }
      }
    }
  }
  .speica-style {
    position: absolute;
    top: 20px;
    right: 0px;
  }
  .up-drill-button {
    // position: absolute;
    // top: 3px;
    // left: -13px;
    cursor: pointer;
    margin-right: 5px;
    &.grid-up-drill-button{
      // left: 3px;
    }
    i {
      font-size: 22px;
      color: var(--sdp-srk-bxwzs);
    }
  }
  .up-drill-container-button {
    top: 2px;
    left: 10px;
  }
  .element-chart {
    .placeholder-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 16px;
      color: var(--sdp-srk-bxwzs);
    }
    .text {
      font-size: 16px;
      color: var(--sdp-srk-bxwzs);
      height: 100%;
      margin-bottom: 10px;
    }
    .no-data-permissions {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      .title-nowrap {
        white-space: nowrap;
      }
      .chart-pointer {
        cursor: pointer;
      }
      .text {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    /deep/ .el-tooltip__popper[data-sdp-el-insert='el-tooltip'],  /deep/ .el-tooltip__popper[data-sdp-el-insert-firefox='el-tooltip']{
      position: absolute !important;
    }
  }
  .transition-duration-100 {
    transition-duration: 100ms!important;
  }
  .choice-fornone {
    position: absolute;
    top: 0;
    right: 25px;
    > div {
      float: right;
    }
  }
  .choice-tabs {
    display: flex;
    margin-right: 25px;
    .chioce-tab-item {
      display: inline-block;
      font-size: 14px;
      height: 18px;
      line-height: 18px;
      // max-width: $element-container-tab-max-width;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #CECECE;

      &:not(:last-child) {
        margin-right: 24px;
      }

      &:not(.active):hover {
        opacity: 0.5 !important;
      }
      cursor: pointer;
    }
  }
  .chart-choice-tabbar {
    width: calc(100% - 5px);
    margin-bottom: 12px;
    &.chart-choice-tabbar-custom-type1 {
      .cube-scroll-wrapper {
        text-align: left;
      }
    }
    &.chart-choice-tabbar-custom {
      .cube-scroll-nav-bar.cube-scroll-nav-bar_horizontal {
        .cube-scroll-content {
          width: auto;
        }
      }
    }
    &.chartChoiceTabbarWidth100 {
      .cube-scroll-nav-bar.cube-scroll-nav-bar_horizontal {
        .cube-scroll-content {
          width: 100%;
        }
      }
    }

    .cube-scroll-nav-bar.cube-scroll-nav-bar_horizontal {
      // .cube-scroll-content {
      //    width: 100%;
      // }
      .cube-scroll-nav-bar-items {
        padding: 2px;
        display: flex;
      }
      .cube-scroll-nav-bar-item {
        padding: 0 10px;
        font-size: 12px;
        letter-spacing: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        > span {
          height: 28px;
          line-height: 28px;
        }
      }
    }
    &.indicator-underline{
      .cube-scroll-nav-bar.cube-scroll-nav-bar_horizontal{
        .cube-scroll-nav-bar-items {
          padding: 0 2px;
        }
      }
    }
  }
  .calendar-btn {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    .el-button, .el-button:focus {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border-color: $color-AYFYBK;
      background-color: transparent;
      color: $color-AYFYJT;
    }
    .el-button:hover, .el-button:active {
      border-color: $color-AYFYBKGL !important;
      background-color: $color-AYFYXZDS !important;
      color: $color-AYFYJTGL !important;
    }
    .el-button:nth-of-type(2) {
      margin-left: 30px;
    }
    /deep/ .el-button--small.is-circle {
      padding: 5px;
    }
  }
  .chart-desc {
    margin-left: 5px;
    color: var(--sdp-zs);
    line-height: 19px;
    font-size: 12px !important;
  }

  .chart-tip-popover {
    display: flex;

    &.is-mobile i {
      color: var(--sdp-xxbt2);
    }
  }

  .element-chart-desc{
    color: var(--sdp-xxbt2);
    margin-left: 5px;
    height: 16px;
    line-height: 18px;
    .icon-sdp-miaoshu{
      font-size: 16px;
    }
  }
  .show-state-icon{
    display: flex;
    gap:5px;
    margin-left:5px;
    line-height: 1;
    // padding-top: 2px;
    i{
      color: var(--sdp-xxbt2);
      margin-left: 0;
      font-size: 18px;
    }
  }

</style>
<style lang="scss" scoped>
$FontFamily: var(--chioce-font-family);
$FontWeight: var(--chioce-font-weight);
$FontStyle: var(--chioce-font-style);
$FontColor: var(--chioce-color);
$TextDecoration: var(--chioce-text-decoration);
$DateDimensionFamily: var(--date-dimension-font-family);
$DateDimensionWeight: var(--date-dimension-font-weight);
$DateDimensionStyle: var(--date-dimension-font-style);
$DateDimensionColor: var(--date-dimension-color);
$DateDimensionDecoration: var(--date-dimension-text-decoration);
.indicator-normal{
  color: var(--chioce-color) !important;
  font-family: var(--chioce-font-family) !important;
  font-weight: var(--chioce-font-weight) !important;
  font-style: var(--chioce-font-style) !important;
  text-decoration-line: var(--chioce-text-decoration) !important;
}
.indicator-selected{
  color: var(--chioce-active-color) !important;
  font-family: var(--chioce-active-font-family) !important;
  font-weight: var(--chioce-active-font-weight) !important;
  font-style: var(--chioce-active-font-style) !important;
  text-decoration-line: var(--chioce-active-text-decoration) !important;

  // background: var(--chioce-active-background-color) !important;
  // background: var(--chioce-active-button-color) !important;
}
/deep/ .cube-scroll-nav-bar-item{
  @extend .indicator-normal;
}
/deep/ .sdp-params-theme-element-top-background {
  background: transparent !important;
  .el-input .el-input__inner {
    background: transparent !important;
    font-family: $DateDimensionFamily !important;
    font-weight: $DateDimensionWeight !important;
    font-style: $DateDimensionStyle !important;
    color: $DateDimensionColor !important;
    text-decoration-line: $DateDimensionDecoration !important;
  }
}
.element-chart {
  .mobile-indicator {
      width: 100%;
      border-radius: 3px;
      height: 28px;
      // right: 0;
      // left: 0;
      cursor: pointer;
      text-align: center;
      border: 1px solid $color-ZBXZK;
      background: transparent;
      line-height: 26px;
      font-size: 14px;
      margin-bottom: 12px;
      box-shadow: none;
      @extend .indicator-selected;
      > i {
        line-height: 26px;
        float: right;
        margin-right: 10px;
        color: $color-ZBXXKXLJT;
      }
      .el-input.is-focus .el-input__inner {
        border-color: $color-main-preview!important;
      }
      .el-input__inner:focus {
        border-color: $color-main-preview!important;
      }
    }
  .chart-choice-tabbar{
    &:not(.indicator-underline) /deep/ {
      .cube-scroll-nav-bar.cube-scroll-nav-bar_horizontal {
        // 以前的默认色
        // background-color: $color-KBXXKDS;
        // 用户设置的颜色
        background-color: var(--chioce-active-background-color) !important;
        border-radius: 8px;

      }
    }
    /deep/ .cube-scroll-nav-bar-item_active {
        border-radius: 8px;
        // 以前的默认色
        // background-color: $color-KBXXK;
        box-shadow: 0 1px 2px 0 rgba(0,0,0,0.15);
        background: var(--chioce-active-button-color) !important;
        @extend .indicator-selected;
        > span{
          @extend .indicator-selected;
        }
      }
    &.indicator-underline{
      .cube-scroll-nav-bar.cube-scroll-nav-bar_horizontal{
        background: transparent;
      }
      /deep/ .cube-scroll-nav-bar-item_active{
        box-shadow: none;
        position: relative;
        border-radius: 0;
        background: transparent!important;
        &::after{
          height: 2px;
          content: "";
          position: absolute;
          width: 28px;
          // 以前的默认色
          // background-color: currentColor;
          // 用户设置的颜色-下划线
          background-color: var(--chioce-active-under-line-color) !important;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
  .sdp-drill-bread {
    height: 24px;
    line-height: 24px;
    .drill-bread-ul {
      .drill-bread-item {
        height: 24px;
        line-height: 24px;
        display: inline-block;
        cursor: pointer;
        color: var(--sdp-color-KBDBTBGL);

        &:nth-child(n+2)::before {
          content: '>';
          margin: 0 4px;
        }

        &:last-child {
          color: var(--sdp-zs);
        }
      }
    }
  }
}

.dot{
  margin-right: 6px;
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 10px;
  vertical-align: middle;
}

.simple-grid-margin-top{
  margin-top: 12px;
}
</style>
