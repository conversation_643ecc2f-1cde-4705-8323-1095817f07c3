import { <PERSON><PERSON><PERSON>, getData<PERSON><PERSON><PERSON><PERSON>ields, setDataFiltersFields } from 'packages/base/board/displayPanel/datasetReplace'
import { CHART_ALIAS_TYPE } from '../constant'
import { getDataSetDIC, getUsedFieldFromExp, getOldDataSetIdDIC, getOldFieldDIC, setExpValue, getFilterDataSetAndField, replaceFilterDataSetAndField } from 'packages/base/board/displayPanel/supernatant/boardElements/elementTable/dataset'
import { DatasetConfig } from '../../../../datasetReplace/datasetReplace'
import {
  getTipSetListUsedField,
  getTipSetUsedField,
} from '../../../chartSet/functions/modules/tipSetting/dataset'

export function compatibilityDatasetField(element) {
  // todo: 地图方案
  // const { chartUserConfig, chioceTab = [], drillSettings = {} } = element.content

  // let datasetId = drillSettings.dataSetId
  // const commonParams = {
  //   datasetId,
  // }

  // if (chioceTab?.length) {
  //   chioceTab.forEach(c => {
  //     console.log(11111, element.id, c.id)
  //     replaceDatasetFieldId.call(this, c.saveObj.chartUserConfig, { ...commonParams, content: c.saveObj })
  //   })
  // }
  // console.log(2222, element.id)
  // replaceDatasetFieldId.call(this, chartUserConfig, { ...commonParams, content: element.content })
}


// 钻取样式数据集替换
export function replaceDrillStyleDataSetAndField(element: any, replaceRule: any) {
  if (!element) return
  if (!element?.content?.drillStyle) return

  const drillStyle = element?.content?.drillStyle

  replaceRule.fieldList.forEach(item => {
    if (drillStyle?.[item.oldField.parentId]?.children) {
      drillStyle[item.oldField.parentId].children = Object.fromEntries(Object.entries(drillStyle[item.oldField.parentId].children).map(([key, value]: [any, any]) => {
        if (value.children) {
          Object.entries(value.children).forEach(([childKey, childValue]: [any, any]) => {
            if (item.oldField.labeName === childKey) {
              value.children[item.newField.labeName] = childValue
              delete value.children[item.oldField.labeName]
            }
          })
        }
        return [key, value]
      }))
    }
  })

  replaceRule.datasetList.forEach(item => {
    if (drillStyle?.[item.oldId]) {
      drillStyle[item.newId] = drillStyle[item.oldId]
      delete drillStyle[item.oldId]
    }
  })
}

// 替换
export function datasetFieldReplace(element, replaceRule) {
  const { mapSchemeSetting = {}, chioceTab = [], ...otherContent } = element.content
  const { datasetList = [] } = replaceRule
  const oldDataSetIdDIC = getOldDataSetIdDIC(replaceRule)
  const oldFieldDIC = getOldFieldDIC(replaceRule)
  const _replaceRule = { ...replaceRule, oldDataSetIdDIC, oldFieldDIC, oldDatasetId: datasetList[0].oldId }

  if (chioceTab?.length) {
    chioceTab.forEach((c, i) => {
      if (isUseThisDataset(c.saveObj, datasetList[0].oldId)) {
        replaceDatasetFieldIntoContent.call(this, c.saveObj, _replaceRule, ['chioceTab', i], element)
      }
      if (c.saveObj.mapSchemeSetting?.schemeList?.length) {
        c.saveObj.mapSchemeSetting.schemeList.forEach((s, si) => {
          if (isUseThisDataset(s.saveObj, datasetList[0].oldId)) {
            replaceDatasetFieldIntoContent.call(this, s.saveObj, _replaceRule, ['chioceTab', i, 'mapSchemeSetting', si], element)
          }
        })
      }

      // 处理指标交互设置中的数据集
      ;(c?.saveObj?.interactionOptions || []).forEach(interactionOption => {
        if (interactionOption?.associElementsObj) {
          interactionOption.associElementsObj = Object.fromEntries(
            (
              Object.entries(interactionOption.associElementsObj) || {})
                .map(([id, associElementsArr]) => {
                  (associElementsArr || []).forEach(associElementsObj => {
                    if (associElementsObj.seletedDataSetId) {
                      const newId = replaceRule.datasetList.find(
                        item => item.oldId === associElementsObj.seletedDataSetId
                      ).newId

                      if (newId) {
                        associElementsObj.seletedDataSetId = newId
                        associElementsObj.seletedDataSetName
                          = replaceRule?.datasetData?.[newId]?.labeName || associElementsObj.seletedDataSetName
                      }
                    }
                  })

                  return [id, associElementsArr]
                }
            )
          )
        }
      })
    })
  }

  // 替换钻取样式
  replaceDrillStyleDataSetAndField(element, replaceRule)

  if (isUseThisDataset(otherContent, datasetList[0].oldId)) {
    replaceDatasetFieldIntoContent.call(this, otherContent, _replaceRule, [], element)
    Object.assign(element.content, otherContent)
  }
  if (mapSchemeSetting?.schemeList?.length) {
    mapSchemeSetting.schemeList.forEach((s, si) => {
      if (isUseThisDataset(s.saveObj, datasetList[0].oldId)) {
        replaceDatasetFieldIntoContent.call(this, s.saveObj, _replaceRule, ['mapSchemeSetting', si], element)
      }
    })
  }
  const { wordCloudSetting = {} } = element.content.chartUserConfig
  // 词云图
  if (wordCloudSetting?.colorSeriesField) {
    const dataSetId = wordCloudSetting.colorSeriesField.dataSetId
    const newId = _replaceRule.oldDataSetIdDIC[dataSetId]
    if (newId) {
      wordCloudSetting.colorSeriesField.dataSetId = newId
    }
  }
  replaceFilterDataSetAndField(element, replaceRule)
}
function replaceDatasetFieldIntoContent(content, replaceRule, keyPath: (number|string)[] = [], element) {
  const { dimensionList = [], extendDimensionList = [], metricsContainer = {}, warnLineSettingList = [], calendarSettings = {}, gaugeTarget = {}, contrastList = [], longitudeList = [], latitudeList = [], bandwidthData = {}, hoverDimensionList = [], hoverMetricList = [], xAuxiliaryLineData = [], yAuxiliaryLineData = [] } = content.chartUserConfig
  const exceptKey: string[] = ['dimensionList', 'metricsContainer', 'extendDimensionList', 'gaugeTarget', 'contrastList', 'longitudeList', 'latitudeList', 'bandwidthData', 'calendarSettings', 'hoverDimensionList', 'hoverMetricList', 'dataset', 'superLinkOptions']
  content.drillSettings.filters = setDataFiltersFields(content.drillSettings.filters || [], replaceRule)

  // 数据集替换掉之后，清掉指定维度值指标预警
  delete content.chartUserConfig.dimensionValueIndicatorWarningList
  delete content.chartSettings.extendDimension
  if (content.associatedData?.referenceDatasetId?.includes(replaceRule.datasetList[0].oldId)) {
    const { associatedDataset = {}, dataSetJoinsColumns = [], } = content.associatedData
    associatedDataset.children?.forEach(c => {
      c.webFieldFrom = 'associationDataset'
    })
    dataSetJoinsColumns.forEach(c => {
      c.webFieldFrom = 'associationDataset'
    })
  }
  setNewFieldIntoStraight(content, replaceRule, exceptKey)
  // 超链接-要放在维度的前面
  if (content.superLinkOptions?.length) {
    content.superLinkOptions.forEach(s => {
      s.labelBoard.dataSetId && (s.labelBoard.dataSetId = replaceRule.oldDataSetIdDIC[s.labelBoard.dataSetId] || s.labelBoard.dataSetId)
      ;(s.labelBoard.chartDimension || []).forEach(d => {
        const superFiledIndex = s.parameterField.findIndex(p => p === d.label)
        // 处理有指标选择器的情况
        let _dimensionList = dimensionList
        const { chioceTab } = element.content
        if (chioceTab?.length) {
          const tab = chioceTab.find(ch => s.labelBoard.site.indexOf(ch.id) > -1)
          if (tab) {
            _dimensionList = tab.saveObj?.chartUserConfig?.dimensionList || []
          }
        }
        const dimensionItem = _dimensionList.find(m => m.keyName === d.keyName) || {}
        setNewField({ ...d, labeName: dimensionItem.labeName }, replaceRule, (newField) => {
          d.alias = dimensionItem.alias || newField.labeName
          d.dataSetId = newField.parentId
          d.label = newField.labeName
          s.parameterField[superFiledIndex] && (s.parameterField[superFiledIndex] = newField.labeName)
        }, false)
      })
    })
  }
  // 维度
  dimensionList.forEach((d, i) => {
    setNewField(d, replaceRule, (newField) => {
      // 自定义维度
      if (d.customDimension?.expression) {
        setExpValue(replaceRule.oldDataSetIdDIC, replaceRule.oldFieldDIC, replaceRule.oldDatasetId, d.customDimension.expression, (id, exp) => {
          d.customDimension.expression = exp
        })
      }
      if (d.customDimension?.datasetId) {
        d.customDimension.datasetId = newField.parentId
      }
      // 钻取-必须放在最前面，否则会找不到维度
      const drillList = (content.drillList || []).filter(r => r.drillDimensionKey === d.keyName || r.keyName === d.labeName)
      drillList.forEach(r => {
        r.keyName = newField.labeName
        r.drillDimensionKey = d.keyName
      })
      const layersField = content.drillSettings.layers[0].dimension[i] || {}
      setNewAlias(layersField, d, newField, 'alias')
      layersField.parentId = newField.parentId
      if (layersField.exp && d.customDimension?.expression) layersField.exp = d.customDimension.expression
    }, true)
    // 自定义排序
    if (d.order === 'customSort' && d.orderList?.length) {
      setNewField(d.orderList[0], replaceRule, (newField) => {
        d.orderList[0].alias = newField.labeName
        const layersField = content.drillSettings.layers[0].dimension[i].orderList?.[0] || {}
        setNewAlias(layersField, {}, newField, 'alias')
      }, true)
    }

  })
  // 扩展维度
  extendDimensionList.forEach((d, i) => {
    setNewField(d, replaceRule, (newField) => {
      const layersField = content.drillSettings.layers[0].extendDimension[i] || {}
      setNewAlias(layersField, d, newField, 'alias')
    }, true)
    // 自定义排序
    if (d.order === 'customSort' && d.orderList?.length) {
      setNewField(d.orderList[0], replaceRule, (newField) => {
        d.orderList[0].alias = newField.labeName
        const layersField = content.drillSettings.layers[0].extendDimension[i].orderList[0] || {}
        setNewAlias(layersField, {}, newField, 'alias')
      }, true)
    }
  })
  ;(metricsContainer.default || []).forEach((d, i) => {
    const { x = [], y = [], size = [], line = [], histogram = [] } = metricsContainer
    const otherMetric = [...x, ...y, ...size, ...line, ...histogram]
    const oldAlias = d.alias || d.labeName
    setNewField(d, replaceRule, (newField) => {
      const layersField = content.drillSettings.layers[0].metrics[i] || {}
      setNewAlias(layersField, d, newField, 'alias')
      warnLineSettingList.forEach(w => {
        if (w.metricKeyName === d.keyName || (w.metric === (d.alias || d.labeName))) {
          setNewAlias(w, d, newField, 'metric')
          w.metricKeyName = d.keyName
        }
      })
      const auxiline = [...xAuxiliaryLineData, ...yAuxiliaryLineData]
      auxiline.forEach(a => {
        if (a.metricValue === oldAlias) setNewAlias(a, d, newField, 'metricValue')
      })
    }, true)
    if (otherMetric.length) {
      const otherField = otherMetric.filter(o => o.keyName === d.keyName)
      otherField.forEach(o => Object.assign(o, d))
    }
  })
  // 目标值
  if (gaugeTarget.defaults && gaugeTarget.type === 'aggType') {
    setNewField(gaugeTarget.defaults, replaceRule, (newField) => {
      const layersField = content.drillSettings.layers[0].gaugeTarget || {}
      setNewAlias(layersField, gaugeTarget.defaults, newField, 'alias')
    }, true)
  }
  if (gaugeTarget.defaultTagdetList && gaugeTarget.type === 'aggType') {
    setNewField(gaugeTarget.defaultTagdetList, replaceRule)
  }
  // 对比值
  if (contrastList?.length) {
    contrastList.forEach(d => {
      setNewField(d, replaceRule, (newField) => {
        const layersField = content.drillSettings.layers[0].metrics.find(m => m.keyName === d.keyName)
        setNewAlias(layersField, d, newField, 'alias')
      }, true)
    })
  }
  // 经纬度
  const longitudeAndLatitudeList = [...longitudeList, ...latitudeList]
  if (longitudeAndLatitudeList?.length) {
    longitudeAndLatitudeList.forEach(d => {
      setNewField(d, replaceRule, (newField) => {
        const layersField = (content.drillSettings.layers[0].latiAndLongitude || []).find(m => m.keyName === d.keyName)
        setNewAlias(layersField, d, newField, 'alias')
      }, true)
    })
  }
  // 带宽
  const bandWidthList = [
    ...(bandwidthData.minBandwidthList || []),
    ...(bandwidthData.maxBandwidthList || []),
    ...(bandwidthData.bandwidthList || []),
  ]
  // 带宽-排名字段
  if (bandwidthData.rankSetConfig?.field?.labeName) bandWidthList.push(bandwidthData.rankSetConfig.field)
  if (bandWidthList.length) {
    const layersFieldList = [content.drillSettings.layers[0].bandwidthReport, ...content.drillSettings.layers[0].metrics]
    bandWidthList.forEach(d => {
      setNewField(d, replaceRule, (newField) => {
        const layersField = layersFieldList.find(m => m && m.keyName === d.keyName)
        setNewAlias(layersField, d, newField, 'alias')
      }, true)
    })
  }
  // 日历图-指标计算
  if (calendarSettings?.factorList?.length) {
    calendarSettings.factorList.forEach(d => {
      setNewField(d, replaceRule, (newField) => {
        const layersField = (content.drillSettings.layers[0].factors || []).find(m => m.keyName === d.keyName)
        setNewAlias(layersField, d, newField, 'alias')
      }, true)
    })
  }
  // 地图-省份字段\提示-维度、度量
  const mapOtherList = [...hoverDimensionList, ...hoverMetricList]
  if (mapOtherList.length) {
    const layersFieldList = [...(content.drillSettings.layers[0].hoverDimension || []), ...(content.drillSettings.layers[0].hoverMetrics || [])]
    mapOtherList.forEach(d => {
      setNewField(d, replaceRule, (newField) => {
        const layersField = layersFieldList.find(m => m && m.keyName === d.keyName)
        setNewAlias(layersField, d, newField, 'alias')
      }, true)
    })
  }

  if (content.associatedData?.referenceDatasetId?.includes(replaceRule.datasetList[0].oldId)) {
    const { dataSetJoins = {}, referenceDatasetId = [], mainDatasetId = '' } = content.associatedData
    const oldId = replaceRule.datasetList[0].oldId
    let newJoins: string[] = []
    dataSetJoins[oldId]?.forEach(d => {
      const { oldField, newField = {} } = replaceRule.fieldList.find(f => f.oldField.labeName === d) || {}
      newJoins.push(newField.labeName)
    })
    dataSetJoins[replaceRule.datasetList[0].newId] = newJoins
    delete dataSetJoins[oldId]
    content.associatedData.referenceDatasetId = referenceDatasetId.map(d => replaceRule.oldDataSetIdDIC[d] || d)
    content.associatedData.mainDatasetId = replaceRule.oldDataSetIdDIC[mainDatasetId] || mainDatasetId
  }
  if (content.dataset?.length) {
    content.dataset = content.dataset.map(d => {
      const newId = replaceRule.oldDataSetIdDIC[d.id] || d.id
      if (d.dateType === 'associateDataset') {
        if (content.associatedData?.dataSetJoinsColumns?.length) {
          d.children = content.associatedData?.dataSetJoinsColumns
        } else {
          setNewFieldIntoStraight(d.children, replaceRule)
        }
      }
      return replaceRule.datasetData[newId] || d
    })
  }
}
function setNewFieldIntoStraight(obj, replaceRule, exceptKey: (number|string)[] = [], keyPath: (number|string)[] = []) {
  if (Array.isArray(obj)) {
    obj.forEach((o, i) => {
      setNewFieldIntoStraight(o, replaceRule, exceptKey, keyPath.concat([i]))
    })
  } else if (!!obj && typeof obj === 'object') {
    if (!keyPath.includes('drillSettings') && keyPath.find(k => exceptKey.includes(k))) return
    // BUGFIX:82876
    let _oldColumnName = obj.columnName
    setNewField(obj, replaceRule)
    if (keyPath.find(k => ['xAuxiliaryLineData', 'yAuxiliaryLineData'].includes(k)) && !obj.auxiliaryLineType && !obj.lineType) {
      // 自定义辅助线里面的字段，简单的判断一下，不是很严谨
      obj.metricValue = obj.labeName
    }
    // 关联数据集字段 layers
    if (keyPath.find(k => k === 'layers') && obj.webFieldFrom === 'associationDataset') {
      obj.columnName = _oldColumnName
    }
    Object.keys(obj).forEach(k => {
      setNewFieldIntoStraight(obj[k], replaceRule, exceptKey, keyPath.concat([k]))
    })
  }
}
function setNewAlias(obj, userObj, newField, key = 'alias') {
  if (!userObj.alias && obj?.[key] && !userObj.webFieldType) {
    obj[key] = newField.labeName
  }
}
function setNewField(obj, replaceRule, callback: any = null, isContinue = false) {
  const labeName = obj.columnName || obj.labeName || obj.field || obj.colorSeriesProp
  const datasetId = obj.parentId || obj.datasetId || obj.dataSetId || replaceRule.oldDatasetId
  const newDatasetId = replaceRule.oldDataSetIdDIC[datasetId]
  if (!newDatasetId || (datasetId === newDatasetId)) return
  if (obj.datasetId) obj.datasetId = newDatasetId
  // 兼容有的字段没有labeName 但是又有parentId的情况
  // 因为下面有labeName不存在就return的判断
  if (!labeName && obj.parentId) obj.parentId = newDatasetId
  // 数据过滤叠加关联数据集
  if (obj.dataSetId && (!obj.filterValueType || replaceRule.oldDataSetIdDIC[obj.dataSetId])) obj.dataSetId = newDatasetId
  // 自定义字段
  if (obj.hasOwnProperty('customFieldName') && obj.parentId) obj.parentId = newDatasetId
  if (!labeName) return // 这个if不能和前面那个if合并
  const fieldList = replaceRule.fieldList || []
  const { oldField, newField = {} } = fieldList.find(f => f.oldField.sdp_only_field_id === obj.sdp_only_field_id || (f.oldField.parentId === datasetId && f.oldField.labeName === labeName)) || {}

  if (callback) {
    callback(newField)
    if (!isContinue) return
  }
  if (!callback || isContinue) {
    if (!newField.labeName) return
    if (obj.columnName) obj.columnName = newField.labeName
    // 关联数据集的名称存的是labeName
    if (obj.labeName && obj.dateType !== 'associateDataset' && !obj.webFieldType) obj.labeName = newField.labeName
    if (obj.webFieldFrom === 'associationDataset' || obj.columnNameOrId) {
      obj.labeName = obj.alias || newField.labeName
      obj.columnNameOrId = newField.labeName
    }
    if (obj.parentId) obj.parentId = newDatasetId
    if (obj.dimensionDataSetId) obj.dimensionDataSetId = newDatasetId
    if (obj.field && typeof obj.field === 'string') obj.field = newField.labeName
    if (obj.sdp_only_field_id) obj.sdp_only_field_id = newField.sdp_only_field_id
    if (obj.colorSeriesProp) obj.colorSeriesProp = newField.labeName
    if (obj.colorFieldKey) obj.colorFieldKey = newField.sdp_only_field_id
    if (obj.otherMetricValue) obj.otherMetricValue = newField.labeName
    if (obj.fieldColumnName) obj.fieldColumnName = newField.labeName
    if (obj.exp && (['expression', 'cross_expression'].find(e => e === obj.aggType || e === obj.type) || obj.customerExprDim)) {
      setExpValue(replaceRule.oldDataSetIdDIC, replaceRule.oldFieldDIC, datasetId, obj.exp, (id, exp) => {
        obj.exp = exp
        if (obj.displayExpression) obj.displayExpression = exp
        if (obj.webFieldType === 'customComputed') {
          obj.labeName = obj.customFieldName
          obj.columnName = obj.exp
          obj.parentId = newDatasetId
        }
      })
    }
    if (obj.metricGroupInfo?.parentId) {
      obj.metricGroupInfo.parentId = newDatasetId
    }
  } else {
    console.log(fieldList, obj, datasetId, labeName)
  }
}
// 获取要替换的字段
export function getUsedDatasetField(element, datasetList) {
  const { mapSchemeSetting = {}, chioceTab = [], ...otherContent } = element.content

  let result = []
  const datasetFieldList = getDataSetDIC(datasetList)[datasetList[0].id]

  let _replaceRule = { oldDatasetId: datasetList[0].id, datasetFieldList, datasetList }
  if (chioceTab?.length) {
    chioceTab.forEach(c => {
      if (isUseThisDataset(c.saveObj, datasetList[0].id)) {
        getUsedFieldList.call(this, c.saveObj, _replaceRule, result)
      }
      if (c.saveObj.mapSchemeSetting?.schemeList?.length) {
        c.saveObj.mapSchemeSetting.schemeList.forEach(s => {
          if (isUseThisDataset(s.saveObj, datasetList[0].id)) {
            getUsedFieldList.call(this, s.saveObj, _replaceRule, result)
          }
        })
      }
    })
  }
  if (isUseThisDataset(otherContent, datasetList[0].id)) {
    getUsedFieldList.call(this, element.content, _replaceRule, result)
  }
  if (mapSchemeSetting?.schemeList?.length) {
    mapSchemeSetting.schemeList.forEach(s => {
      if (isUseThisDataset(s.saveObj, datasetList[0].id)) {
        getUsedFieldList.call(this, s.saveObj, _replaceRule, result)
      }
    })
  }
  let list = getFilterDataSetAndField(element)
  list.forEach(item => {
    const { dataSetId, columnName } = item
    if (!result.find(e => e?.labeName === columnName)) {
      const target = OnlyField.useDatasetIdAndFieldNameGetFieldData(dataSetId, columnName)
      target && result.push(target)
    }
  })
  const tipSetList = getTipSetListUsedField(element, datasetList)
  tipSetList.forEach(item => {
    const { dataSetId, columnName } = item
    if (!result.find(e => e?.labeName === columnName)) {
      const target = OnlyField.useDatasetIdAndFieldNameGetFieldData(dataSetId, columnName)
      target && result.push(target)
    }
  })
  return { [datasetList[0].id]: result }
}
function isUseThisDataset(contentObj, setId) {
  const { referenceDatasetId = [], associatedDataset } = contentObj.associatedData || {}
  const isAcc = associatedDataset && !!Object.keys(associatedDataset).length
  const interactionOptions = Array.isArray(contentObj.interactionOptions) ? contentObj.interactionOptions : []
  const dataSetId = contentObj?.drillSettings?.dataSetId || ''
  const datasetList = (isAcc ? referenceDatasetId : []).concat([dataSetId])
  interactionOptions.forEach(d => {
    datasetList.push(d.dataSetId)
    ;(d.associElements || [])?.forEach(a => {
      datasetList.push(a.dataSetId)
    })
  })

  return datasetList.includes(setId)
}
function getUsedFieldList(content, _replaceRule, result: any[] = []) {
  const { dimensionList = [], extendDimensionList = [], metricsContainer = {}, warnLineSettingList = [], metricLabelDisplay = [], wordCloudSetting = {}, calendarSettings = {}, gaugeTarget = {}, contrastList = [], longitudeList = [], latitudeList = [], dimensionDestinationList = [], bandwidthData = {}, xAuxiliaryLineData = [], yAuxiliaryLineData = [], provinceField = {}, hoverDimensionList = [], hoverMetricList = [] } = content.chartUserConfig
  // 交互
  if (content.interactionOptions?.length) {
    content.interactionOptions.forEach(d => {
      repeatPush(result, d, _replaceRule, 'columnName')
      ;(d.associElements || [])?.forEach(a => {
        repeatPush(result, a, _replaceRule, 'columnName')
      })
    })
  }
  // 关联数据集
  if (content.associatedData?.referenceDatasetId?.includes(_replaceRule.oldDatasetId)) {
    const { dataSetJoinsColumns = [], dataSetJoins = {} } = content.associatedData
    dataSetJoinsColumns.forEach(d => {
      repeatPush(result, d, _replaceRule, 'columnName')
    })
    dataSetJoins[_replaceRule.oldDatasetId]?.forEach(d => {
      repeatPush(result, { labeName: d, parentId: _replaceRule.oldDatasetId }, _replaceRule, 'labeName')
    })
  }
  // 数据过滤
  const filterFields = getDataFiltersFields({
    ids: [_replaceRule.datasetList[0].id],
    filters: content.drillSettings?.filters || [],
    allFields: _replaceRule.datasetList[0].children,
  })
  filterFields.forEach(f => repeatPush(result, f, _replaceRule, 'labeName'))

  // 维度
  dimensionList.forEach(d => {
    repeatPush(result, d, _replaceRule, 'labeName')
    if (d.order === 'customSort' && d.orderList?.length) {
      // 自定义排序
      repeatPush(result, d.orderList[0], _replaceRule, 'columnName')
    }
    if (d.customDimension?.expression) {
      // 自定义维度
      const customFields = getUsedFieldFromExp(d.customDimension.expression, _replaceRule) || []
      customFields.forEach(c => repeatPush(result, c, _replaceRule, 'labeName'))
    }
    const drillList = (content.drillList || []).filter(r => r.drillDimensionKey === d.keyName || r.keyName === d.labeName)
    drillList.forEach(r => {
      repeatPush(result, r, _replaceRule, 'columnName')
      if (r.orderList?.length) {
        // 自定义排序
        repeatPush(result, r.orderList[0], _replaceRule, 'columnName')
      }
    })
  })
  // 扩展维度
  extendDimensionList.forEach(d => {
    repeatPush(result, d, _replaceRule, 'labeName')
    if (d.order === 'customSort' && d.orderList?.length) {
      // 自定义排序
      repeatPush(result, d.orderList[0], _replaceRule, 'columnName')
    }
  })
  // 度量
  metricsContainer.default?.forEach(d => {
    repeatPush(result, d, _replaceRule, 'labeName', true)
    const customMetricList = metricLabelDisplay.find(m => m.keyName === d.keyName)?.customMetricList || []
    customMetricList.forEach(c => {
      repeatPush(result, c.customMetric, _replaceRule, 'labeName', true)
    })
    // 预警
    const warningField = (warnLineSettingList || []).filter(w => w.fieldType === 'other' && (w.metricKeyName === d.keyName || w.metric === (d.alias || d.labeName)))
    warningField.forEach(w => {
      repeatPush(result, w, _replaceRule, 'labeName', true)
    })
  })
  // 目标值
  if (gaugeTarget.defaults && gaugeTarget.type === 'aggType') {
    repeatPush(result, gaugeTarget.defaults, _replaceRule, 'columnName', true)
  }
  // 对比值
  if (contrastList?.length) {
    contrastList.forEach(d => {
      repeatPush(result, d, _replaceRule, 'labeName', true)
    })
  }
  // 经纬度
  const longitudeAndLatitudeList = [...longitudeList, ...latitudeList]
  if (longitudeAndLatitudeList.length) {
    longitudeAndLatitudeList.forEach(d => {
      repeatPush(result, d, _replaceRule, 'labeName')
    })
  }
  // 目的地
  dimensionDestinationList.forEach(d => {
    repeatPush(result, d, _replaceRule, 'labeName')
  })
  // 带宽
  const bandWidthList = [
    ...(bandwidthData.minBandwidthList || []),
    ...(bandwidthData.maxBandwidthList || []),
    ...(bandwidthData.bandwidthList || []),
  ]
  // 带宽-排名字段
  if (bandwidthData.rankSetConfig?.field?.labeName) bandWidthList.push(bandwidthData.rankSetConfig.field)
  bandWidthList.forEach(d => {
    repeatPush(result, d, _replaceRule, 'labeName', true)
  })
  // 词云图
  if (wordCloudSetting?.colorFieldKey || wordCloudSetting?.colorSeriesField) {
    if (wordCloudSetting?.colorFieldKey) {
      repeatPush(result, { ...wordCloudSetting, parentId: content.drillSettings?.dataSetId }, _replaceRule, 'colorSeriesProp')
    }
  }
  // 日历图-指标计算
  if (calendarSettings?.factorList?.length) {
    calendarSettings.factorList.forEach(d => {
      repeatPush(result, d, _replaceRule, 'labeName', true)
    })
  }
  // 辅助线
  const auxiliaryLineList = [...xAuxiliaryLineData, ...yAuxiliaryLineData]
  auxiliaryLineList.forEach(a => {
    a.labeName && repeatPush(result, a, _replaceRule, 'labeName', true)
    if (a.filed1?.labeName) {
      repeatPush(result, a.filed1, _replaceRule, 'labeName', true)
    }
    if (a.filed2?.labeName) {
      repeatPush(result, a.filed2, _replaceRule, 'labeName', true)
    }
  })
  // 地图-省份字段\提示-维度、度量
  const mapOtherList = [...hoverDimensionList, ...hoverMetricList]
  if (provinceField?.labeName) mapOtherList.unshift(provinceField)
  mapOtherList.forEach(d => {
    repeatPush(result, d, _replaceRule, 'labeName', true)
  })
}
function repeatPush(result, field, _replaceRule, key = 'labeName', needExp = false) {
  const datasetId = field.datasetId || field.parentId || field.dataSetId
  if (datasetId && datasetId !== _replaceRule.oldDatasetId) return
  const datasetField = _replaceRule.datasetFieldList.find(f => f.labeName === field[key])
  const isDatasetField = datasetField && !result.find(r => r.labeName === datasetField.labeName)

  if (needExp && field.exp && (['expression', 'cross_expression'].find(e => e === field.aggType || e === field.type))) {
    console.log('field.exp', field.exp)
    if (DatasetConfig.isExcludeExpField && isDatasetField) {
      result.push(datasetField)
    }
    // 自定义计算
    const customFields = getUsedFieldFromExp(field.exp, _replaceRule.datasetFieldList) || []
    customFields.forEach(c => repeatPush(result, c, _replaceRule, 'labeName'))
  } else if (isDatasetField) {
    result.push(datasetField)
  }
}