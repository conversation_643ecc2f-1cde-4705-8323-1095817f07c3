<template>
  <div :class="{'sdp-element-table': true, mobile: isMobile, 'innerContainer': options.innerContainer}">
    <ElementSlot :visible="isMobile" right="0px" top="0px">
      <slot></slot>
    </ElementSlot>
    <table-preview
      v-show="elVisibility && !loading"
      ref="grid"
      :element="element"
      :api="api"
      :tenant-id="tenantId"
      :application="application"
      :styles="style"
      :board-mode="boardMode"
      :original-table-config="element.content.originalTable"
      :table-default-config="element.content.tableDefaultConfig"
      :options="options"
      v-bind="$attrs"
      @render-data="renderData"
      @set-loading="changeLoading"
      @updateElementPaddingRender="updateElementPaddingRender"
      @eventBus="eventBus"
    />
    <div v-show="loading" :style="containerStyle" class="sdp-placeholder-element loading">
      <loading :isLoading="loading"></loading>
    </div>
    <!--占位图-->
    <div v-show="!elVisibility" :style="{height: containerStyle.height}" class="sdp-placeholder-element placeholder" >
      <div>
        <span v-show="!commonData.isPreview">{{$t('sdp.views.table')}}</span>
        <waiting :isLoading="commonData.isPreview && isRenderInteraction"/>
      </div>
    </div>
  </div>
</template>
<script>
// import GirdSetting from './setting'
import { TABLE_STATUS } from 'packages/base/gridPreview/common/constant'
import TablePreview from 'packages/base/gridPreview/index.vue'
import Loading from 'packages/base/common/loading/loading'
import waiting from 'packages/base/common/waiting'
import tableMixin from './tableMixin'
import ElementSlot from '../components/elementSlot.vue'

import { PREVIEW_STATUS, RUN_ELEMENT_TYPE } from 'packages/assets/constant'
// import EventData from 'packages/assets/EventData'
import eventBus from 'packages/assets/eventBus'
import { initFilterSorterHandler } from '../../filterDialog/utils'
import elementResizeDetectorMaker from 'element-resize-detector'

export default {
  inject: {
    utils: { default: () => ({}) },
    commonData: { default: () => ({}) },
    isEdit: { default: false }, // 这个注入不能删
    getBoardStatus: { default: () => '' },
    setErrorStatus: {
      default: () => () => false
    },
  },
  mixins: [tableMixin],
  components: {
    ElementSlot,
    TablePreview,
    // GirdSetting,
    Loading,
    waiting,
  },
  props: {
    options: {
      type: Object,
      default: () => ({
        innerContainer: false
      }),
    },
    element: {
      type: Object,
      default: () => ({}),
    },
    headerHeight: {
      type: String,
      default: '0'
    },
  },
  computed: {
    isMobile() {
      return this.utils.isMobile
    },
    api() {
      return this.utils.api
    },
    tenantId() {
      return this.utils.tenantId
    },
    boardMode() {
      return this.commonData.isPreview ? PREVIEW_STATUS.PREVIEW : PREVIEW_STATUS.EDIT
    },
    containerStyle() {
      const { width, height } = this.style
      return {
        height: this.options.innerVerticalContainer ? `${parseInt(height) - 70}px` : '100%',
        width,
      }
    },
    // 表格显示区域，包括分页
    style() {
      return this.elementStyle
    },
    application() {
      return TABLE_STATUS.board
    },
    id() {
      return this.element.id
    },
    isRenderInteraction() {
      return this.$_getProp(this, 'element.elAttr.isRenderInteraction', false)
    }
  },
  data() {
    return {
      loading: false,
      elVisibility: false,
      elementStyle: {
        width: this.element.style.width,
        height: this.element.style.height,
      }
    }
  },
  created() {
    this.setElementLoadState = this.$_debounce(this.setElementLoadState)
    initFilterSorterHandler(this.element, this.getBoardStatus())
  },
  mounted() {
    // 监听元素宽度变化 动画会影响getBoundingClientRect获取的宽高有问题
    this.erd = elementResizeDetectorMaker()
    this.erd.listenTo(this.$el, (element) => {
      // 小程序IOS端不会进入改回调
      this.$nextTick(() => {
        this.calcStyle({
          width: element.offsetWidth,
          height: element.offsetHeight,
        })
      })
    })
    setTimeout(() => {
      this.calcStyle({
        width: this.$el.offsetWidth,
        height: this.$el.offsetHeight,
      })
    }, 300)
  },
  beforeDestroy() {
    if (this.$el) {
      this.erd.uninstall(this.$el)
    }
  },
  methods: {
    eventBus,
    renderData() {
      this.elVisibility = true
    },
    updateElementPaddingRender() {
      this.calcStyle({
        width: this.$el.offsetWidth,
        height: this.$el.offsetHeight,
      })
    },
    calcStyle({ height, width }) {
      let dw = 0
      let dh = 0
      if (!this.isMobile) {
        const { left, right, top, bottom } = this.element.styleConfig.padding
        // dw = this.options.innerContainer ? 0 : (16 + 16)
        dw = this.options.innerContainer ? 0 : (left + right)
        // dh = this.options.innerContainer ? 1 : (16 + 10)
        dh = this.options.innerContainer ? 1 : (top + bottom)
        this.$attrs.isAdvanceContainer && (dh += 6)
      }
      this.elementStyle = {
        width: width - dw + 'px',
        height: this.options.innerVerticalContainer ? 0 : (height - dh) + 'px',
      }
    },
    changeLoading(val) {
      this.loading = val
    },
    againCreateComp(callback) {
      // 当修改表格的 tableDefaultConfig 配置时， 预览组件已经create
      // 因此需要重新生成新的 组件
      this.elVisibility = false
      setTimeout(() => {
        this.elVisibility = true
        callback()
      }, 1000)
    },
    requestAdapter(data) {
      return this.mixin_table_requestAdapter(data)
    },
    responseAdapter(res) {
      this.mixin_table_responseAdapter(res)
    },
    setBoardQuestParams(req) {
      const vm = this.element.vm
      vm && vm.setBoardQuestParams(req)
    },
    setElementLoadState(state) {
      if (this.commonData.isOffApiLoading) return '关闭loading'
      this.$refs.grid && this.$refs.grid.setLoading(state)
    },
    // 对外提供error 提示
    setElementErrorState(error) {
      this.setErrorStatus(error, this.element.id)
    },
    deleteElementData() {
      this.elVisibility = false
    }
  },
}
</script>
<style lang="scss" scoped>
  .sdp-element-table {
    height: 100%;
    /*padding: 16px 16px 10px;*/
    // [data-theme = 'sdp-dark-blue'] & {
    //   background-color: #212D3B;
    // }
    // [data-theme = 'sdp-dark-blue'] &.mobile {
    //   background-color: #1C1C1E;
    // }
    &.innerContainer {
      padding: 0;
    }
    &.mobile {
      padding: 0;
    }
    .sdp-placeholder-element {
      display: flex;
      justify-content: center;
      align-content: center;
      align-items: center;
      height: 100%;
      font-size: 16px;
      color: var(--sdp-srk-bxwzs);
    }
    /deep/ .el-tooltip__popper[data-sdp-el-insert='el-tooltip'],  /deep/ .el-tooltip__popper[data-sdp-el-insert-firefox='el-tooltip']{
      position: absolute !important;
    }
  }
</style>
