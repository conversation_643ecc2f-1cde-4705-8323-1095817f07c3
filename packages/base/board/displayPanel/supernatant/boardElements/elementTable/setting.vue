<template>
  <el-dialog
    :custom-class="`sdp-dialog grid-dialog ${getCurrentThemeClass && getCurrentThemeClass()}`"
    :visible.sync="visible"
    :show-close="false"
    append-to-body
    :close-on-press-escape="false"
    fullscreen
  >
   <template>
     <grid-design
       v-if="visible"
       :api="api"
       :is-by-the-interaction="isByTheInteraction"
       :tenant-id="utils.tenantId"
       :is-screen="utils.isScreen"
       :lang-code="langCode"
       :edit-template="false"
       :element-config="gridConfig"
       :board-dataset-list="datasetList"
       @update="updateGridConfig"
       @close="close"
     />
   </template>
  </el-dialog>
</template>

<script type="text/ecmascript-6">
import GridDesign from 'packages/base/grid/gridDesignForBoard'
import { tableSaveByFilterSorter } from '../../filterDialog/utils'
import { EVENT_BUS } from '../../../constants'

export default {
  name: 'GirdSetting',
  inject: ['utils', 'langCode', 'sdpBus', 'getCurrentThemeClass'],
  provide() {
    return {
      // 暂时解决bug 24423  原因：gridDesignForBoard组件继承后重新provide了一个初始的utils数据导致组件接受数据不对
      utilsTmp: this.utils
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    gridConfig: {
      type: Object,
    },
    datasetList: {
      type: Array,
      default() {
        return []
      }
    },
    elList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      isMounted: false
    }
  },
  components: {
    GridDesign
  },
  computed: {
    api() {
      return this.utils.api
    },
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 是否被交互
    isByTheInteraction() {
      return this.elList.some(item => {
        const interactionOptions = item.content.interactionOptions
        const flag = interactionOptions?.length && interactionOptions[0].associElements.some(eve => eve.id === this.gridConfig.id)
        return flag
      })
    },
  },
  methods: {
    updateGridConfig(dataScreenRes) {
      // 表格originalTable被替换 会出发 gridPreview 组件中 的 watch 监听
      // 合并数据
      const original = this.gridConfig.content
      this.gridConfig.content = Object.assign(original, JSON.parse(dataScreenRes.contentJson))
      // this.gridConfig.content = JSON.parse(dataScreenRes.contentJson)
      delete dataScreenRes.contentJson
      // 兼容
      const { originalTable } = original
      dataScreenRes.originalTable = this.$_deepClone(originalTable)
      dataScreenRes.tableCells = this.$_flatten(originalTable).filter(cell => cell.i)
      // 再ElTools文件中 会监听 dataScreenRes 的变化
      this.gridConfig.content.dataScreenRes = dataScreenRes
      this.$emit('update:gridSetVisible', false)

      tableSaveByFilterSorter(this.gridConfig, this.elList, this)

      this.updateElWarnData()

      this.close()
    },
    updateElWarnData() {
      const elementWarningData = this.gridConfig?.vm?.getElementWarningData() || {}
      this.sdpBus.$emit(EVENT_BUS.CHANGE_ELEMENT_WARNING_DATA, this.gridConfig.id, { elementWarningData })
    },
    close() {
      this.visible = false
    }
  },
}
</script>

<style lang="scss" scoped>
/deep/.grid-dialog{
  & > .el-dialog__header {
    padding: 0 !important;
  }
  & > .el-dialog__body {
    padding: 0 !important;
    height: 100% !important;
  }
}

</style>
