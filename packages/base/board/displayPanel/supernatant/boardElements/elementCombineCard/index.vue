<template>
  <div style="height: 100%;" :id="'combine_card_' + element.id">
    <ElementSlot :visible="isMobile">
      <slot></slot>
    </ElementSlot>
    <!-- 占位文本 -->
    <div class="placeholder-text" v-if="!isRun && !isCombineDialog">
      {{ commonData.isPreview ? '' : $t('sdp.views.combineCard') }}
      <!-- <loading :isLoading="loading" v-if="cardList.length"></loading> -->
    </div>

    <div v-else class="combine-card" ref="combineCard">
      <!-- <loading :isLoading="loading"></loading> -->
      <div v-if="isEmptyCard && !isCombineDialog" class="placeholder-text empty-element-text"><span>NONE</span></div>
      <card-grid
        :style="{'height': morePage ? 'calc(100% - 27px)' : '100%'}"
        v-if="!isEmptyCard && isCombineDialog"
        ref="cardGrid"
        :cardList="cardList"
        isCombineDialog
        isDraggable
        isResizable
        :cardMargin="[5, 5]"
        :el="element"
        :el-list="elList"
        :screenType="screenType"
        :boardInfo="boardInfo"
        @eventBus="eventBus"
        @update-card-list="(data) => $emit('update-card-list', data)"
        @edit="(data) => $emit('edit', data)">
      </card-grid>

      <el-carousel
        v-if="!isEmptyCard && !isCombineDialog"
        :initial-index="0"
        ref="carousel"
        arrow="never"
        :autoplay="false"
        :height="'100%'"
        :style="{'height': morePage ? 'calc(100% - 15px)' : '100%'}">
        <el-carousel-item v-for="(page, index) in choiceTab" :key="index">
          <card-grid
            @eventBus="eventBus"
            @card-mounted="handleCardMounted"
            :cardList="page.saveObj.cardList"
            ref="cardGrid"
            :cardMargin="[5, 5]"
            :el="element"
            :boardInfo="boardInfo"
            :screenType="screenType"
            :el-list="elList">
          </card-grid>
        </el-carousel-item>
      </el-carousel>

      <div class="page-index" v-if="!isEmptyCard && morePage">
        <div class="switch-icon switch-icon-left" :class="{'notVisible': commonData.isPreview || isApp || checkFirstOrLastPage('first')}" @click="slideRight">
          <i class="el-icon-caret-left swtich-page-left"></i>
        </div>
        <div
          v-for="(item, i) in choiceTab"
          :key="i"
          :class="item.id === element.content.activePageId ? 'select-page-index' : '' "
          class="page-point" :style="i === 0 ? {'margin-left': '0px'} : {}">
        </div>
        <div class="switch-icon switch-icon-right" :class="{'notVisible': commonData.isPreview || isApp || checkFirstOrLastPage('last')}" @click="slideLeft">
          <i class="el-icon-caret-right swtich-page-right"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Hammer from 'hammerjs'
// import Loading from 'packages/base/common/loading/loading'
import cardGrid from '../../combineCardDialog/sdpCardGrid'
// import cardContent from '../elementTagNewCard/index'
import bridge from './bridge'
import EventData from 'packages/assets/EventData'
import eventBus from '../../../../../../assets/eventBus'
import { changeThemeType } from '../../../../../../assets/theme/theme-style/utils'
import { THEME_TYPE } from '../../../../../../assets/constant'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import { insetDom } from 'packages/base/common/loading/components/loadingIcon'
import ElementSlot from '../components/elementSlot.vue'

export default {
  name: 'element-combine-card',
  mixins: [datasetMixin],
  inject: {
    commonData: { default: {} },
    themeData: { default: {} },
    utils: { default: {} },
    fullscreenData: { default: {} },
    isApp: { default: false },
    setLoadingStatus: {
      default: () => () => false
    },
    setErrorStatus: {
      default: () => () => false
    },
  },
  props: {
    elList: {
      type: Array,
      default: () => []
    },
    element: {
      type: Object,
    },
    isCombineDialog: {
      type: Boolean,
      default: false,
    },
    // 移动端屏幕展示方式 竖屏-vertical 横屏-landscape
    screenType: {
      type: String,
    },
    boardInfo: {
      type: Object,
      default: () => {}
    },
  },
  components: { cardGrid, ElementSlot },
  data(vm) {
    const pendingCards = new Set(vm.element.content.choiceTab.reduce((list, item) => [...list, ...item.saveObj.cardList.map(card => card.id)], []))
    return {
      pendingCards,
      loading: false,
      isRun: false, // 是否run过，未run显示占位文本
    }
  },
  created() {
    // 将this挂载在element里面
    const vm = this
    Object.defineProperty(this.element, 'vm', {
      enumerable: false,
      configurable: true,
      get() {
        return vm
      },
    })
    // 组合卡片数据兼容
    this.choiceTab.map(item => {
      this.compatibilityData(item.saveObj.cardList)
    })
    this.compatibilityData(this.cardList)
    if (this.morePage) {
      this.choosePage(this.choiceTab[0].id)
      // this.element.content.activePageId = this.choiceTab[0].id
    }
    this.isRun = false
  },
  mounted() {
    // 组件更新的时候，如果是移动看板，绑定手势
    this.isMobile && !this.isCombineDialog && this.addSwipe()
  },
  computed: {
    isMobile() {
      return this.utils.isMobile
    },
    content() {
      return this.element.content || {}
    },
    cardList() {
      return this.content.cardList || []
    },
    choiceTab() {
      return this.content.choiceTab || []
    },
    morePage() {
      const choiceTab = this.$_getProp(this.element, 'content.choiceTab')
      return choiceTab && choiceTab.length > 1
    },
    activeIndex: {
      get() {
        return this.choiceTab.findIndex(item => item.id === this.content.activePageId)
      },
      set(val) {
        this.$set(this.element.content, 'activePageId', this.choiceTab[val].id)
      }
    },
    isEmptyCard() {
      let emptyCard = true
      this.choiceTab.map(item => {
        let _cardList = item.saveObj.cardList || []
        _cardList.length && (emptyCard = false)
      })
      this.cardList.length && (emptyCard = false)
      return emptyCard
    },
    // 是否开启大屏
    themeFullScreen() {
      return this.themeData.themeFullScreen
    },
  },
  watch: {
    // 'loading': {
    //   handler(_loading) {
    //     if (this.isRun && _loading) {
    //       // 看板没有请求后台数据时，不渲染卡片
    //       this.isRun = false
    //     }
    //   },
    //   immediate: true,
    // },
  },
  methods: {
    eventBus,
    handleCardMounted(cardId) {
      if (this.pendingCards.size === 0) return

      this.pendingCards.delete(cardId)

      if (this.pendingCards.size === 0) {
        this.$nextTick(() => {
          const nextItem = this.choiceTab.find(item => item.id === this.content.activePageId)
          if (nextItem?.saveObj?.cardList) {
            this.element.content.cardList = nextItem.saveObj.cardList
          }
        })
      }
    },
    compatibilityData(cardList) {
      // 清空卡片请求数据
      this.clearCardResponseData(cardList)
      // 兼容网格布局数据
      this.compatibilityLayoutData(cardList)
    },
    clearCardResponseData(cardList) {
      cardList.forEach(card => {
        const optionArray = card.content.optionArray
        Array.isArray(optionArray) && optionArray.forEach(item => {
          const notSaveKeys = ['indexValue', 'rateGrowth', 'growthValue', 'rateCompletion', 'isNone', 'classText', 'arrowClass']
          Object.keys(item).forEach(key => {
            notSaveKeys.includes(key) && delete item[key]
          })
        })
      })
    },
    compatibilityLayoutData(cardList) {
      cardList.forEach(card => {
        const layout = card.content.layout
        if (layout.maxW !== 2) return
        // 网格由两列变更为6列
        Object.assign(layout, {
          maxW: 6,
          w: layout.w * 3,
          x: layout.x * 3,
        })
        Reflect.deleteProperty(layout, 'minW')
        Reflect.deleteProperty(layout, 'minH')
      })
    },
    addSwipe() {
      // Get a reference to an element.
      const square = document.getElementById('combine_card_' + this.element.id)

      if (!square) return

      // Create a manager to manager the element
      const manager = new Hammer.Manager(square)

      // Create a recognizer
      const Swipe = new Hammer.Swipe()

      // Add the recognizer to the manager
      manager.add(Swipe)

      // Subscribe to a desired event
      manager.on('swipeleft', () => this.slideLeft())
      manager.on('swiperight', () => this.slideRight())
    },
    requestAdapter() {
      const { request, requestKeyFn } = bridge.adapter
      console.assert(request && requestKeyFn)
      // if (!this.cardList.length) return
      this.setElementLoadState(true)
      this.isRun = true
      const requestParams = typeof request === 'function' ? request.call(this, this.element) : false
      const requestKey = requestKeyFn()
      return {
        requestKey,
        requestParams
      }
    },
    responseAdapter(res = {}) {
      this.isRun = true
      let cardList = []
      this.choiceTab.map(item => cardList.push(...item.saveObj.cardList))
      if (!cardList.length) {
        this.setElementLoadState(false)
        return
      }
      let currentCard = cardList.find(item => item.id === res.id)
      this.$set(currentCard.content, 'card', res.cartResponseSum)
      // 当前页数据也需要更新
      let currentPageCard = this.cardList.find(item => item.id === res.id)
      if (currentPageCard) {
        this.$set(currentPageCard.content, 'card', res.cartResponseSum)
      }
      // 设置返回数据标识为true
      currentCard.content.isResponse = true
      // 拿到所有卡片返回数据后，取消loading动画
      let allCardResponse = true
      cardList.forEach(item => {
        if (!item.content.isResponse) {
          allCardResponse = false
        }
      })
      if (allCardResponse) {
        this.setElementLoadState(false)
        cardList.forEach(item => {
          this.$delete(item.content, 'isResponse')
        })
      }
    },
    // 对外提供控制loading
    setElementLoadState(loading) {
      if (this.commonData.isOffApiLoading) return '关闭loading'
      if (this.loading === loading) return
      this.loading = loading
      this.elDom = (this.isCombineDialog || this.themeFullScreen || this.fullscreenData.enlargeVisible) ? this.$el : (document.getElementById(this.element.id) || this.$el)
      if (this.setLoadingStatus(loading, this.element.id)) {
        return
      }
      this.loadingInstance && this.loadingInstance.close()
      if (loading && this.elDom) {
        this.loadingInstance = this.$loading({
          target: this.elDom,
          spinner: 'sdp-loading-gif',
        })
        insetDom(this.loadingInstance.$el)
        this.$nextTick(() => {
          let curThemeType = this.themeData.themeType
          changeThemeType({
            theme: this.isMobile ? this.themeData.themeType : curThemeType,
            dom: this.elDom,
            projectName: this.utils.env?.projectName
          })
        })
      }
    },
    // 对外提供error 提示
    setElementErrorState(error) {
      this.setErrorStatus(error, this.element.id)
    },
    // 判断当前页是否第一页或最后一页
    checkFirstOrLastPage(pageFlag) {
      let { choiceTab, activePageId } = this.element.content
      if (!choiceTab || !choiceTab.length) return
      const currentPageIndex = choiceTab.findIndex(item => item.id === activePageId)
      return pageFlag === 'first' ? currentPageIndex === 0 : currentPageIndex === choiceTab.length - 1
    },
    slideLeft() {
      // 左滑切换下一页
      let { choiceTab, activePageId } = this.element.content
      if (!choiceTab || !choiceTab.length) return
      const oldPageIndex = choiceTab.findIndex(item => item.id === activePageId)
      if (!choiceTab[oldPageIndex + 1]) return
      // this.element.content.activePageId = choiceTab[oldPageIndex + 1].id
      this.choosePage(choiceTab[oldPageIndex + 1].id)
      this.$nextTick(() => {
        const carousel = this.$refs.carousel
        carousel && carousel.next()
      })
    },
    slideRight() {
      // 右滑切换上一页
      let { choiceTab, activePageId } = this.element.content
      if (!choiceTab || !choiceTab.length) return
      const oldPageIndex = choiceTab.findIndex(item => item.id === activePageId)
      if (!choiceTab[oldPageIndex - 1]) return
      // this.element.content.activePageId = choiceTab[oldPageIndex - 1].id
      this.choosePage(choiceTab[oldPageIndex - 1].id)
      this.$nextTick(() => {
        const carousel = this.$refs.carousel
        carousel && carousel.prev()
      })
    },
    // 保存当前page页配置
    saveCurrentPage() {
      const { choiceTab, activePageId } = this.element.content
      if (!activePageId) {
        this.$set(this.element.content, 'activePageId', 1)
      }

      const oldPageData = choiceTab.find(item => item.id === (activePageId || 1))
      const saveObj = this.$_deepClone(this.element.content)

      Reflect.deleteProperty(saveObj, 'choiceTab')
      Reflect.deleteProperty(saveObj, 'activePageId')
      Reflect.deleteProperty(saveObj, 'borderRadius')
      Object.assign(oldPageData, { saveObj })

    },
    // 切换page页
    choosePage(currentPageId, deletePage) {
      let { choiceTab, activePageId } = this.element.content
      if (currentPageId === activePageId) return

      // 保存当前page页配置
      !deletePage && this.saveCurrentPage()

      // 恢复当前选中page页配置
      const currentPageData = choiceTab.find(item => item.id === currentPageId)
      this.$_filterDeepClone(this.element.content, currentPageData.saveObj, ['choiceTab', 'activePageId', 'borderRadius'])
      this.$set(this.element.content, 'activePageId', currentPageId)
      this.$emit('choose-page')
    },
    refreshEl() {
      const eventData = new EventData({
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'refreshEl',
        type: 'refreshEl',
        data: {
          ids: [this.element.id],
        }
      })
      this.$emit('eventBus', eventData)
    },
    callCardGridMethod(method, params) {
      const cardGrid = Array.isArray(this.$refs.cardGrid) ? this.$refs.cardGrid : [this.$refs.cardGrid]
      cardGrid.forEach(item => item[method](params))
    },
  },
}
</script>

<style lang="scss" scoped>
.placeholder-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: #999999;
}
.empty-element-text {
  color: #999;
  font-size: 36px;
}
/deep/ .combine-card {
  height: 100%;
  .grid-layout {
    // height: 100%;
    .sdp-grid-item-header {
      display: none;
    }
  }
  /deep/ .tag-card {
    min-width: 140px;
    min-height: 100px;
  }
}
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.page-index {
  width: calc(100% + 14px);
  margin-left: -7px;
  height: 24px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  .page-point {
    cursor: pointer;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    margin-left: 10px;
    border: 1px solid var(--sdp-fs2);
    background: var(--sdp-fs2);
  }
  .select-page-index {
    //border: 1px solid $color-main;
    //background: $color-main;
    border: 1px solid $color-ZHKPXZYD;
    background-color: $color-ZHKPXZYD;
  }
  .switch-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      cursor: pointer;
      font-size: 18px;
      color: $color-ZHKPXZYD;
    }
  }
  .switch-icon-left {
    margin-right: 24px;
  }
  .switch-icon-right {
    margin-left: 24px;
  }
  .notVisible {
    visibility: hidden;
  }
}
/deep/ .el-carousel {
  .el-carousel__indicators {
    display: none;
  }
}
</style>
