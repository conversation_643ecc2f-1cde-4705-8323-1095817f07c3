<template>
    <!-- :srcdoc="srcdoc"  -->
    <div class="custom-preview-container">
      <ElementTitle v-if="isShowTitle" :class="`preview-title export-title-${element.id}`" :element="element" :screenType="screenType" @eventBus="eventBus"></ElementTitle>
      <iframe
          v-if="isShow"
          :data-tag="element.id"
          ref="iframeRef"
          :class="{ isNoDrop }"
          class="previewBox"
          :sandbox="iframeSandbox"
          src="about:blank"
          frameborder="0"
      ></iframe>
    </div>
</template>

<script>// @ts-nocheck

import { SOURCE_TYPE } from '../utils'
import { EVENT_BUS, TYPE_INTERACTION } from 'packages/base/board/displayPanel/constants'
import { EVENT_TYPE } from '../utils/constant'
import mixinUtils from '../mixins/index'
import { SUPERLINK_CONST_TYPE, EVENT_DATA_PARAMS } from '../../../../../../../assets/constant'
import EventData from '../../../../../../../assets/EventData'
import ElementTitle from './elementTitle.vue'
import eventBus from 'packages/assets/eventBus'
import { setThemeIntoContent } from '../utils/theme'
import { isCanInteraction } from 'packages/base/board/displayPanel/utils'
import { filterMapVerify } from '../../../../utils'
import { TYPE_ELEMENT_GROUP } from '../../../../params/utils/constants'
// import {saveTableData } from '../editorSetting/api'
export default {
    name: 'elementCustomerPreview',
    inject: ['communication', 'sdpBus', 'commonData', 'langCode', 'themeData', 'fullscreenData', 'utils'],
    mixins: [mixinUtils],
    components: { ElementTitle },
    props: {
        isShowTitle: {
          type: Boolean,
          default: true
        },
        element: {
            type: Object,
            required: true
        },
        sandboxProp: {
            type: Array,
            default: () => ['allow-scripts', 'allow-modals', 'allow-same-origin']
        },
        screenType: {
            type: String,
        },
        elList: {
            type: Array,
            default: () => []
        },
    },
    data() {
        const iframeSandbox = this.sandboxProp.join(' ')
        return {
            iframeSandbox,
            source: SOURCE_TYPE.SDP_CUSTOMER_PREVIEW,
            targetData: null,
            isNoDrop: true,
            isShow: false,
        }
    },
    computed: {
        srcdoc() {
            const html = this.element?.content?.iframeConfig?.html || ''
            const javascript = this.element?.content?.iframeConfig?.javascript || ''
            // eslint-disable-next-line no-useless-escape
            return `${html}<script id="sdp-js" sdp-cantact-id="${this.elementId}">${javascript}<\/script>`
        },
        elementId() {
            return this.element.id
        },
        isPreview() {
            return this.commonData.isPreview
        },
        // 是否支持超链接跳转
        superLinkNeedClick() {
          return !!this.element.content?.superLinkOptions?.length && !this.isLarge
        },
        // 是否支持交互
        interactionNeedClick() {
          return !!this.interactionOptions?.length && !this.isLarge
        },
        superLinkOptions() {
          return this.element.content?.superLinkOptions || []
        },
        interactionOptions() {
          return this.element.content?.interactionOptions || []
        },
        isLarge() {
          return this.fullscreenData.enlargeVisible
        },
        themeType() {
            return this.themeData.themeType
        },
    },
    watch: {
        srcdoc: {
            handler() {
                this.ready()
            },
        },
        isPreview: {
            handler(val) {
                val && (this.isNoDrop = false)
            },
            immediate: true
        }
    },
    created() {
        setThemeIntoContent(this.element.content, this.themeType)
    },
    mounted() {
        this.ready()
        this.sdpBus.$on(EVENT_BUS.CUSTOMR_DISPATACH_DATA, this.receiveIframePassData)
        const _this = this

        document.addEventListener('click', () => {
            if (this.isPreview) return
            _this.$set(_this, 'isNoDrop', true)
        })
    },
    beforeDestroy() {
        this.sdpBus.$off(EVENT_BUS.CUSTOMR_DISPATACH_DATA, this.receiveIframePassData)
    },
    methods: {
        eventBus,
        [EVENT_TYPE.INIT_DATA](data) {
            console.log(`msgA ${this.source}: ${EVENT_TYPE.INIT_DATA}`, data)

            this.targetData = data

            this.$emit('initComplete', data)
        },
        [EVENT_TYPE.GET_API_DATA](data) {
            console.log(`msgA ${this.source}: ${EVENT_TYPE.GET_API_DATA}`, data)

            const json = this.element?.content?.iframeConfig?.json || ''

            let jsonObj = null

            try {
                jsonObj = JSON.parse(json)
            } catch (err) {
                console.error('json解析错误', err)
                return
            }
            let showElementDescription = ''
            try {
                showElementDescription = (this.commonData && this.commonData.getBoardInfo()) ? this.commonData.getBoardInfo().showElementDescription : ''
            } catch (err) {
                console.error('showElementDescription解析错误', err)
            }

            this.sendDataGetIframe({
                eventType: EVENT_TYPE.INIT_DATA,
                data: {
                    res: data,
                    params: this.getParams(this.targetData),
                    // 需要传入的配置
                    config: Object.assign({
                        tenantId: this.utils.tenantId || '',
                        baseURL: this.utils?.api?.defaults?.baseURL,
                        langCode: this.langCode,
                        themeType: this.themeType,
                        isPreview: this.commonData.isPreview,
                        isPreviewLangCode: this.commonData.boardSlectLang(),
                        // showElementDescription: this.commonData?.getBoardInfo()?.showElementDescription || ''
                        showElementDescription: showElementDescription
                    }, jsonObj)
                },
            })
        },

        [EVENT_TYPE.SUPER_LINK](data = {}) {
          if (!this.superLinkNeedClick) return

          console.log(`msgA ${this.source}: ${EVENT_TYPE.SUPER_LINK}`, data)
          console.log('%c 执行了自定义超链接操作', 'padding: 8px;background:#FF9933;', data)

          const { field = '', value = '', site = '' } = data

          const superLinkOptions = this.superLinkOptions.find(item => {
            return item.labelBoard.type === SUPERLINK_CONST_TYPE.variavle && (site ? site === item.labelBoard.site : item.labelBoard.label === field)
          })
          if (!superLinkOptions) return

          if (superLinkOptions?.openMethod === '2' && (this.utils.isPcMobile || this.commonData.isMobileDataReport())) {
            return void 'PC转移动端后不能打开弹窗超链接'
          }

          if (this.isPreview) {
            const { parameterField = [], dataSets = [], labelBoard } = superLinkOptions
            let dataSetsParams = []
            dataSets.forEach(item => {
              item.columnName.forEach((columnItem, columnIndex) => {
                value && dataSetsParams.push({
                  columnName: columnItem,
                  dataSetId: item.dataSetId,
                  values: [value],
                })
              })
            })

            const eventData = new EventData({
              type: 'setSuperLink',
              target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
              targetFn: 'setSuperLink',
              data: {
                options: superLinkOptions,
                superLink: {
                  dataSets: dataSetsParams,
                  dataSetId: labelBoard.dataSetId,
                },
              },
            })
            // console.log('自定义eventData', eventData)
            this.$emit('eventBus', eventData)
          } else {
            this.$message.warning(this.$t('sdp.views.bplsClick'))
          }
        },

        [EVENT_TYPE.INTERACTION](param = {}) {
            if (!this.interactionNeedClick) return

            const { field = '', value = '', parentId = '' } = param

            const interactionOptions = this.interactionOptions.filter(item => {
                const { dataSetId, columnName } = item.associElements[0]
                return parentId === dataSetId || columnName === field
            })

             if (!interactionOptions || !interactionOptions.length) return

            const associElements = interactionOptions[0].associElements
            // 判断是否可以进行交互
            if (!isCanInteraction(this.element._containerId, associElements || [], this.elList)) return

            // 是否点击相同图块标识
            let theSameInteraction = true
            interactionOptions.map(item => item.values ? item.values[0] : null).forEach((curVal) => {
                if (!value || curVal !== value) {
                    theSameInteraction = false
                }
            })

            const data = {
                ids: [
                this.element.id,
                ...interactionOptions[0].associElements.map(
                    elSetting => elSetting.id
                )
                ],
                element: { type: TYPE_INTERACTION.INTERACTION_SETTING }
            }

            data.ids.forEach((id, i) => {
                if (i === 0) return
                const associElement = this.elList.find(el => el.id === id)
                if (associElement.content.drillSettings?.drillDimensions) {
                    delete associElement.content.drillSettings.drillDimensions
                }
            })

            this.interactionOptions.forEach((item) => {
                const { dataSetId, columnName } = item.associElements[0]
                // 只给对应字段设置交互值
                if (parentId !== dataSetId || columnName !== field) {
                    this.$delete(item, 'values')
                    return
                }
                // 判断过滤条件是否修改
                if (item.openFilter && filterMapVerify({ type: TYPE_ELEMENT_GROUP.CHART, filterMap: item.filterMap, filterData: this.$_getProp(this, 'element.content.filters', []) })) {
                    this.$delete(item, 'filterMap')
                    this.$delete(item, 'openFilter')
                }

                if (theSameInteraction) {
                    this.$delete(item, 'values')
                } else {
                    this.$set(item, 'values', [value])
                }
            })

            const target = this.utils.isMobile ? EVENT_DATA_PARAMS.displayPanelMobile : EVENT_DATA_PARAMS.displayPanel
            const eventData = new EventData({
                source: 'elementCustomerPreview',
                target,
                targetFn: 'refreshEl',
                data,
            })

            this.$emit('eventBus', eventData)
        },
        [EVENT_TYPE.GET_EXPORT_DATA](data) {
            this.$emit('getExportData', data)
        },
        [EVENT_TYPE.REFRESH_ELEMENT](data) {
          if (!Array.isArray(data) || !data.length) return

          const target = this.utils.isMobile ? EVENT_DATA_PARAMS.displayPanelMobile : EVENT_DATA_PARAMS.displayPanel
          const eventData = new EventData({
              source: 'elementCustomerPreview',
              target,
              targetFn: 'refreshEl',
              data: {
                ids: data
              },
          })

          this.$emit('eventBus', eventData)
        },

        // 收到数据
        receiveIframePassData(params) {
            const { eventType, data, source, sdpCantactId: targetSdpCantactId } = params

            if (source !== this.source) return

            if (targetSdpCantactId !== this.elementId) return

            console.log(`msgA ${this.source}: 接收数据preview的数据`, params)

            switch (eventType) {
                case EVENT_TYPE.INIT_DATA:
                case EVENT_TYPE.SUPER_LINK:
                case EVENT_TYPE.INTERACTION:
                case EVENT_TYPE.GET_EXPORT_DATA:
                case EVENT_TYPE.REFRESH_ELEMENT:
                    this[eventType](data)
                    break
            }
        },

        // 发送数据
        sendDataGetIframe(params) {
            console.log(`msgA ${this.source}: 发送数据preview的数据`, params)

            const dom = this.$refs.iframeRef

            if (dom) {
                this.communication.sendMessage({
                    ...params,
                    source: this.source,
                    sdpCantactId: this.elementId
                }, dom.contentWindow)
            }
        },

        // 初始化iframe
        ready() {
            this.isShow = false

            setTimeout(() => {
                this.isShow = true

                this.$nextTick(() => {
                    const dom = this.$refs.iframeRef

                    if (dom) {
                        dom.parentElement.ondblclick = () => {
                            if (this.isPreview) return
                            this.$set(this, 'isNoDrop', false)
                        }
                        const initCall = () => {
                            dom.contentWindow.document.write(this.srcdoc)
                            this.$emit('initComplete')
                        }

                        dom.onload = () => {
                            initCall()
                        }

                        initCall()
                    }
                })
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.custom-preview-container{
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.previewBox {
    width: 100%;
    height: 100%;
}
.isNoDrop {
    pointer-events: none;
}
</style>
