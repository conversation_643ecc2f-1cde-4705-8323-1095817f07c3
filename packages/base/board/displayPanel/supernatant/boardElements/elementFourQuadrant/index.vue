<template>
  <div style="height: 100%;  width: 100%;" :class="[ !isRun ? `a${element.id}` : '']">
    <ElementSlot :visible="utils.isMobile">
      <slot></slot>
    </ElementSlot>
    <!-- 占位文本 -->
    <div v-if="!isRun" class="text">
      <div  class="placeholder-text">{{ commonData.isPreview ? '' : $t('sdp.views.FourQuadrant') }}</div>
      <!-- <loading v-if="fourQuadrantId" :isLoading="loading" ></loading> -->
      <waiting v-if="!loading" :isLoading="commonData.isPreview && isRenderInteraction"/>
    </div>

    <div v-else class="four-table loading-settings" :class="`a${element.id}`">
      <!-- <loading :isLoading="loading"></loading> -->
      <!-- PC端多合一表格 -->
      <div v-if="!emptyFourQuadrant && !isMobile" class="flex">
        <div
          :class="['table-box', 'table-' + response.length]"
          v-for="(item,i) in response"
          :key="i"
          :style="customStyle()"
        >
          <div>
            <span class="table-name"> {{item && item.name}} </span>
            <el-tooltip
              v-if="commonData.showElementDescription && fourQuadrantTableConfig[i].description && selectShow"
              class="table-tooltip table-description"
              effect="dark"
              placement="bottom"
              :append-to-body="appendToBody"
              v-sdp-el-insert="'el-tooltip$#$elementFourQuadrant'"
              :content="fourQuadrantTableConfig[i].description">
              <i class="el-tooltip icon-sdp-tishi"></i>
            </el-tooltip>

            <el-popover
              v-if="boardInfo && boardInfo.showElementTip && !!element.tipSetList && element.tipSetList[i]&& element.tipSetList[i].displayType === 'hover' && checkTipShow({ isTipList: true, tipSetIndex: i, element, commonData, utils, langCode })"
              effect="dark"
              :popper-class="`${getCurrentThemeClass()} sdp-tip-popover`"
              class="table-tooltip table-description"
              :trigger="isMobile ? 'click' : 'hover'"
              placement="bottom"
            >
              <tip-render ref="tipRender" :element="element" :isTipList="true" :tipSetIndex="i" @refresh="handleTipRefresh(0, i)"/>
              <i class="icon-sdp-gengxinshijian1" style="font-size: 12px" slot="reference"></i>
            </el-popover>
          </div>
          <!--提示-->
          <div v-if="boardInfo && boardInfo.showElementTip && !!element.tipSetList && element.tipSetList[i]&& element.tipSetList[i].displayType === 'tile'">
            <tip-render ref="tipRender" :element="element" :isTipList="true" :tipSetIndex="i" @refresh="handleTipRefresh($event, i)"/>
          </div>
          <!-- <el-scrollbar style="height: 100%;"> -->
            <table-content
              ref="tableContent"
              :style="{ height: !isMobile ? `calc(100% - 27px - ${tipHeight[i]}px)` : `` }"
              :tableContent="item"
              :fourQuadrantTableConfig="fourQuadrantTableConfig[i]"
              :type="typeFun(item)"
              :metricsData="getMetrics(i)"
              :element="element"
              :board-info="boardInfo"
              :enableThemeStyle="themeType === 'sdp-dark-blue'">
            </table-content>
          <!-- </el-scrollbar> -->
        </div>
      </div>
      <!-- 移动端多合一表格 -->
      <div v-if="!emptyFourQuadrant && isMobile" style="height:100%; width:100%;" :style="{ padding: isAppAdvanceContainer ? 0 : 0 }">
        <div class="table-name-list">
          <div
            v-for="(name, index) in tableNameList"
            :key="index"
            class="option"
            :class="[activeTableIndex === index ? 'active-table' : '']"
            :style="tableNameStyle"
            :title="name"
            @click="changeHandler(index)">

              <span>{{ name }}</span>
              <i class="el-tooltip icon-sdp-miaoshu" @click.stop="showDescriptionInfoPopup(fourQuadrantTableConfig[index].description)"  v-if="commonData.showElementDescription && fourQuadrantTableConfig[index].description && selectShow && !fullscreenData.enlargeVisible"></i>

            <el-popover
              v-if="boardInfo && boardInfo.showElementTip && !!element.tipSetList && element.tipSetList[index]&& element.tipSetList[index].displayType === 'hover'"
              effect="dark"
              :popper-class="`${getCurrentThemeClass()} sdp-tip-popover`"
              class="table-tooltip table-description"
              :trigger="isMobile ? 'click' : 'hover'"
              placement="bottom"
            >
              <tip-render ref="tipRender" :element="element" :isTipList="true" :tipSetIndex="index" @refresh="handleTipRefresh(0, index)"/>
              <i class="el-tooltip el-icon-s-opportunity" style="margin-top: 4px;" slot="reference"></i>
            </el-popover>
          </div>
        </div>
        <el-carousel
          class="mobile-table sdp-grid-item-drag-ignore"
          :class="[fourQuadrantClass, !isPreview || element.isFocus ? 'is-focus' : '']"
          ref="carousel"
          arrow="never"
          :autoplay="false"
          indicator-position="none"
          :height="'100%'">
          <el-carousel-item v-for="(table, index) in response" :key="index" :name="index + ''">
            <!--提示-->
            <div
              v-if="boardInfo && boardInfo.showElementTip && !!element.tipSetList && element.tipSetList[index]&& element.tipSetList[index].displayType === 'tile'"
              style="margin-bottom: 4px;"
            >
              <tip-render ref="tipRender" :element="element" :isTipList="true" :tipSetIndex="index" @refresh="handleTipRefresh($event, index)"/>
            </div>
            <cube-scroll
              ref="verticalScroll"
              direction="vertical"
              class="horizontal-scroll-list-wrap"
              :class="{ 'no-data-scroll': table && table.rows && !table.rows.length }"
              :style="{ height: `calc(100% - ${tipHeight[index] ? (tipHeight[index] + 4) : 0}px)`}"
              :scrollEvents="['scroll']"
              @hook:mounted="handleScrollMounted(index)"
              @hook:destroyed="handleScrollDestroyed(index)"
              :options="verticalOptions">
              <!-- <cube-scroll ref="horizontalScroll" direction="horizontal" class="horizontal-scroll-list-wrap" :options="horizontalOptions"> -->
                <table-content
                  ref="tableContent"
                  :element="element"
                  :type="typeFun(table)"
                  :metricsData="getMetrics(index)"
                  :tableContent="table"
                  :board-info="boardInfo"
                  :fourQuadrantTableConfig="fourQuadrantTableConfig[index]">
                </table-content>
              <!-- </cube-scroll> -->
            </cube-scroll>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div v-if="emptyFourQuadrant && !loading" class="placeholder-text empty-element-text"><span>NONE</span></div>
    </div>
  </div>
</template>

<script>
import tableContent from './tableContent'
// import Loading from 'packages/base/common/scroll/loading/loading.vue'
import Loading from 'packages/base/common/loading/loading'
import waiting from 'packages/base/common/waiting'
import bridge from './bridge'
import { ScrollNavBar } from 'cube-ui'
import { THEME_TYPE } from 'packages/assets/constant'
import SdpElInsert from 'packages/directives/v-el-insert'
import scrollMixin from './scrollMixin'
import elementMixin from 'packages/base/board/displayPanel/supernatant/mixins/elementMixin'
import { insetDom } from 'packages/base/common/loading/components/loadingIcon'
import ElementSlot from '../components/elementSlot.vue'
import descriptionInfoMixin from 'packages/base/board/mixins/descriptionInfoMixin'
import TipRender
  from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/tipRender.vue";
import {checkTipShow} from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/utils";

export default {
  name: 'elementFourQuadrant',
  mixins: [scrollMixin, elementMixin,descriptionInfoMixin],
  inject: {
    commonData: { default: {} },
    themeData: { default: {} },
    utils: { default: {} },
    fullscreenData: { default: {} },
    langCode: { default: 'zh' },
    getCurrentThemeClass: { default: () => () => '' },
    setLoadingStatus: {
      default: () => () => false
    },
    setErrorStatus: {
      default: () => () => false
    },
    getUnknownName: { default: () => (a, b) => b },
  },
  props: {
    isAdvanceContainer: Boolean,
    element: {
      type: Object,
      default: () => ({})
    },
    isFullScreen: {
      type: Boolean,
      default: false
    },
    boardInfo: {
      type: Object,
      default: () => ({})
    },
    // 是否是全屏状态
    fullscreen: {
      type: Boolean,
      default: false,
    },
  },
  directives: {
    SdpElInsert,
  },
  components: {
    TipRender,
    tableContent,
    // Loading,
    waiting,
    ElementSlot
  },
  data() {
    return {
      tableData: [],
      loading: false,
      isRun: false,
      // horizontalOptions: {
      //   scrollbar: {
      //     fade: true,
      //     interactive: true,
      //   }
      // },
      activeTableIndex: 0,
      selectShow: true,
      tipHeight: {
        0: 0,
        1: 0,
        2: 0,
        3: 0,
      }
    }
  },
  created() {
    const vm = this
    Object.defineProperty(this.element, 'vm', {
      enumerable: false,
      configurable: true,
      get() {
        return vm
      },
    })
    this.element.content.loading = false
    this.isRun = false
    this.response = []
    // 初始化删除老元素的base64属性
    if (this.element.content.base64) {
      Reflect.deleteProperty(this.element.content, 'base64')
    }
  },
  watch: {
    'loading': {
      handler(_loading) {
        // 看板请求数据时,isRun设为true
        if (!this.isRun && _loading) {
          this.isRun = true
        }
      },
      immediate: true,
    },
    fullscreen() {
      // 全屏切换的时候要让tooltip、select等组件重新生成一下
      this.selectShow = false
      this.$nextTick(() => {
        this.selectShow = true
      })
    },
  },
  computed: {
    isRenderInteraction() {
      return this.$_getProp(this, 'element.elAttr.isRenderInteraction', false)
    },
    isMobile() {
      return this.utils.isMobile
    },

    isAppAdvanceContainer() {
      return this.isMobile && this.isAdvanceContainer
    },

    styleHeight() {
      const { height } = this.element.style
      return {
        height
      }
    },
    boxHeight() {
      const len = this.response.length
      return {
        height: `${100 / Math.ceil(len / 2) - 4}%`
      }
    },
    content() {
      return this.element.content
    },
    response: {
      get() {
        return this.element.content.response || []
      },
      set(val) {
        this.$set(this.element.content, 'response', val)
      }
    },
    // 是否存在后台返回数据
    emptyFourQuadrant() {
      return this.response.length === 0
    },
    // 是否设置过表格内容
    fourQuadrantId() {
      return this.$_getProp(this, 'content.fourQuadrantOptions.id')
    },
    // 表格配置
    fourQuadrantTableConfig() {
      return this.$_getProp(this, 'content.fourQuadrantOptions.tables', [])
    },
    // 表格名称列表
    tableNameList() {
      return this.response.map(item => item && item.name)
    },
    // 多合一表格类名
    fourQuadrantClass() {
      return 'four_quadrant_' + this.element.id
    },
    // 多合一表格名称tab样式
    tableNameStyle() {
      const width = 100 / this.tableNameList.length
      return {
        'width': width + '%'
      }
    },
    isPreview() {
      return this.commonData.isPreview
    },
    // 是否开启大屏
    themeFullScreen() {
      return this.themeData.themeFullScreen
    },
    // 主题类型
    themeType() {
      return this.themeData.themeType || THEME_TYPE.default
    },
    // 是否显示主题样式  大屏模式、预览时显示
    isShowThemeStyle() {
      return this.themeFullScreen || this.isPreview
    },
    // 大屏、全屏、放大时tooltip的DOM直接增加到父级内
    appendToBody() {
      return this.themeFullScreen || !this.fullscreenData.enlargeVisible
    },
  },

  methods: {
    checkTipShow,
    typeFun(data = {}) {
      const datas = data.tableResponse
      for (let key in datas) {
        if (datas[key].type) {
          return datas[key].type
        }
      }
      return ''
    },
    handleTipRefresh(height, index) {
      this.$nextTick(() => {
        this.$set(this.tipHeight, index, height)

        this.updateElementPaddingRender()
      })
    },
    customStyle() {
      if (this.response.length) {
        const columNumber = Math.ceil(this.response.length / 2)
        const height = this.isMobile ? 'calc(100% - 12px)' : `calc(${100 / columNumber}% - 12px)`
        return {
          height,
          // minHeight: '200px'
        }
      } else {
        return {}
      }
    },
    updateElementPaddingRender() {
      const scrolls = this.$refs.verticalScroll
      if (scrolls) {
        scrolls.forEach(scroll => {
          scroll.refresh()
        })
      }
    },
    dataFixed() {
      const content = this.element.content
      content.fourQuadrantOptions.tables.forEach((tabItem, tabIndex) => {
        const { dimension = [], metrics = [] } = tabItem
        const dimensionAnotherNameList = content[tabIndex + 1]?.dimensionObj?.anotherNameList || []
        const metricAnotherNameList = content[tabIndex + 1]?.metricsObj?.anotherNameList || []
        dimension.forEach((dimensionItem, i) => {
          if (!dimensionAnotherNameList[i]) dimensionItem.viewColumnName = this.getUnknownName(tabItem.dataSetId, dimensionItem.columnName)
        })
        metrics.forEach((d, i) => {
          if (!metricAnotherNameList[i]) d.viewColumnName = this.getUnknownName(tabItem.dataSetId, d.columnName)
        })
      })
    },
    requestAdapter() {
      if (!this.fourQuadrantId) {
        this.isRun = true
        return false
      }
      this.dataFixed()
      const { request, requestKeyFn } = bridge.adapter
      this.setElementLoadState(true)
      // 清空上一次请求的返回数据
      this.$set(this.content, 'response', [])
      const requestParams = typeof request === 'function' ? request(this.element) : false
      const requestKey = requestKeyFn()
      return {
        requestKey,
        requestParams
      }
    },
    responseAdapter(res) {
      this.isRun = true
      if (res.id === this.element.id) {
        console.debug('多合一表格拿到数据', res)
        // 处理后的响应数据
        const resData = bridge.adapter.response(res)
        if (!resData.length) {
          this.setElementLoadState(false)
          return
        }
        const tableList = this.content.fourQuadrantOptions.tables || []
        // 获取子表格索引，保存返回数据
        const columnData = this.transformRowsToColumnData(resData[0])
        this.$set(resData[0], 'tableResponse', columnData)

        let singleTableResponse = resData[0]
        // 子表格id不存在时匹配name
        let tableIndex = tableList.findIndex(item => item.id === res.childrenId || (!item.id && item.name === singleTableResponse.name))
        res.childrenId && Object.assign(singleTableResponse, {
          id: res.childrenId
        })
        this.$set(this.content.response, tableIndex, singleTableResponse)
        // 获取多合一表格子元素id
        const tableIds = []
        tableList.forEach(item => {
          item.id && tableIds.push(item.id)
        })
        // 判断是否拿到全部返回数据
        let allResponseData = true
        tableIds.forEach(id => {
          let currentRes = this.content.response.find(item => item && item.id === id)
          if (!currentRes) {
            allResponseData = false
          }
        })
        // 拿到所有返回数据后，取消loading动画 重新计算滚动条位置
        if (allResponseData) {
          this.setElementLoadState(false)
          this.activeTableIndex = 0
          this.$nextTick(() => {
            const tableContent = this.$refs.tableContent || []
            tableContent.forEach(item => {
              // 表格数据懒加载处理，设置初始数据量
              item.setInitialTableList()
              // 重新计算滚动条位置
              this.themeFullScreen && item.updateScrollBar()
              // 清空表格排序数据
              // item.clearSortData()
            })
          })
        }
      }
      return res.response
    },
    // 将rows组装成类似表格数据结构
    transformRowsToColumnData(resData) {
      const { rows, columns } = resData
      const tableResponse = {}
      rows.forEach((item) => {
        Object.keys(item).forEach(alias => {
          if (!columns.includes(alias)) return
          tableResponse[alias] = tableResponse[alias] || {
            data: []
          }
          tableResponse[alias].data.push(item['VIEWFORMAT_' + alias])
        })
      })
      return tableResponse
    },
    // 对外提供清空卡片返回数据方法
    deleteElementData() {
      this.isRun = false
      this.$set(this.content, 'response', [])
      this.setElementLoadState(false) // 多合一表格仅交互时渲染关闭loading
    },
    // 对外提供控制loading
    setElementLoadState(loading) {
      if (this.commonData.isOffApiLoading) return '关闭loading'
      if (this.loading === loading) return
      this.loading = loading
      this.elDom = (this.themeFullScreen || this.fullscreenData.enlargeVisible) ? this.$el : (document.getElementById(this.element.id) || this.$el)
      this.loadingInstance && this.loadingInstance.close()
      if (this.setLoadingStatus(loading, this.element.id)) return
      if (loading && this.elDom) {
        this.loadingInstance = this.$loading({
          target: this.elDom,
          spinner: 'sdp-loading-gif',
        })
        insetDom(this.loadingInstance.$el)
      }
    },
    // 对外提供error 提示
    setElementErrorState(error) {
      this.setErrorStatus(error, this.element.id)
    },
    changeHandler(activeKey) {
      const carousel = this.$refs.carousel
      carousel && carousel.setActiveItem(activeKey.toString())
      this.activeTableIndex = activeKey
    },
    getMetrics(index) {
      const tables = this.content.fourQuadrantOptions.tables || []
      return tables[index] ? tables[index].metrics : []
    },
  },
}
</script>

<style lang="scss" scoped>
@import "packages/assets/styles/loadingSettings.scss";
.flex {
  display: flex;
  flex-wrap: wrap;
  height: calc(100% - 2px);
  // min-height: 200px;
}
.four-table {
  height: 100%;
  padding: 0;
  color: var(--sdp-xxbt2);
  /deep/ .el-tooltip__popper[data-sdp-el-insert='el-tooltip'],  /deep/ .el-tooltip__popper[data-sdp-el-insert-firefox='el-tooltip']{
    position: absolute !important;
  }
  // overflow: auto;
  .table-box {
    width: calc(50% - 18px);
    // overflow-y: hidden;
    overflow: auto;
    display: inline-block;
    margin-left: 12px;
    margin-top: 12px;
    box-sizing: border-box;
    // background-color: #fff;
    /deep/ .el-scrollbar__wrap{
      overflow-x: hidden;
    }
    &.table-1{
      width: calc(100% - 24px);
    }
  }
  .mobile-table {
    height: calc(100% - 42px);
    &:not(.is-focus) {
      &:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 99;
      }
    }
  }
  .table-name-list {
    display: flex;
    margin-bottom: 12px;
    height: 30px;
    width: 100%;
    background-color: $color-KBXXKDS;
    border-radius: 8px;
    .option {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      text-align: center;
      padding: 0 10px;
      > span {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        line-height: 30px;
        font-family: PingFang-SC-Medium;
        font-size: 12px;
        color: $color-KBXXKWXZWZ;
        //color: #333333;
        letter-spacing: 0;
        text-align: center;
      }
    }
    .active-table {
      margin: 2px;
      height: 26px;
      background-color: $color-KBXXK;
      box-shadow: 0 1px 2px 0 rgba(0,0,0,0.15);
      border-radius: 8px;
      > span {
        color: $color-KBXXWXZWZ;
        line-height: 26px;
      }
    }
    .icon-sdp-miaoshu, .el-icon-s-opportunity{
      font-size: 16px;
      // color: var(--sdp-zs);
      color: var(--sdp-xxbt2);
      margin-left: 5px;
      height: 16px;
      line-height: 18px;
    }
  }
}
.table-name {
  font-family: PingFangSC-Medium;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 7px;
  margin-right: 3px;
  font-weight: bold;
  color: var(--sdp-xxbt1);
  vertical-align: middle;
}
.table-description {
  cursor: pointer;
  color: var(--sdp-zs);
  vertical-align: middle;
  font-size: 12px;
}
.placeholder-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: var(--sdp-srk-bxwzs);
}
.text{
  font-size: 16px;
  color: var(--sdp-srk-bxwzs);
}
.empty-element-text {
  color: #999;
  font-size: 36px;
}
/deep/ .cube-scroll-nav-bar.cube-scroll-nav-bar_horizontal {
  background-color: #eee;
  height: 30px;
  line-height: 30px;
  border-radius: 8px;

  .cube-scroll-nav-bar-item {
    padding: 0 10px;
    font-size: 12px;
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333333;
  }
  .cube-scroll-nav-bar-item_active {
    border-radius: 8px;
    background: #FEFFFE;
    box-shadow: 0 1px 2px 0 rgba(0,0,0,0.15);
  }
}
.horizontal-scroll-list-wrap {
  /deep/ .cube-scroll-content {
    display: inline-block;
    min-width: 100%;
    height: auto !important;
  }
}
.no-data-scroll {
  /deep/ .cube-scroll-content {
    height: 100% !important;
  }
  /deep/ .cube-scroll-list-wrapper {
    height: 100%;
  }
}
.table-theme {
  color: #E9E9EF;
}
</style>
