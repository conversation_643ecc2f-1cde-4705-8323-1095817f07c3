import { TYPE_ELEMENT, TYPE_ELEMENT_GROUP } from 'packages/base/board/displayPanel/constants'
import i18n from 'packages/assets/locale'
import Vue from 'vue'
import {Content, Layout, Styles, StyleConfig, StoreLayout} from '../elementClasses'
import { flatten, deepClone, JSO<PERSON>lone } from 'packages/assets/utils/globalTools'
import { DATA_FILTER } from 'packages/base/grid/helpers/constants/index'
import { updateReportLogObj } from 'packages/assets/utils/reportLog'
import { chartPreview } from '../../chartSet/api'
import { setLgeTypeValue } from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'
import DatasetBase, { FieldType, ReplaceRuleType } from '../../../datasetReplace/datasetReplace'
import { getUsedDatasetField, datasetFieldReplace } from './helper/dataset'
import {setDefaultElementPadding} from "../../../utils";
import {
  replaceTipSetListUsedDataSetAndField,
  replaceTipSetUsedDataSetAndField
} from "../../chartSet/functions/modules/tipSetting/dataset";

const element = {
  name: i18n.t('sdp.views.newCard'),
  groupName: i18n.t('sdp.views.specialControl'),
  groupType: TYPE_ELEMENT_GROUP.SPEC,
  type: TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
  componentName: 'element-tag-new-card'
}

export class ElementTagNewCardDataset extends DatasetBase {
  includesDataPath = []
  excludesDataPath = []
  elementType = TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD
  isBindField = false
  callDatasetReplace(element: any, replaceRule: ReplaceRuleType): void {
    datasetFieldReplace(element, replaceRule)
  }
  getElementDatasetIdAndUseField(element: any, datasetList: any[]): { [id: string]: FieldType[] } | boolean {
    return getUsedDatasetField(element, datasetList)
  }
  getElementDatasetIdAndUseFieldDecorator(...rest: [any, any[]]) {
    return this.decorator(...rest)
}
}

class ElContent extends Content {
  constructor(type) {
    super(type)
    this.loading = false
    this.tagNewCardContent = 'singleIndex'
    this.optionArray = ''
    this.chartUserConfig = {
      cardNameStyle: [], // 卡片名称样式
      subCardNameStyle: [], // 副卡片名称样式
      indexValueStyle: [], // 指标值样式
      growthCompare: 'growthRate', // 增长对比
      growthCompareSetting: true,
      remark: '', // 备注
    }
    this.drillSettings = {}
  }
}

class ElStyle extends Styles {
  constructor(styleOpt, options = {}) {
    super(styleOpt, options)
    if (options.boardType === 'dataReport') {
      this.width = Math.floor((options.pageSize?.pageWidth || 0) / 2) || 313
      this.height = 146
    } else if (options.isScreen) {
      this.width = 313
      this.height = 148
    }
  }
}

class ElLayout extends Layout {
  constructor(element, options) {
    super(element, options)
    this.h = 12
    this.w = options.isMobile ? 144 : 24
  }
}

class ElStoreLayout extends StoreLayout {
  constructor(element, options) {
    super(element, options)
    this.pcLayout.h = 12
    this.pcLayout.w = 24
    this.mobileLayout.h = 12
    this.mobileLayout.w = 144
  }
}

class ElStyleConfig extends StyleConfig {
  constructor(styleOpt, options = {}) {
    super(styleOpt, options)
    if (options.boardType === 'dataReport') {
      this.size.width = Math.floor((options.pageSize?.pageWidth || 0) / 2) || 313
      this.size.height = 146
    } else if (options.isScreen) {
      this.size.width = 313
      this.size.height = 148
    }
    setDefaultElementPadding(this, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, options.isMobile)
  }
}

function associationDatasetParam(ret, el, chartUserConfig) {
  const { associatedData, dataset } = el.content
  if (!chartUserConfig.datasetAssociation || !associatedData || !Object.keys(associatedData).length) return
  const { sqlJoins, dataSetJoinsColumns, dataSetJoins, associatedDataset = {}, dataSetJoinsType = '2' } = deepClone(associatedData)
  // 当结果数据集字段未设置别名时，需要给一个默认值
  dataSetJoinsColumns.forEach(item => {
    if (!item.alias) {
      item.alias = item.labeName || item.columnName
    }
  })
  let param = {
    joinDataSetId: associatedDataset.id,
    dataSetJoinsColumns,
    dataSetJoinsType,
  }
  if (dataSetJoinsType === '2') {
    Object.assign(param, {
      sqlJoins,
      dataSetJoins: [dataSetJoins],
    })
  }
  Object.assign(ret, param)
}

// 组装卡片请求数据
function groupRequestParams(element) {
  const el = deepClone(element)
  const id = el.id
  const cartElementSum = []
  const { optionArray, drillSettings = {}, chartUserConfig = {}, tagNewCardContent } = el.content
  const { growthCompare, compareType, growthCompareSetting, warnLineSettingList, referenceValueList = [] } = chartUserConfig
  const isRateCard = tagNewCardContent === 'rate'
  const isCompareCard = tagNewCardContent === 'compare'
  const isRankCard = tagNewCardContent === 'rank'
  Array.isArray(optionArray) && optionArray.forEach((item, index) => {
    if (item.cartType && item.dataSetId && item.cartCount && (item.dimension || isRateCard)) {
      let numericalType
      if (!item.hasOwnProperty('numericalType')) {
        numericalType = (item.pointNum || item.pointNum === 0) ? 'numerical' : 'normal'
        item.dimension[0].columnName.includes('CURRENCY') && (numericalType = 'normal')
      } else {
        numericalType = item.numericalType
      }
      const param = {
        'pointNum': item.pointNum,
        'numericalType': numericalType,
        'cartType': item.cartType,
        'dataSetId': item.dataSetId,
        'cartCount': item.cartCount,
        'metrics': item.metrics && item.metrics.filter(e => e.columnName !== '' && e.aggType !== '' && e.columnType !== '' && e.opertator !== ''),
        'dimension': item.dimension && item.dimension.filter(e => e.columnName !== '' && e.aggType !== '' && e.columnType !== ''),
      }
      if (isRateCard) {
        let rate = item.ratioValue ? item.ratioValue.filter(e => e.columnName !== '' && e.aggType !== '' && e.columnType !== '') : []
        // 参考值
        if (referenceValueList.length) {
          referenceValueList.forEach(e => {
            if (!e.filed.labeName) return
            let filed = Object.assign(e.filed, {
              alias: e.referenceValueKey,
            })
            if (e.format) {
              filed.viewFormat = e.format === 'normal' ? { convention: 'normal' } : { [e.format]: { decimalNum: e.decimalNum || 0 } }
            }
            rate.push(filed)
          })
        }
        Object.assign(param, {
          'cartType': 'rate',
          'rate': rate,
          'contrast': item.compareValue && item.compareValue.filter(e => e.columnName !== '' && e.aggType !== '' && e.columnType !== ''),
        })
        delete param.metrics
      }
      if (isCompareCard) {
        Object.assign(param, {
          'cartType': 'compare',
          'contrast': item.compareValue && item.compareValue.filter(e => e.columnName !== '' && e.aggType !== '' && e.columnType !== ''),
        })
        delete param.metrics
      }
      if (isRankCard) {
        Object.assign(param, {
          'cartType': 'rank',
          'topN': 1,
          'dims': item.rankValue && item.rankValue.filter(e => e.columnName !== '' && e.aggType !== '' && e.columnType !== ''),
        })
        delete param.metrics
      }
      // 拷贝一份数据
      const paramCopy = JSON.parse(JSON.stringify(param))
      // 获取卡片对比方式
      let compareInfo = {
        compSubType: !growthCompare || growthCompare === 'growthRate' ? 'growth_rate' : 'growth_in_value'
      }
      const handleKeys = ['metrics', 'dimension', 'rate', 'contrast', 'dims']
      handleKeys.forEach(key => {
        if (!paramCopy[key]) return
        // 非比率卡片指标值，需要补充对比方式、增长率等数据
        const isNormalCardDimension = !isRateCard && !isCompareCard && key === 'dimension'
        paramCopy[key].forEach(item => {
          item.labeName && (item.columnName = item.labeName)
          if (isNormalCardDimension) {
            item.compare = growthCompareSetting
            // 增长对比开启
            if (growthCompareSetting) {
              // 设置卡片比较方式 同比、环比、特殊比较
              compareType && (item.compareRule = compareType)
              item.compareInfo = compareInfo
            } else {
              item.compareInfo = {}
            }
          }
          if (isRateCard || isCompareCard) {
            Object.assign(item, {
              compareInfo: item.aggType === 'CONTRAST' && item.selectedConttast ? { compSubType: item.selectedConttast } : {},
              compareRule: item.aggType === 'CONTRAST' && item.selectedConttastMode ? item.selectedConttastMode : '',
            })
          }

          setLgeTypeValue(item) // 自定义、业务函数、排序时，需要设置bFxType和lgeType

          if (key === 'dimension' && item.viewFormat?.convention) {
            item.viewFormat.convention = 'normal'
          }
          if (item.viewFormat?.hasOwnProperty('zeroFormat')) {
            const zeroFormat = item.viewFormat.zeroFormat
            if (!zeroFormat || zeroFormat === 'none') {
              delete item.viewFormat.zeroFormat
            } else {
              item.viewFormat.zeroFormat = (typeof zeroFormat === 'boolean' && zeroFormat) || zeroFormat === 'open'
            }
          }
        })
      })
      // 预警数据
      if (warnLineSettingList && warnLineSettingList[index]) {
        Object.assign(paramCopy, {
          judgeIntersection: false,
          alterWithParamComp: true,
          alterWithDimension: true,
          alterLogic: warnLineSettingList[index].map(w => {
            if (isRateCard || isCompareCard) {
              delete w.viewColumnName
            }
            return w
          })
        })
      }
      cartElementSum.push(paramCopy)
    }
  })
  // if (cartElementSum.length === 0) return
  // 数据过滤补充日期函数传参
  if (Array.isArray(drillSettings.filters)) {
    drillSettings.filters.forEach(retItem => {
      retItem.referToCalendar = drillSettings.caculationMethodType === DATA_FILTER.targetCaculationMethod
      if (retItem.referToCalendar && drillSettings.hasOwnProperty('disabledCalendarFnFilter')) {
        retItem.disabledCalendarFnFilter = drillSettings.disabledCalendarFnFilter
      }
    })
  }
  // 处理自定义计算字段属性
  cartElementSum.forEach((cardItem: any) => {
    const keys = Object.keys(cardItem)
    keys.forEach(key => {
      if (!Array.isArray(cardItem[key])) return
      cardItem[key] = cardItem[key].map(c => {
        c = {
          ...c,
          ...(c.customFieldInfo || {})
        }
        const o = this.getCustomDatasetFieldDetail(c, el.type)
        // 更新element.content中的字段数据
        if (c.webFieldType === 'customComputed') {
          extendFieldByKeyName.call(this, element, c, o)
        }
        return o
      })
    })

    // 处理关联数据集数据
    const associationDataset = keys.some(key => {
      return Array.isArray(cardItem[key]) && cardItem[key].some(item => item.webFieldFrom === 'associationDataset')
    })
    if (associationDataset) {
      associationDatasetParam(cardItem, el, chartUserConfig)
    }
  })
  const params = { id, cartElementSum, filters: drillSettings.filters || [] }
  return params
}

function extendFieldByKeyName(element, originField, newField) {
  const extendObj: any = {
    metricGroupInfo: this.getMetricGroupInfo(originField),
    columnName: newField.columnName,
    customerExprField: true,
    exp: newField.exp,
    keyName: newField.keyName,
    customerExprDim: newField.customerExprDim,
    aggType: newField.aggType,
    webFieldType: newField.webFieldType,
  }
  const propKeys = ['dimension', 'metrics', 'ratioValue', 'compareValue', 'rankValue']
  const { optionArray = [] } = element.content
  optionArray.forEach(option => {
    const keys = Object.keys(option)
    keys.forEach(key => {
      if (propKeys.includes(key) && Array.isArray(option[key])) {
        option[key].forEach((field) => {
          if (field.keyName === newField.keyName) {
            this.$set(field, 'customFieldInfo', extendObj)
          }
        })
      }
    })
  })
}

const adapter = {
  requestKeyFn(el) {
    return 'cartElenmentMultiple'
  },
  request(el) {
    console.log('THIS', this)
    const params = groupRequestParams.call(this, el)
    return params
  },
  renderFns(el, data) {
    Vue.set(el.content, 'card', data)
    const { chartUserConfig = {} } = el.content
    // 保存空值
    const measurementNullValue = data && data[0] ? data[0].measurementNullValue : ''
    Vue.set(chartUserConfig, 'measurementNullValue', measurementNullValue)
    // 保存预警数据
    if (chartUserConfig.warnLineSettingList) {
      let rowData = {}
      Array.isArray(data) && data.forEach(item => {
        const rows = item.rows ? item.rows[0] : {}
        Object.assign(rowData, { ...rows })
      })
      chartUserConfig.warnLineSettingList.forEach(cardWarnLine => {
        cardWarnLine.forEach(line => {
          const calType = ['isNull', 'isNotNull', 'range']
          line.resValue = calType.includes(line.calType) ? '' : rowData[line.id] || ''
        })
      })
    }
  },
  response(response = {}) {
    return response.cartResponseSum
  }
}

// 调后台接口获取数据
async function getElResponse(el) {
  const { response, requestKeyFn, request, renderFns } = adapter
  const param = {
    languageCode: '',
    currency: '',
    tenantId: this.utils.tenantId,
  }
  // 获得元素的参数
  const requestParams = request.call(this, el)
  // 获得元素对应 param 上的 key
  const key = requestKeyFn()
  param[key] = param[key] || []
  param[key].push(requestParams)

  // 添加操作日志字段
  const { id, elName: name } = el
  const { isSubscribe, isRemind, isMobileApp, isMobile } = this.commonData || {}
  const { isLargeScreen, isDataReport } = this.utils || {}
  param.reportLog = updateReportLogObj({
    id,
    name,
    isSubscribe,
    isRemind,
    isMobileApp,
    isMobile,
    isLargeScreen,
    isDataReport,
    type: 'card',
    langCode: this.langCode,
  })

  // 同一个接口, 复用图形api
  const responseParam = await chartPreview(this.utils.api, param)
  const resData = response(responseParam ? responseParam.cartElenmentMultiple[0] : '')
  renderFns(el, resData)
  return resData
}

export default {
  element,
  adapter,
  ElContent,
  ElStyle,
  ElLayout,
  ElStoreLayout,
  groupRequestParams,
  ElStyleConfig,
  getElResponse,
}
