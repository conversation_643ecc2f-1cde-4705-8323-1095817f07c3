<template>
  <div :key="element.id" class="tag-new-card card-hyperlink"  :class="[cardClass, isDarkTheme ? 'loading-background' : '']">
    <ElementSlot :visible="isMobile">
      <slot></slot>
    </ElementSlot>
    <!-- 占位文本 -->
    <div v-if="mask" class="text">
      <div  class="placeholder-text">{{ isPreview ? '' : $t('sdp.views.newCard') }}</div>
      <!-- <loading v-if="cardList.length" :isLoading="loading"></loading> -->
      <waiting v-if="!loading" :isLoading="isPreview && isRenderInteraction"/>
    </div>
    <!-- S == 卡片 -->
    <div v-else class="tag-card" :style="cardNewStyle" @click="superLink($event)">
      <!-- 单/双指标卡片 -->
      <div
        v-if="!isDupontIndicator && !isCompareCard && !isRateCard && !isRankCard"
        class="card-content"
        :class="[`a${element.id}`, element._containerId ? 'container-card-padding' : '', cardType, cardPadding]">
        <div class="tag-wrapper"
          v-for="(item,index) in cardList"
          :key="index"
          @click="superLink($event, index)"
          :style="getIndicatorCardStyle(index)">
          <ElementTitle
            v-if="chartUserConfig.fixedTitle"
            :titleText="item.cardName"
            :subTitleText="item.subCardName"
            :boardInfo="boardInfo"
            :titleStyle="getCardStyle('cardNameStyle', index)"
            :subTitleStyle="getCardStyle('subCardNameStyle', index)"
            :titleClass="getCardNameClass(item, index, !chartUserConfig.fixedTitle)"
            :descriptionText="selectShow ? index === 0 ? chartUserConfig.remark : chartUserConfig.remarkTwo : ''"
            :element="element"
            :isWarning="!!isWarning[index]"
            :parentElement="parentElement"
            :contentItem="item"
            :index="index"
            @eventBus="eventBus"
            :isCombineCard="isCombineCard"
          ></ElementTitle>
          <div class="content-container">
            <div :class="['row-wrapper', chartUserConfig.fixedTitle ? 'fixed-title-content-margin':'']">
              <ElementTitle
                v-if="!chartUserConfig.fixedTitle"
                :titleText="item.cardName"
                :subTitleText="item.subCardName"
                :boardInfo="boardInfo"
                :titleStyle="getCardStyle('cardNameStyle', index)"
                :subTitleStyle="getCardStyle('subCardNameStyle', index)"
                :titleClass="getCardNameClass(item, index, !chartUserConfig.fixedTitle)"
                :descriptionText="selectShow ? index === 0 ? chartUserConfig.remark : chartUserConfig.remarkTwo : ''"
                :element="element"
                :isWarning="!!isWarning[index]"
                :parentElement="parentElement"
                :contentItem="item"
                :index="index"
                @eventBus="eventBus"
                :isCombineCard="isCombineCard"
              ></ElementTitle>
              <div class="middle-area" :class="getMiddleAreaClass(index)" :style="{ 'align-items': showRetoucherIcon('indicator',index) ? 'center' : '' }">
                <img :src="retoucherPictureUrl(index)" alt=""  v-if="showRetoucherIcon('indicator',index)" :style="retoucherIconStyle(index)" class="retoucher-icon-img">
                <div
                  :class="getIndexValueClass(item, index)"
                  v-if="item.dimension.length && item.indexValue"
                  :title="item.indexValue"
                  :style="getCardStyle('indexValueStyle', index)"
                  @click="interActionClickHandler({ columnName: getFieldAlias(item.dimension), dataSetId: item.dataSetId, site: getFieldSite(item.dimension) })">
                  <ScrollNumber
                    v-if="isLargeScreen && chartUserConfig.indexValueAnimation"
                    :value="item.indexValue"
                    :element="element"
                    :customStyle="getCardStyle('indexValueStyle', index)"
                  />

                  <template v-else>
                    <font
                      :class="getGradientClass(item, index)"
                      :style="getCardGradientColorStyle('indexValueStyle', index)"
                    >
                      {{ item.indexValue }}
                    </font>
                  </template>
                </div>
                <div
                  v-if="item.dimension.length && dimensionUnitShow[index] && dimensionUnit[index] && dimensionUnit[index].unit"
                  class="dimension-unit UNITTAG"
                  :class="getDimensionUnitClass(index)"
                  :style="getCardStyle('dimensionUnit', index, 'style')">
                    {{ item.indexValue === chartUserConfig.measurementNullValue ? '' : dimensionUnit[index].unit }}
                </div>
              </div>
              <!-- pc端卡片需要完成率占位区域 -->
              <GrowthCompare :elList="elList" :cardContent="item" :element="element" :index="index" :indexAlignMethod="indexAlignMethod" :isCombineCard="isCombineCard" @eventBus="eventBus" />
            </div>
          </div>
        </div>
        <div class="cutline" v-if="cardList.length && !isShowContent && isShowCuttingLine" :style="{backgroundColor: cuttingLineColor}"></div>
      </div>
      <!-- 比率卡片 -->
      <rate-card v-if="isRateCard"
        class="rate-card-content"
        :class="[cardPadding]"
        ref="RateCard"
        :element="element"
        :elList="elList"
        :boardInfo="boardInfo"
        :parentElement="parentElement"
        :loading="loading"
        :chartUserConfig="chartUserConfig"
        :cardContent="optionArray[0]"
        :rateCardData="rowsData(0)"
        :isPreview="isPreview"
        :selectShow="selectShow"
        @eventBus="eventBus">
      </rate-card>
      <!-- 比较卡片 -->
      <compare-card v-if="isCompareCard"
        :class="[cardPadding]"
        ref="CompareCard"
        :element="element"
        :elList="elList"
        :boardInfo="boardInfo"
        :parentElement="parentElement"
        :loading="loading"
        :chartUserConfig="chartUserConfig"
        :cardContent="optionArray[0]"
        :responseData="rowsData(0)"
        :selectShow="selectShow"
        :isPreview="isPreview"
        @eventBus="eventBus">
      </compare-card>
      <rank-card v-if="isRankCard"
        class="rate-card-content"
        :class="[cardPadding]"
        ref="RankCard"
        :element="element"
        :elList="elList"
        :boardInfo="boardInfo"
        :parentElement="parentElement"
        :loading="loading"
        :chartUserConfig="chartUserConfig"
        :cardContent="optionArray[0]"
        :rateCardData="rowsData(0)"
        :isPreview="isPreview"
        :selectShow="selectShow"
        @eventBus="eventBus">
      </rank-card>

      <!-- 杜邦分析图 -->
      <DupontIndicator
        v-if="cardList.length && isDupontIndicator"
        ref="dupontIndicator"
        :element="element"
        :boardInfo="boardInfo"
        :parentElement="parentElement"
        :node="node"
        :isEditable="isEditable"
        :isCardSet="isCardSet"
        :cardContent="cardList[0]"
        @eventBus="eventBus"
        @expanded-fold-event="$emit('expanded-fold-event', $event)" />

      <div v-if="!isCombineCard && checkPreventReuqst()" :style="dupontIndicatorSize" :class="{ 'dupontIndicator-empty-element-text': isDupontIndicator }" class="placeholder-text empty-element-text"><span>NONE</span></div>
    </div>
  </div>
</template>

<script>
import waiting from 'packages/base/common/waiting'
import bridge from './bridge'
import EventData from 'packages/assets/EventData'
import { TAGNEWCARD, THEME_TYPE, RUN_TYPE, WARNLINE_TYPE_CONNECT_STR } from 'packages/assets/constant'
import { BOARD_ELEMENT_THEME } from 'packages/base/board/displayPanel/supernatant/boardElements/BoardElement.ts'
import { INTERACTION_OPTIONS } from './constant'
import RateCard from './rateCard'
import CompareCard from './compareCard'
import RankCard from './rankCard'
import cardMixin from '../../cardSet/cardMixin'
import { getThemeConfig } from '../../cardSet//colorConfig'
import SdpElInsert from 'packages/directives/v-el-insert'
import { elStyleFormatter } from 'packages/base/board/displayPanel/utils'
import getWarnDataMixin from './getWarnDataMixin'
import { getGlobalParamId } from '../elementChart/constant'
import DupontIndicator from '../elementDupontAnalysis/components/dupontIndicator'
import GrowthCompare from './growthCompare'
import compatibility from './compatibility'
import elementMixin from 'packages/base/board/displayPanel/supernatant/mixins/elementMixin'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import ScrollNumber from './scrollNumber'
import { insetDom } from 'packages/base/common/loading/components/loadingIcon'
import {setElPaddingStyle} from '../../../utils'
import {TYPE_ELEMENT} from "../../../constants";

import ElementSlot from '../components/elementSlot.vue'
import indicatorRetoucherMixin from 'packages/base/board/displayPanel/supernatant/mixins/indicatorRetoucherMixin' // 卡片的指标修饰图

export default {
  name: 'elementTagNewCard',
  inject: {
    commonData: { default: {} },
    themeData: { default: {} },
    utils: { default: {} },
    fullscreenData: { default: {} },
    setLoadingStatus: {
      default: () => () => false
    },
    tenantData: { default: {} },
    isEdit: { default: false }, // 这个注入不能删
    setErrorStatus: {
      default: () => () => false
    },
    boardWarningSubscribeData: { default: {} },
  },
  mixins: [cardMixin, getWarnDataMixin, elementMixin, datasetMixin, indicatorRetoucherMixin],
  components: {
    // loading,
    RateCard,
    waiting,
    CompareCard,
    RankCard,
    GrowthCompare,
    DupontIndicator,
    ScrollNumber,
    ElementSlot
  },
  directives: {
    SdpElInsert,
  },
  props: {
    parentElement: {
      type: Object,
      dfault: () => ({}),
    },
    // 是否模板预览
    isTemplatePreview: {
      type: Boolean,
      default: false,
    },
    // 是否组合卡片
    isCombineCard: {
      type: Boolean,
      default: false,
    },
    // 是否为组合卡片弹窗内卡片
    isCombineDialog: {
      type: Boolean,
      default: false,
    },
    init: {
      type: Boolean,
      default: true,
    },
    // 是否是全屏状态
    fullscreen: {
      type: Boolean,
      default: false,
    },
    boardInfo: {
      type: Object,
      default: () => ({})
    },
    // 只有杜邦分析图会传入节点数据
    node: {
      type: Object,
      default: () => ({})
    },
    isEditable: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      date: new Date(),
      initCard: {
        cardName: '卡片名称',
        rateCompletion: this.$t('sdp.views.complateRate'), // 完成率
        indexValue: this.$t('sdp.views.indexValue'), // 指标值
        rateGrowth: this.$t('sdp.views.growthRate'), // 增长率
      },
      rateCost: this.$t('sdp.views.T_timeParam'),
      title: '',
      superLinkBool: true,
      emptyNewTag: true, // 卡片是否run true-未run
      loading: false,
      nameLimitStyle: [],
      limitCardName: ['', ''], // 标题可显示内容
      selectShow: true,
    }
  },

  computed: {
    isRenderInteraction() {
      return this.$_getProp(this, 'element.elAttr.isRenderInteraction', false)
    },
    dupontIndicatorSize() {
      return {
        width: this.node?.data?.width + 'px', height: this.node?.data?.height + 'px'
      }
    },
    cardSize() {
      if (!this.isCardSet) return {}

      // 杜邦分析图
      if (this.isDupontIndicator && this.node.data) {
        return this.dupontIndicatorSize
      }

      if (this.utils.isScreen) {
        return elStyleFormatter(this.element, this.themeType).fSize
      }
      let { width, height } = this.element.style
      // 容器内取默认值
      if (this.element._containerId) {
        width = '440px'
        height = '150px'
      }
      // 组合卡片单指标取默认值一半
      if (this.isCombineCard) {
        width = this.isSingleIndexCard ? '220px' : '440px'
        height = '150px'
      }

      return { width, height }
    },
    needPadding() {
      return this.element.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD &&
        !this.isCombineCard &&
        !this.isDupontIndicator &&
        this.$attrs.isCardSet === undefined &&
        this.$attrs.isContainer === undefined
    },
    cardNewStyle() {
      let width = this.cardSize.width ? this.cardSize.width : '100%'
      let height = this.cardSize.height ? this.cardSize.height : '100%'
      let top = this.cardSize.height ? '50%' : '0'
      let marginTop = this.cardSize.height ? '-' + parseInt(this.cardSize.height) / 2 + 'px' : 'auto'
      let cardStyle = {
        cursor: this.cursorHard ? 'pointer' : '',
        position: 'relative',
        top: top,
        width: width,
        height: height,
        margin: '0px auto',
        'margin-top': marginTop,
        'max-width': '100%',
        'max-height': '100%',
      }
      // 组合卡片双指标卡片背景色可分开设置
      if (!this.isCombineCard || this.cardType !== TAGNEWCARD.TWOINDICES) {
        cardStyle.background = this.mask ? '' : this.getCardBackgroundColor()
      }
      if (this.isCombineCard) {
        const st = this.getCardContentStyle()
        Object.assign(cardStyle, st)
      }
      if (this.needPadding) {
        return [cardStyle, setElPaddingStyle(this.element)]
      }
      return [cardStyle]
    },
    content() {
      return this.element.content
    },
    optionArray() {
      return this.content.optionArray || []
    },
    chartUserConfig() {
      return this.element.content.chartUserConfig || {}
    },
    // 卡片标题对齐方式
    nameAlignMethod() {
      return this.chartUserConfig.nameAlignMethod || ['center', 'center']
    },
    dimensionUnitShow() {
      return this.chartUserConfig.dimensionUnitShow || []
    },
    dimensionUnit() {
      return this.chartUserConfig.dimensionUnit || []
    },
    // 卡片指标值对齐方式
    indexAlignMethod() {
      const defaultAlign = this.isShowContent ? ['center'] : ['center', 'center']
      return this.chartUserConfig.indexAlignMethod || defaultAlign
    },
    // 增长对比 默认为增长率
    growthCompareMethod() {
      const method = this.chartUserConfig.growthCompare || 'growthRate'
      return method === 'growthRate'
    },
    tagNewCardContent() {
      return this.element.content.tagNewCardContent
    },
    isShowContent() {
      return this.tagNewCardContent === 'singleIndex'
    },
    isDupontIndicator() {
      return this.element.content.elementType === 'dupontIndicator'
    },
    // 卡片内容
    cardList() {
      if (this.isRankCard || this.isRateCard || this.isRankCard) return []
      let { optionArray, responseData } = this.element.content
      if (Array.isArray(optionArray) && responseData) {
        let temp = []
        optionArray.forEach((item, index) => {
          let card = {}
          const notSaveKeys = ['indexValue', 'rateGrowth', 'growthValue', 'rateCompletion', 'isNone', 'classText', 'arrowClass']
          Object.keys(item).forEach(key => {
            !notSaveKeys.includes(key) && (card[key] = item[key])
          })
          responseData[index] && Object.assign(card, responseData[index])
          temp.push(card)
        })
        return temp
      }
      return []
    },
    api() {
      return this.utils.api || function() {}
    },
    isPreview() {
      return  this.commonData.isPreview || !!this.isTemplatePreview
    },
    mask() {
      // 非组合卡片 且未run时显示占位
      return !this.isCombineCard && this.emptyNewTag
    },
    // 卡片唯一标识 组合卡片弹窗内卡片、编辑框内卡片、编辑框外卡片
    cardClass() {
      return this.isCardSet ? `_${this.element.id}_card_set` : this.isCombineDialog ? `_${this.element.id}_combine_dialog` : `_${this.element.id}`
    },
    globalParameterIdList() {
      const globalParameterList = this.tenantData.globalParameterList || []
      return globalParameterList.map(e => e.id)
    },
    // 卡片标题类名
    cardNameClass() {
      return this.cardClass + '_name'
    },
    // 移动端单指标卡片
    mobileSingleCard() {
      return this.isMobile && this.isShowContent
    },
    cardNames() {
      const optionArray = this.element.content.optionArray
      if (Array.isArray(optionArray)) {
        let temp = []
        optionArray.forEach(item => {
          temp.push(item.cardName)
        })
        return temp
      }
      return ''
    },
    // 是否显示分割线
    isShowCuttingLine() {
      return this.chartUserConfig.hasOwnProperty('isShowCuttingLine') ? this.chartUserConfig.isShowCuttingLine : true
    },
    // 是否开启大屏
    themeFullScreen() {
      return this.themeData.themeFullScreen
    },
    // 主题类型
    themeType() {
      return this.themeData.themeType || THEME_TYPE.default
    },
    // 是否显示主题样式  大屏模式、预览、在编辑界面内时显示
    isShowThemeStyle() {
      return this.isCardSet || this.themeFullScreen || this.isPreview
    },
    cardPadding() {
      let paddingClass = 'card-padding'
      const { textWrap, selfAdaption, fixedTitle } = this.chartUserConfig
      if ([TAGNEWCARD.SINGLEINDEX, TAGNEWCARD.TWOINDICES].includes(this.cardType)) {
        if (!selfAdaption && textWrap) {
          paddingClass = 'indicator-card-padding'
        }
        if (this.isCombineCard) {
          paddingClass = this.isDoubleIndexCard ? 'tag-combine-card-double' : 'tag-combine-card'
        }
      }
      if (fixedTitle && this.cardType === TAGNEWCARD.RATECARD) {
        paddingClass = 'rate-card-padding'
      }
      if (this.isMobile && !this.isCombineCard) {
        paddingClass = 'mobile-card-padding'
      }
      return paddingClass
    },
    // 卡片是否可点击
    cursorHard() {
      const superLinkOptions = this.element.content.superLinkOptions || this.parentElement?.content?.superLinkOptions || []
      return this.isPreview && superLinkOptions.length && superLinkOptions.find(item => item.labelBoard.id.includes(this.element.id))
    },
    // 是否为真大屏
    isLargeScreen() {
      return this.utils.isLargeScreen
    },
  },

  watch: {
    'element.content': {
      handler(val, oldval) {
        if (Object.keys(val).includes('card')) {
          this.setCardParams()
        }
      },
      deep: true,
      immediate: true,
    },
    'loading': {
      handler(_loading) {
        if (this.emptyNewTag && _loading) {
          // 看板没有请求后台数据时，不渲染卡片
          this.emptyNewTag = false
        }
      },
      immediate: true,
    },
    fullscreen() {
      // 全屏切换的时候要让tooltip、select等组件重新生成一下
      this.selectShow = false
      this.$nextTick(() => {
        this.selectShow = true
      })
    },
    'commonData.isPreview'() {
      this.refreshEl(
        { ids: [this.element.id], },
        { targetFn: 'refreshElByForce', }
      )
    },
  },

  created() {
    const vm = this
    Object.defineProperty(this.element, 'vm', {
      enumerable: false,
      configurable: true,
      get() {
        return vm
      },
    })
    const { content } = this.element
    const { base64, optionArray = [], chartUserConfig } = content
    // 初始化删除老元素的base64属性
    if (base64) {
      Reflect.deleteProperty(this.element.content, 'base64')
    }
    // 清空卡片请求数据
    if (!this.isCombineCard && this.configs?.type !== 'template') {
      this.$delete(this.element.content, 'responseData')
      this.$delete(this.element.content, 'rateCardResponse')
      this.$delete(this.element.content, 'rankCardResponse')
      Array.isArray(optionArray) && optionArray.forEach(item => {
        const notSaveKeys = ['indexValue', 'rateGrowth', 'growthValue', 'rateCompletion', 'isNone', 'classText', 'arrowClass']
        Object.keys(item).forEach(key => {
          notSaveKeys.includes(key) && delete item[key]
        })
      })
    }
    compatibility.call(this, content)
  },
  mounted() {
    this.initSubCardNameStyle()
    this.csh()
    this.$emit('card-mounted', this.element.id)
  },
  methods: {
    getCardContentStyle() {
      if (this.isCombineCard) {
        if (this.parentElement?.content?.borderRadius) {
          return {
            'border-radius': this.parentElement.content.borderRadius,
            'overflow': 'hidden',
          }
        }
      }
      return {}
    },
    rowsData(index) {
      const responseData = this.content.responseData
      if (!Array.isArray(responseData) || !responseData[index] || !responseData[index].rows) return {}
      return responseData[index].rows[0] || {}
    },
    getIndicatorCardStyle(index) {
      let style = {
        width: this.cardList.length === 2 ? '50%' : '100%',
        ...this.getDoubleIndexPadding(index),
      }
      // 组合卡片双指标卡片背景色可分开设置
      if (this.isCombineCard && this.cardType === TAGNEWCARD.TWOINDICES) {
        style.background = this.mask ? '' : this.getCardBackgroundColor(index)
      }
      return style
    },
    initSubCardNameStyle() {
      const subCardNameStyleExists = this.element?.content?.chartUserConfig?.subCardNameStyle !== undefined
      if (!subCardNameStyleExists) {
        this.$set(this.element.content.chartUserConfig, 'subCardNameStyle', [])
      }
    },
    csh() {
      var chartUserConfig = this.element.content.chartUserConfig
      let defaultAlign
      let defaultName
      // console.log(this.checkRest())
      if (this.checkRest()) {
        defaultAlign = this.isShowContent ? ['right'] : ['left', 'right']
        defaultName = this.isShowContent ? ['left'] : ['left', 'right']
      } else {
        defaultAlign = this.isShowContent ? ['center'] : ['left', 'right']
        defaultName = this.isShowContent ? ['center'] : ['left', 'right']
      }
      const nameAlignMethod = chartUserConfig.nameAlignMethod ? chartUserConfig.nameAlignMethod : defaultName
      const indexAlignMethod = chartUserConfig.indexAlignMethod ? chartUserConfig.indexAlignMethod : defaultAlign
      this.$set(this.element.content.chartUserConfig, 'nameAlignMethod', nameAlignMethod)
      this.$set(this.element.content.chartUserConfig, 'indexAlignMethod', indexAlignMethod)
    },
    // 根据是否有指标值 目标值 决定默认值流程
    // 没有指标值 目标值时 走新的默认流程 否则走老的默认流程
    checkRest() {
      var aLength = this.chartUserConfig.indexCascaderValue ? this.chartUserConfig.indexCascaderValue.length : 0
      var bLength = this.chartUserConfig.targetCascaderValue ? this.chartUserConfig.targetCascaderValue.length : 0
      if (aLength === 0 && aLength === 0) {
        return false
      } else {
        return true
      }
    },
    getElementTitle() {
      let elName = this.element.elName || ''
      if (this.cardType !== TAGNEWCARD.TWOINDICES) {
        const cardContent = this.optionArray[0] || {}
        elName = cardContent.cardName || ''
      }
      return {
        text: elName,
        textStyle: {},
      }
    },
    superLink(event, cardIndex) {
      // this.themeFullScreen 8056 【昆仑】大屏跳转【PC】
      if (this.mask || this.loading || this.fullscreenData.enlargeVisible) return false
      let superLinkOptions = this.element.content.superLinkOptions || this.parentElement?.content?.superLinkOptions

      if (superLinkOptions?.openMethod === '2' && (this.utils.isPcMobile || this.commonData.isMobileDataReport())) {
        return void 'PC转移动端后不能打开弹窗超链接'
      }

      if (superLinkOptions && this.superLinkBool) {
        this.superLinkBool = false
        if (this.isPreview) {
          const options = superLinkOptions.find(item => {
            let superLinkId = this.element.elName
            if (this.cardType === TAGNEWCARD.TWOINDICES) {
              event.stopPropagation()
              return item.labelBoard.id === `${this.element.id}_${cardIndex}` || item.labelBoard.id === this.element.id
            }
            return item.labelBoard.id === this.element.id
          })
          if (options) {
            const eventData = new EventData({
              type: 'setSuperLink',
              target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
              targetFn: 'setSuperLink',
              data: { options },
            })
            this.$emit('eventBus', eventData)
          }
        } else {
          // this.$message.warning(this.$t('sdp.views.bplsClick'))
        }
        this.superLinkBool = true
      }
    },
    dupontIndicatorResponseHandler() {
      if (this.$refs.dupontIndicator) {
        this.$refs.dupontIndicator.isWarning = {}
      }
    },
    // 看板编辑界面需要显示颜色，看板设计界面由外部统一设置背景色
    getCardBackgroundColor(index = 0) {
      let curThemeType = this.themeData?.appThemeType || this.themeType
      if (this.isCombineCard) {
        let cardColor = getThemeConfig(this.element, { prop: 'cardColor', themeType: this.themeType, i: index })
        // 深邃蓝主题只存在默认色，设置颜色保存到暗黑蓝主题下
        let deepBlueColor = cardColor && cardColor !== '#212D3B' ? cardColor : '#263A61'
        // object 为新需求渐变色参数
        if (typeof cardColor === 'object') {
          if (cardColor.fillColorType === '' || cardColor.fillColorType === 'pure') {
            cardColor = cardColor.fillColor
          } else {
            cardColor = `linear-gradient(${cardColor.fillGradientAngle}deg, ${(cardColor.gradientColor || []).join(',')})`
          }
        }
        // return curThemeType === THEME_TYPE.deepBlue ? deepBlueColor : cardColor
        return curThemeType === THEME_TYPE.deepBlue && (!cardColor || cardColor === '#212D3B') ? deepBlueColor : cardColor
      } else {
        if (this.utils.isScreen && !this.utils.isMobile) {
         return elStyleFormatter(this.element, this.themeType).background
        }
        const themeMap = this.element.themeMap
        if (!this.isCardSet || !themeMap) return ''
        const path = this.isMobile ? 'mobileBgc' : 'pcBgc'
        const cardColor = themeMap[curThemeType][path]
        return typeof cardColor === 'object' ? BOARD_ELEMENT_THEME[curThemeType][path] : cardColor
      }
    },
    getDoubleIndexPadding(index = 0) {
      // 组合卡片双指标的padding需要设置在对应指标上
      if (!this.isDoubleIndexCard || !this.isCombineCard) return ''
      return index === 0 ? { 'padding-left': '8px' } : { 'padding-right': '8px' }
    },
    // 设置卡片名称
    setCardName(val) {
      val.forEach((item, index) => {
        this.cardList[index].cardName = item
      })
    },
    setCardParams() {
      const card = this.element.content.card
      const cardResponseData = []
      Array.isArray(card) && card.forEach((item, index) => {
        // 当rows为空时，显示空值
        const measurementNullValue = item.hasOwnProperty('measurementNullValue') ? item.measurementNullValue : ''
        if (!cardResponseData[index]) { cardResponseData[index] = {} }
        Object.assign(
          cardResponseData[index],
          {
            rows: item.rows,
            hiddenKeys: item.hiddenKeys || [],
            dataMaskRuleParamMap: item.dataMaskRuleParamMap || {},
          }
        )
        // 单双指标卡片返回数据结构与其他卡片不一致
        if ([TAGNEWCARD.SINGLEINDEX, TAGNEWCARD.TWOINDICES].includes(this.cardType)) {
          const rateCompletionNum = item.rows && item.rows.length ? Number(item.rows[0]['VFMT_RAW_rateCompletion']) * 100 : 0
          Object.assign(cardResponseData[index], {
            indexValue: item.indexValue || measurementNullValue,
            rateGrowth: item.rateGrowth || measurementNullValue,
            growthValue: item.growthValue || measurementNullValue,
            rateCompletion: item.rateCompletion || measurementNullValue,
            rateCompletionNum: rateCompletionNum < 0 ? 0 : rateCompletionNum > 100 ? 100 : rateCompletionNum || 0,
          })
          this.setClass(index, cardResponseData[index])

          if (!this.chartUserConfig.isShowNegativeSign) {
            ['rateGrowth', 'growthValue'].forEach(key => {
              let growth = item[key]
              if (growth && growth !== measurementNullValue) {
                cardResponseData[index][key] = growth.includes('(') ? `${growth.replace('(', '').replace(')', '')}` : `${growth.replace('-', '')}`
              }
            })
          }
        }
      })
      this.emptyNewTag = false
      // this.rateCost = 'same days on last period'
      this.$set(this.element.content, 'responseData', this.$_JSONClone(cardResponseData))
      delete this.element.content.card
    },
    // 千分位兼容保留小数 默认保留两位
    growthTrans(data, num = 2) {
      let _data = data
      // 去除千分位
      let metadata = data.split(',').join('')
      // 当数值为空值时，返回-
      if (isNaN(metadata)) return '-'
      // let afterDecimalPoint = metadata.split('.')[1]
      // 不存在小数点或小数点为1位时返回原数值
      // if (!afterDecimalPoint || afterDecimalPoint.length <= num) return _data
      // 保留小数点后两位，并增加千分位
      metadata = parseFloat(metadata).toFixed(num).replace(/\d{1,3}(?=(\d{3})+(\.|$))/g, '$&,')
      return metadata
    },
    // 千分位转换 el-progress组件使用
    growthTransInt(data, num = 0) {
      // 去除千分位
      let metadata = data.split(',').join('')
      // 当数值为空值时，返回-
      if (isNaN(metadata)) return 0
      return Number(parseFloat(metadata).toFixed(num))
    },
    // 增长对比 增长率显示百分比 增长值显示具体数值
    setClass(index, responseData) {
      console.log('element', this.element)
      let { rateGrowth, growthValue } = responseData
      let growth = this.growthCompareMethod ? rateGrowth : growthValue
      if (!growth) return
      // 返回值带括号 或 返回值带负号（空值也可能为 - ，需要判断growth长度）
      const isNegative = growth.includes('(') || (growth.includes('-') && growth.length > 1)
      this.$set(responseData, 'isNegative', isNegative)
    },
    // 判断卡片为空
    checkPreventReuqst() {
      let preventRequest = false
      if (!this.content.optionArray || !this.content.optionArray.length) return true
      // const dimension = []
      // const ratioValue = []
      // const compareValue = []
      // this.content.optionArray.map(item => {
      //   // 比率卡片存在维度、比率值、对比值其中一个数据即可；单/双指标卡片必须存在维度数据
      //   item.dimension && item.dimension.length && dimension.push(...item.dimension)
      //   item.ratioValue && item.ratioValue.length && ratioValue.push(...item.ratioValue)
      //   item.compareValue && item.compareValue.length && compareValue.push(...item.compareValue)
      // })
      // if ((this.isRateCard && !dimension.length && !ratioValue.length && !compareValue.length) || (!this.isRateCard && !dimension.length)) {
      //   preventRequest = true
      // }
      return preventRequest
    },
    requestAdapter() {
      // 卡片为空时不请求数据
      if (this.checkPreventReuqst()) {
        this.emptyNewTag = false
        return false
      }
      const { request, requestKeyFn } = bridge.adapter
      console.assert(request && requestKeyFn)
      const requestParams = request.call(this, this.element)
      const requestKey = requestKeyFn()
      this.setElementLoadState(true)
      const requestData = requestParams ? {
        requestKey,
        requestParams
      } : null
      return requestData
    },
    responseAdapter(res) {
      this.setElementLoadState(false)
      if (res.id === this.element.id) {
        const refNames = ['RateCard', 'CompareCard', 'RankCard']
        // 处理后的响应数据
        this.isWarning = {}
        refNames.forEach(refName => {
          if (this.$refs[refName]) {
            this.$refs[refName].isWarning = {}
          }
        })
        const resData = bridge.adapter.response(res)
        bridge.adapter.renderFns(this.element, resData)
      }
    },
    // 交互设置拼装数据
    getInteractionData() {
      let cardContent = this.optionArray[0]
      let arr = []
      if (this.isCompareCard) {
        let { compareValue = [], dimension = [] } = cardContent
        arr.push(...compareValue, ...dimension)
      } else if (this.isRankCard) {
        let { rankValue = [], dimension = [] } = cardContent
        arr.push(...rankValue, ...dimension)
      } else if (this.isRateCard) {
        let { ratioValue = [], compareValue = [], dimension = [] } = cardContent
        arr.push(...ratioValue, ...compareValue, ...dimension)
      } else {
        const _optionArray = this.$_deepClone(this.optionArray)
        _optionArray.forEach((list, index) => {
          let { metrics = [], dimension = [] } = list
          if (_optionArray.length > 1) {
            metrics.map(item => {
              let tagTitle = this.$t('sdp.views.complateRateRate', { prop: index + 1 })
              item.cardName = tagTitle
            })
            dimension.map(item => {
              let tagTitle = this.$t('sdp.views.indexTag', { prop: index + 1 })
              item.cardName = tagTitle
            })
          }
          arr.push(...metrics, ...dimension)
        })
      }
      let { growthCompareSetting, growthCompare } = this.chartUserConfig
      let growthOptions = {}
      let growthTwoOptions = {}
      let data = arr.reduce((list, item) => {
        let { parentId, labeName, alias, keyName, cardName } = item
        let existItem = list.find(cur => cur.id === parentId) || {}
        if (!existItem?.fieldList?.length) list.push(existItem)
        existItem.id = parentId
        existItem.fieldList = existItem.fieldList || []
        existItem.fieldList.push({
          webFieldType: item.webFieldType,
          label: alias || labeName,
          value: labeName,
          site: keyName || alias || labeName,
          cardName
        })
        // 比率卡片和比较卡片没有增长率增长值
        if (growthCompareSetting && existItem.fieldList.length === arr.length && !this.isCompareCard && !this.isRateCard) {
          growthOptions = {
           label: growthCompare === 'growthRate' ? this.$t('sdp.views.growthRate') : this.$t('sdp.views.growthValue'),
           value: `${INTERACTION_OPTIONS.growthValue}_0`,
           site: `${INTERACTION_OPTIONS.growthValue}_0`,
         }
         if (this.optionArray.length > 1) {
           growthOptions.cardName = this.$t('sdp.views.indexTag', { prop: 1 })
           growthTwoOptions = {
             label: growthCompare === 'growthRate' ? this.$t('sdp.views.growthRate') : this.$t('sdp.views.growthValue'),
             value: `${INTERACTION_OPTIONS.growthValue}_1`,
             site: `${INTERACTION_OPTIONS.growthValue}_1`,
             cardName: this.$t('sdp.views.indexTag', { prop: 2 })
           }
         }
         existItem.fieldList.push(growthOptions)
          growthTwoOptions?.value && existItem.fieldList.push(growthTwoOptions)
        }
        return list
      }, [])
      console.log(data, '交互设置拼装数据')
      return data.filter(e => e.id)
    },
    // 收集使用的全局参数id return ['id1', 'id2']
    getElementGlobalParamIdList() {
      // 存在自定义计算的字段：指标值、目标值、对比值、比率值、
      const optionArray = this.content.optionArray || []
      const metricList = {
        [TAGNEWCARD.SINGLEINDEX]: ['dimension', 'metrics'],
        [TAGNEWCARD.TWOINDICES]: ['dimension', 'metrics'],
        [TAGNEWCARD.RATECARD]: ['dimension', 'ratioValue', 'compareValue'],
        [TAGNEWCARD.COMPARECARD]: ['dimension', 'compareValue'],
        [TAGNEWCARD.RANKCARD]: ['dimension'],
      }
      const cardMetricList = metricList[this.tagNewCardContent]
      let metric = []
      optionArray.forEach(cardContent => {
        cardMetricList.forEach(key => {
          cardContent[key] && metric.push(...cardContent[key])
        })
      })
      const ids = getGlobalParamId(metric.map(m => this.getCustomFieldPreview(m)), this.globalParameterIdList)
      console.log('使用的全局参数:', ids)
      return Array.from(new Set(ids))
    },
    // 对外提供清空卡片返回数据方法
    deleteElementData() {
      this.emptyNewTag = true
      this.setElementLoadState(false)
      this.$delete(this.element.content, 'responseData')
      this.$delete(this.element.content, 'rateCardResponse')
    },
    // 对外提供控制loading
    setElementLoadState(loading) {
      if (this.commonData.isOffApiLoading) return '关闭loading'
      if (this.loading === loading) return
      this.loading = loading
      this.elDom = (this.isCardSet || this.themeFullScreen || this.fullscreenData.enlargeVisible) ? this.$el : (document.getElementById(this.element.id) || this.$el)
      if (this.setLoadingStatus(loading, this.element.id)) {
        return
      }
      this.loadingInstance && this.loadingInstance.close()
      if (loading && this.elDom) {
        this.loadingInstance = this.$loading({
          target: this.elDom,
          spinner: 'sdp-loading-gif',
        })
        insetDom(this.loadingInstance.$el)
      }
    },
    // 对外提供error 提示
    setElementErrorState(error) {
      this.setErrorStatus(error, this.element.id)
    },
    // 获取卡片类名 placeholder-占位区域标识
    getCardNameClass(item, index, placeholder) {
      const nameAlign = this.nameAlignMethod[index]
      let nameClass = ['text-' + nameAlign, this.cardNameClass]
      // 未设置标题样式时，默认标题加粗
      // const cardNameStyle = this.getCardStyle('cardNameStyle', index)
      // if (!cardNameStyle || !cardNameStyle.hasOwnProperty('font-weight')) {
      //   nameClass.push('font-bold')
      // }
      // 单指标卡片、完成率存在且标题非居中对齐时，设置标题最大宽度
      if (this.isShowContent && item.metrics.length && nameAlign !== 'center') {
        nameClass.push('max-width')
      }
      if (this.isShowContent && nameAlign === 'right' && item.metrics.length) {
        nameClass.push('card-name-right')
      }
      // 固定标题样式
      if (this.chartUserConfig.fixedTitle && !placeholder) {
        nameClass.push('vertical-top')
        if (this.themeFullScreen) {
          nameClass.push('full-screen-fixed-title')
        }
        // 开启了固定标题，并且开启了指标修饰符
        if(this.showRetoucherIcon('title')){
          nameClass.push('vertical-top-retoucher')
        }
      }
      if (this.isMobile) {
        nameClass.push('card-name-mobile')
      }
      return nameClass
    },
    getMiddleAreaClass(index) {
      let nameClass = []
      if (this.dimensionUnitShow[index] || this.element?.content?.optionArray[index]?.retoucher?.visible) {
        nameClass.push('middle-area-exist-unit')
        nameClass.push('middle-area-' + this.indexAlignMethod[index])
      }
      if(this.element?.content?.optionArray[index]?.retoucher?.visible && this.isMobile){
          nameClass.push('middle-area-retoucher-mobile')
      }
      return nameClass
    },
    // 获取卡片可设置预警条件字段别名 ['ratioValue', 'compareValue', 'dimension']
    getMetricList() {
      const optionArray = this.content.optionArray
      const keys = ['ratioValue', 'compareValue', 'dimension']
      let metricsList = []
      Array.isArray(optionArray) && optionArray.forEach(cardContent => {
        keys.forEach(key => {
          if (cardContent[key]) {
            metricsList.push(...cardContent[key].map(e => e.alias || e.labeName))
          }
        })
      })
      return metricsList
    },
    // 获取引用的自定义字段
    getCustomFieldsUsedInElement(webFieldTypes = ['customComputed']) {
      if (webFieldTypes[0] === 'ALL') webFieldTypes = ['metricGroup', 'customComputed']
      const result = []
      const fieldObj = {
        [TAGNEWCARD.SINGLEINDEX]: ['dimension', 'metrics'],
        [TAGNEWCARD.TWOINDICES]: ['dimension', 'metrics'],
        [TAGNEWCARD.RATECARD]: ['dimension', 'ratioValue', 'compareValue'],
        [TAGNEWCARD.COMPARECARD]: ['dimension', 'compareValue'],
        [TAGNEWCARD.RANKCARD]: ['diemsnion', 'rankValue'],
      }
      const { tagNewCardContent = '', optionArray = [] } = this.element.content
      const cardProps = fieldObj[tagNewCardContent] || []
      ;(optionArray || []).forEach(option => {
        cardProps.forEach(prop => {
          if (Array.isArray(option[prop])) {
            option[prop].forEach(field => {
              if (webFieldTypes.includes(field?.webFieldType) && !result.find(f => f.id === field.id)) {
                result.push({
                  parentId: field.parentId,
                  id: field.id,
                  webFieldType: field.webFieldType
                })
              }
            })
          }
        })
      })
      return result
    },
  },
}
</script>

<style lang="scss" scoped>
.tag-new-card {
  line-height: 1.5; // OMS设置了line-height全局样式，与OMS保持一致
  height: 100%;
  width: 100%;
  /deep/ .el-tooltip__popper[data-sdp-el-insert='el-tooltip'],  /deep/ .el-tooltip__popper[data-sdp-el-insert-firefox='el-tooltip']{
    position: absolute !important;
  }
}
[iscardset], [iscontainer], .default-padding, .combine-card {
  .card-padding {
    padding: 12px 18px 22px 18px;
  }
  .indicator-card-padding {
    padding: 12px 18px 17px 18px;
  }
  .rate-card-padding {
    padding: 12px 18px 10px 18px;
  }
  .mobile-card-padding {
    padding: 0px 18px 0px 18px;
  }
}
.tag-card {
  // NOTICE:注释掉防止loading不能居中
  display: flex;
  display: -webkit-flex;
  align-items:center;
  justify-content:center;
  // min-width: 287px;
  // min-height: 150px;
  height: 100%;
  .card-padding {
    /*padding: 12px 18px 22px 18px;*/
  }
  .indicator-card-padding {
    /*padding: 12px 18px 17px 18px;*/
  }
  .rate-card-padding {
    /*padding: 12px 18px 10px 18px;*/
  }
  .mobile-card-padding {
    /*padding: 0px 18px 0px 18px;*/
  }
  .mask{
    background-color: var(--sdp-gjys-bjs) !important;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 3;
  }
  .card-content {
    display: flex;
    display: -webkit-flex;
    align-items:center;
    justify-content:center;
    height: 100%;
    width: 100%;
    /deep/ {
      .element-title-container{
        padding-right: 4px;
      }
    }
    /deep/ .vertical-top{
      width: 100%;
      line-height: 16px !important;
      margin-top: 4px;
      &.card-name-mobile{
        padding-top: 7px;
      }
    }
  }
  /deep/ .warningBell-position{
    position: absolute;
    right: 0;
    top: 0;
    z-index: 10;
  }
  .tag-wrapper{
    text-align: left;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    .content-container {
      display: flex;
      align-items: center;
      flex: 1;
    }
    .row-wrapper{
      width: 100%;
      .row-center{
        @include themeMixin('cardIndexValueFont');
        font-family: Roboto-Regular;
        font-size: 32px;
        letter-spacing: 0.67px;
        line-height: 36px;
        height: 36px;
        padding-right: 4px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .row-center-mobile{
        @include themeMixin('cardIndexValueFont');
        margin-bottom: 5px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-family: Roboto-Regular;
        font-size: 26px;
        letter-spacing: 0px;
        line-height: 36px;
        height: 36px;
        padding-right: 4px;
      }
      .combine-card-row-center{
        margin: 0 0 5px 0;
      }
      .result {
        color: #B3B3B3;
        vertical-align: top;
        > span{
          font-family: Roboto-Regular;
          font-size: 12px;
          letter-spacing: 0.1px;
        }
        .growth-compare-text {
          color: var(--sdp-fx-mrwzs);
          font-family: 'Roboto-Regular';
          font-size: 12px;
        }
        .growth-compare-value {
          display: inline-flex;
          align-items: baseline;
          &:not(.row-reverse) .complation-text {
            padding-left: 2px;
          }
          &.row-reverse .complation-text {
            padding-right: 2px;
          }
        }
        .row-reverse {
          flex-direction: row-reverse;
        }
      }
      .rate-completion {
        vertical-align: top;
        > span {
          font-size: 12px;
          font-family: Roboto-Regular;
          padding-right: 3px;
          @include themeMixin('cardCompletRateFont');
        }
      }
    }
  }
  .twoIndeces {
    .tag-wrapper:nth-of-type(1){
      padding-right: 10px;
    }
    .tag-wrapper:nth-of-type(2){
      padding-left: 10px;
      text-align: right;
      .result{
        position: relative;
      }
    }
  }
  .tag-bottom{
    font-family: Roboto-Regular;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 10px;
    text-align: center;
    color: #B3B3B3;
  }
  .card-name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: Roboto-Regular;
    font-size: 14px;
    color: var(--sdp-fx-mrwzs);
    padding-right: 4px;
    // > span {
    //   vertical-align: middle;
    // }
  }
  .card-name-area {
    height: 38px;
    line-height: 38px !important; // 兼容老数据,以前在cardNameStyle中存在line-height
  }
  .full-screen-fixed-title {
    margin-top: -6px;
  }
  .vertical-top{
    width: 100%;
    line-height: 16px !important;
    margin-top: 4px;
    &.card-name-mobile{
      padding-top: 7px;
    }
  }
}
.flex-column {
  flex-flow: column;
}
.rate-completion-right {
  position: absolute;
  top: 26px;
  right: 10px;
}
.rate-completion-left {
  position: absolute;
  top: 26px;
  left: 10px;
}
.text-bottom{
  text-align: center;
  font-family: Roboto-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #B3B3B3;
  letter-spacing: -0.05px;
  line-height: 12px;
  position: absolute;
  bottom: 15px;
  left: 20px;
  right: 20px;
}
.text-right{
  text-align: right;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.none{
  display: none;
}
.complation-text {
  font-family: Roboto-Regular;
  font-size: 12px;
  display: inline-block;
}
.tag-card-two-mobile{
  padding: 12px 18px 22px 18px !important;
}
.tag-combine-card {
  padding: 0px 8px !important;
}
.tag-combine-card-double {
  padding: 0 !important;
}
.cursor-pointer{
  cursor: pointer;
  text-decoration: underline;
  font{
    text-decoration: underline;
  }
  font:hover{
    background-color: var(--cursor-pointer-background);
    border-radius: 8px;
  }
}
.cursor-pointer-active{
  font{
    background-color: var(--cursor-pointer-background);
    border-radius: 8px;
  }
}
// .cursor-pointer:hover{
//   background-color: rgba(0, 0, 0, 0.1);
// }
.text-wrap /deep/ {
  .el-progress {
    position: relative;
    top: 3px;
  }
}
/deep/ .el-progress {
  margin-right: 3px;
  .el-progress__text {
    display: none;
  }
  .el-progress-circle__track {
    stroke: #e5e9f2;
  }
  &.dark-theme-progeress {
    .el-progress-circle__track {
      stroke: #1F222C;
    }
  }
}
.placeholder-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
      color: var(--sdp-srk-bxwzs);
}
.dupontIndicator-empty-element-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.text {
  font-size: 16px;
  color: #999999;
}
.loading-background {
  /deep/ .loading-mask {
    background-color: rgba(33, 45, 59, .9);
  }
}
.empty-element-text {
  color: #999;
  font-size: 36px;
}
.font-bold {
  font-weight: bold;
}
.max-width {
  max-width: 100%;
}
.card-name-right {
  padding-left: 20%;
}
.completion-combine-card {
  margin-top: 0;
}
.cutline {
  position: absolute;
  width: 1px;
  background-color: #EEEEEE;
  height: 48px;
  margin-top: 3px;
  left: 50%;
}
.description {
  color: var(--sdp-zs);
  vertical-align: middle;
  margin-left: 8px;
  font-weight: normal;
  font-size: 12px;
}
.center-tooltip {
  position: absolute;
  right: -19px;
}
.mobile-growth-value {
  position: absolute;
  bottom: 54px;
  left: 20px;
  right: 20px;
}
.middle-area-exist-unit {
  display: flex;
  overflow: hidden;
  align-items: baseline;
  height: 36px;
  .dimension-unit {
    white-space: nowrap;
    line-height: 36px;
  }
}
.middle-area-retoucher-mobile{

  .UNITTAG{
    margin-bottom: 5px;
  }
  .retoucher-icon-img{
    margin-bottom: 5px;
  }
}
.middle-area-left {
  justify-content: flex-start;
}
.middle-area-right {
  justify-content: flex-end;
}
.middle-area-center {
  justify-content: center;
}
.middle-area {
  @include themeMixin('cardIndexValueFont');
  margin-top: 6px;
  div:last-child {
    padding-right: 4px;
  }
}
</style>
