<template>
  <div class="rich-text" :class="[isSetting ? 'element-text-dialog' : '']" tabindex="-1" @blur="hiddenMenu">
    <div
      ref="richTextContent"
      class="rich-text-content"
      :style="{...richTextStyle, ...textWhileSpace, ...textPaddingRight, ...customStyle, ...customStyleVar }"
      :contenteditable="isSetting"
      :class="richTextClass"
      @input="changeRichText"
      @mouseup="handleMouseup"
      @keydown="handleKeyDown"
      @contextmenu="handleContextMenu"
      @mousedown="handleMousedown"
      @scroll="handleScroll"
      @focus="$emit('currnetRichText')"
    ></div>
    <div v-show="tips" class="tips">{{ tips }}</div>
    <!-- 数据字段 -->
    <div class="filed" v-if="showField" :style="filedStyle">
      <ul v-if="filedList.length">
        <li v-for="item in filedList" :key="item.uniqueKey" @click="addField(item)">{{ isLocaleSetting ? (dragcascaderValue[item.uniqueKey].alias || getUnknownName(item.parentId, item.labeName)) : getUnknownName(item.parentId, item.labeName) }}</li>
      </ul>
      <div v-else>{{ $t('sdp.views.noFiledData') }}</div>
    </div>
    <!-- 度量函数 -->
    <CascaderMenu
      v-if="showCascaderMenu"
      :item="currentCascader"
      :dragList="[currentCascader]"
      :cascaderMenuStyle="cascaderMenuStyle"
      :element="element"
      :datasetList="datasetList"
      @selectMess="dragCascaderSelect"
      @alias-change="changeAlias"
      @close-cascader-menu="showCascaderMenu = false"
    />
  </div>
</template>

<script>
import CascaderMenu from './cascaderMenu'
import Vue from 'vue'
import earlyWarningMixin from 'packages/base/board/displayPanel/supernatant/boardElements/elementText/components/earlyWarning/mixin'
import { isCanInteraction } from '../../../utils'

export default {
  mixins: [earlyWarningMixin],
  props: {
    // 富文本框最大长度提示
    maxLength: {
      type: Number,
      default: 0
    },
    // 为富文本整体内容添加的样式
    customStyle: {
      type: Object,
      default: () => {},
    },
    // 传入类型不同，使用不同逻辑
    logicType: {
      type: String,
      default: 'default' // default | earlyWarning
    },
    // 不同逻辑需要用到的数据
    logicTypeData: {
      type: Object,
      default: () => {}
    },
    isSetting: {
      type: Boolean,
      default: false,
    },
    // 标识是否用于多语言设置
    isLocaleSetting: {
      type: Boolean,
      default: false,
    },
    element: {
      type: Object,
    },
    datasetList: {
      type: Array,
      default: () => [],
    },
    selectedDatasetId: {
      type: String,
    },
    htmlField: {
      type: String,
      default: 'richTextHtml'
    },
    // 'locales.en' 数据区间, 比如有content下的和多语言下的
    scope: {
      type: String,
      default: 'content'
    },
    isActive: {
      type: Boolean,
      default: false
    },
  },
  inject: {
    themeData: { default: {} },
    fullscreenData: { default: {} },
    getUnknownName: { default: () => (datasetId, labeName) => labeName },
  },
  components: {
    CascaderMenu,
  },
  data() {
    return {
      tips: '',
      showField: false,
      anchorPosition: {}, // 锚点位置
      containerPadding: 10, // 组件padding，计算位置时需要加上
      currentCascader: {},
      showCascaderMenu: false,
      cascaderMenuStyle: {},
      currRange: '',
      oldTarget: null
    }
  },
  created() {
    this.deleteRichTextContent = this.$_debounce(this.deleteRichTextContent, 100)
    this.changeRichText = this.$_debounce(this.changeRichText, 200)
    this.regainStyle = this.$_debounce(this.regainStyle, 100)
  },
  mounted() {
    this.initData('mounted')
    this.$el.addEventListener('copy', this.handleCopy)
    // 添加全局鼠标事件监听
    document.addEventListener('mouseup', this.handleGlobalMouseup)
  },
  computed: {
    customStyleVar () {
      return {
        '--rich-text-anchor-measure-text-decoration': this.customStyle?.['text-decoration'] || 'none',
        '--rich-text-anchor-measure-font-style': this.customStyle?.['font-style'] || 'normal'
      }
    },
    themeType() {
      return this.themeData.themeType || 'sdp-classic-white'
    },
    richTextClass() {
      return this.isSetting ? `${this.element.id}_isDialog_${this.scope}_${this.htmlField}` : `${this.element.id}_${this.scope}_${this.htmlField}`
    },
    textStyle() {
      // TODO 需要处理大屏预览字体缩放 this.isChartLarge ? { 'font-size': this.$_getPxOfHighResolution(textStyle['font-size']) } : {},
      return this.contentScope?.textStyle || {}
    },
    titleStyle() {
      return this.contentScope?.titleStyle || {}
    },
    orderStyle() {
      return this.contentScope?.orderStyle || {}
    },
    richTextStyle() {
      return this.htmlField === 'titleHtml' ? this.titleStyle : this.htmlField === 'orderHtml' ? this.orderStyle : this.textStyle
    },
    isChartLarge() {
      return this.fullscreenData.enlargeVisible
    },
    textPaddingRight() {
      if (this.isSetting) return {}
      let right = 0
      if (this.textStyle['font-style']) {
        let fontSize = parseInt(this.textStyle['font-size'])
        switch (fontSize) {
          case 72:
            right = 16
            break
          case 48:
            right = 10
            break
          case 36:
            right = 8
            break
          case 28:
          case 26:
          case 24:
            right = 6
            break
          case 22:
          case 20:
          case 18:
            right = 4
            break
          default:
            right = 2
            break
        }
      }
      return { 'padding-right': `${right}px` }
    },
    textWhileSpace() {
      if (this.isSetting) return {}
      let agent = navigator.userAgent.toLowerCase()
      return { 'white-space': agent.indexOf('firefox') > 0 && this.textStyle['text-align'] === 'justify' ? 'pre-line' : 'pre-wrap' }
    },
    filedList() {
      // 多语言编辑,选择的是已被引用的字段列表
      if (this.isLocaleSetting) return this.referenceField
      const textDataset = this.datasetList.find(item => item.id === this.selectedDatasetId)
      return textDataset?.children || []
    },
    filedStyle() {
      return Object.keys(this.anchorPosition).length ? {
        top: this.anchorPosition.top + 'px',
        left: this.anchorPosition.left + 'px',
      } : {}
    },
    // 引用数据集字段
    referenceField() {
      if (this.logicType === 'earlyWarning') {
        return this.logicTypeData?.richTextEl?.content?.referenceField || []
      }
      return this.element.content?.referenceField || []
    },
    dragcascaderValue() {
      if (this.logicType === 'earlyWarning') {
        return this.logicTypeData?.richTextEl?.content?.dragcascaderValue || []
      }
      return this.element.content?.dragcascaderValue || {}
    },
    textResponse() {
      return this.element.content?.textResponse || {}
    },
    // 区间对象, scope默认为 content, 所以默认的区间对象为 this.element.content
    // 传入 locales.en 时,区间对象为 this.element.content.locales.en
    contentScope() {
      const arr = this.scope.split('.')
      if (arr.length === 1) {
        return this.element[arr[0]] || {}
      }
      let o = this.element
      for (let i = 0; i < arr.length; i++) {
        o = o[arr[i]]
        if (!o) return {}
      }
      return o
    },
  },
  watch: {
    'element.content.interactionOptions'(val, oldVal) {
      if (val !== oldVal) {
        this.addCursorPointer()
      }
    }
  },
  methods: {
    handleCopy(event) {
      // 如果选中的是字段中的内容，则要将复制内容替换为整个字段
      const clipboardData = event.clipboardData || window.clipboardData
      const parent = window?.getSelection()?.getRangeAt(0)?.commonAncestorContainer?.parentNode
      if (parent?.className === 'anchor-measure') {
        const dom = document.createElement('div')
        dom.appendChild(parent.cloneNode(true))
        clipboardData.setData('text/html', dom.innerHTML)
        event.preventDefault()
      }
    },
    getRichTextDom() {
      const richTextDom = this.$refs.richTextContent
      return [richTextDom]
    },

    initData(type) {
      // const richTextDom = document.getElementsByClassName(this.richTextClass)
      const richTextDom = this.getRichTextDom()

      if (!richTextDom[0]) return
      if (type === 'changeElementTextStatus') {
        richTextDom[0].innerHTML = '<p><br></p>'
        richTextDom[0].focus()
        return
      }
      const richTextContent = this.transformRichTextHtml(this.contentScope?.[this.htmlField], this.isSetting ? '1' : '2')
      richTextDom[0].innerHTML = richTextContent

      // 文本框innerHTML更新后，需要重新绑定字段点击事件
      this.bindFieldEvent(richTextDom[0])

      if (type === 'openDialog') {
        this.hiddenMenu()
      }
      if (type === 'mounted') {
        document.execCommand('defaultParagraphSeparator', false, 'p')
      }
      this.$nextTick(() => {
        this.setTips()
        this.addCursorPointer()
      })
    },
    handleRepeateFields(dom, oldHtml) {
      let newHtml = dom.innerHTML
      if (oldHtml === newHtml || this.isLocaleSetting) return
      const ids = Object.keys(this.dragcascaderValue)
      ids.forEach(id => {
        const oldIndex = oldHtml.indexOf(`data-id="${id}"`)
        console.log('老的位置', oldIndex)
        // 如果长度 > 2 说明就有重复的
        const newIdSplits = newHtml.split(`data-id="${id}"`)
        if (newIdSplits.length > 2) {
          const newIndex = newHtml.indexOf(`data-id="${id}"`)
          console.log('新的位置：', newIndex)
          const isBefore = newIndex > oldIndex
          console.log('插在 ->', isBefore ? '前面' : '后面')
          const _fragment = document.getElementsByClassName(this.richTextClass)[0]
          const nodes = _fragment.querySelectorAll(`span[data-id=${id}]`)
          const node = isBefore ? nodes[0] : nodes[nodes.length - 1]
          // 类型新增字段的逻辑
          const newFieldId = this.$_generateKeyName('filed')
          node.setAttribute('data-id', newFieldId)
          let cascaderValue = this.$_JSONClone(this.dragcascaderValue[id])
          let newField = this.$_JSONClone(this.referenceField.find(f => f.uniqueKey === id))
          cascaderValue.uniqueKey = newFieldId
          this.referenceField.push({ ...newField, uniqueKey: newFieldId })
          this.dragcascaderValue[newFieldId] = cascaderValue
        }
      })
      this.deleteRichTextContent()
    },
    bindFieldEvent(dom) {
      // 给每个字段绑定点击事件
      const fieldDom = dom.querySelectorAll('.anchor-measure')
      console.log(fieldDom, 'fieldDom')

      fieldDom.forEach(e => {
        e.removeEventListener('click', this.clickAnchorMeasure)
        e.addEventListener('click', this.clickAnchorMeasure)
      })
    },
    // 转换富文本框html type: 1-转成显示维度、度量字段 2-显示数据 3-维度度量字段转成字段id
    transformRichTextHtml(html, type) {
      if (!html) return ''
      if (!this.referenceField.length) return html

      let fragment = document.createDocumentFragment()
      let virtualDom = document.createElement('div')
      virtualDom.innerHTML = html
      fragment.appendChild(virtualDom)
      const rows = this.textResponse?.rows?.[0] || {}

      this.referenceField.forEach(item => {
        const cascader = this.dragcascaderValue[item.uniqueKey]
        const fieldDoms = fragment.querySelectorAll(`span[data-id=${item.uniqueKey}]`)
        if (!fieldDoms?.length) return
        Array.from(fieldDoms).forEach(fieldDom => {
          if (type === '1') {
            const labeName = this.getUnknownName(cascader.parentId, cascader.labeName)
            fieldDom.innerHTML = `${cascader.alias || labeName}&nbsp;&nbsp;&nbsp;&nbsp;`
            if (!fieldDom.getAttribute('title')) {
              const filedDataset = this.datasetList.find(dataSet => dataSet.id === item.parentId)
              fieldDom.setAttribute('title', `${filedDataset.labeName}: ${labeName}`)
            }
          } else if (type === '2') {
            fieldDom.innerHTML = rows[`VIEWFORMAT_${cascader.alias || cascader.labeName}`] || ''
            fieldDom.removeAttribute('title')
            fieldDom = this.addEarlyWarningDom(cascader, fieldDom, this.element.content)
          } else if (type === '3') {
            fieldDom.innerHTML = `{{${item.uniqueKey}}}`
          }
        })
      })
      return fragment.firstElementChild.innerHTML
    },
    dragCascaderSelect() {
      this.showCascaderMenu = false
    },
    handleContextMenu(e) {
      e.preventDefault()
    },
    handleMousedown(e) {
      if (!this.isSetting) return
      document.execCommand('unselect')
    },
    handleScroll(e) {
      if (!this.isSetting) return
      this.hiddenMenu()
    },
    handleKeyDown(e) {
      if (!this.isSetting || ['titleHtml', 'orderHtml'].includes(this.htmlField)) return
      // ctrl+v默认行为
      if ((e.ctrlKey || e.metaKey) && e.keyCode === 86) {
        // const richTextDom = document.getElementsByClassName(this.richTextClass)[0]
        const richTextDom = this.getRichTextDom()[0]
        const oldHtml = richTextDom.innerHTML
        setTimeout(() => {
          this.handleRepeateFields(richTextDom, oldHtml)
          this.bindFieldEvent(this.$el)
        }, 1000)
      }
      // 禁用ctrl+Z默认行为
      if (e.ctrlKey && e.keyCode === 90) {
        e.returnValue = false
        e.preventDefault()
        return
      }
      if (e.key === '*' || (e.shiftKey && e.code === 'Digit8')) {
        setTimeout(() => {
          // 获取鼠标光标位置
          let selection = window.getSelection()
          let range = selection.getRangeAt(0)

          document.execCommand('delete')
          // document.execCommand('insertHTML', false, `<span id="anchor">*</span>`)
          // 增加*锚点，用于选择字段时直接替换
          let anchorDom = document.createElement('span')
          anchorDom.id = 'anchor'
          anchorDom.innerHTML = '*'
          range.insertNode(anchorDom)

          selection.removeAllRanges()
          // let newRange = document.createRange()
          // newRange.selectNodeContents(anchorDom)
          // newRange.collapse(false)
          // selection.addRange(newRange)

          let nodePosition = document.getElementById('anchor').getBoundingClientRect()
          let contanierPosition = this.$el.getBoundingClientRect()
          this.anchorPosition = {
            top: nodePosition.top + nodePosition.height - contanierPosition.top,
            left: nodePosition.left + nodePosition.width - contanierPosition.left,
          }
          this.showField = true
          this.deleteRichTextContent()
        })

      } else if (e.key === 'Backspace') {
        this.deleteRichTextContent()
      }
    },
    deleteRichTextContent() {
      this.$nextTick(() => {
        // 只有原本的才更新
        // 删除字段需要更新referenceField、dragcascaderValue
        // 历史缺陷：62419，需要在changeRichText保存了历史操作记录后再更新引用字段
        setTimeout(() => {
          !this.isLocaleSetting && this.updateReferenceField()
        }, 300)

        // const richTextDom = document.getElementsByClassName(this.richTextClass)
        const richTextDom = this.getRichTextDom()
        if (richTextDom[0] && (!richTextDom[0].innerHTML || richTextDom[0].innerHTML === '<br>')) {
          richTextDom[0].innerHTML = '<p><br></p>'
        }
      })
    },
    updateReferenceField() {
      // const richTextDom = document.getElementsByClassName(this.richTextClass)[0]
      const richTextDom = this.getRichTextDom()[0]
      const fieldDom = richTextDom.querySelectorAll('.anchor-measure')
      let ids = []
      fieldDom.forEach(item => { ids.push(item.getAttribute('data-id')) })

      if (ids.length === this.referenceField.length) return
      let newReferenceField = []
      this.referenceField.forEach(item => {
        if (!ids.includes(item.uniqueKey)) {
          Reflect.deleteProperty(this.element.content.dragcascaderValue, item.uniqueKey)
          Reflect.deleteProperty(this.element.content.cascaderStyle, item.uniqueKey)
        } else {
          newReferenceField.push(item)
        }
      })
      this.$set(this.element.content, 'referenceField', newReferenceField)
    },
    hiddenMenu() {
      this.showCascaderMenu = false
      this.hiddenField()
    },
    changeRichText(e) {
      console.log(e)
      // document.execCommand也会触发input事件
      if (!e.inputType || ['formatBold', 'formatItalic', 'formatUnderline', 'formatStrikeThrough'].includes(e.inputType)) return
      this.$emit('record-operation')
      this.saveRichTextContent()
    },
    setTips() {
      const getHtmlLength = (htmlString) => {
        const element = document.createElement('div')
        element.innerHTML = htmlString
        // 获取所有具有class为anchor-measure的元素
        const elements = element.getElementsByClassName('anchor-measure')
        // 将HTMLCollection转换为数组，以便在循环中删除元素
        const elementsArray = Array.from(elements)
        // 遍历元素数组并删除每个元素
        elementsArray.forEach(element => {
          element.remove()
        })
        const textContent = element.innerText.replace(/\s/g, '')
        console.log('textContent: ', textContent, textContent.length)
        const length = textContent.length
        return length
      }
      const richTextLenght = getHtmlLength(this.getRichTextDom()[0]?.innerHTML)
      if (this.maxLength > 0 && richTextLenght > this.maxLength) {
        this.tips = `${richTextLenght}/${this.maxLength}`
      } else {
        this.tips = ''
      }
    },
    saveRichTextContent() {
      // 在弹框点击确认时再调用transformRichTextHtml转换
      // const richTextHtml = document.getElementsByClassName(this.richTextClass)[0]?.innerHTML
      const richTextHtml = this.getRichTextDom()[0]?.innerHTML
      this.setTips()
      this.$set(this.contentScope, this.htmlField, richTextHtml || '')
    },
    hiddenField() {
      this.showField = false
      // 删除锚点
      const anchorDom = document.getElementById('anchor')
      if (!anchorDom) return
      anchorDom.removeAttribute('id')
    },
    // 样式回显
    regainStyle() {
      let selection = window.getSelection()
      let range = selection.getRangeAt(0)
      const { startOffset, endOffset, startContainer, endContainer, commonAncestorContainer } = range
      // nodeType为3是文本类型
      let rangeNode = startContainer.nodeType === 3 ? startContainer.parentNode : startContainer
      let style = {}
      const rangeNodeClass = rangeNode.className || []
      console.log('range...........', range, rangeNode)

      if (rangeNodeClass.includes('anchor-measure')) {
        // 选中字段
        const uniqueKey = rangeNode.getAttribute('data-id')
        const cascaderStyle = this.contentScope.cascaderStyle
        if (cascaderStyle?.[uniqueKey]) {
          style = { ...cascaderStyle[uniqueKey] }
        }
      }
      if (!rangeNodeClass.includes(this.richTextClass)) {
        const nodeStyle = this.getTextStyle(rangeNode)
        Object.assign(style, nodeStyle)
      }
      // if (rangeNode.getAttribute('type')) {
      //   Object.assign(style, { type: rangeNode.getAttribute('type') })
      // }
      // if (rangeNode.getAttribute('angle')) {
      //   Object.assign(style, { angle: parseInt(rangeNode.getAttribute('angle')) })
      // }
      // if (rangeNode.getAttribute('gradientColor')) {
      //   Object.assign(style, { gradientColor: rangeNode.getAttribute('gradientColor').split(',') })
      // }
      // 选择多行时，行高置为空
      if (startContainer !== endContainer && style['line-height']) {
        if (commonAncestorContainer && commonAncestorContainer.nodeName === 'P') {
          let nodeStyle = this.getNodeStyle(commonAncestorContainer)
          style['line-height'] = nodeStyle['line-height'] || ''
        } else {
          style['line-height'] = ''
        }
      }
      this.$emit('set-style', style, this.contentScope)
      this.saveRange()
    },
    searchParentSpan(node, parentNodeList = []) {
      if (!node || !node.tagName) return parentNodeList
      if (['span', 'font', 'p'].includes(node.tagName.toLowerCase())) {
        parentNodeList.unshift(node)
      }
      return node.tagName?.toLowerCase?.() === 'p' || node.parentNode?.className?.includes?.(this.richTextClass) ? parentNodeList : this.searchParentSpan(node.parentNode, parentNodeList)
    },
    getTextStyle(node) {
      let style = {}
      let nodeStyle = this.getNodeStyle(node)
      // 部分样式作用在父级span、font上
      const spanNodeList = this.searchParentSpan(node.parentNode)
      if (spanNodeList.length) {
        spanNodeList.forEach(item => {
          let parentNodeStyle = this.getNodeStyle(item)
          if (item.nodeName === 'P' && parentNodeStyle['line-height']) {
            parentNodeStyle = { 'line-height': parentNodeStyle['line-height'] }
          }
          Object.assign(style, { ...parentNodeStyle })
        })
      }
      return Object.assign(style, { ...nodeStyle })
    },
    getNodeStyle(node) {
      let style = {}
      if (node.style) {
        const styleList = ['backgroundColor', 'color', 'fontStyle', 'fontSize', 'fontFamily', 'fontWeight', 'textDecoration', 'textDecorationLine', 'paddingLeft', 'textAlign', 'justifyContent', 'lineHeight']
        styleList.forEach(key => {
          node.style[key] && (style[this.$_humpToUnderline(key)] = node.style[key])
          // 移除两端的字符串
          if (key === 'fontFamily' && style['font-family'] && style['font-family'][0] === '"') {
            style['font-family'] = style['font-family'].split('"')[1]
          }
        })
      }
      // font标签的color是加在属性上的
      const tagName = node.tagName.toLowerCase()
      if (tagName === 'font') {
        node.color && (style.color = node.color)
        node.face && (style['font-family'] = node.face)
      }
      if (tagName === 'b') {
        style['font-weight'] = 'bold'
      }
      if (style['line-height']) {
        style['line-height'] = Number(style['line-height'])
      }
      return style
    },
    // 保存选区
    saveRange() {
      const selection = window.getSelection()
      let range = selection.getRangeAt && selection.rangeCount ? selection.getRangeAt(0) : document.createRange()
      this.currRange = range.cloneRange()
      console.log('保存选区2222222222', range)
    },
    // 恢复选区
    restoreRange() {
      if (!this.currRange) return
      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(this.currRange.cloneRange())
      console.log('恢复选区3333333', this.currRange)
    },
    handleMouseup(e) {
      if (!this.isSetting) return
      const selection = window.getSelection()
      if (!this.isRangeIntersectsElement(selection.getRangeAt(0))) {
        selection.removeAllRanges()
        return
      }
      e.preventDefault()
      this.hiddenField()
      this.showCascaderMenu = false
      this.regainStyle()
    },
    changeStyle({ type, value, cssKey, commandKey, otherStyle }) {
      if (type === 'clearStyle') {
        this.clearWholeTextStyle()
        return
      }
      // const richTextDom = document.getElementsByClassName(this.richTextClass)[0]
      const richTextDom = this.getRichTextDom()[0]
      // richTextDom.focus()
      // 8679
      // let fontSelection = window.getSelection()
      // let fontRange = fontSelection?.getRangeAt(0)
      // if (cssKey === 'color' || cssKey === 'font-size' || ['type', 'angle', 'gradientColor'].includes(cssKey)) {
      if (cssKey === 'color' || cssKey === 'font-size') {
        this.restoreRange()
      }
      let selection = window.getSelection()
      let selectNone = true
      let selectionField = []

      if (this.currRange) {
        let range = this.currRange
        const { startOffset, endOffset, startContainer, commonAncestorContainer } = range
        // 获取选区内字段
        selectionField = this.getSelectionField(range)

        // 未选中文字（当选中字段+文字时，startOffset与endOffset相同，需要判断是否选中字段）
        selectNone = startOffset === endOffset && !selectionField.length

        let rangeNode = startContainer.nodeType === 3 ? startContainer.parentNode : startContainer
        // 当只选中字段部分文字时，getSelectionField函数获取不到对应字段
        const onlySelectField = rangeNode?.className?.includes?.('anchor-measure')
        if (onlySelectField && !selectionField.includes(rangeNode.getAttribute('data-id'))) {
          selectionField.push(rangeNode.getAttribute('data-id'))
        }
      }

      if (selectNone) {
        // 先聚焦到编辑器，避免选中整个页面
        richTextDom.focus()
        document.execCommand('selectAll')
        selectionField = this.referenceField.map(item => item.uniqueKey)
      }

      const isCancelStyle = !value[cssKey]
      const execCommandList = {
        'color': 'foreColor',
        'font-family': 'fontName',
        'font-style': 'italic',
        'text-decoration': commandKey === 'underline' ? 'underline' : 'strikeThrough',
        'font-weight': 'bold',
        'font-size': 'fontSize',
      }
      if (execCommandList[cssKey]) {
        // 如果是color又是空字符串，则指定为全局的字体颜色
        if (cssKey === 'color' && value[cssKey] === '') {
          value[cssKey] = this.contentScope.textStyle.color
        }
        const isEffect = document.execCommand(execCommandList[cssKey], false, cssKey === 'font-size' ? 7 : value[cssKey])

        console.log('isEffect', isEffect)
        if (!isEffect) {
          const range = selection?.getRangeAt(0)

          if (!range) return

          // const docFragment = range.cloneContents()
          // console.log(richTextDom.querySelectorAll('*'))

          // docFragment.childNodes.forEach((item) => {
          //   if (item.nodeType === 3) return

          //   const node = richTextDom.querySelectorAll(item.localName)
          //   const el = [...node].find((el) => el.innerHTML === item.innerHTML)
          //   console.log(el, node, item)

          //   el.style[cssKey] = cssKey === 'font-size' ? 7 : value[cssKey]
          // })
          const { startContainer } = range

          let rangeNode = startContainer.nodeType === 3 ? startContainer.parentNode : startContainer

          rangeNode.style[cssKey] = cssKey === 'font-size' ? 7 : value[cssKey]
        }
      }
      // 8679 todo 渐变色
      // if (['color', 'type', 'angle', 'gradientColor'].includes(cssKey)) {
      //   const { type: fType, angle, gradientColor } = otherStyle || {}
      //   if (fType === 'gradient') {
      //     if (!fontRange) return
      //
      //     const { startContainer, endContainer } = fontRange
      //     let rangeNode = startContainer.nodeType === 3 ? startContainer.parentNode : startContainer
      //
      //     console.log('8679>', cssKey, rangeNode, startContainer)
      //
      //     if (rangeNode && rangeNode?.nodeName !== 'P') {
      //       rangeNode.style['color'] = otherStyle['color']
      //       rangeNode.style['-webkit-background-clip'] = 'text'
      //       rangeNode.style['-webkit-text-fill-color'] = 'transparent'
      //       rangeNode.style['background-image'] = `linear-gradient(${angle}deg, ${gradientColor.join(', ')})`
      //       rangeNode.style['background-clip'] = 'text'
      //       rangeNode.setAttribute('type', fType)
      //       rangeNode.setAttribute('angle', angle)
      //       rangeNode.setAttribute('gradientColor', gradientColor)
      //       rangeNode.classList.add('gradient')
      //     }
      //   } else {
      //     if (cssKey === 'color') {
      //       let range = this.currRange
      //       const { startContainer } = range
      //       let rangeNode = startContainer.nodeType === 3 ? startContainer.parentNode : startContainer
      //
      //       // rangeNode.style['-webkit-background-clip'] = 'border-box'
      //       // rangeNode.style['-webkit-text-fill-color'] = 'transparent'
      //       // rangeNode.style['background-image'] = `none`
      //       // rangeNode.style['background-clip'] = 'border-box'
      //       rangeNode.removeAttribute('style')
      //       rangeNode.removeAttribute('type')
      //       rangeNode.removeAttribute('angle')
      //       rangeNode.removeAttribute('gradientColor')
      //       rangeNode.removeAttribute('class')
      //     }
      //   }
      // }
      // 设置行高
      if (cssKey === 'line-height') {
        const range = selection.getRangeAt(0)
        let container = range.commonAncestorContainer
        // 如果没有选中任何内容
        if (container.nodeType === 1 && container.classList[0] === 'rich-text-content') {
          const setCssKey = (list) => {
            list.forEach(node => {
              if (node.nodeType !== 1) return
              if (['P'].includes(node.nodeName)) {
                node.style.lineHeight = value[cssKey]
              }
              if (node.childNodes.length) {
                setCssKey(Array.from(node.childNodes))
              }
            })
          }
          setCssKey(Array.from(container.childNodes))
        } else {
          let temp = container
          while (temp.nodeName !== 'P' && temp.nodeName !== 'DIV') {
            if (temp.nodeName !== 'P') temp = temp.parentElement
          }
          if (temp.nodeName === 'P') {
            temp.style.lineHeight = value[cssKey]
          }
        }
      }

      richTextDom.style.visibility = 'hidden'

      // 替换b、u、i、strike标签
      const selectorList = ['b', 'i', 'u', 'strike', 'font[size="7"]']
      // 火狐浏览器不兼容font标签
      if (navigator.userAgent.indexOf('Firefox') !== -1) {
        selectorList.push('font')
      }
      selectorList.forEach(selector => {
        let dom = richTextDom.querySelectorAll(selector)
        if (!dom.length) return
        dom.forEach(item => {
          // 判断是否存在兄弟节点，如果不存在兄弟节点且父级节点不为p标签，则直接将样式作用在父级
          if (item.parentNode.childNodes.length === 1 && !['p', 'div'].includes(item.parentNode.tagName.toLowerCase())) {
            item.parentNode.style[cssKey] = value[cssKey]
            if (item.nodeName === 'FONT') {
              item.color && (item.parentNode.style.color = item.color)
              item.face && (item.parentNode.style['font-family'] = item.face)
            }
            item.parentNode.innerHTML = item.innerHTML
          } else {
            // 直接将标签替换成span
            let span = document.createElement('span')
            span.innerHTML = item.innerHTML

            // 保留原先节点上的样式
            let style = { ...value }
            if (item.tagName.toLowerCase() === 'font') {
              item.color && (style.color = item.color)
              item.face && (style['font-family'] = item.face)
            }
            let styleStr = item.getAttribute('style')

            Object.keys(style).map(key => {
              styleStr = `${ styleStr || '' }${ key }: ${ style[key] };`
            })
            span.setAttribute('style', styleStr)
            item.parentNode.replaceChild(span, item)
          }
        })
      })

      // 给选区内的字段增加样式
      selectionField.forEach(uniqueKey => {
        let fieldDoms = richTextDom.querySelectorAll(`span.anchor-measure[data-id="${uniqueKey}"]`)
        if (!fieldDoms?.length) return
        Array.from(fieldDoms).forEach(fieldDom => {
          try {
            // 判断当前获取的dom是否在选取范围相交
            console.log('是否相交：', this.currRange.intersectsNode(fieldDom))
            if (!this.currRange.intersectsNode(fieldDom) && !selectNone) return
          } catch (err) {
            console.log(err);
            return
          }

          fieldDom.style[cssKey] = value[cssKey]
          const needCssKey = ['font-size', 'color', 'font-family', 'font-weight', 'font-style', 'text-decoration']
          let fieldStyle = {}
          needCssKey.forEach(cssKey => { fieldStyle[cssKey] = fieldDom.style[cssKey] })
          this.$set(this.contentScope.cascaderStyle, uniqueKey, fieldStyle)
        })
      })

      // 处理富文本dom
      const handleRichDoms = (node) => {
        // 如果不是元素节点，直接返回
        if (node.nodeType !== 1) return
        // 获取计算后的样式
        const computedStyle = window.getComputedStyle(node)
        const textDecoration = computedStyle.textDecoration

        // 解析当前节点的装饰样式
        let currentDecorations = []
        if (textDecoration !== 'none') {
          currentDecorations = textDecoration.split(' ').filter(d => ['underline', 'line-through', 'overline'].includes(d))
        }

        // 获取子节点（排除空文本节点）
        const childNodes = Array.from(node.childNodes)
          .filter(child => child.nodeType === 1 || (child.nodeType === 3 && child.textContent.trim()))

        // 如果只有一个文本节点且当前节点有装饰样式，保持当前样式
        if (childNodes.length === 1 && childNodes[0].nodeType === 3 && currentDecorations.length > 0) {
          node.style.textDecoration = currentDecorations.join(' ')
          return
        }

        // 处理子节点
        childNodes.forEach(childNode => {
          if (childNode.nodeType === 3) {
            // 文本节点：只有当父节点有装饰样式时才包装span
            if (currentDecorations.length > 0) {
              const span = document.createElement('span')
              span.textContent = childNode.textContent
              span.style.textDecoration = currentDecorations.join(' ')
              node.replaceChild(span, childNode)
            }
          } else if (childNode.nodeType === 1) {
            // 元素节点：合并样式并递归
            const childStyle = window.getComputedStyle(childNode)
            const childDecorations = childStyle.textDecoration.split(' ')
              .filter(d => ['underline', 'line-through', 'overline'].includes(d))
            // 只有当存在装饰样式时才进行合并
            if (currentDecorations.length > 0 || childDecorations.length > 0) {
              const mergedDecorations = [...new Set([...currentDecorations, ...childDecorations])]
              childNode.style.textDecoration = mergedDecorations.join(' ')
            }
            // 递归处理
            handleRichDoms(childNode)
          }
        })

        // 如果有子节点且当前节点不是根节点，清除当前节点样式
        if (childNodes.length > 0 && !node.classList.contains('rich-text-content')) {
          node.style.textDecoration = ''
        }
      }
      Array.from(richTextDom.childNodes).forEach(node => {
        Array.from(node.childNodes).forEach(childNode => {
          handleRichDoms(childNode)
        })
      })

      richTextDom.style.visibility = ''
      this.saveRichTextContent()

      if (selectNone) {
        document.execCommand('unselect')
      }
      // selection.removeAllRanges()
      // 在最后重新保存当前选区
      this.$nextTick(() => {
        const selection = window.getSelection()
        if (!selection.isCollapsed) {
          this.saveRange()
        } else {
          this.currRange = ''
          selection.removeAllRanges()
        }
      })
    },
    addNodeToRange(node) {
      const selection = window.getSelection()
      selection.removeAllRanges()
      const newRange = document.createRange()
      newRange.selectNode(node)
      selection.addRange(newRange)
      this.saveRange()
    },
    // 获取选取内的字段
    getSelectionField(range) {
      let docFragment = range.cloneContents()
      let selectionDom = document.createElement('div')
      selectionDom.appendChild(docFragment)

      const anchorMeasure = [...selectionDom.querySelectorAll('.anchor-measure')].filter(item => item.innerHTML)
      let selectionField = []
      anchorMeasure.forEach(item => selectionField.push(item.getAttribute('data-id')))
      return selectionField
    },
    clearWholeTextStyle() {
      // const richTextDom = document.getElementsByClassName(this.richTextClass)[0]
      const richTextDom = this.getRichTextDom()[0]
      const richTextHtml = richTextDom.innerHTML
      // 将字段转成uniqueKey，过滤样式内容后，再将uniqueKey转成字段
      let fragment = document.createDocumentFragment()
      let virtualDom = document.createElement('div')
      virtualDom.innerHTML = this.transformRichTextHtml(richTextHtml, '3')
      virtualDom.childNodes.forEach(node => {
        if (!node) return
        node.innerHTML = !node.innerText && node.innerHTML.includes('<br>') ? '<br>' : node.innerText
        if (node.getAttribute('style')) {
          node.removeAttribute('style')
        }
        // 数据兼容：部分看板存在错误结构，第一行没被p标签包裹
        if (node.className?.includes?.('anchor-measure')) {
          let textNode = document.createTextNode(node.innerText)
          virtualDom.replaceChild(textNode, node)
        }
      })
      // 将uniqueKey转成字段
      this.referenceField.forEach(item => {
        let labeName = this.getUnknownName(item.parentId, item.labeName)
        const filedDataset = this.datasetList.find(dataSet => dataSet.id === item.parentId)
        virtualDom.innerHTML = virtualDom.innerHTML.replace(`{{${item.uniqueKey}}}`, `<span data-id="${item.uniqueKey}" class="anchor-measure" contenteditable="false" title="${filedDataset.labeName}: ${labeName}}">${item.alias || labeName}&nbsp;&nbsp;&nbsp;&nbsp;</span>`)
      })
      richTextDom.innerHTML = virtualDom.innerHTML
      this.saveRichTextContent()
      this.bindFieldEvent(richTextDom)
      this.$set(this.element.content, 'cascaderStyle', {})
    },
    addField(filed) {
      if (this.oldTarget) {
        this.changeField(filed)
        this.oldTarget = null
        return
      }
      // 增加字段时，需要先输入*，触发changeRichText函数，已经记录过上一次内容
      let uniqueKey = this.isLocaleSetting ? filed.uniqueKey : this.$_generateKeyName('filed')
      let cascaderValue = null

      if (!this.isLocaleSetting) {
        this.referenceField.push({ ...filed, uniqueKey })
        cascaderValue = this.$_JSONClone(filed)
        cascaderValue.uniqueKey = uniqueKey
      }

      // const richTextDom = document.getElementsByClassName(this.richTextClass)[0]
      const richTextDom = this.getRichTextDom()[0]
      let anchorDom = richTextDom.querySelector('#anchor')

      anchorDom.setAttribute('data-id', uniqueKey)
      anchorDom.className = 'anchor-measure'
      const style = this.getTextStyle(anchorDom)
      Object.keys(style).map(key => {
        anchorDom.style[key] = style[key]
      })

      const labeName = this.getUnknownName(filed.parentId, filed.labeName)
      anchorDom.innerHTML = `${ (this.isLocaleSetting ? this.dragcascaderValue[filed.uniqueKey].alias : filed.alias) || labeName}&nbsp;&nbsp;&nbsp;&nbsp;`

      const filedDataset = this.datasetList.find(item => item.id === filed.parentId)
      const attributesList = {
        contenteditable: false,
        title: `${filedDataset.labeName}: ${labeName}`
      }
      Object.keys(attributesList).forEach(attribute => anchorDom.setAttribute(attribute, attributesList[attribute]))

      anchorDom.addEventListener('click', this.clickAnchorMeasure)

      let span = document.createElement('span')
      span.innerHTML = '&nbsp;'
      anchorDom.after(span)
      this.addNodeToRange(anchorDom)

      if (!this.isLocaleSetting) {
        // 设置默认aggType
        if (!cascaderValue.hasOwnProperty('aggType') && ['string', 'number'].includes(cascaderValue.columnTpe)) {
          cascaderValue.aggType = cascaderValue.columnTpe === 'string' ? 'MAX' : 'SUM'
        }
        this.dragcascaderValue[uniqueKey] = cascaderValue
      }
      this.hiddenField()
      this.saveRichTextContent()
    },
    // 多语言设置场景,更换字段
    changeField(field) {
      let dom = this.oldTarget
      const labeName = this.getUnknownName(field.parentId, field.labeName)
      dom.className = 'anchor-measure'
      dom.innerHTML = `${(this.isLocaleSetting ? this.dragcascaderValue[field.uniqueKey].alias : field.alias) || labeName}&nbsp;&nbsp;&nbsp;&nbsp;`
      const filedDataset = this.datasetList.find(item => item.id === field.parentId)
      const attributesList = {
        contenteditable: false,
        title: `${filedDataset.labeName}: ${labeName}`,
        'data-id': field.uniqueKey
      }
      Object.keys(attributesList).forEach(attribute => dom.setAttribute(attribute, attributesList[attribute]))
      this.hiddenField()
      this.saveRichTextContent()
    },
    clickAnchorMeasure(e) {
      const uniqueKey = e.target.getAttribute('data-id') || e.currentTarget.getAttribute('data-id')
      this.currentCascader = this.dragcascaderValue[uniqueKey]
      if (this.isSetting) {
        // 如果是多语言编辑的富文本
        if (this.isLocaleSetting) {
          this.oldTarget = e.target
          let nodePosition = e.target.getBoundingClientRect()
          let contanierPosition = this.$el.getBoundingClientRect()
          this.anchorPosition = {
            top: nodePosition.top + nodePosition.height - contanierPosition.top,
            left: nodePosition.left + nodePosition.width - contanierPosition.left,
          }
          this.showField = true
          return
        }
        // 非多语言编辑处理逻辑
        if (this.showCascaderMenu) {
          this.showCascaderMenu = false
          return
        }
        // 获取当前字段位置
        const rect = e.target.getBoundingClientRect()
        // 字段点击分区，点击字段名称区域展示字段对应数据集，点击伪元素区域展示度量函数下拉框
        const fieldFontSize = this.contentScope?.cascaderStyle?.[uniqueKey]?.['font-size'] || 12
        // 计算伪元素区域，伪元素尺寸为0.36em，距离右边界4px
        let afterArea = 0.36 * parseInt(fieldFontSize) * 2 + 4
        afterArea < 20 && (afterArea = 20)
        if (e.offsetX < rect.width - afterArea) {
          this.$emit('change-dataset', this.currentCascader.parentId)
          return
        }
        const contanierPosition = this.$el.getBoundingClientRect()
        this.cascaderMenuStyle = {
          left: `${rect.left - contanierPosition.left}px`,
          top: `${rect.top - contanierPosition.top + rect.height}px`,
        }
        this.showCascaderMenu = true
      } else {
        // 交互、超链接
        const value = e.target.textContent
        const metric = this.referenceField.find(item => item.uniqueKey === uniqueKey)
        this.$emit('element-interaction', { values: [value], metric })
      }
    },
    getRichTextData() {
      // const richTextHtml = document.getElementsByClassName(this.richTextClass)[0]?.innerHTML
      // const richText = document.getElementsByClassName(this.richTextClass)[0]?.innerText
      const richTextHtml = this.getRichTextDom()[0]?.innerHTML
      const richText = this.getRichTextDom()[0]?.innerText
      return {
        richTextHtml: this.transformRichTextHtml(richTextHtml, '3'),
        richText
      }
    },
    changeAlias() {
      // 当改变字段别名时,不仅仅只有当前富文本改变,另外的也要变,移到外面去
      this.$emit('update-locales-alias', this.currentCascader)
    },
    addCursorPointer() {
      if (this.isSetting) return
      const richTextDom = this.getRichTextDom()[0]
      const fieldDom = richTextDom.querySelectorAll('.anchor-measure')
      Array.from(fieldDom).forEach(item => {
        const id = item.getAttribute('data-id')
        const metric = this.referenceField.find(item => item.uniqueKey === id)
        if (!metric) return (item.style.cursor = 'default')
        const interactionOptions = this.element.content.interactionOptions && this.element.content.interactionOptions.filter(item => {
          const { dataSetId, columnName } = item.associElements[0]
          return dataSetId === metric?.parentId && columnName === metric?.labeName
        })
        if (!interactionOptions?.length) return (item.style.cursor = 'default')
        const associElements = interactionOptions[0].associElements
        if (!isCanInteraction(this.element._containerId, associElements || [], this.elList)) return (item.style.cursor = 'default')
        item.style.cursor = 'pointer'
      })
    },
    handleGlobalMouseup(e) {
      if (!this.isSetting || !this.isActive) return
      // 延迟执行，确保selection已经更新
      setTimeout(() => {
        const selection = window.getSelection()
        if (selection.isCollapsed) return
        const range = selection.getRangeAt(0)
        if (this.isRangeEqual(range, this.currRange)) return
        if (this.isRangeIntersectsElement(range)) {
          this.handleMouseup(e)
        } else {
          // 如果选区不在富文本区域内，则清除选区
          selection.removeAllRanges()
        }
      }, 0)
    },
    isRangeEqual(range1, range2) {
      if (!range1 || !range2) return false
      return range1.startContainer === range2.startContainer &&
      range1.startOffset === range2.startOffset &&
      range1.endContainer === range2.endContainer &&
      range1.endOffset === range2.endOffset
    },
    // 检查选区是否与元素相交
    isRangeIntersectsElement(range) {
      const richTextDom = this.getRichTextDom()[0]
      // 检查选区的公共祖先节点是否包含在目标元素内
      return richTextDom.contains(range.commonAncestorContainer)
    },
  },
  beforeDestroy() {
    document.removeEventListener('mouseup', this.handleGlobalMouseup)
  },
  destroyed() {
    // const richTextDom = document.getElementsByClassName(this.richTextClass)
    const richTextDom = this.getRichTextDom()
    if (!richTextDom[0]) return
    const fieldDom = richTextDom[0].querySelectorAll('.anchor-measure')
    fieldDom.forEach(e => {
      e.removeEventListener('click', this.clickAnchorMeasure)
    })
    this.$el.removeEventListener('copy', this.handleCopy)
  },
}
</script>

<style lang="scss" scoped>
.rich-text {
  position: relative;
  .tips {
    position: absolute;
    color: var(--sdp-qcgls);
    background: var(--sdp-tb-bj);
    padding: 2px;
    right: 8px;
    bottom: 4px;
  }
  &.element-text-dialog {
    .rich-text-content{
      caret-color: var(--sdp-text-caret);
    }
    .rich-text-content /deep/ {
      height: 164px;
      padding: 10px 12px;
      overflow: auto;
      .anchor-measure {
        // 解决 mac 中自动添加的 br 导致样式出现问题
        .Apple-interchange-newline {
          display: none!important;
        }
        border: 1px solid #DDDDDD;
        background-color: var(--sdp-fs1);
        border-color: var(--sdp-cszj-bkfgx);
        padding: 6px;
        cursor: default;
        display: inline-block;
        // vertical-align: text-bottom;
        border-radius: 2px;
        &::after {
          content: "";
          position: absolute;
          top: 0;
          bottom: 0;
          right: 4px;
          width: 0;
          height: 0;
          margin: auto 0;
          border: 0.36em solid;
          border-bottom-width: 0;
          border-bottom-color: transparent;
          border-left-color: transparent;
          border-right-color: transparent;
          pointer-events: auto;
          cursor: pointer;
          position: absolute;
          color: #DDDDDD;
        }
      }
    }
  }
  .rich-text-content /deep/ {
    .anchor-measure {
      // 通过变量控制字段样式，因为在外部无法改动 inline-block 中的下划线以及斜体等样式
      text-decoration: var(--rich-text-anchor-measure-text-decoration, none);
      font-style: var(--rich-text-anchor-measure-font-style, initial);
      white-space: nowrap;
      position: relative;
      cursor: default;
      display: inline-block;
    }
  }
  .filed {
    max-height: 300px;
    max-width: 260px;
    min-width: 150px;
    position: absolute;
    overflow: auto;
    z-index: 9999;
    border: 1px solid;
    color:var(--sdp-cszjsz-wzs1);
    background-color: var(--sdp-szk-bjs);
    border-color: var(--sdp-cszj-bkfgx);
    li {
      height: 35px;
      padding: 0 15px;
      line-height: 35px;
      white-space: nowrap;
      cursor: pointer;
      &:hover {
        background-color: var(--sdp-ycsz-hgls);
        color: var(--sdp-cszjsz-wzs1);
      }
    }
    > div {
      height: 35px;
      padding: 0 15px;
      line-height: 35px;
    }
  }
}
</style>
<style lang="scss">
  // 8679 文字渐变色
  .gradient {
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .no-gradient {
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    background-image: none !important;
  }
  .gradient-overflow {
     width: 100%;
     display: block;
     white-space: nowrap;
     text-overflow: ellipsis;
     overflow: hidden;
   }
</style>
