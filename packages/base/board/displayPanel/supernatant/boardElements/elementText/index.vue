<template>
  <div
    :class="{
    'textColor': true,
    'is-focus': !utils.isMobile || !commonData.isPreview || element.isFocus,
    'only-title': !!element.content.title && emptyRichText && element.content.hasOwnProperty('titleVerticalCenter'),
    'not-mobile': !utils.isMobile,
    }"
  >
    <ElementSlot :visible="utils.isMobile">
      <slot></slot>
    </ElementSlot>
    <!-- 占位文本 -->
    <div v-if="!hasResponseData" class="text">
      <div class="placeholder-text">
        {{ commonData.isPreview ? "" : $t("sdp.views.warnStyleTypeTxt") }}
      </div>
      <!-- <loading v-if="cardList.length" :isLoading="loading"></loading> -->
    </div>

    <div v-else class="element-text-container">
      <!-- 无内容时显示占位文本 -->
      <div v-if="showNone" class="placeholder-image"><span>NONE</span></div>

      <div
        class="DataBriefing"
        v-if="isFullScreen && !showNone && !loading"
        ref="verticalScroll"
        @touchstart.stop="handleTouchStart"
      >
        <div class="content-box" style="flex-direction: column; display: flex; height: 100%">
          <div v-if="!!renderTitle" :class="['title_value', 'title_color']" :style="titleStyle" :id="`sdp-element-text-title-${element.id}`">
            <span
              :style="getStyle()"
              :title="renderTitle"
              :class="{ 'pointer-title': superLinkNeedClick, 'gradient': checkIsGradient(titleStyle) }"
              @click="titleClickHandler"
            >{{ renderTitle }}</span>
          </div>

          <rich-text
            v-if="!emptyRichText"
            :id="`sdp-element-text-richText-${element.id}`"
            ref="richTextRef"
            class="text_value text_color"
            :element="element"
            :scope="contentScope.content.richTextHtml && !onlyHTMLTags(contentScope.content.richTextHtml) ? contentScope.scope : 'content'"
            @element-interaction="elementInteraction">
          </rich-text>
          <!-- <div v-if="!!element.content.text" :style="textStyle" :class="['text_value', 'text_color']">
            <p :style="`${textPaddingRight}${textWhileSpace}`">{{ element.content.text }}</p>
          </div> -->
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import SdpElScrollbar from '../../../../../../directives/v-el-scrollbar'
import EventData from 'packages/assets/EventData'
// import scrollMixin from 'packages/base/board/displayPanel/supernatant/boardElements/elementFourQuadrant/scrollMixin'
import RichText from './richText'
import { setElementTextContent } from './colorConfig'
import { DATA_FILTER } from 'packages/base/grid/helpers/constants/index'
import { SUPERLINK_CONST_TYPE, EVENT_DATA_PARAMS } from 'packages/assets/constant'
import {isCanInteraction, getComponentParamShow, checkIsGradient, getGradientStyle} from 'packages/base/board/displayPanel/utils'
import { TYPE_INTERACTION } from 'packages/base/board/displayPanel/constants'
import { filterMapVerify } from '../../../utils'
import { TYPE_ELEMENT_GROUP } from '../../../params/utils/constants'
import { transfromZeroFormat } from '../elementChart/bridge'
import elementMixin from 'packages/base/board/displayPanel/supernatant/mixins/elementMixin'
import { getAggType, setLgeTypeValue } from 'packages/base/board/displayPanel/supernatant/chartSet/common/modules/drag-cascader/helper'
import datasetMixin from 'packages/base/board/displayPanel/supernatant/mixins/datasetMixin'
import { insetDom } from 'packages/base/common/loading/components/loadingIcon'
import { THIS_PERIOD_KEY_TYPE } from '../../../params/paramElement/bussinessCalendar/bridge'
import ElementSlot from '../components/elementSlot.vue'

export default {
  mixins: [elementMixin, datasetMixin],
  inject: {
    utils: { default: {} },
    commonData: { default: {} },
    themeData: { default: {} },
    tenantData: { default: {} },
    datasetList: { default: [] },
    fullscreenData: { default: {} },
    langCode: { default: 'zh' },
    setLoadingStatus: {
      default: () => () => false
    },
    setErrorStatus: {
      default: () => () => false
    },
    getBoardData: {
      default: () => () => ({})
    },
    sdpBus: { default: {} }
  },
  directives: { SdpElScrollbar },
  name: 'element-text',
  watch: {
    'themeData.themeFullScreen'(val) {
      this.isFullScreen = false
      setTimeout(() => {
        this.isFullScreen = true
      })
    },
    themeType: {
      handler(val, oldVal) {
        // watch immediate 执行顺序优先于created，需要先执行一遍兼容代码
        this.compatibility()
        setElementTextContent(this.element, this.themeType)
        // 多语言的主题样式
        if (this.element.content.locales) {
          Object.keys(this.element.content.locales).forEach(key => {
            setElementTextContent(this.element, this.themeType, this.element.content.locales[key], `locales.${key}`)
          })
        }
      },
      immediate: true,
    },
    'contentScope.scope': {
      handler(val, oldVal) {
        if (val !== oldVal) {
          this.$nextTick(() => {
            this.$refs.richTextRef && this.$refs.richTextRef.initData('response')
          })
        }
      }
    }
  },
  props: {
    element: {
      type: Object,
      default: () => ({}),
    },
    screenType: {
      type: String,
    },
    elList: {
      type: Array,
      default: () => []
    },
    boardInfo: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    // 是否支持超链接跳转
    superLinkNeedClick() {
      return this.screenType === 'vertical' && !!this.element.content?.superLinkOptions?.length && !this.isChartLarge
    },
    isChartLarge() {
      return this.fullscreenData.enlargeVisible
    },
    titleStyle() {
      let { titleStyle, text, title } = this.contentScope.content
      // 如果多语言没有值,样式也采用原来的
      if (!title) titleStyle = this.element.content.titleStyle
      return Object.assign(
        {},
        this.$_deepClone(titleStyle),
        this.isChartLarge ? { 'font-size': this.$_getPxOfHighResolution(titleStyle['font-size']) } : {},
        text ? { marginBottom: '8px' } : {}
      )
    },
    emptyRichText() {
      return !this.renderRichTextHtml || this.onlyHTMLTags(this.renderRichTextHtml)
    },
    themeType() {
      return this.themeData.themeType || 'sdp-classic-white'
    },
    titleWhileSpace() {
      let agent = navigator.userAgent.toLowerCase()
      let { titleStyle, title } = this.contentScope.content
      // 如果多语言没有值,样式也采用原来的
      if (!title) titleStyle = this.element.content.titleStyle
      if (
        agent.indexOf('firefox') > 0 &&
        titleStyle['text-align'] === 'justify'
      ) {
        return 'white-space: pre-line;'
      } else {
        return 'white-space: pre-wrap;'
      }
    },
    titlePaddingRight() {
      let right = 0
      let { titleStyle, title } = this.contentScope.content
      // 如果多语言没有值,样式也采用原来的
      if (!title) titleStyle = this.element.content.titleStyle
      if (titleStyle['font-style']) {
        let fontSize = parseInt(titleStyle['font-size'])
        switch (fontSize) {
          case 72:
            right = 16
            break
          case 48:
            right = 10
            break
          case 36:
            right = 8
            break
          case 28:
          case 26:
          case 24:
            right = 6
            break
          case 22:
          case 20:
          case 18:
            right = 4
            break
          default:
            right = 2
            break
        }
      }
      return `padding-right: ${right}px;`
    },
    scrollOptions() {
      if (this.utils.isMobile) {
        return this.verticalOptions
      }
      return {}
    },
    scrollComponent() {
      if (this.utils.isMobile) return 'cube-scroll'
      return 'elScrollbar'
    },
    superLinkOptions() {
      return this.element.content?.superLinkOptions || []
    },
    showNone() {
      return this.hasResponseData && !this.renderTitle && !this.renderRichTextHtml
    },
    contentScope() {
      const boardLang = this.commonData.boardSlectLang ? this.commonData.boardSlectLang() : ''
      const isDailyConcerned = !!this.utils?.isDailyConcerned
      const lang = isDailyConcerned ? boardLang || this.langCode : boardLang
      if ((this.commonData.isPreview || isDailyConcerned) && lang) {
        const locales = this.element.content?.locales || {}
        if (locales[lang]) {
          return {
            content: locales[lang],
            scope: `content.locales.${lang}`
          }
        }
      }
      return {
        content: this.element.content,
        scope: `content`
      }
    },
    // 用于渲染的标题
    renderTitle() {
      const { content, scope } = this.contentScope
      if (scope === 'content') {
        return content.title
      }
      return content.title || this.element.content.title
    },
    // 用于渲染的文本
    renderRichTextHtml() {
      const { content, scope } = this.contentScope
      if (scope === 'content') {
        return content.richTextHtml
      }
      if (content.richTextHtml && !this.onlyHTMLTags(content.richTextHtml)) {
        return content.richTextHtml
      }
      return this.element.content.richTextHtml
    },
    langArr() {
      return this.utils.languageList
    },
  },
  data() {
    return {
      isFullScreen: true,
      loading: false,
      hasResponseData: false,
    }
  },
  components: {
    ElementSlot,
    RichText,
  },
  created() {
    // 将this挂载在element里面
    const vm = this
    Object.defineProperty(this.element, 'vm', {
      enumerable: false,
      configurable: true,
      get() {
        return vm
      },
    })
    try {
      this.compatibility()
    } catch (error) {
      console.log(error)
    }
    this.elementInteraction = this.$_debounce(this.elementInteraction)
  },
  methods: {
    onlyHTMLTags(htmlString) {
      // 创建一个新的DOM元素
      const tempDiv = document.createElement('div');
      // 设置元素的内容为要检查的HTML字符串
      tempDiv.innerHTML = htmlString;
      // 获取元素的纯文本内容
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      // 返回一个布尔值，表示是否只有HTML标签
      return !textContent.length;
    },
    handleTouchStart() {
      console.log('Bugfix: 69393，什么都不做，防止触发外层cube-scroll的touchstart事件')
    },
    compatibility() {
      if (this.element.content.hasOwnProperty('richTextHtml')) {
        const richTextHtml = this.element.content.richTextHtml
        if (richTextHtml && !richTextHtml.includes('<p>')) {
          this.$set(this.element.content, 'richTextHtml', `<p>${richTextHtml}</p>`)
        }
        const newLocales = this.compatibilityLocales()
        this.$set(this.element.content, 'locales', newLocales)
        return
      }
      const { text, textStyle, textThemeStyle, title, titleStyle, titleThemeStyle, superLinkOptions = [] } = this.element.content
      let richTextHtml = text ? `<p>${text.replace(/\n/g, '<br>')}</p>` : ''
      let themeConfig = {}
      const themeList = ['sdp-classic-white', 'sdp-dark-blue']
      themeList.forEach(theme => {
        themeConfig[theme] = {
          titleStyle: titleThemeStyle[theme] || {},
          textStyle: textThemeStyle[theme] || {},
          richTextHtml,
        }
      })
      const newLocales = this.compatibilityLocales()
      const newContent = {
        title,
        titleStyle,
        textStyle, // 富文本整体样式
        richTextHtml,
        referenceField: [], // 富文本引用数据集字段
        dragcascaderValue: {},
        cascaderStyle: {},
        themeConfig,
        locales: newLocales
      }
      if (superLinkOptions?.length) {
        Object.assign(newContent, { superLinkOptions })
      }
      this.$set(this.element, 'content', newContent)
    },
    compatibilityLocales() {
      // 多语言初始化
      const { locales = {}, titleStyle, textStyle } = this.element.content
      this.langArr.map(lang => {
        let base = locales[lang.isoCode]
        if (!base) {
          // 文本元素,兼容老的多语言
          const multipleLangItems = this.boardInfo.metaDashboardElementLanList || []
          const oldLang = multipleLangItems.find(o => o.key === `${this.element.id}_title` && o.languageCode === lang.isoCode)
          const oldTitle = oldLang?.value || ''
          base = {
            title: oldTitle,
            titleStyle: this.$_deepClone(titleStyle),
            richTextHtml: '',
            textStyle: this.$_deepClone(textStyle),
            cascaderStyle: {}
          }
          locales[lang.isoCode] = base
        } else {
          if (base.richTextHtml && !base.richTextHtml.includes('<p>')) {
            base.richTextHtml = `<p>${base.richTextHtml}</p>`
          }
        }
      })
      return locales
    },
    getElementTitle() {
      return {
        text: this.contentScope.content?.title || this.element.content?.title,
        textStyle: this.titleStyle,
      }
    },
    checkIsGradient,
    getStyle() {
      const [titleWhileSpaceKey, titleWhileSpaceValue] = this.titleWhileSpace.replace(';', '').split(': ')
      const [titlePaddingRightKey, titlePaddingRightValue] = this.titlePaddingRight.replace(';', '').split(': ')
      const style = {
        [titleWhileSpaceKey]: titleWhileSpaceValue,
        [titlePaddingRightKey]: titlePaddingRightValue,
      }

      let { titleStyle, title } = this.contentScope.content
      // 如果多语言没有值,样式也采用原来的
      if (!title) titleStyle = this.element.content.titleStyle

      const { type, angle, gradientColor} = titleStyle
      if (type === 'gradient') {
        return Object.assign(style, getGradientStyle(angle, gradientColor, style), { 'display': 'inline-block'})
      }
      return style
    },
    // 对外提供清空返回数据方法
    deleteElementData() {
      this.setElementLoadState(false)
      this.$delete(this.element.content, 'textResponse')
    },
    // 对外提供控制loading
    setElementLoadState(loading) {
      if (this.commonData.isOffApiLoading) return '关闭loading'
      if (this.loading === loading) return
      this.loading = loading
      this.elDom = (this.themeData.themeFullScreen || this.fullscreenData.enlargeVisible) ? this.$el : (document.getElementById(this.element.id) || this.$el)
      if (this.setLoadingStatus(loading, this.element.id)) {
        return
      }
      this.loadingInstance && this.loadingInstance.close()
      if (loading && this.elDom) {
        this.loadingInstance = this.$loading({
          target: this.elDom,
          spinner: 'sdp-loading-gif',
        })
        insetDom(this.loadingInstance.$el)
      }
    },
    getAggType(e) {
      if (e.customerExprDim) return undefined
      if (e.columnTpe === 'date' && (e.aggType !== 'expression')) return 'MAX'
      return getAggType(e)
    },
    requestAdapter({ type }) {
      const { referenceField = [], dragcascaderValue = {}, caculationMethodType, disabledCalendarFnFilter = false, filters = [], earlyWarningOptions = [] } = this.element.content
      if (!referenceField.length) {
        this.hasResponseData = true
        this.$nextTick(() => {
          this.$refs.richTextRef && this.$refs.richTextRef.initData('response')
        })
        return false
      }
      this.setElementLoadState(true)
      const metrics = referenceField.map(item => {
        const metric = dragcascaderValue[item.uniqueKey]
        const metricParams = this.$_JSONClone(metric)
        transfromZeroFormat(metricParams)
        let result = Object.assign(
          metricParams,
          {
            columnType: metric.columnTpe,
            columnName: metric.labeName,
            alias: metric.alias || metric.labeName,
            aggType: this.getAggType(metric),
            dataSetId: metric.parentId,
          },
          metric.columnTpe === 'date' ? {
            compareShowNameType: metric.aggType === 'MAX' ? undefined : 'periodtext_curr_day',
          } : {
            compareInfo: metric.aggType === 'CONTRAST' && metric.selectedConttast ? { compSubType: metric.selectedConttast } : {},
            compareRule: metric.aggType === 'CONTRAST' && metric.selectedConttastMode ? metric.selectedConttastMode : '',
          },
          metric.customFieldInfo || {}
        )

        setLgeTypeValue(result, metric) // 自定义、业务函数、排序时，需要设置bFxType和lgeType
        const o = this.getCustomDatasetFieldDetail(result, this.element.type)
        if (item.webFieldType === 'customComputed') {
          this.extendFieldByKeyName(metric, item, o)
        }
        return o
      })

      const warningFields = this.$_JSONClone(earlyWarningOptions.filter(item => item.fieldType !== 'fixed').map(item => item?.contrastField))
      const warningMetrics = warningFields.map(metric => {
        const metricParams = this.$_JSONClone(metric)
        transfromZeroFormat(metricParams)
        let result = Object.assign(
          metricParams,
          {
            columnType: metric.columnTpe,
            columnName: metric.labeName,
            alias: metric.alias || metric.labeName,
            aggType: this.getAggType(metric),
            dataSetId: metric.parentId,
          },
          metric.columnTpe === 'date' ? {
            compareShowNameType: metric.aggType === 'MAX' ? undefined : 'periodtext_curr_day',
          } : {
            compareInfo: metric.aggType === 'CONTRAST' && metric.selectedConttast ? { compSubType: metric.selectedConttast } : {},
            compareRule: metric.aggType === 'CONTRAST' && metric.selectedConttastMode ? metric.selectedConttastMode : '',
          },
          metric.customFieldInfo || {}
        )
        setLgeTypeValue(result, metric) // 自定义、业务函数、排序时，需要设置bFxType和lgeType
        const o = this.getCustomDatasetFieldDetail(result, this.element.type)
        if (metric.webFieldType === 'customComputed') {
          this.extendFieldByKeyName(metric, metric, o)
        }
        return o
      })

      // 预警 注释 所需字段
      const warningRichTextFields = this.$_JSONClone(earlyWarningOptions.filter(item => item?.styles?.useRichText).map(item => {
        return item?.richTextEl?.content?.referenceField || []
      }).flat())
      const warningRichTextDragcascader = earlyWarningOptions?.map(item => {
        return item?.richTextEl?.content?.dragcascaderValue
      })
      const warningRichTextDragcascaderValues = Object.assign({}, ...warningRichTextDragcascader)
      const warningRichTextMetrics = warningRichTextFields.map(item => {
        const metric = warningRichTextDragcascaderValues[item.uniqueKey]
        const metricParams = this.$_JSONClone(metric)
        transfromZeroFormat(metricParams)
        let result = Object.assign(
          metricParams,
          {
            columnType: metric.columnTpe,
            columnName: metric.labeName,
            alias: metric.alias || metric.labeName,
            aggType: this.getAggType(metric),
            dataSetId: metric.parentId,
          },
          metric.columnTpe === 'date' ? {
            compareShowNameType: metric.aggType === 'MAX' ? undefined : 'periodtext_curr_day',
          } : {
            compareInfo: metric.aggType === 'CONTRAST' && metric.selectedConttast ? { compSubType: metric.selectedConttast } : {},
            compareRule: metric.aggType === 'CONTRAST' && metric.selectedConttastMode ? metric.selectedConttastMode : '',
          },
          metric.customFieldInfo || {}
        )
        setLgeTypeValue(result, metric) // 自定义、业务函数、排序时，需要设置bFxType和lgeType
        const o = this.getCustomDatasetFieldDetail(result, this.element.type)
        if (metric.webFieldType === 'customComputed') {
          this.extendFieldByKeyName(metric, metric, o)
        }
        return o
      })

      const requestParams = {
        id: this.element.id,
        metrics: [...metrics, ...warningMetrics, ...warningRichTextMetrics],
      }
      if (filters.length) {
        const referToCalendar = caculationMethodType === DATA_FILTER.targetCaculationMethod
        requestParams.filters = filters.map(item => {
          return Object.assign(this.$_JSONClone(item), { referToCalendar, disabledCalendarFnFilter: referToCalendar ? disabledCalendarFnFilter : undefined })
        })
      }
      return {
        requestKey: 'textElement',
        requestParams,
      }
    },
    responseAdapter(res) {
      const boardData = this.getBoardData()
      const componentParamShow = getComponentParamShow({
        paramsPanelList: boardData.paramsPanelList,
        runType: this.commonData.paramsType,
        dateFormat: this?.tenantData?.tenant?.dateFormat,
        elementId: this.element.id,
        sdpBus: this.sdpBus
      })
      console.log('componentParamShow', componentParamShow)
      // 处理接口返回值, 符合参数组件替换的则进行替换
      const textResponse = res.textResponse || {}
      if (componentParamShow) {
        const row = textResponse?.rows?.[0] || {}
        const { referenceField, dragcascaderValue } = this.element.content
        referenceField.forEach(field => {
          const cascader = dragcascaderValue[field.uniqueKey]
          if (cascader && cascader.textDomain && cascader.textDomain !== 'none') {
            let val = componentParamShow[cascader.textDomain]
            if (cascader.textDomain === 'timePre') {
              switch (cascader.compareType) {
                case THIS_PERIOD_KEY_TYPE.infrasys:
                  val = componentParamShow['timePre_infrasys']
                  break
                case THIS_PERIOD_KEY_TYPE.monthOnMonth:
                  val = componentParamShow['timePre_month']
                  break
                case THIS_PERIOD_KEY_TYPE.yearOnYear:
                  val = componentParamShow['timePre_year']
                  break
              }
            }
            val && (row[`VIEWFORMAT_${cascader.alias || cascader.labeName}`] = val)
          }
        })
      }
      this.setElementLoadState(false)
      this.$set(this.element.content, 'textResponse', textResponse)
      this.hasResponseData = true
      this.$nextTick(() => {
        this.$refs.richTextRef && this.$refs.richTextRef.initData('response')
      })
    },
    // 返回看板跳转当前文本和变量内容
    getVariableAndText() {
      const variable = []
      const text = []
      let { referenceField = [], title: titleText } = this.element.content
      const fun = function (rowData, rol, datasetList) {
        const { labeName, parentId, id } = rowData
        let datasetItem = datasetList.find(item => item.id === parentId)
        const itemData = {
          val: labeName,
          dataSetId: parentId,
          dataSetName: datasetItem?.labeName || '',
          site: id,
          keyName: id
        }
        variable.push(itemData)
      }
      referenceField.filter(item => item.columnTpe !== 'date').forEach((rowData, rol) => {
        if (!variable.find(el => el.site === rowData?.id)) {
          fun(rowData, rol, this.datasetList)
        }
      })

      titleText = titleText.trim ? titleText.trim() : ''

      return {
        titleText,
        text,
        variable,
      }
    },
    // 交互设置拼装数据
    getInteractionData() {
      let { referenceField = [], dataSetIds = [] } = this.element.content
      let data = referenceField.filter(item => ['string'].includes(item.columnTpe)).reduce((arr, item) => {
        let { labeName, parentId, id } = item
        let datasetItem = this.datasetList.find(data => data.id === parentId)
        let existItem = arr.find(cur => cur.id === parentId) || {}
        existItem.id = parentId
        existItem.name = datasetItem?.labeName || ''
        existItem.fieldList = existItem.fieldList || []
        if (existItem.fieldList && !existItem.fieldList.find(el => el.site === id)) {
          existItem.fieldList.push({
            label: labeName,
            value: labeName,
            site: id,
            webFieldType: item.webFieldType,
          })
          if (existItem.fieldList?.length <= 1) arr.push(existItem)
        }
        return arr
      }, [])
      console.log(data, '交互设置拼装数据')
      return data.filter(e => e.id)
    },
    titleClickHandler() {
      // 大屏模式下禁止交互、跳转
      if (!this.superLinkNeedClick) return
      const titleSuperLinkOptions = this.superLinkOptions.find(item => item.labelBoard.type !== SUPERLINK_CONST_TYPE.variavle && item.labelBoard.id === this.element.id)
      if (!titleSuperLinkOptions) return

      if (titleSuperLinkOptions?.openMethod === '2' && (this.utils.isPcMobile || this.commonData.isMobileDataReport())) {
        return void 'PC转移动端后不能打开弹窗超链接'
      }

      if (this.commonData.isPreview) {
        const eventData = new EventData({
          type: 'setSuperLink',
          target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
          targetFn: 'setSuperLink',
          data: {
            options: titleSuperLinkOptions,
          },
        })
        this.$emit('eventBus', eventData)
      } else {
        this.$message.warning(this.$t('sdp.views.bplsClick'))
      }
    },
    elementInteraction(params) {
      // 字段超链接
      this.dimensionClickHandler(params)
      // 交互
      this.interActionClickHandler(params)
    },
    interActionClickHandler({ values, metric }) {
      // 大屏模式下禁止交互、跳转,this.themeFullScreen(预览时大屏)，this.fullscreenData.enlargeVisible(预览时元素放大或全屏)
      if (this.isChartLarge) return
      const interactionOptions = this.element.content.interactionOptions && this.element.content.interactionOptions.filter(item => {
        const { dataSetId, columnName } = item.associElements[0]
        return dataSetId === metric?.parentId && columnName === metric?.labeName
      })
      if (!interactionOptions || !interactionOptions.length) return
      const associElements = interactionOptions[0].associElements
      // 判断是否可以进行交互
      if (!isCanInteraction(this.element._containerId, associElements || [], this.elList)) return

      // 是否点击相同图块标识
      let theSameInteraction = true
      interactionOptions.map(item => item.values ? item.values[0] : null).forEach((curVal, index) => {
        if (values[index] === null || curVal !== values[index]) {
          theSameInteraction = false
        }
      })

      const data = {
        ids: [
          this.element.id,
          ...interactionOptions[0].associElements.map(
            elSetting => elSetting.id
          )
        ],
        element: { type: TYPE_INTERACTION.INTERACTION_SETTING }
      }
      data.ids.forEach((id, i) => {
        if (i === 0) return
        const associElement = this.elList.find(el => el.id === id)
        if (associElement.content.drillSettings?.drillDimensions) {
          delete associElement.content.drillSettings.drillDimensions
        }
      })
      this.element.content.interactionOptions.forEach((item, index) => {
        const { dataSetId, columnName } = item.associElements[0]
        // 只给对应字段设置交互值
        if (dataSetId !== metric.parentId || columnName !== metric.labeName) {
          this.$delete(item, 'values')
          return
        }
        // 判断过滤条件是否修改
        if (item.openFilter && filterMapVerify({ type: TYPE_ELEMENT_GROUP.CHART, filterMap: item.filterMap, filterData: this.$_getProp(this, 'element.content.filters', []) })) {
          this.$delete(item, 'filterMap')
          this.$delete(item, 'openFilter')
        }

        if (theSameInteraction) {
          this.$delete(item, 'values')
        } else {
          this.$set(item, 'values', values)
        }
      })
      this.refreshEl(data)
    },
    dimensionClickHandler({ values, metric }) {
      if (!this.superLinkNeedClick) return
      const superLinkOptions = this.superLinkOptions.find(item => {
        return item.labelBoard.type === SUPERLINK_CONST_TYPE.variavle && item.labelBoard.label === metric.labeName
      })
      if (!superLinkOptions) return

      if (superLinkOptions?.openMethod === '2' && (this.utils.isPcMobile || this.commonData.isMobileDataReport())) {
        return void 'PC转移动端后不能打开弹窗超链接'
      }

      if (this.commonData.isPreview) {
        const { parameterField = [], dataSets = [], labelBoard } = superLinkOptions
        let dataSetsParams = []
        dataSets.forEach(item => {
          item.columnName.forEach((columnItem, columnIndex) => {
            values[columnIndex] && dataSetsParams.push({
              columnName: columnItem,
              dataSetId: item.dataSetId,
              values: [values[columnIndex]],
            })
          })
        })

        const eventData = new EventData({
          type: 'setSuperLink',
          target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
          targetFn: 'setSuperLink',
          data: {
            options: superLinkOptions,
            superLink: {
              dataSets: dataSetsParams,
              dataSetId: labelBoard.dataSetId,
            },
          },
        })
        this.$emit('eventBus', eventData)
      } else {
        this.$message.warning(this.$t('sdp.views.bplsClick'))
      }
    },
    // 刷新编辑界面外的元素
    refreshEl(data) {
      const target = this.utils.isMobile ? EVENT_DATA_PARAMS.displayPanelMobile : EVENT_DATA_PARAMS.displayPanel
      const eventData = new EventData({
        source: 'elementText',
        target,
        targetFn: 'refreshEl',
        data,
      })
      this.$emit('eventBus', eventData)
    },
    // 对外提供error 提示
    setElementErrorState(error) {
      this.setErrorStatus(error, this.element.id)
    },
    // 获取引用的自定义字段
    getCustomFieldsUsedInElement(webFieldTypes = ['customComputed']) {
      if (webFieldTypes[0] === 'ALL') webFieldTypes = ['metricGroup', 'customComputed']
      const result = []
      this.element.content.referenceField.forEach(field => {
        if (webFieldTypes.includes(field?.webFieldType) && !result.find(f => f.id === field.id)) {
          result.push({
            parentId: field.parentId,
            id: field.id,
            webFieldType: field.webFieldType
          })
        }
      })
      return result
    },
    extendFieldByKeyName(data, originField, newField) {
      const extendObj = {
        metricGroupInfo: this.getMetricGroupInfo(originField),
        columnName: newField.columnName,
        customerExprField: true,
        exp: newField.exp,
        keyName: newField.keyName,
        customerExprDim: newField.customerExprDim,
        aggType: newField.customerExprDim ? undefined : 'expression',
        webFieldType: newField.webFieldType,
      }
      // 自定义字段信息
      this.$set(data, 'customFieldInfo', extendObj)
    }
  },
}
</script>
<style lang="scss" scoped>
/deep/ .DataBriefing{
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
.textColor {
  height: 100%;
  width: 100%;
  /*padding: 25px;*/
  &.not-mobile{
    padding: 0;
    .element-text-container{
      /*padding: 14px;*/
      box-sizing: border-box;
    }
  }
  &.only-title{
    /*padding-top: 6px;*/
    /*padding-bottom: 6px;*/
    .element-text-container{
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    &.not-mobile{
      padding: 0;
      .element-text-container{
        /*padding: 6px 14px;*/
      }
      /deep/ .el-scrollbar__wrap{
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
    }
    .DataBriefing{
      height: fit-content;
      max-height: 100%;
    }
  }
  color: var(--sdp-cszjsz-wzs1);
  &:not(.is-focus) {
    &:before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 99;
    }
  }
}
[iscontainer] {
  &.not-mobile{
    .element-text-container{
      padding: 14px;
    }
  }
  &.only-title {
    padding-top: 6px;
    padding-bottom: 6px;
    &.not-mobile{
      .element-text-container{
        padding: 6px 14px;
      }
    }
  }
}
.text_bg {
  background-color: var(--sdp-tb-bj);
}

.text_color {
  color: var(--sdp-cszjsz-wzs1);
}
.title_color {
  color: var(--sdp-cszjsz-wzs1);
}

.placeholder-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: var(--sdp-srk-bxwzs);
}

.element-text-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .placeholder-image {
    width: 100%;
    height: 100%;
    color: #999;
    font-size: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
}

.text {
  font-size: 16px;
  color: #999999;
}

.text_value {
  font-size: 12px;
  font-family: NotoSansHans-Regular;
  line-height: 1;
  justify-content: left;
  text-align: left;
  width: 100%;
  height: 100%;
  padding-bottom: 4px;
  margin-top: 10px;
}

.title_value {
  font-size: 14px;
  font-family: NotoSansHans-Regular;
  justify-content: left;
  text-align: left;
  height: auto;
  width: 100%;
  line-height: initial;
  .pointer-title {
    cursor: pointer;
  }
}
</style>
