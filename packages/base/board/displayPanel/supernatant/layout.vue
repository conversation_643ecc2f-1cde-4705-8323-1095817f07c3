<template>
  <!-- 特别提醒: 此组件当前是以 sdp-grid-layout 作为组件的根元素,
  如果 DOM 结构需要调整, 务必修改 packages/base/board/displayPanelMobile/index.vue 组件中的相关样式:
  .supernatant-layout-item > .sdp-grid-layout-container -->
    <sdp-grid-layout
        ref="layoutRef"
        :layout="layout"
        :rowHeight="rowHeight"
        :colNum="colNum"
        :margin="margin"
        :referenceLineOpts="notShowReferenceLine ? undefined : referenceLineOpts"
        :minHeight="gridLayoutMinHeight"
        :isDraggable="!commonData.isPreview"
        :isResizable="!commonData.isPreview"
        @layout-updated="updateMinHeight"
        @layout-ready="layoutReady"
      >
      <!-- getElement(item.i).style @move="disable" @moved="movedHandler" :itemStyle="getItemStyle(item.i)"-->
        <sdp-grid-item
            class="vue-grid-item"
            :ref="item.i"
            v-for="item in layout"
            :key="item.i"
            dragAllowFrom=".sdp-grid-item-body"
            :dragIgnoreFrom="`.bscroll-indicator,.sdp-sheet-scrollbar-thumb${ignoreDragMaps[item.i] ? `,${ignoreDragMaps[item.i]}` : ''}`"
            :minW="getMinW(item)"
            :minH="[TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE].includes(getElement(item.i).type) ? 3 : 6"
            v-bind="item"
            @resize="disable"
            @resized="resizeHandler"
            @mousedown="gridItemMousedown"
            @moved="gridItemMoved"
            @click="gridItemClickHandler(item.i)"
          >
            <div class="is-force-margin-left" v-if="isMobile && commonData.isPreview" :style="getBgColor(item.i, 'left')"></div>
            <div class="is-force-margin-right" v-if="isMobile && commonData.isPreview" :style="getBgColor(item.i, 'right')"></div>
            <sdp-grid-item-header class="hearder" v-if="!commonData.isMobileApp && !isAddDailyPreview && (!commonData.isPreview || (!isMobile && !getElement(item.i).style.cornerMark))" :id="item.i">
              <template slot="toolbar" slot-scope="{ isHold }">
                <el-tools :isHold="isHold" :boardScale="boardScale" :elementExport="boardInfo.elementExport" :boardInfo="boardInfo" :element="getElement(item.i)" :elList="elList" :elementId="item.i" class="elTools" :btns='elTools.btns' :moreBtns='elTools.moreBtns' :moreBtn='elTools.moreBtn' :containerBtn="elTools.containerBtn" :dailyConcernData="dailyConcernData" @eventBus="eventBus" />
              </template>
            </sdp-grid-item-header>
            <sdp-grid-item-body class="sdpGridItemBody" :style="getElementStyle(item.i)" :class="[(getElement(item.i).id === highlightItem.id || (getElement(item.i).content.drillSettings && getElement(item.i).content.drillSettings.datasethighLightItemFlag )) && !commonData.isPreview ? 'body' : '', getElement(item.i).style.border?'element-border':'']">
              <slot
                :i="item.i"
                :el="getElement(item.i)"
                :itemStyle="getItemStyle(item.i)"
                :boardInfo="boardInfo"
                :isBrowserAdaptationMode="isBrowserAdaptationMode"
                :browserAdaptationEnabled="browserAdaptationEnabled"
              ></slot>
            </sdp-grid-item-body>
          </sdp-grid-item>
    </sdp-grid-layout>
</template>

<script>// @ts-nocheck

import eventBus from 'packages/assets/eventBus'
import {
  SdpGridItem,
  SdpGridItemBody,
  SdpGridItemHeader,
  SdpGridLayout,
} from '../../../../base/common/sdpGrid/index'
import { getPosition } from './utils/utils'
import { EVENT_BUS, SUPERNATANT_LAYOUT, SUPERNATANT_TRANSITION_DURATION, TYPE_ELEMENT } from '../constants'
import elTools from './elTools'
import EventData from 'packages/assets/EventData'
import { THEME_CONFIG } from '../../../grid/helpers/constants'
import { BOARD_ELEMENT_THEME } from './boardElements/BoardElement'

import containerBrowserAdaptationModeMixin from './containerBrowserAdaptationMode'
import { RECORD_OPERATE_TYPE } from '../../mixins/boardRecord'
import { GRID_LAYOUT_ROW_HEIGHT_FOR_BORDER_DISPLAY } from '../../../../assets/constant'

// cleanStyle
export default {
  name: SUPERNATANT_LAYOUT,
  inject: {
    commonData: { default: {} },
    themeData: { default: {} },
    sdpBus: { default: {} },
    parentScroll: {
      default: null
    },
    utils: { default: {} },
    boardRecord: { default: {} },
    getBoardUnderIdElement: { default: () => (name) => { return document.getElementById(name.slice(1)) } },
    elBgColorFormatter: { default: () => ({}) },
    getActiveDisplayPanel: { default: () => () => '' }
  },

  mixins: [ containerBrowserAdaptationModeMixin ],

  props: {
    needTransition: Boolean,
    init: Boolean,
    isFinish: Boolean,
    boardScale: Number,
    content: {
      type: Array,
      default: () => []
    },
    elList: {
      type: Array,
      default: () => []
    },
    boardInfo: {
      type: Object,
    },
    layoutDeploy: {
      type: Object,
    },
    active: {
      type: Boolean,
      default: false,
    },
    highlightItem: {
      type: Object,
      default: () => {}
    },
    dailyConcernData: {
      type: Object,
      default: () => {}
    },
    flattenElementComp: {
      type: Array,
    },
    elTools: {
      type: Object,
    },
    referenceLineOpts: Object,
    fullscreenRestHeight: {
      type: Number,
      required: true,
    },
    isScreenSkipToScreen: {
      type: Boolean
    },
    tagId: {
      type: String
    },
    storeSet: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    elTools,
    SdpGridLayout,
    SdpGridItem,
    SdpGridItemHeader,
    SdpGridItemBody,
  },
  data() {
    return {
      TYPE_ELEMENT,
      layout: [],
      rowHeight: this.layoutDeploy.rowHeight,
      // colNum: this.layoutDeploy.colNum,
      // margin: this.layoutDeploy.margin,
      // observer: null,
      gridLayoutMinHeight: 0,
      oldElList: [],
      layoutRef: null,
      isAppClass: false,
      // 在这个map中存在class名，则表示该class名不会被拖动
      ignoreDragMaps: {
        // 元素ID：'.sdp-chart-content'
        // '8535e54c-f46a-4aa0-e0aa-5a6e67d8bc28': ',.sdp-chart-content'
      },
      mutaionObservers: {
        // 元素ID：MutationObserver实例
      }
    }
  },
  computed: {
    isApp() {
      return this.commonData.isMobileApp
    },
    isPreview() {
      // 初始化layout格式
      // this.initElementStyle()
      return this.commonData.isPreview
    },
    visibleElList() {
      let list = this.elList.filter(el => !el._containerId)
      if ((this.isMobile && this.utils.isPcMobile) || this.commonData.isMobileDataReport()) {
        const o = this.storeSet?.mobileSet?.elementList || {}
        let checked = o[this.tagId] || []
        list = list.filter(item => checked.includes(item.id))
      }
      return list
    },
    currentElList() {
      let list = this.elList.filter(el => this.content.includes(el.id))
      if ((this.isMobile && this.utils.isPcMobile) || this.commonData.isMobileDataReport()) {
        const o = this.storeSet?.mobileSet?.elementList || {}
        let checked = o[this.tagId] || []
        list = list.filter(item => checked.includes(item.id))
      }
      return list
    },

    isMobile() {
      return this.utils.isMobile
    },

    notShowReferenceLine() {
      return !this.active || this.isMobile || this.isPreview
    },
    getLayoutRef() {
      return this.layoutRef
    },
    gridLayoutWidth() {
      if (this.getLayoutRef) {
        return this.getLayoutRef.width || 0
      } else {
        return 0
      }
    },
    colNum() {
      return this.layoutDeploy.colNum || GRID_LAYOUT_ROW_HEIGHT_FOR_BORDER_DISPLAY
    },
    margin() {
      return this.layoutDeploy.margin
      // return [32, 32]
    },

    advanceContainerIncludeElsIdList() {
      return this.elList.reduce((idList, el) => {
        if (el.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
          const tabList = el.content.tabList
          tabList.forEach(tab => idList.push(...tab.content.includedElsIdList))
        }
        return idList
      }, [])
    },

    activeDisplayPanel() {
      if (this.getActiveDisplayPanel) {
        return this.getActiveDisplayPanel()
      }
      return ''
    },

    isAddDailyPreview() {
      const sbiOptions = this.utils?.sbiOptions || {}
      return sbiOptions.isSbiDashPreview && sbiOptions.isAddDailyConcernElement
    },
  },
  watch: {
    'themeData.themeFullScreen': {
      immediate: false,
      handler(bool) {
        // themeData.screenMode --> 是否开启大屏
        // themeData.themeFullScreen --> 是否为大屏状态
        if (this.commonData.isPreview && this.themeData.screenMode) {
          // bool ? this.toOnePictureMode() : this.exitOnePictureMode()
          // setTimeout(() => (this.themeData.isOnePictureMode = bool))
          const callback = () => (this.themeData.isOnePictureMode = bool)
          bool ? this.toOnePictureMode(callback) : this.exitOnePictureMode(callback)
        }
      }
    },
    active(val) {
      this.updateMinHeight()
      val && this.updateResize()
      this.$nextTick(() => {
        if (val) {
          // 调用图形重新渲染接口
          this.flattenElementComp.forEach(el => {
            if (this.content.includes(el.id || (el.element && el.element.id))) {
              el.resizeEcharts && el.resizeEcharts({ retainScroll: false, })
            }
          })
        }
      })
    },
    visibleElList: {
      handler(val) {
        // 预览状态改变看板元素状态
        this.layout = val.filter(el => this.content.includes(el.id)).map(el => el.layout)
        // this.$nextTick(() => {
        //   this.initElementStyle()
        // })
      },
      immediate: true,
    },
    isPreview: {
      handler(val, old = false) {
        // 预览状态改变看板元素状态 看板跳转直接关闭会拦截
        // if (val !== old) {
        this.layout.forEach(item => {
          this.$set(item, 'static', val)
        })
        // this.$nextTick(() => {
        //   this.initElementStyle()
        // })
        // }
      },
      immediate: true,
    },
    gridLayoutWidth(val) {
      this.setContainerStyle(val)
    },
    'utils.isPersonalAttentionEdit'(val) {
      if (val) {
        setTimeout(() => {
          this.updateMinHeight()
        }, 10)
      }
    },
    currentElList: {
      handler(val) {
        this.debounceListenGridChartDoms()
      },
      deep: true,
    },

  },
  mounted() {
    if (this.parentScroll) {
      this.parentScroll.disable = this.$_throttle(this.parentScroll.disable)
    }

    this.updateMinHeight()
    this.isScreenSkipToScreen && this.superLinkInitToOnePictureMode()

    this.isAppClass = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  },

  created() {
    this['sdpBus'].$on(EVENT_BUS.FEATURE_HEIGHT_UPDATE, this.updateMinHeight)
    this['sdpBus'].$on(EVENT_BUS.LAYOUT_CHANGE, this.updateResize)
    window.addEventListener('resize', this.updateMinHeight)
    this.debounceListenGridChartDoms = this.$_debounce(this.listenGridChartDoms, 500)
  },

  destroyed() {
    // window.removeEventListener('resize', this.initElementStyle)
    this['sdpBus'].$off(EVENT_BUS.FEATURE_HEIGHT_UPDATE, this.updateMinHeight)
    this['sdpBus'].$off(EVENT_BUS.LAYOUT_CHANGE, this.updateResize)
    window.removeEventListener('resize', this.updateMinHeight)
    this.clearMutationObservers()
  },
  methods: {
    eventBus,
    updateResize() {
      this.$nextTick(() => {
        // eslint-disable-next-line no-unused-expressions
        this.$refs.layoutRef?.onWindowResize?.()
      })
    },
    getMinW(item) {
      if (this.isMobile || this.commonData.isMobileApp) {
        return 16
      }
      if (this.getElement(item.i).type === 'text') {
        return 3
      }
      if (this.layoutDeploy.colNum < 144) {
        return 1
      }
      return 6
    },
    getBgColor(id, type) {
      const el = this.getElement(id)
      const bg = this.elBgColorFormatter(el, true, { getPositionColor: type })
      const { style = {} } = el || {}
      if (style.hasOwnProperty('border')) {
        Object.assign(bg, {
          border: style['border'],
          borderRightWidth: 0,
          borderLeftWidth: 0,
        })
      }
      if (style.hasOwnProperty('borderRadius')) {
        let obj = { borderRadius: style['borderRadius'] }
        if (type === 'left') {
          obj['borderTopRightRadius'] = 0
          obj['borderBottomRightRadius'] = 0
        } else {
          obj['borderTopLeftRadius'] = 0
          obj['borderBottomLeftRadius'] = 0
        }
        Object.assign(bg, obj)
      }
      return bg
    },
    superLinkInitToOnePictureMode() {
      // 解决初始化第一次
      console.log('kyz === 1')
      if (this.themeData.themeFullScreen) {
        const callback = () => (this.themeData.isOnePictureMode = true)
        this.toOnePictureMode(callback)
      }
    },
    resizeGrid() {
      this.$refs.layoutRef.onWindowResize()
    },
    // 初始化style
    // initElementStyle(callback) {
    // this.$nextTick(() => {
    // this.layout
    // this.currentElList.forEach(item => {
    //   const { width, height } = getPosition.call(this, item.layout)
    //   const el = this.getElement(item.id)
    //   // 清除不需要的样式 暂时禁用
    //   // cleanStyle(el.style)
    //   this.$set(el.style, 'width', width)
    //   this.$set(el.style, 'height', height)
    // })
    //     callback && callback()
    //   })
    // },
    getElement(id) {
      return this.elList.find(el => el.id === id)
    },
    getElementStyle(id) {
      const el = this.getElement(id)
      const { style = {}, themeMap = {} } = el || {}
      return { ...style }
    },
    // 尺寸动态和最后修改单个element宽高
    resizeHandler(i, newH, newW, newHPx, newWPx) {
      console.log('%c resized>', 'background: green; color: red;', newHPx, newWPx)
      // const el = this.getElement(i)
      // this.$set(el.style, 'width', newWPx + 'px')
      // this.$set(el.style, 'height', newHPx + 'px')
      // this.parentScroll && this.parentScroll.enable()

      if (this.elList.length <= 1) {
        this.$nextTick(() => {
          this.updateLayoutByElList()
        })
      }

      this['sdpBus'].$emit(EVENT_BUS.HIDE_ADVANCE_ITEM, {
        id: i,
        type: 'resized'
      })
      this.recordELementLayout('element_resized_layout')
    },
    // movedHandler() {
    //   // this.parentScroll && this.parentScroll.enable()
    // },
    disable(i) {
      this.parentScroll && this.parentScroll.disable()
      this['sdpBus'].$emit(EVENT_BUS.HIDE_ADVANCE_ITEM, {
        id: i,
        type: 'resize'
      })
    },
    gridItemClickHandler(id) {
      if (this.isPreview) return
      this.$emit('gridItemClickHandler', id)
    },
    gridItemMousedown(event) {
      if (this.isPreview) return
      console.log('%c gridItemMousedown>', 'background: pink; color: red;', event)
      this.oldElList = this.elList.map(el => {
        return {
          id: el.id,
          value: {
            layout: this.$_deepClone(el.layout),
          }
        }
      })
    },
    gridItemMoved(event) {
      if (this.isPreview) return
      console.log('%c gridItemMoved>', 'background: pink; color: red;', event)
      this.recordELementLayout()
    },
    recordELementLayout(source = 'element_moved_layout') {
      const valueList = this.elList.map(el => {
        return {
          id: el.id,
          value: {
            layout: this.$_deepClone(el.layout),
          }
        }
      })
      this.boardRecord.undoSave({
        type: RECORD_OPERATE_TYPE.ELEMENTS,
        key: '',
        value: valueList,
        oldValue: this.oldElList,
        callback: () => {
          this.updateLayoutByElList()
        },
        source: 'element_moved_layout'
      })
    },
    updateLayoutByElList() {
      const visibleElIdList = this.visibleElList.map(e => e.id)
      this.layout = this.elList.filter(el => !el._containerId && visibleElIdList.includes(el.id)).filter(el => this.content.includes(el.id)).map(el => el.layout)
    },
    setContainerStyle(layoutWidth) {
      if (this.rowHeight !== this.layoutDeploy.rowHeight) return

      this.currentElList.filter(el => !this.advanceContainerIncludeElsIdList.includes(el.id)).forEach(el => {
          const { width, height } = getPosition.call(this, el.layout, layoutWidth)
          console.log('layoutWidth', width, height)
          this.$set(el.style, 'width', width)
          this.$set(el.style, 'height', height)
        })
    },
    getItemStyle(id) {
      if (this.activeDisplayPanel === 'PC') return
      this.$nextTick(() => {
        const ref = this.$refs[id]
        if (ref) {
          const { style } = ref
          // const itemMargin = this.margin[0] + 'px'
          if (style) {
            const el = this.currentElList.find(el => el.id === id)
            this.$set(el.style, 'width', style.width)
            this.$set(el.style, 'height', style.height)
            // this.$set(el.style, 'margin', itemMargin)
          }
        }
      })
    },

    updateMinHeight() {
      if (this.notShowReferenceLine) return

      this.gridLayoutMinHeight = window.innerHeight

      setTimeout(() => {
        // const borderBox = document.getElementById('board-display-panel-border-box')
        const idName = this.utils.isDailyConcerned ? '#data-screen-table' : '#board-display-panel-border-box'
        const borderBox = this.getBoardUnderIdElement(idName)

        if (!borderBox) return

        const borderBoxHeight = borderBox.clientHeight

        const borderBoxTop = borderBox.getBoundingClientRect().top

        const top = this.$el.getBoundingClientRect().top

        this.gridLayoutMinHeight = borderBoxHeight + borderBoxTop - top
      }, this.needTransition ? SUPERNATANT_TRANSITION_DURATION : 0)
    },

    layoutReady() {
      this.layoutRef = this.$refs.layoutRef
      this.$emit('layout-ready', this.$refs.layoutRef)
    },

    toOnePictureMode(callback) {
      console.log('\n大屏自适应 - toOnePictureMode - start')

      setTimeout(() => {
        const marginY = this.margin[1]
        const height = this.$el.clientHeight
        const restHeight = this.fullscreenRestHeight
        const gridLayoutHeight = Math.round(+height)
        const gridLayoutTop = Math.round(+restHeight)
        const totalHeight = window.screen.height - gridLayoutTop
        // alert(gridLayoutTop)
        const rowCount = (gridLayoutHeight - marginY) / (this.rowHeight + marginY)

        this.rowHeight = (totalHeight - marginY - marginY * rowCount) / rowCount
        console.log('大屏自适应 - rowHeight:', this.rowHeight)

        callback && callback()

        console.log('大屏自适应 - toOnePictureMode - end\n\n')
      })
    },

    exitOnePictureMode(callback) {
      console.log('\n大屏自适应 - exitOnePictureMode - start')

      this.rowHeight = this.layoutDeploy.rowHeight
      console.log('大屏自适应 - rowHeight:', this.rowHeight)

      setTimeout(() => {
        this.$refs.layoutRef.onWindowResize()
        callback && callback()

        console.log('大屏自适应 - exitOnePictureMode - end\n\n')
      })
    },
    // dataZoom 开启的图形需要让它在编辑界面能够被拖动缩放条
    listenGridChartDoms() {
      this.clearMutationObservers()
      if (this.isPreview || this.isMobile) return
      const charts = this.currentElList.filter(item => item.type === 'chart' && item.content?.chartUserConfig?.dataZoomChecked)
      if (!charts.length) return
      charts.forEach(chart => {
        let id = chart._containerId ? chart._containerId : chart.id
        const el = document.getElementById(id)
        if (el) {
          const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
              const dom = mutation.target
              if (mutation.type === 'attributes' && dom.nodeName === 'DIV' && dom?.firstChild?.nodeName === 'CANVAS') {
                const chartId = mutation?.target?.parentNode?.getAttribute('element-id')
                if (!chartId) return
                if (dom && dom.style.cursor === 'ew-resize') {
                  this.$set(this.ignoreDragMaps, id, `.sdp-chart-content.chart_content_${chartId}`)
                } else {
                  this.$set(this.ignoreDragMaps, id, '')
                }
                this.$nextTick(() => {
                  this.$refs[id] && this.$refs[id].tryMakeDraggable()
                })
              }
            })
          })
          observer.observe(el, {
            attributes: true,
            childList: true,
            subtree: true
          })
          this.$set(this.mutaionObservers, id, observer)
        }
      })
    },
    clearMutationObservers() {
      Object.keys(this.mutaionObservers).forEach(key => {
        this.mutaionObservers[key].disconnect()
      })
      this.mutaionObservers = {}
    }
  },
}
</script>

<style lang="scss" scoped>
/deep/ .vue-grid-item {
  &.no-touch {
    touch-action: auto !important;
  }
  /*transition: none;*/
  position: relative;

  &:hover {
    z-index: 2;
  }
  .is-force-margin-left {
    height: 100%;
    width: 10px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: left 300ms, z-index 50ms;
    opacity: 0;
  }
  .is-force-margin-right {
    height: 100%;
    width: 10px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: -1;
    transition: right 300ms, z-index 50ms;
    opacity: 0;
  }
  .is-force {
    .is-force-margin-left{
      left: -9px;
      z-index: 1;
      transition: left 300ms, z-index 50ms;
      opacity: 1;

      border-color: transparent !important; // 处理bug: 85067,  在选中的时候四个角有边框，将border-color 置为transparent
    }
    .is-force-margin-right{
      right: -9px;
      z-index: 1;
      transition: right 300ms, z-index 50ms;
      opacity: 1;
      
      border-color: transparent !important; // 处理bug: 85067,  在选中的时候四个角有边框，将border-color 置为transparent
    }
    .sdpGridItemBody {
      border-radius: 0 !important;
    }
    .sdp-tools-dots {
      display: block !important;
    }
  }
}
.element-border {
  border-color: var(--sdp-srk-bks)!important;
}
.elTools{
  /*padding: 8px 12px 8px 0;*/
  height: 30px;
  line-height: 30px;
  // border-radius: 8px;
}
.hearder{
  top: 1px;
  right: 1px;
  z-index: 20000;
  width: calc(100% - 2px);
}
.body {
  border: 1px solid var(--sdp-zs) !important;
  @include sdp-mixin-style($type: (
    boxShadow: (elementClickShadow:true)
  ), $self: (
    sdp-classic-white: (
      elementClickShadow: 0 2px 5px 0 rgba(0,24,120,0.40)
    ),
    sdp-dark-blue: (
      elementClickShadow: 0 2px 5px 0 rgba(107,144,185,0.32)
    )
  ));
}
</style>
