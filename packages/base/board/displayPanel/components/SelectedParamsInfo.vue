<template>
  <div class="selected-params-info" :class="[isTabChange?'info-mask':'', isDarkMode ? 'theme-background' : '']" v-show="show">
    <!-- 数据更新时间 -->
    <div class="param-list" v-if="boardUpdateTime.show">
      <div class="param-item">
        <span class="name" :class="[isDarkMode ? 'theme-dark-mode-name' : '']">{{boardUpdateTime.titleName}}:</span>
        <p class="value-list">
          <span class="value deletevalue" :class="[isDarkMode ? 'theme-dark-mode-detail' : '']">{{boardUpdateTime.updateTime}}</span>
        </p>
      </div>
    </div>
    <div
      class="param-list"
      v-for="(paramEl,index) in orderParamsArr"
      :key="index"
    >
      <div class="param-item" v-if="bool(paramEl)" v-show="calender(paramEl)">
        <span :style="isAll(paramEl)?'display:initial !important;':''" class="name" :class="[isDarkMode ? 'theme-dark-mode-name' : '']">{{paramEl.elName}}:</span>
        <span
          v-if="!isAll(paramEl)"
          class="value deletevalue"
          :class="[isDarkMode ? 'theme-dark-mode-detail' : '']"
          style="-webkit-box-orient: vertical;"
          :title="showParamsValue(paramEl)"
          v-text="languageChangeWithAll(showParamsValue(paramEl))"
        ></span>
        <div
          v-if="isAll(paramEl)"
          :style="'display:contents'"
          class="value deletevalue"
          :class="[isDarkMode ? 'theme-dark-mode-detail' : '']"
          style="-webkit-box-orient: vertical;"
          :title="showParamsValue(paramEl)"
          @click="isShowAll = !isShowAll"
        >{{languageChangeWithAll(showParamsValue(paramEl))}}
           <i
             :class="[isDarkMode ? 'theme-dark-mode-detail' : '',isShowAll?'el-icon-arrow-down':'el-icon-arrow-right']"
             v-if="isAll(paramEl)" ></i>
        </div>
      </div>
      <span
        v-if="bool(paramEl) && isAll(paramEl) && isShowAll"
        class="value deletevalue"
        :class="[isDarkMode ? 'theme-dark-mode-detail' : '']"
        style="-webkit-box-orient: vertical;"
        :title="showParamsAllValue(paramEl)"
        v-text="languageChangeWithAll(showParamsAllValue(paramEl))"
      ></span>

      <div v-show="calender(paramEl)" v-for="(el, names, i) in getLabels(paramEl)" :key="names" v-else>
        <div class="param-item" v-if="!paramEl.isHideElement && Array.isArray(el)&&el.length">
          <span :style="isAll2(paramEl, el)?'display:initial !important;':''" class="name" :class="[isDarkMode ? 'theme-dark-mode-name' : '']">{{nameFun(names, paramEl)}}:</span>
          <span
            v-if="!isAll2(paramEl,el)"
            class="value deletevalue"
            :class="[isDarkMode ? 'theme-dark-mode-detail' : '']"
            style="-webkit-box-orient: vertical;"
            :style="{'word-break': ['firefox', 'safari'].includes(agent) ? 'normal' : 'break-all'}"
            :title="decideFun(el,'title')"
          >{{languageChangeWithAll(decideFun(el).join(', '))}}</span>
          <div
            v-if="isAll2(paramEl,el)"
            class="value deletevalue"
            :class="[isDarkMode ? 'theme-dark-mode-detail' : '']"
            style="-webkit-box-orient: vertical;display:contents"
            :style="{'word-break': ['firefox', 'safari'].includes(agent) ? 'normal' : 'break-all'}"
            :title="decideFun(el,'title')"
            @click="isAll2(paramEl,el)? checkAll(i): ''"
          >{{languageChangeWithAll(decideFun(el).join(', '))}}
           <i
             :class="[isDarkMode ? 'theme-dark-mode-detail' : '',isCheck(i)?'el-icon-arrow-down':'el-icon-arrow-right']"
             v-if="isAll2(paramEl,el)" ></i>
          </div>
          <span
            v-if="isAll2(paramEl,el) && isCheck(i)"
            class="deletevalue value-all"
            :class="[isDarkMode ? 'theme-dark-mode-detail' : '']"
            style="-webkit-box-orient: vertical;"
            :title="showParamsAllValue(paramEl)"
            v-text="languageChangeWithAll(showParamsAllValue(paramEl))"
          ></span>
        </div>
      </div>
    </div>
    <!-- 货币语言特殊处理 -->
    <div class="param-list" v-if="languaeAndCurrency && boardInfo && boardInfo.currencyInfo">
      <div class="param-item">
        <el-tooltip class="item" effect="dark" :content="$t('sdp.views.currencyTips')" placement="bottom">
          <!-- <i class="icon sdpiconfont icon-sdp-beizhu" :class="[ isDarkMode ? 'theme-dark-mode-icon ' : '']" ></i> -->
        </el-tooltip>
        <span class="name" :class="[isDarkMode ? 'theme-dark-mode-name' : '']">{{currencyAlias}}:</span>
        <p class="value-list">
          <span class="value deletevalue" :class="[isDarkMode ? 'theme-dark-mode-detail' : '']">{{languaeAndCurrency}}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { TEXT_RADIO_TYPE, TYPE_PARAM_ELEMENT } from 'packages/base/board/displayPanel/params/utils/constants'
import { sortLayout } from 'packages/base/common/sdpGrid/utils'
import { getParamElementLabels } from './utils'
import { RUN_TYPE, THEME_TYPE } from 'packages/assets/constant'
import { PARAM_FILTER_LABLE_TYPE_LABEL_SELECT } from '../params/paramElement/locationNew/settingMixin_intersectionLocation'
import { languageChangeWithAll } from '../params/utils/utils'
import { LabelSelectType } from '../params/paramElement/locationNew/constant'
import { COMMON_VALUE } from '../params/paramElement/locationNew/utils'
import { getCurrency } from '../supernatant/utils/utils'
import { EVENT_BUS } from 'packages/base/board/displayPanel/constants'
import { boardUpdateTime } from 'packages/base/board/displayPanel/utils/helpers/api'

export default {
  inject: ['commonData', 'utils', 'themeData', 'tenantData', 'sdpLangcode', 'levelData', 'sdpBus'],
  name: 'selectedParamsInfo',
  props: {
    paramsPanelList: {
      type: Array,
      default: () => [],
    },
    languageAndCurrency: {
      type: Object
    },
    isEnterPriseCurrency: {
      type: Boolean
    },
    show: {
      type: Boolean,
      default: true
    },
    exporting: {
      type: Boolean,
      default: false
    },
    paramsType: {
      type: String
    },
    boardInfo: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    exporting: {
      handler(val) {
        if (val) {
          try {
            document.querySelectorAll('.deletevalue').forEach(item => {
              item.className = 'deletevalue theme-dark-mode-name'
            })
          } catch (err) {
            console.log(err)
          }
        } else {
          try {
            document.querySelectorAll('.deletevalue').forEach(item => {
              item.className = 'deletevalue value theme-dark-mode-detail'
            })
          } catch (err) {
            console.log(err)
          }
        }
      },
      immediate: true
    },
    activeTabIds(val) {
      // let time = this.isFirstChange ? 1500 : 500
      this.isTabChange = true
      // setTimeout(() => {
      //   this.isFirstChange = false
      //   this.isTabChange = false
      // }, time)
    },
    languageAndCurrency: {
      handler(newval) {
        this.setCurrency()
      },
      deep: true
    },
    isEnterPriseCurrency(newval) {
      this.setCurrency()
    },
    paramsPanelCont: {
      handler(newval) {
        this.orderParamsList(newval)
      },
      immediate: true
    },
    activeTab: {
      handler(newval) {
        this.runtype = newval?.runtype || ''
      },
      deep: true,
      immediate: true
    },
    'boardInfo.id': {
      handler(newVal, oldVal) {
        if(newVal && newVal !== '' && newVal !== oldVal && !this.isMobile) {
          this.getBoardUpdateTime()
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    boardSlectLang() {
      return this.commonData.isPreview ? this.commonData.boardSlectLang() : ''
    },
    activeTab() {
      return this.paramsPanelList.find(panel => panel.active)
    },
    isDarkMode() {
      return this.themeData.enableTheme && this.commonData.isPreview
    },
    paramsPanelCont() {
      const activePanel = this.paramsPanelList.find(
        panel => panel.active
      )
      if (activePanel) {
        return activePanel.content.filter(
          paramEl =>
            this.getLabels(paramEl) !== undefined &&
            this.getLabels(paramEl) !== null
        )
      } else {
        return []
      }
    },
    activeTabIds() {
      const activePanel = this.paramsPanelList.find(
        panel => panel.active
      )
      return activePanel?.id || ''
    },
    currencyAlias() {
      // 如果配置了location参数组件
      let locationItem = this.paramsPanelCont.find(k => k.type === TYPE_PARAM_ELEMENT.LOCATION_NEW)
      if (locationItem) {
        return locationItem.content.currency || 'Currency'
      }
      // 如果没配置location参数组件
      // let noLocationItem = this.boardInfo.moreLanguageList.find(item => item.id.includes(this.activeTabIds))
      // if (noLocationItem) {
      //   let currencyItem = noLocationItem.children.find(item => item.value === 'Currency')
      //   if (currencyItem) {
      //     return currencyItem.res.find(item => item.languageCode === this.boardSlectLang)?.value || 'Currency'
      //   }
      // }
      try {
        const { boardInfo, activeTabIds, boardSlectLang } = this
        if (boardInfo && boardInfo.moreLanguageList) {
          const noLocationItem = boardInfo.moreLanguageList.find((item) => item.id.includes(activeTabIds))
          if (noLocationItem) {
            const currencyItem = noLocationItem.children.find((item) => item.value === 'Currency')
            if (currencyItem) {
              return currencyItem.res.find((item) => item.languageCode === boardSlectLang)?.value || 'Currency'
          }
        }
         }
      } catch (e) {
        console.log('%c [ e ]-244', 'font-size:13px; background:#4b5ddd; color:#8fa1ff;', e)
      }
      return 'Currency'
    },
    isLazyLoading() {
      return this.levelData.isLazyLoading
    },
    api() {
      return this.utils.api || function() {}
    },
    isMobile() {
      return this.utils.isMobile
    },
  },

  data() {
    return {
      languageChangeWithAll: languageChangeWithAll,
      runtype: '',
      objectData: null,
      isTabChange: false,
      isFirstChange: true,
      languaeAndCurrency: '',
      agent: '',
      orderParamsArr: [],
      isShowAll: false,
      showAll0: false,
      showAll1: false,
      boardUpdateTime: {
        show: false
      }
    }
  },

  methods: {
    checkAll(i) {
      if (i === 0) {
        this.showAll0 = !this.showAll0
      } else {
        this.showAll1 = !this.showAll1
      }
    },
    isCheck(i) {
      if (i === 0) {
        return this.showAll0
      } else {
        return this.showAll1
      }
    },
    orderParamsList(list = []) {
      let res = []
      // list = this.paramsPanelCont
      const activePanel = this.paramsPanelList.find(
        panel => panel.active
      )
      let newLayout = sortLayout(JSON.parse(JSON.stringify(activePanel.layout)))
      newLayout.forEach(v => {
        list.forEach(k => {
          if (k.id === v.i) {
            res.push(k)
          }
        })
      })
      let quickDate = list.find(v => [TYPE_PARAM_ELEMENT.CALENDAR_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION, TYPE_PARAM_ELEMENT.LOCATION_QUICK].includes(v.type))
      let dataIndex = res.findIndex(v => v.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR || v.type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR)
      if (quickDate) {
        if (dataIndex >= 0) {
          res.splice(dataIndex, 0, quickDate)
        } else {
          res.push(quickDate)
        }
      }
      this.$emit('setOrderParamsList', res)
      this.orderParamsArr = res
      this.$nextTick(() => {
        this.isTabChange = false
      })
    },
    calender(paramEl) {
      switch (paramEl.type) {
        case TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR:
        case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR: {
          // 如果有location快选需要显示日历
          return this.runtype !== RUN_TYPE.paramsRun || this.paramsPanelCont.some(el => el.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK)
        }
        case TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION:
        case TYPE_PARAM_ELEMENT.CALENDAR_QUICK:
        case TYPE_PARAM_ELEMENT.LOCATION_QUICK: {
          return this.runtype === RUN_TYPE.paramsRun
        }
        case TYPE_PARAM_ELEMENT.LOCATION_NEW: {
          return this.runtype !== RUN_TYPE.paramsRun || !this.paramsPanelCont.some(el => el.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK)
        }
      }
      return true
    },
    bool(paramEl) {
      var activeparams = this.paramsPanelList.find(a => a.active)
      const data = getParamElementLabels.call(this, paramEl, activeparams.content, this.boardSlectLang)
      if (paramEl.type === TYPE_PARAM_ELEMENT.SEARCH) {
        let searchV = paramEl.content.options.dataSets[0]
        return !paramEl.isHideElement && searchV && (searchV.chkField.values.length || searchV.amtField.values.length)
      }
      return !paramEl.isHideElement && Array.isArray(data) && (data.length > 0)
    },
    decideFun(data, title) {
      // const data = getParamElementLabels(paramEl)
      if (title) {
        if (data !== null && data !== undefined) {
          return data.join(';')
        }
      }
      if (data !== null && data !== undefined) {
        if (data.includes('ALL')) {
          const arr = data.filter(e => e !== 'ALL')
          arr[0] = `${this.$t('sdp.select.allCapital')}: ${arr[0]}`
          return arr
        }
        return data
      }
    },
    paramEls(val) {
      const arr = [...new Set(val)]
      return arr.join(',')
    },
    nameFun(names, paramEl, el) {
      if (names === 'Location') {
        return paramEl.elName
      }
      // 物业比较处理
      if (names === 'Compare' && paramEl.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
        if (paramEl.content.options.isCompare) {
          return paramEl.content.LocationCompareName
        }
      }
      // 日历处理
      if (names === 'Prior Period') {
        return paramEl.content.campareAlise
      }
      // 日历快选名称处理
      // if (paramEl.type === TYPE_PARAM_ELEMENT.CALENDAR_QUICK) {
      //   let dateEl = this.orderParamsList.filter((item) => { return item.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR })
      //   if (dateEl) {
      //     return dateEl.elName
      //   } else {
      //     return 'Dates'
      //   }
      // }
      // if (paramEl.type === TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION) {
      //   let dateEl = this.orderParamsList.filter((item) => { return item.type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR })
      //   if (dateEl) {
      //     return dateEl.elName
      //   } else {
      //     return 'Dates'
      //   }
      // }
      return names
    },
    showParamsValue(paramEl) {
      if (paramEl.type === TYPE_PARAM_ELEMENT.SEARCH) {
        let searchV = paramEl.content.options.dataSets[0]
        let res = ''
        let billText = ''
        let moneyTest = ''
        if (searchV) {
          if (searchV.chkField.values.length) {
            // billText = `${this.$t('sdp.views.Check')} ${searchV.chkField.values[0]}`
            billText = `${paramEl.content.language.check.name} ${searchV.chkField.values[0]}`
          }
          if (searchV.amtField.values.length) {
            // moneyTest = `${this.$t('sdp.views.Amount')} ${searchV.amtField.filterType} ${searchV.amtField.values[0]}`
            moneyTest = `${paramEl.content.language.amount.name} ${searchV.amtField.filterType} ${searchV.amtField.values[0]}`
          }
        }
        res = billText || moneyTest
        if (billText && moneyTest) {
          res = `${billText}，${moneyTest}`
        }
        return res
      } else {
        if (paramEl.type === TYPE_PARAM_ELEMENT.LOCATION_NEW && (
          paramEl.content.options.paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT ||
          (paramEl.content.labelSelectType === LabelSelectType.Multi && paramEl.content.saveActiveTabSelectType === COMMON_VALUE.DIMENSIONS_VALUE)
        )) {
          return this.getLabels(paramEl).join(this.$t('sdp.punctuation.semicolonSpaceForEn'))
        } else if ([TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS, TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS].includes(paramEl.type)) {
          const labels = this.getLabels(paramEl)
          if (labels.length && labels.includes('ALL')) {
            const res = labels.filter(e => e !== 'ALL').join(', ')
            return `${this.$t('sdp.select.allCapital')}: ${res}`
          } else {
            return labels.join(', ')
          }
        } else if (paramEl.type !== TYPE_PARAM_ELEMENT.TAGNEW) {
          return this.getLabels(paramEl).join(', ')
        } else if (paramEl.type === TYPE_PARAM_ELEMENT.TEXTBOX) {
          return 'dhfdjgh'
          // if (paramEl.content.radioVal === TEXT_RADIO_TYPE.TIME) {
          //   return this.getLabels(paramEl).join(' ~ ')
          // }
          // return this.getLabels(paramEl).join(', ')
        } else {
          return this.getLabels(paramEl).join(' ')
        }
      }
    },
    getLabels(el, title) {
      let activeparams = this.paramsPanelList.find(a => a.active)
      const data = getParamElementLabels.call(this, el, activeparams.content, this.boardSlectLang)
      if (title) {
        if (data !== null && data !== undefined) {
          return data.join(';')
        }
      } else {
        if (data !== null && data !== undefined) {
          // 解决bug
          if ([TYPE_PARAM_ELEMENT.LOCATION_NEW].includes(el.type) && !Array.isArray(data)) {
            const arr = Object.keys(data)
            if (arr.findIndex(key => key === el.elName)) {
              return arr.reverse().reduce((a, b) => (a[b] = data[b]) ? a : {}, {})
            }
          }
          return data
        }
      }
    },
    setCurrency() {
      this.languaeAndCurrency = getCurrency(this.languageAndCurrency, this.tenantData, this.isEnterPriseCurrency)
    },
    getBrowserInfo() {
      let agent = navigator.userAgent.toLowerCase()

      let regStr_ie = /msie;/gi
      let regStr_ff = /firefox/gi
      let regStr_chrome = /chrome/gi
      let regStr_saf = /safari/gi
      // IE
      if (agent.includes('msie')) {
        return agent.match(regStr_ie)
      }

      // firefox
      if (agent.includes('firefox')) {
        return agent.match(regStr_ff)
      }

      // Chrome
      if (agent.includes('chrome')) {
        return agent.match(regStr_chrome)
      }

      // Safari
      if (agent.includes('safari') && agent.includes('chrome')) {
        return agent.match(regStr_saf)
      }
      return ['']
    },
    isAll(paramEl) {
      return paramEl.type === TYPE_PARAM_ELEMENT.LOCATION_NEW && this.showParamsValue(paramEl) === 'ALL' && !this.isLazyLoading
    },
    isAll2(paramEl, el) {
      let s = this.decideFun(el, 'title')
      return paramEl.type === TYPE_PARAM_ELEMENT.LOCATION_NEW && s === 'ALL' && !this.isLazyLoading
    },
    showParamsAllValue(paramEl) {
      let value = ''
      paramEl.content.pullDownData.forEach((item, i) => {
        if (i === 0) {
          value = item.name
        } else {
          value = value + ',  ' + item.name
        }
        })
      return value
    },
    getSelectedParamsInfo(data) {
      const info = this.orderParamsArr.map(paramEl => {
        if (this.bool(paramEl) && this.calender(paramEl)) {
          return {
            name: paramEl.elName,
            value: this.languageChangeWithAll(this.showParamsValue(paramEl)),
          }
        } else if (this.calender(paramEl)) {
          const obj = this.getLabels(paramEl)
          const arr = []
          for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
              const el = obj[key]
              arr.push({
                name: this.nameFun(key, paramEl),
                value: this.languageChangeWithAll(this.decideFun(el).join(', ')),
              })
            }
          }

          return arr
        }
      })

      this.languaeAndCurrency && this.boardInfo?.currencyInfo && info.push({
        name: this.currencyAlias,
        value: this.languaeAndCurrency
      })

      data.componentParams = JSON.stringify(info)
    },
    //获取数据更新时间
    getBoardUpdateTime() {
      let param ={
        tenantId: this.utils.tenantId || '',
        boardId: this.boardInfo.id || '',
        languageCode:  this.boardSlectLang || this.sdpLangcode || 'en'
      }
      boardUpdateTime(this.api, param).then(res => {
        if(res?.titleName ) this.$set(this, 'boardUpdateTime', {...res, show: true})
        if(res?.status === '1') {
          this.$set(this.boardUpdateTime, 'updateTime', this.$t('sdp.views.quotedRuleExpired'))
          this.$message({
            message: `${res?.titleName}: ${this.$t('sdp.views.quotedRuleExpired')}`,
            type: 'warning',
          })
        }
      }).catch(() => {})
    }
  },
  mounted() {
    this.setCurrency()
    this.agent = this.getBrowserInfo()[0]
    this.orderParamsList = this.$_debounce(this.orderParamsList, 800)

    this.sdpBus.$on(EVENT_BUS.GET_SELECTED_PARAMS_INFO, this.getSelectedParamsInfo)
  },
  destroyed() {
    this.sdpBus.$off(EVENT_BUS.GET_SELECTED_PARAMS_INFO, this.getSelectedParamsInfo)
  }
}
</script>

<style lang="scss" scoped>
.theme-background {
  // @include sdp-background_0();
  background: transparent;
}
// .icon-sdp-beizhu{
//   font-size: 12px;
//   margin-right: 2px;
//   color:#666666;
//   @include operation_icon-0()
// }
.info-mask{
  opacity: 0.1;
}
.selected-params-info {
  // padding: 34px 48px 19px 48px;
  color: var(--sdp-zjbxx-wzs) !important;
  padding: 16px;
  // h3 {
  //   font-size: 16px;
  //   color: #455964;
  //   i {
  //     color: #455964;
  //   }
  // }
  .param-list {
    //  padding: 6px 10px;
    display: table;
    .param-item {
      display: table-row;
      .name {
        display: table-cell;
        font-family: NotoSansHans-Regular;
        font-size: 14px;
        white-space: nowrap;
        font-weight: 700;
        padding-right: 10px;
        color: var(--sdp-zjbxx-wzs) !important;
      }
      .deletevalue {
        word-break: break-all;
      }
      .value {
        font-size: 12px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        color: var(--sdp-zjbxx-wzs) !important;
      }
      .value-all {
        font-size: 12px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: var(--sdp-zjbxx-wzs) !important;
      }
    }
  }
}
.theme-dark-mode-name, .theme-dark-mode-detail {
  color: var(--sdp-zjbxx-wzs) !important;
}
.theme-dark-mode-icon {
  color: var(--sdp-zjbts) !important;
}
</style>
