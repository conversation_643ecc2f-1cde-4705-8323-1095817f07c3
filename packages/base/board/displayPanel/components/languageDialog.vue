<template>
  <el-dialog :custom-class="`sdp-dialog multilingual-dialog ${getCurrentThemeClass()}`"
    :title="$t('sdp.views.b_boardMoreLanguage')" :visible.sync="popperShow" :close-on-click-modal="false" append-to-body
    :modal="!isAutoAddMultiLanguage" :style="{ visibility: isAutoAddMultiLanguage ? 'hidden' : 'visible' }"
    @closed="closedHandler" @open="initData">
    <loading :isLoading="loading" :text="$t('sdp.message.Matching')" />
    <div class="left-tab-group-content" v-if="(hasTabDesc || (hasTipSet && notScreen)) && languageFrom === ''">
      <div :class="[
    'radio-item',
    'radio-item-icon',
    {
      'selected': treeTabsActive === 'lange',
    }]" @click="handelTreeTabsChange('lange')">
        <span>{{ $t('sdp.views.language') }}</span>
      </div>
      <div
        v-if="hasTabDesc"
        :class="[
    'radio-item',
    'radio-item-icon',
    {
      'selected': treeTabsActive === 'desc',
    }]" style="50%" @click="handelTreeTabsChange('desc')">
        <span>{{ $t('sdp.boardAttr.ElementDescription') }}</span>
      </div>
      <!--元素提示-->
      <div
        v-if="hasTipSet && notScreen"
        :class="['radio-item','radio-item-icon',{
          'selected': treeTabsActive === 'tip',
        }]"
        @click="handelTreeTabsChange('tip')"
      >
        <span>{{ $t('sdp.boardAttr.ElementHint') }}</span>
      </div>
    </div>
    <el-button v-if="showAutomaticMatching && treeTabsActive !== 'desc' && treeTabsActive !== 'tip'" type="primary"
      @click="handleAutomaticMatching">{{ $t('sdp.button.AutomaticMatching') }}</el-button>
    <el-button type="primary" @click="handleAutomaticMatchingDesc" v-if="treeTabsActive === 'desc'">{{
    $t('sdp.button.AutomaticMatching') }}</el-button>
    <el-table v-if="popperShow" style="margin-top: 15px;"
      :height="showAutomaticMatching && hasTabDesc && languageFrom === '' ? 'calc(100% - 32px - 34px)' : showAutomaticMatching ? 'calc(100% - 32px)' : 'calc(100%)'"
      :data="filterAfterBoardTreeData" ref="multipleTable" row-key="id" border default-expand-all
      :row-class-name="tableRowClassName" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      v-show="treeTabsActive === 'lange'">
      <el-table-column prop="value" label="" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.aliasName || scope.row.value }}</span>
        </template>
      </el-table-column>
      <el-table-column v-for="(lang, index) in langArr" prop="name" :key="lang.name">
        <template slot="header">
          <div class="title" :title="lang.name">{{ lang.name }}</div>
        </template>
        <template slot-scope="scope">
          <el-input type="textarea" v-if="getRowValues(scope, index)" resize="none" size="mini"
            :disabled="getRowDisabled(scope, index)" :maxlength="isBoardTree ? undefined : 1000"
            v-model="scope.row.res[index].value" />
        </template>
      </el-table-column>
      <el-table-column class-name="delete" fixed="right" width="42">
        <template slot-scope="{row}">
          <i v-if="!row.isTop" @click="deleteRow(row)" class="icon icon-sdp-rongqishanchu"></i>
        </template>
      </el-table-column>
    </el-table>

    <el-table v-if="popperShow" style="margin-top: 15px;"
      :height="showAutomaticMatching ? 'calc(100% - 32px - 34px)' : 'calc(100% - 34px)'" :data="descTreeData"
      row-key="id" border :row-class-name="tableRowClassName"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" v-show="treeTabsActive === 'desc'">
      <el-table-column prop="value" label="" width="240">
        <template slot-scope="scope">
          <span>{{ scope.row.aliasName || scope.row.value }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="desc" :label="$t('sdp.boardAttr.ElementDescription')">
        <template slot-scope="scope">
          <!-- <span>{{ scope.row.chartDescription }}</span> -->
          <el-input type="textarea" resize="none" size="mini" v-if="scope.row.descInputVisible"
            v-model="scope.row.chartDescription" />
        </template>
      </el-table-column>
      <el-table-column v-for="(lang, index) in langArr" prop="name" :key="lang.name">
        <template slot="header">
          <div class="title" :title="lang.name">{{ lang.name }}</div>
        </template>
        <template slot-scope="scope">
          <el-input type="textarea" v-if="getRowValues(scope, index)" resize="none" size="mini"
            :disabled="getRowDisabled(scope, index)" :maxlength="isBoardTree ? undefined : 1000"
            v-model="scope.row.res[index].value" />
        </template>
      </el-table-column>
      <el-table-column class-name="delete" fixed="right" width="42">
        <template slot-scope="{row}">
          <i v-if="row.descInputVisible" @click="deleteRow(row)" class="icon icon-sdp-rongqishanchu"></i>
        </template>
      </el-table-column>
    </el-table>

    <div class="tip-set-content" v-show="treeTabsActive === 'tip'">
      <div class="tip-set-left">
        <!--<div-->
        <!--  v-for="item in elementTipList"-->
        <!--  class="el-item"-->
        <!--  :class="{ 'active': elementTipActive === item.id }"-->
        <!--  :key="`tip${item.id}`"-->
        <!--  :title="item.label"-->
        <!--  @click="handleTipItemClick(item)"-->
        <!--&gt;-->
        <!--  {{ item.label }}-->
        <!--</div>-->
        <el-tree
          v-if="elementTipList.length"
          ref="tipTree"
          :data="elementTipList"
          :node-key="'id'"
          highlight-current
          @node-click="handleTipItemClick"
        >
          <div class="el-tree-node__label custom-tree-node" slot-scope="{ node, data }" :title="node.label">
            {{ node.label }}
          </div>
        </el-tree>
      </div>
      <div class="tip-set-right">
        <tip-set
          v-if="elementTipActive && popperShow"
          ref="tipSet"
          :key="elementTipActive"
          autoInit
          :datasetList="datasetList"
          :element="elementStore"
          :isTipList="isTipList"
          :tipSetIndex="tipSetIndex"
        />
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">{{ $t('sdp.button.ensure') }}</el-button>
      <el-button @click="handleCancel">{{ $t('sdp.button.cancel') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { cookie } from 'packages/assets/utils/store'
// import { TYPE_PARAM_ELEMENT } from 'packages/base/board/displayPanel/params/utils/constants'
import autoAddMultiLanguageMixin from './languageDialogMixin_autoAddMultiLanguage'
import {generateLanguageItem, getElementTitle} from './utils'
import { EVENT_BUS, TYPE_ELEMENT } from '../constants'
import { TAGNEWCARD } from 'packages/assets/constant'
import {
  getLanguageDataList,
} from '../api'
import loading from '../../../common/loading/loading.vue'
import { ALL_PROJECT_NAME } from 'packages/components/mixins/commonMixin'
import TipSet from "packages/base/board/displayPanel/supernatant/chartSet/functions/modules/tipSetting/tipSet.vue";

export default {
  name: 'languageDialog',

  mixins: [autoAddMultiLanguageMixin],

  components: {
    TipSet,
    loading
  },

  inject: ['utils', 'sdpBus', 'getCurrentThemeClass'],
  props: {
    popoutVisible: {
      type: Boolean,
      default: false,
    },
    // 区分表格设计和数据看板的左侧列表
    isBoardTree: {
      type: Boolean,
      default: true,
    },
    // 是否有元素描述的tab页
    hasTabDesc: {
      type: Boolean,
      default: false,
    },
    hasTipSet: {
      type: Boolean,
      default: false,
    },
    datasetList: {
      type: Array,
      default: () => [],
    },
    hasTabDescLangTableElementList: { //表格的多语言数据
      type: Array,
      default: () => [],
    },
    hasTabDescLangCustomerElementList: { //自定义元素的多语言数据
      type: Array,
      default: () => [],
    },
    // 杜邦分析图先注释，后续需求会需要
    // hasTabDescLangDupontElementList:{ //杜邦分析的多语言数据
    //   type: Array,
    //   default: () => [],
    // },
    // 数据看板的左侧列表数据
    boardTreeData: {
      type: Array,
      default: () => [],
    },
    // 表格设计的左侧列表数据
    formDataList: {
      type: Array,
      default: () => [],
    },
    boardStatus: {
      type: String,
      default: '0',
    },
    // 表格缓存的多语言内容的对象
    newTableContent: {
      type: Object,
    },
    // 看板缓存的多语言内容的对象
    newBoardContent: {
      type: Object,
    },
    // 看板的勾选状态
    checkedTreeList: {
      type: Array,
      default: () => [],
    },
    // 参数组件tab
    paramsPanelList: {
      type: Array,
      default: () => [],
    },
    elList: {
      type: Array,
      default: () => [],
    },
    // 图表、卡片设计中编辑多语言
    languageFrom: {
      type: String,
      default: () => '',
    }
  },
  data() {
    return {
      languageInputList: [],
      langTitleList: [],
      langElementList: [],
      hasTabDescLangElementList: [],
      boardFlag: false,
      loading: false,
      treeTabsActive: 'lange',
      descTreeData: [],
      hasOldData: false,
      filterAfterBoardTreeMap: [], // 将编辑场景下配置的存在这个数组，再次编辑通过'自动匹配'在插入多语言信息
      combineCardLangDataCache: {}, // 组合卡片编辑场景多语言数据缓存
      elementTipList: [],
      elementTipActive: '',
      elementStore: {},
      isTipList: false,
      tipSetIndex: '',
      TIP_ELEMENT: [
        TYPE_ELEMENT.CHART,
        TYPE_ELEMENT.TABLE,
        TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
        TYPE_ELEMENT.FOUR_QUADRANT,
        TYPE_ELEMENT.DUPONT_ANALYSIS,
        TYPE_ELEMENT.COMBINE_CARD,
      ],
    }
  },
  computed: {
    filterAfterBoardTreeData() { // 对boardTreeData重构，去除空值数据
      const filterChildrenByIsHideDesc = (node) => {
        if (node.children && node.children.length > 0) {
          // false
          const filteredChildren = node.children.filter((child) => (!child.isHideDesc || (!this.hasTabDesc && child.value !== ''))); //如果是有元素描述tab的，只要isHideDesc是true，就隐藏。没有元素描述tab的，在多语言tab页，只要value是空的就过滤掉。
          const filteredNode = { ...node, children: filteredChildren.map(filterChildrenByIsHideDesc) };
          return filteredNode;
        }
        return node;
      };

      const data = this.boardTreeData.map(filterChildrenByIsHideDesc);
      return data
    },
    showAutomaticMatching() {
      return [ALL_PROJECT_NAME.OMS, ALL_PROJECT_NAME.EMS, ALL_PROJECT_NAME.SBI, ALL_PROJECT_NAME.INFRASYS_ER].includes(this.utils.env?.projectName)
    },
    langArr() {
      return this.utils.languageList
    },
    popperShow: {
      get() {
        return this.popoutVisible
      },
      set(val) {
        this.$emit('update:languageFrom', '')
        this.$emit('update:popoutVisible', val)
      },
    },
    disabledParamIdList() {
      const list = []
      // 4601 模板
      // this.paramsPanelList.forEach(panel => {
      //   panel.content.forEach(param => {
      //     if (param.isModel && param.modelId && param.content.syncFlag) {
      //     // if (param.isModel && param.modelId) {
      //       list.push(param.id)
      //     }
      //   })
      // })
      return list
    },
    isoCode() {
      return cookie.get('language') ? cookie.get('language') : 'en'
    },
    tableId() {
      let tableId = this.isBoardTree ? '' : (this.formDataList[0].children[0].tableId || '')
      return tableId
    },
    notScreen() {
      return !this.utils.isScreen || this.utils.isDataReport
    },
    getRowValues(scope, index) {
      return function (scope, index) {
        return scope.row && !scope.row.isTop && scope.row?.res?.[index]
      }
    }
  },
  watch: {
    popperShow(val) {
      if (val) {
        this.initTableData()
        this.$nextTick(() => {
          this.boardTreeData.forEach(item => {
            if (item.children.length === 0) return

            const multipleTableRef = this.$refs['multipleTable']

            if (!multipleTableRef || !multipleTableRef['toggleRowExpansion']) return

            multipleTableRef['toggleRowExpansion'](item, true)
          })
          if (this.hasOldData) {
            if (this.languageFrom === 'combineCard') {
              let key = this.boardTreeData[0].children[0].id
              if(this.combineCardLangDataCache[key]){
                this.$emit('update:boardTreeData', this.$_deepClone(this.combineCardLangDataCache[key]))
                // this.boardTreeData =  this.$_deepClone(this.combineCardLangDataCache[key])
              }
            }else{
              this.languageMatching()
            }
          }
        })
      }
    },

  },

  methods: {
    handleAutomaticMatching() {
      if (!this.hasLanguageDataBoardTreeData()) {
        this.automaticMatching()
        return
      }
      this.$sdp_eng_confirm(`${this.$t('sdp.confirm.OverwriteDataTips')}`, `${this.$t('sdp.confirm.Tips')}`, {
        confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
        cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
        closeOnHashChange: false,
        type: 'warning',
        closeOnClickModal: false,
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog',
      }).then(() => {
        this.automaticMatching()
      }).catch(() => { })
    },
    hasLanguageDataBoardTreeData() {
      let has = false
      function recursion(data) {
        for (let i = 0, len = data.length; i < len; i++) {
          // 需要填写并且获取到了名称相同的数据
          if (data[i].res) {
            // 为不同语言输入项赋值
            data[i].res.forEach(item => {
              if (item.value !== '') {
                has = true
              }
            })
          }

          if (has) {
            break
          }

          if (data[i].children) {
            recursion(data[i].children)
          }
        }
      }
      recursion(this.boardTreeData)
      return has
    },
    automaticMatching() {
      this.loading = true
      // 获取配置好的语言数据
      getLanguageDataList(this.utils.api, { 'page': 1, 'limit': 999, 'name': '', tenantId: this.utils.tenantId || '' }).then(res => res.rows).then(res => {
        // 递归寻找名称相同项
        function recursion(data) {
          for (let i = 0, len = data.length; i < len; i++) {
            if (data[i].children) {
              recursion(data[i].children)
            }
            // 获取相应名称数据，无项目名称数据将为 undefined
            const languageData = res.find((item) => {
              return item.name === data[i].value
            })
            // 需要填写并且获取到了名称相同的数据
            if (data[i].res && languageData) {
              // 为不同语言输入项赋值
              data[i].res.forEach(item => {
                item.value = languageData.languageMap[item.languageCode] || ''
              })
            }
          }
        }
        recursion(this.boardTreeData)
        this.loading = false
        this.$message({
          message: this.$t('sdp.message.SuccessfulMatch'),
          type: 'success'
        })
      }).catch(() => {
        this.loading = false
      })
    },
    handleAutomaticMatchingDesc() {
      if (!this.hasLanguageDataDescTreeData()) {
        this.automaticMatchingDesc()
        return
      }
      this.$sdp_eng_confirm(`${this.$t('sdp.confirm.OverwriteDataTips')}`, `${this.$t('sdp.confirm.Tips')}`, {
        confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
        cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
        closeOnHashChange: false,
        type: 'warning',
        closeOnClickModal: false,
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog',
      }).then(() => {
        this.automaticMatchingDesc()
      }).catch(() => { })
    },
    hasLanguageDataDescTreeData() {
      let has = false
      function recursion(data) {
        for (let i = 0, len = data.length; i < len; i++) {
          // 需要填写并且获取到了名称相同的数据
          if (data[i].res) {
            // 为不同语言输入项赋值
            data[i].res.forEach(item => {
              if (item.value !== '') {
                has = true
              }
            })
          }

          if (has) {
            break
          }

          if (data[i].children) {
            recursion(data[i].children)
          }
        }
      }
      recursion(this.descTreeData)
      return has
    },
    automaticMatchingDesc() {
      this.loading = true
      // 获取配置好的语言数据
      getLanguageDataList(this.utils.api, { 'page': 1, 'limit': 999, 'name': '', tenantId: this.utils.tenantId || '' }).then(res => res.rows).then(res => {
        console.log('%c [ res ]-440', 'font-size:13px; background:#546be2; color:#98afff;', res)
        // 递归寻找名称相同项
        function recursion(data) {
          console.log('%c [ data ]-443', 'font-size:13px; background:#5272bb; color:#96b6ff;', data)
          for (let i = 0, len = data.length; i < len; i++) {
            if (data[i].children) {
              recursion(data[i].children)
            }
            // 获取相应名称数据，无项目名称数据将为 undefined
            const languageData = res.find((item) => {
              console.log('%c [ item ]-448', 'font-size:13px; background:#c8d5da; color:#ffffff;', item)
              return item.name === data[i].chartDescription
            })
            // 需要填写并且获取到了名称相同的数据
            if (data[i].res && languageData) {
              // 为不同语言输入项赋值
              data[i].res.forEach(item => {
                item.value = languageData.languageMap[item.languageCode] || ''
              })
            }
          }
        }
        recursion(this.descTreeData)
        this.loading = false
        this.$message({
          message: this.$t('sdp.message.SuccessfulMatch'),
          type: 'success'
        })
      }).catch(() => {
        this.loading = false
      })
    },
    deleteRow(row) {
      row.res.map(v => {
        v.value = ''
      })
    },
    getRowDisabled(scope, index) {
      return scope.row && scope.row.id && this.disabledParamIdList.includes(scope.row.id)
    },
    saveInitData() {
      let newBoardContentCopy = this.$_deepClone(this.newBoardContent)
      let newTableContentCopy = this.$_deepClone(this.newTableContent)
      this.langTitleList = this.isBoardTree ? newBoardContentCopy.metaDashboardLanguageList : newTableContentCopy.tableControlsLanguageList
      this.langElementList = this.isBoardTree ? newBoardContentCopy.metaDashboardElementLanList : newTableContentCopy.tableControlsElementLanList
      // if(this.hasTabDesc){ // 处理表格的多语言
      //   // 如果是有元素描述的 表格的情况
      //   this.hasTabDescLangElementList = newTableContentCopy.tableControlsElementLanList
      // }
    },
    getAllLangs(list) {
      list.forEach((item) => {
        this.setLanguageInputLis(item)
        if (item.children) {
          this.getAllLangs(item.children)
        }
      })
    },
    initTableData() {
      this.saveInitData()
      this.getAllLangs(this.boardTreeData)
      if (this.hasTabDesc) {
        this.hasTabDescLangTableElementList.forEach(i => { // 如果表格数据中多语言未生成，在此处重新生成
          if (!i.res.length) {
            let inputListArr = []
            this.langArr.forEach(item => {
              inputListArr.push({
                element: i.id,
                key: 'remark',
                languageCode: item.isoCode,
                name: undefined,
                type: 'remark',
                value: '',
              })
            })
            this.$set(i, 'res', inputListArr)
          }
        })
        // 自定义元素的多语言
        this.hasTabDescLangCustomerElementList.forEach(i => { // 如果自定义数据中多语言未生成，在此处重新生成
          if (!i.res.length) {
            let inputListArr = []
            this.langArr.forEach(item => {
              inputListArr.push({
                element: i.id,
                key: 'remark',
                languageCode: item.isoCode,
                name: undefined,
                type: 'remark',
                value: '',
              })
            })
            this.$set(i, 'res', inputListArr)
          }
        })

        // 杜邦分析图先注释，后续需求会需要
        // this.hasTabDescLangDupontElementList.forEach(i=>{ // 如果杜邦数据中多语言未生成，在此处重新生成
        //   // 结构如下：
        //   // let obj = {
        //   //   children: [], // 这里面放的是每个卡片的数据
        //   //   value:item.elName,
        //   //   descInputVisible:false,
        //   //   id:item.id,
        //   //   type:item.type,
        //   //   content:item.content,
        //   // }
        //   i.children.forEach(ii=>{
        //     if(!ii.res.length){
        //       let inputListArr = []
        //       this.langArr.forEach(item => {
        //         inputListArr.push({
        //           // element:i.id,
        //           // key:'remark',
        //           // languageCode:item.isoCode,
        //           // name:undefined,
        //           // type:'remark',
        //           // value:'',


        //           key:`${ii.langId}_remark`,
        //           languageCode:item.isoCode,
        //           value:'',
        //           name:`${ii.chartDescription}|remark`
        //         })
        //         // key:`${i.id}._remark`,
        //         //   languageCode:item.isoCode,
        //         //   name:undefined,
        //         //   value:'',
        //       })
        //       this.$set(ii, 'res', inputListArr)
        //     }
        //   })
        // })
        this.getDescAllLangs(this.boardTreeData) //根据多语言tab中的数据构建出一个数组（元素描述tab）
      }
      if (this.hasTipSet && this.notScreen) {
        this.initElementTipSet()
        const firstNode = this.getFirstChildNode(this.elementTipList)
        this.elementTipActive = firstNode.id

        this.tipSetIndex = firstNode.tipIndex
        this.isTipList = firstNode.isTipList
        this.$set(this, 'elementStore', firstNode.element)

        this.$nextTick(() => {
          this.setTreeCurrentKey()
          this.initTipSet()
        })
      }
    },
    getDescAllLangs(data) {
      let arr = []
      let elementList = []
      if (data.find(i => i.id === 'element')) {
        elementList = [data.find(i => i.id === 'element')]
      }
      let elList = this.elList.filter(_ => _.type !== TYPE_ELEMENT.CONTAINER && _.type !== TYPE_ELEMENT.TABLE && _.type !== TYPE_ELEMENT.DUPONT_ANALYSIS && _.type !== TYPE_ELEMENT.TEXT && _.type !== TYPE_ELEMENT.CUSTOMER_ELEMENT).map(i => {
        return {
          id: i.id,
          value: i.elName,
          type: i.type,
          chioceTab: i.content?.chioceTab ? i.content.chioceTab : null,
          children: [],
          descInputVisible: true, //元素描述是否可以编辑，指标名称不能编辑
          content: i.content
        }
      })
      // 扁平化出来的数组 arr
      this.treeToArr(elementList, arr) // 将boardTreeData 中元素部分扁平化
      elList.forEach(item => {
        arr.forEach(item2 => {
          if (item.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) { // 卡片的情况
            if (item.content.tagNewCardContent === TAGNEWCARD.TWOINDICES) { // 双指标
              item.descInputVisible = false
              let rules1 = (item2.id + '').includes('_remark')
              let rules2 = (item2.id + '').includes('_remarkTwo')
              let isSameEl = (item2.id + '').includes(item.id)
              if (isSameEl && rules2) {
                item.children.push({
                  res: item2.res,
                  chartDescription: item2.value,
                  _descId: item2.id,
                  id: item2.id,
                  value: item.content.optionArray[1].cardName,
                  descInputVisible: true
                })
              } else if (isSameEl && rules1) {
                item.children.push({
                  res: item2.res,
                  chartDescription: item2.value,
                  _descId: item2.id,
                  id: item2.id,
                  value: item.content.optionArray[0].cardName,
                  descInputVisible: true
                })
              }
            } else {
              let rules = item2.id.includes('_remark')
              let isSameEl = item2.id.includes(item.id)
              if (isSameEl && rules) {
                item.res = item2.res
                item.chartDescription = item2.value
                item._descId = item2.id
              }
            }
          }
          else if (item.type === 'chart' && item.chioceTab !== null && !!item.chioceTab?.length && item.id == item2.parentId) { // 图形存在指标的情况
            let chioceTabList = item.chioceTab
            item.descInputVisible = false
            // let rules = item2.id.includes('_chartDescription')
            // let isSameEl = item2.id.includes(item.id)
            if (item2.id.includes('_chartDescription')) {
              // let index = item2.id.split('_')[item2.id.split('_').length-2]
              let len = item2.id.split('_').length - 2
              let index = +item2.id.split('_')[len]  //  "Yg5h3PVs-sCpTReXx1DKm_1_chartDescription"  取出当前指标的索引,取倒数第二个g5h3PVs-sCpT_ReXx1DKm_1_chartDescription 可能存在这种
              item.children.push({
                res: item2.res,
                chartDescription: chioceTabList[index].saveObj.chartUserConfig.chartDescription,
                _descId: item2.id,
                id: chioceTabList[index].id + '|||' + item.id, //老看板指标图像的id是1，2，3 这种。多个指标图形下会导致ID重复，所以加上自己的图形ID区分。
                // value:item2.value,
                value: chioceTabList[index].name,
                descInputVisible: true
              })
            }
          } else if (item.type === TYPE_ELEMENT.FOUR_QUADRANT) { // 多合一表格的情况
            let tabList = item.content.fourQuadrantOptions.tables
            item.descInputVisible = false
            let rules = item2.id.includes('_description')
            let isSameEl = item2.id.includes(item.id)
            if (isSameEl && rules) {
              let len = item2.id.split('_').length - 2
              let index = +item2.id.split('_')[len]
              item.children.push({
                res: item2.res,
                chartDescription: tabList[index].description,
                _descId: item2.id,
                id: tabList[index].id,
                // value:item2.value,
                value: tabList[index].name,
                descInputVisible: true
              })
            }

          } else {
            // item2._isDesc
            let rules = item2.id.includes('_chartDescription') || item2.id.includes('_remark')
            if (item.id == item2.parentId && rules) { // _isDesc是元素描述
              item.res = item2.res
              item.chartDescription = item2.value
              item._descId = item2.id
            }
          }
        })
      })
      // 杜邦分析图先注释，后续需求会需要
      // this.descTreeData = [...elList,...this.hasTabDescLangTableElementList,...this.hasTabDescLangDupontElementList]
      // this.descTreeData = [...elList,...this.hasTabDescLangTableElementList]
      this.descTreeData = [...elList, ...this.hasTabDescLangTableElementList, ...this.hasTabDescLangCustomerElementList]
    },
    initData() {
      // this.boardFlag = false
      this.treeTabsActive = 'lange'
      this.languageInputList = []
    },
    confirm() {
      this.saveTipSet()
      const validForm = this.validataData()
      if (!validForm) return
      if (this.languageFrom) {
        if (this.languageFrom !== 'combineCard') {
          let result = this.getEditLanguage()
          if (result) {
            this.popperShow = false
            this.hasOldData = true
          }
        } else {
          this.combineCardLangDataCache[this.boardTreeData[0].children[0].id] = this.boardTreeData
          this.popperShow = false
          this.hasOldData = true
        }
        return
      } else {
        if(this.hasOldData) {
          this.verifyBoardTreeData()
          this.languageInputList = []
          this.treeToArr(this.boardTreeData, this.languageInputList)
          this.languageInputList = this.languageInputList.filter((item) => !item.isTop)
          let arr = []
          this.languageInputList.forEach(v => {
            this.langElementList = this.langElementList.filter(k=> k.key !== v.id)
            v.res.forEach(item => {
              arr.push({
                languageCode: item.languageCode,
                key:item.key,
                name: item.name,
                value: item.value
              })
            })
          })
          this.langElementList = [...this.langElementList, ...arr]
          if (this.isBoardTree) {
            this.newBoardContent.metaDashboardLanguageList = this.langTitleList
            this.newBoardContent.metaDashboardElementLanList = this.langElementList
          } else {
            this.newTableContent.tableControlsLanguageList = this.langTitleList
            this.newTableContent.tableControlsElementLanList = this.langElementList
          }
          this.updateWarningData()
          this.popperShow = false
          return
        }
      }

      // 在这里将元素描述融入到boardTreeData中
      this.hasTabDesc && this.cellDescTreeData()
      this.hasTabDesc && !!this.hasTabDescLangTableElementList.length && this.updateTableElListDesc()
      this.hasTabDesc && !!this.hasTabDescLangCustomerElementList.length && this.updateCustomerElListDesc()
      // 杜邦分析图先注释，后续需求会需要
      // this.hasTabDesc && !!this.hasTabDescLangDupontElementList.length && this.updateDupontElListDesc()
      this.verifyBoardTreeData()
      this.saveData()
      this.saveTipSetToElement()
      this.updateWarningData()
      this.popperShow = false
      this.$emit('updateMultiLan', this.boardTreeData)
    },
    cellDescTreeData() { // 在这里将元素描述融入到boardTreeData中
      // this.descTreeData  this.boardTreeData
      let arr = []
      this.treeToArr(this.descTreeData, arr)
      arr = arr.filter(i => i._descId)
      arr.forEach((item) => {
        this.assignResToBoardTreeData(this.boardTreeData, item);
      });

      this.$emit('updateElListDesc', arr)
    },
    updateTableElListDesc() {
      console.log(this.hasTabDescLangTableElementList);
      this.hasTabDescLangTableElementList.forEach(item => {
        this.$emit('updateTableElListDesc', item)
      })
    },
    updateCustomerElListDesc() {
      console.log(this.hasTabDescLangCustomerElementList);
      this.hasTabDescLangCustomerElementList.forEach(item => {
        this.$emit('updateCustomerElListDesc', item)
      })
    },
    // 杜邦分析图先注释，后续需求会需要
    // updateDupontElListDesc(){
    //   this.hasTabDescLangDupontElementList.forEach(item=>{
    //     this.$emit('updateDupontElListDesc',item)
    //   })
    // },
    assignResToBoardTreeData(boardTreeData, arr) {
      for (const node of boardTreeData) {
        if (node.id === arr._descId) {
          node.res = arr.res;
          return;
        }
        if (node.children) {
          this.assignResToBoardTreeData(node.children, arr);
        }
      }
    },
    verifyBoardTreeData() {
      this.boardTreeData.map(list => {
        list.children && list.children.map(row => {
          row.res && row.res.map(item => {
            item.value && (item.value = item.value.trim())
          })
        })
      })
    },
    validataData() {
      // 校验表头分组数据重复
      const tableHeaderData = this.boardTreeData.find(e => e.id === 'tableHeader')
      if (tableHeaderData) {
        const groupList = tableHeaderData.children[0].children
        for (let i in this.langArr) {
          const labelList = groupList.map(e => e.res[i].value).filter(e => e)
          const resNameList = Array.from(new Set(labelList))
          if (resNameList.length < labelList.length) {
            this.$message.warning(this.$t('sdp.views.duplicateGroupName'))
            return false
          }
        }
      }

      return true
    },
    // 更新预警数据多语言
    updateWarningData() {
      this.elList?.length && this.elList.filter(el => el.type !== TYPE_ELEMENT.DUPONT_ANALYSIS).forEach(el => {
        this.sdpBus.$emit(EVENT_BUS.CHANGE_ELEMENT_WARNING_DATA, el.id, { moreLanguageList: this.boardTreeData, type: 'updateLanguageConfig', elType: el.type, })
      })
    },
    handleCancel() {
      this.$sdp_eng_confirm(`${this.$t('sdp.views.closeNotSave')}`, this.$t('sdp.dialog.hint'), {
        confirmButtonText: this.$t('sdp.button.ensure'),
        cancelButtonText: this.$t('sdp.button.cancel'),
        type: 'warning',
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'theme-messagebox sdp-dialog',
        callback: (action) => {
          if (action === 'confirm') {
            this.popperShow = false
          }
          // if (action === 'cancel') {
          // }
        }
      })
    },
    tableRowClassName({ row, rowIndex }) {
      // if(row.isHideDesc){
      //   return 'hide-row'
      // }
      if (row.isTop) {
        return 'header-row'
      }
      return 'input-row'
    },
    // 将多语言翻译数组排序
    orderLang(list) {
      let res = []
      this.langArr.forEach(v => {
        list.forEach(k => {
          if (k.languageCode === v.isoCode) {
            res.push(k)
          }
        })
      })
      return res
    },
    // 获取对应多语言数据
    setLanguageInputLis(data) {
      if (!data.isTop) {
        let res
        if (data.isToplanguage) {
          res = this.langTitleList.filter(item => item.type === data.isToplanguage || item.key === data.isToplanguage)
          res = this.orderLang(res)
        } else {
          let indexs = []
          res = this.langElementList.filter((item, index) => {
            if (data.id.indexOf('_metricsAlias') > 0) {
              let keyName = item.name && item.name.split('|')[2]
              // 兼容日历上期的特殊处理
              if (data.realValue === 'Prior Period') {
                if (keyName && (item.key === data.id || item.key === data.oldId) && keyName !== data.value) {
                  indexs.push(index)
                  return false
                }
              } else {
                if (keyName && item.key === data.id && keyName !== data.value) {
                  indexs.push(index)
                  return false
                }
              }
            }
            // 需要清空维度和度量的多语言值
            if (data.id.includes('_dimensionAlias')) {
              let keyName = item.name && item.name.split('|')[2]
              if (keyName && item.key === data.id && keyName !== data.value) {
                indexs.push(index)
                return false
              }
            }
            if (data.id.includes('_columnsAlias')) {
              let keyName = item.name && item.name.split('|')[2]
              if (keyName && item.key === data.id && keyName !== data.value) {
                indexs.push(index)
                return false
              }
            }
            if ((data.name && data.name.includes('rankValue')) || (data.name && data.name.includes('ratioValue')) || (data.name && data.name.includes('dimension')) || (data.name && data.name.includes('compareValue'))) {
              if (data.id === item.key && (item.name && item.name !== data.name)) {
                item.value = ''
                item.name = data.name
              }
            }
            // 兼容日历上期的特殊处理
            if (data.oldId && data.realValue === 'Prior Period') {
              if (item.key === data.id || item.key === data.oldId) {
                this.$delete(item)
                return true
              } else {
                return false
              }
            } else {
              return item.key === data.id
            }
          })
          if (indexs.length) this.langElementList = this.langElementList.filter((v, i) => !indexs.includes(i))
          res = this.orderLang(res)
        }

        if (!res.length) {
          // 该条内容没有多语言信息
          const inputListArr = []
          this.langArr.forEach(item => {
            const obj = generateLanguageItem(item.isoCode, { ...data, i18nValue: '', })
            inputListArr.push(obj)
          })
          res = inputListArr
        } else if (res.length < this.langArr.length) {
          // 该条内容只有部分多语言信息
          const inputListArr = []
          this.langArr.forEach(item => {
            if (!res.map(eve => eve.languageCode).includes(item.isoCode)) {
              const obj = generateLanguageItem(item.isoCode, { ...data, i18nValue: '', })
              inputListArr.push(obj)
            }
          })
          res.push(...inputListArr)
          res = this.orderLang(res)
        }
        res.forEach((item) => {
          // 兼容日历上期的特殊处理
          if ((item.key === data.id || item.key === data.oldId) && data.realValue === 'Prior Period') {
            item.name = data.name
          } else if (item.key === data.id) {
            item.name = data.name
          }
        })
        // let title = this.getPreTitle(this.boardTreeData, data.id)
        this.$set(data, 'res', res)
      }
    },
    getPreTitle(list, id, arr = []) {
      let temp = arr
      for (const obj of list) {
        temp.push(obj.value)
        if (obj.id === id) {
          return temp.join('-')
        }
        if (obj.children && obj.children.length) {
          let result = this.getPreTitle(obj.children, id, temp)
          if (result) return result
        }
        temp.pop()
      }
      return false
    },
    // 多语言直接做缓存保存，然后保存看板或者表格时统一发给后台保存
    saveData() {
      // const tree = this.isBoardTree ? this.$refs.boardTree : this.$refs.tableTree
      // const checkedTreeList = tree.getCheckedNodes()
      // this.checkedTreeList.splice(0, this.checkedTreeList.length, ...checkedTreeList)
      // let langTitleList = this.newTableContent ? this.newTableContent.tableControlsLanguageList : this.newBoardContent.metaDashboardLanguageList
      // let langElementList = this.newTableContent ? this.newTableContent.tableControlsElementLanList : this.newBoardContent.metaDashboardElementLanList
      if (this.isBoardTree) {
        this.newBoardContent.metaDashboardLanguageList = this.langTitleList
        this.newBoardContent.metaDashboardElementLanList = this.langElementList
      } else {
        this.newTableContent.tableControlsLanguageList = this.langTitleList
        this.newTableContent.tableControlsElementLanList = this.langElementList
      }
      // if(this.hasTabDesc){ // 处理表格的多语言
      //   // 如果是有元素描述的
      //   this.newTableContent.tableControlsElementLanList = this.hasTabDescLangElementList
      // }
      this.langTitleList.length = 0
      this.langElementList.length = 0
      this.languageInputList = []
      this.treeToArr(this.boardTreeData, this.languageInputList)
      this.languageInputList = this.languageInputList.filter((item) => !item.isTop)
      this.languageInputList.forEach(item => {
        const tempArr = item.res[0].type ? this.langTitleList : this.langElementList
        const key = item.res[0].type ? 'type' : 'key'
        item.res[0].type ? this.saveTempTableNew(tempArr, item.res, key, item.isToplanguage) : this.saveTempTable(tempArr, item.res, key)
      })
      // this.$message.success(this.$t('sdp.message.saveSuccessMsg'))
    },
    // 将树状结构转换成扁平数组
    treeToArr(list, arr) {
      list.forEach((item) => {
        arr.push(item)
        if (item && item.children) {
          this.treeToArr(item.children, arr)
        }
      })
    },
    saveTempTable(arr, tableRow, key) {
      // 新看板保存的多语言内容等到看板保存时，一起保存
      tableRow.forEach(item => {
        arr.push({
          languageCode: item.languageCode,
          [key]: item[key],
          name: item.name,
          value: item.value
        })
      })
    },
    saveTempTableNew(arr, tableRow, key, value) {
      // 新看板保存的多语言内容等到看板保存时，一起保存
      tableRow.forEach(item => {
        arr.push({
          languageCode: item.languageCode,
          [key]: value,
          name: item.name,
          value: item.value
        })
      })
    },
    renderTree(h, { node, data, store }) {
      return (
        <span title={data.value}>{data.value}</span>
      )
    },
    handelTreeTabsChange(tab) {
      this.treeTabsActive = tab

      if (tab === 'tip') {
        this.setTreeCurrentKey()
      }
      // if(tab==='indicator'){
      //   this.getIndicatorTreeData()
      // }else{
      //   this.getTreeData()
      // }
    },
    // 模板设计时退出编辑
    cleanEditLanguage() {
      this.hasOldData = false
      this.$emit('update:languageFrom', '')
      this.filterAfterBoardTreeMap = []
    },
    // 模板设计时 保存
    saveEditLanguage() {
      if (this.hasOldData) {
        this.confirm()
      }
    },
    // 组合卡片弹框保存
    saveCombineCard(){
      this.languageInputList = []
      let combineCardLangData = []
      for(let key in this.combineCardLangDataCache) {
        combineCardLangData.push(this.combineCardLangDataCache[key][0])
      }
      if(combineCardLangData.length > 0){
        this.treeToArr(combineCardLangData, this.languageInputList)
        this.languageInputList = this.languageInputList.filter((item) => !item.isTop)
        let arr = []
        this.languageInputList.forEach(v => {
          this.langElementList = this.langElementList.filter(k=> k.key !== v.id)
          v.res.forEach(item => {
            arr.push({
              languageCode: item.languageCode,
              key:item.key,
              name: item.name,
              value: item.value
            })
          })
        })
        this.langElementList = [...this.langElementList, ...arr]
        if (this.isBoardTree) {
          this.newBoardContent.metaDashboardLanguageList = this.langTitleList
          this.newBoardContent.metaDashboardElementLanList = this.langElementList
        } else {
          this.newTableContent.tableControlsLanguageList = this.langTitleList
          this.newTableContent.tableControlsElementLanList = this.langElementList
        }
        this.popperShow = false
      }
    },
    // 组合卡片弹框关闭
    closeCombineCard(){
      this.hasOldData = false
      this.$emit('update:languageFrom', '')
      this.combineCardLangDataCache = {}
    },
    // 校验多语言重复
    checkLanguage(list = []) {
      let languageList = list.filter(v => !v.isTop)
      let languageListRepeat = []
      let langMap = {}
      this.langArr.forEach(k=>{
        langMap[k.isoCode] = []
      })
      languageList.forEach(v => {
        if (v.res && v.res.length > 0) {
          v.res.forEach(item => {
            if (item.value) {
              if (langMap[item.languageCode].includes(item.value)) {
                languageListRepeat.push(item.value)
              } else {
                langMap[item.languageCode].push(item.value)
              }
            }
          })
        }
      })
      return [...new Set(languageListRepeat)]
    },
    // fix 77047 根据元素进行多语言重复的校验
    checkAllLanguage(){
      let elementLanguageList = this.filterAfterBoardTreeData.find(v=>v.id === 'element')
      if(elementLanguageList && elementLanguageList.children && elementLanguageList.children.length > 0){
      let result = elementLanguageList.children.some(v=>{
        let name = v.value
        let arr = []
        this.treeToArr([v], arr)
        let languageListRepeat = []
        let langMap = {}
        this.langArr.forEach(k=>{
          langMap[k.isoCode] = []
        })
        arr.forEach(e=>{
          if (e.res && e.res.length > 0) {
            e.res.forEach(item => {
              if (item.value) {
                if (langMap[item.languageCode].includes(item.value)) {
                  languageListRepeat.push(item.value)
                } else {
                  langMap[item.languageCode].push(item.value)
                }
              }
            })
          }
        })
        if(languageListRepeat.length > 0){
          this.$message.warning(name + ' ' + this.$t('sdp.message.DuplicateLanguage', {
            prop: languageListRepeat.toString()
          }))
        }
        return languageListRepeat.length > 0
      })
      return result
     }
    },
    // 将模板设计编辑场景下编辑的多语言 组装成可以'自动匹配'的数据格式
    getEditLanguage() {
      let arr = []
      this.treeToArr(this.boardTreeData, arr)
      // let languageListRepeat = this.checkLanguage(arr)
      // if (languageListRepeat.length > 0) {
      //   this.$message.warning(this.$t('sdp.message.DuplicateLanguage', {
      //     prop: languageListRepeat.toString()
      //   }))
      //   return false
      // }
      this.filterAfterBoardTreeMap = arr.filter(v => v.res && v.res.length > 0).map(v => {
        let languageMap = {}
        v.res.forEach(item => {
          languageMap[item.languageCode] = item.value
        })
        return {
          name: v.value,
          languageMap: languageMap
        }
      })
      return true
    },
    languageMatching() {
      this.loading = true
      let _this = this
      // 递归寻找名称相同项
      function recursion(data) {
        let res = _this.filterAfterBoardTreeMap
        for (let i = 0, len = data.length; i < len; i++) {
          if (data[i].children) {
            recursion(data[i].children)
          }
          // 获取相应名称数据，无项目名称数据将为 undefined
          const languageData = res.find((item) => {
            return item.name === data[i].value
          })
          // 需要填写并且获取到了名称相同的数据
          if (data[i].res && languageData) {
            // 为不同语言输入项赋值
            data[i].res.forEach(item => {
              item.value = languageData.languageMap[item.languageCode] || ''
            })
          }
        }
      }
      recursion(this.boardTreeData)
      this.loading = false
    },
    /** *********元素提示相关*********** **/
    elTipSetDataSet(el, list, filterList) {
      if (el.type === TYPE_ELEMENT.TABLE) {
        list.push({
          id: el.id,
          label: el.elName,
          isTipList: false,
          tipIndex: '',
          element: this.$_deepClone(el)
        })
      }
      if (el.type === TYPE_ELEMENT.CHART) {
        const elClone = this.$_deepClone(el)
        const { chioceTab, saveIndex } = el.content
        if (!chioceTab || chioceTab.length <= 1) {
          list.push({
            id: el.id,
            label: el.elName,
            isTipList: true,
            tipIndex: el.id,
            element: elClone
          })
        } else {
          const children = chioceTab.map(tab => {
            return {
              id: `${el.id}_${tab.id}`,
              label: tab.name,
              isTipList: true,
              tipIndex: tab.id,
              element: elClone
            }
          })
          list.push({
            id: el.id,
            label: el.elName,
            isTipList: true,
            tipIndex: el.id,
            element: elClone,
            children
          })
        }
      }
      if (el.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
        const elClone = this.$_deepClone(el)
        const { optionArray } = el.content
        if (optionArray.length <= 1) {
          list.push({
            id: el.id,
            label: el.elName,
            isTipList: true,
            tipIndex: 0,
            element: elClone
          })
        } else {
          const children = optionArray.map((item, index) => {
            return {
              id: `${el.id}_${index}`,
              label: index === 0 ? this.$t('sdp.views.cardOne') : this.$t('sdp.views.cardTwo'),
              isTipList: true,
              tipIndex: index,
              element: elClone
            }
          })
          list.push({
            id: el.id,
            label: el.elName,
            isTipList: true,
            tipIndex: el.id,
            element: elClone,
            children
          })
        }
      }
      if (el.type === TYPE_ELEMENT.FOUR_QUADRANT) {
        const elClone = this.$_deepClone(el)
        const { fourQuadrantOptions = {} } = el.content
        const { tables = [] } = fourQuadrantOptions
        if (tables.length <= 1) {
          list.push({
            id: el.id,
            label: el.elName,
            isTipList: true,
            tipIndex: 0,
            element: elClone
          })
        } else {
          const children = tables.map((item, index) => {
            return {
              id: `${el.id}_${index}`,
              label: item.name,
              isTipList: true,
              tipIndex: index,
              element: elClone
            }
          })
          list.push({
            id: el.id,
            label: el.elName,
            isTipList: true,
            tipIndex: el.id,
            element: elClone,
            children
          })
        }
      }
      if (el.type === TYPE_ELEMENT.DUPONT_ANALYSIS) {
        const elClone = this.$_deepClone(el)
        const { cardData = [] } = elClone.content
        const children = cardData.map((item, index) => {
          return {
            id: `${el.id}_${item.id}`,
            label: item.content.optionArray[0].cardName,
            isTipList: true,
            tipIndex: index,
            element: item
          }
        })
        list.push({
          id: el.id,
          label: el.elName,
          isTipList: true,
          tipIndex: el.id,
          element: elClone,
          children
        })
      }
      if (el.type === TYPE_ELEMENT.COMBINE_CARD) {
        const elClone = this.$_deepClone(el)
        const { choiceTab = [] } = elClone.content
        const tabChildren = choiceTab.map((tab, index) => {
          const { saveObj } = tab
          const { cardList } = saveObj
          const children = cardList.map((card, cardIndex) => {
            const { optionArray } = card.content

            if (optionArray.length <= 1) {
              return {
                id: `${tab.id}_${card.id}`,
                label: card.elName,
                isTipList: true,
                tipIndex: 0,
                element: card
              }
            } else {
              const children = optionArray.map((item, index) => {
                return {
                  id: `${tab.id}_${card.id}_${index}`,
                  label: index === 0 ? this.$t('sdp.views.cardOne') : this.$t('sdp.views.cardTwo'),
                  isTipList: true,
                  tipIndex: index,
                  element: card
                }
              })
              return ({
                id: card.id,
                label: card.elName,
                isTipList: true,
                tipIndex: card.id,
                element: card,
                children
              })
            }
          })

          return {
            id: `${el.id}_${tab.id}`,
            label: tab.name,
            isTipList: false,
            tipIndex: '',
            element: tab,
            children,
          }
        })

        list.push({
          id: el.id,
          label: el.elName,
          isTipList: false,
          tipIndex: '',
          element: elClone,
          children: tabChildren,
        })
      }
      if (el.type === TYPE_ELEMENT.CONTAINER) {
        // 高级容器
        if (el.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
          const { tabList = [] } = el.content
          const tabChildren = tabList.map(tab => {
            const children = []
            const { includedElsIdList = [] } = tab.content
            includedElsIdList.forEach(id => {
              const targetEl = filterList.find(e => e.id === id)
              if (targetEl) {
                this.elTipSetDataSet(targetEl, children, filterList)
              }
            })
            if (children.length) {
              return {
                label: tab.title,
                id: `${el.id}_${tab.name}`,
                isTipList: false,
                tipIndex: '',
                element: '',
                children
              }
            }
            return false
          }).filter(e => e)

          if (tabChildren.length) {
            list.push({
              label: el.elName,
              id: `${el.id}`,
              isTipList: false,
              tipIndex: '',
              element: this.$_deepClone(el),
              children: tabChildren,
            })
          }
        } else {
          const { includedElIds = [] } = el.content
          const children = []
          includedElIds.forEach(id => {
            const targetEl = filterList.find(e => e.id === id)
            if (targetEl) {
              this.elTipSetDataSet(targetEl, children, filterList)
            }
          })
          if (children.length) {
            list.push({
              id: el.id,
              label: el.elName,
              isTipList: true,
              tipIndex: el.id,
              element: this.$_deepClone(el),
              children
            })
          }
        }
      }
    },
    initElementTipSet() {
      const filterList = this.elList.filter(e => this.TIP_ELEMENT.includes(e.type) || e.type === TYPE_ELEMENT.CONTAINER)
      const notInContainerElList = filterList.filter(e => !e._containerId)

      const list = []
      notInContainerElList.forEach(el => {
        this.elTipSetDataSet(el, list, filterList)
      })

      console.log('list,', list)
      // const list = filterList.map(e => {
      //   const obj = {
      //     label: getElementTitle(e),
      //     id: e.id,
      //     element: this.$_deepClone(e),
      //   }
      //   return obj
      // })

      this.$set(this, 'elementTipList', list)
    },
    getFirstChildNode(tree) {
      if (!Array.isArray(tree) || tree.length === 0) {
        return null
      }

      // 先检查当前层级的第一个节点
      const firstNode = tree[0]

      // 如果该节点没有子节点，则返回其ID
      if (!firstNode.children || firstNode.children.length === 0) {
        return firstNode
      }

      // 否则递归检查其子节点
      return this.getFirstChildNode(firstNode.children)
    },
    findTreeNodeById(tree, id, childrenKey = 'children') {
      if (!Array.isArray(tree)) return null

      for (const node of tree) {
        if (node.id === id) return node

        if (node[childrenKey] && node[childrenKey].length > 0) {
          const found = this.findTreeNodeById(node[childrenKey], id, childrenKey)
          if (found) return found
        }
      }

      return null
    },
    setTreeCurrentKey(key = this.elementTipActive) {
      const ref = this.$refs.tipTree
      this.$nextTick(() => {
        ref && ref.setCurrentKey(key)
      })
    },
    handleTipItemClick(data) {
      if (this.elementTipActive === data.id || !data.element || data.children) return

      this.saveTipSet()

      this.elementTipActive = ''
      this.$nextTick(() => {
        this.elementTipActive = data.id
        this.tipSetIndex = data.tipIndex
        this.isTipList = data.isTipList
        this.$set(this, 'elementStore', data.element)

        this.initTipSet()
      })
    },
    initTipSet() {
      const ref = this.$refs.tipSet
      this.$nextTick(() => {
        ref && ref.initData()
      })
    },
    saveTipSet() {
      const ref = this.$refs.tipSet
      if (ref) {
        const saveData = ref.saveTipSet()
        if (!saveData.richTextHtml) return
        // console.log('saveTipSet', saveData)
        if (this.isTipList) {
          const tipSetList = this.elementStore.tipSetList || []
          const targetIndex = tipSetList.findIndex(e => e.id === this.tipSetIndex)
          if (targetIndex !== -1) {
            this.$set(this.elementStore.tipSetList, targetIndex, this.$_deepClone(saveData))
          } else {
            if (!this.elementStore.tipSetList) {
              this.$set(this.elementStore, 'tipSetList', [])
            }
            if (typeof this.tipSetIndex === 'number' && this.elementStore.type !== TYPE_ELEMENT.CHART) {
              this.$set(this.elementStore.tipSetList, this.tipSetIndex, this.$_deepClone(saveData))
            } else {
              this.elementStore.tipSetList.push(this.$_deepClone(saveData))
            }
          }
        } else {
          this.$set(this.elementStore, 'tipSet', this.$_deepClone(saveData))
        }
        // const target = this.elementTipList.find(e => e.id === this.elementTipActive)
        // this.$set(target.element, 'tipSet', this.$_deepClone(saveData))
      }
    },
    saveTipSetToElement() {
      this.elList.filter(e => this.TIP_ELEMENT.includes(e.type)).forEach(item => {
        const target = this.findTreeNodeById(this.elementTipList, item.id)
        if (target) {
          if (target.element.type === TYPE_ELEMENT.DUPONT_ANALYSIS) {
            this.$set(item.content, 'cardData', target.element.content.cardData)
          } else if (target.element.type === TYPE_ELEMENT.COMBINE_CARD) {
            this.$set(item.content, 'choiceTab', target.element.content.choiceTab)
          } else {
            if (target?.element?.tipSet) {
              this.$set(item, 'tipSet', target.element.tipSet)
            }
            if (target?.element?.tipSetList) {
              this.$set(item, 'tipSetList', target.element.tipSetList)
            }
          }
        }
      })
    },
    /** *******元素提示相关 结束********* **/
  },
}
</script>

<style lang="scss" scoped>
$widthRowItem: 170px;
$heightRowItem: 34px;

.el-dialog__wrapper /deep/ {
  .el-dialog__header {
    text-align: left;
    font-size: 20px;
    padding: 20px 24px 10px;
  }
}

.multilingual-dialog {
  background-color: var(--sdp-szk-bjs);

  /deep/ .el-icon-arrow-right:before {
    content: "\e791";
  }

  /deep/ .el-tree-node__content {
    font-size: 12px;
    line-height: 14px;
  }

  .icon {
    color: var(--sdp-qcgls);
    cursor: pointer;
    margin-left: 3px;
    font-size: 15px;
  }

  .left-tab-group-content {
    width: 300px;
    display: flex;
    margin-bottom: 10px;

    .radio-item {
      flex: 1;
      border: 1px solid var(--sdp-zs);
      color: var(--sdp-zs);
      cursor: pointer;
      text-align: center;
      font-size: 12px;
      height: 24px;
      line-height: 22px;
      box-sizing: border-box;

      &+.radio-item {
        border-left: 0;
      }

      &:first-child {
        border-radius: 3px 0 0 3px;
      }

      &:last-child {
        border-radius: 0 3px 3px 0;
      }

      &.disable {
        border-color: var(--sdp-jys);
        cursor: not-allowed;

        &.selected {
          background: var(--sdp-jys);
        }
      }

      &.selected {
        background: var(--sdp-zs);
        color: #fff !important;
        position: relative;

        [class*='icon-sdp'] {
          color: #fff !important;
        }

        &+.selected::before {
          content: "";
          position: absolute;
          border-left: 1px solid var(--sdp-srk-bxwzs);
          left: -1px;
          top: 1px;
          bottom: 1px;
          width: 1px;
        }
      }
    }

    .full-radio-item {
      width: 100%;
    }
  }
}

.el-tree {
  display: inline-block;
}

.icon-delete {
  font-size: 14px;
  position: absolute;
  top: 50%;
  margin-top: -16px;
  right: 11px;
  cursor: pointer;
  color: #D8D8D8;

  &:hover {
    color: $elementPrimary;
  }
}

.languageItem {
  position: relative;
  padding-right: 26px;

  div {
    display: inline-block;
  }
}

/deep/ .header-row {
  background-color: #F2F2F2 !important;

  td {
    padding: 2px 0;
  }
}

/deep/ .hide-row {
  display: none;
}

/deep/ .input-row {
  td {
    padding: 2px 0;
  }

  input {
    border: none;
  }
}

/deep/ .sdp-dialog {
  width: 70%;
  height: 65%;
  min-width: 980px;
  min-height: 480px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .el-dialog__body {
    overflow: hidden;
    flex: 1;
  }

  .el-table {
    tr {
      height: 1px;

      @media screen and (-webkit-min-device-pixel-ratio:0) {
        td {
          height: 1px;
        }
      }

      @media screen and (min--moz-device-pixel-ratio:0) {
        td {
          height: 100%;
        }
      }
    }

    .cell {
      height: 100%;
      line-height: 24px;
      display: flex;
      align-items: center;

      .el-textarea {
        height: 100%;

        .el-textarea__inner {
          height: 100% !important;
          padding: 0;
          background-color: transparent;
          border: 1px solid var(--sdp-srk-bks);
        }
      }
    }
  }

  .el-table__header {
    th {
      padding: 4px 0;
    }
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}

::-webkit-scrollbar-corner {
  // background: #f7f7f7;
}

::-webkit-scrollbar-thumb {
  background: #e6e6e6;
  border-radius: 5px;
}

::-webkit-scrollbar-track {
  // background: #f7f7f7;
  border-radius: 5px;
}

.title {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
}

.title {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
}

.el-table {
  background: transparent !important;

  /deep/ .el-table__fixed-right-patch {
    background: transparent !important;
    border-bottom-color: transparent !important;
  }
}

.tip-set-content {
  width: 100%;
  height: calc(100% - 34px);
  box-shadow: inset 0px 0.5px 0px var(--sdp-bg-bks);

  display: flex;

  .tip-set-left {
    width: 224px;
    height: 100%;
    overflow-y: auto;
    padding: 24px 24px 24px 0;
    box-shadow: inset -0.5px 0px 0px var(--sdp-bg-bks);

    .el-item {
      width: 100%;
      height: 32px;
      padding: 0px 8px 0px 34px;

      color: var(--sdp-cszj-tgl);

      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;

      position: relative;
      cursor: pointer;
      transition: all .3s linear;

      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      display: block;

      &::before {
        content: ' ';
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: transparent;
        opacity: 0.1;
        transition: all .3s linear;
      }

      &:hover, &.active {
        color: var(--sdp-zs);

        &::before {
          background: var(--sdp-zs);
        }
      }
    }

    /deep/ {
      .el-tree {
        width: 100%;

        .el-tree-node {
          .el-tree-node__content {
            height: 32px;
            padding: 0 4px;

            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            &:hover {
              //background-color: var(--sdp-fs2);
              background-color: var(--sdp-sjj-hs);
              color: var(--sdp-zs);
            }

            .el-tree-node__expand-icon {
              padding: 0 4px 0 0 ;
            }
          }

          &.is-current {
            &> .el-tree-node__content {
              color: var(--sdp-zs);
              background-color: var(--sdp-sjj-hs) !important;
            }
          }
          //&.is-expanded {
          //  .el-tree-node__content {
          //    background-color: transparent !important;
          //  }
          //}
          .el-tree-node__label, .custom-tree-node {
            font-size: 12px;
            display: block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  .tip-set-right {
    width: calc(100% - 224px);
    height: 100%;
    padding: 24px;
    overflow: auto;
  }
}
</style>
<style lang="scss">
.multilingual-dialog {
  background-color: var(--sdp-tb-bj);

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: var(--sdp-bg-bks) !important;
  }

  .cell {
    color: var(--sdp-xxbt2);

    .title {
      color: var(--sdp-xxbt1);
    }
  }

  .el-table__expand-icon+span {
    color: var(--sdp-xxbt1);
  }

  .el-table th,
  .el-table tr {
    background-color: var(--sdp-tb-bj);
  }

  .el-textarea__inner {
    line-height: normal !important;
  }

  .el-dialog__title {
    font-weight: 600;
  }

  .el-table--border td,
  .el-table--border th,
  .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-color: var(--sdp-bg-bks) !important;
  }

  .el-table--border,
  .el-table--group {
    border: 1px solid var(--sdp-bg-bks) !important;
  }

  .el-table__body tr.hover-row>td {
    background-color: var(--sdp-sjj-hs) !important;
  }
}
</style>
