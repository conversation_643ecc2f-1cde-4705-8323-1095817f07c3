import { TYPE_PARAM_ELEMENT } from './params/utils/constants'
import { assmbleMetric } from 'packages/base/gridPreview/common/js/utils'
import { VIRTUAL_DATASET_KEY } from '../../../assets/constant'
import { deepClone } from 'packages/assets/utils/globalTools.ts'

import { ELEMENT_TITLE_SONTYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementTitle/helpers/constant'
export const SUPERNATANT_LAYOUT = 'SupernatantLayout'

export const REFRESH_COMPONENT = 'refreshComponent'

export const LAZY_LOAD = 'lazyLoad'

export const SUPERNATANT_TRANSITION_DURATION = 1000

export const MAX_COUNT_ELEMENTS_IN_BOARD = 200
export const MAX_COUNT_MESSAGE_ELEMENTS_IN_BOARD = 50
export const MAX_COUNT_ADVANCE_CONTAINER_BOARD = 15

export const EVENT_BUS = {
  PRARM_GANGED_SAVE: 'paramGangedSave',
  PRARM_GANGED_CALLBACK: 'paramGangedCallback',
  SET_PRARM_GANGED_OPEN: 'setParamGangedOpen',
  PARAM_ELEMENT_HIDE_EDIT: 'paramElementHideEdit',
  FEATURE_HEIGHT_UPDATE: 'featureHeightUpdate',
  SET_VIEW_LEVEL_LIST: 'setViewLevelList',
  SET_APPEND_MESSAGE_EL: ' setAppendMessageEl',
  API_CHANGE_FULL_SCREEN: 'apiChangeFullScreen',
  CHANGE_PANEL_TAB: 'changePanelTab',
  CAROUSEL_CLEAR: 'carouselClear',
  CAROUSEL_CLICK: 'carouselClick',
  SET_LOCATION_DATA: 'setLocationData',
  RUN_SET_LOCATION_DATA: 'runSetLocationData',
  CHART_CHOOSE_HANDLER_CHANGE: 'chart_choose_handler_change', // 图形指标选择器变化
  GET_ACTIVE_COMPONENT_ID: 'getActiveComponentId',
  OUTER_SELECT_COMPONENT: 'outerSelectComponent',
  HIDE_ADVANCE_ITEM: 'hideAdvanceItem',
  BUSSINESS_CALENDAR_QUICK_CLOSE: 'bussinessCalendarQuickClose', // 关闭日历图表快选
  QUICK_COMPONENT_OPERATE: 'quickComponentOperate', // 快选运行
  CLOSE_POPUP: 'closePopup',
  CHANGE_HORIZONTAL: 'changeHorizontal',
  DISPLAY_PANEL_HANDLER_RUN: 'displayPanelHandlerRun', // 运行按钮点击时
  LAYOUT_CHANGE: 'layoutChange',
  CHANGE_ELEMENT_WARNING_DATA: 'changeElementWarningData', // 修改元素预警数据
  ELEMENT_MOVE_CHANGE: 'elementMoveChange', // 元素位置移动
  CUSTOMR_SAVE: 'customrSave',
  CUSTOMR_INIT: 'customrInit',
  CUSTOMR_INSERT_DATA: 'customrInsertData',
  CUSTOMR_DISPATACH_DATA: 'customrDispatachData',
  CUSTOMR_EXPORT: 'curstomrExport',
  ON_EXPORT_SUCCESS: 'onExportSuccess',
  SBI_BUTTON_COLLECT: 'sbiButtonCollect',
  SBI_BUTTON_EXPORT: 'sbiButtonExport',
  SBI_BUTTON_SUBSCRIBE: 'sbiButtonSubscribe', // 订阅
  SBI_BUTTON_PIN: 'sbiButtonPin', // 钉住
  SBI_CHANGE_EDIT: 'sbiChangeEdit', // 编辑
  DAILY_CONCERN_BTN: 'dailyConcernBtn',
  DAILY_CONCERN_RUN: 'dailyConcernRun',
  DAILY_CONCERN_SET_PREVIEW_STATE: 'dailyConcernSetPreviewState',
  SET_TABLE_HEADER: 'setTableHeader',
  RUN_SWITCH_LANG: 'runSwitchLang',
  FREEZE_TABLE_HEADER: 'freezeTableHeader',
  SWITCH_START_WEEK: 'switchStartWeek',
  HAND_CLICK_USER_SUBMIT: 'setThisPeriodKey',
  LARGESCREEN_LAYER_ACTION: 'largeScreenLayerAction', // 大屏右侧设置面板触发层级变动操作,
  LARGESCREEN_LAYER_CHANGE: 'largeScreenLayerChange', // 大屏图层变动
  LARGESCREEN_TABLE_SCROLL_END: 'largeScreenTableScrollEnd', // 大屏 表格滚动完成事件
  TOGGLE_BG: 'toggleBg',
  GET_PARAM_COLLECT_REQUEST: 'getParamCollectRequest',
  REFRESH_COMPONENT_KEY: 'refreshComponentKey',
  UPDATE_ALL_ELEMENTS_WARNING_DATA: 'updateAllElementsWarningData', // 更新所有元素的预警信息
  GET_TOP_DATA: 'getTopData',
  ELEMENT_EDITOR_SAVE: 'elementEditorSave',
  FETCH_CHART_STANDARD_SCHEME_LIST: 'fetchChartStandardSchemeList', // 拉取标准色系列表
  SET_PCMOBILE_ELEMENT_SCREENSHOT: 'setPcMobileElementScreenShoot', // PcMobile 更新元素截图
  FINISH_PARAMS_STATUS: 'finishParamsStatus',
  DBLCLICK_INDICATOR: 'dblclickIndicator',
  SET_SCALE: 'setScale',
  UPDATE_PARAMS_PANEL_BIND: 'updateParamsPanelBind',
  PULLING_DOWM_UPDATE: 'pullingDownUpdate',
  DECOMPOSITION: 'decomposition', // 分解树专用
  GET_SELECTED_PARAMS_INFO: 'getSelectedParamsInfo',
  EXPORT_TABLE_DATA: 'exportTableData',
  EXTERNAL_CALL: 'externalCall',
  OPERATE_BOX_CONFIRM: 'operateBoxConfirm',
  PARAM_CONFIRM: 'paramConfirm',
  PARAM_BACK_BEFORE: 'paramBackBefore',
}

export enum EXTERNAL_CALL_TYPE {
  INDICATOR_RREVIEW = 'indicatorPreview'
}

export const GET_PLAT_VALUE_BY_CODE_PID_TYPE = {
  DATA_WAREHOUSE_MODELING_SWITCH: 'DATA_WAREHOUSE_MODELING_SWITCH',
  LOCATION_LAZY_LOADING: 'LOCATION_LAZY_LOADING'
}

export const WINDOW_SETTINGS = {
  TYPE: {
    DEFAULT: 'system',
    SYSTEM: 'system',
    FIXED: 'fixed',
    CUSTOM: 'custom',
  },
  SYS_VALUE: {
    DEFAULT: 'large',
    LARGE: 'large',
    STANDARD: 'standard',
    VERTICAL: 'vertical',
    HORIZONTAL: 'horizontal',
  }
}

// 组件类型分组
export const TYPE_ELEMENT_GROUP = {
  CHART: 'chart',
  IMAGE: 'image',
  TEXT: 'text',
  TABLE: 'table',
  PARAM: 'param',
  SPEC: 'spec',
  CONTAINER: 'container',
  TIME: 'time',
  WEATHER: 'weather',
  GROUP: 'group',
  WEB: 'web',
  SCROLL_TEXT: 'scroll-text',
  MATERIAL_LIBRARY: 'material-library',
  CUSTOMER_ELEMENT: 'customer-element',
  DUPONT_ANALYSIS: 'dupont-analysis',
}
export const TYPE_ELEMENT = {
  CHART: 'chart',
  IMAGE: 'image',
  TABLE: 'table',
  TEXT: 'text',
  ELEMENT_TEMPLATE: 'elementTemplate',
  WEATHER: 'weather',
  TIME: 'time',
  SCROLL_TEXT: 'scroll-text',
  // 特殊控件
  WEB: 'web',
  CONTAINER: 'chartContainer',
  ADVANCE_CONTAINER: 'advance-container',
  ELEMENT_TAG_CARD: 'element-tag-card',
  ELEMENT_YEAR_CARD: 'element-year-card',
  FOUR_QUADRANT: 'element-four-quadrant',
  COMPARE_TABLE: 'element-compare-table',
  ELEMENT_TAG_NEW_CARD: 'element-tag-new-card',
  INTERACTION_OPTIONS: 'interactionOptions',
  LINKSETTING: 'linkSetting',
  UNDEFINED: 'undefined',
  COMBINE_CARD: 'combine-card',
  ELEMENT_GROUP: 'element-group',
  MATERIAL_LIBRARY: 'material-library',
  CUSTOMER_ELEMENT: 'customer-element',
  COPY_ELEMENT: 'copy-element',
  DUPONT_ANALYSIS: 'dupont-analysis',
  ELEMENT_TITLE: 'element-title',
  // 从看板引入元素
  ELEMENT_REFERENCE: 'element-reference',
} as const
export const ELEMENT_TYPE = {
  [TYPE_ELEMENT.TABLE]: '1',
  [TYPE_ELEMENT.CHART]: '2',
  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD]: '3',
  [TYPE_ELEMENT.CUSTOMER_ELEMENT]: '4',
  [TYPE_ELEMENT.DUPONT_ANALYSIS]: '5',
  [TYPE_ELEMENT.ELEMENT_TITLE]: '6',
}

// 获取图形引用的数据集
export function getChartContentDataSetId(content) {
  let result = []
  const { drillSettings, chioceTab, associatedData, dataset } = content
  drillSettings.dataSetId && result.push(drillSettings.dataSetId)
  // 关联数据集处理
  if (associatedData && Object.keys(associatedData).length) {
    result = [...associatedData.referenceDatasetId]
  }
  // 关联数据集选中但是没有设置数据
  if (dataset?.length) {
    dataset.map(item => {
      result.push(item.id)
    })
  }
  getMapSchemeDatasetId(content)

  if (chioceTab?.length && chioceTab.length > 1) {
    chioceTab.forEach(c => getMapSchemeDatasetId(c.saveObj))
  }
  result = Array.from(new Set(result))
  return result

  function getMapSchemeDatasetId(cont) {
    if (cont.mapSchemeSetting?.schemeList?.length) {
      cont.mapSchemeSetting.schemeList.forEach(s => {
        s.saveObj.drillSettings.dataSetId && result.push(s.saveObj.drillSettings.dataSetId)
      })
    }
  }
}

// 获取看板元素所有数据集
/**
 * 对看板元素数据列表的数据进行转换
 * @export
 * @param {Array,String,undefined} type 看板元素数据列表
 * @returns {Array}
 */
export function getElListDataSetIds(elList, type?): string[] {
  const idsSet = new Set()
  let includeType = [
    TYPE_ELEMENT.CHART,
    TYPE_ELEMENT.TABLE,
    TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD,
    TYPE_ELEMENT.FOUR_QUADRANT,
    TYPE_ELEMENT.COMBINE_CARD,
    TYPE_ELEMENT.TEXT,
    TYPE_ELEMENT.CUSTOMER_ELEMENT,
    TYPE_ELEMENT.DUPONT_ANALYSIS,
    TYPE_ELEMENT.ELEMENT_TITLE,
  ]
  if (Array.isArray(type)) {
    includeType = includeType.filter(item => type.includes(item))
  } else if (typeof type === 'string') {
    includeType = includeType.filter(item => type === item)
  }
  elList.forEach(e => {
    if (includeType.includes(e.type)) {
      getSingleElListDataSetIds(e, idsSet)
    }
  })
  return [...idsSet]
}

// 提取出各图形类型获取数据集id的方法作为公共方法-（需求9963）
export function getSingleElListDataSetIds(e, idsSet, isGetIdsFlag = false) {
  const {
    drillSettings,
    dataSetIds,
    optionArray,
    fourQuadrantOptions,
    // 图形的指标选择器
    choiceTab,
    cardData,
  } = e.content

  const tipSetIdsFun = (tipSet, ids) => {
    tipSet.datasetIds.forEach(item => {
      if (!ids.includes(item)) {
        ids.push(item)
      }
    })
  }

  let ids = null
  if (drillSettings && e.type === TYPE_ELEMENT.CHART) {
    ids = getChartContentDataSetId(e.content)
  } else if (dataSetIds && e.type === TYPE_ELEMENT.TABLE) {
    ids = getTableContentDataSetId(e.content) // 表格数据集
  } else if (dataSetIds && ([TYPE_ELEMENT.TEXT, TYPE_ELEMENT.ELEMENT_TITLE].includes(e.type))) {
    ids = dataSetIds
  } else if (optionArray && optionArray.length && e.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
    // 卡片数据集
    ids = optionArray.map(item => item.dataSetId)
  } else if (fourQuadrantOptions && e.type === TYPE_ELEMENT.FOUR_QUADRANT) {
    ids = fourQuadrantOptions.tables.map(item => item.dataSetId) // 多合一表格数据集
  } else if (choiceTab && e.type === TYPE_ELEMENT.COMBINE_CARD) {
    const arr = []
    if (Array.isArray(choiceTab)) {
      choiceTab.forEach(item => {
        item.saveObj.cardList.forEach(el => {
          let optionArray = el.content.optionArray ? el.content.optionArray.map(v => v.dataSetId).filter(v => v) : []
          arr.push(...optionArray)

          if (el.tipSetList) {
            el.tipSetList.forEach(tipSet => {
              tipSetIdsFun(tipSet, arr)
            })
          }
        })
      })
    }
    ids = arr
  } else if (e.type === TYPE_ELEMENT.CUSTOMER_ELEMENT) {
    ids = e.content.config.dataSetIds
  } else if (cardData?.length && e.type === TYPE_ELEMENT.DUPONT_ANALYSIS) {
    const arr = []
    cardData.forEach(card => {
      card?.content?.optionArray && arr.push(...card.content.optionArray.map(item => item.dataSetId))
      if (card.tipSetList) {
        card.tipSetList.forEach(tipSet => {
          tipSetIdsFun(tipSet, arr)
        })
      }
    })
    ids = arr
  }
  if (e.tipSet) {
    tipSetIdsFun(e.tipSet, ids)
  }
  if (e.tipSetList) {
    e.tipSetList.forEach(tipSet => {
      tipSetIdsFun(tipSet, ids)
    })
  }
  if (!ids) {
    const mdg = `元素${e.id},没有获取关联的数据集id，请检查`
    // throw new Error(mdg)
    console.log(mdg)
    ids = []
  }
  if(isGetIdsFlag) {
    return ids
  } else {
    ids.forEach(id => id && idsSet.add(id))
  }

}


// 图表容器的弹窗类型
export const TYPE_CHART_CONTAINER_DIALOG = {
  setting: 'SETTING',
  preview: 'PREVIEW',
}

// 标记字段
export const ELEMENTS_TAG = {
  NEWTABLE: 'newTable',
  REFERENCETABLE: 'referenceTable',
}

export const TOOL_TAG = {
  TOOL: 'tool',
}

export const TRENDS_DIMENSION_EXCHANGE = {
  TIME_DIMENSION: 'timeDimension',
  TRENDS_DIMENSION: 'trendsDimension',
  EVENT_DIMENSION: 'eventDimension',
}

export const MATERIAL_ELEMENT = [
  {
    NAME: '线条',
    TYPE: 'LINE',
    CHILDREN: [
      { NAME: '分割线1', TYPE: 'DIVIDING_LINE_1', IMG: 'line1.png' },
      { NAME: '虚线1', TYPE: 'DOTTED_LINE_1', IMG: 'line2.png' },
      { NAME: '虚线2', TYPE: 'DOTTED_LINE_2', IMG: 'line3.png' },
      { NAME: '箭头1', TYPE: 'ARROW_LINE_1', IMG: 'line4.png' },
      { NAME: '箭头2', TYPE: 'ARROW_LINE_2', IMG: 'line5.png' },
      { NAME: '分割线2', TYPE: 'DIVIDING_LINE_2', IMG: 'line6.png' },
      { NAME: '分割线3', TYPE: 'DIVIDING_LINE_3', IMG: 'line7.png' },
      { NAME: '分割线4', TYPE: 'DIVIDING_LINE_4', IMG: 'line8.png' },
    ]
  },
  {
    NAME: '边框',
    TYPE: 'BORDER',
    CHILDREN: [
      { NAME: '边框1', TYPE: 'BORDER_1', IMG: 'border1.png' },
      { NAME: '边框2', TYPE: 'BORDER_2', IMG: 'border2.png' },
      { NAME: '边框3', TYPE: 'BORDER_3', IMG: 'border3.png' },
      { NAME: '边框4', TYPE: 'BORDER_4', IMG: 'border4.png' },
      { NAME: '边框5', TYPE: 'BORDER_5', IMG: 'border5.png' },
    ]
  },
  {
    NAME: '标题',
    TYPE: 'TITLE',
    CHILDREN: [
      { NAME: '标题1', TYPE: 'TITLE_1', IMG: 'title1.png' },
      { NAME: '标题2', TYPE: 'TITLE_2', IMG: 'title2.png' },
      { NAME: '标题3', TYPE: 'TITLE_3', IMG: 'title3.png' },
      { NAME: '标题4', TYPE: 'TITLE_4', IMG: 'title4.png' },
      { NAME: '标题5', TYPE: 'TITLE_5', IMG: 'title5.png' },
    ]
  }
]
export const ELEMENT_TITLE_TYPES = [
  {
    TYPE: 'TITLE_COMMON',
    CHILDREN: [
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_LEVEL_1, placeholder: 'sdp.views.titleLevel1' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_LEVEL_2, placeholder: 'sdp.views.titleLevel2' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_LEVEL_3, placeholder: 'sdp.views.titleLevel3' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_LEVEL_TEXT, placeholder: 'sdp.views.titleLevelText' },
    ]
  },
  {
    TYPE: 'TITLE_ALIGN_CENTER',
    CHILDREN: [
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ALIGN_CENTER_1, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ALIGN_CENTER_2, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ALIGN_CENTER_3, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ALIGN_CENTER_4, placeholder: 'sdp.views.titleInputPlaceholder' },
    ]
  },
  {
    TYPE: 'TITLE_ORDER',
    CHILDREN: [
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ORDER_1, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ORDER_2, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ORDER_3, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ORDER_4, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ORDER_5, placeholder: 'sdp.views.titleInputPlaceholder' },
      { TYPE: ELEMENT_TITLE_SONTYPE.TITLE_ORDER_6, placeholder: 'sdp.views.titleInputPlaceholder' },
    ]
  }
]

export const API_REQUEST_DATA_TYPE = 'apiRequestDataType'

export const TYPE_INTERACTION = {
  INTERACTION_STATE: 'interactionState',
  INTERACTION_SETTING: 'interactionSetting',
  INTERACTION_SETTING_INDICATOR: 'interactionSettingIndicator', // 卡片对图形的指标选择器交互方式
}

export const WARNING_ELEMENT_TYPE = {
  CHART: '1',
  TABLE: '2',
  CARD: '3',
}

export let TYPE_SUPERSETTING: { INTELLIGENT: 'intelligent', PLAN_RASK: 'planTask'; INIT: 'init'; RETREAT: 'retreat'; GO_FORWARD: 'goForward'; SKIP: 'skip'; USER_STATE: 'userState'; CLOSE_SKIP: 'closeSkip'; SUBSCRIBE: 'subscribe' } = {
  CLOSE_SKIP: 'closeSkip', // 清空超链接
  SKIP: 'skip', // 超链接标识
  GO_FORWARD: 'goForward', // 超链接前进
  RETREAT: 'retreat', // 超链接返回
  INIT: 'init', // 初始化
  PLAN_RASK: 'planTask', // 计划任务
  SUBSCRIBE: 'subscribe', // 订阅提醒
  USER_STATE: 'userState',
  INTELLIGENT: 'intelligent' // 智能搜索
}

// 数据看板操作日志对象
export const OPERATION_LOG = {
  // 二级菜单国际码
  menuI18Key: 'BI_L01',
  // 一级菜单国际码
  modelI18Key: 'OMSCD01BBSJ',
  // tenantId: localStorage.getItem('shiji'),
  // 日志类型：1登录日志，2操作日志
  logType: '2',
  // 操作类型1新增，2修改，3删除
  operateType: '',
  // 操作页面ID
  operatePageId: '',
  // 文件夹ID
  superNodeName: '',
  // 本节点ID
  objectId: '',
  // 本节点名称
  objectName: '',
  // 操作内容（前后对比的JSON串）
  operateContent: '',
}
// 获取图形多语言（添加name属性）
export function _getMoreLangChart(each, children, index) {
  const { title, chartDescription } = each.content.chartUserConfig
  title.text && children.push({ id: `${each.id}_${index}_chartTitle`, value: title.text })
  chartDescription && children.push({ id: `${each.id}_${index}_chartDescription`, value: chartDescription })
  if (each.content.alias === 've-scatter-normal') {
    // 散点图做特殊处理
    each.content.chartData.columns.forEach((item, i) => {
      children.push({ id: `${each.id}_${i}_${index}_columnsAlias`, name: `${each.id}|${index}|${item}`, value: item })
    })
  } else {
    each.content.chartSettings.dimension.forEach((item, i) => {
      children.push({ id: `${each.id}_${i}_${index}_dimensionAlias`, name: `${each.id}|${index}|${item}`, value: item })
    })
    each.content.chartSettings.metrics.forEach((item, i) => {
      children.push({ id: `${each.id}_${i}_${index}_metricsAlias`, name: `${each.id}|${index}|${item}`, value: item })
    })
  }
  return children
}
// 获得多语言的适配器（除去表格）
export const getLangElement = {

  [TYPE_ELEMENT.CHART](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    let chioceTab = el.content.chioceTab
    if (chioceTab && chioceTab.length) {
      chioceTab.forEach((item, index) => {
        let obj = JSON.parse(JSON.stringify(el))
        obj.content = item.saveObj
        children = _getMoreLangChart(obj, children, index)
        children.push({ id: `${el.id}_${item.id}_indicatrixAlias`, value: item.name })
      })
    } else {
      children = _getMoreLangChart(el, children, 'x')
    }
    el.elName && languageList.children.push({ id: `${el.id}`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.FOUR_QUADRANT](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    const tables = el.content.fourQuadrantOptions.tables
    tables.length && tables.forEach((e, i) => {
      e.name && children.push({ id: `${el.id}_${i}_fourQuadrant`, value: e.name })
      e.metrics.length && e.metrics.forEach((eve, index) => {
        eve.viewColumnName && children.push({ id: `${el.id}_${i}_${index}_metricsAlias`, value: eve.viewColumnName })
      })
      e.dimension.length && e.dimension.forEach((eve, index) => {
        eve.viewColumnName && children.push({ id: `${el.id}_${i}_${index}_dimensionAlias`, value: eve.viewColumnName })
      })
    })
    el.elName && languageList.children.push({ id: `${el.id}`, value: el.elName, children: children })
  },

  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD](paramsObj) {
    const { el, languageList } = paramsObj
    const optionArray = el.content.optionArray
    optionArray.length && optionArray.forEach((e, i) => {
      e.cardName && languageList.children.push({ id: `${el.id}_${i}_cardTitle`, value: e.cardName })
    })
  },

  [TYPE_ELEMENT.CONTAINER](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    const containerTitle = el.content.settings.title
    const containerElAlias = el.content.settings.elAlias
    containerTitle && children.push({ id: `${el.id}_containerTitle`, value: containerTitle })
    if (Object.keys(containerElAlias).length) {
      Object.keys(containerElAlias).forEach((e, i) => {
        containerElAlias[e] && children.push({ id: `${el.id}_${i}_containerElAlias`, value: containerElAlias[e] || el.content.dragSelectsEls.filter(eve => eve.id === e)[0].elName })
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}`, value: el.elName, children: children })
  },

  [TYPE_ELEMENT.COMBINE_CARD](paramsObj) {

  },
}

export function _getchartLangObj(each, arr, titleKey, contentKey, index, chartLangObj, that) {
  // titleKey = titleKey || 'type'
  contentKey = contentKey || 'key'
  let chartUserConfig = each.content.chartUserConfig
  const labelObj1 = arr.filter(item => item[contentKey] === `${each.id}_${index}_chartTitle`)[0]
  const labelObj2 = arr.filter(item => item[contentKey] === `${each.id}_${index}_chartDescription`)[0]
  // this.$set(chartUserConfig.title, 'text', labelObj1 ? (labelObj1.value || chartUserConfig.title.text) : chartUserConfig.title.text)
  // this.$set(chartUserConfig, 'chartDescription', labelObj2 ? (labelObj2.value || chartUserConfig.chartDescription) : chartUserConfig.chartDescription)
  chartUserConfig.title.text = labelObj1 ? (labelObj1.value || chartUserConfig.title.text) : chartUserConfig.title.text
  chartUserConfig.chartDescription = labelObj2 ? (labelObj2.value || chartUserConfig.chartDescription) : chartUserConfig.chartDescription
  if (each.content.alias === 've-scatter-normal') {
    each.content.chartData.columns.forEach((v, i) => {
      const labelObj = arr.filter(item => item[contentKey] === `${each.id}_${i}_${index}_columnsAlias`)[0]
      that.$set(each.content.chartData.columns, i, labelObj ? (labelObj.value || v) : v)

      let name = labelObj && labelObj.name
      if (name) {
        let obj = {
          id: name.split('|')[0],
          index: name.split('|')[1],
          key: name.split('|')[2],
          value: labelObj.value
        }
        chartLangObj.push(obj)
      }
    })
  } else {
    each.content.chartSettings.dimension.forEach((v, i) => {
      const labelObj = arr.filter(item => item[contentKey] === `${each.id}_${i}_${index}_dimensionAlias`)[0]
      that.$set(each.content.chartSettings.dimension, i, labelObj ? (labelObj.value || v) : v)

      let name = labelObj && labelObj.name
      if (name) {
        let obj = {
          id: name.split('|')[0],
          index: name.split('|')[1],
          key: name.split('|')[2],
          value: labelObj.value
        }
        chartLangObj.push(obj)
      }
    })
    each.content.chartSettings.metrics.forEach((v, i) => {
      const labelObj = arr.filter(item => item[contentKey] === `${each.id}_${i}_${index}_metricsAlias`)[0]
      that.$set(each.content.chartSettings.metrics, i, labelObj ? (labelObj.value || v) : v)

      let name = labelObj && labelObj.name
      if (name) {
        let obj = {
          id: name.split('|')[0],
          index: name.split('|')[1],
          key: name.split('|')[2],
          value: labelObj.value
        }
        chartLangObj.push(obj)
      }
    })
  }
}

export function _checkPreviewChart(each, arr, titleKey, contentKey, that) {
  // 只替换获取多语言的几个配置，剩余通过chartDataReplaceLang替换
  let chartLangObj = []
  let chioceTab = each.content.chioceTab
  if (chioceTab && chioceTab.length) {
    _getchartLangObj(each, arr, titleKey, contentKey, each.content.saveIndex, chartLangObj, that)
    chioceTab.forEach((v, i) => {
      let obj = JSON.parse(JSON.stringify(each))
      obj.content = v.saveObj
      _getchartLangObj(obj, arr, titleKey, contentKey, i, chartLangObj, that)
      const labelObj = arr.filter(item => item[contentKey] === `${each.id}_${v.id}_indicatrixAlias`)[0]
      v.name = labelObj ? (labelObj.value || v.name) : v.name
    })
  } else {
    _getchartLangObj(each, arr, titleKey, contentKey, 'x', chartLangObj, that)
  }
  that.chartLangObj = chartLangObj
}
// 根据绑定的看板元素获取使用到的所有数据集
export function getAllDataSetIds(list = [], elList = []) {
  // list 看板元素id数组
  // elList 所有看板元素
  let arr: string[] = []
  // list.filter(x => {
  //   elList.forEach(item => {
  //     if (item.id === x) {
  //       const datasetIds = actionGetDataSetIds(item.type, item.content)
  //       datasetIds && datasetIds.forEach(id => {
  //         if (arr.indexOf(id) === -1) {
  //           arr.push(id)
  //         }
  //       })
  //     }
  //   })
  // })

  const els = elList.filter((el) => list.includes(el.id))

  arr = getElListDataSetIds(els)

  return arr
}
// 参数组件根据布局取第一个
export function parmasLayout(list) {
  let paramsObj
      list.forEach(item => {
        // this.$message({ type: 'error', message: this.$t('sdp.views.financialCalendar') })
        if (paramsObj) {
          if (paramsObj.content.layout) {
            if (paramsObj.content.layout.y > item.content.layout.y) {
              paramsObj = item
            } else if (paramsObj.content.layout.y === item.content.layout.y) {
              if (paramsObj.content.layout.x > item.content.layout.x) {
                paramsObj = item
              }
            }
          }
          if (paramsObj.type === 'CalendarQuick' || paramsObj.type === 'DateQuickOperation') {
            paramsObj = item
          }
        } else {
          paramsObj = item
        }
      })
      return paramsObj
}
export function getTableContentDataSetId(content) {
  const {
    dataSetIds = [],
    tableDefaultConfig
  } = content
  const virtualIds = tableDefaultConfig?.dataSetJoinsConfig?.referenceDatasetId || []
  const ids = dataSetIds.filter(id => id !== VIRTUAL_DATASET_KEY)
  return [...virtualIds, ...ids]
}
export function actionGetDataSetIds(type, content) {
  const types = {
    [TYPE_ELEMENT.TABLE]: () => {
      console.log('table', content)
      // return content.dataSetIds ? content.dataSetIds.filter((item) => {
      //   return item
      // }) : []
      return getTableContentDataSetId(content)
    },
    [TYPE_ELEMENT.CHART]: () => {
      console.log('chart', content)
      return content.drillSettings.dataSetId ? [content.drillSettings.dataSetId] : []
    },
    [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD]: () => {
      console.log('tag_new_card', content)
      return content.optionArray ? content.optionArray.map(item => item.dataSetId).filter(item => {
        return item
      }) : []
    },
    [TYPE_ELEMENT.ELEMENT_YEAR_CARD]: () => {
      console.log('year_card', content)
      return content.options.dataSetId ? content.options.dataSetId.filter((item) => {
        return item
      }) : []
    },
    [TYPE_ELEMENT.COMPARE_TABLE]: () => {
      console.log('comparm_table', content)
      return content.options.dataSetId ? content.options.dataSetId.filter((item) => {
        return item
      }) : []
    },
    [TYPE_ELEMENT.FOUR_QUADRANT]: () => {
      console.log('four_quadrant', content.fourQuadrantOptions)
      return content.fourQuadrantOptions.tables.length ? content.fourQuadrantOptions.tables.map(item => item.dataSetId) : []
    },
    [TYPE_ELEMENT.COMBINE_CARD]: () => {
      const arr = []
      content.choiceTab.forEach(item => {
        item.saveObj.cardList.forEach(el => {
          let optionArray = el.content.optionArray ? el.content.optionArray.map(v => v.dataSetId).filter(v => v) : []
          arr.push(...optionArray)
        })
      })
      return arr
    },
    [TYPE_ELEMENT.DUPONT_ANALYSIS]: () => {
      const arr = []
      content.cardData && content.cardData.forEach(el => {
        let dataSetIds = el.content.optionArray ? el.content.optionArray.map(v => v.dataSetId).filter(v => v) : []
        arr.push(...dataSetIds)
      })
      return arr
    },
    'default': () => {
      return content.options.dataSetId.filter((item) => {
        return item
      })
    },
  }
  return types[(type || 'default')] ? types[(type || 'default')]() : ''
}

export function adapter(content, type, elList, el) {
  let errList = {}
  let arr = []
  let list = []
  let status = false
  switch (type) {
    case TYPE_PARAM_ELEMENT.CALENDAR_QUICK:
      arr = getAllDataSetIds(content.bindElement, elList)
      list = content.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.DATE_TYPE:
      arr = getAllDataSetIds(content.bindTableElements, elList)
      if (arr.indexOf(content.dataSetId) === -1 || !arr.length) {
        status = true
      }
      // 存在不正常设置的情况时
      if (status) {
        el.$set(content, 'notReset', true)
      }
      break
    case TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION:
      arr = getAllDataSetIds(content.bindElement, elList)
      list = content.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR:
      arr = getAllDataSetIds(content.bindElement, elList)
      list = content.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      content.dataSets.forEach(check => {
        if (arr.indexOf(check.dataSetId) === -1 || !arr.length) {
          status = true
        }
      })
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR:
      arr = getAllDataSetIds(content.bindElement, elList)
      list = content.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.SEARCH:
      arr = getAllDataSetIds(content.options.bindTableElements, elList)
      list = content.options.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content.options, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.PEOPLE_LAYER:
      arr = getAllDataSetIds(content.options.bindElements, elList)
      list = content.options.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content.options, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.TEXTBOX:
      arr = getAllDataSetIds(content.textboxOptions.bindElements, elList)
      list = content.textboxOptions.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length && content.radioVal !== '2') {
        el.$set(content, 'notReset', true)
      }
      el.$set(content.textboxOptions, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.LOCATION_NEW:
      arr = getAllDataSetIds(content.options.bindElements, elList)
      list = content.options.dataSetFields.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不匹配的数据集时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content.options, 'dataSetFields', list)
      break
    case TYPE_PARAM_ELEMENT.GROUP_BY:
      arr = getAllDataSetIds(content.bindTableElements, elList)
      if ((arr.indexOf(content.dataSetId) === -1 && content.dataSetId) || !arr.length) {
        status = true
      }
      // 存在不正常设置的情况时
      if (status) {
        el.$set(content, 'notReset', true)
      }
      break
    case TYPE_PARAM_ELEMENT.BREAKDOWN:
      arr = getAllDataSetIds(content.bindTableElements, elList)
      if ((arr.indexOf(content.dataSetId) === -1 && content.dataSetId) || !arr.length) {
        status = true
      }
      // 存在不正常设置的情况时
      if (status) {
        el.$set(content, 'notReset', true)
      }
      break
    case TYPE_PARAM_ELEMENT.GROUP:
      arr = getAllDataSetIds(content.groupOptions.bindTableElements, elList)
      if ((arr.indexOf(content.groupOptions.dataSetId) === -1 || !arr.length) || !arr.length) {
        status = true
      }
      // 存在不正常设置的情况时
      if (status) {
        el.$set(content, 'notReset', true)
      }
      break
    case TYPE_PARAM_ELEMENT.SELECT_ORDINARY:
      arr = getAllDataSetIds(content.bindElements, elList)
      list = content.dataSets.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content, 'dataSets', list)
      break
    case TYPE_PARAM_ELEMENT.TAG:
      arr = getAllDataSetIds(content.options.bindElements, elList)
      list = content.options.tagFields.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // 存在不正常设置的情况时
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content.options, 'tagFields', list)
      // if (status) {
      //   el.$set(content, 'notReset', true)
      // } else if (arr.length > content.options.tagFields.length) {
      //   el.$set(content, 'notReset', true)
      // }
      break
    case TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS:
      arr = getAllDataSetIds(content.bindElements, elList)
      list = content.dimensionArr.filter(item => arr.indexOf(item.id) !== -1)
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content, 'dimensionArr', list)
      // console.log(list)
      break
    case TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS:
      arr = getAllDataSetIds(content.bindElements, elList)
      list = content.dimensionArr.filter(item => arr.indexOf(item.id) !== -1)
      if (!list.length) {
        el.$set(content, 'notReset', true)
      }
      el.$set(content, 'dimensionArr', list)
      // console.log(list)
      break
    case TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX:
      const {
        dataSetSelected,
      } = content

      arr = getAllDataSetIds(content.bindElements, elList)

      list = [dataSetSelected].filter(item => arr.includes(item))

      if (!list.length) {
        el.$set(content, 'notReset', true)
      }

      el.$set(content, 'dataSetSelected', list.length ? list[0] : '')
      break
    case TYPE_PARAM_ELEMENT.METRIC:
      let bindElements = content.bindElements
      let metricList = getMetric(bindElements, elList, el)
      el.$set(content, 'metricList', metricList)
      break
  }
  return errList
}
// assmbleMetric 表格metric配置格式化
// 获取metric
export function getMetric(bindElements, elList, el) {
  let metricList = []
  const langCode = el.commonData.boardSlectLang()
  elList.forEach(item => {
    if (!bindElements.includes(item.id)) return
    switch (item.type) {
      case TYPE_ELEMENT.CHART:
        let moreLanguageList = []
        if (el.boardInfo?.moreLanguageList !== undefined) {
          moreLanguageList = el.boardInfo.moreLanguageList
        } else if (el.$attr?.boardInfo?.moreLanguageList !== undefined) {
          moreLanguageList = el.$attr.boardInfo.moreLanguageList
        }
        let metaDashboardElementLanList = []
        if (el.newBoardContent?.metaDashboardElementLanList && el.newBoardContent.metaDashboardElementLanList.length) {
          metaDashboardElementLanList = el.newBoardContent.metaDashboardElementLanList
        } else if (el.boardInfo?.metaDashboardElementLanList !== undefined) {
          metaDashboardElementLanList = el.boardInfo.metaDashboardElementLanList
        } else if (el.$attr?.boardInfo?.metaDashboardElementLanList !== undefined) {
          metaDashboardElementLanList = el.$attr.boardInfo.metaDashboardElementLanList
        }
        item.content.chioceTab && item.content.chioceTab.forEach(tab => {
          let data = {
            name: tab.name,
            type: 'normal',
            value: undefined,
            isTable: false,
            res: []
          }
          let needElementLanList = true
          if (moreLanguageList && moreLanguageList.length && item.content.saveIndex !== undefined) {
            const elementItem = moreLanguageList.find(e => e.id === 'element')
            elementItem.children.forEach(el1 => {
              if (el1.id === item.id) {
                el1.children.forEach(el2 => {
                  if (el2.id === `${item.id}_${tab.id || 0}_indicatrixAlias`) {
                    data.value = el2.value
                    // data.res = el2.res
                    // if (!el2.res || !el2.res.length) {
                    //   needElementLanList = true
                    //   data.res = []
                    // }
                  }
                })
              }
            })
          }
          if (needElementLanList && langCode && metaDashboardElementLanList && metaDashboardElementLanList.length) {
            metaDashboardElementLanList.forEach(metaItem => {
              if (metaItem.key === `${item.id}_${tab.id || 0}_indicatrixAlias`) {
                data.res.push(metaItem)
              }
            })
            if (data.res && !data.res.length) data.res = undefined
          }
          !metricList.find(a => a.name === data.name || a.value === data.name || (a.value === data.value && data.value) || a.name === data.value) && metricList.push(data)
        })
        break
      case TYPE_ELEMENT.TABLE:
        // console.log(item)
        let metricInfo = item.content.tableDefaultConfig.metricInfo
        const { tableControlsElementLanList } = item.content.dataScreenRes
        // console.log(metricInfo)
        if (!metricInfo || (metricInfo.metricTable.length === 0 && metricInfo.metricGroupForm.length === 0)) break
        let newData = assmbleMetric(metricInfo.metricTable, metricInfo.metricGroupForm, tableControlsElementLanList) || {}
        // console.log(newData)
        newData.config.forEach(a => {
          // groupNameList
          let language = { value: '' }
          if (a.uukey === 'alias') {
            language = a.aliasList ? a.aliasList.find(l => l.languageCode === langCode) || {} : {}
          }
          if (a.uukey === 'group') {
            language = a.groupNameList ? a.groupNameList.find(l => l.languageCode === langCode) || {} : {}
          }
          let str = language.value && el.commonData.isPreview ? language.value : ''
          // let language = groupNameList
          let data = {
            name: str || a.value,
            oldName: a.value,
            uukey: a.uukey,
            aliasList: a.aliasList,
            groupNameList: a.groupNameList,
            isTable: true
            // type: a.groupName ? 'group' : 'normal',
            // alias: a.alias
          }
          const targetIndex = metricList.findIndex(b => (b.name === data.name || b.value === data.name))
          if (targetIndex >= 0 && !metricList[targetIndex]?.res?.length && (a.uukey === 'alias' ? data?.aliasList?.length : data?.groupNameList?.length)) {
            metricList.splice(targetIndex, 1, data)
          }
          !metricList.find(b => b.isTable && (b.name === data.name || b.value === data.name)) && metricList.push(data)
        })
        // item.content.tableDefaultConfig.metricInfo && item.content.chioceTab.forEach(tab => {
        //   let data = {
        //     name: tab.name
        //   }
        //   !metricList.find(a => a.name === tab.name) && metricList.push(data)
        // })
        break
      default:
        break
    }
  })
  let newArr = []
  metricList.forEach((metricItem) => {
    const isNoFind = newArr.findIndex((item) => { return metricItem.name === item.name }) === -1
    if (!metricItem.value && metricItem.name) {
      metricItem.value = metricItem.name
    }
    isNoFind && (newArr.push(metricItem))
  })
  return newArr
}
// 获取metric
export function getMetricWithChosed(bindElements, elList, el, moreLanguageList, chosedList, oldLanguage = '') {
  let metricList = []
  chosedList.forEach(item => {
    if (item === 'ALL') return
    metricList.push({
      name: item
    })
  })
  elList.forEach(item => {
    if (!bindElements.includes(item.id)) return
    const langCode = el.commonData.boardSlectLang()
    switch (item.type) {
      case TYPE_ELEMENT.CHART:
        item.content.chioceTab && item.content.chioceTab.forEach(tab => {
          let data = {
            name: tab.name,
            type: 'normal'
          }
          let tmp = null
          let old = null
          if (moreLanguageList && moreLanguageList.length) {
            const elementItem = moreLanguageList.find(e => e.id === 'element')
            elementItem.children.forEach(el1 => {
              if (el1.id === item.id) {
                el1.children.forEach(el2 => {
                  if ((el2.value === tab.name || (el2.res && el2.res?.findIndex(e => tab.name === e.value) !== -1))) {
                    tmp = el2
                    old = oldLanguage === '' ? el2 : el2.res ? el2.res.find(e => e.languageCode === oldLanguage) : el2
                    const target = langCode === '' ? el2 : el2.res ? el2.res.find(e => e.languageCode === langCode) : el2
                    data.name = target.value
                  } else if (el2.value === tab.name && el2.res === undefined) {
                    data.name = el2.value
                  }
                })
              }
            })
          }
          if (oldLanguage === '') {
            data.name = tmp?.value
          }
          !metricList.find(a => a.name === data.name) && (chosedList.includes(tmp?.value) || chosedList.includes(old?.value)) && metricList.push(data)
        })
        break
      case TYPE_ELEMENT.TABLE:
        let metricInfo = item.content.tableDefaultConfig.metricInfo
        const { tableControlsElementLanList } = item.content.dataScreenRes
        if (!metricInfo || (metricInfo.metricTable.length === 0 && metricInfo.metricGroupForm.length === 0)) break
        let newData = assmbleMetric(metricInfo.metricTable, metricInfo.metricGroupForm, tableControlsElementLanList) || {}
        // console.log(newData)
        newData.config.forEach(a => {
          let language = { value: '' }
          if (a.uukey === 'alias') {
            language = a.aliasList ? a.aliasList.find(l => l.languageCode === langCode) || {} : {}
          } else if (a.uukey === 'group') {
            if (chosedList.length) {
              const target = a.groupNameList.find(e => e.languageCode === oldLanguage)
              if (target && (chosedList.includes(target.value) || chosedList.includes(a.value))) {
                language = a.groupNameList ? a.groupNameList.find(l => l.languageCode === langCode) || {} : {}
              }
            }
          }
          let str = language.value && el.commonData.isPreview ? language.value : ''
          let data = {
            name: str
          }
          !metricList.find(b => b.name === data.name) && str !== '' && metricList.push(data)
        })
        /**
         * cn->en  a.value -> language.value 可以判断
         * en-cn 当前语言和之后的语言如何判断
         * 通过用content记录之前的语言代码 对数据进行取值返回
         */
        break
      default:
        break
    }
  })
  return metricList
}
// 获取参数组件绑定的看板元素id
// 解决各个参数组件bindElement存放位置不统一的问题
// 返回参数组件绑定的看板元素id的数组
export function paramsBindElements(param) {
  // param 参数组件
  let bindElements = []
  switch (param.type) {
    case TYPE_PARAM_ELEMENT.CALENDAR_QUICK:
    case TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION:
    case TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR:
    case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR:
      bindElements = param.content.bindElement
      break
    case TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS:
    case TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS:
    case TYPE_PARAM_ELEMENT.TIME:
      bindElements = param.content.bindElements
      break
    case TYPE_PARAM_ELEMENT.SEARCH:
      bindElements = param.content.options.bindTableElements
      break
    case TYPE_PARAM_ELEMENT.TEXTBOX:
      bindElements = param.content.textboxOptions.bindElements
      break
    case TYPE_PARAM_ELEMENT.LOCATION_NEW:
      bindElements = param.content.options.bindElements
      break
    case TYPE_PARAM_ELEMENT.DATE_TYPE:
    case TYPE_PARAM_ELEMENT.GROUP_BY:
    case TYPE_PARAM_ELEMENT.BREAKDOWN:
      bindElements = param.content.bindTableElements
      break
    case TYPE_PARAM_ELEMENT.GROUP:
      bindElements = param.content.groupOptions.bindTableElements
      break
    case TYPE_PARAM_ELEMENT.SELECT_ORDINARY:
    case TYPE_PARAM_ELEMENT.METRIC:
    case TYPE_PARAM_ELEMENT.TAGNEW:
    case TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX:
    case TYPE_PARAM_ELEMENT.Omit_Zero_Total:
    case TYPE_PARAM_ELEMENT.HIERARCHY_FILTER:
      bindElements = param.content.bindElements
      break
    case TYPE_PARAM_ELEMENT.PEOPLE_LAYER:
    case TYPE_PARAM_ELEMENT.TAG:
      bindElements = param.content.options.bindElements
      break
  }
  return bindElements || []
}
// 检查看板元素时候又被参数组件绑定并返回参数组件绑定的数据集id
export function paramsBindDatasets(param, id) {
  // param 参数组件
  let dataSets = []
  let content = param.content
  let datasetIdKey = 'dataSetId'
  switch (param.type) {
    case TYPE_PARAM_ELEMENT.CALENDAR_QUICK:
    case TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION:
    case TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR:
    case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR:
      if (content.bindElement.includes(id)) {
        dataSets = content.dataSets
      }
      break
    case TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS:
    case TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS:
      datasetIdKey = 'id'
      if (content.bindElements.includes(id)) {
        dataSets = content.dimensionArr
      }
      break
    case TYPE_PARAM_ELEMENT.SEARCH:
      if (content.options.bindTableElements.includes(id)) {
        dataSets = content.options.dataSets
      }
      break
    case TYPE_PARAM_ELEMENT.TEXTBOX:
      if (content.textboxOptions.bindElements.includes(id)) {
        dataSets = content.textboxOptions.dataSets
      }
      break
    case TYPE_PARAM_ELEMENT.LOCATION_NEW:
      if (content.options.bindElements.includes(id)) {
        dataSets = content.options.dataSetFields
      }
      break
    case TYPE_PARAM_ELEMENT.DATE_TYPE:
    case TYPE_PARAM_ELEMENT.GROUP_BY:
    case TYPE_PARAM_ELEMENT.BREAKDOWN:
      if (content.bindTableElements.includes(id)) {
        dataSets = [{ [datasetIdKey]: content.dataSetId }]
      }
      break
    case TYPE_PARAM_ELEMENT.GROUP:
      if (content.groupOptions.bindTableElements.includes(id)) {
        dataSets = [{ [datasetIdKey]: content.groupOptions.dataSetId }]
      }
      break
    case TYPE_PARAM_ELEMENT.SELECT_ORDINARY:
      if (content.bindElements.includes(id)) {
        dataSets = content.dataSets
      }
      break
    // case TYPE_PARAM_ELEMENT.PEOPLE_LAYER:
    case TYPE_PARAM_ELEMENT.TAG:
      if (content.options.bindElements.includes(id)) {
        dataSets = content.options.tagFields
      }
      break
    case TYPE_PARAM_ELEMENT.TIME:
      datasetIdKey = 'id'
      if (content.bindElements.includes(id)) {
        dataSets = content.configs
      }
      break
  }
  return (dataSets || []).map(d => {
    return Object.assign({}, deepClone(d), { datasetId: d[datasetIdKey], })
  })
}

// 替换看板元素多语言适配器（除去表格）
export const checkLangElement = {

  [TYPE_ELEMENT.CHART](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    _checkPreviewChart(el, arr, 'type', contentKey, this)
    // let chartUserConfig = el.content.chartUserConfig
    // const labelObj1 = arr.filter(item => item[contentKey] === `${el.id}_chartTitle`)[0]
    // const labelObj2 = arr.filter(item => item[contentKey] === `${el.id}_chartDescription`)[0]
    // this.$set(chartUserConfig.title, 'text', labelObj1 ? (labelObj1.value || chartUserConfig.title.text) : chartUserConfig.title.text)
    // this.$set(chartUserConfig, 'chartDescription', labelObj2 ? (labelObj2.value || chartUserConfig.chartDescription) : chartUserConfig.chartDescription)

    // el.content.chartSettings.dimension.forEach((v, i) => {
    //   const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_dimensionAlias`)[0]
    //   el.content.chartSettings.dimension[i] = labelObj ? (labelObj.value || v) : v
    // })
    // el.content.chartSettings.metrics.forEach((v, i) => {
    //   const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_metricsAlias`)[0]
    //   el.content.chartSettings.metrics[i] = labelObj ? (labelObj.value || v) : v
    // })
    // el.content.chioceTab && el.content.chioceTab.forEach((v, i) => {
    //   const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${v.id}_indicatrixAlias`)[0]
    //   v.name = labelObj ? (labelObj.value || v.name) : v.name
    // })
  },

  [TYPE_ELEMENT.FOUR_QUADRANT](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    const tables = el.content.response
    tables.length && tables.forEach((e, i) => {
      const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_fourQuadrant`)[0]
      e.name = labelObj ? (labelObj.value || e.name) : e.name
      let metricsIndex = 0
      e.aliasColumnName.length && e.aliasColumnName.forEach((eve, index) => {
        let labelObj = {}
        if (index < e.dimAliasColumnName.length) {
          labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_${index}_dimensionAlias`)[0]
        } else {
          labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_${metricsIndex}_metricsAlias`)[0]
          metricsIndex = metricsIndex + 1
        }
        let viewColumnName = e.viewColumnName[index]
        this.$set(e.viewColumnName, index, labelObj ? (labelObj.value || viewColumnName) : viewColumnName)
      })
    })
  },

  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    const optionArray = el.content.optionArray
    optionArray.length && optionArray.forEach((e, i) => {
      const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_cardTitle`)[0]
      e.cardName = labelObj ? (labelObj.value || e.cardName) : e.cardName
    })
  },

  [TYPE_ELEMENT.CONTAINER](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    const labelObj = arr.filter(item => item[contentKey] === `${el.id}_containerTitle`)[0]
    const containerElAlias = el.content.settings.elAlias
    el.content.settings.title = labelObj ? (labelObj.value || el.content.settings.title) : el.content.settings.title
    if (Object.keys(containerElAlias).length) {
      Object.keys(containerElAlias).forEach((e, i) => {
        const containerElAliasObj = arr.filter(item => item[contentKey] === `${el.id}_${e}_containerElAlias`)[0]
        if (containerElAlias[e]) {
          containerElAlias[e] = containerElAliasObj ? (containerElAliasObj.value || containerElAlias[e]) : containerElAlias[e]
        }
      })
    }
  },

}

export const REFRESH_EL = [TYPE_ELEMENT.CHART, TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD, TYPE_ELEMENT.FOUR_QUADRANT, TYPE_ELEMENT.TABLE]

export const DEFAULT_REFRESH_EL = () => ({ isRefreshEl: false, time: 1, unit: 60 })
export const DEFAULT_ELATTR = () => ({ titleNavigation: false, })

export const TABLE_TITLE_CLASS_NAME_BY_EXPORT = 'table-title-by-export'
export const TABLE_TITLE_TEXT_CLASS_NAME_BY_EXPORT = 'table-title-text-by-export'
export const TABLE_TITLE_LINE_HEIGHT = 1.4

// 订阅方式
export const SUBSCRIPTION_TYPE = {
  LinkExport: '0', // 链接导出
  attachments: '1', // 附件
  link: '2' // 链接预览
}

// 要显示包含今天的快选项
export const SHOW_INCLUDETO_DAY = [
  'Past 7 days', // 过去7天
  'Last 30 days', // 过去30天
  'Last 90 days', // 过去90天
  'Week to Date', // 本周
  'Month to Date', // 本月
  'Quarter to Date', // 本季度
  'Year to Date', // 本年
  'Last 6 Months', // 近6月
  'The Most Recent Year', // 最近一年
  'Last n days', // last n days
]
