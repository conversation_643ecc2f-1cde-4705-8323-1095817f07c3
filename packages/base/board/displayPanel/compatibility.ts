// 白名单
import Vue from 'vue'
import { PARAMS_TYPE_LIST } from './params/paramElement/bridge'
import {
  GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY, GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS,
  INFORMATION_KEY,
  refreshDataInit,
  TAGNEWCARD,
  THEME_TYPE
} from 'packages/assets/constant'
import { BOARD_ELEMENT_THEME, BoardElement } from './supernatant/boardElements/BoardElement'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import { styleConversion } from './utils'
import { JSONClone, getProp, deepClone } from '../../../assets/utils/globalTools'
import { isAdvanceContainer_fn, isOrdinaryContainer_fn } from '../../../assets/utils/helper'
import {
  ADVANCE_CONTAINER_PL,
  ADVANCE_CONTAINER_PR,
  PC_ADVANCE_CONTAINER_GRID_LAYOUT_MARGIN_X,
  PC_TOP_PADDING,
  PC_SHOW_TITLE_OR_SHOW_CURSOR_DEFAULT,
  PC_CONTAINER_TITLE_DEFAULT_FONT_SIZE
} from './supernatant/boardElements/elementContainer/constants'
import compatibleData from './utils/compatibility'
import { TYPE_PARAM_ELEMENT } from './params/utils/constants'
import { initcalendarPcObj } from './utils/compatibility/mixins_initcalendar_business_pc'
import { initcalendarAppObj } from './utils/compatibility/mixins_initcalendar_business_app'
import { initcalendarFinancial } from './utils/compatibility/mixins_initcalendar_financial'
import { ELEMENT_PADDING_DEFAULT, ELEMENT_PADDING_DEFAULT_MOBILE } from './supernatant/boardElements/constant'
import { PC_MOBILE_DISABLED_ELEMENT, PC_MOBILE_DISABLED_PARAM } from '../pcMobile/constans'
import { BoardParamElement } from 'packages/base/board/displayPanel/params/utils/boardParamElement'
import pc2mobile from './utils/pc2mobile'
import pc2mobileBeforeCompatible from './utils/pc2mobileBeforeCompatible'
import { SOURCE_DATA_TYPE } from './supernatant/boardElements/elementChart/constant'

function isNeedHandleCalendar(type) {
  return [
    TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS,
    TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS,
    TYPE_PARAM_ELEMENT.CALENDAR_QUICK,
    TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION,
  ].some(item => item === type)
}

// 过滤废弃的参数组件
export function compatibleParamsPanelList(paramsPanelList, vm) {

  paramsPanelList.forEach(item => {
    if (!Array.isArray(item.content)) return

    // loaction
    item.content = item.content.filter(el => PARAMS_TYPE_LIST.indexOf(el.type) !== -1).map(item => {
      isNeedHandleCalendar(item.type) && compatibleData(item)
      return item
    })

    // 日历重构数据兼容
    item.content.forEach((item) => {
      if (item.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR) {
        const initcalendar = () => {
          if (vm.utils.isMobile) {
            initcalendarAppObj.initcalendar(item, vm)
          } else {
            initcalendarPcObj.initcalendar(item, vm)
          }
        }
        compatibleData(item, initcalendar)
      } else if (item.type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR) {
        const initcalendar = () => {
          initcalendarFinancial.initcalendar(item, vm)
        }
        compatibleData(item, initcalendar)
      }
    })

    // 下拉框兼容方法 兼容没有维度字段
    item.content.forEach(item => {
      if (item.type === TYPE_PARAM_ELEMENT.SELECT_ORDINARY) {
        console.log('尝试兼容 下拉框')
        if (item.content?.dataSets && item.content?.dataSets?.length) {
          item.content?.dataSets?.forEach(dataset => {
            if (dataset?.associatedField === void 0) {
              vm.$set(dataset, 'associatedField', dataset?.columnName || '')
            }
          })
        }
      }
    })
  })

  paramsPanelList.forEach(panel => {
    if (!Array.isArray(panel.content)) return

    panel.content.forEach((item) => {
      if (vm.isDataReport && !item.content.storeLayout && !PC_MOBILE_DISABLED_PARAM.includes(item.type)) {
        const param = new BoardParamElement({ type: item.type })
        if (!param) return
        vm.$set(item.content, 'storeLayout', deepClone(param.content.storeLayout))

        item.content.storeLayout.pcLayout.i = item.id
        item.content.storeLayout.mobileLayout.i = item.id
      }
    })
  })
}

function isPC(context) {
  const { isMobileApp } = context.commonData
  return !(context.utils.isMobile || isMobileApp)
}

// 兼容函数列表
const compatibleFnList = []

// 交互兼容多维度
compatibleFnList.push((elList, context) => {
  elList.forEach(item => {
    const interactionOptions = context.$_getProp(item, 'content.interactionOptions', null)
    if (context.$_checkedType(interactionOptions, 'Object')) {
      context.$set(item.content, 'interactionOptions', [interactionOptions])
    }
  })
})
// 兼容layout
compatibleFnList.push((elList, _this) => {
  if (!_this.isDataReport) return
  const defaultOptions = {
    boardType: 'dataReport',
    isMobile: !_this.utils.isMobile,
    isScreen: _this.utils.isScreen || false,
    isDataReport: _this.isDataReport || true,
  }

  elList.forEach(item => {
    if (item.storeLayout || PC_MOBILE_DISABLED_ELEMENT.includes(item.type)) return

    const element = new BoardElement({
      type: item.type,
      ...defaultOptions,
    })

    _this.$set(item, 'storeLayout', deepClone(element.storeLayout))

    item.storeLayout.pcLayout.i = item.id
    item.storeLayout.mobileLayout.i = item.id
  })
})
// 超链接兼容多语言
compatibleFnList.push((elList, context) => {
  elList.forEach(item => {
    const superFun = (data, srt = 'content.superLinkOptions') => {
      const superLinkOptions = context.$_getProp(data, srt, [])
      if (superLinkOptions.length) {
        superLinkOptions.forEach(v => {
          v.soleId || context.$set(v, 'soleId', context.$_generateUUID())
          // 兼容指标器
          // if (item.type === TYPE_ELEMENT_GROUP.CHART) {
          //   const { site, label } = v.labelBoard
          //   site === label && context.$set(v.labelBoard, 'site', 1)
          // }
        })
      }
    }
    superFun(item)
    // if (item.type === TYPE_ELEMENT_GROUP.CHART) {
    //   const chioceTab = item.content.chioceTab
    //   if (chioceTab) {
    //     chioceTab.forEach(v => {
    //       superFun(v, 'saveObj.superLinkOptions')
    //     })
    //   }
    // }
  })
})

// 移动端看板元素宽度
// bugfix 58199  影响：7794 这种调整会失效
// compatibleFnList.push((elList, context) => {
//   const colNum = GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY
//   isPC(context) || elList.forEach(({ layout }) => layout.w !== colNum && (layout.w = colNum))
// })

// 大屏数据兼容
compatibleFnList.push((elList, context) => {
  if (!context.isLargeScreen) {
      context.boardInfo.screenModeDate && context.$set(context.boardInfo.screenModeDate, 'screenMode', false)
      return
  }
  // const { refreshData, screenMode, interval = 1, dataRefreshTime = false, propertyNumber = false, themeType } = context.boardInfo.screenModeDate
  const { refreshData = {}, screenMode, interval = 1, themeType, isCloseCarousel = false, BottomColor, BottomPadding, TopColor, TopPadding, screenSpaceMode } = context.boardInfo.screenModeDate
  const { DATA_UPDATE_TIME, DATA_REFRESH_TIME, PROPERTY_NUMBER } = INFORMATION_KEY
  // if (!refreshData) {
  //   const refreshData = context.boardData.paramsPanelList.reduce((pre, next, index) => {
  //     if (next) {
  //       let clone = context.$_deepClone(refreshDataInit)
  //       clone.interval = index ? 1 : interval
  //       // clone.dataRefreshTime = dataRefreshTime
  //       // clone.propertyNumber = propertyNumber
  //       pre[next.id] = clone
  //     }
  //     return pre
  //   }, {})
  //   context.$set(context.boardInfo, 'screenModeDate', {
  //     screenMode,
  //     refreshData,
  //     themeType,
  //   })
  // } else {
  const newRefreshData = context.boardData.paramsPanelList.reduce((pre, item, index) => {
    let tabData = context.$_deepClone(refreshDataInit)
    if (refreshData[item.id]) {
      tabData = refreshData[item.id]
    } else if (!index) {
      // 兼容refreshData对象还没的 并且是第一，使用原来的interval数据
      tabData.interval = interval
    }
    let list = tabData.groupList
    if (!tabData.groupList) {
      // 老数据兼容
      list = [{
        name: DATA_UPDATE_TIME,
        show: tabData ? tabData.dataUpdateTime : false,
        position: 'right',
      }, {
        name: DATA_REFRESH_TIME,
        show: tabData ? tabData.dataRefreshTime : false,
        position: 'right',
      }, {
        name: PROPERTY_NUMBER,
        show: tabData ? tabData.propertyNumber : false,
        position: 'right',
      }]
    }
    tabData.groupList = checkRefreshData(list, refreshDataInit.groupList)
    pre[item.id] = tabData
    return pre
  }, {})
  // context.$set(context.boardInfo.screenModeDate, 'refreshData', newRefreshData)
  // 清除不需要数据
  Object.values(newRefreshData).forEach(item => {
    if (item) {
      ['dataRefreshTime', 'propertyNumber', 'dataUpdateTime'].forEach(key => {
        delete item[key]
      })
    }
  })
  const screenModeDate = {
    isCloseCarousel,
    screenMode,
    refreshData: newRefreshData,
    themeType,
    BottomColor,
    BottomPadding,
    TopColor,
    TopPadding,
    screenSpaceMode
  }
  Object.keys(screenModeDate).forEach(key => {
    context.$set(context.boardInfo.screenModeDate, key, screenModeDate[key])
  })
  context.$set(context.boardInfo, 'screenModeDate', context.boardInfo.screenModeDate)
  // }
})

export function checkRefreshData(olList, defaultList) {
  if (olList.length === defaultList.length) {
    return olList
  } else {
    let list = []
    defaultList.forEach(item => {
      let newObj = olList.find(el => el.name === item.name)
      if (!newObj) {
        list.push(item)
      }
    })
    return [...olList, ...list]
  }
}

// 兼容关联数据
compatibleFnList.push((elList, _this) => {
  if (_this.boardInfo.paramGangedList) {
    let saveArr: any[] = []

    const deep = (arr, list) => {
      arr.forEach(el => {
        if (el.children.length) {
          list.push(...el.children)
          deep(el.children, list)
        }
      })
    }

    const fn = (arr) => {
      arr.forEach(el => {
        if (el.children.length) {
          const children: any[] = [...el.children]
          deep(el.children, children)

          saveArr.push({
            id: el.id,
            type: el.type,
            children: children.map(item => ({
              id: item.id,
              type: item.type,
            }))
          })

          fn(el.children)
        }
      })
    }

    Object.values(_this.boardInfo.paramGangedList).forEach((item) => {
      saveArr = []

      if (Array.isArray(item) && item.length) {
        // 如果没有parentId说明是执行过兼容代码直接return
        if (item[0].parentId === undefined) return
        fn(item)
        item.splice(0, item.length, ...saveArr)
      }
    })
  }
})

// 兼容看板元素多主题
compatibleFnList.push((elList, _this) => {
  const { isMobile, isJinMaoApp } = _this.utils || {}
  elList.forEach(el => {
    const boardElementTheme = _this.$_JSONClone(BOARD_ELEMENT_THEME)

    if (!Object.keys(el).includes('themeMap')) {
      const shadowColor = el.style.boxShadow.split(' ').pop()
      let { isOpen, themeType } = _this.utils.themeParameters || {}
      if (isOpen && shadowColor !== BOARD_ELEMENT_THEME[themeType].shadowColor) {
        boardElementTheme[themeType].shadowColor = shadowColor
      }
      _this.$set(el, 'themeMap', boardElementTheme)
    }

    const themeMap = el.themeMap
    // Mobile兼容蓝色主题
    const setMobileElStyle = () => {
      _this.$set(el.themeMap, THEME_TYPE.deepBlue, {
        ...el.themeMap[THEME_TYPE.darkBlue],
        v33202_compatible: true
      })
    }

    Object.keys(boardElementTheme).forEach(key => {
        if (!themeMap[key]) {
          if (THEME_TYPE.deepBlue === key && isMobile && isJinMaoApp) {
            setMobileElStyle()
          } else {
            _this.$set(el.themeMap, key, boardElementTheme[key])
          }
        }
    })

    // pc兼容蓝色主题
    if (!isMobile && el.themeMap[THEME_TYPE.deepBlue] && !el.themeMap[THEME_TYPE.deepBlue].v33202_compatible) {
      // if (isMobile) {
      //   setMobileElStyle()
      // } else {
      const boardElementTheme = _this.$_JSONClone(BOARD_ELEMENT_THEME)
      _this.$set(el.themeMap, THEME_TYPE.deepBlue, boardElementTheme[THEME_TYPE.deepBlue])
      // }
    }

    // 移动端兼容容器标题
    if (isMobile && isOrdinaryContainer_fn(el)) {
      const themeStyleMap = el.content.themeStyleMap || {}

      if (themeStyleMap[THEME_TYPE.darkBlue] && !themeStyleMap[THEME_TYPE.deepBlue]) {
        _this.$set(el.content.themeStyleMap, THEME_TYPE.deepBlue, themeStyleMap[THEME_TYPE.darkBlue])
      }
    }

    // 兼容卡片已设置的背景色
    if (el.type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
      const theme = Object.keys(THEME_TYPE).map(item => THEME_TYPE[item])
      const themeTypeList = [...new Set(theme)]
      themeTypeList.forEach(themeType => {
        const themeConfig = themeType === THEME_TYPE['classicWhite'] ? el.content.chartUserConfig : el.content.chartUserConfig.darkTheme
        if (!themeConfig || !themeConfig.cardColor || Array.isArray(themeConfig.cardColor)) return
        const bgcKey = isMobile ? 'mobileBgc' : 'pcBgc'
        el.themeMap[themeType][bgcKey] = themeConfig.cardColor
        // 删除原先背景色设置
        _this.$delete(themeConfig, 'cardColor')
      })
    }

    // const cKey = THEME_TYPE['classicWhite']
    // if (Object.keys(el.themeMap[cKey]).length !== Object.keys(BOARD_ELEMENT_THEME[cKey]).length) {
    //   const nThemeMap = {}
    //   for (const key in el.themeMap) {
    //     if (Object.hasOwnProperty.call(el.themeMap, key)) {
    //       nThemeMap[key] = { ...BOARD_ELEMENT_THEME[key], ...el.themeMap[key] }
    //     }
    //   }
    //   this.$set(el, 'themeMap', nThemeMap)
    // }

  })
})

// 表格多语言兼容
compatibleFnList.push((elList, _this) => {
  elList.forEach(el => {
    if (el.type === TYPE_ELEMENT.TABLE) {
      if (el.content?.dataScreenRes) {
        const { tableControlsElementLanList = [], tableControlsLanguageList = [] } = el.content.dataScreenRes
        const lan = {
          tableControlsElementLanList: tableControlsElementLanList.filter(e => !(e.type === true && e.key === true && e.element)),
          tableControlsLanguageList: tableControlsLanguageList.filter(e => !(e.type === true && e.key === true && e.element)),
        }
        Object.assign(el.content.dataScreenRes, lan)
      }
    }
  })
})

// 图形兼容指标数据
compatibleFnList.push((elList, _this) => {
  elList.forEach(el => {
    if (el.type !== TYPE_ELEMENT.CHART) return

    if (!el.content.drillSettings || el.content.drillSettings.indexFlagIds || el.content.drillSettings.sourceDataType) {
      return
    }

    const dataSetId = el.content.drillSettings.dataSetId

    Object.assign(el.content.drillSettings, el.content.drillSettings.indexFlag ? {
      indexFlagIds: [dataSetId],
      sourceDataType: SOURCE_DATA_TYPE.indexFlag
    } : {
      indexFlagIds: [],
      sourceDataType: SOURCE_DATA_TYPE.dataset
    })
  })
})

function copyThemeDarkToBlueFun(obj) {
  if (typeof obj !== 'object') return
  if (Array.isArray(obj)) {
    obj.forEach(item => {
      copyThemeDarkToBlueFun(item)
    })
  } else if (typeof obj === 'object') {
    if (obj[THEME_TYPE.classicWhite] && obj[THEME_TYPE.darkBlue]) {
      if (!obj[THEME_TYPE.deepBlue]) {
        obj[THEME_TYPE.deepBlue] = deepClone(obj[THEME_TYPE.darkBlue])
      } else {
        Object.assign(obj[THEME_TYPE.deepBlue], deepClone(obj[THEME_TYPE.darkBlue]))
      }
    }

    Object.keys(obj).forEach(key => {
      copyThemeDarkToBlueFun(obj[key])
    })
  }
}

// 内边距兼容
export function elementPaddingCompatible(element, _this) {
  const { isMobile } = typeof _this === 'boolean' ? { isMobile: _this } : _this.utils || {}
  if (!element.styleConfig) {
    Vue.set(element, 'styleConfig', {})
  }
  const { type, styleConfig, style, content } = element
  const paddingList = isMobile ? ELEMENT_PADDING_DEFAULT_MOBILE[type] : ELEMENT_PADDING_DEFAULT[type]
  if (!paddingList) return

  // 相关逻辑 b4f58fd4-d2db-4028-882e-48d122c95a92
  if (!styleConfig.padding) {
    Vue.set(styleConfig, 'padding', {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    })
    styleConfig.padding.top = paddingList[0]
    styleConfig.padding.right = paddingList[1]
    styleConfig.padding.bottom = paddingList[2]
    styleConfig.padding.left = paddingList[3]

    if (type === TYPE_ELEMENT.TEXT) {
      const onlyTitle = !!element.content.title &&
        element.content.hasOwnProperty('titleVerticalCenter') &&
        (!element.content.richTextHtml || element.content.richTextHtml === '<p><br></p>')

      if (onlyTitle) {
        styleConfig.padding.top = 6
        styleConfig.padding.bottom = 6
      }
    } else if (type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
      const content = element.content || {}
      const cardType = content.tagNewCardContent || TAGNEWCARD.SINGLEINDEX
      const chartUserConfig = content.chartUserConfig || {}
      const { textWrap, selfAdaption, fixedTitle } = chartUserConfig

      if ([TAGNEWCARD.SINGLEINDEX, TAGNEWCARD.TWOINDICES].includes(cardType) && !selfAdaption && textWrap) {
        // 'indicator-card-padding'
        styleConfig.padding.top = 12
        styleConfig.padding.right = 18
        styleConfig.padding.bottom = 17
        styleConfig.padding.left = 18
      }
      if (fixedTitle && cardType === TAGNEWCARD.RATECARD) {
        // 'rate-card-padding'
        styleConfig.padding.top = 12
        styleConfig.padding.right = 18
        styleConfig.padding.bottom = 10
        styleConfig.padding.left = 18
      }
      if (isMobile) {
        // 'mobile-card-padding'
        styleConfig.padding.top = 0
        styleConfig.padding.right = 18
        styleConfig.padding.bottom = 0
        styleConfig.padding.left = 18
      }
    } else if (type === TYPE_ELEMENT.COMBINE_CARD) {
      // 开了是 7 7 12 7
      // 没开是 有tab则12 没则0
      if (style.hasOwnProperty('showPadding')) {
        if (!style.showPadding) {
          styleConfig.padding.top = 0
          styleConfig.padding.right = 0
          styleConfig.padding.bottom = 0
          styleConfig.padding.left = 0

          if (content?.choiceTab?.length > 1) {
            styleConfig.padding.bottom = 12
          }
        }
      }
    }
  }
}
export function paddingCompatibleFun(element, padding, isMobile) {
  const { type } = element
  const paddingList = isMobile ? ELEMENT_PADDING_DEFAULT_MOBILE[type] : ELEMENT_PADDING_DEFAULT[type]
  if (!paddingList) return

  padding.top = paddingList[0]
  padding.right = paddingList[1]
  padding.bottom = paddingList[2]
  padding.left = paddingList[3]

  if (type === TYPE_ELEMENT.TEXT) {
    const onlyTitle = !!element.content.title &&
      element.content.hasOwnProperty('titleVerticalCenter') &&
      (!element.content.richTextHtml || element.content.richTextHtml === '<p><br></p>')

    if (onlyTitle) {
      padding.top = 6
      padding.bottom = 6
    }
  } else if (type === TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD) {
    const content = element.content || {}
    const cardType = content.tagNewCardContent || TAGNEWCARD.SINGLEINDEX
    const chartUserConfig = content.chartUserConfig || {}
    const { textWrap, selfAdaption, fixedTitle } = chartUserConfig

    if ([TAGNEWCARD.SINGLEINDEX, TAGNEWCARD.TWOINDICES].includes(cardType) && !selfAdaption && textWrap) {
      // 'indicator-card-padding'
      padding.top = 12
      padding.right = 18
      padding.bottom = 17
      padding.left = 18
    }
    if (fixedTitle && cardType === TAGNEWCARD.RATECARD) {
      // 'rate-card-padding'
      padding.top = 12
      padding.right = 18
      padding.bottom = 10
      padding.left = 18
    }
    if (isMobile) {
      // 'mobile-card-padding'
      padding.top = 0
      padding.right = 18
      padding.bottom = 0
      padding.left = 18
    }
  }
}
compatibleFnList.push((elList, _this) => {
  elList.forEach(el => {
    elementPaddingCompatible(el, _this)
  })
})

// 表格主题移动端兼容
function elementTableThemeMobileCompatible(elList, _this) {
  const { isMobile, isJinMaoApp } = _this.utils || {}
  if (!isMobile || !isJinMaoApp) return

  elList.forEach(el => {
    if (el.type === TYPE_ELEMENT.TABLE && !el.content.version) {
      copyThemeDarkToBlueFun(el.content)
      if (el.themeMap?.[THEME_TYPE.darkBlue] && el.themeMap?.[THEME_TYPE.deepBlue]) {
        if (el.themeMap?.[THEME_TYPE.darkBlue]?.shadowColor) {
          el.themeMap[THEME_TYPE.deepBlue].shadowColor = el.themeMap[THEME_TYPE.darkBlue].shadowColor
        }
      }
      el.content.version = '3.32.02'
    }
  })
}
compatibleFnList.push(elementTableThemeMobileCompatible)

// 清除tagModeStack的els
compatibleFnList.push((elList, _this) => {
  if (_this.boardInfo?.tagModeStack?.els) {
    delete _this.boardInfo?.tagModeStack?.els
  }
})

export function interactive_compatible(elList) {
  // 所有的兼容代码跑之前先执行
  this.utils.isPC2App && pc2mobileBeforeCompatible.call(this)

  compatibleFnList.forEach(fn => {
    fn(elList, this)
  })

  // 所有的兼容代码跑完后再执行
  this.utils.isPC2App && pc2mobile.call(this)
}

export function adapter_screen_data({ content, channel }) {
  const screenMode = content?.boardInfo?.screenModeDate?.screenMode
  if (!(screenMode && channel === '1')) {
    throw Error('不是大屏哦')

  }
  const advanceContainerIds = content.elList.filter(el => isAdvanceContainer_fn(el)).map(el => el.id)
  const adapter = (el) => {
    const { style, layout, themeMap } = el
    if (themeMap) {
      Object.entries(themeMap).forEach(([theme, val]) => {
        if (!Object.keys(val).includes('gradientColor')) {
          val['gradientColor'] = BOARD_ELEMENT_THEME[theme]['gradientColor']
        }
        if (!Object.keys(val).includes('borderColor')) {
          val['borderColor'] = BOARD_ELEMENT_THEME[theme]['borderColor']
        }
      })
    } else {
      el.themeMap = JSONClone(BOARD_ELEMENT_THEME)
    }
    if (isAdvanceContainer_fn(el) || (el.type !== TYPE_ELEMENT.CONTAINER && !el._containerId)) {
      el['styleConfig'] = styleConversion(style, layout)
    }
    return el
  }
  const adapterAdvanceContainerChild = (el, arr) => {
    const { style, layout } = el
    if (advanceContainerIds.includes(el._containerId)) {
      const advanceContainer = arr.find(e => e.id === el._containerId)
      if (advanceContainer) {
        const { styleConfig, content: { settings: { showCursor, showContainerTitle }, tabList, themeStyleMap, fontStyle } } = advanceContainer
        tabList.forEach(tab => {
          const { includedElsIdList = [] } = tab.content
          if (includedElsIdList.includes(el.id)) {
            const layouts = includedElsIdList.map(id => arr.find(el => el.id === id)).map(el => el.layout)
            const totalH = Math.max(...layouts.map(({ y, h }) => y + h))
            const containerWidth = styleConfig.size.width - (ADVANCE_CONTAINER_PR + ADVANCE_CONTAINER_PL)
            const getContainerHeight = () => {
              const defaultHeight = PC_TOP_PADDING + ((showCursor || showContainerTitle) ? PC_SHOW_TITLE_OR_SHOW_CURSOR_DEFAULT : 0)
              const themeType = 'sdp-dark-blue'
              const fontData = themeStyleMap ? getProp(themeStyleMap[themeType], 'fontData', undefined) : undefined
              let cloneFontStyle = JSONClone(fontStyle)
              if (cloneFontStyle === undefined && fontData) {
                cloneFontStyle = fontData
              } else if (cloneFontStyle && fontData) {
                cloneFontStyle.color = fontData.color
              }
              const newFontStyle = (showContainerTitle && cloneFontStyle) || cloneFontStyle
              let fixHeight = newFontStyle && newFontStyle['font-size']
                ? parseFloat(newFontStyle['font-size']) - PC_CONTAINER_TITLE_DEFAULT_FONT_SIZE
                : 0
              const heightTitle = defaultHeight + fixHeight
              const elBottomPadding = 10
              return styleConfig.size.height - heightTitle - elBottomPadding
            }
            const rowHeight = (() => {
              const containerHeight = getContainerHeight()
              const layoutH = totalH
              const gap = PC_ADVANCE_CONTAINER_GRID_LAYOUT_MARGIN_X
              return (containerHeight - gap) / layoutH - gap
            })()
            el['styleConfig'] = styleConversion(style, layout, containerWidth, rowHeight)
          }
        })
        // rowHeight,
      }
    }
    return el
  }
  content.elList = content.elList.filter(el => {
    return isAdvanceContainer_fn(el) || advanceContainerIds.includes(el._containerId) || (el.type !== TYPE_ELEMENT.CONTAINER && !el._containerId)
  }).map(el => adapter(el)).map((el, index, arr) => adapterAdvanceContainerChild(el, arr)).map((el, index) => {
    el.zIndex = (index + 1)
    return el
  })
}

export function compatibleInjectCondition(paramsPanelList, datasetList) {
  const setInjectCondition = (paramElement) => {
    if (!paramElement) return
    const isParameterBindfield = (dataSetId, injectConditionId) => {
      const dataset = datasetList.find(item => item.id === dataSetId && item?.parameterBindfield === '1')

      if (!dataset || !Array.isArray(dataset.parameterBindfieldList) || !dataset.parameterBindfieldList.length) return false

      return dataset.parameterBindfieldList.some(item => item.id === injectConditionId)
    }

    switch (paramElement.type) {
      case TYPE_PARAM_ELEMENT.LOCATION_NEW: {
        const injectConditionParameter = paramElement.content.options.injectConditionParameter || []
        if (injectConditionParameter.length) {
          paramElement.content.options.injectConditionParameter = injectConditionParameter.filter(item => isParameterBindfield(item.dataSetId, item.injectConditionId))
        }
      }
        break
      case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR:
      case TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR:
      case TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION:
      case TYPE_PARAM_ELEMENT.SELECT_ORDINARY: {
        const injectConditionParameter = paramElement.content.injectConditionParameter || []
        if (injectConditionParameter.length) {
          paramElement.content.injectConditionParameter = injectConditionParameter.filter(item => isParameterBindfield(item.dataSetId, item.injectConditionId))
        }
      }
        break
    }
  }

  paramsPanelList.forEach(panel => {
    if (!Array.isArray(panel.content)) return

    panel.content.forEach((item) => {
      setInjectCondition(item)
    })
  })
}
