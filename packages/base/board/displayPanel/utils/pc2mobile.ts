import {
  BG_MAP_STRING,
  GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY,
  GRID_LAYOUT_MARGIN_FOR_BORDER_DISPLAY,
  GRID_LAYOUT_ROW_HEIGHT_FOR_BORDER_DISPLAY,
} from '../../../../assets/constant'
import {
  isAdvanceContainer_fn,
  isContainer_fn,
  isOrdinaryContainer_fn,
} from '../../../../assets/utils/helper'
import { TYPE_ELEMENT } from '../constants'
import { TYPE_PARAM_ELEMENT_AS_CONST } from '../params/utils/constants'
import { hideChartOfMobile } from '../supernatant/boardElements/elementChart/constant'
import {TYPE_LIST} from "../params/paramElement/bussinessCalendar/components/constants";

const rowHeight = GRID_LAYOUT_ROW_HEIGHT_FOR_BORDER_DISPLAY
const pcRowHeight = rowHeight
const [rowSpacing, colSpacing] = GRID_LAYOUT_MARGIN_FOR_BORDER_DISPLAY

const getSortElList = (elList) => {
  return elList.sort((a, b) => {
    if (a.layout.y === b.layout.y) {
      return a.layout.x - b.layout.x
    }
    return a.layout.y - b.layout.y
  })
}

function resetLayout() {
  const {
    colNum: pcColNum,
    row: pcRowSpacing,
    col: pcColSpacing,
  } = this.boardInfo.boardLayout

  this.boardInfo.boardLayout = {
    colNum: GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY,
    row: GRID_LAYOUT_MARGIN_FOR_BORDER_DISPLAY[0],
    col: GRID_LAYOUT_MARGIN_FOR_BORDER_DISPLAY[1],
  }

  const outOfContainerElList = this.boardData.elList.filter(
    (el) => !el._containerId
  )
  const sortedElList = getSortElList(outOfContainerElList)
  const mobileWidth = 375 - 2 - rowSpacing * 2
  const pcWidth = 1248 - pcRowSpacing * 2

  let currentY = 0

  sortedElList.forEach((el) => {
    el.layout.x = 0
    el.layout.y = currentY

    // (el.layout.h * (pcRowHeight + pcColSpacing) - pcColSpacing) / (el.layout.w * (pcWidth / pcColNum)) = (h * (rowHeight + colSpacing) - colSpacing) / mobileWidth
    // h = (colSpacing + mobileWidth * (el.layout.h * (pcRowHeight + pcColSpacing) - pcColSpacing) / (el.layout.w * (pcWidth / pcColNum))) / (rowHeight + colSpacing)
    el.layout.h = Math.round(
      (colSpacing +
        (mobileWidth *
          (el.layout.h * (pcRowHeight + pcColSpacing) - pcColSpacing)) /
          (el.layout.w * (pcWidth / pcColNum))) /
        (rowHeight + colSpacing)
    )

    el.layout.w = GRID_LAYOUT_COL_NUM_FOR_BORDER_DISPLAY
    currentY += el.layout.h
  })
}

function filterElement(cb) {
  const removeElTypes: string[] = [TYPE_ELEMENT.DUPONT_ANALYSIS]
  const removeEls: any[] = this.boardData.elList.filter((el) => removeElTypes.includes(el.type))

  // 过滤图形元素
  const chartEls: any[] = this.boardData.elList.filter((el) => el.type === TYPE_ELEMENT.CHART)
  // 处理图形元素的"指标选择器"
  chartEls.forEach(el => {
    if (!Array.isArray(el.content.chioceTab)) return
    const saveIndexID = el.content.chioceTab[el.content.saveIndex].id
    // 移除"指标选择器"里的无效元素
    el.content.chioceTab = el.content.chioceTab.filter(item => !hideChartOfMobile.includes(item.saveObj.alias))
    if (!el.content.chioceTab.length) return
    // 重定向"指标选择器"的指针
    const saveIndex = el.content.chioceTab.findIndex(item => item.id === saveIndexID)
    if (saveIndex === -1) {
      el.content.saveIndex = 0
      Object.assign(el.content, el.content.chioceTab[0].saveObj)
    } else {
      el.content.saveIndex = saveIndex
    }
  })
  // 需要移除的图形元素
  const removeCharts: any[] = chartEls.filter((el) => hideChartOfMobile.includes(el.content.alias))

  const removeIds: string[] = [...removeEls, ...removeCharts].map(el => el.id)
  this.boardData.elList.splice(0, this.boardData.elList.length, ...this.boardData.elList.filter(el => !removeIds.includes(el.id)))
  cb(removeIds)
}

function filterParams() {
  const excludes = [
    TYPE_PARAM_ELEMENT_AS_CONST.DATE_QUICK_OPERATION,
    TYPE_PARAM_ELEMENT_AS_CONST.CONTAINER_SELECT,
    TYPE_PARAM_ELEMENT_AS_CONST.DATE_TYPE,
    TYPE_PARAM_ELEMENT_AS_CONST.PEOPLE_LAYER,
    TYPE_PARAM_ELEMENT_AS_CONST.LOCATION_QUICK,
  ]
  this.boardData.paramsPanelList.forEach((item) => {
    item.content = item.content.filter((item) => !excludes.includes(item.type))
  })
}

function metricFieldConversion () {
  this.boardData.paramsPanelList.forEach((item) => {
    item.content
      .filter(item => item.type === TYPE_PARAM_ELEMENT_AS_CONST.METRIC)
      .forEach((item) => {
        item.content.defaultValueMobile = [].concat(item.content.defaultValue)
        delete item.content.defaultValue
      })
  })
}
function bussinessCalendarConversion () {
  this.boardData.paramsPanelList.forEach((item) => {
    item.content
      .filter(item => item.type === TYPE_PARAM_ELEMENT_AS_CONST.BUSSINESS_CALENDAR)
      .forEach((item) => {
        const userSetting = item.content.userSetting
        let hasSet = false
        if (userSetting.defaultData?.isDefault) {
          userSetting.selectType = TYPE_LIST.default
        }
        if (userSetting.yearData?.isYear) {
          userSetting.selectType = TYPE_LIST.Year
          hasSet = true
        }
        if (userSetting.monthData?.isMonth) {
          if (hasSet) {
            userSetting.monthData.isMonth = false
          } else {
            userSetting.selectType = TYPE_LIST.Month
            hasSet = true
          }
        }
        if (userSetting.weekData?.isWeek) {
          if (hasSet) {
            userSetting.weekData.isWeek = false
          } else {
            hasSet = true
            userSetting.selectType = TYPE_LIST.Week
          }
        }
        if (userSetting.intervalData?.isInterval) {
          if (hasSet) {
            userSetting.intervalData.isInterval = false
          } else {
            hasSet = true
            userSetting.selectType = TYPE_LIST.Interval
          }
        }
        if (userSetting.eventData?.isEvent) {
          if (hasSet) {
            userSetting.eventData.isEvent = false
          } else {
            hasSet = true
            userSetting.selectType = TYPE_LIST.Event
          }
        }
      })
  })
}

// 元素背景色字段转换
function mobileBgcFieldConversion() {
  this.boardData.elList.forEach((el) => {
    Object.keys(el.themeMap).forEach(themeType => {
      el.themeMap[themeType][BG_MAP_STRING.mobile] = el.themeMap[themeType][BG_MAP_STRING.pc]
    })
  })
}

// boardInfo 下的字段兼容
function boardInfoFieldConversion() {
  this.boardInfo.tabBarComponentConfig.isCurrencyUnitAbbrBtn = this.boardInfo.isCurrencyUnitAbbrBtn
}

function filterIncludedElIds(removeIds) {
  this.boardData.elList.forEach((el) => {
    if (isOrdinaryContainer_fn(el)) {
      el.content.includedElIds = el.content.includedElIds.filter(
        (id) => !removeIds.includes(id)
      )
    } else if (isAdvanceContainer_fn(el)) {
      el.content.tabList.forEach((tab) => {
        tab.content.includedElsIdList = tab.content.includedElsIdList.filter(
          (id) => !removeIds.includes(id)
        )
      })
    }
  })
}

function clearSuperLinkOptions() {
  this.boardData.elList.forEach((el) => delete el.content.superLinkOptions)
}

function clearAdvanceContainerFilterSorterConfig() {
  this.boardData.elList
    .filter(el => isAdvanceContainer_fn(el))
    .forEach((el) => {
      delete el.filterSortDisplayMode
      delete el.content.filterSorterStyle
      el.content.tabList.forEach(tab => {
        delete tab.content.filterData
        delete tab.content.sorterData
        delete tab.content.filterSortDisplayMode
      })
    })
}

function clearFilterSortDisplayMode() {
  this.boardData.elList.forEach((el) => delete el.filterSortDisplayMode)
}

function clearChartUserConfigIsHover() {
  this.boardData.elList
    .filter(el => el.type === TYPE_ELEMENT.CHART)
    .forEach((el) => (el.content.chartUserConfig.isHover = true))
}

function clearInteractionOptions(removeIds) {
  this.boardData.elList.forEach((el) => {
    if (Array.isArray(el.content.interactionOptions)) {
      el.content.interactionOptions = el.content.interactionOptions.filter(
        (options) => {
          options.associElements = options.associElements.filter(
            (item) => !removeIds.includes(item.id)
          )
          return options.associElements.length !== 0
        }
      )
      !el.content.interactionOptions.length &&
        delete el.content.interactionOptions
    }
  })
}

// 移除配置项 -- 看板标签模式
function clearTagModeStack() {
  this.boardInfo.nameSub = ''
  this.boardInfo.openBoardTab = false
  delete this.boardInfo.tagModeStack
}

function resetKanbanStatus() {
  this.boardInfo.metaType = '0' // 当前看板是否为副本
}

function resetTabWidthValue() {
  this.boardData.elList
    .filter((el) => isContainer_fn(el))
    .forEach((el) => delete el.content.settings.tabWidthValue)
}

function resetLocationSettings() {
  this.boardData.paramsPanelList.forEach((item) => {
    item.content.forEach((item) => {
      if (item.type === TYPE_PARAM_ELEMENT_AS_CONST.LOCATION_NEW) {
        item.content.labelPickerConfig.isLabelPicker = false
        item.content.options.hierarchyType = ''
        item.content.options.paramType = '1'
        Array.isArray(item.content.options.dataSetFields) && item.content.options.dataSetFields.forEach(item => {
          item.outletNameField = {
            columnName: '',
            lgeType: ''
          }
          item.shopNameField = {
            columnName: '',
            lgeType: ''
          }
        })
      }
    })
  })
}

function pc2mobile() {
  const removeIds: string[] = []

  Array(
    ...[
      filterElement.bind(this, (ids) => removeIds.push(...ids)),
      filterParams.bind(this),
      metricFieldConversion.bind(this),
      bussinessCalendarConversion.bind(this),
      mobileBgcFieldConversion.bind(this),
      boardInfoFieldConversion.bind(this),
      clearSuperLinkOptions.bind(this),
      clearAdvanceContainerFilterSorterConfig.bind(this),
      clearFilterSortDisplayMode.bind(this),
      clearChartUserConfigIsHover.bind(this),
      clearInteractionOptions.bind(this, removeIds),
      filterIncludedElIds.bind(this, removeIds),
      resetLayout.bind(this),
      clearTagModeStack.bind(this),
      resetKanbanStatus.bind(this),
      resetTabWidthValue.bind(this),
      resetLocationSettings.bind(this),
    ]
  ).forEach((fn) => {
    try {
      fn()
    } catch (error) {
      console.error(error)
    }
  })
}

export default pc2mobile
