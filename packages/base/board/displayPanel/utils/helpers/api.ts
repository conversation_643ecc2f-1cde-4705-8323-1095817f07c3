// import { Message } from 'element-ui'
// import { Toast } from 'cube-ui'

import Vue from 'vue'

// 获取看板信息
export function getBoardInfo(api, id, code) {
  const str = code ? `/bi/metaDashboard/getBoard/${id}/${code}` : `/bi/metaDashboard/getBoard/${id}`
  return api.get(str)
}

// 获取tenantData
export function getTenantConfig(api, id) {
  return api.post('/rule/quickCalendarConfig/getTenantConfig/' + id)
}

// 获得看板数据2.0
export function getBoardMergeInfo(api: any, id: string, code?) {
  const str = code ? `/bi/metaDashboard/getBoardMergeInfo/${id}/${code}` : `/bi/metaDashboard/getBoardMergeInfo/${id}`
  return api.get(str)
}

// 获取看板预警订阅数据
export function getBoardWarningSubscribeData(api, params) {
  return api.post(`/bi/metaDashboardElement/listByBoardId/${ params.boardId }/${ params.isPermissionWarning }`)
}

// 编辑预警订阅数据
export function updateElementWarningSubscribeData(api, params) {
  return api.post(`/warning/userRuleSubscription/upsert`, params)
}

// 启用禁用预警订阅
export function enableWarningSubscribe(api, params) {
  return api.post(`/warning/userRuleSubscription/enable`, params)
}

// 启用禁用预警订阅
export function deleteWarningSubscribe(api, params) {
  return api.post(`/warning/unValidSubscribe/delete/${ params }`)
}

// app端获取预警详情
export function getWarningSubscribeInfo(api, params) {
  return api.post(`/warning/userRuleSubscription/appGetById/${ params.id }/${ params.invalidFlag || '0' }`)
}

// 获取偏好设置参数
export function getRuntimeParams(api, boardId) {
  return api.get('/bi/tableParams/get/tableParams', {
    params: {
      boardId,
      elementId: '',
    },
  })
}

export function preview(api, cancelToken, params) {
  return previewMergeApi({ api, params, cancelToken })
}

let vm = null

export const cacheVm = (_this) => {
  if (!_this || _this instanceof Vue) {
    vm = _this
  }
}

export const errToast = (error) => {
  if (vm) {
    if (/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)) {
      vm.$createToast({
          type: 'warn',
          time: 3 * 1000,
          txt: error.message
      }).show()
    } else {
      vm.$message({
        showClose: true,
        duration: 3 * 1000,
        message: error.message,
        type: 'error',
      })
    }
  }
}

export const isErr504And500 = (error) => (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) || error.message === 'Network Error' || error?.response?.status === 504 || error?.response?.status === 500

export const errCall = (error) => {
  if (isErr504And500(error)) {
    error.message = 'Query timeout, please contact administrator !'
    errToast(error)
  }
  // return Promise.reject(error)
}

export function previewMergeApi({ api, params, cancelToken = undefined }) {
  const queryId = params?.reportLog?.queryId
  const config = {
    ...cancelToken || {},
    headers: {
      ...cancelToken?.['headers'] || {},
      ...queryId ? { 'Query-Id': queryId } : {},
    }
  }
 // 需要加判断
  const apiPreviewUrl = Vue.prototype?.$getSystemConfig?.('api.urls.preview')
  return api.post(apiPreviewUrl || '/report/engine/preview', params, config)
}

// 判断看板是否需要进入加载
export function boardIntoHasRun(api) {
  return api.get('/system/config/get/value/response/param_control_t_f')
}

export function getTableConfigList(api, ids) {
  return api.post('/bi/talbe/get/tables', ids)
}

// 保存整个看板数据(新增)
export function saveBoard(api, params) {
  return api.post('/bi/metaDashboard/add', params)
}

// sbi保存整个看板数据(新增)
export function saveSbiMetaDashboard(api, params) {
  return api.post('/bi/sbiMetaDashboard/add', params)
}

// sbi更新整个看板数据(新增)
export function updateSbiMetaDashboard(api, params) {
  return api.post('/bi/sbiMetaDashboard/update', params)
}

export function updateBoard(api, params) {
  return api.post('/bi/metaDashboard/update', params)
}

// 导出图表和表格pdf
// export function exportPdf(api, param) {
//   return api.post('/export/fileExport/pdfexport', param, { responseType: 'blob' })
// }

// 获取看板水印
export function getBoardWatermark(api, tenantId) {
  return api.get('/admin/tenant/getWaterImage', {
    params: {
      tenantId,
    }
  })
}

// 获得计划任务
export function getParams(api, id) {
  // return api.get(`/job/subscribe/get/params/${id}`)
  return api.get(`/admin/busiboardplan/get/params/${id}`)
}

// 获取格式
export function getLessee(api, id) {
  return api.get('/admin/tenant/' + id)
}

// 判断是有财务日历
export function getTabCalendar(api, params) {
  return api.get(`/rule/accountCalendarDef/getTabCalendar/${params.isCompany}/${params.tenantId}`)
}

// 保存标签
export function saveLabelList(api, param) {
  return api.post(`/admin/pagetabs/bindDashboardTag`, param)
}

// 获取看板是否有导出权限
export function getBoardExportFlag(api, params) {
  return api.get(`/system/export/getBoardExportFlag`, { params })
}

// sbi获取看板是否有导出权限
export function getSbiBoardExport(api, params) {
  return api.get(`/admin/sbi/rolefunc/getBoardExport/${params.dashboardId}`)
}

// 保存或更新全局看板按钮设置
export function saveGlobalBtnSetting (api, param) {
  return api.post(`/bi/metaDashboard/saveOrUpdateGlobalSetting`, param)
}

// 获取全局看板按钮设置
export function getGlobalBtnSetting (api, params) {
  return api.get(`/bi/metaDashboard/getGlobalSetting`, { params })
}

// 获取全局参数数据
export function getGlobalParameterList(api, params) {
  return api.post(`/bi/globalParameter/bind/list`, params)
}

// 获取全局参数属性
export function getGlobalParameterProperty(api, params) {
  return api.post(`/bi/globalParameter/property`, params)
}

// 未发布看板---发布
export function releaseTenantBoard(api, params) {
  return api.post(`/bi/metaDashboard/releaseTenantBoard`, params)
}

// 副本看板--发布
export function update(api, id) {
  return api.get(`/bi/metaDashboard/replicate/update/${id}`)
}

// 获取数据更新时间
export function boardUpdateTime(api, params) {
  return api.post(`/report/metaDashboardRuleController/boardUpdateTime`, params)
}
