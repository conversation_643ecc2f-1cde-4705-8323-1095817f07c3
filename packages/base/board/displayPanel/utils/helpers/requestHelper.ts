import { boardIntoHasRun, getBoardExportFlag, getBoardMergeInfo, getTenantConfig, preview } from './api'
import { getTenantBoardParams } from '../../api'
import * as api from './api'
import { PREVIEW_STATUS } from 'packages/assets/constant'
import { TYPE_PARAM_ELEMENT } from 'packages/base/board/displayPanel/params/utils/constants'
import { ALL_PROJECT_NAME } from '../../../../../components/mixins/commonMixin'
import { getPlatValueByCodePid } from '../../params/api'
import { RecordBoardLog, TITLE_STYLE } from '../../utils'
import Vue from "vue";
import { THEME_TYPE } from '../../../../../assets/constant'

export default {
  getBoardContByid,
  requestEl,
  boardHasRun,
  tenantConfig
}

/**
 * 请求单个看板元素
 * @param {*} id
 * @param {*} options
 */
export async function requestEl(cancelToken, options) {
  if (!this.api) {
    throw new Error('没有传入ajax请求实例')
  }
  console.log('数据来了啊', options)
  return preview(this.api, cancelToken, options)
}

export function boardHasRun() {
  if (!this.api) {
    throw new Error('没有传入ajax请求实例')
  }
  return boardIntoHasRun(this.api)
}

/**
 * 获取看板内容(包括偏好设置)
 */
const RES_DEFAULT = {
  boardRes: null,
  runtimeRes: null,
  params: {},
  lang: null,
  initApiCall: null
}
// let requestRes = null

export function tenantConfig() {
  if (!this.api) {
    throw new Error('没有传入ajax请求实例')
  }
  // BugFix: 84735 未绑定租户情况下不调用此接口，后端解决不了这种情况下的403
  if (!this.utils.tenantId) {
    this.setTenantData({})
    return Promise.resolve()
  }
  return getTenantConfig(this.api, this.utils.tenantId).then(res => {
    console.log(res, '没有传入ajax请求实例')
    this.setTenantData(res)
  })
}
// 4014 移动步长
function layoutReSet(content) {
  content.elList.forEach(item => {
    console.log(item)
    if (item.layout) {
      item.layout.h = item.layout.h * 6
      item.layout.w = item.layout.w * 6
      item.layout.x = item.layout.x * 6
      item.layout.y = item.layout.y * 6
    }
  })
  content.isReSetLayout = true
  return JSON.stringify(content)
}
export function initApiCall(requestRes: any = {}, callback) {
  return Promise.all([
    tenantConfig.call(this),
    getPlatValueByCodePid(this.api, 'PLATFORM_FLAG'),
    // getPlatValueByCodePid(this.api, 'TABLE_DATA_EXPORT'),
  ]).then((res) => {
    requestRes.initApiCall = true

    try {
      const data = JSON.parse(res[1])
      const platformFlag = data[this.commonData.pid]
      this.$set(this.commonData, 'platformFlag', platformFlag || '')
      this.$set(this.commonData, 'isTableDataExport', res?.[2] === '1')
    } catch (err) {
      this.$set(this.commonData, 'platformFlag', '')
    }

    callback && initBoardResHelper({
      callback,
      requestRes
    })
  })
}

export function getBoardContByid(params, callback, errCallback, isLink = false) {
  if (!this.api) {
    throw new Error('没有传入ajax请求实例')
  }
  const { boardId, previewType, isOpenMethod = false, isSuperLink = false } = params
  const requestRes: any = Object.assign({}, RES_DEFAULT)
  // 初始化
  Object.assign(requestRes, { boardRes: null, runtimeRes: null, boardExportFlag: null, params: {} })

  this.recordBoardLog.setStartTime()

  getBoardMergeInfo(this.api, boardId).then(res => {
    let content = JSON.parse(res.metaDashboardDetailVO.content)
    if (!content['v06_t1605144706905']) {
      content['v06_t1605144706905'] = true
      res.metaDashboardDetailVO.content = layoutReSet(content)
    }
    if (res.metaDashboardDetailVO.channel === '2' && content['v06_t1605144706905'] === true) {
      const createDate = res.metaDashboardDetailVO?.createDate

      if (typeof createDate === 'string') {
        const createTime = new Date(createDate.split(' ')[0]).getTime()
        const updateTime = new Date('2024-12-04').getTime()
        if (createTime < updateTime) {
          const c = typeof res.metaDashboardDetailVO.content === 'string' ? JSON.parse(res.metaDashboardDetailVO.content) : content

          const themeDataTitle = c?.themeDataTitle?.[THEME_TYPE.darkBlue]
          if (themeDataTitle) {
            if (themeDataTitle?.title?.color === '#222') {
              themeDataTitle.title.color = '#fff'
            }
          } else {
            c.themeDataTitle = Object.keys(TITLE_STYLE).reduce((pre, key) => {
              let color = THEME_TYPE.darkBlue === key ? '#fff' : TITLE_STYLE[key].color
              if (THEME_TYPE.classicWhite === key && c?.nameCss?.color) {
                color = c.nameCss.color
              }
              pre[key] = {
                title: {
                  color: color
                }
              }
              return pre
            }, {})
          }

          c['v06_t1605144706905'] = 'V4.09.02'
          res.metaDashboardDetailVO.content = JSON.stringify(c)
        }
      }
    }
    requestRes.boardRes = res.metaDashboardDetailVO
    requestRes.boardRes.mobileHasFav = res.hasFav
    requestRes.boardRes.nowStr = res.nowStr
    requestRes.boardRes.limit = res.limit
    requestRes.boardRes.isTenantUser = res.isTenantUser
    requestRes.boardRes.referFlag = res.referFlag
    requestRes.boardRes.platformFlag = res.platformFlag
    this.$set(requestRes.boardRes, 'platformFlag', res.platformFlag)
    // 存储静态资源替换 url 所需信息
    Vue.prototype.assetsInfo = {
      imgLeadingPath: res.imgLeadingPath,
      imgRegularRule: res.imgRegularRule,
    }

    if (isLink) {
      initBoardResHelper({
        callback,
        requestRes,
        execute: true
      })
      return
    }

    initApiCall.call(this, requestRes, callback)

    // sbi 收藏按钮设置
    if (this.utils.env?.projectName === ALL_PROJECT_NAME.SBI) {
      this.$set(requestRes.boardRes, 'hasFav', res.hasFav)
    }
    this.setLevelData(res.metaDashboardDetailVO.busiUserVo)
    let { userLevel = '', roleList = [], currencyUnitFlag = '0' } = res.metaDashboardDetailVO.busiUserVo || {}
    let adminLevel = roleList.some(item => item.level === 0)
    // 获取看板偏好设置
    let isTenantAdmin = userLevel === '2' && adminLevel
    // 报表单位简写
    if (!isSuperLink && typeof this.modalDatas?.modalIsCurrencyAbbr !== 'boolean') {
      this.isCurrencyAbbr = this.isCurrencyAbbr || currencyUnitFlag === '1'
    }
    const params = {
      dashboardId: boardId,
      tenantId: this.utils.tenantId
    }
    if (this.utils.isMobile) {
      requestRes.boardExportFlag = '0'
    } else {
      // 获取看板是否有导出权限
      let apiUrl = this.utils.env?.projectName === ALL_PROJECT_NAME.SBI ? 'getSbiBoardExport' : 'getBoardExportFlag'
      api[apiUrl](this.api, params).then(res => {
        // this.setTenantData(res)
        requestRes.boardExportFlag = res
        initBoardResHelper({
          callback,
          requestRes
        })
      }).catch(err => {
        errCallback && errCallback()
        console.log('获取看板是否有导出权限' + JSON.stringify(err))
        this.setFullScreenLoading()
      })
    }

    if (isTenantAdmin && previewType === PREVIEW_STATUS.TENANT) {
      getTenantBoardParams(this.api, boardId).then(res => {
        requestRes.runtimeRes = res
        initBoardResHelper({
          callback,
          requestRes
        })
      }).catch(err => {
        errCallback && errCallback()
        console.log('2' + JSON.stringify(err))
        this.setFullScreenLoading()
      })
    } else {
      requestRes.runtimeRes = res.metaDashboardDetailVO.tableParamsVo
      initBoardResHelper({
        callback,
        requestRes
      })
    }

    isOpenMethod ? (requestRes.lang = {}) : this.getEnterpriseLang().then(() => {
      requestRes.lang = {}
      initBoardResHelper({
        callback,
        requestRes
      })
    })
  }).catch(err => {
    console.log('1' + JSON.stringify(err))
    errCallback && errCallback()
    this.setFullScreenLoading()
  })
}

function initBoardResHelper(params) {
  const {
    execute,
    callback,
    requestRes
  } = params
  const requestMethods = ['boardRes', 'runtimeRes', 'boardExportFlag', 'lang', 'initApiCall']
  const res = requestRes
  let finish = true
  requestMethods.forEach(name => {
    if (res[name] === null) {
      finish = false
    }
  })
  if (finish || execute) {
    requestFinishHandler({
      callback,
      requestRes
    })
  }
}

function requestFinishHandler(params) {
  const {
    callback,
    requestRes
  } = params
  const boardRes = requestRes.boardRes
  const {
    content,
    contentJson,
  } = boardRes
  const contentObject = content || contentJson
  const boardContObj = typeof contentObject === 'string' ? JSON.parse(contentObject) : contentObject

  boardContObj.elList.forEach((item) => {
    if (item.prveStyle) {
      Object.assign(item.style, item.prveStyle)
      delete item.prveStyle
    }
    return item
  })
  // 需要用到的数据通过该对象返回出去
  // 处理偏好设置后台的数据
  // boardRes.content = boardContObj
  boardRes.content = transGroup(boardContObj)
  callback(Object.assign({}, requestRes))
  // requestRes = null // 释放内存
}
function transGroup(obj) {
  obj.paramsPanelList.forEach(item => {
    item.content.forEach(params => {
      if (params.type === TYPE_PARAM_ELEMENT.GROUP_BY || params.type === TYPE_PARAM_ELEMENT.BREAKDOWN || params.type === TYPE_PARAM_ELEMENT.GROUP) {
        var open
        var bindTableElements
        if (params.type === TYPE_PARAM_ELEMENT.GROUP) {
          open = params.content.groupOptions.open
          bindTableElements = params.content.groupOptions.bindTableElements
        } else {
          open = params.content.open
          bindTableElements = params.content.bindTableElements
        }
        if (open && bindTableElements.length) {
          setTableHierarchy(bindTableElements, obj.elList)
        }
        if (params.type === TYPE_PARAM_ELEMENT.GROUP) {
          params.content.groupOptions.open = undefined
        } else {
          params.content.open = undefined
        }
      }
    })
  })
  return obj
}
function setTableHierarchy (list, elList) {
  list.forEach(item => {
    var table = elList.find(el => el.id === item && el.type === 'table')
    if (table && !table.content.tableDefaultConfig.hierarchy) {
      table.content.tableDefaultConfig.hierarchy = 'all'
    }
  })
}
