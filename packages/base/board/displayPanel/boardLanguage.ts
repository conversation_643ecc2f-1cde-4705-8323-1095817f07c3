import { TYPE_PARAM_ELEMENT } from 'packages/base/board/displayPanel/params/utils/constants'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import { WARNLINE_TYPE_CONNECT_STR, TAGNEWCARD } from 'packages/assets/constant'
import { DIMENSION_VALUE_INDICATOR_WARNING_CHART, HAS_TARGET_CHART, EXTEND_DIMENSION_CHART, DIMENSION_WARNING_CHART, WARNING_LINE_CHART, CHART_ALIAS_TYPE } from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/constant'
import { ALL_PROJECT_NAME } from '../../../components/mixins/commonMixin'
import Field from 'packages/base/board/displayPanel/supernatant/boardElements/elementChart/userConfig/modules/field'
import UserConfig from './supernatant/boardElements/elementChart/userConfig'
import {decompositionSetting} from "./supernatant/boardElements/elementChart/chartsList/chartConfig";
export { TYPE_SUPERSETTING } from 'packages/base/board/displayPanel/constants'


// 组件类型分组
let vm
export const TYPE_ELEMENT_GROUP = {
  CHART: 'chart',
  IMAGE: 'image',
  TEXT: 'text',
  TIME: 'time',
  WEB: 'web',
  SCROLL_TEXT: 'scroll-text',
  TABLE: 'table',
  PARAM: 'param',
  SPEC: 'spec',
  CONTAINER: 'container'
}

// 图表容器的弹窗类型
export const TYPE_CHART_CONTAINER_DIALOG = {
  setting: 'SETTING',
  preview: 'PREVIEW',
}

// 标记字段
export const ELEMENTS_TAG = {
  NEWTABLE: 'newTable',
  REFERENCETABLE: 'referenceTable',
}

export const TYPE_INTERACTION = {
  INTERACTION_STATE: 'interactionState',
  INTERACTION_SETTING: 'interactionSetting'
}

// export const TYPE_SUPERSETTING = {
//   CLOSE_SKIP: 'closeSkip', // 清空超链接
//   SKIP: 'skip', // 超链接标识
//   GO_FORWARD: 'goForward', // 超链接前进
//   RETREAT: 'retreat', // 超链接返回
//   INIT: 'init', // 初始化
//   PLAN_RASK: 'planTask', // 计划任务
//   SUBSCRIBE: 'subscribe', // 订阅提醒
// }

// 数据看板操作日志对象
export const OPERATION_LOG = {
  // 渠道（PC、SUB、APP）。注：SUB（订阅提醒，包括PC、APP）
  channel: 'PC',
  // 切换租户得到的企业ID
  tenantId: '',
  // 本地浏览器时间 2018-11-23 00:00:00
  updateDateLocal: '',
  // 二级菜单国际码
  menuI18Key: 'BI_L01',
  // 一级菜单国际码
  modelI18Key: 'OMSCD01BBSJ',
  // tenantId: localStorage.getItem('shiji'),
  // 日志类型：1登录日志，2操作日志
  logType: '2',
  // 操作类型1新增，2修改，3删除
  operateType: '',
  // 操作页面ID
  operatePageId: '',
  // 文件夹ID
  superNodeName: '',
  // 本节点ID
  objectId: '',
  // 本节点名称
  objectName: '',
  // 操作内容（前后对比的JSON串）
  operateContent: '',
}
interface ObjectInterface {
  [key: string]: any;
}
function _checkPreviewChart(each, arr, titleKey, contentKey) {
  // 只替换获取多语言的几个配置，剩余通过chartDataReplaceLang替换
  let chartLangObj = []
  let chioceTab = each.content.chioceTab
  if (chioceTab && chioceTab.length) {
    let params = {
      contentKey,
      index: each.content.saveIndex,
      chartLangObj
    }
    _getChartLangObj(each, arr, params)
    // 先注释掉替换指标选择器的数据，有bug
    chioceTab.forEach((v, i) => {
      // let obj = JSON.parse(JSON.stringify(each))
      // obj.content = v.saveObj
      // let params = {
      //   contentKey,
      //   index: i,
      //   saveIndex: each.content.saveIndex,
      //   chartLangObj
      // }
      // _getChartLangObj(obj, arr, params)
      const labelObj = arr.filter(item => item[contentKey] === `${each.id}_${v.id}_indicatrixAlias`)[0]
      v.name = labelObj ? (labelObj.value || v.name) : v.name

      const labelObj1 = arr.filter(item => item[contentKey] === `${each.id}_${i}_chartTitle`)[0]
      const chartUserConfig = v.saveObj.chartUserConfig
      if (chartUserConfig.title?.text) {
        chartUserConfig.title.text = labelObj1 ? (labelObj1.value || chartUserConfig.title.text) : chartUserConfig.title.text
      }
    })
  } else {
    let params = {
      contentKey,
      index: 'x',
      chartLangObj
    }
    _getChartLangObj(each, arr, params)
  }
  return chartLangObj
  // vm.chartLangObj.push(...chartLangObj)
}

function _replaceCombineCard(el, arr, content, contentKey) {
  let cardList = content.cardList
  cardList.length && cardList.forEach((e, i) => {
    const optionArray = e.content.optionArray
    optionArray.length && optionArray.forEach((v, i) => {
      const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${e.id}_${i}_cardTitle`)[0]
      v.cardName = labelObj ? (labelObj.value || v.cardName) : v.cardName
    })
  })
}

function _replaceCommonChartContent(content, params, fieldArr = false) {
  if (!content) return
  let { arr, id, isDul, saveIndex } = params
  Array.isArray(content) &&
  content.forEach((item, index) => {
    // 退出预览还原多语言时，content为多语言值，由于多语言值可以重复，还原时会存在问题
    const originalName = Array.isArray(fieldArr) ? fieldArr[index].alias || fieldArr[index].labeName : item
    arr.some(v => {
      if (isDul) {
        if (
          saveIndex === v.index &&
          v.id === id &&
          originalName === v.key &&
          v.value !== ''
        ) {
          vm.$set(content, index, v.value)
          return true
        }
      } else {
        if (v.id === id && originalName === v.key && v.value !== '') {
          vm.$set(content, index, v.value)
          return true
        }
      }
    })
  })
}

function _replaceScatterContent(content, params) {
  let chartDataRows = content.chartData.rows
  if (!content) return
  if (content.alias === 've-scatter-normal') {
    Object.keys(chartDataRows).forEach(key => {
      let obj = chartDataRows[key]
      _replaceRows(obj, params)
    })
  } else {
    Array.isArray(chartDataRows) &&
    _replaceRows(chartDataRows, params)

    if (content.alias === 've-grid-normal') return

    // 替换拓展维度
    chartDataRows.forEach((row) => {
      if (row.extendData && Array.isArray(row.extendData)) {
        _replaceRows(row.extendData, params)
      }
    })
  }

}

function _replaceRows(obj, params) {
  let { arr, id, isDul, saveIndex } = params
  obj.forEach(chartItem => {
    arr.forEach(langObj => {
      Object.keys(chartItem).forEach(key => {
        if (isDul && langObj.index !== undefined) {
          const specailIndex = langObj.index.split('$ms$')[0]
          if (
            saveIndex === specailIndex &&
            langObj.id === id &&
            key === langObj.key &&
            langObj.value !== ''
          ) {
            chartItem[langObj.value] = chartItem[key]
          }
          if (
            saveIndex === specailIndex &&
            langObj.id === id &&
            key === `VIEWFORMAT_${langObj.key}` &&
            langObj.value !== ''
          ) {
            chartItem[`VIEWFORMAT_${langObj.value}`] = chartItem[key]
          }
        } else {
          if (langObj.id === id && key === langObj.key && langObj.value !== '') {
            chartItem[langObj.value] = chartItem[key]
          }
          if (langObj.id === id && key === `VIEWFORMAT_${langObj.key}` && langObj.value !== '') {
            chartItem[`VIEWFORMAT_${langObj.value}`] = chartItem[key]
          }
        }
      })
    })
  })
}

function _getLangObj(labelObj, chartLangObj) {
  let name = labelObj && labelObj.name
  if (name) {
    let obj = {
      id: name.split('|')[0],
      index: name.split('|')[1],
      key: name.split('|')[2],
      value: labelObj.aliasName || labelObj.value,
      name: name
    }
    chartLangObj.push(obj)
  }
}

function _getChartLangObj(each, arr, params) {
  let chartUserConfig = each.content.chartUserConfig
  const { warnLineSettingList = [], warningMethod = {}, chartAlias, dimensionValueIndicatorWarningList = [], scatterSetting, measureConfig = {}, decompositionSetting = {} } = chartUserConfig
  let { contentKey, index, chartLangObj } = params
  const labelObj1 = arr.filter(item => item[contentKey] === `${each.id}_${index}_chartTitle`)[0]
  const labelObj2 = arr.filter(item => item[contentKey] === `${each.id}_${index}_chartDescription`)[0]
  if (chartUserConfig.title?.text) {
    chartUserConfig.title.text = labelObj1 ? (labelObj1.value || chartUserConfig.title.text) : chartUserConfig.title.text
  }
  if (chartUserConfig.chartDescription) {
    chartUserConfig.chartDescription = labelObj2 ? (labelObj2.value || chartUserConfig.chartDescription) : chartUserConfig.chartDescription
  }
  if (each.content.alias === 've-scatter-normal') {
    each.content.chartData.columns.forEach((v, i) => {
      const labelObj = arr.find(item => item[contentKey] === `${each.id}_${i}_${index}_columnsAlias`)
      vm.$set(each.content.chartData.columns, i, labelObj ? (labelObj.aliasName || labelObj.value || v) : v)
      _getLangObj(labelObj, chartLangObj)
    })
    if (scatterSetting?.quadrantConfig?.length) {
      scatterSetting.quadrantConfig.forEach((quadrantItem, quadrantIndex) => {
        if (quadrantItem.areaRemark) {
          const labelObj = arr.find(item => item[contentKey] === `${each.id}_${quadrantIndex}_${index}_areaRemark`)
          vm.$set(quadrantItem, 'lang_alias', labelObj ? (labelObj.value || quadrantItem.areaRemark) : quadrantItem.areaRemark)
          _getLangObj(labelObj, chartLangObj)
        }
      })
    }
  } else {
    // 维度
    setChartListSettingsLang({ vm, params, each, arr, chartArr: each.content.chartSettings.dimension, key: 'dimensionAlias', keyStr: 'content.chartUserConfig.dimensionList' })
    // 度量
    setChartListSettingsLang({ vm, params, each, arr, chartArr: each.content.chartSettings.metrics, key: 'metricsAlias', keyStr: 'content.chartUserConfig.metricsContainer.default' })
    setChartListSettingsLang({ vm, params, each, arr, chartArr: each.content.chartSettings.customMetric, key: 'customMetricAlias', keyStr: 'content.chartUserConfig.metricLabelDisplay' })
    // 预警
    setChartListSettingsLang({ vm, params, each, arr, chartArr: chartUserConfig.warnLineSettingList, key: 'warningAlias', keyStr: 'content.chartUserConfig.warnLineSettingList', valKey: 'alias' })
    // hover维度、度量
    setChartListSettingsLang({ vm, params, each, arr, chartArr: chartUserConfig.hoverDimensionList, key: 'hoverDimensionAlias', keyStr: 'content.chartUserConfig.hoverDimensionList', valKey: 'alias' })
    setChartListSettingsLang({ vm, params, each, arr, chartArr: chartUserConfig.hoverMetricList, key: 'hoverMetricsAlias', keyStr: 'content.chartUserConfig.hoverMetricList', valKey: 'alias' })
    // 地图经纬度
    // todo kyz longitudeList
    setChartListSettingsLang({ vm, params, each, arr, chartArr: chartUserConfig.longitudeList, key: 'longitudeAlias', keyStr: 'content.chartUserConfig.longitudeList', valKey: 'alias' })
    setChartListSettingsLang({ vm, params, each, arr, chartArr: chartUserConfig.latitudeList, key: 'latitudeAlias', keyStr: 'content.chartUserConfig.latitudeList', valKey: 'alias' })

    // each.content.chartSettings.dimension.forEach((v, i) => {
    //   // const labelObj = arr.filter(item => item[contentKey] === `${each.id}_${i}_${index}_dimensionAlias`)[0]
    //   // if (labelObj && labelObj.name !== `${each.id}|${index}|${v}`) return
    //   const labelObj = arr.filter(item => {
    //     let keyName = item.name && item.name.split('|')[2]
    //     // 维度修改别名需要清空多语言
    //     if (keyName) {
    //       let dimensionObj = vm.$_getProp(each, 'content.chartUserConfig.dimensionList', [])
    //       let alias = dimensionObj[i]?.alias
    //       return item[contentKey] === `${each.id}_${i}_${index}_dimensionAlias` && alias === keyName
    //     }
    //     return item[contentKey] === `${each.id}_${i}_${index}_dimensionAlias`
    //   })[0]
    //   vm.$set(each.content.chartSettings.dimension, i, labelObj ? (labelObj.value || v) : v)
    //   _getLangObj(labelObj, chartLangObj)
    // })
    // each.content.chartSettings.metrics.forEach((v, i) => {
    //   const labelObj = arr.filter(item => {
    //     let keyName = item.name && item.name.split('|')[2]
    //     // 度量修改别名需要清空多语言
    //     if (keyName) {
    //       let metricsObj = vm.$_getProp(each, 'content.chartUserConfig.metricsContainer.default', [])
    //       let alias = metricsObj[i] && metricsObj[i].alias
    //       return item[contentKey] === `${each.id}_${i}_${index}_metricsAlias` && alias === keyName
    //     }
    //     return item[contentKey] === `${each.id}_${i}_${index}_metricsAlias`
    //   })[0]
    //   vm.$set(each.content.chartSettings.metrics, i, labelObj ? (labelObj.value || v) : v)
    //   _getLangObj(labelObj, chartLangObj)
    // })
    // 目标值多语言
    // if (['ve-bar-percent', 've-ring-multiple'].includes(each.content.alias)) {
    const target = each.content.chartSettings.target
    setChartListSettingsLang({ vm, params, each, arr, chartArr: target, key: 'targetAlias', keyStr: 'content.chartUserConfig.gaugeTarget.defaults'})
      // Array.isArray(target) && target.forEach((v, i) => {
      //   const labelObj = arr.filter(item => {
      //     let keyName = item.name && item.name.split('|')[2]
      //     // 目标值修改别名需要清空多语言
      //     if (keyName) {
      //       let gaugeTarget = vm.$_getProp(each, 'content.chartUserConfig.gaugeTarget.defaults', {})
      //       let alias = gaugeTarget.alias
      //       return item[contentKey] === `${each.id}_${i}_${index}_targetAlias` && alias === keyName
      //     }
      //     return item[contentKey] === `${each.id}_${i}_${index}_targetAlias`
      //   })[0]
      //   vm.$set(each.content.chartSettings.target, i, labelObj ? (labelObj.value || v) : v)
      //   _getLangObj(labelObj, chartLangObj)
      // })
    // }
    // 主维度度量
    if(chartUserConfig?.mainDimensionMeasureConfig?.enable) {
      const isComposite = chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE
      let _mainDimensionMeasureArr: any
      let keyStr: string
      if(isComposite) {
        _mainDimensionMeasureArr = chartUserConfig.mainDimensionMeasureConfig?.compositeTypes?.filter(c=> c.legendName?.length && c.enable)
        keyStr= 'content.chartUserConfig.mainDimensionMeasureConfig.compositeTypes'
      } else {
        _mainDimensionMeasureArr = [chartUserConfig.mainDimensionMeasureConfig]
        keyStr= 'content.chartUserConfig.mainDimensionMeasureConfig'
      }
      setChartListSettingsLang({ vm, params, each, arr, chartArr:_mainDimensionMeasureArr, key: 'mainDimensionMeasureAlias', keyStr: 'content.chartUserConfig.mainDimensionMeasureConfig.compositeTypes', valKey: 'legendName' })
    }
  }
  // 瀑布图累计值、对比值替换
  if (each.content.alias === 've-waterfall') {
    const accumulativeNameObj = arr.filter(item => item[contentKey] === `${each.id}_${index}_accumulativeName`)[0]
    let accumulative = vm.$_getProp(each, 'content.chartUserConfig.waterfallSetting.accumulative', '')
    accumulative && accumulative.name && vm.$set(accumulative, 'name', accumulativeNameObj?.value || accumulative.name)
    // 图例名称
    let legendName = each.content.chartUserConfig.waterfallSetting?.legendName || {}
    Object.keys(legendName).forEach((key, i) => {
      const legendObj = arr.filter(item => item[contentKey] === `${each.id}_${index}_${i}_legendName`)[0]
      vm.$set(legendName, key, legendObj?.value || legendName[key])
    })
    // 对比值
    let contrastName = each.content.chartUserConfig.waterfallSetting?.contrast?.name
    const contrastObj = arr.filter(item => item[contentKey] === `${each.id}_${index}_contrastName`)[0]
    contrastName && vm.$set(each.content.chartUserConfig.waterfallSetting?.contrast, 'name', contrastObj?.value || contrastName)
  }
  // 水滴图标题替换
  if (each.content.alias === 've-liquidfill') {
    const liquidFillTitleObj = arr.filter(item => item[contentKey] === `${each.id}_${index}_liquidFillTitle`)[0]
    let liquidFillSetting = vm.$_getProp(each, 'content.chartUserConfig.liquidFillSetting', {})
    liquidFillSetting && liquidFillSetting.title && vm.$set(liquidFillSetting, 'title', liquidFillTitleObj ? (liquidFillTitleObj.value || liquidFillSetting.title) : liquidFillSetting.title)
  }
  // 漏斗图转换率替换
  if (each.content.alias === 've-funnel') {
    const obj = arr.filter(item => item[contentKey] === `${each.id}_${index}_converesionRate`)[0]
    let funnelSettings = vm.$_getProp(each, 'content.chartUserConfig.funnelSettings', {})
    funnelSettings.converesionRateDescribe && vm.$set(funnelSettings, 'converesionRateDescribe', obj && obj.value ? obj.value : funnelSettings.converesionRateDescribe)
  }
  // 树图节点标题替换
  if (each.content.alias === 've-tree') {
    const obj = arr.filter(item => item[contentKey] === `${each.id}_${index}_treeBaseName`)[0]
    let treeSetting = vm.$_getProp(each, 'content.chartUserConfig.treeSetting', {})
    treeSetting.baseName && vm.$set(treeSetting, 'baseName', obj && obj.value ? obj.value : treeSetting.baseName)
  }
  // 仪表盘
  if (each.content.alias === 've-gauge-normal') {
    let prefix = `${each.id}_${index}_`
    const keys = [`${ prefix }gaugeTitle`]
    let gaugeLangObj: ObjectInterface = {}
    arr.filter(item => keys.includes(item[contentKey])).forEach(item => gaugeLangObj[item[contentKey].replace(prefix, '')] = item)
    let settingGauge = vm.$_getProp(each, 'content.chartUserConfig.gaugeTarget.settingGauge', {})
    if (settingGauge) {
      vm.$set(settingGauge, 'title', gaugeLangObj.gaugeTitle?.value || settingGauge.title)
    }
  }
  if (each.content.alias === 've-gauge-normal' || each.content.alias === 've-liquidfill') {
    let prefix = `${each.id}_${index}_`
    const keys = [`${ prefix }gaugeIndicatorLabel`, `${ prefix }gaugeTargetLabel`]
    let gaugeLangObj: ObjectInterface = {}
    arr.filter(item => keys.includes(item[contentKey])).forEach(item => gaugeLangObj[item[contentKey].replace(prefix, '')] = item)
    let settingGauge = vm.$_getProp(each, 'content.chartUserConfig.gaugeTarget.settingGauge', {})
    if (settingGauge) {
      if (settingGauge.dataDisplay && settingGauge.dataDisplay.indicatorLabel && settingGauge.dataDisplay.showIndicator) {
        vm.$set(settingGauge.dataDisplay, 'lang_indicatorLabel', gaugeLangObj.gaugeIndicatorLabel?.value || settingGauge.dataDisplay.indicatorLabel)
      }
      if (settingGauge.dataDisplay && settingGauge.dataDisplay.targetLabel && settingGauge.dataDisplay.showTarget) {
        vm.$set(settingGauge.dataDisplay, 'lang_targetLabel', gaugeLangObj.gaugeTargetLabel?.value || settingGauge.dataDisplay.targetLabel)
      }
    }
  }
  // 预警线替换
  let warnLine = [...warnLineSettingList]
  if (warningMethod.dimensionMethod === 4 && DIMENSION_VALUE_INDICATOR_WARNING_CHART.includes(chartAlias) && dimensionValueIndicatorWarningList?.length) {
    warnLine.push(...dimensionValueIndicatorWarningList)
    setChartListSettingsLang({ vm, params, each, arr, chartArr: dimensionValueIndicatorWarningList.filter(ds => ds.fieldType === 'other').map(ds => ({ alias: ds.matchField.alias, ...ds })), key: 'warningAlias', keyStr: 'content.chartUserConfig.dimensionValueIndicatorWarningList.$index.matchField', valKey: 'alias' })
  }
  warnLine.forEach((v, i) => {
    const labelObj = arr.find(item => item[contentKey] === `${each.id}_${v.id}`)
    // const isDimensionWarn = vm.$_getProp(each, 'content.chartUserConfig.compareMethod') === 1
    vm.$set(v, 'name', labelObj ? (labelObj.value || v.name) : v.name)
    // if (!isDimensionWarn || v.fieldType === 'fixed' || v.fieldType === 'other') {
    //   vm.$set(v, 'name', labelObj ? (labelObj.value || v.name) : v.name)
    // }
  })
  // 分解树 占比
  if (each.content.alias === CHART_ALIAS_TYPE.VE_DECOMPOSITION) {
    const lanId = `${each.id}_${index}_decomposition_percent`
    const labelObj = arr.find(item => item[contentKey] === lanId)

    vm.$set(decompositionSetting, 'showPercentLabel', labelObj ? (labelObj.value || defaultLabel) : '')
  }
  // 度量汇总替换
  if (measureConfig) {
    const { measureSummaryValue, measureSummaryAlias, summaryList } = measureConfig
    if (summaryList && Array.isArray(summaryList)) {
      summaryList.forEach((item, idx) => {
        const lanId = `${each.id}_${index}_${idx}_summaryAlias`
        const labelObj = arr.find(item => item[contentKey] === lanId)
        const defaultLabel = item.summaryAlias

        vm.$set(item, 'summaryAlias', labelObj ? (labelObj.value || defaultLabel) : defaultLabel)
      })
    } else {
      if (measureSummaryAlias && measureSummaryValue) {
        const lanId = `${each.id}_${index}_measureSummaryAlias`
        const labelObj = arr.find(item => item[contentKey] === lanId)
        const defaultLabel = measureConfig.measureSummaryAlias

        vm.$set(measureConfig, 'measureSummaryAlias', labelObj ? (labelObj.value || defaultLabel) : defaultLabel)
      }
    }
  }

  // 钻取别名
  if (each.content.alias === CHART_ALIAS_TYPE.VE_GRID && each.content?.drillList?.length) {
    each.content.drillList.forEach((item, idx) => {
      if (item.drillId) {
        const lanId = `${each.id}_${index}_${item.drillId}_drillAlias`
        const labelObj = arr.find(item => item[contentKey] === lanId)
        item.alias && vm.$set(item, 'alias', labelObj ? (labelObj.value || item.alias) : item.alias)
      }
    })
  }

  // 指标维度预警替换
  let dimensionWarn = vm.$_getProp(each, 'content.chartUserConfig.dimensionWarningList', [])
  dimensionWarn.forEach((d, i) => {
    d.warningList.forEach(w => {
      const labelObj = arr.filter(item => item[contentKey] === `${each.id}_${w.id}`)[0]
      vm.$set(w, 'name', labelObj?.value || w.name)
    })
  })
  // 辅助线多语言替换
  let xLine = vm.$_getProp(each, 'content.chartUserConfig.xAuxiliaryLineData', [])
  let yLine = vm.$_getProp(each, 'content.chartUserConfig.yAuxiliaryLineData', [])
  xLine.forEach((v, i) => {
    const labelObj = arr.filter(item => item[contentKey] === v.id)[0]
    vm.$set(v, 'name', labelObj ? (labelObj.value || v.name) : v.name)
  })
  yLine.forEach(v => {
    const labelObj = arr.filter(item => item[contentKey] === v.id)[0]
    vm.$set(v, 'name', labelObj ? (labelObj.value || v.name) : v.name)
  })
  // XY轴
  let xAxis = vm.$_getProp(each, 'content.chartUserConfig.xAxisSetting', {})
  let yAxis = vm.$_getProp(each, 'content.chartUserConfig.yAxisSetting', [])
  if (xAxis.chartAxisTitle) {
    const xAxisObj = arr.filter(item => item[contentKey] === `${each.id}_${index}_xAxis`)[0]
    vm.$set(xAxis, 'chartAxisTitle', xAxisObj ? (xAxisObj.value || xAxis.chartAxisTitle) : xAxis.chartAxisTitle)
  }
  yAxis.forEach((v, i) => {
    if (v && v.chartAxisTitle) {
      const yAxisObj = arr.filter(item => item[contentKey] === `${each.id}_${index}_${i}_yAxis`)[0]
      vm.$set(v, 'chartAxisTitle', yAxisObj ? (yAxisObj.value || v.chartAxisTitle) : v.chartAxisTitle)
    }
  })
}

// 获取卡片预警多语言
function _getCardWarningLine(el, combineCardEl?) {
  const { optionArray, chartUserConfig: config, elementType } = el.content
  let children = []
  let combineCardStr = combineCardEl ? combineCardEl.id + '_' : '' // 兼容组合卡片标题多语言数据
  optionArray.length && optionArray.forEach((e, i) => {
    elementType !== 'dupontIndicator' && e.cardName && children.push({ id: `${combineCardStr}${el.id}_${i}_cardTitle`, value: e.cardName })
    elementType !== 'dupontIndicator' && e.subCardName && children.push({ id: `${combineCardStr}${el.id}_${i}_subCardTitle`, value: e.subCardName })
    const warningList = config.warnLineSettingList ? config.warnLineSettingList[i]?.filter(v => v.allowSubscribe) : []
    warningList.forEach(item => {
      const cardWarnLineTypeArr = item.cardWarnLineType.split(WARNLINE_TYPE_CONNECT_STR)
      const metricItem = e[cardWarnLineTypeArr[0]]?.find(m => (m.alias || m.labeName) === item.metric)
      if (metricItem) {
        children.push({ id: `${el.id}_${i}_${metricItem.keyName}`, value: item.metric })
      }
      item.name && children.push({ id: `${el.id}_${i}_${item.id}`, value: item.name })
      item.alias && children.push({ id: `${el.id}_${i}_${item.id}_${ item.alias }`, value: item.alias })
    })
  })
  return children
}

// 获取组合卡片多语言
function _getCombineCard(each, children) {
  let cardList = each.content.cardList
  cardList.forEach(cardItem => {
    let langArr = _getCardWarningLine(cardItem, each)
    children.push(...langArr)
  })
  return children
}

export function _getCardElementLang(paramsObj) {
  let children = []
  const { el, languageList } = paramsObj
  const id = el.langId || el.id
  const { optionArray, tagNewCardContent } = el.content
  const config = el.content.chartUserConfig || {}
  if ([TAGNEWCARD.RATECARD].includes(tagNewCardContent)) {
    _generateLangObj.call(this, config, 'ratioUnit', children, id)
    _generateLangObj.call(this, config, 'referenceValueList', children, id)
    _generateLangObj.call(this, config, 'dimension', children, id, optionArray[0]?.dimension)
  }
  if ([TAGNEWCARD.RATECARD, TAGNEWCARD.COMPARECARD].includes(tagNewCardContent)) {
    _generateLangObj.call(this, config, 'compareValue', children, id, optionArray[0]?.compareValue)
  }
  _generateLangObj.call(this, config, 'remark', children, id)
  if(tagNewCardContent=== TAGNEWCARD.TWOINDICES){ // 双指标卡片少一个备注
    _generateLangObj.call(this, config, 'remarkTwo', children, id)
  }
  _generateLangObj.call(this, config, 'dimensionUnit', children, id)
  _generateLangObj.call(this, config, 'growthCompareText', children, id)
  _generateLangObj.call(this, config, 'completionText', children, id)

  let warningLangArr = _getCardWarningLine(el)
  children.push(...warningLangArr)

  if (el.content.superLinkOptions && el.content.superLinkOptions.length > 0) {
    let superLinkOptions = el.content.superLinkOptions
    superLinkOptions.forEach((e, i) => {
      e.style.name && children.push({ id: `${id}_${e.soleId}_${i}_linkTitle`, value: e.style.name })
    })
  }
  el.elName && languageList && languageList.children.push({ id: `${id}`, value: el.elName, children: children })
  return children
}

// 获取图形多语言（添加name属性）
function _getMoreLangChart(each, children, index, params) {
  const { title = {}, chartDescription, hoverDimensionList = [], hoverMetricList = [], longitudeList = [], latitudeList = [], metricLabelDisplay = [], warnLineSettingList = [], dimensionValueIndicatorWarningList = [], warningMethod = {}, chartAlias, gaugeTarget = {}, mapMode, scatterSetting, mainDimensionMeasureConfig = {}, measureConfig = {} } = each.content.chartUserConfig
  const { dimensionList = [], metricAllList = [], dimensionExtendList = []} = each.vm?.UserConfig || params?.UserConfig || new UserConfig(each, each.vm)
  const {compositeTypes = []} = mainDimensionMeasureConfig
  !/\$ms\$/g.test(index) && title.text && children.push({ id: `${each.id}_${index}_chartTitle`, parentId: each.id, value: title.text })
  let isHideDesc = !this.isMobile && !this.utils.isDataReport && !this.utils.isLargeScreen
  if(isHideDesc){
    children.push({ id: `${each.id}_${index}_chartDescription`, parentId: each.id, value: chartDescription||'' ,'isHideDesc': true })
  }else{
    chartDescription && children.push({ id: `${each.id}_${index}_chartDescription`, parentId: each.id, value: chartDescription||'' })
  }
  // 多语言设置不需要汇总的图形
  const noMeasureConfigArr = ['ve-bar-Heatmap']

  if (chartAlias === 've-scatter-normal') {
    // 散点图做特殊处理
    const fieldArray = dimensionList.concat(metricAllList)
    pushChildren('columnsAlias', generateNames.call(this, fieldArray))
    if (scatterSetting?.quadrantConfig?.length) {
      scatterSetting.quadrantConfig.forEach((quadrantItem, quadrantIndex) => {
        if (quadrantItem.areaRemark) {
          children.push({ id: `${each.id}_${quadrantIndex}_${index}_areaRemark`, parentId: each.id, value: quadrantItem.areaRemark })
        }
      })
    }
  } else {
    // 维度
    pushChildren('dimensionAlias', generateNames.call(this, dimensionList))
    if (EXTEND_DIMENSION_CHART.includes(chartAlias) && dimensionExtendList.length) {
      if(dimensionValueIndicatorWarningList.length) {
        let _dimensionExtendList = dimensionExtendList.map((e, i) => ({ ...e, _fieldIndex: i })).filter(e => dimensionValueIndicatorWarningList.find(d => d.dimensionSetting.find(ds => ds.fieldKeyName === e.keyName)))
        pushChildren('extendDimensionAlias', generateNames.call(this, _dimensionExtendList))
      }
      // 主维度度量
      if(mainDimensionMeasureConfig?.enable) {
        const isComposite = chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE
        if(isComposite) {
          (compositeTypes || []).forEach((com,i) => {
            if(com?.legendName && com.enable) {
              children.push({id:`${each.id}_${i}_${index}_mainDimensionMeasureAlias`, parentId: each.id, name: `${each.id}|${index}|${com.legendName}`,aliasName: com.legendName, value: com.legendName})
            }
          })
        } else if(mainDimensionMeasureConfig?.legendName){
          children.push({id:`${each.id}_${0}_${index}_mainDimensionMeasureAlias`, parentId: each.id, name: `${each.id}|${index}|${mainDimensionMeasureConfig.legendName}`,aliasName: mainDimensionMeasureConfig.legendName, value: mainDimensionMeasureConfig.legendName})
        }
      }
    }

    // 度量
    pushChildren('metricsAlias', generateNames.call(this, metricAllList))

    ;(metricAllList || []).forEach((item, i) => {
      // 自定义度量
      const currentMetricLabel = metricLabelDisplay.find(ml => ml.keyName === item.keyName)
      if (currentMetricLabel && currentMetricLabel.labelType === 'custom') {
        currentMetricLabel.customMetricList?.length && currentMetricLabel.customMetricList.forEach((val) => {
          children.push({ id: `${each.id}_${index}_customMetricAlias_${val.customMetricAlias}`, parentId: each.id, name: `${each.id}|${index}|${val.customMetricAlias}`, value: val.customMetricAlias })
        })
      }
    })
    // hover维度和度量
    pushChildren('hoverDimensionAlias', generateNames.call(this, hoverDimensionList))
    pushChildren('hoverMetricsAlias', generateNames.call(this, hoverMetricList))

    // todo kyz longitudeList
    // 经纬度
    if (mapMode === 'longitudeAndLatitude') {
      pushChildren('longitudeAlias', generateNames.call(this, longitudeList))
      pushChildren('latitudeAlias', generateNames.call(this, latitudeList))
    }

    // 目标值
    if (gaugeTarget.type === 'aggType' && gaugeTarget.defaults && HAS_TARGET_CHART.includes(chartAlias)) {
      pushChildren('targetAlias', generateNames.call(this, [gaugeTarget.defaults]))
    }
  }
  if (chartAlias === 've-waterfall') {
    // 瀑布图累计值、对比值、图例名称
    let accumulativeName = this.$_getProp(each, 'content.chartUserConfig.waterfallSetting.accumulative.name', '')
    accumulativeName && children.push({ id: `${each.id}_${index}_accumulativeName`, parentId: each.id, value: accumulativeName })
    // 图例名称
    let legendName = each.content.chartUserConfig.waterfallSetting?.legendName || {}
    Object.keys(legendName).forEach((key, i) => {
      children.push({ id: `${each.id}_${index}_${i}_legendName`, parentId: each.id, value: legendName[key] })
    })
    // 对比值
    let contrastName = each.content.chartUserConfig.waterfallSetting?.contrast?.name
    contrastName && children.push({ id: `${each.id}_${index}_contrastName`, parentId: each.id, value: contrastName })
  } else if (chartAlias === 've-liquidfill') {
    // 水滴图标题
    let liquidFillTitle = this.$_getProp(each, 'content.chartUserConfig.liquidFillSetting.title', '')
    liquidFillTitle && children.push({ id: `${each.id}_${index}_liquidFillTitle`, parentId: each.id, value: liquidFillTitle })
  } else if (chartAlias === 've-funnel') {
    // 漏斗图转换率
    let converesionRateDescribe = this.$_getProp(each, 'content.chartUserConfig.funnelSettings.converesionRateDescribe', '')
    converesionRateDescribe && children.push({ id: `${each.id}_${index}_converesionRate`, parentId: each.id, value: converesionRateDescribe })
  } else if (chartAlias === 've-tree') {
    // 树图节点标题
    let baseName = this.$_getProp(each, 'content.chartUserConfig.treeSetting.baseName', '')
    baseName && children.push({ id: `${each.id}_${index}_treeBaseName`, parentId: each.id, value: baseName })
  } else if (chartAlias === 've-gauge-normal') {
    // 仪表盘标题
    let gaugeTitle = this.$_getProp(each, 'content.chartUserConfig.gaugeTarget.settingGauge.title', '')
    gaugeTitle && children.push({ id: `${each.id}_${index}_gaugeTitle`, parentId: each.id, value: gaugeTitle })
  }

  let _liquidFillSetting = this.$_getProp(each, 'content.chartUserConfig.liquidFillSetting', {})
  if (chartAlias === 've-gauge-normal' || (chartAlias === 've-liquidfill' && _liquidFillSetting?.mode === 'normal')) {
    let indicatorLabel = this.$_getProp(each, 'content.chartUserConfig.gaugeTarget.settingGauge.dataDisplay.showIndicator') && this.$_getProp(each, 'content.chartUserConfig.gaugeTarget.settingGauge.dataDisplay.indicatorLabel', '')
    indicatorLabel && children.push({ id: `${each.id}_${index}_gaugeIndicatorLabel`, parentId: each.id, value: indicatorLabel })
    let targetLabel = this.$_getProp(each, 'content.chartUserConfig.gaugeTarget.settingGauge.dataDisplay.showTarget') && this.$_getProp(each, 'content.chartUserConfig.gaugeTarget.settingGauge.dataDisplay.targetLabel', '')
    targetLabel && children.push({ id: `${each.id}_${index}_gaugeTargetLabel`, parentId: each.id, value: targetLabel })
  }

  // 分解树 占比
  if (chartAlias === CHART_ALIAS_TYPE.VE_DECOMPOSITION) {
    children.push({
      id: `${each.id}_${index}_decomposition_percent`,
      parentId: each.id,
      value: this.$t('sdp.views.percent')
    })
  }

  if (measureConfig && !noMeasureConfigArr.includes(chartAlias) ) {
    const { measureSummaryValue, measureSummaryAlias, summaryList } = measureConfig
    if (summaryList && Array.isArray(summaryList)) {
      summaryList.forEach((item, idx) => {
        const { summaryValue, summaryAlias } = item

        if (summaryAlias && summaryValue) {
          children.push({
            id: `${each.id}_${index}_${idx}_summaryAlias`,
            parentId: each.id,
            value: summaryAlias
          })
        }
      })
    } else {
      if (measureSummaryAlias && measureSummaryValue) {
        children.push({
          id: `${each.id}_${index}_measureSummaryAlias`,
          parentId: each.id,
          value: measureSummaryAlias
        })
      }
    }
  }

  // 钻取别名
  if (each.content.alias === CHART_ALIAS_TYPE.VE_GRID && each.content?.drillList?.length) {
    each.content.drillList.forEach((item, idx) => {
      if (item.drillId && item.alias) {
        children.push({
          id: `${each.id}_${index}_${item.drillId}_drillAlias`,
          parentId: each.id,
          value: item.alias
        })
      }
    })
  }

  // 预警线
  if (WARNING_LINE_CHART.includes(chartAlias)) {
    let warnLine = [...warnLineSettingList]
    if (warningMethod.dimensionMethod === 4 && DIMENSION_VALUE_INDICATOR_WARNING_CHART.includes(chartAlias) && dimensionValueIndicatorWarningList?.length) {
      warnLine.push(...dimensionValueIndicatorWarningList)
    }
    warnLine.forEach((item) => {
      children.push({ id: `${each.id}_${item.id}`, parentId: each.id, value: item.name })
      if (item.fieldType !== 'other') return
      const comparedObject = Number(item.compareMethod) === 4 ? item.matchField : item
      const comparedObjectName = generateNames.call(this, [comparedObject])
      const originalName = comparedObjectName.originalName[0]
      const aliasName = comparedObjectName.aliasName[0]
      originalName && children.push({ id: `${each.id}_${ index }_${ item.id }_${ originalName }`, parentId: each.id, name: `${each.id}|${index}|${ originalName }`, value: originalName, aliasName: aliasName, })
    })
    if (DIMENSION_WARNING_CHART.includes(chartAlias)) {
      let orderList = [];
      [...dimensionList, ...dimensionExtendList].forEach(d => {
        if (d.order === 'customSort' && d.orderList?.length) {
          orderList = orderList.concat(d.orderList)
        }
      })
      const indicatorWarnDimenison = [...dimensionList, ...dimensionExtendList, ...orderList]
      // 指标维度预警
      let dimensionWarn = this.$_deepClone(this.$_getProp(each, 'content.chartUserConfig.dimensionWarningList', []))
      dimensionWarn.forEach((item, i) => {
        item.warningList.forEach(w => {
          if (indicatorWarnDimenison.find(d => d.keyName === w.fieldKeyName)) {
            children.push({ id: `${each.id}_${w.id}`, parentId: each.id, value: w.name })
          }
        })
      })
    }
  }

  // 辅助线
  let xLine = this.$_deepClone(this.$_getProp(each, 'content.chartUserConfig.xAuxiliaryLineData', []))
  let yLine = this.$_deepClone(this.$_getProp(each, 'content.chartUserConfig.yAuxiliaryLineData', []))
  xLine.forEach((item, i) => {
    children.push({ id: item.id, parentId: each.id, value: item.name })
  })
  yLine.forEach((item, i) => {
    children.push({ id: item.id, parentId: each.id, value: item.name })
  })
  // XY轴
  let xAxis = this.$_deepClone(this.$_getProp(each, 'content.chartUserConfig.xAxisSetting', {}))
  let yAxis = this.$_deepClone(this.$_getProp(each, 'content.chartUserConfig.yAxisSetting', []))
  xAxis.chartAxisTitle && children.push({ id: `${each.id}_${index}_xAxis`, parentId: each.id, value: xAxis.chartAxisTitle })
  yAxis.forEach((item, i) => {
    if (!item) return
    item.chartAxisTitle && children.push({ id: `${each.id}_${index}_${i}_yAxis`, parentId: each.id, value: item.chartAxisTitle })
  })
  return children
  function generateNames(arr) {
    let _arr = arr
    if (!Array.isArray(arr)) _arr = []
    let originalName: string[] = []
    let aliasName: string[] = []
    let fieldIndex: any[] = []
    _arr.forEach((item, i) => {
      const alias = item.webFieldType ? this.getDatasetLabel(item) : item.hasOwnProperty('alias_origin') ? item.alias_origin : item.alias
      originalName.push(alias || item.labeName)
      aliasName.push(this.getDatasetLabel(item))
      fieldIndex.push(item.hasOwnProperty('_fieldIndex') ? item._fieldIndex : i)
    })
    return {
      originalName: originalName,
      aliasName: aliasName,
      fieldIndex: fieldIndex,
    }
  }
  function pushChildren(childKey, nameMap: any = null) {
    if (!nameMap) return
    nameMap.originalName.forEach((item, i) => {
      children.push({
        id: `${ each.id }_${ nameMap.fieldIndex[i] }_${ index }_${ childKey }`,
        parentId: each.id,
        name: `${ each.id }|${ index }|${ item }`,
        value: item,
        aliasName: nameMap.aliasName[i],
      })
    })
  }
}

// 获取看板对应的预览语言的内容
export function getPreviewContentList(lang, langObj) {
  const arr = []
  Object.keys(langObj).forEach(item => {
    arr.push(...langObj[item].filter(eve => {
      if (this && (eve.type === 'name' || eve.type === 'nameSub')) {
        if (this.boardInfo[eve.type] === '') { // 如果是清空了的话 不仅不能给上翻译，还要重置之前的翻译内容
          eve.value = ''
        }
        return eve.languageCode === lang && this.boardInfo[eve.type] !== ''
      } else {
        return eve.languageCode === lang
      }
    }))
  })
  return arr
}
export function checkPreviewName(arr, titleKey) {
  let nameSubObj
  if (this.boardInfo.tagModeStack && Object.keys(this.boardInfo.tagModeStack).length > 0) {
    let settingTitle = this.boardInfo.tagModeStack.settingTitle
    let hasSetTitle = {}
    for (let key in settingTitle) {
      if (settingTitle[key].title) {
        hasSetTitle[key] = settingTitle[key]
      }
    }
    if (Object.keys(hasSetTitle).length >= 1) {
      let activeTab = this.paramsPanelList.find(v => v.active).id
      if (titleKey === 'type') {
        Object.keys(hasSetTitle).map(v => {
          let obj = arr.find(i => {
            if (i.key) {
              return i.key.split('|')[1] === v && i.key.split('|')[0] === 'nameSub'
            } else {
              return false
            }
          })
          hasSetTitle[v].currentLang = obj ? obj.value : ''
        })
        let currentObj = arr.find(v => v.key && v.key.split('|')[1] === activeTab && v.key.split('|')[0] === 'nameSub')
        nameSubObj = currentObj || (arr.filter(item => item[titleKey] === 'nameSub')[0])
      }
      if (titleKey === 'isToplanguage') {
        // 退出预览需要还原缓存
        nameSubObj = arr.find(v => v.id && v.id.split('|')[1] === activeTab && v.id.split('|')[0] === 'nameSub') || arr.filter(item => item[titleKey] === 'nameSub')[0]
        Object.keys(hasSetTitle).map(v => { // 不止要还原subName 因为在退出预览时会初始化一遍 导致非当前标签页多语言混乱
          hasSetTitle[v].title = hasSetTitle[v].locTitle
          delete hasSetTitle[v].locTitle
          hasSetTitle[v].currentLang = ''
        })
      }
    }
  } else {
    nameSubObj = arr.filter(item => item[titleKey] === 'nameSub')[0]
  }
  const nameObj = arr.filter(item => item[titleKey] === 'name')[0]
  const codeObj = arr.filter(item => item[titleKey] === 'code')[0]
  // 是否为副本 并且为sbi
  const isDupl = this.boardInfo?.metaType === '1' && this.utils.env?.projectName === ALL_PROJECT_NAME.SBI
  const copyStr = isDupl ? 'Dupl_' : ''
  // 14202 【超链接】跳转到账单详情显示有问题
  this.boardInfo.nameSub = this.boardInfo.nameSub && nameSubObj?.value
    ? nameSubObj.value
    : this.boardInfo.nameSub
  this.boardInfo.name = nameObj
    ? nameObj.value || this.boardInfo.name
    : this.boardInfo.name
  this.boardInfo.code = codeObj?.value
    ? codeObj.value
    : this.boardInfo.code
}
export function fn(el, arr, contentKey, key = 'label') {
  const labelObj = arr.filter(item => item[contentKey] === el.id)[0]
  el[key] = labelObj ? labelObj.value || el[key] : el[key]
}

export function switchParamsPanelList(content, each, vm, arr, contentKey = 'key') {
    content.forEach(eve => {
      const labelObj = arr.filter(item => item[contentKey] === eve.id)[0]
      vm.$set(eve, 'elName', labelObj ? labelObj.value || eve.elName : eve.elName)
      // eve.elName = labelObj ? labelObj.value || eve.elName : eve.elName
      if (eve.type === 'groupBy' || eve.type === 'breakdown') {
        eve.content.selectedDimensionFieldsObj.forEach((v, i) => {
          const labelObj = arr.filter(
            v => v[contentKey] === `${each.id}_${eve.type}_${i}_alias`
          )[0]
          v.alias = labelObj ? labelObj.value || v.alias : v.alias
        })
      } else if (eve.type === TYPE_PARAM_ELEMENT.TEXTBOX) {
        if (eve.content.radioVal === '5') {
          let fromLang, toLang, intervalLang
          arr.some(v => {
            if (v[contentKey] === `${each.id}_${eve.type}_${ eve.content.radioVal }_from`) {
              eve.content.metricGroupField.lang_from = v.value || 'From'
              fromLang = true
            } else if (v[contentKey] === `${each.id}_${eve.type}_${ eve.content.radioVal }_to`) {
              eve.content.metricGroupField.lang_to = v.value || 'To'
              toLang = true
            } else if (v[contentKey] === `${each.id}_${eve.type}_${ eve.content.radioVal }_interval`) {
              eve.content.metricGroupField.lang_interval = v.value || 'Interval'
              intervalLang = true
            }
            return fromLang && toLang && intervalLang
          })
        }
      } else if (eve.type === 'dateType') {
        const labelObj = arr.filter(
          v => v[contentKey] === `${each.id}_${eve.type}_alias`
        )[0]
        eve.content.alias = labelObj
          ? labelObj.value || eve.content.alias
          : eve.content.alias
        const labelObj2 = arr.filter(
          v => v[contentKey] === `${each.id}_${eve.type}_amalgamateAlias`
        )[0]
        eve.content.amalgamateAlias = labelObj2
          ? labelObj2.value || eve.content.amalgamateAlias
          : eve.content.amalgamateAlias
      } else if (eve.type === 'peopleLayer') {
        eve.content.alias && eve.content.alias.forEach((v, i) => {
          const labelObj = arr.filter(
            m => m[contentKey] === `${each.id}_${eve.type}_${i}_alias`
          )[0]
          vm.$set(eve.content.alias, i, labelObj ? labelObj.value || v : v)
          // eve.content.alias[i] = labelObj ? labelObj.value || v : v
        })
      } else if (eve.type === 'locationNew') {
        let v = eve.content.LocationCompareName
        const labelObj = arr.filter(
          m => m[contentKey] === `${each.id}_${eve.type}_compare`
        )[0]
        vm.$set(eve.content, 'LocationCompareName', labelObj ? labelObj.value || v : v)
        const currencyObj = arr.filter(
          m => {
            return m[contentKey] === `${each.id}_currency` || m[contentKey] === 'currency'
          }
        )[0]
        vm.$set(eve.content, 'currency', currencyObj ? currencyObj.value || 'Currency' : 'Currency')

        const hierarchiesObj = arr.filter(
          m => {
            return m[contentKey] === `${each.id}_hierarchies` || m[contentKey] === 'hierarchies'
          }
        )[0]
        vm.$set(eve.content, 'hierarchies', hierarchiesObj ? hierarchiesObj.value || 'Hierarchies' : 'Hierarchies')

        const dimensionsObj = arr.filter(
          m => {
            return m[contentKey] === `${each.id}_dimensions` || m[contentKey] === 'dimensions'
          }
        )[0]
        vm.$set(eve.content, 'dimensions', dimensionsObj ? dimensionsObj.value || 'Dimensions' : 'Dimensions')

      } else if (eve.type === 'CalendarQuick' || eve.type === 'DateQuickOperation' || eve.type === 'financialCalendar' || eve.type === 'bussinessCalendar') {
        eve.content.compare && eve.content.compare.forEach((item) => {
          const priorObj = arr.filter(
            m => (m[contentKey] === `${each.id}_${item.id}_prior` || (item.value === 'Prior Period' && m[contentKey] === `${each.id}_${eve.type}_prior`))
          )[0]
          // vm.$set(item, 'name', priorObj ? priorObj.value || item.name : item.name)
          vm.$set(item, 'languageName', priorObj ? priorObj.value || undefined : undefined)
        })
      } else if (eve.type === 'search') {
        eve.content.language && Object.keys(eve.content.language).forEach((item) => {
          const priorObj = arr.filter(
            m => (m[contentKey] === `${each.id}_${eve.content.language[item].id}_search_${eve.content.language[item].value}`)
          )[0]
          vm.$set(eve.content.language[item], 'name', priorObj ? priorObj.value || eve.content.language[item].realName : eve.content.language[item].realName)
        })
      } else if (eve.type === 'timeParams') {
        eve.content.language && Object.keys(eve.content.language).forEach((item) => {
          const priorObj = arr.filter(
            m => (m[contentKey] === `${each.id}_${eve.content.language[item].id}_timeParams_${eve.content.language[item].value}`)
          )[0]
          let str = contentKey === 'key' && priorObj ? priorObj.value : ''
          vm.$set(eve.content.aliseConfig, eve.content.language[item].value + 'Language', priorObj ? str : '')
        })
      } else if (eve.type === TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS) {
        eve.content.language && Object.keys(eve.content.language).forEach((item) => {
          const priorObj = arr.filter(
            m => (m[contentKey] === `${each.id}_${eve.content.language[item].id}_businessTrends_${eve.content.language[item].value}`)
          )[0]
          let str = contentKey === 'key' && priorObj ? priorObj.value : ''
          vm.$set(eve.content.aliseConfig, eve.content.language[item].value + 'Language', priorObj ? str : '')
        })
      } else if (eve.type === TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS) {
        eve.content.language && Object.keys(eve.content.language).forEach((item) => {
          const priorObj = arr.filter(
            m => (m[contentKey] === `${each.id}_${eve.content.language[item].id}_financialTrends_${eve.content.language[item].value}`)
          )[0]
          let str = contentKey === 'key' && priorObj ? priorObj.value : ''
          vm.$set(eve.content.aliseConfig, eve.content.language[item].value + 'Language', priorObj ? str : '')
        })
      } else if (eve.type === TYPE_PARAM_ELEMENT.Omit_Zero_Total) {
        const yesLanVal = arr.filter(
          v => v[contentKey] === `${each.id}_YES`
        )[0]
        vm.$set(eve.content, 'yesLabel', yesLanVal ? yesLanVal.value || eve.content.yesLabel : eve.content.yesLabel)

        const noLanVal = arr.filter(
          v => v[contentKey] === `${each.id}_NO`
        )[0]
        vm.$set(eve.content, 'noLabel', noLanVal ? noLanVal.value || eve.content.noLabel : eve.content.noLabel)
      }
    })
}

export function checkParamsPanelListContent(paramsPanelList, vm, arr, contentKey = 'key') {
  paramsPanelList.forEach(each => {
    fn(each, arr, contentKey)
    switchParamsPanelList(each.content, each, vm, arr, contentKey)
  })
}

// 替换多语言
export function checkPreviewContent(
  arr,
  titleKey = 'type',
  contentKey = 'key',
  type
) {
  vm = this
  vm.chartLangObj = []
  if (type === 'init') {
    this.$nextTick(() => {
      checkPreviewName.call(this, arr, titleKey)
    })
  } else {
    checkPreviewName.call(this, arr, titleKey)
  }
  // 替换看板中除表格之外的多语言内容
  // realLang 真实的当前语言
  // this.paramsPanelList.forEach(each => {
  //   fn(each, arr, contentKey)
  //   switchParamsPanelList(each.content, each, vm, arr, contentKey)
  // })
  checkParamsPanelListContent(this.paramsPanelList, vm, arr, contentKey)

  this.elList.forEach(each => {
    // if (each.type !== TYPE_ELEMENT.TABLE) {
    if (each.type) {
      fn(each, arr, contentKey, 'elName')
      // 此处是适配器
      checkLangElement[each.type] && checkLangElement[each.type].call(this, { el: each, arr, contentKey })
    }
  })
  Array.isArray(this.dynamicTags) && this.dynamicTags.forEach(each => {
    fn(each, arr, contentKey)
  })
  let boardBtnOptions = this.boardInfo?.boardBtnOptions || {}
  let activeBtnType = this.boardInfo?.activeBtnType || 'singleData'
  let length = boardBtnOptions[activeBtnType]?.length
  if (length && length > 0) {
    boardBtnOptions[activeBtnType].forEach(each => {
      const labelObj = arr.filter(item => item[contentKey] === each.id)[0]
      each.reName[this.sdpLangcode] = labelObj ? labelObj.value || each.reName[this.sdpLangcode] : each.reName[this.sdpLangcode]
    })
  }
}

export function switchElLanguage({
  arr = [],
  contentKey = 'key',
  elList = [],
}) {
  vm = this
  elList.forEach(el => {
    if (el.type) {
      fn(el, arr, contentKey, 'elName')
      // 此处是适配器
      checkLangElement[el.type] && checkLangElement[el.type].call(this, { el, arr, contentKey })
    }
    // 特殊处理表格 文本框 杜邦分析图
  })
}

export function getParamsPanelListLanguageContent(paramsPanelList) {
  const moreLanguageContent: any[] = []
  paramsPanelList.forEach(item => {
    moreLanguageContent.push({
      id: `components_${item.id}`,
      isTop: true,
      value: `${item.label} ${this.$t('sdp.views.b_component')}`,
      children: []
    })
  })

  moreLanguageContent.forEach(item => {
    paramsPanelList.forEach(each => {
      if (each.id === item.id.substring(item.id.indexOf('_') + 1)) {
        each.label &&
          item.children.push({ id: `${each.id}`, parentId: item.id, value: each.label })
          // Currency 不放在location之下了
          item.children.push({ id: `${each.id}_currency`, parentId: item.id, value: 'Currency' })
        each.content.forEach(eve => {
          if (eve.type !== TYPE_PARAM_ELEMENT.LOCATION_QUICK) {
            let arr: any[] = []
            if (eve.type === 'groupBy' || eve.type === 'breakdown') {
              eve.content.selectedDimensionFieldsObj.forEach((v, i) => {
                v.alias &&
                arr.push({
                  id: `${each.id}_${eve.type}_${i}_alias`,
                  parentId: eve.id,
                  value: v.alias
                })
              })
            } else if (eve.type === TYPE_PARAM_ELEMENT.TEXTBOX) {
              if (eve.content.radioVal === '5') {
                arr.push({
                  id: `${each.id}_${eve.type}_${ eve.content.radioVal }_from`,
                  parentId: eve.id,
                  value: 'From',
                })
                arr.push({
                  id: `${each.id}_${eve.type}_${ eve.content.radioVal }_to`,
                  parentId: eve.id,
                  value: 'To',
                })
                arr.push({
                  id: `${each.id}_${eve.type}_${ eve.content.radioVal }_interval`,
                  parentId: eve.id,
                  value: 'Interval',
                })
              }
            } else if (eve.type === 'dateType') {
              eve.content.alias &&
              arr.push({
                id: `${each.id}_${eve.type}_alias`,
                parentId: eve.id,
                value: eve.content.alias
              })
              eve.content.amalgamateAlias &&
              arr.push({
                id: `${each.id}_${eve.type}_amalgamateAlias`,
                parentId: eve.id,
                value: eve.content.amalgamateAlias
              })
            } else if (eve.type === 'peopleLayer') {
              eve.content.alias &&
              eve.content.alias.forEach((v, i) => {
                v &&
                  arr.push({
                    id: `${each.id}_${eve.type}_${i}_alias`,
                    parentId: eve.id,
                    value: v
                  })
              })
            } else if (eve.type === 'locationNew') {
              // Currency 不放在location之下了
              // arr.push({
              //   id: `${each.id}_currency`,
              //   parentId: eve.id,
              //   value: 'Currency'
              // })
              arr.push({
                id: `${each.id}_hierarchies`,
                parentId: eve.id,
                value: eve.content.hierarchies
              })
              arr.push({
                id: `${each.id}_dimensions`,
                parentId: eve.id,
                value: eve.content.dimensions
              })
              arr.push({
                id: `${each.id}_${eve.type}_compare`,
                parentId: eve.id,
                type: eve.type,
                value: 'Compare'
              })
            } else if (eve.type === 'CalendarQuick' || eve.type === 'DateQuickOperation' || eve.type === 'financialCalendar' || eve.type === 'bussinessCalendar') {

              eve.content.compare && eve.content.compare.forEach((item) => {
                arr.push({
                  id: `${each.id}_${item.id}_prior`,
                  oldId: `${each.id}_${eve.type}_prior`,
                  parentId: eve.id,
                  value: item.name,
                  type: eve.type,
                  realValue: item.value,
                })
              })
              if (eve.type === 'financialCalendar' || eve.type === 'bussinessCalendar') {
                if (!eve.content.compare) {
                  arr.push({
                    id: this.$_generateUUID(),
                    name: 'Compare',
                    value: 'Compare'
                  })
                }
              }
            } else if (eve.type === TYPE_PARAM_ELEMENT.SEARCH) {
              eve.content.language && Object.keys(eve.content.language).forEach((item) => {
                arr.push({
                  id: `${each.id}_${eve.content.language[item].id}_search_${eve.content.language[item].value}`,
                  parentId: eve.id,
                  value: eve.content.language[item].name,
                  type: eve.type,
                  realValue: eve.content.language[item].value,
                })
              })
            } else if (eve.type === TYPE_PARAM_ELEMENT.TIME) {
              eve.content.language && Object.keys(eve.content.language).forEach((item) => {
                arr.push({
                  id: `${each.id}_${eve.content.language[item].id}_timeParams_${eve.content.language[item].value}`,
                  parentId: eve.id,
                  value: eve.content.language[item].label,
                  type: eve.type,
                })
              })
            } else if (eve.type === TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS) {
              eve.content.language && Object.keys(eve.content.language).forEach((item) => {
                arr.push({
                  id: `${each.id}_${eve.content.language[item].id}_businessTrends_${eve.content.language[item].value}`,
                  parentId: eve.id,
                  value: eve.content.language[item].label,
                  type: eve.type,
                })
              })
            } else if (eve.type === TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS) {
              eve.content.language && Object.keys(eve.content.language).forEach((item) => {
                arr.push({
                  id: `${each.id}_${eve.content.language[item].id}_financialTrends_${eve.content.language[item].value}`,
                  parentId: eve.id,
                  value: eve.content.language[item].label,
                  type: eve.type,
                })
              })
            } else if (eve.type === TYPE_PARAM_ELEMENT.Omit_Zero_Total) {
              arr.push({
                id: `${each.id}_YES`,
                parentId: eve.id,
                value: eve.content.yesLabel,
                type: eve.type,
              })
              arr.push({
                id: `${each.id}_NO`,
                parentId: eve.id,
                value: eve.content.noLabel,
                type: eve.type,
              })
            }
            eve.elName &&
            item.children.push({
              id: `${eve.id}`,
              parentId: item.id,
              value: eve.elName,
              children: arr
            })
          }
        })
      }
    })
  })

  return moreLanguageContent
}

// 获取多语言可替换元素树状结构数据
export function getMoreLanguageContent() {
  const moreLanguageContent = [
    {
      id: 'dataBoard',
      isTop: true,
      value: this.$t('sdp.views.b_board'),
      children: []
    }
  ]
  // this.paramsPanelList.forEach(item => {
  //   moreLanguageContent.push({
  //     id: `components_${item.id}`,
  //     isTop: true,
  //     value: `${item.label} ${this.$t('sdp.views.b_component')}`,
  //     children: []
  //   })
  // })
  moreLanguageContent.push({
    id: 'element',
    isTop: true,
    value: this.$t('sdp.views.b_elements'),
    children: []
  })
  if (!this.isMobile) {
    moreLanguageContent.push({
      id: 'componentBtn',
      isTop: true,
      value: this.$t('sdp.views.b_componentBtn'),
      children: []
    })
  }
  moreLanguageContent.forEach(item => {
    switch (item.id) {
      case 'dataBoard':
        this.boardInfo.code &&
          item.children.push({
            id: 'code',
            parentId: item.id,
            value: this.boardInfo.code,
            isToplanguage: 'code'
          })
        this.boardInfo.name &&
          item.children.push({
            id: 'name',
            parentId: item.id,
            value: this.boardInfo.name,
            isToplanguage: 'name'
          })
        this.boardInfo.remark &&
          item.children.push({
            id: 'remark',
            parentId: item.id,
            value: this.boardInfo.remark,
            isToplanguage: 'remark'
          })
        // 如果设置了看板标签标题则需要为每一个设置了标题的内容加上多语言
        if (this.boardInfo.tagModeStack && Object.keys(this.boardInfo.tagModeStack).length > 0) {
          let settingTitle = this.boardInfo.tagModeStack.settingTitle
          let hasSetTitle = {}
          for (let key in settingTitle) {
            if (settingTitle[key].title) {
              hasSetTitle[key] = settingTitle[key]
              settingTitle[key].locTitle = settingTitle[key].title
            }
          }
          if (Object.keys(hasSetTitle).length >= 1) {
            Object.keys(hasSetTitle).map(v => {
              item.children.push({
                id: `nameSub|${v}`,
                value: hasSetTitle[v].title
              })
            })
          }
        } else {
          this.boardInfo.nameSub &&
          item.children.push({
            id: 'nameSub',
            parentId: item.id,
            value: this.boardInfo.nameSub,
            isToplanguage: 'nameSub'
          })
        }

        break
      case 'element': {
        const elList = this.originElList ? this.$_deepClone(this.originElList) || this.elList : this.elList
        const isOrigin = !!this.originElList
        this.originElList && this.$set(this, 'originElList', undefined)
        elList.forEach(each => {
          // 表格的名字到表格内部特殊处理
          // if (each.type !== TYPE_ELEMENT.TABLE) {
          if (each.type) {
              // 此处是适配器
              getLangElement[each.type] && getLangElement[each.type].call(this, {
                el: each,
                isOrigin,
                languageList: item
              })
          }
        })
        // 标签页名字
        let length = this.dynamicTags?.length
        if (length && length > 1) {
          if (this.isMobile) {
            this.dynamicTags.forEach(each => {
              each.label &&
              item.children.push({ id: `${each.id}`, parentId: item.id, value: each.label })
            })
          } else {
            for (let i = length - 1; i >= 0; i--) {
              const each = this.dynamicTags[i]
              each.label &&
              item.children.push({ id: `${each.id}`, parentId: item.id, value: each.label })
            }
          }
        }
        break
      }
      case 'componentBtn':
        // 标签页名字
        let boardBtnOptions = this.boardInfo?.boardBtnOptions || {}
        let activeBtnType = this.boardInfo?.activeBtnType || 'singleData'
        let length = boardBtnOptions[activeBtnType]?.length
        if (length && length > 0) {
          if (!this.isMobile) {
            boardBtnOptions[activeBtnType].forEach(each => {
              each.id &&
              item.children.push({ id: `${each.id}`, parentId: item.id, value: each.reName[this.sdpLangcode] })
            })
          }
        }
        break
      // default:
      //   this.paramsPanelList.forEach(each => {
      //     if (each.id === item.id.substring(item.id.indexOf('_') + 1)) {
      //       each.label &&
      //         item.children.push({ id: `${each.id}`, parentId: item.id, value: each.label })
      //       each.content.forEach(eve => {
      //         if (eve.type !== TYPE_PARAM_ELEMENT.LOCATION_QUICK) {
      //           let arr: any[] = []
      //           if (eve.type === 'groupBy' || eve.type === 'breakdown') {
      //             eve.content.selectedDimensionFieldsObj.forEach((v, i) => {
      //               v.alias &&
      //               arr.push({
      //                 id: `${each.id}_${eve.type}_${i}_alias`,
      //                 parentId: eve.id,
      //                 value: v.alias
      //               })
      //             })
      //           } else if (eve.type === TYPE_PARAM_ELEMENT.TEXTBOX) {
      //             if (eve.content.radioVal === '5') {
      //               arr.push({
      //                 id: `${each.id}_${eve.type}_${ eve.content.radioVal }_from`,
      //                 parentId: eve.id,
      //                 value: 'From',
      //               })
      //               arr.push({
      //                 id: `${each.id}_${eve.type}_${ eve.content.radioVal }_to`,
      //                 parentId: eve.id,
      //                 value: 'To',
      //               })
      //               arr.push({
      //                 id: `${each.id}_${eve.type}_${ eve.content.radioVal }_interval`,
      //                 parentId: eve.id,
      //                 value: 'Interval',
      //               })
      //             }
      //           } else if (eve.type === 'dateType') {
      //             eve.content.alias &&
      //             arr.push({
      //               id: `${each.id}_${eve.type}_alias`,
      //               parentId: eve.id,
      //               value: eve.content.alias
      //             })
      //             eve.content.amalgamateAlias &&
      //             arr.push({
      //               id: `${each.id}_${eve.type}_amalgamateAlias`,
      //               parentId: eve.id,
      //               value: eve.content.amalgamateAlias
      //             })
      //           } else if (eve.type === 'peopleLayer') {
      //             eve.content.alias &&
      //             eve.content.alias.forEach((v, i) => {
      //               v &&
      //                 arr.push({
      //                   id: `${each.id}_${eve.type}_${i}_alias`,
      //                   parentId: eve.id,
      //                   value: v
      //                 })
      //             })
      //           } else if (eve.type === 'locationNew') {
      //             arr.push({
      //               id: `${each.id}_currency`,
      //               parentId: eve.id,
      //               value: 'Currency'
      //             })
      //             arr.push({
      //               id: `${each.id}_hierarchies`,
      //               parentId: eve.id,
      //               value: eve.content.hierarchies
      //             })
      //             arr.push({
      //               id: `${each.id}_dimensions`,
      //               parentId: eve.id,
      //               value: eve.content.dimensions
      //             })
      //             arr.push({
      //               id: `${each.id}_${eve.type}_compare`,
      //               parentId: eve.id,
      //               type: eve.type,
      //               value: 'Compare'
      //             })
      //           } else if (eve.type === 'CalendarQuick' || eve.type === 'DateQuickOperation' || eve.type === 'financialCalendar' || eve.type === 'bussinessCalendar') {

      //             eve.content.compare && eve.content.compare.forEach((item) => {
      //               arr.push({
      //                 id: `${each.id}_${item.id}_prior`,
      //                 oldId: `${each.id}_${eve.type}_prior`,
      //                 parentId: eve.id,
      //                 value: item.name,
      //                 type: eve.type,
      //                 realValue: item.value,
      //               })
      //             })
      //             if (eve.type === 'financialCalendar' || eve.type === 'bussinessCalendar') {
      //               if (!eve.content.compare) {
      //                 arr.push({
      //                   id: this.$_generateUUID(),
      //                   name: 'Compare',
      //                   value: 'Compare'
      //                 })
      //               }
      //             }
      //           } else if (eve.type === TYPE_PARAM_ELEMENT.SEARCH) {
      //             eve.content.language && Object.keys(eve.content.language).forEach((item) => {
      //               arr.push({
      //                 id: `${each.id}_${eve.content.language[item].id}_search_${eve.content.language[item].value}`,
      //                 parentId: eve.id,
      //                 value: eve.content.language[item].name,
      //                 type: eve.type,
      //                 realValue: eve.content.language[item].value,
      //               })
      //             })
      //           } else if (eve.type === TYPE_PARAM_ELEMENT.TIME) {
      //             eve.content.language && Object.keys(eve.content.language).forEach((item) => {
      //               arr.push({
      //                 id: `${each.id}_${eve.content.language[item].id}_timeParams_${eve.content.language[item].value}`,
      //                 parentId: eve.id,
      //                 value: eve.content.language[item].label,
      //                 type: eve.type,
      //               })
      //             })
      //           } else if (eve.type === TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS) {
      //             eve.content.language && Object.keys(eve.content.language).forEach((item) => {
      //               arr.push({
      //                 id: `${each.id}_${eve.content.language[item].id}_businessTrends_${eve.content.language[item].value}`,
      //                 parentId: eve.id,
      //                 value: eve.content.language[item].label,
      //                 type: eve.type,
      //               })
      //             })
      //           } else if (eve.type === TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS) {
      //             eve.content.language && Object.keys(eve.content.language).forEach((item) => {
      //               arr.push({
      //                 id: `${each.id}_${eve.content.language[item].id}_financialTrends_${eve.content.language[item].value}`,
      //                 parentId: eve.id,
      //                 value: eve.content.language[item].label,
      //                 type: eve.type,
      //               })
      //             })
      //           } else if (eve.type === TYPE_PARAM_ELEMENT.Omit_Zero_Total) {
      //             arr.push({
      //               id: `${each.id}_YES`,
      //               parentId: eve.id,
      //               value: eve.content.yesLabel,
      //               type: eve.type,
      //             })
      //             arr.push({
      //               id: `${each.id}_NO`,
      //               parentId: eve.id,
      //               value: eve.content.noLabel,
      //               type: eve.type,
      //             })
      //           }
      //           eve.elName &&
      //           item.children.push({
      //             id: `${eve.id}`,
      //             parentId: item.id,
      //             value: eve.elName,
      //             children: arr
      //           })
      //         }
      //       })
      //     }
      //   })
      //   break
    }
  })

  const arr = getParamsPanelListLanguageContent.call(this, this.paramsPanelList)

  const [item, ...items] = moreLanguageContent
  return [item, ...arr, ...items]
}
// 替换content多语言key
// @params {Array} 多语言
// @params {object} content
// @params {String} 图形id
// 散点图需要单独处理，散点图多语言取的是chartData的colums对象，替换多语言时需要替换chartData的rows和chartSetting
export function contentReplaceLang(arr, content, id) {
  vm = this
  let chartData = content.chartData
  let chartSettings = content.chartSettings
  let isDul = content.chioceTab && !!content.chioceTab.length
  let saveIndex = isDul ? content.saveIndex.toString() : ''
  const { dimensionList = [], metricsContainer = {}, gaugeTarget = {} } = content.chartUserConfig
  // const { dimension, metrics } = content.chartResponse
  // const targetList: any = gaugeTarget.defaults ? [gaugeTarget.defaults] : []
  let params = {
    arr,
    id,
    isDul,
    saveIndex
  }
  _replaceCommonChartContent(chartSettings.dimension, params, dimensionList)
  _replaceCommonChartContent(chartSettings.originDimension, params, dimensionList)
  _replaceCommonChartContent(chartSettings.metrics, params, metricsContainer.default)
  _replaceCommonChartContent(chartSettings.customMetric, params)
  _replaceCommonChartContent(chartSettings.target, params)
  _replaceCommonChartContent(chartSettings.showLine, params)
  if (chartSettings.stack && chartSettings.stack.length) {
    chartSettings.stack.forEach(item => {
      _replaceCommonChartContent(item, params)
    })
  }
  let right = this.$_getProp(chartSettings, 'axisSite.right', [])
  if (right.length) {
    _replaceCommonChartContent(chartSettings.axisSite.right, params)
  }
  _replaceCommonChartContent(chartData.columns, params)
  _replaceScatterContent(content, params)
}

// 替换看板元素多语言适配器（除去表格）
export const checkLangElement = {
  [TYPE_ELEMENT.TABLE](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    if (el.content.superLinkOptions && el.content.superLinkOptions.length > 0) {
      let superLinkOptions = el.content.superLinkOptions
      superLinkOptions.forEach((e, i) => {
        if (e.style.name) {
          const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${e.soleId}_${i}_linkTitle`)[0]
          e.style.name = labelObj ? (labelObj.value || e.style.name) : e.style.name
        }
      })
    }
    if (el.filterData && el.filterData.form) {
      el.filterData.form.forEach(v => {
        let langsObj = arr.find(i => i[contentKey] === `${el.id}_${v.datasetId}_${v.keyName}_filterAlias`)
        v.alias = langsObj ? langsObj.value || v.alias : v.alias

      })
    }
  },
  [TYPE_ELEMENT.TEXT](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    let elName = arr.find(i => i[contentKey] === `${el.id}_elText`)
    el.elName = elName ? elName.value || el.elName : el.elName
  },
  [TYPE_ELEMENT.ELEMENT_TITLE](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    let elName = arr.find(i => i[contentKey] === `${el.id}_elElementTitle`)
    el.elName = elName ? elName.value || el.elName : el.elName
  },
  [TYPE_ELEMENT.WEATHER](paramsObj) {
    // 天气组件目前没有多语言
    const { el, arr, contentKey } = paramsObj

    let elName = arr.find(i => i[contentKey] === `${el.id}_elWeather`)
    el.elName = elName ? elName.value || el.elName : el.elName
  },
  [TYPE_ELEMENT.MATERIAL_LIBRARY](paramsObj) {
    // 素材库组件目前没有多语言
  },
  [TYPE_ELEMENT.TIME](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    let customFormat = arr.find(i => i[contentKey] === `${el.id}_customFormat`)
    el.content.customFormat = customFormat ? customFormat.value || el.content.customFormat : el.content.customFormat

    let elName = arr.find(i => i[contentKey] === `${el.id}_elTime`)
    el.elName = elName ? elName.value || el.elName : el.elName
  },
  [TYPE_ELEMENT.SCROLL_TEXT](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    let scrollText = arr.find(i => i[contentKey] === `${el.id}_text`)
    el.content.text = scrollText ? scrollText.value || el.content.text : el.content.text

    let elName = arr.find(i => i[contentKey] === `${el.id}_elScrollText`)
    el.elName = elName ? elName.value || el.elName : el.elName
  },
  [TYPE_ELEMENT.WEB](paramsObj) {
  },
  [TYPE_ELEMENT.IMAGE](paramsObj) {
  },
  [TYPE_ELEMENT.ELEMENT_GROUP](paramsObj) {
  },
  [TYPE_ELEMENT.COMBINE_CARD](paramsObj) {
  },
  [TYPE_ELEMENT.CHART](paramsObj) {
    if (!vm) { vm = this }
    const { el, arr, contentKey } = paramsObj
    const chartLangObj = _checkPreviewChart(el, arr, 'type', contentKey)
    contentReplaceLang.call(this, chartLangObj, el.content, el.id)
    // 先注释掉替换指标选择器的数据，有bug
    // let chioceTab = el.content.chioceTab
    // if (chioceTab && chioceTab.length) {
    //   chioceTab.forEach((item) => {
    //     contentReplaceLang.call(this, this.chartLangObj, item.saveObj, el.id)
    //   })
    // }
    if (el.filterData && el.filterData.form) {
      el.filterData.form.forEach(v => {
        let langsObj = arr.find(i => i[contentKey] === `${el.id}_${v.datasetId}_${v.keyName}_filterAlias`)
        if (langsObj && langsObj.value) {
          if (langsObj.value === v.keyName) {
            v.alias = ''
          } else {
            v.alias = langsObj.value
          }
        }
        // v.alias = langsObj ? langsObj.value || v.alias : v.alias
      })
    }
    if (el.sorterData && el.sorterData.form) {
      el.sorterData.form.forEach(v => {
        let langsObj = chartLangObj.find(i => i.key === v.fieldAlias)
        if (langsObj && langsObj.value) {
          if (langsObj.value === v.fieldAlias) {
            v.alias = ''
          } else {
            v.alias = langsObj.value
          }
        }
      })
    }
    setTimeout(() => {
      // 移动端组件多语言替换，考虑组件没生成，需要延时，后期优化更好的办法
      if (el.vm?.isMobile && el.vm?.$refs.addMobileLegend) {
        el.vm.$refs.addMobileLegend.initDataList()
      }
      if (el.vm) {
        el.vm.UserConfig.init()
      }
    }, 50)
  },

  [TYPE_ELEMENT.FOUR_QUADRANT](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    const { response: tables, fourQuadrantOptions } = el.content
    Array.isArray(tables) && tables.forEach((e, i) => {
      const labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_fourQuadrant`)[0]
      e.name = labelObj ? (labelObj.value || e.name) : e.name
      let metricsIndex = 0
      e.aliasColumnName.length && e.aliasColumnName.forEach((eve, index) => {
        let labelObj = {}
        if (index < e.dimAliasColumnName.length) {
          labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_${index}_dimensionAlias`)[0]
        } else {
          labelObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_${metricsIndex}_metricsAlias`)[0]
          metricsIndex = metricsIndex + 1
        }
        let viewColumnName = e.viewColumnName[index]
        this.$set(e.viewColumnName, index, labelObj ? (labelObj.value || viewColumnName) : viewColumnName)
      })
    })
    // 替换fourQuadrantOptions内的数据，元素描述保存在fourQuadrantOptions.tables
    const fourQuadrantTable = fourQuadrantOptions.tables || []
    fourQuadrantTable.forEach((e, i) => {
      // 元素描述
      const descriptionObj = arr.filter(item => item[contentKey] === `${el.id}_${i}_description`)[0]
      e.description = descriptionObj ? (descriptionObj.value || e.description) : e.description
    })
  },

  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD](paramsObj) {
    replaceCardElementLang.call(this, paramsObj)
  },

  [TYPE_ELEMENT.COMBINE_CARD](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    _replaceCombineCard(el, arr, el.content, contentKey)
    let choiceTab = el.content.choiceTab
    if (choiceTab && choiceTab.length) {
      choiceTab.forEach((item) => {
        _replaceCombineCard(el, arr, item.saveObj, contentKey)
      })
    }
  },

  [TYPE_ELEMENT.CONTAINER](paramsObj) {
    const { el, arr, contentKey } = paramsObj
    const labelObj = arr.filter(item => item[contentKey] === `${el.id}_containerTitle`)[0]
    const containerElAlias = el.content.settings.elAlias
    el.content.settings.title = labelObj ? (labelObj.value || el.content.settings.title) : el.content.settings.title
    if (el.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
      el.content.tabList.map(v => {
        let langsObj = arr.find(i => i[contentKey] === `${el.id}_${v.name}_containerElAlias`)
        v.title = langsObj ? langsObj.value || v.title : v.title
        if (v.content.filterData && v.content.filterData.form) {
          v.content.filterData.form.forEach(v1 => {
            if (v1.name) {
              let langsObj = arr.find(i => i[contentKey] === `${el.id}_${v1.name}_filterGroupName`)
              v1.groupName = langsObj ? langsObj.value || v1.groupName : v1.groupName
            }
          })
        }
        if (v.content.sorterData && v.content.sorterData.form) {
          v.content.sorterData.form.forEach(v1 => {
            if (v1.name) {
              let langsObj = arr.find(i => i[contentKey] === `${el.id}_${v1.name}_sorterGroupName`)
              v1.groupName = langsObj ? langsObj.value || v1.groupName : v1.groupName
            }
          })
        }
      })
    } else {
      if (Object.keys(containerElAlias).length) {
        Object.keys(containerElAlias).forEach((e, i) => {
          const containerElAliasObj = arr.filter(item => item[contentKey] === `${el.id}_${e}_containerElAlias`)[0]
          if (containerElAlias[e]) {
            containerElAlias[e] = containerElAliasObj ? (containerElAliasObj.value || containerElAlias[e]) : containerElAlias[e]
          }
        })
      }
    }
  },
  [TYPE_ELEMENT.CUSTOMER_ELEMENT](paramsObj) {
      const { el, arr, contentKey } = paramsObj
      if (el.content.config?.title?.text) {
        let elTitle = arr.find(i => i[contentKey] === `${el.id}_elementTitle`)
        el.content.config.title.text = elTitle?.value || el.content.config.title.text
      }

      let elName = arr.find(i => i[contentKey] === `${el.id}_customerElement`)
      el.elName = elName?.value || el.elName
  },
  // [TYPE_ELEMENT.DUPONT_ANALYSIS](paramsObj) {
  //   const { el, arr, contentKey } = paramsObj

  //   let elName = arr.find(i => i[contentKey] === `${el.id}_dupontAnalysis`)
  //   el.elName = elName?.value || el.elName

  //   let langTitle = arr.find(i => i[contentKey] === `${el.id}_title`)
  //   const title = el.content?.userConfig?.title
  //   if (title?.value && langTitle) {
  //     title.value = langTitle.value
  //   }

  //   if (!el?.content?.cardData?.length) return
  //   el.content.cardData.forEach(item => {
  //     replaceCardElementLang.call(this, { el: item, arr, contentKey, parent: el })
  //   })
  // }
}

export function replaceCardElementLang(paramsObj) {
  const { el, arr, contentKey } = paramsObj
  const id = el.langId || el.id

  const { optionArray, tagNewCardContent } = el.content
  optionArray.length && optionArray.forEach((e, i) => {
    const labelObj = arr.filter(item => item[contentKey] === `${id}_${i}_cardTitle`)[0]
    e.cardName = labelObj?.value || e.cardName

    const subLabelObj = arr.filter(item => item[contentKey] === `${id}_${i}_subCardTitle`)[0]
    e.subCardName = subLabelObj?.value || e.subCardName
  })
  if ([TAGNEWCARD.RATECARD].includes(tagNewCardContent)) {
    _setLangObj(el.content.chartUserConfig, 'ratioUnit', contentKey, arr, id)
    _setLangObj(el.content.chartUserConfig, 'referenceValueList', contentKey, arr, id)
    _setLangObj(el.content.chartUserConfig, 'dimension', contentKey, arr, id, optionArray[0]?.dimension)
  }
  if ([TAGNEWCARD.RATECARD, TAGNEWCARD.COMPARECARD].includes(tagNewCardContent)) {
    _setLangObj(el.content.chartUserConfig, 'compareValue', contentKey, arr, id, optionArray[0]?.compareValue)
  }
  _setLangObj(el.content.chartUserConfig, 'dimensionUnit', contentKey, arr, id)
  _setLangObj(el.content.chartUserConfig, 'remark', contentKey, arr, id)
  if(tagNewCardContent=== TAGNEWCARD.TWOINDICES){ // 双指标卡片少一个备注
    _setLangObj(el.content.chartUserConfig, 'remarkTwo', contentKey, arr, id)
  }
  _setLangObj(el.content.chartUserConfig, 'growthCompareText', contentKey, arr, id)
  _setLangObj(el.content.chartUserConfig, 'completionText', contentKey, arr, id)
  if (el.content.superLinkOptions && el.content.superLinkOptions.length > 0) {
    let superLinkOptions = el.content.superLinkOptions
    superLinkOptions.forEach((e, i) => {
      if (e.style.name) {
        const labelObj = arr.filter(item => item[contentKey] === `${id}_${e.soleId}_${i}_linkTitle`)[0]
        e.style.name = labelObj ? (labelObj.value || e.style.name) : e.style.name
      }
    })
  }
}

// 获得多语言的适配器（除去表格） hbw
export const getLangElement = {
  [TYPE_ELEMENT.TABLE](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    if (el.content.superLinkOptions && el.content.superLinkOptions.length > 0) {
      let superLinkOptions = el.content.superLinkOptions
      superLinkOptions.forEach((e, i) => {
        e.style.name && children.push({ id: `${el.id}_${e.soleId}_${i}_linkTitle`, value: e.style.name })
      })
    }
    if (el.filterData && el.filterData.form) {
      el.filterData.form.forEach(v => {
        if (v.keyName) {
          children.push({
            id: `${el.id}_${v.datasetId}_${v.keyName}_filterAlias`,
            parentId: el.id,
            value: v.alias ? v.alias : v.keyName
          })
        }
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.CHART](paramsObj) {
    let children = []
    const { el, languageList, isOrigin } = paramsObj
    if (!el.vm && isOrigin) {
      const vm = this.elList.find(e => e.id === el.id).vm
      Object.defineProperty(el, 'vm', {
        enumerable: false,
        configurable: true,
        get() {
          return vm
        },
      })
    }
    let chioceTab = el.content.chioceTab
    if (chioceTab && chioceTab.length) {
      chioceTab.forEach((item, index) => {
        let obj = JSON.parse(JSON.stringify(el))
        obj.content = item.saveObj
        const fieldInstance1 = new Field(obj, el.vm)
        const UserConfig1 = fieldInstance1.getAll()
        children = _getMoreLangChart.call(this, obj, children, index, { UserConfig: UserConfig1 })
        const mapSchemeSettingList = item.saveObj.mapSchemeSetting?.schemeList || []

        mapSchemeSettingList.length && mapSchemeSettingList.forEach((scheme) => {
          if (scheme.childChartAlias === 've-map-world') return
          obj.content = scheme.saveObj
          const fieldInstance2 = new Field(obj, el.vm)
          const UserConfig2 = fieldInstance2.getAll()
          children = _getMoreLangChart.call(this, obj, children, index + '$ms$' + scheme.childChartAlias, { UserConfig: UserConfig2 })
        })
        children.push({ id: `${el.id}_${item.id}_indicatrixAlias`, parentId: el.id, value: item.name })
      })
    } else {
      children = _getMoreLangChart.call(this, el, children, 'x')
      const mapSchemeSettingList = el.content.mapSchemeSetting?.schemeList || []

      mapSchemeSettingList.length && mapSchemeSettingList.forEach((scheme) => {
        if (scheme.childChartAlias === 've-map-world') return
        let obj = JSON.parse(JSON.stringify(el))
        obj.content = scheme.saveObj
        const fieldInstance2 = new Field(obj, el.vm)
        const UserConfig2 = fieldInstance2.getAll()
        children = _getMoreLangChart.call(this, obj, children, 'x' + '$ms$' + scheme.childChartAlias, { UserConfig: UserConfig2 })
      })
    }
    if (el.filterData && el.filterData.form) {
      el.filterData.form.forEach(v => {
        if (v.keyName) {
          children.push({
            id: `${el.id}_${v.datasetId}_${v.keyName}_filterAlias`,
            parentId: el.id,
            value: v.alias ? v.alias : v.keyName,
          })
        }
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}`, parentId: languageList.id, value: el.elName, children: children })
  },

  [TYPE_ELEMENT.FOUR_QUADRANT](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    const tables = el.content.fourQuadrantOptions.tables
    tables.length && tables.forEach((e, i) => {
      e.name && children.push({ id: `${el.id}_${i}_fourQuadrant`, parentId: languageList.id, value: e.name })
      e.metrics.length && e.metrics.forEach((eve, index) => {
        eve.viewColumnName && children.push({ id: `${el.id}_${i}_${index}_metricsAlias`, parentId: languageList.id, value: eve.viewColumnName })
      })
      e.dimension.length && e.dimension.forEach((eve, index) => {
        eve.viewColumnName && children.push({ id: `${el.id}_${i}_${index}_dimensionAlias`, parentId: languageList.id, value: eve.viewColumnName })
      })
       let isHideDesc = !this.isMobile && !this.utils.isDataReport && !this.utils.isLargeScreen
       if(isHideDesc){
         children.push({ id: `${el.id}_${i}_description`, parentId: languageList.id, value: e.description,'isHideDesc': true})
       }else{
        e.description &&  children.push({ id: `${el.id}_${i}_description`, parentId: languageList.id, value: e.description })
       }
    })
    el.elName && languageList.children.push({ id: `${el.id}`, parentId: languageList.id, value: el.elName, children: children })
  },

  [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD](paramsObj) {
    _getCardElementLang.call(this, paramsObj)
  },

  // [TYPE_ELEMENT.DUPONT_ANALYSIS](paramsObj) {
  //   const { el, languageList } = paramsObj
  //   let children = []

  //   let title = el.content?.userConfig?.title?.value
  //   if (title) {
  //     children.push({
  //       id: `${el.id}_title`,
  //       parentId: languageList.id,
  //       value: title
  //     })
  //   }
  //   el.elName && languageList.children.push({ id: `${el.id}_dupontAnalysis`, value: el.elName, children: children })

  //   if (!el?.content?.cardData?.length) return

  //   el.content.cardData.forEach(item => {
  //       let langItem = _getCardElementLang.call(this, { parent: el, el: item })
  //       const cardName = item.content.optionArray?.[0]?.cardName
  //       cardName && children.push({ id: `${el.id}_${item.id}_0_cardTitle`, value: cardName, children: langItem })
  //   })
  // },

  [TYPE_ELEMENT.COMBINE_CARD](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    let choiceTab = el.content.choiceTab
    if (choiceTab && choiceTab.length) {
      choiceTab.forEach((item) => {
        let obj = JSON.parse(JSON.stringify(el))
        obj.content = item.saveObj
        children = _getCombineCard.call(this, obj, children)
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}_combineCard`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.CUSTOMER_ELEMENT](paramsObj) {
    let children: any[] = []
    const { el, languageList } = paramsObj
    let title = el.content.config.title || {}
    if (title?.text) {
      children.push({
        id: `${el.id}_elementTitle`,
        parentId: languageList.id,
        value: title.text,
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}_customerElement`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.CONTAINER](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    const containerTitle = el.content.settings.title
    const containerElAlias = el.content.settings.elAlias
    containerTitle && children.push({ id: `${el.id}_containerTitle`, value: containerTitle })
    if (el.subType === TYPE_ELEMENT.ADVANCE_CONTAINER) {
      if (el.content.tabList && el.content.tabList.length > 0) {
        el.content.tabList.map(v => {
          children.push({
            id: `${el.id}_${v.name}_containerElAlias`,
            parentId: languageList.id,
            value: v.title
          })
          let content = v.content
          if (content.filterData && content.filterData.form) {
            content.filterData.form.forEach(v1 => {
              v1.name = v1.groupName
              if (v1.name) {
                children.push({
                  id: `${el.id}_${v1.groupName}_filterGroupName`,
                  parentId: languageList.id,
                  value: v1.groupName
                })
              }
            })
          }
          if (content.sorterData && content.sorterData.form) {
            content.sorterData.form.forEach(v1 => {
              v1.name = v1.groupName
              if (v1.name) {
                children.push({
                  id: `${el.id}_${v1.groupName}_sorterGroupName`,
                  parentId: languageList.id,
                  value: v1.groupName
                })
              }
            })
          }
        })
      }
    } else {
      if (Object.keys(containerElAlias).length) {
        Object.keys(containerElAlias).forEach((e, i) => {
          containerElAlias[e] && children.push({ id: `${el.id}_${e}_containerElAlias`, parentId: languageList.id, value: containerElAlias[e] || el.content.dragSelectsEls.filter(eve => eve.id === e)[0].elName })
        })
      }
    }

    el.elName && languageList.children.push({ id: `${el.id}`, value: el.elName, parentId: languageList.id, children: children })
  },

  [TYPE_ELEMENT.TEXT](paramsObj) {
    let children: any = []
    const { el, languageList } = paramsObj
    if (el.content.earlyWarningOptions && el.content.earlyWarningOptions.length > 0) {
      let earlyWarningOptions = el.content.earlyWarningOptions
      earlyWarningOptions.forEach((e, i) => {
        e.name && children.push({ id: `${el.id}_earlyWarningOptions_${e.id}_name`, value: e.name })
        if (e.fieldType === 'other') {
          e?.contrastField?.labeName && children.push({ id: `${el.id}_earlyWarningOptions_${e.id}_contrastName`, value: (e.contrastField.alias || e.contrastField.labeName) })
        }
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.ELEMENT_TITLE](paramsObj) {
    let children: any[] = []
    const { el, languageList } = paramsObj
    el.elName && languageList.children.push({ id: `${el.id}_elElementTitle`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.TIME](paramsObj) {
    let children = []
    const { el, languageList } = paramsObj
    let customFormat = el.content.customFormat
    if (customFormat) {
      children.push({
        id: `${el.id}_customFormat`,
        parentId: languageList.id,
        value: customFormat
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}_elTime`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.WEATHER](paramsObj) {
    // 天气组件目前没有多语言
    let children = []
    const { el, languageList } = paramsObj
    el.elName && languageList.children.push({ id: `${el.id}_elWeather`, value: el.elName, children: children })
  },
  [TYPE_ELEMENT.WEB](paramsObj) {
  },
  [TYPE_ELEMENT.IMAGE](paramsObj) {
  },
  [TYPE_ELEMENT.SCROLL_TEXT](paramsObj) {

    let children = []
    const { el, languageList } = paramsObj
    let customFormat = el.content.text
    if (customFormat) {
      children.push({
        id: `${el.id}_text`,
        parentId: languageList.id,
        value: customFormat
      })
    }
    el.elName && languageList.children.push({ id: `${el.id}_elScrollText`, value: el.elName, children: children })
  }
}
// 使用name去替换的字段回退
export function rollBackChartLangObj(lang, langObj, elList = this.elList) {
  let chartLangObj = []
  let arr = getPreviewContentList(lang, langObj)
  let chartEl = elList.filter(each => {
    return each.type === TYPE_ELEMENT.CHART
  })
  if (!chartEl || !chartEl.length) return
  arr.forEach(item => {
    if (item.name) {
      _getLangObj(item, chartLangObj)
    }
  })
  let backChartLangObj = chartLangObj.filter(item => item.value)
  let tempStr = ''
  backChartLangObj.forEach(item => {
    tempStr = item.key
    item.key = item.value
    item.value = tempStr
  })
  chartEl.forEach(el => {
    contentReplaceLang.call(this, backChartLangObj, el.content, el.id)
    let chioceTab = el.content.chioceTab
    if (chioceTab && chioceTab.length) {
      chioceTab.forEach((item) => {
        contentReplaceLang.call(this, chartLangObj, item.saveObj, el.id)
      })
    }
    // if (el.content.alias === 've-grid-normal') {
    //   el.vm.justCellText({ from: 'rollBackChartLangObj', preventRequest: true })
    // }
  })

}

function _generateLangObj(target, key, children, id, fieldList?) {
  let isHideDesc = !this.isMobile && !this.utils.isDataReport && !this.utils.isLargeScreen
  if(isHideDesc){
    if (!target[key] && (key!=='remark' && key !=='remarkTwo')) return
  }else{
    if (!target[key]) return
  }
  const uniqueKeyList = {
    'referenceValueList': 'referenceValueKey',
  }
  if (Array.isArray(target[key])) {
    const sepcialKey = ['dimensionUnit', 'ratioUnit', 'referenceValueList']
    return target[key].map((v, i) => {
      // 用索引匹配可能不准确
      const currentObj = fieldList?.[i] || v
      let uniqueKey = fieldList?.[i]?.keyName || uniqueKeyList[key] || i
      if (sepcialKey.includes(key) && v && v.unit) {
        const labelObj = children.find(item => item.name === `${id}|${v.unit}|${key}`)
        // 指标值单位多语言需去重处理
        if (key === 'dimensionUnit' && labelObj) return
        children.push({
          id: `${id}_${uniqueKey}_${key}`,
          value: v.unit,
          aliasName: this.getDatasetLabel(currentObj),
          name: `${id}|${v.unit}|${key}` // 由于指标值多语言需去重处理，所以相同单位的name相同，替换多语言时，通过name匹配
        })
      } else if (!sepcialKey.includes(key)) {
        children.push({
          id: `${id}_${uniqueKey}_${key}`,
          value: v,
          aliasName: this.getDatasetLabel(currentObj),
          name: `${v}|${uniqueKey}|${key}`
        })
      }
    })
  } else {
    if(isHideDesc){
      if(key==='remark' || key==='remarkTwo'){
        children.push({
          id: `${id}_${key}`,
          value: target[key] || '',
          name: `${target[key]}|${key}`,
          'isHideDesc':true
        })
      }else{
        children.push({
          id: `${id}_${key}`,
          value: target[key] || '',
          name: `${target[key]}|${key}`,
        })
      }
    }else{
      children.push({
        id: `${id}_${key}`,
        value: target[key],
        name: `${target[key]}|${key}`
      })
    }
  }

}

function _setLangObj(target, key, contentKey, arr, id, fieldList?) {
  if (!target[key]) return
  const uniqueKeyList = {
    'referenceValueList': 'referenceValueKey',
  }
  if (Array.isArray(target[key])) {
    const sepcialKey = ['dimensionUnit', 'ratioUnit', 'referenceValueList']
    target[key] = target[key].map((v, i) => {
      // 用索引匹配可能不准确
      let uniqueKey = fieldList?.[i]?.keyName || uniqueKeyList[key] || i
      let labelObj = arr.filter(item => item[contentKey] === `${id}_${uniqueKey}_${key}`)[0]
      // 指标值单位需要特殊处理，相同单位的name相同
      if (sepcialKey.includes(key) && v && v.unit) {
        if (key === 'dimensionUnit') {
          const originalUnit = target.dimensionUnitContent[i]
          labelObj = arr.filter(item => item.name === `${id}|${originalUnit}|${key}`)[0]
        }
        v.unit = labelObj ? (labelObj.aliasName || labelObj.value || v.unit) : v.unit
      } else if (!sepcialKey.includes(key)) {
        v = labelObj ? (labelObj.aliasName || labelObj.value || v) : v
      }
      return v
    })
  } else {
    let labelObj = arr.filter(item => item[contentKey] === `${id}_${key}`)[0]
    target[key] = labelObj ? (labelObj.aliasName || labelObj.value || target[key]) : target[key]
  }
}

function setChartListSettingsLang({ vm, params, chartArr, each, key, keyStr, arr, valKey = '' }) {
  if (!chartArr?.length) return
  let { contentKey, index, chartLangObj } = params
 const { chartAlias }= each.content.chartUserConfig
  let specialKey = index
  if (each.content.mapSchemeSetting?.schemeList?.length) {
    if (each.content.chartUserConfig.childChartAlias !== 've-map-world') {
      specialKey = index + '$ms$' + each.content.chartUserConfig.childChartAlias
    }
  }
  // 因自定义度量是二位数组，所以特殊处理
  if (key === 'customMetricAlias') {
    chartArr.forEach((temp, i) => {
      if (temp && Array.isArray(temp)) {
        temp.forEach((v, k) => {
          const labelObj = arr.filter(item => {
            let keyName = item.name && item.name.split('|')[2]
            const isSame = item[contentKey] === (v && (item.key || item.id)?.includes(key) && item.value ? `${each.id}_${specialKey}_${key}_${keyName}` : `${each.id}_${i}_${specialKey}_${key}`)
            // 字段修改别名需要清空多语言
            if (keyName) {
              let fieldObj = vm.$_getProp(each, keyStr, [])
              let alias = fieldObj[i]?.customMetricList.find(v => v.customMetricAlias === keyName)?.customMetricAlias
              return isSame && alias === keyName
            }
            return isSame
          })[k]
          vm.$set(chartArr[i], k, labelObj ? (labelObj.value || v) : v)
          _getLangObj(labelObj, chartLangObj)
        })
      }
    })
  } else {
    chartArr.forEach((v, i) => {
      const keyIndexStr = keyStr.split('.$index.')
      const labelObj = arr.find(item => {
        let keyName = item.name && item.name.split('|')[2]
        const isSame = item[contentKey] === (key === 'warningAlias' ? `${each.id}_${specialKey}_${v.id}_${v.alias || v.labeName}` : `${each.id}_${i}_${specialKey}_${key}`)
        if (!isSame) return false
        // 字段修改别名需要清空多语言
        if (keyName) {
          if(key === 'mainDimensionMeasureAlias') {
            vm.$set(chartArr[i], 'originalName', keyName)
          }
          const isComposite = chartAlias === CHART_ALIAS_TYPE.VE_COMPOSITE
          let fieldObj = key === 'mainDimensionMeasureAlias' && !isComposite ?  [chartArr[i]] : vm.$_getProp(each, keyIndexStr[0], [])
          let currentObj: any = {}
          if (Array.isArray(fieldObj)) {
            if (keyIndexStr.length === 2) {
              currentObj = vm.$_getProp(fieldObj, `${ i }.${ keyIndexStr[1] }`, {})
            } else if (keyIndexStr.length === 1) {
              currentObj = fieldObj[i]
            }
          } else {
            if (keyIndexStr.length === 2) {
              currentObj = vm.$_getProp(fieldObj, `${ keyIndexStr[1] }`, {})
            } else if (keyIndexStr.length === 1) {
              currentObj = fieldObj
            }
          }
          return (currentObj.webFieldType ? vm.getDatasetLabel(currentObj) : (currentObj?.alias || currentObj?.labeName || currentObj?.originalName)) === keyName
        }
        return true
      })
      if (valKey) {
        const _langVal = labelObj ? (labelObj.value || v[valKey]) : v[valKey]
        if (keyIndexStr.length === 2) {
          let currentObj = vm.$_getProp(chartArr[i], `${ keyIndexStr[1] }`, {})
          if(key === 'mainDimensionMeasureAlias') {
            vm.$set(currentObj, valKey, _langVal)
          }else{
            vm.$set(currentObj, `lang_${valKey}`, _langVal)
          }
        } else {
          if(key === 'mainDimensionMeasureAlias') {
            vm.$set(chartArr[i], valKey, _langVal)
           } else{
            vm.$set(chartArr[i], `lang_${valKey}`, _langVal)
          }
        }
      } else {
        vm.$set(chartArr, i, labelObj ? (labelObj.value || v) : v)
      }
      _getLangObj(labelObj, chartLangObj)
    })
  }
}
