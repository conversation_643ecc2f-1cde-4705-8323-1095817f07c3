<template>
  <div :class="{'chang_lian': isChangLian}"
       :style="{'cursor': commonData.isAdvanceContainerEdit ? 'not-allowed' : 'pointer', ...parameterAreaStyle}">
    <div class="mobileTitle" :style="{ fontSize: `${commonData.isMobileApp ? '20' : '16'}px` }" v-if="isMobile && (!commonData.isPreview || !isShowElment)" >
      <span style="font-weight: 500">{{$t('sdp.views.MoreFilter')}}</span>

      <div
        class="tab-add-style-mobile"
        v-if="isMobile && !commonData.isPreview && !utils.isPcMobile"
      >
        <div @click="() => $refs.tab && $refs.tab.addParamPanel()" class="tab-add-style-mobile-box">
          <span class="icon sdpiconfont icon-sdp-add mobile-icon"></span>
        </div>
        <!-- <div @click="() => $refs.tab && $refs.tab.gangedClick()" class="tab-add-style-mobile-box">
          <span class="icon sdpiconfont icon-sdp-sdp-canshuzujianguanlian mobile-icon"></span>
        </div> -->
      </div>
    </div>
    <div
      :class="['board-params-panel',commonData.isPreview?'separate-line':'normal-status', {'board-params-panel-mobile':isMobile, 'board-params-panel-pc theme-background': !isMobile, 'mobile-params-bgc': !!paramsPanelOpacity && isMobile }]"
      :style="{'pointer-events': commonData.isAdvanceContainerEdit ? 'none' : '', ...setLargeScreenFormatter('parameterAreaBg')}"
      v-show="isGetWebsocketData"
      element-loading-spinner="sdp-loading-gif"
      v-loading="!paramsPanelOpacityLoading"
    >
      <div
        :style="{ opacity: paramsPanelOpacity, height: '100%' }"
        id="paramsPanelBox"
        class="toggle-block"
        v-show="(paramsPanelShow || (commonData.isPreview && !isMobile && !boardInfo.hideComponent)) && paramsPanelOpacity"
      >
        <tab
          @eventBus="eventBus"
          @onExportSuccess="() => $emit('onExportSuccess')"
          @onExportFail="() => $emit('onExportFail')"
          @clearParamGangedSave="clearParamGangedSave"
          :layoutLen.sync="layoutLen"
          :elementComp="elementComp"
          ref="tab"
          v-bind="tabBind"
        >
          <template v-slot:default="slotProps">
            <div style="height: 100%" v-if="slotProps.paramList.active" :key="curTabId">
              <template v-if="!isMobile">
                <sdp-grid-layout
                  :layout="slotProps.layout"
                  :rowHeight="rowHeight"
                  :useCssTransforms="false"
                  :colNum="colNum"
                  :isResizable="false"
                  :isDraggable="!commonData.isPreview"
                  :margin="[0, 0]"
                  ref="sdpGrid"
                  @layout-mounted="layoutMountedHandler"
                  @layout-updated="layoutUpdatedHandler"
                >
                  <div
                    v-for="item in slotProps.paramList.content"
                    :key="item.id"
                    v-show="slotProps.setThemeFullScreenItemShow(item.id)"
                  >
                    <sdp-grid-item
                      v-bind="slotProps.layout.find(e => e.i === item.id) || item.content.layout"
                      :style="{ position: 'absolute', width: '100%' }"
                      @move="moveHandler"
                      @resize="resizeHandler"
                      @moved="movedHandler"
                      @resized="resizedHandler"
                      v-if="setHideParam(item.type)"
                    >
                      <sdp-grid-item-body
                        class="layout-border"
                        :style="{ border: 'none', backgroundColor: 'transparent'}"
                      >
                        <params-panel-item
                          ref="params-panel-item"
                          class="paramcomponent params-style"
                          :param-element="item"
                          :isPreview="commonData.isPreview"
                          @eventBus="eventBus"
                          :edit="canEditParamElement(item)"
                        >
                          <component
                            :is="item.type"
                            class="params-font"
                            :class="getParamsUnifiedStyle(item.type)"
                            :ref="item.id"
                            @eventBus="eventBus"
                            @finishHook="finishHook"
                            @paramsHandleMethod="paramsHandleMethod"
                            :board="board"
                            :beforeHook="beforeHook"
                            :param-element="item"
                            :shopId="locationData.shopIds"
                            :locationData="locationData"
                            :nowStr="nowStr"
                            :boardInfo="boardInfo"
                            :finishParams="finishParams"
                            :conformityApiData="conformityApiData"
                            :paramsType="paramsType"
                            :datasetList="datasetListVal"
                            :elementComp="elementComp"
                            :paramGangedSave="paramGangedSave"
                          />
                          <!--move时禁止点击事件 s-->
                          <div
                            v-if="moveStatus"
                            style="position: absolute;width: 100%;height: 100%;top: 0;z-index: 99999;"
                          ></div>
                          <!--move时禁止点击事件 e-->
                        </params-panel-item>
                      </sdp-grid-item-body>
                    </sdp-grid-item>
                  </div>
                </sdp-grid-layout>
              </template>
              <template v-else>
                <cube-scroll
                  ref="scroll"
                  :class="{ isHide: slotProps.isHide, 'params-panel-bg': !slotProps.paramList.content.filter(item => getParamShow(item)).length }"
                  class="params-panel-component"
                  :style="getParamPanelHeight()"
                >
                  <sdp-grid-layout
                    :layout="slotProps.layout"
                    :rowHeight="rowHeight"
                    :useCssTransforms="false"
                    :colNum="colNum"
                    :isResizable="false"
                    :isDraggable="!commonData.isPreview"
                    :margin="[0, 0]"
                    ref="sdpGrid"
                    @layout-mounted="layoutMountedHandler"
                    @layout-updated="layoutUpdatedHandler"
                  >
                    <div v-for="(item) in slotProps.paramList.content.filter(item => getParamShow(item))"
                         :key="item.id"
                    >
                      <sdp-grid-item
                        v-bind="slotProps.layout.find(e => e.i === item.id) || item.content.layout"
                        @move="moveHandler"
                        @resize="resizeHandler"
                        @moved="movedHandler"
                        @mousedown="gridItemMousedown"
                        @resized="resizedHandler"
                      >
                        <GuidePopover
                        :content="$t('sdp.guide.clickEditToSetTheFilter')"
                        :value="isShowStepTipsByFilterConfigs && item === invalidFilter"
                        :step="stepEntryFilterConfigs"
                        :markPaddingLeft="16"
                        :markPaddingRight="16"
                        :markPaddingTop="0"
                        :markPaddingBottom="0"
                        :arrowOffsetX="-16"
                        :tipsOffsetY="-18"
                      >
                        <sdp-grid-item-body
                          :class="Math.max(...slotProps.layout.map(item => item.y)) === (slotProps.layout.find(e => e.i === item.id) || item.content.layout).y ? '' : 'line'"
                          class="params-style layout-border mobile-box"
                          :style="{ border: 'none', backgroundColor: 'transparent'}"
                        >
                          <el-tools
                            style="line-height: normal;"
                            ref="el-tools"
                            mode="param"
                            :element="item"
                            :visible="isVisible(item.type, isMobile ? 'MOBILE' : 'PC')"
                            :isPreview="commonData.isPreview"
                            :activeTabIds="slotProps.activeTabId"
                            @eventBus="eventBus"
                            :edit="canEditParamElement(item)"
                          />
                          <div
                            class="box"
                            style="width: 100%;position: relative"
                            :class="{disabled: item.type === TYPE_PARAM_ELEMENT.LOCATION_NEW && !needShopId}"
                          >
                            <p
                              @click="clickEl(item, 'content')"
                              class="box-content border-bottom-1px"
                            >{{item.elName}}
                            </p>
                            <i v-if="item.isHideElement" class="icon-sdp-Hyincang"></i>
                            <div
                              class="box-icon"
                              @click="clickEl(item, 'icon')"
                            >
                              <i class="el-icon-arrow-right"></i>
                            </div>
                          </div>
                          <!--move时禁止点击事件 s-->
                          <div
                            v-if="moveStatus"
                            style="position: absolute;width: 100%;height: 100%;top: 0px;z-index: 99999;"
                          ></div>
                          <!--move时禁止点击事件 e-->
                        </sdp-grid-item-body>
                        </GuidePopover>
                      </sdp-grid-item>
                    </div>
                  </sdp-grid-layout>
                </cube-scroll>
              </template>
            </div>
          </template>
        </tab>
          <!--tab标签页 v-show="!isThemeFullScreen"e-->
        <div class="params-panel__row" :class="[{ 'staic-row': isAnimalCss }]" ref="paramsButton">
          <div style="padding-bottom: 0px;position: relative;margin: 0;padding-left: 15px;"
            class="operate-btn sdp-run-theme-button params-panel__row params-style"
            v-if="!isMobile"
          >
            <div :style="{ opacity: Number(isParameterBtnInit) }" v-if="!isThemeFullScreen || layoutLen">
              <template v-if="activeBtnOptions && activeBtnOptions.length">
                <el-button v-for="(btnEl, btnIndex) in activeBtnOptions"
                            ref="btnEl"
                           :key="btnEl.id"
                           style="display: inline-block"
                           v-show="btnEl.active"
                           class="btn-run"
                           :id="btnEl.id"
                           :class="isChangLian ? btnEl.id === 'Run' ? 'platformFlagRun' : 'platformFlagCancel' : ''"
                           :style="btnSettingStyle(btnEl, btnIndex)"
                           @mouseenter.native="changeBtnHover(btnEl, 'onMouseover', true)"
                           @mouseleave.native="changeBtnHover(btnEl, 'onMouseover', false)"
                           @focus.native="changeBtnHover(btnEl, 'isActive', true, 'focus')"
                           @blur.native="changeBtnHover(btnEl, 'isActive', false)"
                           @click="btnTargetFn(btnEl.targetFn);changeBtnHover(btnEl, 'isActive', true, 'focus')"
                >{{btnEl.reName[sdpLangcode] || $t(`sdp.views.${btnEl.id}`)}}</el-button>
              </template>
              <template v-else>
                <el-button  ref="btnEl"
                  :class="isChangLian ? 'btn-query platformFlagRun' : 'btn-run'"
                  @click="handRun()"
                >{{isChangLian ? $t('sdp.views.Query') : $t('sdp.views.Run')}}</el-button>
                <el-button  ref="btnEl"
                  :class="isChangLian ? 'platformFlagCancel' : ''"
                  class="btn-cancel"
                  @click="Cancel"
                >{{$t('sdp.views.Cancel')}}</el-button>
              </template>
            </div>
            <div v-if="!isMobile && !commonData.isPreview && activeBtnOptions.length" class="tool-list">
              <img :src="editImg" @click="btnSettingShow = true" style="width: 10px;height: 10px;float: right">
            </div>
          </div>
          <!-- 移动端操作按钮 -->
          <template v-else>
            <!-- <div class="moblie-botton-box">
              <el-button v-if="appPreviewParameter" class="sdp-btn-back" @click="exitComponentByParamsPanel" >
                <i class="icon sdpiconfont icon-sdp-zuojiantou"></i>
              </el-button>

              <el-button
                :class="commonData.isMobileApp?'sdp-btn-run-pc':'sdp-btn-run-m'"
                @click="runMobile()"
              >{{ $t('sdp.views.Run') }}</el-button>
            </div> -->

            <div class="moblie-botton-box">
              <el-button class="sdp-btn-back" v-if="appPreviewParameter" @click="exitComponentByParamsPanel">
                <i class="icon sdpiconfont icon-sdp-zuojiantou"></i>
              </el-button>
              <button type="button" :style="appPreviewParameter ? {} : { margin: 0 }" class="moblie-botton" @click="runMobile()">
                {{ $t('sdp.views.Run') }}
              </button>
            </div>
          </template>
          <div
            class="calendar-style"
            v-show="paramsPanelShow && isShowCalendar"
            :key="calendar.id"
            v-if="!isMobile"
          >
            <params-panel-item
              ref="params-panel-items"
              class="paramcomponent"
              style="padding-top: 12px;"
              :param-element="calendar"
              :isPreview="commonData.isPreview"
              @eventBus="eventBus"
            >
              <component
                :is="calendar.type"
                :ref="calendar.id"
                :board="board"
                :nowStr="nowStr"
                @eventBus="eventBus"
                @finishHook="finishHook"
                @paramsHandleMethod="paramsHandleMethod"
                :beforeHook="beforeHook"
                :param-element="calendar"
                :paramsType="paramsType"
                :locationDatas="locationData"
                :datasetList="datasetListVal"
                :conformityApiData="conformityApiData"
              />
              <!--日历快选-->
            </params-panel-item>
          </div>
        </div>
      </div>
      <div :class="['toggle-panel', 'sdp-bottom-theme-boder',paramsPanelShow ? '':className, { 'staic-icon': isAnimalCss }]" v-if="!isMobile">
        <div @mouseenter="hoverParams"
          :style="{'cursor': commonData.staticParamsPanel && commonData.isPreview ? 'not-allowed' : 'pointer',
          'pointer-events': 'painted'}"
        >
          <ParamsToggle
            @mouseenter="keepHoverParams"
            :toggle="toggle"
            :isMoveDisabled.sync="isMoveDisabled"
            @hiddenParams="paramsPanelShowToggle"
            v-model="paramsPanelShow"
          />
        </div>
      </div>
    </div>
    <!-- 移动端显示 -->
    <div v-if="isMobile">
      <template v-for="el in activeParamsPanel">
        <cube-page
          :isShow="el.id === activeMobileElId"
          :title="title"
          :key="el.id"
          :type="el.type"
          class="cube-page"
          :isIconQuick="isIconQuick"
          :isHeaderShow="!isIconQuick"
          :class="{
            // 'sdp-quick-page': isIconQuick || [TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR, TYPE_PARAM_ELEMENT.LOCATION_NEW].includes(el.type),
            // 'sdp-transparent-page': [TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR, TYPE_PARAM_ELEMENT.LOCATION_NEW].includes(el.type),
            'sdp-params_bussiness-calendar': [TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR].includes(el.type)
          }"
          :isHeaderBottomLine="el.type !== TYPE_PARAM_ELEMENT.TAGNEW"
          @eventBus="eventBus"
          @back="back"
        >
          <component
            :is="el.type"
            :visible="el.id === activeMobileElId"
            :ref="el.id"
            @eventBus="eventBus"
            @finishHook="finishHook"
            @paramsHandleMethod="paramsHandleMethod"
            :clickMobileType="clickMobileType"
            :board="board"
            :beforeHook="beforeHook"
            :param-element="el"
            :nowStr="nowStr"
            @cube-calendar-popup="cubePopupCalendarEvent"
            :shopId="locationData.shopIds"
            :locationData="locationData"
            :boardInfo="boardInfo"
            :paramsType="paramsType"
            :datasetList="datasetListVal"
            :finishParams="finishParams"
            :conformityApiData="conformityApiData"
            :paramGangedSave="paramGangedSave"
            :elementComp="elementComp"
            :appPreviewParameter="appPreviewParameter"
          />
        </cube-page>
      </template>
    </div>
    <el-dialog
      :title="`${$t('sdp.views.btnSet')}`"
      :visible.sync="btnSettingShow"
      v-if="btnSettingShow && activeBtnOptions.length"
      :custom-class="`sdp-dialog ${getCurrentThemeClass()}`"
      :append-to-body="true"
      :close-on-click-modal="false"
      width="980px"
      class="sdp-paramsetting param-font sdp-title-start"
    >
      <btn-setting :boardBtnOptions.sync="boardBtnOptions" :activeBtnType.sync="boardInfo.activeBtnType"
                   ref="btnSetting" :sdpLangcode="sdpLangcode" :isChangLian="isChangLian"></btn-setting>
      <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handelBtnSetting" class="btn-width btn-sure-color">{{$t('sdp.button.ensure')}}</el-button>
      <el-button @click="btnSettingShow = false" class="btn-width btn-cancel-color">{{$t('sdp.button.cancel')}}</el-button>
    </span>
    </el-dialog>
  </div>
</template>
<script>
import tab from './tab.vue'
import paramsblock from './paramsblock/index.vue'
import ParamsToggle from '../components/ParamsToggle'
import EventData from 'packages/assets/EventData'
import eventBus from 'packages/assets/eventBus'
import {
  CREATE_TOAST_TIME,
  EVENT_DATA_PARAMS,
  GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS,
  PARAMS_PANEL_TYPE,
  RUN_TYPE,
  STATIC_BASE_PATH
} from 'packages/assets/constant'
import { parmasLayout, TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import ParamsPanelItem from './ParamsPanelItem'
import ParamComponents from './paramElement/ParamComponents'
import { componentNameList } from './paramElement/ParamElContent'
import { SdpGridItem, SdpGridItemBody, SdpGridLayout } from './../../../common/sdpGrid/index'
import { FINISH_TYPE, HAND_RUN, TYPE_PARAM_ELEMENT } from './utils/constants'
import { getParamGanged, typeRunObject } from '../utils'
import CubePage from '../paramsMobile/cube-page.vue'
import ElTools from './ElTools.vue'
import { EVENT_BUS, GET_PLAT_VALUE_BY_CODE_PID_TYPE } from '../constants'
import RunHandler from './utils/RunHandler'
import emitter from 'packages/assets/emitter'
import btnSetting from '../../settingPanel/components/btnSetting'
import { TYPE_SUPERSETTING } from 'packages/base/board/displayPanel/boardLanguage'
import { layoutFullScreenSort, layoutMobileSort } from './utils/utils'
import { largeScreenFormatter } from '../components/utils'
import Theme from 'packages/assets/newTheme/utils'
import { kanbanGuideStepEntryFilterConfigsMixinWithMobile } from 'packages/base/KanbanGuide'
import paramElementDisableMixins from 'packages/base/board/displayPanel/mixins/paramElementDisable'
import { PC_MOBILE_DISABLED_PARAM } from '../../pcMobile/constans'
import { RECORD_OPERATE_TYPE } from '../../mixins/boardRecord'
import { OnlyField } from 'packages/base/board/displayPanel/datasetReplace/index'
import calcFns from '../../../grid/helpers/constants/calcFns'

export default {
  inject: {
    sdpBus: {
      default: () => () => {
      }
    },
    requestAppEvent: {
      default: () => () => {
      }
    },
    boardRecord: { default: () => () => {} },
    commonData: { default: {} },
    utils: { default: {} },
    themeData: { default: {} },
    runHandler: {},
    datasetList: { default: () => () => [] },
    filterServerNeedParams: { default: () => () => [] },
    paramsPanelTypeRef: { type: Object, default() { return {} } },
    getBoardUnderIdElement: { default: () => (name) => { return document.getElementById(name.slice(1)) } },
    isHorizontal: { default: () => false },
    // 环境多语言，外界传入（不支持的变成en）
    sdpLangcode: { default: 'en' },
    onErr: { default: () => () => {} },
    getCurrentThemeClass: { default: () => () => '' },
    getActiveDisplayPanel: { default: () => () => '' }
  },
  mixins: [paramElementDisableMixins, emitter, kanbanGuideStepEntryFilterConfigsMixinWithMobile],
  name: 'paramsPanel',
  components: {
    tab,
    ParamsToggle,
    paramsblock,
    ParamsPanelItem,
    SdpGridLayout,
    SdpGridItem,
    SdpGridItemBody,
    CubePage,
    ElTools,
    btnSetting,
    ...ParamComponents
  },
  provide: () => {
    return {
      newParamElement: {
        _data: [],
        has(id) {
          return this._data.some(str => str === id)
        },
        delHas(id) {
          const status = this._data.some(str => str === id)
          this._data = this._data.filter(str => str !== id)
          return status
        },
        set(id) {
          this._data.push(id)
        }
      }
    }
  },
  props: {
    appPreviewParameter: Boolean,
    isPreview: {
      type: Boolean,
      default: false
    },
    isShowElment: {
      type: Boolean,
      default: false
    },
    boardInfo: {
      type: Object
    },
    datasetListFromProps: {
      type: Array
    },
    languageAndCurrency: {
      type: Object
    },
    isEnterPriseCurrency: {
      type: Boolean
    },
    elList: {
      type: Array,
      required: true
    },
    paramsPanelList: {
      type: Array,
      required: true
    },
    orderParamsList: {
      type: Array
    },
    dynamicTags: {
      type: Array,
      required: true,
    },
    beforeHook: {
      type: Function
    },
    paramsType: {
      type: String,
    },
    // 预览类型
    previewType: {
      type: String,
      default: '',
    },
    nowStr: {
      type: String,
    },
    toggle: {
      type: Number,
      required: true
    },
    levelData: {
      type: Object,
      default: () => ({})
    },
    boardStatus: {
      type: String,
    },
    activeTabIds: {
      type: String,
    },
    conformityApiData: {
      type: Object
    },
    paramsPanelHeight: {
      type: Number,
    },
    isFinish: {
      type: Boolean
    },
    dynamicTagsRun: {
      type: Object
    },
    getWebsocketData: {
      type: Object,
      default: () => ({
        reqElementData: {},
        switch: false,
      })
    },
    parent: {
      type: Object
    },
    paramsDataItem: {
      type: Object,
    },
    paramsPanelShowSync: {
      type: Boolean,
      default: true
    },
    storeSet: {
      type: Object,
      default: () => ({})
    },
  },
  data () {
    return {
      watchParamsPanelList: false,
      curTabId: 0, // 当前设置id
      clickElUnwatchFn: null,
      layoutLen: 0,
      componentNameList,
      defaultEventData: {
        source: 'paramsPanel'
      },
      tabRef: null,
      rowHeight: 2,
      colNum: GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS,
      maxRows: 1,
      boardOld: {
        elList: this.elList,
        paramsPanelList: this.paramsPanelList
      },
      finishParams: {},
      finishResult: false,
      autoRunCallback: null,
      autoRun: false,
      moveStatus: false,
      activeMobileElId: '',
      clickMobileType: '',
      messageData: {},
      locationData: {
        shopIds: [],
        outletIds: [],
        id: '',
        componentStatus: '',
        type: '',
      },
      isMoveDisabled: false, // 误触区域
      hoverDisabled: false,
      TYPE_PARAM_ELEMENT,
      // isFinishResult: true,
      isToggle: true,
      metricFinishList: [],
      editImg: STATIC_BASE_PATH.images + 'sdp-params-edit.png',
      btnSettingShow: false,
      paramGangedSave: {},
      elementComp: [],
      isParameterBtnInit: false,
      oldLayout: [],
      cantEditParamElementList: [],
      displayPanelToggleFresh: false
    }
  },
  computed: {
    board() {
      return {
        elList: this.elList,
        paramsPanelList: this.paramsPanelList
      }
    },
    datasetListVal() {
      if (this.utils.isPcMobile) {
        return this.datasetListFromProps
      }
      return this.datasetList
    },
    isChangLian () {
      return this.commonData.platformFlag === 'SCM_PID'
      // return ['p00042', 'p00008'].includes(this.commonData.pid)
    },
    getParamShow (item) {
      return function (item) {
        let pcMobileLimit = false
        if (this.utils.isDataReport && this.utils.isPcMobile) {
          pcMobileLimit = PC_MOBILE_DISABLED_PARAM.includes(item.type)
        }
        return (!this.commonData.isPreview || !item.isHideElement) &&
          !pcMobileLimit &&
          item.type !== 'CalendarQuick' && item.type !== 'DateQuickOperation'
        // (item.type !== 'CalendarQuick'&& item.type !== 'DateQuickOperation')
      }
    },
    tabBind() {
      return {
        paramsDataItem: this.paramsDataItem,
        nowStr: this.nowStr,
        displayRef: this.parent,
        elList: this.elList,
        paramsPanelList: this.paramsPanelList,
        orderParamsList: this.orderParamsList,
        languageAndCurrency: this.languageAndCurrency,
        isEnterPriseCurrency: this.isEnterPriseCurrency,
        dynamicTags: this.dynamicTags,
        boardInfo: this.boardInfo,
        previewType: this.previewType,
        levelData: this.levelData,
        finishResult: this.finishResult,
        paramsType: this.paramsType,
        boardStatus: this.boardStatus,
        activeTabIds: this.activeTabIds,
        conformityApiData: this.conformityApiData,
        isFinish: this.isFinish,
        locationData: this.locationData,
        isThemeFullScreen: this.isThemeFullScreen,
        storeSet: this.storeSet }
    },
    paramsPanelOpacity() {
      const isQuick = [
        PARAMS_PANEL_TYPE.QUICK,
        PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_LOCATION,
        PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_BUSSINESS_CALENDAR,
      ].includes(this.paramsPanelTypeRef.value)
      return Number(!isQuick)
    },
    paramsPanelOpacityLoading() {
      return true
      return this.paramsPanelTypeRef.value !== PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_LOCATION
    },
    setLargeScreenFormatter (formatter) {
      return function (formatter) {
        let activePanel = this.paramsPanelList.find(item => item.active)
        const canvasBg = activePanel?.[formatter]
        if (this.isMobile || !canvasBg) return {}
        return largeScreenFormatter(activePanel, formatter, this.utils.themeParameters)
      }
    },
    isGetWebsocketData() {
      if (this.isThemeFullScreen && this.boardInfo.openBoardTab) {
        return this.getWebsocketData.switch
      }
      return true
    },
    tabActiveId() {
      let id = this.tabRef ? this.tabRef.activeTabId : this.activeTabIds
      return id
    },
    title () {
      const el = this.activeParamsPanel.find(el => this.activeMobileElId === el.id) || {}
      return el.elName || ''
    },
    activeParamsPanel () {
      let a = this.paramsPanelList.find(item => item.id === this.tabActiveId)
      if (this.utils.isPcMobile && a) {
        const paramList = this.storeSet?.mobileSet?.paramList || {}
        const setIds = paramList[a.id] || []
        return a.content.filter(e => e.type !== TYPE_PARAM_ELEMENT.CONTAINER_SELECT && setIds.includes(e.id))
      }
      return a ? a.content.filter(e => e.type !== TYPE_PARAM_ELEMENT.CONTAINER_SELECT) : []
    },
    api () {
      return this.utils.api || function () {}
    },
    isShowCalendar () {
      // 大屏判断是否开启了日历快选
      if (this.paramsDataItem && !this.isScreen) {
        const { open = false } = this.paramsDataItem.content[this.calendar.id] || {}
        return open
      }
      return this.setElementScreenShow(this.calendar.id) && true
    },
    calendar () {
      let quickArr = [TYPE_PARAM_ELEMENT.LOCATION_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION, TYPE_PARAM_ELEMENT.CALENDAR_QUICK]
      let result = this.activeParamsPanel.find(el => quickArr.includes(el.type)) || {}
      return result
    },
    isMobile () {
      return this.utils.isMobile
    },
    isScreen () {
      return this.utils.isScreen
    },
    isAnimalCss() {
      return this.commonData.isPreview && !this.isMobile && !this.paramsPanelShow
    },
    className() {
      return !this.commonData.isPreview && !this.paramsPanelShow ? 'panelH' : 'panelH1'
    },
    // 层级视角是否有物业
    needShopId() {
      const { viewLevelList = [] } = this.commonData.getViewLevelData()
      return viewLevelList.some(({ levelType }) => levelType === '1')
    },
    isOpenAutoRefresh() {
      return this.boardInfo.autoRefreshData.isAutoRefresh && this.commonData.isPreview
    },
    isThemeFullScreen() {
      return this.commonData.isPreview && this.themeData.themeFullScreen && this.boardInfo.screenModeDate.screenMode
    },
    isThemeFullScreenAndOpenBoardTab() {
      return this.isThemeFullScreen && this.boardInfo.openBoardTab
    },
    isIconQuick() {
      return [
        PARAMS_PANEL_TYPE.QUICK,
        PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_BUSSINESS_CALENDAR,
        PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_LOCATION
      ].includes(this.paramsPanelTypeRef.value)
    },
    isNeedResetParamsPanelType() {
      return !(this.appPreviewParameter || this.activeMobileElId)
    },

    paramsPanelShow: {
      get() {
        return this.paramsPanelShowSync
      },
      set(val) {
        this.$emit('update:paramsPanelShowSync', val)
      }
    },
    hideElement() {
      let activePanel = this.paramsPanelList.find(item => item.active)
      let hideElement = activePanel.content.filter(item => {
        return !item.isHideElement
      })
      return !this.commonData.isPreview || hideElement.length
    },
    activeBtnOptions () {
      return this.boardBtnOptions[this.boardInfo?.initBtnType || 'singleData'] || []
    },
    boardBtnOptions() {
      return this.boardInfo?.boardBtnOptions || {}
    }, // 参数组件按钮设置
    // 按钮样式设置
    btnSettingStyle (btnEl, index) {
      let themeType = this.themeData.themeType
      let lastEl = index - 1 || 0
      return function (btnEl, index) {
        return {
          ...btnEl.fontStyle,
          backgroundColor: (btnEl.onMouseover || btnEl.isActive ? '' : btnEl.btnBgColor[themeType] || Theme.getThemeVariableValue(themeType, '--sdp-rxbg')) + '!important',
          borderColor: (btnEl.onMouseover || btnEl.isActive ? '' : btnEl.btnBorderColor[themeType] || Theme.getThemeVariableValue(themeType, '--sdp-rxbc')) + '!important',
          color: (btnEl.onMouseover || btnEl.isActive ? '' : btnEl.fontColor[themeType] || Theme.getThemeVariableValue(themeType, '--sdp-rxwz')) + '!important',
          height: 'auto',
          padding: this.isChangLian ? '10px 20px' : '',
          minHeight: this.isChangLian ? '40px' : '20px',
          width: 'auto',
          minWidth: this.isChangLian ? '70px' : '',
          marginLeft: !this.activeBtnOptions[lastEl].active ? '0px' : ''
        }
      }
    },
    activeDisplayPanel() {
      if (this.getActiveDisplayPanel) {
        return this.getActiveDisplayPanel()
      }
      return ''
    },
    parameterAreaStyle() {
      if (this.isMobile) return { height: '100%' }
      let themeType = this.themeData.themeType
      const { borderLine, splitLine, borderRadius } = this.boardInfo.globalParameterAreaStyle?.[themeType] || {}
      let result = {}
      if (borderLine) {
        result = {
          [`--color-ParameterBorderLineType`]: borderLine.lineType,
          [`--color-ParameterBorderLineWidth`]: `${borderLine.lineWidth}px`,
          [`--color-ParameterBorderLineColor`]: borderLine.color
        }
      }
      if (splitLine) {
        result = {
          ...result,
          [`--color-ParameterSplitLineType`]: splitLine.lineType,
          [`--color-ParameterSplitLineWidth`]: `${splitLine.lineWidth}px`,
          [`--color-ParameterSplitLineColor`]: splitLine.color
        }
      }
      if (borderRadius) {
        result = {
          ...result,
          [`--color-ParameterBorderRadius`]: `${borderRadius}px`
        }
      }
      return result
    }
  },
  watch: {
    finishResult(val) {
      this.finishResultState(val)
    },
    isHorizontal() {
      console.log('isHorizontal', this.isHorizontal)
    },
    tabActiveId() {
      this.clearClickElUnwatchFn()
    },

    paramsPanelShow (val, old) {
      if (val !== old) {
        this.parent.setSlideInTransition?.()
      }
      if (val) {
        this.$nextTick(() => {
          this.$refs.sdpGrid && this.$refs.sdpGrid.onWindowResize()
        })
      } else {
        val !== old && this.startFullScreenCarousel()
      }
    },
    'commonData.isPreview' (val) {
      if (!val) {
        this.$nextTick(() => {
          this.$refs.sdpGrid && this.$refs.sdpGrid.onWindowResize()
        })
      } else {
        this.$nextTick(() => {
          this.commonData.staticParamsPanel && this.$refs.sdpGrid && this.$refs.sdpGrid.onWindowResize()
        })
      }
      this.back()
    },
    isGetWebsocketData(val) {
      this.$nextTick(() => {
        this.commonData.staticParamsPanel && this.$refs.sdpGrid && this.$refs.sdpGrid.onWindowResize()
      })
    },
    activeParamsPanel(val) {
      this.setFilterListByGuide(val)
      let arr = val.map(el => el?.id).filter(e => e)
      Object.keys(this.finishParams).forEach(id => {
        if (!arr.includes(id)) {
          this.$delete(this.finishParams, id)
        }
      })
      // this.clearParamGangedSave()
      // 处理dom延迟
      this.$nextTick(() => {
        this.elementComp = this.activeParamsPanel.map(item => Array.isArray(this.$refs[item.id]) ? this.$refs[item.id][0] : this.$refs[item.id]).filter(e => e)
      })
    },
    'themeData.themeFullScreen'(val) {
      if (val) {
        this.$set(this.themeData, 'infoBarObj', {})
        this.handlerCollectParamsPanelData()
      }
    },

    isNeedResetParamsPanelType(bool) {
      bool && (this.paramsPanelTypeRef.value = PARAMS_PANEL_TYPE.NORMAL)
    },
    // 'activeBtnOptions.length'(val) {
    //   if (val) this.addBtnEventListener()
    // },
    activeDisplayPanel: {
      handler(val, oldVal) {
        if (!this.utils.isDataReport || val === oldVal || !val || this.displayPanelToggleFresh || !this?.$parent?.$parent?.hasPcMobile || !this.activeParamsPanel.length) return
        if (val === 'PC' && this.utils.isPcMobileEdit) return
        if (val === 'MOBILE' && !this.utils.isPcMobileEdit) return
        this.displayPanelToggleFresh = true
      },
      immediate: true
    }
  },
  errorCaptured (errer, vm, info) {
    const { paramElement } = vm
    if (paramElement) {
      const { id } = paramElement
      if (this.activeParamsPanel.some(el => el.id === id)) {
        this.finishHook({ id, type: FINISH_TYPE.ERROR })
      }
    }
  },
  mounted() {
    this.paramsPanelShowToggle = this.$_debounce(this.paramsPanelShowToggle, 500)
    this.isAutoRun = this.$_debounce(this.isAutoRun, 500)
    this.toolTipMessage = this.$_throttle(this.toolTipMessage, 500)
    this.finishResultState = this.$_debounce(this.finishResultState, 500)
    this.tabRef = this.$refs.tab
    this.sdpBus.$on(EVENT_BUS.SET_LOCATION_DATA, this.setLocationData)
    this.sdpBus.$on(EVENT_BUS.GET_PARAM_COLLECT_REQUEST, this.collectRequest)
    this['sdpBus'].$on(EVENT_BUS.PARAM_ELEMENT_HIDE_EDIT, this.paramElementHideEdit)

    // Bugfix: 69313 解决第一次切移动端状态为false的情况
    if (this.activeDisplayPanel === 'MOBILE' && !this.displayPanelToggleFresh) {
      this.displayPanelToggleFresh = true
    }
    this.refreshScroll = this.$_debounce(this.refreshScroll)
  },
  destroyed () {
    this.sdpBus.$off(EVENT_BUS.SET_LOCATION_DATA, this.setLocationData)
    this.sdpBus.$off(EVENT_BUS.GET_PARAM_COLLECT_REQUEST, this.collectRequest)
    this['sdpBus'].$off(EVENT_BUS.PARAM_ELEMENT_HIDE_EDIT, this.paramElementHideEdit)
    // this.removeBtnEventListener()
  },
  methods: {
    eventBus,
    finishResultState(val) {
      console.log(val, 'finishResultState')
      val && this.$emit('finishResultState', val)
    },
    paramElementHideEdit(data) {
      // console.log('不能编辑的参数组件列表: ', data)
      this.cantEditParamElementList = data
    },
    canEditParamElement(item) {
      // console.log('检测参数组件是否能够编辑: ', item.type)
      return !this.cantEditParamElementList.includes(item.type)
    },
    setInitParameterBtn() {
      this.isParameterBtnInit = true
    },
    clearParamGangedSave() {
      this.paramGangedSave = {}
    },
    // 是否隐藏参数组件
    setElementScreenShow(id) {
      if (!this.commonData.isPreview) return true
      let activePanel = this.paramsPanelList.find(item => item.active)
      let curItem = activePanel?.content?.find(item => {
        return item.id === id
      })
      let { isHideElement = false } = curItem || {}
      return !isHideElement
    },
    getParamPanelHeight() {
      // let { isHorizontal, isPreview } = this.commonData
      // let mobileElementPanel = document.getElementById('mobileParamsPanel')?.offsetHeight || document.getElementById('mobileElementPanel')?.offsetHeight
      // let mobileHeight = mobileElementPanel || 0
      // const paramsBtn = 44 + (mobileHeight * 0.05)
      // const tabHeight = document.getElementsByClassName('tab-border-content')?.[0]?.offsetHeight || 0
      // let height = mobileHeight - paramsBtn - tabHeight - 20
      // height = height > 0 ? height.toFixed(0) : isHorizontal && isPreview ? 240 : 630
      setTimeout(() => {
        this.refreshScroll()
      })
      return {
        // height: isHorizontal && isPreview ? '240px' : '630px',
        height: `100%`
      }
    },
    // 重新计算cube-scroll的滚动距离
    refreshScroll () {
      console.log('refreshScroll')
      // 编辑时可以鼠标滚动
      this.$nextTick(() => {
        const scroll = this.$refs['scroll']
        if (scroll) {
          scroll.refresh()
        }
      })
    },
    getParamsUnifiedStyle(type) {
      if ([TYPE_PARAM_ELEMENT.DATE_TYPE, TYPE_PARAM_ELEMENT.CONTAINER_SELECT, TYPE_PARAM_ELEMENT.Omit_Zero_Total, TYPE_PARAM_ELEMENT.TEXTBOX, TYPE_PARAM_ELEMENT.SEARCH, TYPE_PARAM_ELEMENT.PEOPLE_LAYER].includes(type)) {
        return 'params-unified-style-row'
      }
      return 'params-unified-style-column'
    },
    exitComponentByParamsPanel() {
      this.$emit('eventBus', new EventData({
        target: ['displayPanelMobile', 'displayPanelPcToMobile'],
        targetFn: 'exitComponent',
      }))
    },
    back() {
      this.activeMobileElId = ''
      this.$emit('back')
    },
    // 初始化run值
    initFinishResult() {
      this.finishResult = !this.activeParamsPanel.length
    },
    handlerCollectParamsPanelData() {
      setTimeout(() => {
        if (!this.componentsRemind()) return
        this.setloading({ loading: true })
        if (this.setFinishResult(this.paramsType)) {
          this.collectParamsPanelData()
        } else {
          this.autoRun = true
        }
      }, 50)
    },
    // 收集参数组件
    collectParamsPanelData(data = {}) {
      this.$nextTick(() => {
        if (this.getWebsocketData.switch) return void '防止多次调用'
        const { list } = data
        const reqElementData = this.getWebsocketData.reqElementData
        const preParamsPanelList = this.paramsPanelList.find(item => item.active) || {}
        const { id = '', content } = list || preParamsPanelList
        const setReqElementData = () => {
          const preContent = content.filter(e => e.type !== TYPE_PARAM_ELEMENT.CONTAINER_SELECT && e.type !== TYPE_PARAM_ELEMENT.METRIC)
          let data = {}
          if (preContent.length) {
            const paramsId = this.parent.filterParamsId({
              type: this.paramsType
            })
            const paramsRequestCallback = this.filterServerNeedParams.filterServerNeedParams(this.elList)
            data = this.collectRequest({ paramsId, refreshObj: false, paramsRequestCallback })
          }
          this.$set(reqElementData, id, data)
        }
        if (this.boardInfo.openBoardTab) {
          let index = this.paramsPanelList.findIndex(el => el.id === id)
          if (!Object.keys(reqElementData).includes(id)) {
            setReqElementData()
            const nextParamsPanelList = this.paramsPanelList[++index % this.paramsPanelList.length]
            const nextContent = nextParamsPanelList.content.filter(e => e.type !== TYPE_PARAM_ELEMENT.CONTAINER_SELECT && e.type !== TYPE_PARAM_ELEMENT.METRIC)
            if (nextContent.length) {
              // 下一个切换的tab是当前tab的话跳过点击
              if (nextParamsPanelList.id === preParamsPanelList.id) {
                this.$set(this.getWebsocketData, 'switch', this.themeData.themeFullScreen || this.isOpenAutoRefresh)
              } else {
                this.$set(preParamsPanelList, 'runtype', this.paramsType)
                this.$refs.tab.onPanelChange({
                  data: nextParamsPanelList
                })
                this.autoRun = true
              }
            } else {
              this.collectParamsPanelData({ list: nextParamsPanelList })
            }
          } else {
            this.$set(this.getWebsocketData, 'switch', this.themeData.themeFullScreen || this.isOpenAutoRefresh)
          }
        } else {
        // 拿当前选中的参数组件数据
          setReqElementData()
          this.$set(this.getWebsocketData, 'switch', this.themeData.themeFullScreen || this.isOpenAutoRefresh)
        }
      })
    },
    setHideParam(type) {
      return ![TYPE_PARAM_ELEMENT.LOCATION_QUICK, TYPE_PARAM_ELEMENT.CALENDAR_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION].includes(type)
    },
    cubePopupCalendarEvent(data) {
      this.$emit('cube-calendar-popup', data)
    },
    onWindowResize () {
      this.$nextTick(() => {
        this.$refs.sdpGrid && this.$refs.sdpGrid.onWindowResize()
      })
    },
    closeCalendarss() {
      // 关闭日历组件picker
      var activeTab = this.paramsPanelList.find(item => item.active).content || []
      var paramList = activeTab.filter(item => item.type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR || item.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR) || []
      paramList.forEach(item => {
        if (this.$refs[item.id] && this.$refs[item.id][0].$refs[item.id] && this.$refs[item.id][0].$refs[item.id].picker) {
          this.$refs[item.id][0].$refs[item.id].picker.hide()
        }
      })
    },
    // changeIcon (type) {
    //   // const arr = [TYPE_PARAM_ELEMENT.LOCATION_NEW, TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR, TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR]
    //   const arr = IS_VISIBLE_HEADER_REF.value ? [TYPE_PARAM_ELEMENT.LOCATION_NEW] : []
    //   return arr.includes(type)
    // },
    calendarSkip(data) {
      // console.log(data)
      let item = data.data.param
      // this.clickEl(param, 'icon')
      this.activeMobileElId = item.id
      item && this.$set(item, 'mobileType', false)
      // const ref = this.$refs[item.id][0].$refs[item.id]
      // if (ref) {
      //   await ref.oneCalendar()
      //   await ref.mountedMobile()
      // }
    },

    clearClickElUnwatchFn() {
      if (typeof this.clickElUnwatchFn === 'function') {
        this.clickElUnwatchFn()
        this.clickElUnwatchFn = null
      }
    },

    setClickElUnwatchFn(item, type) {
      this.clearClickElUnwatchFn()

      if (this.isMobile && item.type === TYPE_PARAM_ELEMENT.LOCATION_NEW && this.finishParams[item.id] === FINISH_TYPE.AWAIT) {
        this.clickElUnwatchFn = this.$watch('finishResult', function () {
          this.clickEl(item, type)
        })
      }

      return typeof this.clickElUnwatchFn === 'function'
    },

    // tips：调整时需要对 goToQuickBarComponent 方法进行调整
    clickEl (item, type) {
      if (this.isIconQuick) return
      this.paramsPanelTypeRef.value = PARAMS_PANEL_TYPE.NORMAL

      if (this.setClickElUnwatchFn(item, type)) return

      this.clickMobileType = type
      if (item.type === TYPE_PARAM_ELEMENT.LOCATION_NEW || item.type === 'search') {
        // 抛出事件
        let postData = { type: 'params', data: {} }
        this.requestAppEvent(postData)
      } else {
        // 抛出事件
        if (item.type === TYPE_PARAM_ELEMENT.SELECT_ORDINARY) {
          this.$refs[item.id][0] && this.$refs[item.id][0].reSetOptions()
        }
        if (item.type === TYPE_PARAM_ELEMENT.TAGNEW) {
          this.$refs[item.id][0] && this.$refs[item.id][0].reSetOptions()
        }
        let postData = { type: 'closeMask', data: {} }
        this.requestAppEvent(postData)
      }
      if (item.type === 'bussinessCalendar' || item.type === 'financialCalendar') {
        if (!item.content.quick || (this.isMobile && item.content.quick && !item.content.calendarshow)) {
          // if (item.type === 'financialCalendar') this.$refs[item.id][0].$refs[item.id] && this.$refs[item.id][0].$refs[item.id].selectedShow()
          this.broadcast(item.id, 'handOpenPicker')
        }
      } else {
        this.activeMobileElId = item.id
      }

      setTimeout(() => this.clearClickElUnwatchFn())
    },
    setActiveMobileElId(data) {
      this.activeMobileElId = data.data.id
    },
    setFinishResult (type = RUN_TYPE.handRun) {
      const activeParamsPanel = typeRunObject[type](this.activeParamsPanel)
      // console.log('!!', this.activeParamsPanel, this.$el)
      this.finishResult = this.activeParamsPanel.every(e => {
        if (activeParamsPanel.includes(e.id)) {
          // 处理日历组件
          // tag组件也存在需要过滤的情况
          if (e.skipStatus) {
            if (this.finishParams[e.id] === FINISH_TYPE.FINISH) {
              return true
            }
          } else {
            return true
          }
        }
        switch (this.finishParams[e.id]) {
          case FINISH_TYPE.FINISH: return true
          case FINISH_TYPE.AWAIT: return false
          case FINISH_TYPE.UNFINISH:
          case FINISH_TYPE.ERROR:
          default: {
            if (this.parent?.isIntelligentSearch && !this.parent?.isIntelligentComponent && this.parent.isFirstRunIntelligent) {
              return true
            }
            this.autoRun = false
            this.$emit('unFinishRequest')
            this.isRunCallback()
            console.log('---------------参数组件报错或未完成-----------------', this.activeParamsPanel.find(item => item.id === e.id), this.finishParams[e.id], e.id)
            return false
          }
        }
      })
      console.log('kkk -' + this.finishResult)
      if (this.finishResult) {
        this.$emit('finishRequest')
        // 数据报告切换进行run
        if (this.displayPanelToggleFresh) {
          this.elementComp = this.activeParamsPanel.map(item => Array.isArray(this.$refs[item.id]) ? this.$refs[item.id][0] : this.$refs[item.id]).filter(e => e)
          this.run()
          this.displayPanelToggleFresh = false
        }
      } else {
        if (Object.values(this.finishParams).includes(FINISH_TYPE.AWAIT)) {
          this.$emit('awaitFinishRequest')
        }
      }
      return this.finishResult
    },
    // 是否触发自动run
    isAutoRun () {
      this.setFinishResult(this.paramsType)
      // 判断参数组件数据完成 是否自动调用run
      const isFirstIntelligentSearch = !!(this.parent?.isIntelligentSearch && !this.parent?.isIntelligentComponent && this.parent.isFirstRunIntelligent)
      if (this.finishResult && (this.autoRun || isFirstIntelligentSearch)) {
        if (this.isThemeFullScreen || this.isOpenAutoRefresh || this.commonData.isSubscribe) {
          this.run({
            data: {
              options: {
                type: this.paramsType
              }
            }
          })
        } else {
          this.setloading({ loading: false })
        }
        this.autoRun = false
        this.isRunCallback()
      }
    },
    isRunCallback () {
      if (typeof this.autoRunCallback === 'function') {
        this.autoRunCallback && this.autoRunCallback()
        this.autoRunCallback = null
      }
    },
    // 参数组件run时设置看板元素loading
    setloading (data) {
      this.parent && this.parent.setloading(data)
    },
    // 其他设置
    getActiveIds() {
      const { content = [] } = this.dynamicTags.find(item => item.active) || {}
      return content
    },
    runMobile({ data } = {}) {
      const { isAsync = false, time = 50 } = data || {}

      if (isAsync) {
        setTimeout(() => {
          this.run()
        }, time)
      } else {
        this.run()
      }
      this.$emit('onRunMobile')
    },
     setDynamicTagsRun(data) {
      let current = false
      // 手动点击
      if (Object.is(data, undefined)) {
        this.parent.runSource = data
        current = true
        // 参数组件调用
      } else if (data instanceof EventData) {
        this.parent.runSource = data.source
        current = true
      }
      if (current) {
        for (let key of Object.keys(this.dynamicTagsRun)) {
          this.$set(this.dynamicTagsRun, key, false)
        }
      }
    },
    metricSwitchFinish(data) {
      // let arr = []
      // let elementList = []
      // console.log(data)
      // Object.keys(this.finishParams).forEach(paramId => {
      //   if (this.$refs[paramId] && this.$refs[paramId][0] && this.$refs[paramId][0].paramElement.type === TYPE_PARAM_ELEMENT.METRIC) {
      //     arr.push({
      //       id: paramId,
      //       type: this.finishParams[paramId],
      //       requestAdapter: this.$refs[paramId][0].requestAdapter()
      //     })
      //   }
      // })
      // let unFinishList = arr.filter(paramId => paramId.type !== FINISH_TYPE.FINISH && paramId.type !== FINISH_TYPE.AWAIT) || []
      // if (!unFinishList.length) {
      //   arr.forEach(item => {
      //     let params = this.$refs[item.id][0]
      //     elementList = [...elementList, ...params.paramElement.content.bindElements]
      //   })
      //   console.log(elementList)
      // }
      // this.elList.forEach(item => {
      //   if (item.type === TYPE_ELEMENT.CHART || item.type === TYPE_ELEMENT.TABLE) {
      //     elementList.push(item.id)
      //   }
      // })
      // elementList.includes(data.data.id) && this.metricFinishList.push(data.data.id)
      // console.log(this.metricFinishList)
      // console.log('------------------------------------------------------------------------------------------')
      // if (this.metricFinishList.length === elementList.length) {
      //   let activeTab = this.paramsPanelList.find(item => item.active)
      //   let metricList = activeTab.content.filter(item => item.type === 'metric')
      //   metricList.forEach(item => {
      //     this.$refs[item.id][0].switchFinish && this.$refs[item.id][0].switchFinish('await')
      //   })
      // }
    },
    metricRun(list, check) {
      // let activeTab = this.paramsPanelList.find(item => item.active)
      // let metricList = activeTab.content.filter(item => item.type === 'metric')
      // metricList.forEach(item => {
      //   this.$refs[item.id][0].awaitFinish && this.$refs[item.id][0].awaitFinish('finish')
      // })
      // this.metricFinishList = []
      this.metricCheck(list, check)
    },
    run(data) {
      let activePanel = this.paramsPanelList.find(item => item.active)
      let list = []
      activePanel?.content?.length && activePanel.content.forEach(item => {
        // 测试数据集变化对参数组件影响的方法
        // adapter(item.content, item.type, this.elList, this)
        if (item.content.notReset) {
          list.push(item)
        }
      })
      // 存在错误的参数组件不允许run
      if (list.length) {
        let paramsObj = parmasLayout(list)
        this.$message({ type: 'error', message: activePanel.label + ' ' + this.$t('sdp.message.pleaseSetTheAssociatedElementA') + ' ' + paramsObj.elName + ' ' + this.$t('sdp.message.pleaseSetTheAssociatedElementB') })
        this.isFinish = !this.runCollection.size
        return false
      }
      this.setDynamicTagsRun(data)
      this.parent.removeInteractionState()
      this.parent.removeCurrentDrillDataState()
      this.parent.setLanguageSwitching()
      let type = Object.is(data, undefined) ? RUN_TYPE.handRun : this.$_getProp(data.data, 'options.type', RUN_TYPE.paramsRun)
      this.parent.paramsType = type
      if (!this.componentsRemind()) {
        this.parent && (this.parent.paramLoadStatus = true)
        this.setloading({ loading: false })
        return void '提示参数组件报错'
      }
      this.metricRun()
      // 预览参数是否是初始化状态 订阅导出不走大屏逻辑 && !this.commonData.isSubscribe
      const isSubscribe = this.commonData.isSubscribe
      if ((this.isThemeFullScreen || this.isOpenAutoRefresh || data?.data?.options?.isToFullScreen || isSubscribe) && !this.utils?.intelligentData?.isIntelligentComponent) {
        if (this.activeParamsPanel.length && !this.setFinishResult(type)) {
          this.autoRun = true
          return void '参数组件为完成'
        }
        this.finishResult = true
        if (this.getWebsocketData.switch || isSubscribe) {
          this.$emit('eventBus', new EventData({
            target: ['displayPanel', 'displayPanelMobile'],
            targetFn: 'runByForce',
            type: RunHandler,
            data: {
              ids: this.boardInfo.openBoardTab ? this.getActiveIds() : ['ALL'],
              options: {
                type: this.parent.paramsType
              }
            },
          }))
        } else {
          this.collectParamsPanelData()
        }
      } else {
        // 是否为全屏或者自动刷新调用过来的
        // debugger // RunHandler
        if (this.parent?.isIntelligentSearch && !this.parent?.isIntelligentComponent && this.parent.isFirstRunIntelligent) {
          this.firstRunFilterUnfinsh()
        } else {

        this.runHandler.initRun({
          paramsPanelList: this.paramsPanelList,
          dynamicTags: this.dynamicTags,
          paramsPanelsStatus: this.finishParams,
          runType: this.parent.paramsType,
        })

        }
      }
      this.paramsPanelShowToggle()
      this.sdpBus.$emit(EVENT_BUS.DISPLAY_PANEL_HANDLER_RUN)
      // this.setParamsRunType(type)
      !data && this.checkParamElementStatus()
    },
    // 以下为一次性运行，预览进来时运行一次后结束
    firstRunFilterUnfinsh() {
      const paramsList = this.$_deepClone(this.paramsPanelList)
      const paramsActive = paramsList.find(e => e.active)
      const { elementId = '', shopIds = [], clausesPeriodMapList = [], quickClauses = '' } = this.utils?.intelligentData || {}

      paramsActive.content = paramsActive.content.map(el => {
        if (el.type === TYPE_PARAM_ELEMENT.LOCATION_NEW && !shopIds.length) {
          return false
        }
        if ([TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR].includes(el.type) && (!clausesPeriodMapList.length || !quickClauses)) {
          return false
        }
        // if (![TYPE_PARAM_ELEMENT.LOCATION_NEW, TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR].includes(el.type)) {
        //   return false
        // }
        // return el
        if (this.finishParams[el.id] !== FINISH_TYPE.UNFINISH) {
          return el
        }
      }).filter(e => e)
      this.runHandler.initRun({
        paramsPanelList: paramsList,
        dynamicTags: this.dynamicTags,
        paramsPanelsStatus: this.finishParams,
        runType: this.parent.paramsType,
      })

      // setTimeout(() => {
      //   this.parent.isFirstRunIntelligent = false
      // }, 300)
    },
    // 同一设置参数组件run的类型传下去
    // setParamsRunType(type) {
    //   if (RUN_TYPE.paramsRun === type) {
    //     const { content = [] } = this.paramsPanelList.find(e => e.active) || {}
    //     if (content.some(el => el.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK)) type = RUN_TYPE.locationRun
    //   }
    //   this.$set(this.commonData, 'paramsRunType', type)
    // },
    paramsPanelShowToggle() {
      if (this.isMobile || !this.commonData.isPreview || this.commonData.staticParamsPanel || this.boardInfo?.hideComponent || this.boardInfo?.autoRefreshData.isAutoRefresh) return
      // 没有钉住看板参数
      this.commonData.hoverIcon = false
      this.paramsPanelShow = false
      this.$emit('setSupernatantTop')
      this.setMoveDisabled(1000)
    },
    // 鼠标移到箭头
    hoverParams() {
      // sbi 大屏没有钉住 但是会被影响
      if (this.isMobile || !this.commonData.isPreview || this.paramsPanelShow || this.isMoveDisabled) return
      this.commonData.hoverIcon = true
      this.paramsPanelShow = true
      this.setMoveDisabled(500)
      this.stopCarouselAndSynchronizationTabs()
    },
    // 保持参数组件显示状态
    keepHoverParams() {
      if (this.isMobile || !this.commonData.isPreview) return
      this.commonData.hoverIcon = true
      this.paramsPanelShow = true
      this.setMoveDisabled(500)
      this.stopCarouselAndSynchronizationTabs()
    },
    // 大屏停止轮播同步tsb
    stopCarouselAndSynchronizationTabs() {
      if (this.isThemeFullScreenAndOpenBoardTab) {
        this.parent.carouselClear()
        this.parent.synchronizationTabs()
      }
    },
    // 大屏启动轮播
    startFullScreenCarousel() {
      const isCloseCarousel = this.boardInfo?.screenModeDate?.isCloseCarousel
      // 判断标签模式
      if (this.isThemeFullScreenAndOpenBoardTab && !isCloseCarousel) {
        const { id = '' } = this.dynamicTags.find(item => item.active) || {}
        const time = this.parent.getCarouselTime(id)
        this.parent.carouselClick(time)
      }
    },
    setMoveDisabled(time) {
      this.isMoveDisabled = true
      setTimeout(() => {
        this.isMoveDisabled = false
      }, time)
    },
    // 按钮运行事件
    btnTargetFn(type) {
      this[type]()
    },
    handRun() {
      if (!this.parent.isFinish) {
        this.$message.warning(this.$t('sdp.views.isRunFinish'))
        return
      }
      this.callbackElementCompFun()
      this.run()
      if (Object.keys(this.calendar).length) {
        this.$refs[this.calendar.id].restSubmit && this.$refs[this.calendar.id].restSubmit()
      }
    },
    callbackElementCompFun() {
      this.elementComp.forEach(el => {
        if (el.paramElement.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK) {
          this.$set(el.paramElement.content, 'dimensionTypeId', '')
        }
        if (el.paramElement.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
          this.sdpBus.$emit(EVENT_BUS.RUN_SET_LOCATION_DATA, this.finishParams[el.paramElement.id], HAND_RUN)
        }
      })
    },
    Cancel (type = 'cancel') { // 重置也需要调用事件总线
      const eventData = new EventData({
        ...this.defaultEventData,
        target: EVENT_DATA_PARAMS.parent,
        targetFn: type,
        type: type,
        data: {}
      })
      this.$emit('eventBus', eventData)
      this.runHandler.stopRun()
    },
    // 重置按钮
    reset() {
      if (!this.parent.isFinish) {
        this.$message.warning(this.$t('sdp.views.isRunFinish'))
        return
      }
      const preParamsPanel = this.paramsPanelList.find(item => item.active) || {}
      this.$set(preParamsPanel, 'status', TYPE_SUPERSETTING.INIT)
      // 需要把父组件的这个boardStatus设置为初始化，否则操作之后不会恢复初始化
      // this.$emit('changeBoardStatus', TYPE_SUPERSETTING.INIT)
      const eventData = new EventData({
        ...this.defaultEventData,
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'initParamsType',
        data: preParamsPanel
      })
      this.$emit('eventBus', eventData)
      this.curTabId = this.$_generateUUID()
      setTimeout(() => {
        this.autoRun = true
        this.autoRunCallback = () => {
          this.handRun()
          this.autoRunCallback = null
        }
      })
    },
    toolTipMessage(el) {
      const { elName } = el
      const messageData = this.messageData[el.id] || {}
      const { message = '', messageType = 'info' } = messageData
      // 移动端message特殊处理
      if (this.commonData.isMobileApp || (this.isMobile && this.commonData.isPreview)) {
        const mobileElementPanel = this.getBoardUnderIdElement('#mobileElementPanel')
        // let mobileElementPanel = document.getElementById('mobileElementPanel')
        let confrimToast = this.$createToast({
          type: 'warn',
          time: CREATE_TOAST_TIME,
          txt: `${elName} ${message || this.$t('sdp.views.UnselectedParameter')}`
        })
        this.$_insertElement(mobileElementPanel, confrimToast.$el)
        confrimToast.show()
      } else {
        setTimeout(() => {
          const el = this.$message({
            message: `${elName} ${message || this.$t('sdp.views.UnselectedParameter')}`,
            type: messageType
          })
          this.isThemeFullScreen && this['sdpBus'].$emit(EVENT_BUS.SET_APPEND_MESSAGE_EL, el)
        })
      }
    },
    // 参数组件为完成提示
    componentsRemind () {
      let bool = true
      if (this.parent?.isIntelligentSearch && !this.parent?.isIntelligentComponent && this.parent.isFirstRunIntelligent) return bool
      const activeParamsPanelIds = typeRunObject[this.parent.paramsType](this.activeParamsPanel)
      this.activeParamsPanel.forEach(el => {
        if (!activeParamsPanelIds.includes(el.id) && this.finishParams[el.id] === FINISH_TYPE.UNFINISH) {
          this.toolTipMessage(el)
          this.onErr(el.type)
          bool = false
        }
      })

      console.log('componentsRemind', JSON.stringify(this.finishParams), bool)
      return bool
    },
    // 获得参数组件请求数据
    collectRequest ({ paramsId, refreshObj, paramsRequestCallback, callback }) {
      const elementComp = paramsId.length
        ? this.elementComp.filter(el => !paramsId.includes(el.paramElement.id))
        : this.elementComp
      const data = {}

      let needParamIdList = []
      if (this.utils.isPcMobile) {
        const { mobileSet: { paramList = {} } } = this.storeSet
        needParamIdList = paramList[this.tabActiveId] || []
      }

      let intelligentParamIdList = []
      const isIntellgentPageFirst = !!(this.parent?.isIntelligentSearch && !this.parent?.isIntelligentComponent && this.parent?.isFirstRunIntelligent)
      if (isIntellgentPageFirst) {
        const paramsActiveVal = this.paramsPanelList.find(e => e.active) || {}
        const paramsActive = this.$_deepClone(paramsActiveVal)
        const { shopIds = [], clausesPeriodMapList = [], quickClauses = '' } = this.utils?.intelligentData || {}

        intelligentParamIdList = paramsActive.content.map(el => {
          if (el.type === TYPE_PARAM_ELEMENT.LOCATION_NEW && !shopIds.length) {
            return false
          }
          if ([TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR].includes(el.type) && (!clausesPeriodMapList.length || !quickClauses)) {
            return false
          }
          // if (![TYPE_PARAM_ELEMENT.LOCATION_NEW, TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR].includes(el.type)) {
          //   return false
          // }
          if (this.finishParams[el.id] === FINISH_TYPE.FINISH) {
            return el.id
          } else {
            return false
          }
        }).filter(e => e)
      }

      elementComp.forEach(element => {
        const id = element.id || (element.paramElement && element.paramElement.id)

        if (this.utils.isPcMobile && !needParamIdList.includes(id)) {
          return
        }
        if (isIntellgentPageFirst && !intelligentParamIdList.includes(id)) {
          return
        }

        if (id) {
          // 过滤metric组件
          if (element.requestAdapter && element.paramElement && element.paramElement.type !== TYPE_PARAM_ELEMENT.METRIC) {
            const el = element.requestAdapter(refreshObj)
            if (el.type === 'Array') {
              Object.keys(data).includes(el.key)
                ? data[el.key].push(el.data)
                : Object.assign(data, { [el.key]: [el.data] })
            } else {
              data[el.key] = el.data
            }
            paramsRequestCallback && paramsRequestCallback(id, el)
          }
        }
      })
      console.log('collectRequest-------------------------', data)
      callback && callback(data)

      this.setDateFieldType(data)
      return data
    },
    setDateFieldType(data) {
      if (!Array.isArray(data.calendarComponents)) return

      data.calendarComponents.forEach((item) => {
        Array.isArray(item?.calendarParseParam?.dataSets) && item.calendarParseParam.dataSets.forEach((dataset) => {
          const field = OnlyField.useDatasetIdAndFieldNameGetFieldData(dataset.dataSetId, dataset.columnName)

          if (field) {
             if (field.columnTpe === 'date') {
              dataset.dateFormat = field.dateFormat
            } else {
              delete dataset.dateFormat
            }
          }
        })
      })
    },
    initLocationData() {
      this.$set(this, 'locationData', {
        shopIds: [],
        outletIds: [],
        id: '',
        componentStatus: FINISH_TYPE.AWAIT,
        type: '',
      })
      // tab切换时 重置locationData的状态为等待
      // this.locationData && this.$set(this.locationData, 'componentStatus', FINISH_TYPE.AWAIT)
    },
    // chekc为true时 为通过刷新按钮刷新触发
    metricChange(arr, elementList, check = false) {
      let dataList = []
      // console.log(arr)
      // console.log(elementList)
      arr.forEach(item => {
        item.requestAdapter.data.forEach(el => {
          dataList.push({ name: el })
        })
      })
      let supernatant = this.parent.$refs['supernatant']
      console.log('metric 切换:', dataList)
      this.board.elList.forEach(item => {
        if (item.type === TYPE_ELEMENT.CHART || item.type === TYPE_ELEMENT.TABLE) {
          const comp = item.vm
          if (!comp) return
          if (elementList.includes(item.id)) {
            comp.metricSwitch && comp.metricSwitch(dataList, true)
          } else if (!check) {
            comp.metricSwitch && comp.metricSwitch(dataList, false)
          }
        }
      })
    },
    // 删除metric参数组件时 会调用该方法
    // metricSwitch(data) {
    //   this.metricChange(data.data.paramsList, data.data.elementList)
    // },
    setLocationData(val = null) {
      const oldVal = this.locationData
      if (!val || this.$_equalsObj(oldVal, val)) return

      this.locationData = val
      // Object.keys(val).forEach(key => {
      //   if (key === 'shopIds' && [...oldVal[key]].sort().toString() === val[key].sort().toString()) return
      //   this.$set(oldVal, key, val[key])
      // })
    },
    // 完成钩子函数
    finishHook ({ id, type, message = '', messageType = 'warning' }) {
      // 参数组件提示
      const messageData = this.messageData
      if (message) {
        messageData[id] = {
          messageType,
          message,
        }
      } else {
        messageData[id] && this.$delete(messageData, id)
      }
      // 避免多次调用
      if (this.finishResult && type !== FINISH_TYPE.FINISH) {
        this.finishResult = false
      }
      if (type !== FINISH_TYPE.AWAIT) {
        id && this.sdpBus.$emit(EVENT_BUS.SET_PRARM_GANGED_OPEN, id)
      }

      id && this.$set(this.finishParams, id, type)

      this.sdpBus.$emit(EVENT_BUS.FINISH_PARAMS_STATUS, {
        id, type
      })

      const isFirstIntelligentSearch = !!(this.parent?.isIntelligentSearch && !this.parent?.isIntelligentComponent && this.parent.isFirstRunIntelligent)

      if (type === FINISH_TYPE.FINISH || (isFirstIntelligentSearch && type === FINISH_TYPE.UNFINISH)) {
        this.isAutoRun()
      }

      if (type === FINISH_TYPE.UNFINISH) {
        if (isFirstIntelligentSearch) {
          this.autoRun = this.componentsRemind()
          type = FINISH_TYPE.FINISH
        } else if (this.autoRun) {
          this.autoRun = this.componentsRemind()
          this.setloading({ id: this.getActiveIds(), loading: this.autoRun })
        } else if (!this.runHandler.finishFlag) {
          this.componentsRemind()
          this.setloading({ id: this.getActiveIds(), loading: false })
        }
      }

      // 未完成而且启动了自动run
      this.runHandler[type === FINISH_TYPE.FINISH ? 'finishHook' : 'noFinishHook']({
        hookType: type,
        paramId: id,
        runType: this.parent.paramsType
      })
    },
    messageTrans(data) {
      if (this.isMobile) {
        let mobileElementPanel = data.data.quickType ? document.getElementById('mobileElementPanel') : document.getElementById('mobileParamsPanel')
        let confrimToast = this.$createToast({
          type: 'warn',
          time: CREATE_TOAST_TIME,
          txt: data.data.msg
        })
        this.$_insertElement(mobileElementPanel, confrimToast.$el)
        confrimToast.show()
      } else {
        this.$message.warning(data.data.msg)
      }
    },
    metricCheck(list = [], check = false) {
      let arr = []
      let elementList = []
      Object.keys(this.finishParams).forEach(paramId => {
        if (this.$refs[paramId] && this.$refs[paramId][0] && this.$refs[paramId][0].paramElement.type === TYPE_PARAM_ELEMENT.METRIC) {
          arr.push({
            id: paramId,
            type: this.finishParams[paramId],
            requestAdapter: this.$refs[paramId][0].requestAdapter()
          })
        }
      })
      let unFinishList = arr.filter(paramId => paramId.type !== FINISH_TYPE.FINISH && paramId.type !== FINISH_TYPE.AWAIT) || []
      if (!unFinishList.length) {
        arr.forEach(item => {
          let params = this.$refs[item.id][0]
          elementList = [...elementList, ...params.paramElement.content.bindElements]
        })
      }
      if (list.length && check) {
        let checkList = elementList.filter(item => list.includes(item)) || []
        arr.length && this.metricChange(arr, checkList, check)
        return
      }
      this.metricChange(arr, elementList)
    },
    paramsHandleMethod ({ type, data }) { },
    layoutMountedHandler () {
      this.$nextTick(() => {
        this.$refs.tab.restData()
      })
    },
    layoutUpdatedHandler () {
      this.moveStatus = false
    },
    // 大屏重新设置layout，隐藏组件也需要
    setLayoutMobileSort(layout) {
      let layoutArr = layout
      let cloneLayout = []
      if (this.commonData.isPreview) {
        cloneLayout = this.$_JSONClone(layout.filter(({ i }) => this.setElementScreenShow(i)))
      }
      if (cloneLayout.length && cloneLayout.length !== layout.length) {
        // 重新排序位置 有隐藏的组件时
        layoutArr = this.isMobile ? layoutMobileSort(cloneLayout) : layoutFullScreenSort(cloneLayout)
      }
      return layoutArr
    },
    moveHandler (i, newX, newY) {
      this.moveStatus = true
      this.$nextTick(() => {
        this.$refs.tab.setLayoutRule()
      })
    },
    resizeHandler: (i, newH, newW, newHPx, newWPx) =>
      console.log(
        'RESIZE i=%s H=%i W=%i H(px)=%i W(px)=%i',
        i,
        newH,
        newW,
        newHPx,
        newWPx
      ),
    gridItemMousedown() {
      const tab = this.paramsPanelList.find(tab => tab.id === this.tabActiveId)
      const oldLayout = this.$_deepClone(tab.content.map(e => e.content.layout))
      this.oldLayout = oldLayout
    },
    movedHandler (i, newX, newY) {
      const tabId = this.tabActiveId
      const tab = this.paramsPanelList.find(tab => tab.id === this.tabActiveId)

      this.$nextTick(() => {
        this.$refs.tab.setLayoutRule()

        const newLayout = this.$_deepClone(tab.content.map(e => e.content.layout))

          this.boardRecord.undoSave({
            type: RECORD_OPERATE_TYPE.CALLBACK,
            oldLayout: this.oldLayout,
            tabId,
            newLayout,
            callback: (data, operateType) => {
              const isUndo = operateType === 'undo'
              const { oldLayout, newLayout, tabId } = data
              const val = isUndo ? oldLayout : newLayout

              const activeTab = this.paramsPanelList.find(tab => tab.id === tabId)
              if (activeTab) {
                activeTab.content.forEach(param => {
                  const target = val.find(e => e.i === param.id)
                  if (target) {
                    Object.assign(param.content.layout, target)
                  }
                })
                // this.$set(activeTab, 'layout', this.$_deepClone(val))
                this.$nextTick(() => {
                  this.$refs.tab.setLayoutRule(tabId)
                })
              }
            }
          })
      })
    },
    // 运行按钮的设置完成
    handelBtnSetting () {
      this.$nextTick(() => {
        if (!this.$refs.btnSetting) return
        let btnSetting = this.$refs.btnSetting.setOptions
        this.boardInfo.activeBtnType = this.$refs.btnSetting.activeTab
        let flag = false
        Object.values(btnSetting).forEach(options => {
          options.forEach(item => {
            if (!item.reName[this.sdpLangcode]) flag = true
          })
        })
        if (flag) {
          this.$message.warning(`${this.$t('sdp.placeholder.btnRenameRequired')}`)
          return
        }
        const eventData = new EventData({
          data: btnSetting,
          target: EVENT_DATA_PARAMS.displayPanel,
          targetFn: 'setParameterBtn',
        })
        this.$emit('eventBus', eventData)
        this.btnSettingShow = false
      })
    },
    changeBtnHover (el, field, active, event) {
      if (!el) return
      if (event === 'focus') {
        document.getElementById(el.id).focus()
      }
      this.$set(el, field, active)
    },
    addBtnEventListener() {
      // this.$nextTick(() => {
      //   const btnLists = this.$refs.btnEl || []
      //   btnLists?.length && Array.from(btnLists).forEach(item => {
      //     this.changeBtnHover(item.$el, 'mouseenter', 'onMouseover', true)
      //     this.changeBtnHover(item.$el, 'mouseleave', 'onMouseover', false)
      //     this.changeBtnHover(item.$el, 'click', 'isActive', true)
      //     this.changeBtnHover(item.$el, 'blur', 'isActive', false)
      //   })
      // })
    },
    removeBtnEventListener() {
      // const btnLists = this.$refs.btnEl || []
      // btnLists?.length && Array.from(btnLists).forEach(item => {
      //   ['mouseenter', 'mouseleave', 'click', 'focus', 'blur'].forEach(option => {
      //     item.$el.removeEventListener(option, () => {
      //     })
      //   })
      // })
    },
    resizedHandler: (i, newH, newW, newHPx, newWPx) =>
      console.log(
        'RESIZED i=%s H=%i W=%i H(px)=%i W(px)=%i',
        i,
        newH,
        newW,
        newHPx,
        newWPx
      )
  }
}
</script>

<style lang="scss" scoped>
@import './utils/variable.scss';
@import 'packages/base/board/displayPanel/params/paramElement/locationNew/css/mobileCommon.scss';
.mobileTitle {
  background-color: var(--sdp-color-ZTLBJ);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--sdp-color-KBMC);
  font-size: 16px;
  height: 44px;
  .tab-add-style-mobile {
    position: absolute;
    top: 0;
    right: 12px;
    line-height: 44px;
    height: 44px;
    overflow: hidden;
    z-index: 10;
    cursor: pointer;
    display: flex;
    .tab-add-style-mobile-box {
      height: 48px;
      margin-left: 10px;
    }
    .mobile-icon {
      font-size: 24px !important;
      color: var(--sdp-zjbts);
    }
  }
}
.moblie-botton-box {
  position: relative;
  background: $color-RLMXBJS !important;
  border-color: $color-RLMXBJS !important;
  .moblie-botton {
    background: $color-YXANS !important;
  }
}
.theme-background {
  background-color: var(--sdp-cszj-bjds) !important;
}
.disabled {
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
}
// 移动端样式
.mobile-params-bgc {
  background: $color-KBBJS;
  height: calc(100% - 116px);
  .isHide {
    opacity: 0;
  }
  .params-panel-bg {
    /deep/ .sdp-grid-layout-container {
      background: transparent;
    }
  }
  /deep/ .sdp-grid-layout-container {
    border-radius: 8px;
    margin: 0 16px;
    background: var(--sdp-color-CSZJBJS);
    overflow: hidden;
  }
  /deep/ .vue-grid-layout {
    margin: 8px 0;
  }
}
/deep/ .cube-picker-cancel {
  display: none;
}
/deep/ .vue-grid-item {
  transition: none;
}
.cube-page {
  overflow: hidden;
}
/deep/ .search .wrapper {
  background-color: #F2F2F2;
}
.mobile-box:hover > .el-tools {
  display: block;
}
/deep/ .cube-picker-confirm {
  font-size: 16px;
  color: #455964;
}
/deep/ .cube-picker-confirm:active {
  font-size: 16px;
  color: #455964;
}
.mobile-box {
  cursor: pointer;
  // position: relative;
  // overflow: hidden;
  .el-tools {
    top: 0;
    // right: 10px;
  }
  .box {
    height: 48px;
    line-height: 48px;
    // padding: 0 16px;
  }
}
.border-bottom-1px::after {
  //border-bottom: 1px solid #ebebeb;
  border: none;
  left: 0;
  bottom: 0;
  width: 100%;
  -webkit-transform-origin: 0 bottom;
  transform-origin: 0 bottom;
}
.box-content {
  cursor: pointer;
  display: inline-block;
  font-family: PingFang-SC-Medium;
  font-size: 16px;
  color: $color-XXBTWZ;
  letter-spacing: 0;
  height: 48px;
  line-height: 48px;
  width: calc(100% - 32px);
}
.icon-sdp-Hyincang{
  position: absolute;
  top: 0;
  left: 0px;
  margin-bottom: 8px;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  color: var(--sdp-hxx) !important;
}
.box-icon {
  cursor: pointer;
  color: #b6b6b6;
  float: right;
  height: 48px;
  line-height: 48px;
  .el-icon-arrow-right {
    margin-left: 20px;
  }
}
// pc
.params-style {
  border: 1px dashed transparent !important;
  // border: 1px dashed #B3B1B1!important;
}
.board-params-panel-pc {
  /deep/ .newTab {
    width: 100%;
    padding-left: 9px;
    padding-right: 18px;
    &.newTab-min {
      min-height: 38px;
    }
    &.newTab-auto {
      height: auto;
    }
  }
}
.mobile-params-bgc .sdp-grid-item-wrapper{
  background: $color-CSZJBJS !important;
}
.mobile-params-bgc .sdp-grid-item-wrapper:hover {
  background-color: $color-KBDBTBJH !important;
}
.normal-status {
  .params-style:hover {
    border: 1px dashed var(--sdp-hxx) !important;
    box-sizing: border-box;
    border-radius: 2px;
    .tool-list{
      display: block;
    }
  }
  .paramcomponent {
    user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
  }
  .calendar-style:hover {
    border: 1px dashed !important;
    border-color: var(--sdp-hxx) !important;
  }
  // 编辑状态禁止用户选中 处理火狐浏览器拖动参数组件问题
}
.layout-border {
  border-left: 1px solid #f7f7f7;
  border-right: 1px solid #f7f7f7;
  border-top: 1px solid #f7f7f7;
}
.block {
  border: 1px solid;
  min-width: 200px;
  min-height: 32px;
  line-height: 32px;
  display: inline-block;
}
.static-toggle-panel {
  position: absolute;
  left: 0;
  right: 0;
  height: 22px;
  border-bottom: 1px solid var(--sdp-cszj-srbks);
  z-index: 4;
  transition: 1s height ease-in-out, 1s padding-top ease-in-out,
    1s padding-bottom ease-in-out;
}
.toggle-panel {
  height: 8px;
  border-bottom: var(--color-ParameterSplitLineWidth, 1px) var(--color-ParameterSplitLineType, solid) var(--color-ParameterSplitLineColor, var(--sdp-cszj-srbks));
  pointer-events: none;
}
.calendar-style {
  position: absolute;
  right: 0;
  bottom: 2px;
  font-family: NotoSansHans-Regular;
  font-weight: 500;
  border: 1px dashed transparent !important;
}
.params-font {
  font-family: NotoSansHans-Regular;
  font-weight: 500;
}
.params-unified-style-column {
  display: flex;
  flex-direction: column;
}
.params-unified-style-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.separate-line {
  // border-bottom: 15px solid #dbe4ea
}
.board-params-panel {
  // min-height: 30px;
  position: relative;
  z-index: 5;
  .params-panel__row:last-child {
    margin: 0;
  }
  .params-panel__row {
    display: flex;
    overflow: hidden;
    justify-content: flex-start;
    flex-wrap: wrap;
    // padding: 10px 0 0 0;
    margin: 0 10px;
    // border-bottom: 1px solid #e1e7f4;
    .operate-btn /deep/ {
      .el-button:first-child {
        margin-left: 0;
      }
    }
    .operate-btn {
      position: relative;
      display: inline-block;
      height: auto;
      //margin-top: 9px;
      //margin-bottom: 9px;
      //margin-left: 16px;
      padding: 6px 30px 6px 6px;
      .btn-run {
        font-size: 14px;
        font-family: NotoSansHans-Regular;
        font-style: normal;
        text-decoration: none;
        height: 20px;
      }
      .btn-cancel {
        height: 20px;
      }
      .el-button {
        padding: 0 10px;
        margin: 0 6px;
        border-radius: 2px;
        font-family: NotoSansHans-Regular;
        font-weight: 800;
        // border: 1px solid #444444;
        // background: #f4f4f4;
        // color: #222222;
        color: var(--sdp-rxwz);
        background-color: var(--sdp-rxbg);
        border-color: var(--sdp-rxbc);
        opacity: 0.9;
        -webkit-transition: 0.2s ease-in;
        -o-transition: 0.2s ease-in;
        transition: 0.2s ease-in;
        &:hover {
          background: rgba(69, 89, 100, 0.7) !important;
          border: 1px solid rgba(69, 89, 100, 0.7) !important;
          color: #ffffff !important;
          -webkit-box-shadow: 0 14px 26px -12px rgba(53, 53, 53, 0.14),
            0 4px 23px 0 rgba(60, 62, 64, 0.2),
            0 8px 10px -5px rgba(30, 32, 33, 0.12) !important;
          box-shadow: 0 14px 26px -12px rgba(53, 53, 53, 0.14),
            0 4px 23px 0 rgba(60, 62, 64, 0.2),
            0 8px 10px -5px rgba(30, 32, 33, 0.12) !important;
        }
        &:active,
        &:focus {
          background: rgba(69, 89, 100, 0.7) !important;
          border: 1px solid rgba(69, 89, 100, 0.7) !important;
          color: #ffffff !important;
          /*border-radius: 5px;*/
          -webkit-box-shadow: 0 14px 26px -12px rgba(116, 96, 238, 0.42),
            0 4px 23px 0 rgba(0, 0, 0, 0.12),
            0 8px 10px -5px rgba(116, 96, 238, 0.2) !important;
          box-shadow: 0 14px 26px -12px rgba(53, 53, 53, 0.14),
            0 4px 23px 0 rgba(60, 62, 64, 0.2),
            0 8px 10px -5px rgba(30, 32, 33, 0.12) !important;
        }
      }
      a{
        text-decoration: none;
      }
    }
    .tool-list {
      display: none;
      position: absolute;
      right: 0;
      top: 0;
      img {
        width: 10px;
        height: 10px;
      }
    }
    .platformFlagRun {
      background: #409eff !important;
      color: #fff !important;
      border: 1px solid #409eff !important;

    }
    .platformFlagCancel {
      font-size: 14px;
      background-color: #fff !important;
      color: #606266 !important;
    }
  }
}

/* 移动端样式 */
.board-params-panel-mobile {
  position: static;
  .params-panel__row {
    position: absolute;
    bottom: 0;
    width: 100%;
    justify-content: center;
    z-index: 2; // 设置等级
  }
  .params-style {
    box-sizing: border-box;
    margin-left: 16px;
    margin-right: 16px;
    position: relative;
  }
  .line:before {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid $color-CSZJFGX !important;
    color: #D8D8D8;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
  // .sdp-btn-run-m {
  //   height: 44px;
  //   flex: 1;
  //   background: $color-YXANS !important;
  //   border-color: $color-YXANS !important;
  //   border-radius: 2px;
  //   color: #ffffff !important;
  //   font-family: NotoSansHans-Regular;
  //   font-weight: 800;
  //   font-size: 18px;
  // }
  // .sdp-btn-run-m:hover{
  //   background: $color-YXANS !important;
  //   border-color: $color-YXANS !important;
  //   color: #ffffff !important;
  // }
  // .sdp-btn-run-pc {
  //   height: 44px;
  //   flex: 1;
  //   background: $color-YXANS !important;
  //   border-color: $color-YXANS !important;
  //   border-radius: 2px;
  //   color: #ffffff !important;
  //   font-family: NotoSansHans-Regular;
  //   font-weight: 800;
  //   font-size: 18px;
  // }
  // .moblie-botton-box {
  //   background: $color-RLMXBJS !important;
  //   border-color: $color-RLMXBJS !important;
  //   width: 100%;
  //   display: flex;
  //   box-shadow: inset 0 0.5px 0 0 #d8d8d8;
  //   box-sizing: border-box;
  //   padding: 16px 12px;
  //   align-items: center;
  // }
  // .sdp-btn-back {
  //   font-family: PingFangSC-Regular;
  //   letter-spacing: 0;
  //   text-align: center;
  //   height: 44px;
  //   border-radius: 2px;
  //   flex: 0;
  //   width: 66px;
  //   background: $color-YXZCFHD;
  //   border-color: $color-YXZCFHD;
  //   color: $color-YXZCFHJT;

  //   &:hover, &:active {
  //     color: $color-YXZCFHJT !important;
  //     background: $color-KBDBTBJH !important;
  //     border-color: $color-KBDBTBJH !important;
  //   }

  //   /deep/ i {
  //     line-height: 1 !important;
  //     font-size: 28px !important;
  //   }

  //   padding: 0 10px !important;
  // }
}
.panelH {
  height: 30px;
}
.panelH1 {
  height: 22px;
}
.staic-icon {
  position: absolute;
  left: 0;
  right: 0;
  // background: #f7f7f7;
  z-index: 5;
  border: 0;
  border-top: var(--color-ParameterSplitLineWidth, 1px) var(--color-ParameterSplitLineType, solid) var(--color-ParameterSplitLineColor, var(--sdp-cszj-srbks));
  // border-bottom: 1px solid #e2e2e2;
}
.staticParamsPanel-bottom {
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e2e2;
  pointer-events: none;
}
.staic-row {
  padding-bottom: 8px;
}
.sdp-paramsetting /deep/ .sdp-dialog {
  .el-dialog__header {
    background-color: var(--sdp-szk-bjs);
    .el-dialog__title {
      background-color: var(--sdp-szk-bjs);
    }
  }
  background-color: var(--sdp-szk-bjs);
}
</style>
<style lang="scss">
  @import './utils/variable.scss';
  @import "packages/assets/theme/variable.scss";

  .sdp-params{
    @import './utils/paramsStyle.scss';
  }
  .cube-picker-panel {
     background-color: $color-SLCBJ !important;
  }
  .cube-popup-content, .cube-picker-panel {
    border-top: var(--topBorder);
    border-color: $color-CSZJFGX !important;
  }
  .cube-picker-content {
    background-color: $color-SLCBJ;
  }
  .cube-picker-content .border-top-1px {
    opacity: 0.5;
    background: var(--sdp-gradient-mask-layer) !important;
  }
  .cube-picker-content .border-top-1px::before {
    border: none !important;
  }
  .cube-picker-content .border-bottom-1px {
    opacity: 0.5;
    background: var(--sdp-gradient-mask-layer) !important;
    border-bottom: 1px solid $color-SLCXZFGX !important;
  }
  .cube-picker-content .border-bottom-1px::after {
    border: none !important;
  }
  .cube-picker-title {
    color: $color-SLCXZWZ !important;
  }
  .cube-picker-wheel-item {
    color: $color-SLCXZWZ !important;
  }
  .cube-picker-choose::after {
    border-color: $color-SLCXZFGX !important;
  }
  .cube-picker-choose .cube-picker-confirm {
    color: $color-SLCOK !important;
  }
  .border-bottom-1px{
    border-bottom: 1px solid $color-SLCXZFGX !important;
  }
  .border-top-1px{
    border-top: 1px solid $color-SLCXZFGX !important;
  }
  .border-bottom-1px::after, .border-top-1px::before {
    border: none !important;
  }

  .chang_lian {
    .params-panel__row {
      //height: 50px;
      .btn-query {
        width: 70px !important;
        height: 40px !important;
        font-size: 14px;
      }
      .btn-cancel {
        width: 70px !important;
        height: 40px !important;
      }
    }
  }

  .cube-page.sdp-transparent-page {
    background: transparent !important;
  }
  .cube-page.sdp-quick-page {
    background: transparent !important;
  }
  .sdp-quick-page.page-move-enter, .sdp-quick-page.page-move-leave-active {
    transform: translate(0, -100%) !important;
  }
  .sdp-quick-page.page-move-enter-active, .sdp-quick-page.page-move-leave-active{
    transition: transform 0s !important;
  }
  .sdp-params_bussiness-calendar {
    z-index: 20 !important;
  }
</style>
