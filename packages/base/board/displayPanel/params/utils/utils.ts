import {
  TYPE_PARAM_ELEMENT,
  FINISH_TYPE,
  CA<PERSON><PERSON>ARTYPE, TEXT_RADIO_TYPE, NUMBER_FORMAT, PARAMETER_TYPE, SCOPE_TYPE
} from './constants'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import {
  SKPINAME
} from './methodParams'
import {
  getPriPeriod,
  getformatValue,
} from './api'
import {
  dateFormat
} from './filters'
import EventData from 'packages/assets/EventData'
import { getDataSetTreeByIds } from '../../../mixins/api'
// import { GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS } from '../../../../../assets/constant'
import * as api from '../paramElement/bussinessCalendar/api'
import { GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS, VIRTUAL_DATASET_KEY } from '../../../../../assets/constant'
import { getWeek } from '../paramElement/bussinessCalendar/components/utils'
import { getFormatterFunc } from '../../../../../assets/utils/globalTools'
import { SOURCE_DATA_TYPE } from '../../supernatant/boardElements/elementChart/constant'

export function initTablePage(elList, bool) {
  elList.forEach(item => {
    if (item.type === TYPE_ELEMENT.TABLE) {
      item.content.initTablePage = bool
    }
  })
}
// 走初始化流程
export async function _calendar(el, type, plantime = {}) {
  let paramElement = el.paramElement.content
  let defaultValue = el.paramElement.content.defaultValue
  let defaultValueA = el.paramElement.content.defaultValueA
  let defaultValueB = el.paramElement.content.defaultValueB
  let defaultValueYear = el.paramElement.content.defaultValueYear
  let isYear = el.paramElement.content.isYear
  let defaults = false
  el.paramElement.skipStatus = false
  el.dataAcquisition() // 这个方法是异步的会最后执行
  let selectType = paramElement.selectType || 'default'
  const isBussinessCalenda = el.paramElement.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR
  el.$set(el, 'update', true)
  if ((selectType === 'default' || selectType === 'Year') && (el.options2.length || el.calendarYearList?.length)) {
    if (defaultValue === undefined) {
      defaults = true
    } else {
      if (isYear === undefined) {
        if (defaultValue || defaultValueYear) {
          defaults = true
        } else {
          defaults = false
        }
      } else {
        if (defaultValue && (selectType === 'default' || selectType === 'Year')) {
          defaults = true
        } else {
          defaults = false
        }
        if (defaultValueYear && selectType === 'Year') {
          defaults = true
        }
      }
    }
  } else if (selectType === 'Month' && el.calendarYearList.length && el.calendarMonthList.length) {
    if (defaultValueA === undefined && defaultValueB === undefined) {
      defaults = true
    } else {
      if (defaultValueA && defaultValueB) {
        defaults = true
      } else {
        defaults = false
      }
    }
  }
  const { calendarshow, quick, isDefault } = el.paramElement.content

  if (selectType === 'default' && !isDefault && isBussinessCalenda && !el.utils.isMobile) {
    defaults = false
  }
  if (type === SKPINAME.SAVE_USER_OPERATION) {
    if (selectType === 'default') {
      if (el.paramElement.content.quickClauses) {
        el.value2 = el.paramElement.content.quickClauses.id
        el.options2.forEach(item => {
          if (item.configValue === el.paramElement.content.quickClauses.configValue) {
            el.$set(paramElement, 'quickClauses', item)
            el.value2 = paramElement.quickClauses.id
            el.changeSelect(plantime.dateNormal)
          }
        })
      }
    } else {
      el.userOptions()
    }
    let check = !el.paramElement.skipStatus && !el.value2 && (!el.selectPick || !el.selectPick.length) && (!el.selectYear || !el.selectYear.length) && (!el.selectMonsthList || !el.selectMonsthList.length) && (!el.selectVlaue || !el.selectVlaue.length)
    if (!el.paramElement.content.quickClauses) {
      const { dtList, priDtList } = el.paramElement.content
      if ((dtList && dtList.length > 0) || (priDtList && priDtList.length > 0)) {
        el.finishHook(FINISH_TYPE.FINISH)
      } else {
        // el.finishHook(FINISH_TYPE.UNFINISH)
      }
    }
    if (check) {
      let ignore = {}
      if (!el.paramElement.content.AccordingToYear) {
        if (el.paramElement.content.handSelect) {
          ignore['Compare'] = el.setCompare('Custom', false)
        } else {
          ignore['Compare'] = el.setCompare('pri', false)
        }
      }
      if (el.monthNotSupport) {
        var str = el.$t('sdp.message.ScatteredDateSelectionDoesNotSupport')
        el.finishHook(FINISH_TYPE.UNFINISH, str)
      } else if ((!el.paramElement.content.dtList || el.paramElement.content.dtList.length === 0) && (!el.paramElement.content.priDtList || !el.paramElement.content.priDtList.length)) {
        el.finishHook(FINISH_TYPE.UNFINISH)
      } else {
        el.finishHook(FINISH_TYPE.FINISH)
      }
    }
    // if (el.options2.length) {
    //   if (el.options2[0].configValue !== 'Most Recent' || !el.location.length) {
    //     el.finishHook(FINISH_TYPE.FINISH)
    //   }
    // }
    el.$set(el, 'update', true)
  }

  if (type === SKPINAME.INIT_DATA) {
    // el.$set(el.paramElement.content, 'handSelect', el.paramElement.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 'default' : false)
    el.$set(el.paramElement.content, 'selectVlaue', [])
    el.$set(el.paramElement.content, 'priorPeriodShow', false)

    if ((defaults || selectType === 'Interval' || el.paramElement.content.quick) && !(el.paramElement.content.quick && el.paramElement.content.calendarshow)) {
      if (!el.paramElement.content.quick) {
        switch (selectType) {
          case 'default': {
            el.$set(el.paramElement.content, 'dtList', [])
            el.$set(el.paramElement.content, 'priDtList', [])
            defaultValue = defaultValue || ''
            el.value2 = ''
            if (el.options2.length) {
              el.paramElement.content.quickClauses = {}
              const flag = !el.options2.some((item) => {
                if (item.configValue === defaultValue) {
                  // TODO
                  // el.getLocationData('init')
                  // 初始化时触发日期变更事件
                  // if (!el.locationData.length) {
                  if (!item.disabled) {
                    el.value2 = item.id
                    el.paramElement.content.quickClauses = item
                    console.log(el.paramElement.content.quickClauses)
                    el.changeSelect()
                    if (item.configValue === 'Custom date') {
                      el.paramElement.content.status = false
                      el.finishHook(FINISH_TYPE.UNFINISH)
                    }
                  } else {
                    el.finishHook(FINISH_TYPE.UNFINISH)
                    console.log('该选项置灰中')
                  }
                  // }
                }
                return item.configValue === defaultValue
              })
              if (flag) {
                // if (defaultValue !== '' && el.value2 === '') {
                //   el.value2 = el.options2[0].id
                //   paramElement.quickClauses = el.options2[0]
                // }
                if (el.value2 === '') {
                  el.$set(el, 'value2', '')
                  el.$set(el.paramElement.content, 'thisPeriodKey', {})
                  el.finishHook(FINISH_TYPE.UNFINISH)
                }
                if (el.options2[0].configValue !== 'Most Recent' || !el.location.length) {
                  el.changeSelect()
                }
              }
            }
            break
          }
          case 'Month': {
            el.$set(el.paramElement.content, 'dtList', [])
            el.$set(el.paramElement.content, 'priDtList', [])
            let defaultYear = el.paramElement.content.defaultValueA || el.calendarYearList[0].value
            let timea = el.calendarYearList[0].maxyear + '-' + el.calendarMonthList[0].maxMonth
            let month = parseInt(el.calendarMonthList[0].maxMonth) - 1 + ''
            let year = (parseInt(el.calendarYearList[0].maxyear) - 1) + ''
            let timeb = ''
            if (parseInt(el.calendarMonthList[0].maxMonth) === 1) {
              year = (parseInt(el.calendarYearList[0].maxyear) - 1) + ''
              month = '12'
              timeb = year + '-' + month
            } else {
              if (parseInt(month) < 10) {
                month = '0' + month
              }
              year = el.calendarYearList[0].maxyear + ''
              timeb = year + '-' + month
            }
            var defaultMonth
            if (defaultYear === el.calendarYearList[0].maxyear) {
              if (defaultValueB) {
                if (defaultValueB === 'Current' || defaultValueB === 'Last') {
                  if (defaultValueB === 'Current') {
                    defaultMonth = el.calendarMonthList[0].maxMonth + ''
                    defaultYear = el.calendarYearList[0].maxyear
                  } else {
                    defaultMonth = month + ''
                    defaultYear = year
                  }
                } else {
                  defaultMonth = defaultValueB > el.calendarMonthList[0].maxMonth ? el.calendarMonthList[0].maxMonth + '' : defaultValueB + ''
                }
              } else {
                defaultMonth = el.calendarMonthList[0].maxMonth + ''
              }
            } else {
              defaultMonth = defaultValueB + '' || el.calendarMonthList[0].value + ''
            }
            let str
            if (defaultValueB === 'Current' || defaultValueB === 'Last') {
              str = defaultValueB === 'Current' ? timea + '' : timeb + ''
              // 勾选上月本月时， 年份更新到最新一年 并加入
              defaultYear = defaultValueB === 'Current' ? timea.split('-')[0] : timeb.split('-')[0]
              el.paramElement.content.defaultValueA = defaultValueB === 'Current' ? timea.split('-')[0] : timeb.split('-')[0]
              // !el.paramElement.content.defaultValueList.includes(defaultValueB) && el.paramElement.content.defaultValueList.push(defaultValueB)
              defaultMonth = defaultValueB === 'Current' ? timea.split('-')[1] : timeb.split('-')[1]
            } else {
              str = defaultYear + '-' + defaultMonth
            }
            // console.log(defaultYear)
            // console.log(defaultMonth)
            el.calendarYearList.forEach(b => {
              if (b.value === defaultYear) {
                el.$set(el, 'selectYear', b)
                b.monthList = [defaultMonth]
                b.select = true
              } else {
                b.monthList = []
                b.select = false
              }
            })
            el.calendarMonthList.forEach(a => {
              if (a.value === defaultMonth) {
                a.select = true
              } else {
                a.select = false
              }
            })
            el.$set(el.paramElement.content, 'selectVlaue', [str])
            el.$set(el, 'selectVlaue', [str])
            // el.$delete(el.paramElement.content, 'quickClauses')
            // el.$set(el.paramElement.content, 'dtList', [])
            el.getSelectVlaue()
            break
          }
          case 'Year': {
            let defaultYear
            let defaultValue = el.paramElement.content.isYear ? el.paramElement.content.defaultValueYear : el.paramElement.content.defaultValue
            if (defaultValue === 'Current Year') {
              defaultYear = el.calendarYearList[0].value
            } else {
              defaultYear = defaultValue || el.calendarYearList[0].value
            }
            el.paramElement.content.selectVlaue = [defaultYear]
            el.calendarYearList.forEach(item => {
              if (item.value === defaultYear) {
                item.select = true
              }
            })
            if (el.paramElement.content.selectIdeal === '2') {
              // el.selectYear = [el.calendarYearList[0].value]
              el.$set(el, 'selectYear', [defaultYear])
            } else {
              // el.selectYear = el.calendarYearList[0].value
              el.$set(el, 'selectYear', defaultYear)
            }
            el.$set(el.paramElement.content, 'selectVlaue', [defaultYear])
            el.$delete(el.paramElement.content, 'quickClauses')
            el.$set(el.paramElement.content, 'dtList', [])
            el.$set(el, 'update', true)
            el.getSelectVlaue()
            break
          }
          case 'Interval': {
            el.$set(el, 'value2', '')
            el.$set(el, 'selectYear', '')
            el.$set(el, 'selectValue', [])
            // 清除预览详细信息值 清除默认值
            el.$set(el.paramElement.content, 'dtList', [])
            el.$set(el.paramElement.content, 'priDtList', [])
            el.$set(el.paramElement.content, 'quickClauses', undefined)
            el.$set(el.paramElement.content, 'thisPeriodKey', {})
            el.$set(el, 'selectPick', false)
            el.$set(el.paramElement.content, 'selectVlaue', [])
            el.updataFinishDate()
            el.getSelectVlaue()
            break
          }
        }
      } else {
        el.value2 = ''
        if (el.paramElement.content.AccordingToYear) {
          el.$set(el, 'value2', '')
          el.$set(el, 'selectYear', '')
          el.$set(el, 'selectValue', [])
          el.$set(el.paramElement.content, 'selectVlaue', [])
          // 清除预览详细信息值 清除默认值
          el.$set(el.paramElement.content, 'dtList', [])
          el.$set(el.paramElement.content, 'priDtList', [])
          el.$set(el.paramElement.content, 'quickClauses', undefined)
          el.$set(el.paramElement.content, 'thisPeriodKey', {})
          el.finishHook(FINISH_TYPE.UNFINISH)
          return
        }
        // await el.setNowTime()
        var timeTrans = el.nowStr.replace(new RegExp(/-/gm), '/')
        var nowtime = new Date(timeTrans)
        var yestertime = new Date(nowtime.getTime() - 24 * 3600 * 1000)
        var month = (yestertime.getMonth() + 1) < 10 ? '0' + (yestertime.getMonth() + 1) : (yestertime.getMonth() + 1)
        var day = yestertime.getDate() < 10 ? '0' + yestertime.getDate() : yestertime.getDate()
        var time = yestertime.getFullYear() + '-' + month + '-' + day
        var noFinde = false
        if (el.paramElement.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR) {
          el.firstCalendar = true
          await el.oneCalendar()

        } else {
          await el.clickHandle(true)
          if (el.nowTimeStr) {
            nowtime = new Date(el.nowTimeStr)
            month = (nowtime.getMonth() + 1) < 10 ? '0' + (nowtime.getMonth() + 1) : (nowtime.getMonth() + 1)
            day = nowtime.getDate() < 10 ? '0' + nowtime.getDate() : nowtime.getDate()
            time = nowtime.getFullYear() + '-' + month + '-' + day
          } else if (el.firstCalendar) {
            el.$message.warning(el.$t('sdp.message.CannotFindTheValidDate'))
            el.firstCalendar = false
            noFinde = true
          }
        }
        const { selectType } = el.paramElement.content
        if (el.firstCalendar) {
          const params = {
            targetDateList: [time],
            qiYeFlag: 1,
            quickType: el.paramElement.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 2 : 1,
            tenantId: el.utils.tenantId
          }
          var data = await getPriPeriod(el.api, params)
          if (Object.keys(data).length === 0) {
            el.$set(el.paramElement.content, 'thisPeriodKey', {})
            el.finishHook(FINISH_TYPE.UNFINISH)
          }
          if (Object.keys(data).length) {
            Object.keys(data).forEach((dateTime) => {
              const priPeriodYear = data[dateTime].split(' ')[0]
              el.paramElement.content.priDtList = [priPeriodYear]
            })
          } else {
            el.paramElement.content.priDtList = []
          }
          // el.$set(el.paramElement.content, 'priorPeriodShow', true)
          el.paramElement.content.dtList = [time]
          const SelectDate = {}
          const paramElement = el.paramElement.content
          // paramElement.dtList = this.monthDayListData || []
          // paramElement.priDtList = this.priPeriodListData || []
          SelectDate['this_period_key'] = await getformatValue(el.api, {
            'shopIds': paramElement.shopIds,
            'tenantId': el.utils.tenantId,
            'values': paramElement.dtList,
          })
          if (Object.keys(data).length) {
            SelectDate['pri_period_key'] = await getformatValue(el.api, {
              'shopIds': paramElement.shopIds,
              'tenantId': el.utils.tenantId,
              'values': paramElement.priDtList || [],
            })
          }

          selectType !== 'Month' && el.$set(el.paramElement.content, 'camparePeriod', {})
          if (Object.keys(SelectDate).length) {
            var obj = {
              'this_period_key': SelectDate['this_period_key'] || [],
              'Prior Period': SelectDate['pri_period_key'] || [],
            }
            obj['Compare'] = el.setCompare('pri') || []
            if (el.paramElement.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR && !el.utils.isMobile) {
              if (paramElement.dtList && paramElement.dtList.length === 1) {
                const params = {
                  'tenantId': el.utils.tenantId,
                  'quickType': '2',
                  'quickClauses': 'Today',
                  'dateNormal': time + ' 00:00:00',
                }
                let data = await api['parseClausesFinal'](el.api, params)
                let SelectDate = {}
                let obj = {}
                el.$set(paramElement, 'camparePeriod', data)
                if (data) {
                  if (Object.keys(data.formatValues).length) {
                    Object.keys(data.formatValues).forEach((item, i) => {
                      if (data.formatValues[item].startDate === data.formatValues[item].endDate) {
                        SelectDate[item] = [`${data.formatValues[item].startDate}`]
                      } else {
                        SelectDate[item] = [`${data.formatValues[item].startDate} ~ ${data.formatValues[item].endDate}`]
                      }
                    })
                    obj['this_period_key'] = SelectDate['this_period_key'] || []
                    obj['Prior Period'] = SelectDate['pri_period_key'] || []
                    obj['infrasys'] = SelectDate['pri_period_key_infrasys'] || []
                    obj['month-on-month'] = SelectDate['pri_period_key_month-on-month'] || []
                    obj['year-on-year'] = SelectDate['pri_period_key_year-on-year'] || []
                    obj['Compare'] = el.setCompare('pri') || []
                    el.$set(paramElement, 'thisPeriodKey', obj)
                  }
                }
              } else {
                el.$set(paramElement, 'thisPeriodKey', obj)
              }
            } else {
              el.$set(paramElement, 'thisPeriodKey', obj)
            }
          }
          el.$delete(paramElement, 'quickClauses')
          if (el.monthNotSupport) {
            el.finishHook(FINISH_TYPE.UNFINISH, el.$t('sdp.message.ScatteredDateSelectionDoesNotSupport'))
          } else {
            el.finishHook(FINISH_TYPE.FINISH)
          }
        } else {
          el.$set(el, 'value2', '')
          el.$set(el, 'selectYear', '')
          el.$set(el, 'selectValue', [])
          el.$set(el.paramElement.content, 'selectVlaue', [])
          // 清除预览详细信息值 清除默认值
          el.$set(el.paramElement.content, 'dtList', [])
          el.$set(el.paramElement.content, 'priDtList', [])
          el.$set(el.paramElement.content, 'quickClauses', undefined)
          el.$set(el.paramElement.content, 'thisPeriodKey', {})
          if (noFinde) {
            el.finishHook(FINISH_TYPE.UNFINISH)
          } else {
            el.finishHook(FINISH_TYPE.UNFINISH, el.$t('sdp.message.nosetfinancial'))
          }
          // paramElement.camparePeriod = {}
        }
      }
    } else {
      el.$set(el, 'value2', '')
      el.$set(el, 'selectYear', '')
      el.$set(el, 'selectValue', [])
      el.$set(el.paramElement.content, 'selectVlaue', [])
      // 清除预览详细信息值 清除默认值
      el.$set(el.paramElement.content, 'dtList', [])
      el.$set(el.paramElement.content, 'priDtList', [])
      el.$set(el.paramElement.content, 'quickClauses', undefined)
      el.$set(el.paramElement.content, 'thisPeriodKey', {})
      el.finishHook(FINISH_TYPE.UNFINISH)
      // paramElement.camparePeriod = {}
    }
  }
}
// cakendar数据整合
export function mergeNewParams() {
  const boardArr = this.$store.state.app.boardArr
  const initBoard = this.$store.state.app.initBoard
  if (boardArr.length) {
    const id = JSON.parse(initBoard.content || initBoard.contentJson).paramsPanelList.find((item) => {
      return item.active
    }).id
    const activePanelId = this.board.getActivePanelId()
    const {
      contentObject
    } = boardArr[boardArr.length - 1]
    const {
      paramsPanelList,
    } = JSON.parse(contentObject)
    // 判断为当前tabs
    if (id === activePanelId) {
      const type = this.paramElement.type
      const originContent = paramsPanelList.find((item) => {
        return item.active
      }).content
      if (originContent.length) {
        return originContent.filter((item) => {
          return item.type === type
        })
      } else {
        return void this.$t('sdp.views.C_boardParam')
      }
    } else {
      return void this.$t('sdp.views.C_jumpMess')
    }
  }
}

// 参数组装
export function calendarFun(content, type, location, id, num = 1, special = false, t = undefined) {
  if (
    (type === TYPE_PARAM_ELEMENT.SELECT_PIERIOD_FINANCIAL) ||
    (type === TYPE_PARAM_ELEMENT.SELECT_PIERIOD_BUSSINESS)
  ) {
    // select pieriod
    return {
      'bindTableElements': content.bindElement,
      'dataSets': content.dataSets,
      'dtList': content.dtList ? content.dtList : [],
      'priDtList': content.priDtList ? content.priDtList : [],
    }
  } else {
    let shopObj = null
    if (content.shopIds) {
      shopObj = content.shopIds.reduce((prev, next) => {
        prev.push({
          outShopId: next
        })
        return prev
      }, [])
    }
    let locationParams = {}
    if (type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR) {
      const locationDatas = Array.isArray(t.locationDatas) ? t.locationDatas[0] : t.locationDatas || {}
      locationParams.shopIdList = locationDatas?.shopIds || []
      locationParams.outletIdList = locationDatas?.outletIds || []
    }
    let scattered = false // 是否是零散日期
    let newdDtList = []
    if (!t.value2 && (!t.selectPick || !t.selectPick.length) && (!t.selectYear || !t.selectYear.length) && (!t.selectMonsthList || !t.selectMonsthList.length) && (!t.selectVlaue || !t.selectVlaue.length)) {
      scattered = true
      newdDtList = content.dtList
    }
    if (content.quickClauses && !special) {
      console.log(type)
      let isQuick = !(type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR || type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR)
      let configValue = !isQuick ? content.quickClauses.configValue : content.quickClauses
      const calendarParseParam = Object.assign({
        'quickType': isQuick ? type === TYPE_PARAM_ELEMENT.CALENDAR_QUICK ? '2' : '1' : content.quickClauses.configType,
        'quickClauses': configValue === 'Last 6 Months' ? 'last_half_year' : (configValue || ''),
        'calendarCompType': '1',
        'dataSets': content.dataSets,
        'dateNormal': content.dateNormal,
        'rule': content.rule,
        'dayOrPeriod': '2',
        ...locationParams
      }, shopObj ? {
        'shopRelateOutletsList': shopObj
      } : {}, content.camparePeriod ? Array.isArray(content.camparePeriod) ? {
        'clausesPeriodMapList': content.camparePeriod
      } : {
        'clausesPeriodMap': content.camparePeriod
      } : {})
      return {
        'id': id,
        'bindElements': content.bindElement,
        'calendarParseParam': calendarParseParam,
        'supportCompare': num > 1 ? content.priorPeriod : true
      }
    } else if (scattered && content.AccordingToYear) {
      // 按年类型特殊处理
      // 日历零散日期界面按年类型 非设置的按年类型
      // 按照区间的方式进行传参
      let AccordingToYear = []
      if (content.priDtList && content.priDtList.length) {
        AccordingToYear = reClausesPeriodMap(content, id, type)
      }
      let dtList = [new Date(content.dtList[0]).getFullYear().toString()]
      // content.camparePeriod ? Array.isArray(content.camparePeriod) ? {
      //         'clausesPeriodMapList': content.camparePeriod
      //       } : {
      //         'clausesPeriodMap': content.camparePeriod
      //       } : {}
      const calendarParseParam = Object.assign({
        'quickType': type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 2 : 1,
        // 'calendarCompType': 'Date Interval',
        // 'dtList': content.dtList,
        'calendarCompType': type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR ? CALENDARTYPE.Interval : CALENDARTYPE.Year,
        dtList: type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR ? content.dtList : dtList,
        'dataSets': content.dataSets,
        'priDtList': content.priDtList,
        'dateNormal': content.dateNormal,
        'rule': content.rule,
        'dayOrPeriod': '2',
        ...locationParams
      }, shopObj ? {
        'shopRelateOutletsList': shopObj
      } : {})
      // if (AccordingToYear.length) {
      //   calendarParseParam.clausesPeriodMapList = AccordingToYear
      // }
      return {
        'id': id,
        'bindElements': content.bindElement,
        'calendarParseParam': calendarParseParam,
        'supportCompare': num > 1 ? content.priorPeriod : true
      }
    } else if (scattered && newdDtList.length === 1 && (!content.handSelect || (type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR && content.handSelect === 'scattered'))) {
      let dateNormal = newdDtList[0] + ' 00:00:00'
      // 如果选择的单个零散日期属于yesterday或today时scattered
      // 需要新增customizedToday字段 置为true
      let check = content.oneCalendarList ? content.oneCalendarList.includes(newdDtList[0]) : false
      // let quickClauses = content.AccordingToYear ? 'Whole Year' : 'Today'
      let quickClauses = 'Today'
      const calendarParseParam = Object.assign({
        'quickType': type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 2 : 1,
        // 'quickClauses': content.quickClauses.configValue,
        'quickClauses': quickClauses,
        'calendarCompType': '1',
        'dataSets': content.dataSets,
        'dateNormal': dateNormal,
        'rule': content.rule,
        'dayOrPeriod': '2',
        customizedToday: undefined,
        ...locationParams
      }, shopObj ? {
        'shopRelateOutletsList': shopObj
      } : {}, content.camparePeriod ? Array.isArray(content.camparePeriod) ? {
        'clausesPeriodMapList': content.camparePeriod
      } : {
        'clausesPeriodMap': content.camparePeriod
      } : {})
      if (type !== TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR && !check) {
        calendarParseParam.customizedToday = true
      }
      return {
        'id': id,
        'bindElements': content.bindElement,
        'calendarParseParam': calendarParseParam,
        'supportCompare': num > 1 ? content.priorPeriod : true
      }
    } else if (special) {
      let customData = t.$_getProp(content.customDataObj, 'customData', {})
      let priData = t.$_getProp(content.customDataObj, 'priData', {})
      var list = [customData.startTime, customData.endTime]
      var prilist = [priData.startTime, priData.endTime]
      const calendarParseParam = Object.assign({
        'quickType': type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 2 : 1,
        'dtList': list,
        'calendarCompType': 'Date Interval',
        'dataSets': content.dataSets,
        'dateNormal': content.dateNormal,
        'rule': content.rule,
        'dayOrPeriod': '2',
        ...locationParams
      }, t.$_getProp(content.customDataObj.customData, 'customPri', false) ? {
      // }, priData ? {
        'priDtList': prilist
      } : {},
      shopObj ? {
        'shopRelateOutletsList': shopObj
      } : {})
      return {
        'id': id,
        'bindElements': content.bindElement,
        'calendarParseParam': calendarParseParam,
        'supportCompare': num > 1 ? content.priorPeriod : true
      }
    } else {
      const mytype = content.dtList && content.dtList.length ? '1' : (CALENDARTYPE[content.selectType] || '1')
      let dtList = mytype !== '1' ? dtListTrans(content.selectVlaue, t) : []
      let priDtList = content.selectVlauePri ? mytype !== '1' ? dtListTrans(content.selectVlauePri, t) : content.priDtList : undefined
      let realPri = content.priDtList && content.priDtList.length ? content.priDtList : priDtList
      const calendarParseParam = Object.assign({
        'quickType': type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 2 : 1,
        'dtList': content.dtList && content.dtList.length ? content.dtList : dtList,
        'calendarCompType': mytype,
        'dataSets': content.dataSets,
        'dateNormal': content.dateNormal,
        'rule': content.rule,
        'dayOrPeriod': '2',
        ...locationParams
      }, realPri ? {
        'priDtList': realPri
      } : {},
      shopObj ? {
        'shopRelateOutletsList': shopObj
      } : {}, content.camparePeriod ? Array.isArray(content.camparePeriod) ? {
        'clausesPeriodMapList': !realPri || !realPri.length ? content.camparePeriod : undefined
      } : {
        'clausesPeriodMap': !realPri || !realPri.length ? content.camparePeriod : undefined
      } : {})
      return {
        'id': id,
        'bindElements': content.bindElement,
        'calendarParseParam': calendarParseParam,
        'supportCompare': num > 1 ? content.priorPeriod : true
      }
    }
  }
}
export function reClausesPeriodMap(data, id, type) {
  let { dtList, priDtList } = data
  let arr = ['pri_period_key_infrasys', 'pri_period_key_month-on-month', 'pri_period_key_year-on-year', 'this_period_key']
  let mapObj = {}
  arr.forEach(item => {
    mapObj[item] = {
      calendarType: type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 2 : 1,
      endDate: item !== 'this_period_key' ? priDtList[1] : dtList[1],
      id,
      startDate: item !== 'this_period_key' ? priDtList[0] : dtList[0],
      supportCompare: data.priorPeriod
    }
  })
  return [mapObj]
}
export function dtListTrans(data, t) {
  if (data.indexOf('Current') === -1 && data.indexOf('Last') === -1) {
    return data
  } else {
    let arr = []
    let timea = t.calendarYearList[0].maxyear + '-' + t.calendarMonthList[0].maxMonth
    let timeb = ''
    if (parseInt(t.calendarMonthList[0].maxMonth) === 1) {
      timeb = (parseInt(t.calendarYearList[0].maxyear) - 1) + '-' + '12'
    } else {
      let month = parseInt(t.calendarMonthList[0].maxMonth) - 1 + ''
      if (parseInt(month) < 10) {
        month = '0' + month
      }
      timeb = t.calendarYearList[0].maxyear + '-' + month
    }
    data.forEach(item => {
      if (item === 'Current') {
        arr.push(timea)
      } else if (item === 'Last') {
        arr.push(timeb)
      } else if (item !== timea && item !== timeb) {
        arr.push(item)
      }
    })
    return arr
  }
}
export function isLeapYear(year) {
  return (year % 100 === 0 ? (year % 400 === 0 ? 1 : 0) : (year % 4 === 0 ? 1 : 0))
}

export function replacements(type) {
  const board = this.board
  // const arr = mergeNewParams.call(this)

  const calendarArr = []
  board.paramsPanelList.forEach((val) => {
    if (val.active) {
      val.content.forEach((v) => {
        if (v.type === type) {
          calendarArr.push(v.content.identifying)
        }
      })
    }
  })
  // 得到当前的key
  // const index = calendarArr.sort((a, b) => {
  //   return a - b
  // }).findIndex((item) => {
  //   return item === this.paramElement.content.identifying
  // })
  // 判断是否在当前key中
  // if (arr) {
  //   arr.sort((a, b) => {
  //     return a.content.identifying - b.content.identifying
  //   })
  //   return arr[index]
  // }
}
export const additionalMethods = {
  changeParamElementSetting(targetId, assignableObj) {
    const eventData = new EventData({
      ...this.defaultEventData,
      data: {
        targetId,
        assignableObj
      },
      target: this.utils.isMobile ? 'displayPanelMobile' : 'displayPanel',
      targetFn: 'changeParamElementSetting'
    })
    this.$emit('eventBus', eventData)
  }
}
export const getDimensionInIndex = {
  selectionChange(selectEL, selectList, type) {
    const selectListfilter = []
    selectList.forEach(item => {
      selectListfilter.push(...actionGetDataSetIds(item.type, item.content))
    })
    return Array.from(new Set(selectListfilter))
  },
}

// 获取所有级联选择器的数据
export function getAllCascaderValues(option) {
  let model = []
  function getTrees(arr, str, level) {
    arr.forEach(item => {
      let newStr = str.length ? [...str, item.name] : [item.name]
      if (item.children) {
        getTrees(item.children, newStr, level + 1)
      } else {
        model.push(newStr)
      }
    })
  }
  getTrees(option, [], 0)
  return model
}

export const MLS = '$mls$'

export function setCodeArr(val, languageCode) {
  if (!val) return val

  let codeArr = val.code.split(MLS)
  if (codeArr?.length > languageCode || codeArr?.length === Number(languageCode)) {
    return codeArr[languageCode - 1] ? codeArr[languageCode - 1] : val.defaultValue
  } else {
    if (codeArr?.length === 1) {
      return codeArr[0]
    } else {
      return val.defaultValue
    }
  }
}

export const methodsObj = {
  // 需求4903 日期参数组件绑定字段选项时需剔除标记了多语言或货币的字段
  filterLanguageAndCurrencyData (selectEL, selectList, type) {
    this.selectionChange(selectEL, selectList, type, true)
  },
  // 元素的选择事件
  async selectionChange(selectEL, selectList, type, filterLanguageAndCurrencyFlag = false) {
    const selectListfilter = []
    selectList.forEach(item => {
      selectListfilter.push(...actionGetDataSetIds(item.type, item.content))
    })

    if (selectListfilter.length) {
      await this.getFiled(Array.from(new Set(selectListfilter)), type, filterLanguageAndCurrencyFlag)
    }

    this.selectionChangeCallback && this.selectionChangeCallback()
  },
  // 根据数据集得到对应字段
  async getFiled(dataSetIds, type, filterLanguageAndCurrencyFlag = false) {
    // 获取多个字段
    // TODO 后台解决接口问题
    // filterLanguageAndCurrencyFlag 过滤多语言和币种标识 默认为false，不进行过滤。为true则进行过滤
    let datasetsFields = await getDataSetTreeByIds(this.api, { ids: dataSetIds, tenantId: this.utils.tenantId, filterLanguageAndCurrencyFlag: filterLanguageAndCurrencyFlag }, this.aliasDict)

    datasetsFields = datasetsFields.map(item => {
      const { children, id, labeName } = item
      const tempArr = children.map(({ labeName, columnTpe, lgeType = '', comment = '', ...obj }) => {
        return {
          value: labeName,
          columnType: columnTpe,
          labeName,
          lgeType,
          comment,
          ...obj
        }
      })
      return {
        id,
        fieldList: tempArr,
        name: labeName
      }
    })
    // debugger
    // datasetsFields.forEach((item) => {
    //   dataSets.forEach((every) => {
    //     item.id === every.id && (item.name = every.name)
    //   })
    // })
    this.addDimension(datasetsFields, type)
    this.dimensionFields = datasetsFields.map(item => item.name)
    this.dimensionFields = Array.from(new Set(this.dimensionFields))
  },
  addDimension(dimensions, type) {
    const filterComponentList = [TYPE_PARAM_ELEMENT.PAYMENT_GROUP, TYPE_PARAM_ELEMENT.LOCATION_NEW, TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX]
    if (!filterComponentList.includes(this.settingParam.type)) {
      this.dimensionArr = dimensions
      let dataSets
      if (this.settingParam.type === TYPE_PARAM_ELEMENT.TEXTBOX) {
        dataSets = this.settingParam.content.textboxOptions.dataSets
      } else {
        dataSets = this.settingParam?.content?.dataSets || this.settingParam?.content?.options?.dataSets
      }
      if (dataSets && dataSets.length && (this.settingParam.type !== TYPE_PARAM_ELEMENT.SELECT_ORDINARY)) {
        if (this.settingParam.type === TYPE_PARAM_ELEMENT.TEXTBOX) {
         // this.textBoxSetData(dataSets, this.settingParam?.content?.radioVal)
        } else {
          this.selectedDimensionFields = []
          dataSets.forEach(item => {
            this.dimensionArr.forEach((val, i) => {
              if (val.id === item.dataSetId) {
                this.selectedDimensionFields[i] = item.columnName
                if (item.orderFields && item.orderFields.length) this.selectedOutlrtsField[i] = item.orderFields = [0]
              }
            })
          })
        }

      } else {
        if (this.settingParam.type === TYPE_PARAM_ELEMENT.TEXTBOX) {
          this.selectedDimensionFieldTexts = this.selectedDimensionFieldTexts.map(item => {
            return []
          })
          this.selectedDimensionFields = this.selectedDimensionFields.map(item => {
            item = new Array(2).fill('')
            return item
          })
        } else {
          this.selectedDimensionFields = []
        }
      }
      // 下拉框
      if (this.settingParam.type === TYPE_PARAM_ELEMENT.SELECT_ORDINARY) {
        const first = !this.dataSets.length || false

        this.dataSets = []
        for (let i = 0; i < dimensions.length; i++) {
          this.$set(this.dataSets, i, this.setRow(i))
        }
        this.init(first)
      }
      this.dimensionCallback && this.dimensionCallback()
    } else if (this.settingParam.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
      if (type === 'init') {
        const dataSetFields = this.settingParam.content.options.dataSetFields
        this.dimensionArr = dimensions
        if (dataSetFields && dataSetFields.length) {
          dataSetFields.forEach(item => {
            this.dimensionArr.forEach(val => {
              if (val.id === item.dataSetId) {
                const { dimensionTypeField, demensionTypeIdField, shopIdField, shopNameField, outletsField, outletNameField } = item
                this.$set(val, 'selectedDimensionFields', dimensionTypeField.columnName)
                this.$set(val, 'selectedDemensionTypeIdField', demensionTypeIdField.columnName)
                this.$set(val, 'selectedShopIdField', shopIdField.columnName)
                this.$set(val, 'selectedShopNameField', shopNameField.columnName)
                this.$set(val, 'selectedOutletsField', outletsField ? outletsField.columnName : '')
                this.$set(val, 'selectedOutletNameField', outletNameField ? outletNameField.columnName : '')
              }
            })
          })
        }
      } else {
        this.dimensionArr = dimensions.map(item => {
          const e = this.dimensionArr.find(v => v.id === item.id)
          if (e) {
            return Object.assign(e, item)
          }
          return item
        })
      }
    } else if (this.settingParam.type === TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX) {
      this.dataSetCollection = dimensions.map(item => {
        const e = this.dataSetCollection.find(v => v.id === item.id)
        if (e) {
          return Object.assign(e, item)
        }
        return item
      })
    } else {

    }
  },
  textBoxSetData(dataSets, radioVal) {
    this.dataLists = []
    let dimensionLen = this.selectedDimensionFields.length
    this.dimensionArr.forEach((data, i) => {
      this.selectedDimensionFields[i] = dimensionLen ? this.selectedDimensionFields[i] : new Array(2).fill('')
      let dataItem = []
      dataSets.forEach(item => {
        if (item.dataSetId === data.id) {
          if (dimensionLen && data.fieldList.find(item => this.selectedDimensionFields[i].indexOf(item.value) > -1)) {
            dataItem.push(this.selectedDimensionFields[i])
          } else {
            let curName = item?.bindColumnName?.length ? item.bindColumnName : item.columnName
            Array.isArray(curName) ? dataItem.push(...curName) : dataItem.push(curName)
          }
        }
      })
      this.dataLists.push(dataItem)
    })
    this.dataLists.forEach((item, i) => {
      if (radioVal === TEXT_RADIO_TYPE.TIME && item instanceof Array) {
        const startItem = Array.isArray(item[0]) ? item[0]?.[0] : item[0]
        const endItem = Array.isArray(item[0]) ? item[0]?.[1] : item[1]
        this.selectedDimensionFields[i] = [startItem, endItem]
      } else if (radioVal === TEXT_RADIO_TYPE.NORMAL) {
        let is = item.every(it => {
          return it
        })
        if (is) {
          this.$set(this.selectedDimensionFieldTexts, i, Array.isArray(item[0]) ? [] : item)
        }
      }
    })
  },
  // 获取数据集ids
  checkDimensionFieldsIds(selectList) {
    const selectListfilter = []
    selectList.forEach(item => {
      // if (item.type === 'text') return
      selectListfilter.push(...actionGetDataSetIds(item.type, item.content))
    })
    return selectListfilter
  },
}
/**
 * 获取数据集id.
 *
 * @param  {String}  参数组件类型 || 特殊组件类型
 * @return {Array}   数据集id
 */
export function actionGetDataSetIds(type, content) {
  const types = {
    [TYPE_ELEMENT.CUSTOMER_ELEMENT]: () => {
      console.log(TYPE_ELEMENT.CUSTOMER_ELEMENT, content)
      return content?.config?.dataSetIds || []
    },
    [TYPE_ELEMENT.TABLE]: () => {
      const { dataSetJoinsConfig } = content.tableDefaultConfig
      const actualDataSetIds = content.dataSetIds ? content.dataSetIds.filter((item) => {
        return item && item !== VIRTUAL_DATASET_KEY
      }) : []
      const virtualDataSetIds = dataSetJoinsConfig?.referenceDatasetId || []

      return Array.from(new Set([...actualDataSetIds, ...virtualDataSetIds]))
    },
    [TYPE_ELEMENT.CHART]: () => {
      const { associatedData = {}, drillSettings } = content
      if (!drillSettings.dataSetId) return []
      if (drillSettings.sourceDataType === SOURCE_DATA_TYPE.indexFlag) return [...drillSettings.indexFlagIds]
      // 关联数据集
      if (Object.keys(associatedData).length) {
        return associatedData.referenceDatasetId ? [...associatedData.referenceDatasetId] : []
      }
      return [drillSettings.dataSetId]
    },
    [TYPE_ELEMENT.TEXT]: () => {
      return content?.dataSetIds || []
    },
    [TYPE_ELEMENT.ELEMENT_TITLE]: () => {
      return content?.dataSetIds || []
    },
    [TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD]: () => {
      return content.optionArray ? content.optionArray.map(item => item.dataSetId).filter(item => {
        return item
      }) : []
    },
    [TYPE_ELEMENT.ELEMENT_YEAR_CARD]: () => {
      return content.options.dataSetId ? content.options.dataSetId.filter((item) => {
        return item
      }) : []
    },
    [TYPE_ELEMENT.COMPARE_TABLE]: () => {
      return content.options.dataSetId ? content.options.dataSetId.filter((item) => {
        return item
      }) : []
    },
    [TYPE_ELEMENT.FOUR_QUADRANT]: () => {
      return content.fourQuadrantOptions.tables.length ? content.fourQuadrantOptions.tables.map(item => item.dataSetId) : []
    },
    [TYPE_ELEMENT.COMBINE_CARD]: () => {
      const arr = []
      content.choiceTab.forEach(item => {
        item.saveObj.cardList.forEach(el => {
          let optionArray = el.content.optionArray ? el.content.optionArray.map(v => v.dataSetId).filter(v => v) : []
          arr.push(...optionArray)
        })
      })
      return arr
    },
    [TYPE_ELEMENT.DUPONT_ANALYSIS]: () => {
      const arr = []
      content.cardData && content.cardData.forEach(el => {
        let dataSetIds = el.content.optionArray ? el.content.optionArray.map(v => v.dataSetId).filter(v => v) : []
        arr.push(...dataSetIds)
      })
      return arr
    },
    'default': () => {
      return content.options.dataSetId.filter((item) => {
        return item
      })
    },
  }
  return types[(type || 'default')] ? types[(type || 'default')]() : ''
}

export function funGetReName(board, settingParam, quickArr, nameState = true) {
  const activeParamsPanelList = board.paramsPanelList.filter(item => {
    return item.active
  })[0]
    .content.filter(item => {
      return !quickArr.includes(item.type)
    })
  activeParamsPanelList.forEach(item => {
    if (item.id !== settingParam.id) {
      if (item.elName.toLowerCase() === settingParam.elName.toLowerCase()) {
        nameState = false
      }
    }
  })
  return nameState
}
export function generateDateModel(year, month, date, extra, num = 0) {
  const current = `${year}-${(month + 1) > 9 ? (month + 1) : `0${(month + 1)}`}-${date > 9 ? date : `0${date}`}T00:00:00`
  const dateObj = new Date(current)
  const begin = new Date(+dateObj)
  begin.setMonth(0)
  begin.setDate(1)
  const moveDay = function (obj) {
    return obj.getDay() >= num ? obj.getDay() - num : 6 - obj.getDay()
  }
  const day = moveDay(dateObj)
  const beginDay = moveDay(begin)
  const dateCount = Math.ceil((dateObj - begin) / (24 * 60 * 60 * 1000)) + 1
  // 跨年周时间补充
  // let weekCount = Math.ceil((dateCount + beginDay) / 7)
  // const weekMax = getWeek(year, num)
  // const lastDay = new Date(`${year}-12-31`).getDay()
  // const offsetLastDay = (num ? 0 : 6)
  // weekCount === Object.keys(weekList).length - 1 && dayNums.length !== 7
  let model = {
    year,
    month,
    date,
    day,
    timestamp: +dateObj,
    dateCount,
    // weekCount: (weekMax === weekCount && lastDay !== offsetLastDay) ? 1 : weekCount,
    weekCount: Math.ceil((dateCount + beginDay) / 7),
    dateTime: `${year}-${(month + 1) > 9 ? (month + 1) : '0' + (month + 1)}-${date > 9 ? date : '0' + date}`,
  }
  if (model.dateTime === dateFormat(new Date(), 'yyyy-MM-dd')) {
    model.presentTime = true
  }
  // 添加extra内容
  if (extra) {
    model = Object.assign(model, extra)
  }
  return model
}
// 参数 numDay 表示天数位移
export function getMonthDaysArr(year, numDay) {
  // 生成每月天数的数组
  const arr = [31, 28 + isLeapYear(year), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
  // const arr = new Array(31, 28 + isLeapYear(year), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
  const resArr = []
  // 生成一年的所有日期
  arr.forEach((val, index) => {
    const month = []
    for (let i = 1; i <= val; i++) {
      month.push(generateDateModel(year, index, i, {
        'isSelected': false,
        'isStyle': false,
        'isMonth': index % 2 === 0,
      }, numDay))
    }
    if (!month[0].isMonth) {
      month[0]['radius'] = 'reft'
      month[month.length - 1]['radius'] = 'right'
    }
    resArr.push(month)
  })
  return resArr
}

// 获取日期区间内的所有日期
export function handGetDateArray(startDate, endDate) {
  const handGetDateInfo = startDate => startDate.split('-').map(item => parseInt(item))
  const handGetMonthList = year => [31, 28 + isLeapYear(year), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
  const [startY, startM, startD] = handGetDateInfo(startDate)
  const [endY, endM, endD] = handGetDateInfo(endDate)
  const judgeDay = ([currentY, currentM, currentD]) => currentY === endY && currentM === endM && currentD === endD
  const numForMatter = num => num > 9 ? num + '' : '0' + num
  const dateStr = ([y, m, d]) => `${y}-${numForMatter(m)}-${numForMatter(d)}`
  const dateResult = []
  const loopFn = ([y, m, d]) => {
    const flag = judgeDay([y, m, d])
    if (flag) {
      dateResult.push(dateStr([y, m, d]))
      return
    }

    const monthList = handGetMonthList(y)
    const isOverFlowMonth = m > 12
    const isOverFlowDay = d > monthList[m - 1]

    if (isOverFlowMonth) {
      loopFn([y + 1, 1, d])
    } else if (isOverFlowDay) {
      loopFn([y, m + 1, 1])
    } else {
      dateResult.push(dateStr([y, m, d]))
      loopFn([y, m, d + 1])
    }

  }
  loopFn([startY, startM, startD])
  return dateResult
  // const endYear = endTime.split('-')[0]
}

// 获取表格、卡片、图形的同比环比信息
// list 对应的看板元素id
// board 看板数据 用于筛选需要获取的看板元素
export function getCompareDate(list = [], board = {}, t) {
  const elList = board.elList
  let validList = elList.filter(el => list.includes(el.id))
  let compareList = []
  validList.forEach(el => {
    switch (el.type) {
      case TYPE_ELEMENT.TABLE:
        // console.log('表格')
        // console.log(el.content.originalTable)
        let originalTable = t.$_getProp(el.content, 'originalTable', [])
        originalTable.forEach(cell => {
          cell.forEach(item => {
            let rule = t.$_getProp(item.content, 'compareRule', undefined)
            let timeArea = t.$_getProp(item.content, 'timeArea', undefined)
            let list = [ 'pre_period', 'growth_value', 'growth_rate' ]
            if (!rule && list.includes(timeArea)) {
              rule = 'infrasys'
            }
            rule && !compareList.includes(rule) && compareList.push(rule)
          })
        })
        break
      case TYPE_ELEMENT.CHART:
        // console.log('图形')
        // console.log(el.content.chioceTab)
        // console.log(el.content.drillSettings.layers)
        let chioceTab = t.$_getProp(el.content, 'chioceTab', undefined)
        if (chioceTab) {
          chioceTab.forEach(tab => {
            let layersList = t.$_getProp(tab.saveObj.drillSettings, 'layers', [])
            layersList.forEach(layers => {
              layers.metrics && layers.metrics.forEach(metrics => {
                let rule = t.$_getProp(metrics, 'compareRule', undefined)
                rule && !compareList.includes(rule) && compareList.push(rule)
              })
            })
          })
        } else {
          let layersList = t.$_getProp(el.content.drillSettings, 'layers', [])
          layersList.forEach(layers => {
            layers.metrics && layers.metrics.forEach(metrics => {
              let rule = t.$_getProp(metrics, 'compareRule', undefined)
              rule && !compareList.includes(rule) && compareList.push(rule)
            })
          })
        }
        break
      case TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD:
        // console.log('卡片')
        // console.log(el.content.chartUserConfig.compareType)
        let rule = t.$_getProp(el.content.chartUserConfig, 'compareType', undefined)
        rule && !compareList.includes(rule) && compareList.push(rule)
        break
      default:
        break
    }
  })
  return compareList
}
// 自定义时期范围
export function customAdapter(t, isNeed = true) {
  let customData = t.customData
  let priData = t.priData
  let nowTab = t.paramsPanelList.find(tab => tab.active)
  let params = nowTab.content.find(item => item.id === customData.id)
  let num = nowTab.content.filter(item => item.type === customData.type) || []
  let list = [customData.startTime, customData.endTime]
  let prilist = [priData.startTime || '', priData.endTime || '']
  const calendarParseParam = Object.assign({
    'quickType': params.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR ? 2 : 1,
    'dtList': list,
    'calendarCompType': 'Date Interval',
    'dataSets': params.content.dataSets,
    'dateNormal': params.content.dateNormal,
    'rule': params.content.rule,
    'dayOrPeriod': '2',
  }, t.customData.customPri && isNeed ? {
    'priDtList': prilist
  } : {},
  params.shopObj ? {
    'shopRelateOutletsList': params.shopObj
  } : {}, params.content.camparePeriod ? Array.isArray(params.content.camparePeriod) ? {
    'clausesPeriodMapList': params.content.camparePeriod
  } : {
    'clausesPeriodMap': params.content.camparePeriod
  } : {})
  return {
    'id': params.id,
    'bindElements': params.content.bindElement,
    'calendarParseParam': calendarParseParam,
    'supportCompare': num.length > 1 ? params.content.priorPeriod : true
  }
}
export function scrollHeightFix(min = 20, max = 200, id = '', fix = 0) {
  let height
  let maxheight = max // 最大高度
  let minheight = min // 最小高度
  height = document.getElementById(id) ? document.getElementById(id).clientHeight : 0
  if (height) {
    height = height < minheight ? minheight : height + fix
    let heightStyle = height > maxheight ? { height: maxheight + 'px' } : { height: height + 'px' }
    return heightStyle
  } else {
    let heightStyle = { height: minheight + 'px' }
    return heightStyle
  }
}
export function layoutFullScreenSort(layout) {
  const len = GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS
  return layout.sort((a, b) => a.y - b.y || a.x - b.x).map((item, i, arr) => {
    if (i) {
      const { x, y, w, h } = arr[i - 1]
      const elw = x + w
      if (len - elw >= item.w) {
        item.x = elw
        item.y = y
      } else {
        item.x = 0
        item.y = y + h
      }
    } else {
      item.x = 0
      item.y = 0
    }
    return item
  })
}

// 参数组件隐藏排序
export function layoutHideParamsSort(oldArr, newArr) {
  if (!newArr.length) return newArr
  oldArr.sort((a, b) => a.y - b.y || a.x - b.x)
  newArr.sort((a, b) => a.y - b.y || a.x - b.x)
  let oldRow = []
  let newRow = []
  oldArr.map((item, i, arr) => {
    if (i === 0 || arr[i - 1].y !== item.y) {
      // 获取同一行的
      newRow = newArr.filter(layout => { return layout.y === item.y })
      oldRow = oldArr.filter(layout => { return layout.y === item.y })
      if (newRow && oldRow && newRow.length !== oldRow.length) {
        newRow.map((row, index, layout) => {
          newArr.forEach(pre => {
            if (pre === row) {
              if (index === 0) { pre.x = 0 } else { pre.x = layout[index - 1].x + layout[index - 1].w }
            }
          })
        })
      }
    }
  })
  newArr.map((item, i, arr) => {
    if (i) {
      const { x, y, w, h } = arr[i - 1]
      const elh = y + h
      if (item.y > elh) {
        item.y = elh
      }
    } else {
      item.y = 0
    }
  })
  return newArr
}

// 移动端排序
export function layoutMobileSort(layout) {
  return layout.sort((a, b) => a.y - b.y || a.x - b.x).map((item, i, arr) => {
    if (i) {
      const { x, y, w, h } = arr[i - 1]
      item.y = y + h
    } else {
      item.x = 0
      item.y = 0
    }
    return item
  })
}
export function languageChangeWithAll(val) {
  const allCapital = this.$t('sdp.select.allCapital')
  const arr = ['ALL', 'all', 'All', allCapital]
  return arr.includes(val) ? allCapital : val
}

// 匹配全局参数列表的格式
export function formatterGranularity(selGranularity, defField) {
  let defValue = selGranularity?.[defField] ? JSON.parse(selGranularity[defField]) : {}
  if (!defValue) return []
  let list = defValue?.scope === SCOPE_TYPE.LIST && defValue?.list ? defValue.list : []
  if (selGranularity?.type !== PARAMETER_TYPE.NUMBER) return list
  let { type: fmtType, decimal } = defValue.format
  list.forEach(item => {
    let curVal = fmtType === NUMBER_FORMAT.NUMBER ? Number(item.value) : Number(item.value) / 100
    let itemType = fmtType === NUMBER_FORMAT.NUMBER ? 'number' : 'percentage'
    item.value = getFormatterFunc(itemType, 10 ** Number(decimal), true)(curVal)
  })
  return list
}
