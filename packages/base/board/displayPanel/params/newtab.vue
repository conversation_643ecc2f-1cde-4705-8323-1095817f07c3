<template>
  <div style="box-sizing: border-box;">
    <div v-if="!isMobileinPc && commonData.isPreview && activeNum===1 && !themeData.themeFullScreen" class="fix-height">
    </div>
    <div v-if="!isThemeFullScreen && (!commonData.isPreview || (activeNum>1))"
         :style="isMobileinPc ? {
            height: '48px',
            display: 'flex',
            alignItems: 'center'
         } : {
            height: '35px',
         }"
         class="pc-padding">
      <div
        class="tab-border"
        v-if="!commonData.isPreview || (activeNum>1)"
      >
        <div
          @click="adjust('back')"
          v-if="!commonData.isPreview && arrShow"
          class="icon-sdp-zuojiantou left-arrow arrow"
        ></div>
        <div
          @click="adjust('forward')"
          v-if="!commonData.isPreview && arrShow"
          class="icon-sdp-zuojiantou icon-trans right-arrow arrow"
        ></div>
        <div class="tab-wrapper" id="tabBorder" ref="tabBorder" :class="!commonData.isPreview && arrShow ? 'borderMargin' : ''">
          <ul id="tabTrans" v-if="!isMobileinPc" class="list-wrapper">
            <draggable
              v-model="copyParamsPanelList"
              v-if="!commonData.isPreview"
            >
              <transition-group>
                <div
                  v-for="item in paramsPanelList"
                  :key="item.id"
                  :class="{ 'not-allowed': !isFinish }"
                  class="params-content list-item"
                  :ref="item.id"
                >
                  <div
                    v-if="!commonData.isPreview || check(item)"
                    class="tab-style"
                    @dblclick="renameTabTitle(item)"
                    @click="onPanelChange(item, 'click')"
                  >
                    <span :class="[{ 'not-allowed': !isFinish }, item.active?'tab-select':'tab-noselect']" :style="tabBorder(item)" class="tab-font">{{item.label}}</span>
                    <img :class="{ 'not-allowed': !isFinish }" :src="deleteImg" v-if="!commonData.isPreview" @mousemove="changeStatus('remove')" @mouseleave="changeStatus('init')" @click="removeTab(item)" class="dtimg">
                    <div class="copy-btn" v-if="!commonData.isPreview" @click.stop="handleCopyTab(item)">
                      <div class="scale-box">
                        <i class="icon sdpiconfont icon-sdp-APPfuzhi"/>
                      </div>
                    </div>
                  </div>
                </div>
              </transition-group>
            </draggable>
            <template v-else>
              <div
                 v-for="item in paramsPanelList"
                 :key="item.id"
                 :class="{ 'not-allowed': !isFinish }"
                 class="params-content list-item"
                 :ref="item.id"
               >
                 <div
                   v-if="!commonData.isPreview || check(item)"
                   class="tab-style"
                   @dblclick="renameTabTitle(item)"
                   @click="onPanelChange(item, 'click')"
                 >
                   <span :class="[{ 'not-allowed': !isFinish }, item.active?'tab-select':'tab-noselect']" :style="tabBorder(item)" class="tab-font">{{item.label}}</span>
                   <img :class="{ 'not-allowed': !isFinish }" :src="deleteImg" v-if="!commonData.isPreview" @mousemove="changeStatus('remove')" @mouseleave="changeStatus('init')" @click="removeTab(item)" class="dtimg">
                 </div>
               </div>
            </template>
          </ul>
          <component
              :is="elScroll"
              ref="scroll"
              :options="{bounce: false}"
              :data="paramsPanelList"
              direction="horizontal"
              v-else
              class="horizontal-scroll-list-wrap"
          >
            <ul class="list-wrapper">
              <component
                 :is="draggable"
                 v-model="copyParamsPanelList"
                 style="float: left"
                 :options="{
                   filter: '.ignore-elements'
                 }"
                 ref="draggableRef"
              >
                <div
                  v-for="item in paramsPanelList"
                  :key="item.id"
                  class="params-content list-item"
                  :class="{
                    'not-allowed': !isFinish && !isMobileinPc,
                    'ignore-elements': utils.isPcMobile && !commonData.isPreview,
                  }"
                  :ref="item.id"
                >
                  <div
                    v-if="!commonData.isPreview || check(item)"
                    :class="{'tab-style-m': true, 'tab-edit-style': !commonData.isPreview && !utils.isPcMobile}"
                    @dblclick="renameTabTitle(item)"
                    @click="onPanelChange(item, 'click')"
                  >
                    <span :class="[{ 'not-allowed': !isFinish && !isMobileinPc}, item.active?'tab-select-m':'tab-noselect-m']" :style="tabBorder(item)" class="tab-font">{{item.label}}</span>
                    <img :class="{ 'not-allowed': !isFinish && !isMobileinPc}" :src="deleteImg" v-if="!commonData.isPreview && !utils.isPcMobile" @mousemove="changeStatus('remove')" @mouseleave="changeStatus('init')" @click="removeTab(item)" class="dtimg">
                    <div class="copy-btn" v-if="!commonData.isPreview && !utils.isPcMobile" @click.stop="handleCopyTab(item)">
                      <div class="scale-box">
                        <i class="icon sdpiconfont icon-sdp-APPfuzhi"/>
                      </div>
                    </div>
                  </div>
                </div>
              </component>
            </ul>
          </component>
        </div>
      </div>
    </div>
    <div style="position: relative;" :style="{ marginTop: isMobileinPc && activeNum===1 && commonData.isPreview  ? '15px' : '', height: isMobileinPc ? activeNum===1 && commonData.isPreview ? 'calc(100% - 15px)' : 'calc(100% - 48px)' : '100%' }" class="tab-panel-content">
      <slot name="tab-body"></slot>
    </div>
  </div>
</template>

<script>
import EventData from 'packages/assets/EventData'
import { STATIC_BASE_PATH } from 'packages/assets/constant'
import { EVENT_BUS } from '../constants'
const cubeScroll = 'cube-scroll'

export default {
  name: 'newtab',
  componentName: 'newtab',
  inject: ['commonData', 'utils', 'appPreviewParameterRefValue', 'themeData', 'sdpBus', 'runHandler'],
  props: {
    isThemeFullScreen: {
      type: Boolean
    },
    paramsPanelList: {
      type: Array,
      required: true,
    },
    tabId: {
      type: Array,
    },
    activeTabId: {
      type: String
    },
    isFinish: {
      type: Boolean
    },
    openBoardTab: {
      type: Boolean
    }
  },
  data () {
    return {
      deleteImg: STATIC_BASE_PATH.images + '/sdp-params-delete.png',
      status: 'init',
      fix: 0,
      arrShow: false,
      num: 0,
      activeId: 0,
      elScroll: 'div',
      draggable: 'div',
      move: 0,
      isSwitchActiveEl: false,
      cantEditParamElementList: []
    }
  },
  watch: {
    'layout': {
      handler (val, old) {
      },
    },
    'paramsPanelList.length': {
      handler () {
        this.reCalcOffset()
      },
    },
    'commonData.isPreview': {
      handler(val) {
        if (this.isMobileinPc) {
          this.elScroll = val || this.isMobile ? cubeScroll : 'div'
          this.$nextTick(() => {
            if (val) {
              this.move = 0
              const el = this.el()
              if (el) {
                el.style.transform = `translate(0px, 0px) scale(1) translateZ(0px)`
              }
            } else {
              this.reCalcOffset()
            }
            this.$_getProp(this, '$refs.scroll.refresh', function() {})()
          })
        }
      },
      immediate: true
    },
    isMobile: {
      handler(val) {
        this.draggable = val ? 'div' : 'draggable'
      },
      immediate: true
    },
    'appPreviewParameterRefValue.value'(val) {
      val && this.switchActiveEl()
    }
  },
  computed: {
    activeNum() {
      var num = 0
      const paramsPanelList = this.paramsPanelList
      paramsPanelList.forEach(item => {
        if (this.tabId.length && this.tabId.includes(item.labelName)) {
          this.tabId.forEach(a => {
            if (item.labelName === a) {
              num++
            }
          })
        } else {
          num++
        }
      })
      return num
    },
    copyParamsPanelList: {
      set(val) {
        const eventData = new EventData({
          data: {
            paramList: val,
            flag: true
          },
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'sortParamList'
        })
        this.$emit('eventBus', eventData)
      },
      get() {
        return this.paramsPanelList
      }
    },
    isMobile () {
      return this.commonData.isMobileApp
    },
    isMobileinPc () {
      return this.utils.isMobile
    }
  },
  mounted() {
    this['sdpBus'].$on(EVENT_BUS.CHANGE_PANEL_TAB, this.onTabsPanelChange)
    this['sdpBus'].$on(EVENT_BUS.NEW_TAB, this.paramElementHideEdit)
  },
  beforeDestroy() {
    this['sdpBus'].$off(EVENT_BUS.CHANGE_PANEL_TAB, this.onTabsPanelChange)
    this['sdpBus'].$off(EVENT_BUS.NEW_TAB, this.paramElementHideEdit)
  },
  methods: {
    paramElementHideEdit(data) {
      console.log('paramElementHideEdit: ', data)
      this.cantEditParamElementList = data
    },
    el() {
      return this.$_getProp(this, `$refs.draggableRef${this.isMobile ? '' : '.$el'}`, null)
    },
    adjust(flag) {
      this.$nextTick(() => {
        const scroll = this.$refs.scroll
        const el = this.el()
        if (scroll && el) {
          const num = scroll.offsetWidth + this.move
          const w = el.offsetWidth
          if (flag === 'back') {
            if (this.move > 50) {
              this.move -= 50
            } else {
              this.move = 0
            }
          } else {
            if (w - num > 50) {
              this.move += 50
            } else {
              this.move += w - num
            }
          }
          el.style.transform = `translate(-${this.move}px, 0px) scale(1) translateZ(0px)`
        }
      })
    },
    reCalcOffset() {
      if (!this.commonData.isPreview && this.isMobileinPc) {
        this.$nextTick(() => {
          const el = this.el()
          let innerWidth = el ? el.offsetWidth || 0 : 0
          let outerWidth = this.$refs.tabBorder ? this.$refs.tabBorder.offsetWidth || 0 : 0
          let x = innerWidth - outerWidth
          this.arrShow = x > 0
          if (!this.arrShow) {
            this.move = 0
            el.style.transform = `translate(0px, 0px) scale(1) translateZ(0px)`
          } else {
            if (innerWidth - this.move < outerWidth) {
              this.move = innerWidth - outerWidth
              el.style.transform = `translate(-${this.move}px, 0px) scale(1) translateZ(0px)`
            }
          }
        })
      }
    },
    addFixTab() { // 这一步现在变成定位active然后切换过去
      if (this.arrShow) {
        // this.$nextTick(() => {
        //   this.$refs.scroll.refresh()
        //   let x = document.getElementById('tabTrans').clientWidth - document.getElementById('tabBorder').clientWidth
        //   let offsetLeft = this.$refs[this.activeTabId][0].offsetLeft
        //   offsetLeft > x ? this.$refs.scroll.scrollTo(-x, 0, 700, 'swipe') : this.$refs.scroll.scrollTo(-offsetLeft, 0, 700, 'swipe')
        // })
      }
    },
    removeTab (item) {
      const eventData = new EventData({
        ...this.defaultEventData,
        data: item,
        target: 'tab',
        targetFn: 'removeTab'
      })
      this.$emit('eventBus', eventData)
    },
    // 复制组件tab
    handleCopyTab(item) {
      const eventData = new EventData({
        ...this.defaultEventData,
        data: item,
        target: 'tab',
        targetFn: 'handleCopyParamTab'
      })
      this.$emit('eventBus', eventData)
    },
    changeStatus (data) {
      this.status = data
    },
    check (item) {
      var rule = false
      if (this.tabId.length > 0) {
        this.tabId.forEach(a => {
          if (item.labelName && this.activeNum > 0) {
            if (item.labelName === a) {
              rule = true
            }
          } else {
            rule = true
          }
        })
        return rule
      } else {
        return true
      }
      // if (this.commonData.isPreview && this.tabId !== null && !item.labelName) {
      //   return item.labelName === this.tabId
      // } else {
      //   return true
      // }
    },
    changetab () {
      const eventData = new EventData({
        ...this.defaultEventData,
        target: 'tab',
        targetFn: 'changeActivePanelPreview'
      })
      this.$emit('eventBus', eventData)
    },
    tabBorder (data) {
      if (data.active) {
        return { borderBottom: '2px solid', color: '#222222' }
      } else {
        return { color: '#a4a4a4' }
      }
    },
    switchActiveEl() {
      if (this.isSwitchActiveEl) {
        this.$nextTick(() => {
          const scroll = this.$refs.scroll
          const scrollItem = this.$refs[this.paramsPanelList.find(item => item.active)?.id]
          if (scroll && scrollItem) {
            scroll.refresh()
            scroll.scrollToElement(Array.isArray(scrollItem) ? scrollItem[0] : scrollItem)
          }
        })
      }
      this.isSwitchActiveEl = false
    },
    renameTabTitle (data) {
      if (!this.isFinish && !this.isMobile) return
      if (this.utils.isPcMobile) return
      const eventData = new EventData({
        ...this.defaultEventData,
        data: data,
        target: 'tab',
        targetFn: 'renameTabTitle'
      })
      this.$emit('eventBus', eventData)
    },
    onTabsPanelChange(data) {
      this.onPanelChange(data)
      this.isSwitchActiveEl = true
    },
    onPanelChange (data, type) {
      if (type === 'click') {
        // 切换处理清空停止run
        this.runHandler.stopRun()
        this.$emit('clearParamGangedSave')
      }
      if (this.status !== 'remove') {
        if (!this.isFinish && !this.isMobile) return
        const eventData = new EventData({
          ...this.defaultEventData,
          data: data,
          target: 'tab',
          targetFn: 'onPanelChange'
        })
        this.$emit('eventBus', eventData)
      }
      // 在下一个tick内需要重新定位下 兼容火狐相同高度不触发
      if (!this.isMobile) {
        setTimeout(() => {
          const setTop = new EventData({
            target: ['displayPanel'],
            targetFn: 'watchSupernatantTop',
          })
          this.$emit('eventBus', setTop)
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import './utils/variable.scss';
.tab-font {
  cursor: pointer;
}
.tab-select {
  color: var(--sdp-xxbt1) !important;
}
.tab-noselect {
  color: var(--sdp-srk-bxwzs) !important;
}
.tab-select-m {
  color: $color-YXZTAB2 !important;
}
.tab-noselect-m {
  color: $color-WYXZTAB2 !important;
}
.borderMargin {
  overflow: hidden;
  margin-left: 16px;
  margin-right: 16px;
}
.borderPadding {
  padding-left: 12px;
  padding-right: 40px;
}
.params-content {
  display: inline-block;

  .copy-btn {
    width: 10px;
    height: 10px;
    line-height: 10px;
    position: absolute;
    right: 13px;
    top: 0;
    display: none;
    //Hover虚线
    background: #484848;

    .scale-box {
      width: 100%;
      height: 100%;
      line-height:10px;
      transform: scale(0.4);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    i {
      //按钮高亮- FFF
      color: #FFF;
    }
  }
}

.icon-sdp-guanbi {
  position: absolute;
  top: 0px;
  right: 0px;
  // display: none;
}
.icon-sdp-guanbi:hover {
  color: #a4a4a4;
  font-size: 10px;
  height: 20px;
  width: 20px;
  line-height: 20px;
  text-align: center;
  display: inline-block;
  margin-left: 4px;
  position: absolute;
  top: 0px;
  // display: none;
}
.normalType  .tab-style:hover,.tab-edit-style:hover{
  border: 1px dashed #B3B1B1;
  border-color: var(--sdp-hxx) !important;
}

.tab-style:hover .dtimg, .tab-style-m:hover .dtimg {
  display: block;
}

.tab-border {
  // width: 100%;
  // border-bottom: 1px solid #e4e7ed;
  box-sizing: border-box;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}
.tabTrans {
  width: 100%;
  position: relative;
  transition: transform 0.3s;
  padding-left: 10px;
  padding-right: 10px;
}
.tab-style-m {
  //border: 1px dashed transparent;
  display: inline-block;
  padding-left: 6px;
  padding-right: 6px;
  opacity: 0.9;
  font-family: NotoSansHans-Regular;
  font-weight: 800;
  font-size: 16px;
  text-align: left;
  line-height: 34px;
  height: 34px;
  position: relative;
  // float: left;

  &:hover {
    .copy-btn {
      display: block;
    }
  }
}
.tab-edit-style {
  border: 1px dashed transparent;
}
.tab-style {
  border: 1px dashed transparent;
  display: inline-block;
  padding-left: 6px;
  padding-right: 6px;
  opacity: 0.9;
  font-family: NotoSansHans-Regular;
  font-weight: 800;
  font-size: 16px;
  text-align: left;
  line-height: 38px;
  height: 38px;
  position: relative;
  // float: left;

  &:hover {
    .copy-btn {
      display: block;
    }
  }
}
.tab-style-select {
  border-bottom: 2px solid red;
}
.icon-trans {
  transform: rotate(180deg);
}
.arrow {
  display: inline-block;
  cursor: pointer;
  position: absolute;
  height: 35px;
  line-height: 35px;
  z-index: 10;
  font-size: 12px;
  color: #909399;
  top: 0;
}
.left-arrow {
  left: 0px;
}
.right-arrow {
  right: 0px;
}
.dtimg {
  position:absolute;
  top: 0px;
  right: 0px;
  width: 10px;
  display: none;
}

.tab-wrapper /deep/ {
  .horizontal-scroll-list-wrap {
    .cube-scroll-content {
      display: inline-block;
    }
    .list-wrapper {
      white-space: nowrap;
    }
    .list-item {
      display: inline-block;
    }
  }
}
.pc-padding {
  padding-left: 10px;
  padding-right: 10px;
}
.not-allowed {
  cursor: not-allowed;
}
.fix-height {
  height: 38px;
}
</style>
