<template>
  <div
    class="params-tabs"
    v-show="activeStatus.active"
    :class="!commonData.isPreview?'normalType':''"
  >
    <div
      class="tab-add-style"
      v-if="!isMobile"
    >
      <div
        style="display: inline-block;"
        v-if="!commonData.isPreview"
        @click="addParamPanel"
        @mousemove="addShow=false"
        @mouseout="addShow=true"
        :title="openBoardTab ? $t('sdp.views.addTabMode') : $t('sdp.views.AddParameterTAB')"
      >
        <span v-if="addShow"  class="icon sdpiconfont icon-sdp-add"></span>
        <span v-if="!addShow" class="icon sdpiconfont icon-sdp-addhover"></span>
        <!-- <span class="add-style">{{$t('sdp.views.add')}}</span> -->
      </div>
      <!-- <span :title="$t('sdp.views.componentSettings')" v-if="!commonData.isPreview" @click="gangedClick" class="icon sdpiconfont icon-sdp-sdp-canshuzujianguanlian ml"></span> -->
    </div>
    <div ref='point'></div>
    <new-tab
      :paramsPanelList="paramsPanelList"
      :tabId="tabId"
      :isFinish="isFinish"
      :openBoardTab="openBoardTab"
      :activeTabId="activeTabId"
      :isThemeFullScreen="isThemeFullScreen"
      ref="newtab"
      style="position: relative;height:100%;"
      @eventBus="eventBus"
      @clearParamGangedSave="$emit('clearParamGangedSave')"
    >
      <template slot="tab-body">
        <div
          v-for="panel in visibleParamsPanelList"
          :key="panel.id"
          :style="{
            height: panel.active ? '100%' : '0'
          }"
        >
          <div v-if="panel.active"
              style="height: 100%"
               class="newTab"
               :class="[
                 (panel.content.length === 0)&&isMobile?'tab-no-data-theme':'',
                 isThemeFullScreen ? 'newTab-auto' : 'newTab-min'
               ]"
          >
            <div
              v-if="!panel.content.length"
              class="tab-no-data"
              :class="(!isMobile) ? 'tab-no-data-border':''"
            >
              No data
            </div>
            <slot
              :isHide="!panel.content.length"
              style="min-width: 56px;"
              :isPreview="commonData.isPreview"
              :activeTabId="activeTabId"
              :param-list="panel"
              :layout="setThemeFullScreenLayout(panel.layout)"
              :setThemeFullScreenItemShow="setThemeFullScreenItemShow"
            ></slot>
          </div>
        </div>
      </template>
    </new-tab>
    <el-dialog
      :title="rename"
      :visible.sync="renameDialogShow"
      v-if="renameDialogShow"
      width="30%"
      :custom-class="`sdp-dialog ${getCurrentThemeClass()}`"
      :append-to-body="modalAppend"
      :close-on-click-modal="false"
      class="sdp-paramsetting param-font sdp-title-start"
    >
      <el-input
        style="width:100%;margin-top:20px;"
        v-model="editingTab.tempLabel"
        @keyup.enter.native="onRename"
      ></el-input>
      <div class="tab-dialog-bottom">
        <el-button
          type="sdp-ensure"
          @click="onRename"
        >{{$t('sdp.button.ensure')}}</el-button>
        <el-button type="sdp-cancel" @click="renameDialogShow = false">{{$t('sdp.button.cancel')}}</el-button>
      </div>
    </el-dialog>
    <paramSettingDialog
      v-if="paramSettingVisible"
      :visible.sync="paramSettingVisible"
      :param="editparam"
      :param-list="boardShareData"
      :nowStr="nowStr"
      :dynamicTags="dynamicTags"
      :datasetList="datasetList"
      :activeStatus="activeStatus"
      :boardInfo="boardInfo"
      @eventBus="eventBus"
      @cancel="cancelShow"
      @close="paramSettingVisible = false"
    />
    <el-dialog
      :title="Setup"
      :custom-class="`sdp-dialog no-grid-main-scss ${getCurrentThemeClass()}`"
      v-if="settingDialogShow"
      :visible.sync="settingDialogShow"
      width="980px"
      :top="editparam.type === 'bussinessCalendar'?'8vh':'15vh'"
      :height="editparam.type === 'bussinessCalendar' ? '' : '480px'"
      center
      :append-to-body="modalAppend"
      class="sdp-paramsetting param-font sdp-title-start"
      :close-on-click-modal="false"
    >
      <param-component-setting
        ref="param-component-setting"
        :param="editparam"
        :param-list="boardShareData"
        :nowStr="nowStr"
        :dynamicTags="dynamicTags"
        :datasetList="datasetList"
        :activeStatus="activeStatus"
        :boardInfo="boardInfo"
        @eventBus="eventBus"
      />
    </el-dialog>
    <el-dialog
      v-if="showFinancialCalendar"
      class="kyzScroll sdp-calendar param-font calendar-width calendar-width-f"
      :custom-class="`calendar-width calendar-width-f sdp-params-calendar-theme-background ${getCurrentThemeClass()}`"
      :modal="false"
      ref="calender"
      :top="financailPageY+'px'"
      :class="[isCalenderShow ? 'calenderShow' : 'calenderHide']"
      :append-to-body="false"
      :visible.sync="showFinancialCalendar"
      :style="transCalendar()"
    >
      <f-calendar
        :param-element="financialParam"
        :nowStr="nowStr"
        :board="boardShareData"
        :paramsPanelList="paramsPanelList"
        @eventBus="eventBus"
      />
    </el-dialog>
    <el-dialog
      v-if="showBussinessCalendar"
      ref="calender"
      :class="[isCalenderShow ? 'calenderShow' : 'calenderHide']"
      class="kyzScroll sdp-calendar param-font calendar-width-b"
      :custom-class="`calendar-width-b sdp-params-calendar-theme-background dialog-width-auto ${getCurrentThemeClass()}`"
      :modal="false"
      :top="financailPageY+'px'"
      :append-to-body="false"
      :visible.sync="showBussinessCalendar"
      :style="transCalendar()"
    >
      <b-calendar
        :param-element="financialParam"
        :nowStr="nowStr"
        :board="boardShareData"
        ref="bussiness"
        :locationData="locationData"
        :paramsPanelList="paramsPanelList"
        @eventBus="eventBus"
      />
    </el-dialog>
    <el-dialog
      :title="setTimeTop"
      :visible.sync="setTimeShow"
      v-if="setTimeShow"
      :custom-class="`sdp-dialog custom-calendar sdp-params-calendar-theme-background ${getCurrentThemeClass()}`"
      :close-on-click-modal="false"
      modal-append-to-body
      append-to-body
      class="sdp-paramsetting param-font sdp-title-start set-time-style"
    >
      <el-row>
        <el-col :span="24" class="bold-time-title">{{$t('sdp.views.CurrentPeriod')}}</el-col>
        <el-col :span="24" class="time-title sdp-params-calendar-font margin-top-12">{{$t('sdp.views.startDate')}}</el-col>
        <el-col :span="24">
          <el-date-picker
            style="width: 272px;"
            :default-time="defaultTime"
            :type="isSelectHMS ? 'datetime' : 'date'"
            :value-format="isSelectHMS? 'yyyy-MM-dd HH:mm:ss' : 'yyyy-MM-dd'"
            v-model="customData.startTime"
            class="sdp-params-theme-background"
            :popper-class="`sdp-params-customdata-style ${getCurrentThemeClass()}`"
            :placeholder="$t('sdp.placeholder.pleaseSelectDate')">
          </el-date-picker>
          <!-- <el-checkbox
            class='unlimited sdp-params-calendar-font'
            @change="clearDialogTime(true, 'startTime', 'startTimeLimint')"
            v-model="customData.startTimeLimint"
          >{{$t('sdp.views.Unlimited')}}</el-checkbox> -->
        </el-col>
        <el-col :span="24" class="margin-top-24 time-title sdp-params-calendar-font">{{$t('sdp.views.endDate')}}</el-col>
        <el-col :span="24">
          <el-date-picker
            style="width: 272px;"
            :default-time="defaultEndTime"
            v-model="customData.endTime"
            :popper-class="`sdp-params-customdata-style ${getCurrentThemeClass()}`"
            :type="isSelectHMS ? 'datetime' : 'date'"
            :value-format="isSelectHMS? 'yyyy-MM-dd HH:mm:ss' : 'yyyy-MM-dd'"
            class="sdp-params-theme-background"
            :placeholder="$t('sdp.placeholder.pleaseSelectDate')">
          </el-date-picker>
          <!-- <el-checkbox
            class='unlimited sdp-params-calendar-font'
            @change="clearDialogTime(true, 'endTime', 'endTimeLimint')"
            v-model="customData.endTimeLimint"
          >{{$t('sdp.views.Unlimited')}}</el-checkbox> -->
        </el-col>
      </el-row>
      <el-checkbox
        class='sdp-params-calendar-font custom-pri-style'
        v-model="customData.customPri"
        v-if="priorPeriodShow"
        @change="countPriData()"
      >
        <span>{{$t('sdp.views.CustomDateComparison')}}</span>
      </el-checkbox>
      <el-row v-if="customData.customPri">
        <el-col :span="24" class="margin-top-12 bold-time-title">{{$t('sdp.views.PriorPeriod')}}</el-col>
        <el-col :span="24" class="time-title sdp-params-calendar-font margin-top-12">{{$t('sdp.views.startDate')}}</el-col>
        <el-col :span="24">
          <el-date-picker
            style="width: 272px;"
            :default-time="defaultTime"
            v-model="priData.startTime"
            :type="isSelectHMS ? 'datetime' : 'date'"
            :value-format="isSelectHMS? 'yyyy-MM-dd HH:mm:ss' : 'yyyy-MM-dd'"
            class="sdp-params-theme-background"
            :popper-class="`sdp-params-customdata-style ${getCurrentThemeClass()}`"
            @change="setHandSelect()"
            :placeholder="$t('sdp.placeholder.pleaseSelectDate')">
          </el-date-picker>
          <!-- <el-checkbox
            class='unlimited sdp-params-calendar-font'
            @change="clearDialogTime(false, 'startTime', 'startTimeLimint')"
            v-model="priData.startTimeLimint"
          >{{$t('sdp.views.Unlimited')}}</el-checkbox> -->
        </el-col>
        <el-col :span="24" class="time-title sdp-params-calendar-font margin-top-24">{{$t('sdp.views.endDate')}}</el-col>
        <el-col :span="24">
          <el-date-picker
            style="width: 272px;"
            :default-time="defaultEndTime"
            v-model="priData.endTime"
            :type="isSelectHMS ? 'datetime' : 'date'"
            :value-format="isSelectHMS? 'yyyy-MM-dd HH:mm:ss' : 'yyyy-MM-dd'"
            :popper-class="`sdp-params-customdata-style ${getCurrentThemeClass()}`"
            class="sdp-params-theme-background"
            @change="setHandSelect()"
            :placeholder="$t('sdp.placeholder.pleaseSelectDate')">
          </el-date-picker>
          <!-- <el-checkbox
            class='unlimited sdp-params-calendar-font'
            @change="clearDialogTime(false, 'endTime', 'endTimeLimint')"
            v-model="priData.endTimeLimint"
          >{{$t('sdp.views.Unlimited')}}</el-checkbox> -->
        </el-col>
      </el-row>
      <div class="tab-dialog-bottom" style="margin-top: 24px;">
        <el-button
          type="sdp-ensure"
          @click="onSetTime"
        >{{$t('sdp.button.ensure')}}</el-button>
        <el-button type="sdp-cancel" @click="setTimeShow = false">{{$t('sdp.button.cancel')}}</el-button>
      </div>
    </el-dialog>
    <!-- 参数组件关联 -->
    <el-dialog
      :title="$t('sdp.views.associationSettings')"
      append-to-body
      width="640px"
      :custom-class="`sdp-ganged sdp-dialog sdp-paramsetting sdp-params-calendar-theme-background ${getCurrentThemeClass()}`"
      :visible.sync="gangedDialog">
      <param-ganged
        ref="paramGanged"
        :paramsActive="activeStatus"
        :paramGangedList="boardInfo.paramGangedList"
        :gangedDialog="gangedDialog"
      />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="gangedSave">{{$t('sdp.button.ensure')}}</el-button>
        <el-button @click="gangedDialog = false">{{$t('sdp.button.cancel')}}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="$t('sdp.views.ParameterComponentTemplate')"
      append-to-body
      width="980px"
      :custom-class="`sdp-ganged sdp-dialog sdp-dataSet-dialog ${getCurrentThemeClass()}`"
      :boardInfo="boardInfo"
      :visible.sync="publicParamsShow">
      <public-params ref="publicParams" @addParamModel="addParamModel" @closeParamModel="closeParamModel" :boardInfo="boardInfo"/>
    </el-dialog>
  </div>
</template>

<script>
// import ParamComponent from './ParamComponent.vue'
import { EVENT_BUS, TYPE_SUPERSETTING, TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import newTab from './newtab.vue'
import ParamComponentSetting from './ParamComponentSetting.vue'
import publicParams from './publicParams.vue'
import fCalendar from './paramElement/financialCalendar/Calendar.vue'
import bCalendar from './paramElement/bussinessCalendar/pc/render/Calendar.vue'
import paramGanged from './components/paramGanged.vue'
import eventBus from 'packages/assets/eventBus'
import EventData from 'packages/assets/EventData'
import { generateOriginEllist, generateLanguageItem, setCanvasBgDefaultData } from '../components/utils'
import { TYPE_PARAM_ELEMENT } from './utils/constants'
import { BoardParamElement } from './utils/boardParamElement'
import { getLabelList, getDateIntervalPeriod } from './api'
import elementTypeList, { PARAM_GANGED_TYPE, ParamGangedClass } from './bridge'
import Vue from 'vue'
import emitter from 'packages/assets/emitter'
import { customAdapter, layoutFullScreenSort, layoutHideParamsSort } from './utils/utils'
import { CREATE_TOAST_TIME, GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS, THEME_TYPE } from 'packages/assets/constant'
import titleFuncMixin from 'packages/base/board/displayPanel/params/components/titleFuncMixin'
import {getSetBindElementsVar, TITLE_STYLE} from '../utils'
import {COPY_TO_OTHER_PAGE} from "../supernatant/largeScreen/constant";
import { defaultStyle } from './components/constant'
import ParamSettingDialog from './components/paramSettingDialog'
import { ALL_PROJECT_NAME } from '../../../../components/mixins/commonMixin'
import {MAX_COUNT_ELEMENTS_IN_BOARD} from "../constants";
import { t } from '../../../../assets/locale'

const isDev = process.env.NODE_ENV === 'development'

export default {
  name: 'tab',
  componentName: 'tab',
  mixins: [emitter, titleFuncMixin],
  inject: ['commonData', 'utils', 'themeData', 'getDataReport', 'datasetList', 'getBoardUnderIdElement', 'newBoardContent', 'getCurrentThemeClass', 'newParamElement', 'getActiveDisplayPanel', 'sdpBus'],
  components: {
    ParamSettingDialog,
    newTab,
    fCalendar,
    bCalendar,
    publicParams,
    // ParamComponent,
    ParamComponentSetting,
    paramGanged,
  },

  props: {
    elementComp: {
      type: Array,
      default: () => []
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isServer: {
      type: Boolean,
      default: false
    },
    boardInfo: {
      type: Object
    },
    displayRef: {
      type: Object
    },
    nowStr: {
      type: String,
    },
    locationData: {
      type: Object
    },
    languageAndCurrency: {
      type: Object
    },
    isEnterPriseCurrency: {
      type: Boolean
    },
    elList: {
      type: Array,
      required: true,
    },
    paramsPanelList: {
      type: Array,
      required: true,
    },
    orderParamsList: {
      type: Array,
    },
    dynamicTags: {
      type: Array,
    },
    // 预览类型
    previewType: {
      type: String,
      default: '',
    },
    levelData: {
      type: Object,
      default: () => ({})
    },
    paramsType: {
      type: String
    },
    finishResult: {
      type: Boolean,
      default: false
    },
    boardStatus: {
      type: String,
    },
    activeTabIds: {
      type: String,
    },
    conformityApiData: {
      type: Object,
    },
    isFinish: {
      type: Boolean
    },
    isThemeFullScreen: {
      type: Boolean
    },
    paramsDataItem: {
      type: Object
    },
    storeSet: {
      type: Object,
      default: () => ({})
    },
  },
  data () {
    return {
      paramGangedClass: new ParamGangedClass({
        vm: this,
        sdpBus: this.sdpBus
      }),
      isSelectHMS: undefined,
      addShow: true,
      editparam: {},
      publicParamsShow: false,
      renameDialogShow: false,
      settingDialogShow: false,
      paramSettingVisible: false,
      setTimeShow: false,
      showFinancialCalendar: false,
      showBussinessCalendar: false,
      isCalenderShow: false,
      status: true,
      modalAppend: true,
      show: false,
      defaultTime: '00:00:00',
      defaultEndTime: '23:59:59',
      rename: this.$t('sdp.button.reName'),
      Setup: this.$t('sdp.views.Setup'),
      editingTab: {
        tab: {},
        tempLabel: ''
      },
      financialParam: {},
      financailWidth: 0,
      financailPageY: 0,
      activeTabId: '',
      calendarX: 0,
      levelHeightList: [],
      boardShareData: {
        elList: this.elList,
        paramsPanelList: this.paramsPanelList,
        openBoardTab: this.openBoardTab,
        tagModeStack: this.boardInfo.tagModeStack
      },
      tabId: [],
      customData: {}, // 日历组件 自定义日期
      priData: {}, // 日历组件 自定义日期
      setTimeTop: this.$t('sdp.views.SelectDateRange'),
      // 参数组件联动
      gangedDialog: false,
      mobileParamCount: 1, // 对移动端新增count的计数值
      mobileEditParamCount: 0, // 对移动端编辑的count计数值
      maxCountLength: 6, // 最大的计数值
      isDelete: false, // 是否进行删除过
      timer: '', // 定时器变量
      preTabId: '',
      oldTabId: ''
    }
  },
  computed: {
    priorPeriodShow() {
      let params = this.activeStatus.content.find(item => item.id === this.customData.id) || undefined
      if (params) {
        return params.content.priorPeriod
      } else {
        return false
      }
    },
    dataReport() {
      return this.getDataReport?.() || null
    },
    openBoardTab() {
      return this.boardInfo.openBoardTab
    },
    isMobile () {
      return this.utils.isMobile
    },
    isScreen () {
      return this.utils.isScreen
    },
    api () {
      return this.utils.api || function () { }
    },
    // 初始化默认选择第一个
    // activeTabIds () {
    //   return this.paramsPanelList[0].id
    // },
    activeStatus () {
      let paramsActive = this.paramsPanelList.find(item => item.active)
      // console.log(paramsActive)
      return paramsActive
    },
    getServeExportFlag () {
      return false
    },
    projectName() {
      return this.utils.env?.projectName || ''
    },
    isSbiType() {
      return this.projectName === ALL_PROJECT_NAME.SBI || false
    },
    visibleParamsPanelList() {
      if ((this.isMobile && this.utils.isPcMobile) || this.commonData.isMobileDataReport()) {
        const paramsList = []
        const paramDIC = this.storeSet?.mobileSet?.paramList || {}

        this.paramsPanelList.forEach(panel => {
          const pan = {
            ...panel,
            content: [],
            layout: [],
          }

          const checked = paramDIC[panel.id] || []
          panel.content.forEach(item => {
              checked.includes(item.id) && pan.content.push(item)
          })
          panel.layout.forEach(item => {
              checked.includes(item.i) && pan.layout.push(item)
          })

          paramsList.push(pan)
        })

        return paramsList
      } else {
        return this.paramsPanelList
      }
    }
  },
  watch: {
    'commonData.isPreview'(val) {
      val || (this.showBussinessCalendar = false)
    },
    openBoardTab(val) {
      this.boardShareData.openBoardTab = val
      this.$nextTick(() => {
        this.boardShareData.tagModeStack = this.boardInfo.tagModeStack
      })
    },
    conformityApiData(val) {
      if (val.bindBoardTagList) {
        this.getList(false, val.bindBoardTagList)
      } else {
        this.getList(true, val.bindBoardTagList)
      }
    },
    // 'activeStatus.id' (val) {
    //   this.setActiveId()
    // },
    // boardStatus () {
    //   this.$nextTick(() => {
    //     this.restData()
    //   })
    // },
    activeTabIds: {
      handler (val) {
        this.activeTabId = val
      },
      immediate: true,
    },
    activeTabId: {
      handler (val, old) {
        if (val === old) return
        if (val !== old) {
          if (old) {
            this.oldTabId = old
            this.paramsPanelList.some(el => {
              const bool = el.id === old
              if (bool) {
                this.$set(el, 'status', TYPE_SUPERSETTING.PLAN_RASK)
              }
              return bool
            })
          }
        }
        this.changeActivePanel(this.preTabId ? this.preTabId : val, true)
      },
      immediate: true,
    },
  },
  destroyed() {
    // 清空数据，防止内存泄露
    this.boardShareData.elList = null
    this.boardShareData.paramsPanelList = null
    this.boardShareData = null
    clearTimeout(this.timer)
  },
  created() {
    this.getList = this.$_debounce(this.getList, 500)
  },
  mounted () {
    this.boardShareData.paramsPanelList.forEach(item => {
      this.$set(item, 'layout', [])
    })
    if (this.utils.isDataReport && !this.utils.isPcMobile) {
      !this.boardShareData.openBoardTab && (this.boardShareData.openBoardTab = this.boardInfo.openBoardTab)
      this.$nextTick(() => {
        !this.boardShareData.tagModeStack && (this.boardShareData.tagModeStack = this.boardInfo.tagModeStack)
      })
    }
  },
  methods: {
    openModul() {
      this.publicParamsShow = true
      this.$nextTick(() => {
        this.$refs.publicParams.init()
      })
    },
    closeParamModel() {
      this.publicParamsShow = false
    },
    // 大屏重新设置layout，隐藏组件也需要
    setThemeFullScreenLayout(layout) {
      let layoutArr = layout
      let cloneLayout = []
      // if ((this.isThemeFullScreen && !this.isScreen)) {
      //   cloneLayout = this.$_JSONClone(layout.filter(({ i }) => this.setThemeFullScreenItemShow(i)))
      //   layoutArr = layoutFullScreenSort(cloneLayout)
      // } else
      if (this.commonData.isPreview) {
        cloneLayout = this.$_JSONClone(layout.filter(({ i }) => this.setElementScreenShow(i)))
        if (!cloneLayout.length) layoutArr = this.$_JSONClone(cloneLayout)
      }
      if (cloneLayout.length && cloneLayout.length !== layout.length) {
        // 重新排序位置 有隐藏的组件时
        layoutArr = layoutHideParamsSort(this.$_JSONClone(layout), cloneLayout)
      }
      this.$emit('update:layoutLen', layoutArr.length)
      return layoutArr
    },
    // 是否隐藏参数组件
    setElementScreenShow(id) {
      if (!this.commonData.isPreview) return true
      let curItem = this.activeStatus.content.find(item => {
        return item.id === id
      })
      let { isHideElement = false } = curItem || {}
      return !isHideElement
    },
    // 是否显示组件
    setThemeFullScreenItemShow(id) {
      const { open = false } = this.paramsDataItem ? this.paramsDataItem.content[id] || {} : {}
      return this.setElementScreenShow(id) && (this.isScreen || !this.isThemeFullScreen || open)
    },
    recordId(val) { // 通知是否是弹窗超链接
      this.preTabId = val
    },
    // 勾选不限时清空对应选项
    setHandSelect() {
      this.customData.handSelect = true
    },
    clearDialogTime(type = true, str1 = 'startTime', str2 = 'startTimeLimint') {
      if (type) {
        if (this.customData[str2]) {
          this.$set(this.customData, str1, '')
        }
      } else {
        this.customData.handSelect = true
        if (this.priData[str2]) {
          this.$set(this.priData, str1, '')
        }
      }
    },
    outletIdsList () {
      const location = []
      this.boardShareData.paramsPanelList.forEach((val) => {
        if (val.active) {
          val.content.forEach((v) => {
            if (this.locationData.type === 'locationQuick') {
              if (v.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK) {
                location.push(v.content.options)
              }
            } else {
              if (v.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
                location.push(v.content.options)
              }
            }
          })
        }
      })
      if (location.length >= 1) {
        this.locationDatas = location[0]
        return location[0]
      } else {
        this.locationDatas = null
        return null
      }
    },
    async countPriData() {
      const location = this.outletIdsList()
      this.$set(this, 'priData', {})
      if (this.customData.customPri && !this.customData.startTimeLimint && !this.customData.endTimeLimint) {
        let params = {}
        // let shopId = this.locationData.shopIds
        if (location) {
          params = {
            tenantId: this.utils.tenantId,
            calendarComponents: [customAdapter(this, false)],
            locationCombinations: [location]
          }
        } else {
          params = {
            tenantId: this.utils.tenantId,
            calendarComponents: [customAdapter(this, false)],
          }
        }
        let data = await getDateIntervalPeriod(this.api, params)
        if (data.length) {
          let year_on_year = data[0]['pri_period_key_year-on-year'] || {}
          // this.priData.startTime = this_period_key.startDate || ''
          if (this.customData.startTime) {
            this.$set(this.priData, 'startTime', year_on_year.startDate || '')
          }
          // this.priData.endTime = this_period_key.endDate || ''
          if (this.customData.endTime) {
            this.$set(this.priData, 'endTime', year_on_year.endDate || '')
          }
          if (this.customData.startTime && this.customData.endTime) {
            this.customData.handSelect = false
          }
        }
      }
      if (!this.customData.customPri) {
        this.$set(this.priData, 'startTime', '')
        this.$set(this.priData, 'endTime', '')
        this.customData.handSelect = false
      }
    },
    // 关联看板数据的保存
    gangedSave() {
      this.$nextTick(() => {
        this.$emit('clearParamGangedSave')
        const isEquals = this.$refs.paramGanged.save()
        if (!isEquals) {
          const { paramGangedList = {} } = this.boardInfo
          this.paramGangedClass.touchOffParamsGanged({
            data: paramGangedList[this.activeTabId] || [],
            status: PARAM_GANGED_TYPE.SAVE
          })
        }
        this.gangedDialog = false
      })
    },
    eventBus,
    // 参数组件联动
    gangedClick() {
      this.gangedDialog = true
    },
    async getList(check = true, res = []) {
      // var data = await getLabelList(this.api, this.utils.tenantId)
      var data
      if (check) {
        data = await getLabelList(this.api, this.utils.tenantId)
      } else {
        data = res
      }
      // console.log(res)
      // const data = []
      this.tabId = []
      data.forEach(item => {
        if (item.check) {
          this.tabId.push(item.id.toString())
        }
      })
    },
    transCalendar () {
      return { marginLeft: this.calendarX + 'px' }
    },
    setCalendarX () {
      let address = this.$refs.point.getBoundingClientRect()
      this.calendarX = address.left
    },
    // 重置参数组件布局
    restData() {
      if (this.utils.isDataReport && this?.getActiveDisplayPanel) {
        const activeComp = this.getActiveDisplayPanel()
        if (activeComp === 'PC' && this.utils.isPcMobileEdit) return
        if (activeComp === 'MOBILE' && !this.utils.isPcMobileEdit) return
        console.log('在当前激活组件中执行了！', this.$el, this.utils, this.getActiveDisplayPanel())
      }
      var nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      if (nowTab && nowTab.content && nowTab.content.length) {
        this.restLayout(nowTab)
      }
    },
    openSetTime (data) {
      this.$set(this, 'customData', data.data.customData)
      this.$set(this, 'priData', data.data.priData)
      this.$set(this, 'isSelectHMS', !!(data.data.isSelectHMS === undefined || data.data.isSelectHMS))
      data.data.cb && (this.customDataCb = data.data.cb)
      this.$nextTick(() => {
        this.setTimeShow = true
      })
    },
    onSetTime() {
      // console.log(this)
      // console.log(this.customData.id)
      // console.log(this.$parent.$refs[this.customData.id][0])
      // this.$parent.$refs[this.customData.id][0].wss()
      var nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      let customDataObj = {
        customData: this.customData,
        priData: this.priData
      }
      var check = 0
      let err = this.$t('sdp.message.startendtime')
      Object.keys(customDataObj).forEach(item => {
        if (!customDataObj[item].startTime) {
          customDataObj[item].startTime = ''
        }
        if (!customDataObj[item].endTime) {
          customDataObj[item].endTime = ''
        }
        // if (((this.customData.customPri && item === 'priData') || item !== 'priData') && ((!customDataObj[item].startTimeLimint && (customDataObj[item].startTime === '' || customDataObj[item].startTime === null)) || (!customDataObj[item].endTimeLimint && (customDataObj[item].endTime === '' || customDataObj[item].endTime === null)))) {
        //   err = this.$t('sdp.views.PleaseSelectTheDate')
        //   return false
        // }
        let startTime = customDataObj[item].startTime ? customDataObj[item].startTime.replace(new RegExp(/-/gm), '/') : ''
        let dd = new Date(startTime)
        let bb = dd.getTime(dd)
        let endTime = customDataObj[item].endTime ? customDataObj[item].endTime.replace(new RegExp(/-/gm), '/') : ''
        let dl = new Date(endTime)
        let cv = dl.getTime(dl)
        if (customDataObj[item].startTime === '' || customDataObj[item].endTime === '' || bb <= cv) {
          check++
        }
      })
      // bussiness
      if (check === Object.keys(customDataObj).length) {
        if (this.customData.type === 'bussinessCalendar') {
          // const bussinessContent = this.$refs.bussiness
          const { customPri, startTime, endTime } = customDataObj.customData
          const { startTime: priStartTime, endTime: priEndTime } = customDataObj.priData
          if ((startTime && endTime && !customPri) || (startTime && endTime && customPri && priStartTime && priEndTime)) {
              this.customDataCb && this.customDataCb(this.$_deepClone(customDataObj))
          } else {
            if (startTime === '' || (customPri && priStartTime === '')) {
              return this.$message.info(this.$t('sdp.message.lackOfStartTime'))
            }
            if (endTime === '' || (customPri && priEndTime === '')) {
              return this.$message.info(this.$t('sdp.message.lackOfEndTime'))
            }
            // this.$refs.bussiness.clearDate(true)

          }
        } else {
          nowTab.content.forEach(item => {
            if (item.id === this.customData.id) {
              item.content.customDataObj = customDataObj
            }
          })
          this.$parent.$refs[this.customData.id][0].changeSelect()
        }
        this.setTimeShow = false
      } else {
        // startendtime
        this.$message.info(err)
      }
    },
    setFinancailWidth(data) {
      this.financailWidth = data.width
    },
    reSetWidth(data) {
      const client = document.body.clientWidth || document.body.offsetWidth
      const clientH = document.body.clientHeight || document.body.offsetHeight
      const params = data.data.params
      // console.log(params)
      switch (data.data.type) {
        case 'Year':
        case 'Month':
        case 'Interval':
          this.financailWidth = 489
          break
        default:
          if (!params.quick && !params.calendarshow && params.isDefault) {
            this.financailWidth = client >= 1314 ? 1458 : 1219
          } else if (!params.quick && params.calendarshow) {
            this.financailWidth = 385
          } else if (!params.calendarshow) {
            this.financailWidth = client >= 1314 ? 1314 : 1219
          }
          break
      }
      // console.log(this.financailWidth)
      // this.financailWidth = client >= 1314 ? 1474 : 1219
    },
    // 打开日历
    async clickHandle (data) {
      this.isCalenderShow = false
      this.setCalendarX()
      const client = document.body.clientWidth || document.body.offsetWidth
      const clientH = document.body.clientHeight || document.body.offsetHeight
      this.financailPageY = data.data.rect.top + 50
      if (data.data.paramElement) {
        this.financialParam = this.$_deepClone(data.data.paramElement)
      }
      let financailWidth = 0
      if (this.financialParam.type === 'bussinessCalendar') {
        financailWidth = client >= 1314 ? 1458 : 1219
      } else {
        financailWidth = client >= 1314 ? 1314 : 1080
      }
      if (this.commonData.isPreview) {
        financailWidth = client >= 1328 ? 1328 : 1190
        this.$nextTick(() => {
          [...document.querySelectorAll('.kyz-custom1')].forEach((item) => {
            if (!item.style.display) {
              if (!item.getAttribute('style').includes('height')) {
                const num = `${item.getAttribute('style')}height:580px;`
                item.setAttribute('style', num)
              }
            }
          })
        })
      }
      this.financailWidth = financailWidth
      if (TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR === data.data.type || TYPE_PARAM_ELEMENT.SELECT_PIERIOD_FINANCIAL === data.data.type) {
        // this.typeFinancial = data.data.type
        this.showFinancialCalendar = true
      } else {
        // this.typeBussiness = data.data.type
        this.showBussinessCalendar = true
      }
      setTimeout(() => {
        const calender = this.$refs.calender.$el
        let w = calender.offsetWidth
        if (data.data.paramElement.type === 'bussinessCalendar') {
          const { calendarshow, isYear, isDefault, isInterval, isMonth } = data.data.paramElement.content
          if (calendarshow === false && isDefault) {
            w = client > 1366 ? 1460 : 1210
          } else if (calendarshow === false) {
            w = 1310
          } else if (isYear || isMonth || isInterval) {
            w = 490
          } else if (isDefault) {
            w = 320
          }
        }
        const dom = this.getBoardUnderIdElement('#data-screen-table')
        let { width } = this.$_getRect(dom)
        //
        const diff = width - (w + 20)
        const l = data.data.paramElement.content.layout.x * width / GRID_LAYOUT_COL_NUM_FOR_BORDER_PARAMS
        // const rw = data.data.rect.y + data.data.rect.width
        // if (rw > w) {
        //   calender.style.left = l + 'px'
        // } else {
        //   calender.style.rigth = '10px'
        // }
        calender.style.left = `${diff > l ? l : diff}px`
        this.$nextTick(() => {
          this.isCalenderShow = true
        })
      })
    },
    closeFinancialCalendar (obj, quick = false) {
      var data = obj.data.paramElement
      var nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      this.$set(data, 'skipStatus', false)
      this.$set(data.content, 'scattered', true)
      for (let i = 0, len = nowTab.content.length; i < len; i++) {
        if (nowTab.content[i].id === data.id) {
          const _this = this.$parent.$refs[data.id][0]
          this.$delete(data.content, 'quickClauses')
          if (data.content.dtList && data.content.dtList.length !== 1) {
            this.$delete(data.content, 'camparePeriod')
          }
          this.$set(_this, 'value2', '')
          this.$parent.$refs[data.id][0].restValue2()
          if (quick) {
            data.content.status = true
          }
          // const { layout } = nowTab
          // if (nowTab.content[i].layout) {
          //   data.content.layout = layout.find(el => el.i === data.id) || nowTab.content[i].layout
          // }
          this.$set(nowTab.content, [i], data)
          if (!obj.data.monthNotSupport) {
            if ((!data.content.dtList || data.content.dtList.length === 0) && (!data.content.priDtList || data.content.priDtList.length === 0)) {
              this.$parent.$refs[data.id][0].finishHook('unfinish')
            } else {
              this.$parent.$refs[data.id][0].finishHook('finish')
            }
          } else {
            this.$parent.$refs[data.id][0].finishHook('unfinish', this.$t('sdp.message.ScatteredDateSelectionDoesNotSupport'))
          }
        }
      }
      this.restLayout(nowTab)
      this.showFinancialCalendar = false
    },
    closeBussinessCalendar (obj, quick = false) {
      var data = obj.data.paramElement
      var nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      this.$set(data, 'skipStatus', false)
      this.$set(data.content, 'scattered', true)
      for (let i = 0, len = nowTab.content.length; i < len; i++) {
        if (nowTab.content[i].id === data.id) {
          data.content.handSelect !== 'default' && this.$delete(data.content, 'quickClauses')
          if (data.content.dtList && data.content.dtList.length !== 1) {
            this.$delete(data.content, 'camparePeriod')
          }
          this.$nextTick(() => {
            this.$parent.$refs[data.id][0].restValue2()
          })
          if (quick) {
            data.content.status = true
          }
          // const { layout } = nowTab
          // if (nowTab.content[i].layout) {
          //   data.content.layout = layout.find(el => el.i === data.id) || nowTab.content[i].layout
          // }
          this.$set(nowTab.content, [i], data)
          if (!obj.data.monthNotSupport) {
            if ((!data.content.dtList || data.content.dtList.length === 0) && (!data.content.priDtList || data.content.priDtList.length === 0)) {
              this.$parent.$refs[data.id][0].finishHook('unfinish')
            } else {
              this.$parent.$refs[data.id][0].finishHook('finish')
            }
          } else {
            var str = this.$t('sdp.message.ScatteredDateSelectionDoesNotSupport')
            this.$parent.$refs[data.id][0].finishHook('unfinish', this.$t('sdp.message.ScatteredDateSelectionDoesNotSupport'))
          }
        }
      }
      this.restLayout(nowTab)
      this.showBussinessCalendar = false
    },
    adapterBussinessCalendar({ data }) {
      const nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      const paramElement = data.paramElement
      if (Array.isArray(nowTab.content)) {
        nowTab.content.forEach((item, i) => {
          // 数据替换
          if (item.id === paramElement.id) {
            const { layout } = nowTab
            if (item.content.layout) {
              paramElement.content.layout = layout.find(el => el.i === item.id) || item.content.layout
            }
            this.$set(nowTab.content, [i], paramElement)
            this.$nextTick(() => {
              const elementCompItem = this.elementComp.find(el => el?.paramElement?.id === paramElement.id)
              if (elementCompItem && elementCompItem?.restUserDate) {
                elementCompItem.restUserDate()
              }
            })
          }
        })
      }

      // var nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      // // console.log(data)
      // for (let i = 0, len = nowTab.content.length; i < len; i++) {
      //   if (nowTab.content[i].id === data.data.paramElement.id) {
      //     // console.log(data)
      //     this.$set(nowTab.content, [i], data.data.paramElement)
      //       if (Array.isArray(this.$parent.$refs[nowTab.content[i].id])) {
      //         if (this.$parent.$refs[nowTab.content[i].id][0].restUserDate) {
      //           this.$nextTick(() => {
      //             this.$parent.$refs[nowTab.content[i].id][0].restUserDate()
      //           })
      //         }
      //       } else {
      //         if (this.$parent.$refs[nowTab.content[i].id].restUserDate) {
      //           this.$nextTick(() => {
      //             this.$parent.$refs[nowTab.content[i].id].restUserDate()
      //           })
      //         }
      //       }
      //   }
      // }
      this.showBussinessCalendar = false
    },
    dispatchChangePlaceholder({ data }) {
      if (!this.isMobile) {
        data.type === 'financial' ? this.broadcast('fCalendarPc', 'updataPlaceholder', data) : this.broadcast('bussinessCalendarPc', 'updataPlaceholder', data)
      }
    },
    // 删除tab
    removeTab (data) {
      if (data.data.labelName) {
        this.$message.warning(this.$t('sdp.message.tagIsAlready'))
      } else {
        if (this.boardInfo.openBoardTab) {
          this.$sdp_eng_confirm(this.$t('sdp.message.sureDeleteTagMsg'), this.$t('sdp.dialog.hint'), {
            confirmButtonText: this.$t('sdp.button.ensure'),
            cancelButtonText: this.$t('sdp.button.cancel'),
            cancelButtonClass: 'confirm-cancel el-button--sdp-cancel',
            confirmButtonClass: 'el-button--sdp-ensure',
            customClass: 'sdp-dialog',
            type: 'warning',
            closeOnClickModal: false,
          }).then(() => {
            this._removeTab(data)
          }).catch(() => {})
        } else {
          this.$sdp_eng_confirm(this.$t('sdp.message.sureDeleteMsg'), this.$t('sdp.dialog.hint'), {
            confirmButtonText: this.$t('sdp.button.ensure'),
            cancelButtonText: this.$t('sdp.button.cancel'),
            cancelButtonClass: 'confirm-cancel el-button--sdp-cancel',
            confirmButtonClass: 'el-button--sdp-ensure',
            customClass: 'sdp-dialog',
            type: 'warning',
            closeOnClickModal: false,
          }).then(() => {
            this._removeTab(data)
          }).catch(() => {})
        }
      }
    },
    _removeTab(data) {
      const tabs = this.paramsPanelList
      if (tabs.length === 1) {
        this.$message.warning(this.$t('sdp.message.cannotDelete'))
      } else {
        let activeId = this.activeTabId
        if (activeId === data.data.id) {
          tabs.forEach((tab, index) => {
            if (tab.id === data.data.id) {
              const nextTab = tabs[index + 1] || tabs[index - 1]
              if (nextTab) {
                activeId = nextTab.id
              }
            }
          })
        }
        this.changeActivePanel(activeId)
        this.paramsPanelList.splice(tabs.findIndex(tab => tab.id === data.data.id), 1)
        this.reCalcOffset()
        if (!this.isMobile && this.openBoardTab) {
          this.$delete(this.boardInfo.tagModeStack.settingTitle, data.data.id)
          const eventData = new EventData({
            ...this.defaultEventData,
            target: ['displayPanel'],
            targetFn: 'deleteTag',
            data: data.data.id
          })
          this.$emit('eventBus', eventData)
        }
        let tabData = this.paramsPanelList.find(item => item.id === activeId)
        const removeData = new EventData({
          ...this.defaultEventData,
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'initParamsType',
          data: tabData
        })
        this.$emit('eventBus', removeData)
        if (!this.isDelete) {
          this.isDelete = true
          this.mobileEditParamCount = this.paramsPanelList.length + this.mobileEditParamCount
        } // 确认是否删除flag
        this.paramGangedClass.removeGanged(data.data.id, PARAM_GANGED_TYPE.TABS, this.activeTabId)
      }
    },
    showPrintItem () {
      this.show = !this.show
      if (this.show) {
        // const tablePreview = document.getElementById('data-screen-table')
        const tablePreview = this.getBoardUnderIdElement('#data-screen-table')
        tablePreview.addEventListener('click', this.clickHandler, false)
      }
    },
    clickHandler () {
      const languageSwitch = this.$refs['printItem']
      const e = event || window.event
      const eventPath = e.path
      const clickInner = eventPath.includes(languageSwitch)
      if (!clickInner) {
        this.show = false
        this.removeEvent()
      }
    },
    removeEvent () {
      console.log('移除Remove Event')
      // const tablePreview = document.getElementById('data-screen-table')
      const tablePreview = this.getBoardUnderIdElement('#data-screen-table')
      tablePreview && tablePreview.removeEventListener('click', this.clickHandler, false)
    },
    // 切换标签
    onPanelChange (panel) {
      this.changeActivePanel(panel.data.id, true)
      // this.commonData.setboardStatus()
      // this.changeActivePanel(panel.name)
      this.levelHeightList = []
      const eventData = new EventData({
        ...this.defaultEventData,
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'initParamsType',
        data: panel.data
      })
      this.$emit('eventBus', eventData)
    },
    setActiveId () {
      let paramsActive = this.paramsPanelList.find(item => item.active)
      this.activeTabId = paramsActive.id
      const obj = this.paramsPanelList.find(tab => tab.id === paramsActive.id)
      this.restLayout(obj)
    },
    metricCheck(tabId) {
      let oldTabData = this.paramsPanelList.find(tab => tab.active)
      let newTabData = this.paramsPanelList.find(tab => tab.id === tabId)
      let oldMetricList = oldTabData?.content.filter(item => item.type === TYPE_PARAM_ELEMENT.METRIC)
      let newMetricList = newTabData?.content.filter(item => item.type === TYPE_PARAM_ELEMENT.METRIC)
      // 当老tab有metric 切换过去的tab无metric时 需要调用metricSwitch通知所有图形表格
      // if (oldMetricList.length && !newMetricList.length) {
      //   const eventData = new EventData({
      //     ...this.defaultEventData,
      //     target: ['paramsPanel'],
      //     targetFn: 'metricSwitch',
      //     data: {
      //       'elementList': [],
      //       'paramsList': []
      //     }
      //   })
      //   this.$emit('eventBus', eventData)
      // }
    },
    changeActivePanel (id, notFirst) {
      const eventData = new EventData({
        target: ['paramsPanel'],
        targetFn: 'initLocationData',
      })
      this.activeTabId !== id && this.$emit('eventBus', eventData)
      this.metricCheck(id)
      this.activeTabId = id
      this.paramsPanelList.forEach((item) => {
        this.$set(item, 'active', false)
      })
      const obj = this.paramsPanelList.find(tab => tab.id === this.activeTabId) || this.paramsPanelList[0]
      this.$set(obj, 'active', true)

      const activeTagId = this.boardInfo.tagModeStack?.midware?.[obj.id]?.id
      activeTagId && this.dynamicTags.forEach((item) => {
        this.$set(item, 'active', item.id === activeTagId)
      })

      if ((!this.isMobile || this.utils.isPcMobileEdit) && this.openBoardTab && notFirst) {
        const eventData = new EventData({
          ...this.defaultEventData,
          target: ['displayPanel'],
          targetFn: 'addTag',
          data: id
        })
        // 给标题设置已经设置过的值

        if (Object.keys(this.boardInfo.tagModeStack).length > 0 && !this.themeData.themeFullScreen) {
          let subNameData, loc, uni
          loc = this.$_deepClone(this.boardInfo.tagModeStack.settingTitle[id].Tstyle?.[this.themeData.themeType])
          subNameData = new EventData({
            source: this.$options.name,
            target: ['titleBar'],
            targetFn: 'updateTitle',
            data: {
              title: this.boardInfo.tagModeStack.settingTitle[id].title,
              titleStyle: loc
            },
          })
          uni = new EventData({
            ...this.defaultEventData,
            target: ['displayPanel'],
            targetFn: 'combineStyle',
            data: {
              titleStyle: loc
            }
          })
          subNameData && this.$emit('eventBus', subNameData)
          uni && this.$emit('eventBus', uni)
          if (this.commonData.isPreview) { // 如果是预览的情况 nameSub需要被currentLang替换
            const { currentLang, title } = this.boardInfo.tagModeStack.settingTitle[id]
            const nameSub = currentLang || title
            if (typeof nameSub === 'string') {
              this.$set(this.boardInfo, 'nameSub', nameSub)
            }
          }
          this.$emit('eventBus', eventData)
          setTimeout(() => {
            const unbindRecord = this.boardInfo.tagModeStack.unbindRecord
            if (!unbindRecord.hasRebuild) {
              if (this.elementComp) {
                this.elementComp.forEach(el => {
                  el.setElBind && el.setElBind(unbindRecord[obj.id].ids)
                })
                if (unbindRecord[obj.id]) {
                  unbindRecord[obj.id].hasRebuild = true
                }
              }
            }
          }, 1000)
        }
      }
      this.$parent.onWindowResize()
      this.restLayout(obj)
      this.addTrans()
      this.changeActivePanelPreview()
    },
    // 切换到一个有效的标签上
    changeActivePanelPreview () {
      // this.tabId
      // this.activeTabId
      var list = []
      var check = true
      this.paramsPanelList.forEach(item => {
        if (!this.tabId.length || this.tabId.indexOf(item.labelName) !== -1 || (item.labelName === '' || item.labelName === undefined)) {
          list.push(item)
          if (item.id === this.activeTabId) {
            check = false
          }
        }
      })
      if (check && list.length > 0) {
        this.changeActivePanel(list[0].id)
      }
    },
    reBuildObj (obj, el) {
      var cloneObj = JSON.parse(JSON.stringify(obj.content))
      var newarr = cloneObj.sort(function (a, b) {
        var c, d
        if (a.content.layout) {
          c = a.content.layout
        } else {
          let type = a.type
          let elName = a.elName
          let newParam = new BoardParamElement({ type, elName })
          c = newParam.content.layout
        }
        if (b.content.layout) {
          d = b.content.layout
        } else {
          let type = b.type
          let elName = b.elName
          let newParam = new BoardParamElement({ type, elName })
          d = newParam.content.layout
        }
        if (!el.isMobile) {
          if (c.y === d.y) {
            return c.x - d.x
          } else {
            return d.y - c.y
          }
        }
      })
      obj.content = newarr
      return obj
    },
    // 日历组件多语言兼容
    recomparetypeAlise(data) {
      const PRIOR_PERIOD = 'Prior Period'

      const aliasList = data.content?.comparetypeAlise || []

      if (aliasList.every(({ value }) => value !== PRIOR_PERIOD)) {
        aliasList.push({
          id: this.$_generateUUID(),
          name: PRIOR_PERIOD,
          value: PRIOR_PERIOD,
        })
      }

      this.$set(data.content, 'comparetypeAlise', aliasList)
    },
    initcalendar() {
      this.paramsPanelList.forEach(tab => {
        tab.content.forEach(params => {
          if (params.type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR || params.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR) {
            this.recomparetypeAlise(params)
          }
        })
      })
    },
    // 兼容老看板
    restLayout(obj) {
      let arr = []
      this.initcalendar()
      let newobj = this.reBuildObj(obj, this)
      newobj.content.forEach((item, index) => {
        // 重置layout i
        const quickArr = [TYPE_PARAM_ELEMENT.LOCATION_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION, TYPE_PARAM_ELEMENT.CALENDAR_QUICK]
        if (quickArr.includes(item.type)) {
          item.content.layout = []
        } else {
          let type = item.type
          let elName = item.elName
          let newParam = new BoardParamElement({ type, elName })
          if (!item.content.layout) {
            // newParam.content.layout = this.setLayout(newParam.content.layout, 0)
            // console.log(this.reBuildArr(arr))
            item.content.layout = this.setLayout(this.reBuildArr(arr), newParam.content.layout, 0)
          }
          // 需求4847 【畅联】参数组件优化 (PC) 【V3.14已评审】，添加组件标题样式
          if (!item.elNameStyle) {
            item.elNameStyle = this.getDefaultTitleStyle(type, this.themeData.themeType)
          }
          // 没有深邃蓝，加上
          if (item.elNameStyle.color && !item.elNameStyle.color[THEME_TYPE.deepBlue]) {
            item.elNameStyle.color[THEME_TYPE.deepBlue] = defaultStyle[THEME_TYPE.deepBlue].color
          }
          if (!item.isHideElement) {
            this.$set(item, 'isHideElement', false)
          }
          // console.log(this.setLayout(newParam.content.layout, 0))
          item.content.layout.i = item.id
          if (item.type === TYPE_PARAM_ELEMENT.PEOPLE_LAYER || item.type === TYPE_PARAM_ELEMENT.CONTAINER_SELECT) {
            if (item.content.layout.h === 15 || item.content.layout.h === 23 || (item.content.layout.h && item.type === TYPE_PARAM_ELEMENT.CONTAINER_SELECT)) {
            } else {
              item.content.layout.h = newParam.content.layout.h
              item.content.layout.defaultHeight = newParam.content.layout.defaultHeight
            }
          } else {
            item.content.layout.h = newParam.content.layout.h
            item.content.layout.defaultHeight = newParam.content.layout.defaultHeight
          }
          // obj.layout.push(item.content.layout)
          // console.log(newParam.content.layout)
          if (this.isMobile) {
            item.content.layout.w = 7
            item.content.layout.h = 24
          }
          arr.push(item.content.layout)
        }
      })
      this.$set(obj, 'layout', arr)
      this.$nextTick(() => {
        this.setLayoutRule()
      })
    },
    addTrans() {
      this.$nextTick(() => {
        this.$refs.newtab.addFixTab()
      })
    },
    addParamPanel () {
      clearTimeout(this.timer)
      if (this.paramsPanelList.length >= this.maxCountLength) {
        return this.$message.info(this.$t('sdp.message.tabLimit'))
      }
      const newTab = this.isMobile ? this.generateMobileParamPanel() : this.generateParamPanel()
      this.paramsPanelList.push(newTab)
      if (!this.isMobile && this.openBoardTab) {
        const lastTag = this.dynamicTags[0] || {}
        let tag = generateOriginEllist(this.paramsPanelList.length, this.isScreen ? ['canvasBg', { key: 'canvasSize', lastValue: lastTag.canvasSize }] : ['elementBoardBg'])
        let obj = {
          [newTab.id]: tag
        }
        let Tstyle = {
          color: '',
          'font-family': 'NotoSansHans-Regular',
          'font-size': '',
          'font-style': '',
          'font-weight': '',
          'text-decoration': ''
        }
        let titleStyleObj = {}
        Object.keys(TITLE_STYLE).forEach(i => {
          if (!titleStyleObj[i]) {
            titleStyleObj[i] = {}
          }
          Object.assign(titleStyleObj[i], Tstyle, { color: TITLE_STYLE[i].color })
        })
        this.$set(this.boardInfo.tagModeStack.settingTitle, newTab.id, {
          tabName: newTab.label,
          id: newTab.id,
          title: '',
          Tstyle: titleStyleObj
        })
        const eventData = new EventData({
          ...this.defaultEventData,
          target: ['displayPanel'],
          targetFn: 'addDynamicTag',
          data: tag
        })
        this.$emit('eventBus', eventData)
        Object.values(this.boardInfo.tagModeStack.midware).forEach(v => {
          v.active = false
          v.isHide = true
        })
        Object.assign(this.boardInfo.tagModeStack.midware, obj)
      }
      this.changeActivePanel(newTab.id, true)
      this.timer = setTimeout(() => { // 子组件的watch触发的时机在nextTick之后 不调到macroTask会有问题
        this.addTrans()
      }, 0)

    },
    // 生成移动端标签
    generateMobileParamPanel() {
      // 现有逻辑 pc和移动端的逻辑是用一套 改存量风险太大
      let obj = {
        label: '',
        id: this.$_generateUUID(),
        active: true,
        content: [],
        layout: [],
      }
      if (this.boardInfo.id) { // 该情况是已经保存过的移动端看板

        if (this.isDelete) { // 已经删除过 新增走计数器
          this.mobileEditParamCount++
          while (true) {
            if (!this.paramsPanelList.some(v => v.label.trim() === `Tab ${this.mobileEditParamCount}`)) {
              break
            }
            this.mobileEditParamCount++
          }
          obj.label = `Tab ${this.mobileEditParamCount}`
        } else { // 没有删除 新增走旧逻辑
          let accountList = this.paramsPanelList.filter(v => v.label.trim().indexOf(`Tab `) === 0)
          if (accountList.length > 0) { // 存在Tab_空格的情况 需要找出剩下的是否是数字并且赋值
          // 因为可能存在删除的情况 所以数据并不连贯 改删除存量逻辑风险太大 只能每次重新构建最小值
            let arr = []
            accountList.map(v => {
              let trimLabel = v.label.trim()
              let numLabel = trimLabel.substring(`Tab `.length, trimLabel.length)
              if (!Number.isNaN(+numLabel)) { // 还要判断是不是特意取Tab 数字这种类型
                arr.push(+numLabel)
              }
            })
            if (arr.length > 0) { // 如果有类似Tab 1的种类
              let min = this.paramsPanelList.length + 1
              while (true) { // 如果重合了的话就自增
                if (!arr.some(v => v === min)) {
                  break
                }
                min++
              }
              obj.label = `Tab ${min}`
            } else { // 不然直接在长度上增加
              obj.label = `Tab ${this.paramsPanelList.length + 1}`
            }

          } else { // 直接新增1
            obj.label = `Tab ${this.paramsPanelList.length + 1}`
          }
          this.mobileEditParamCount++ // 这里其实可以用mobileParamCount 但是为了更好区分就用新变量
        }
      } else { // 这是首次生成的
        this.mobileParamCount++ // 因为点击的时候默认已经生成一个所以直接累加就行
        obj.label = `Tab ${(this.mobileParamCount)}`
      }
      return obj
    },
    // tab实例
    generateParamPanel () {
      return setCanvasBgDefaultData({
        label: `Tab ${(this.paramsPanelList.length + 1)}`,
        id: this.$_generateUUID(),
        active: true,
        content: [],
        layout: [],
        status: TYPE_SUPERSETTING.INIT
      }, ['parameterAreaBg'])
    },
    // tab 重命名
    renameTabTitle (tab) {
      if (!this.commonData.isPreview) {
        this.editingTab.tab = tab.data
        this.editingTab.tempLabel = tab.data.label
        this.renameDialogShow = true
      }
    },
    onRename () {
      var check = false
      this.paramsPanelList.forEach(item => {
        if (item.label === this.editingTab.tempLabel) {
          if (item.id !== this.editingTab.tab.id) {
            check = true
          }
        }
      })
      if (check) {
        return this.$message.info(this.$t('sdp.message.tabRename'))
      }
      if (this.editingTab.tempLabel.length > 30) {
        return this.$message.info(this.$t('sdp.message.tabNameLength'))
      }
      if (this.editingTab.tempLabel.length === 0) {
        return this.$message.info(this.$t('sdp.message.tabNull'))
      }
      this.editingTab.tab.label = this.editingTab.tempLabel
      this.renameDialogShow = false
      this.reCalcOffset()
    },
    // 复制组件tab
    handleCopyParamTab({ data }) {
      if (!data) return
      if (this.paramsPanelList.length >= this.maxCountLength) {
        return this.$message.info(this.$t('sdp.message.tabLimit'))
      }
      if (!this.isMobile && this.openBoardTab) {
        const tabId = this.boardInfo.tagModeStack.midware[data.id].id
        const tag = this.dynamicTags.find(e => e.id === tabId) || {}
        const exMax = this.checkCopyElementMax(tag)
        if (exMax) return
      }

      console.log('handleCopyParamTab', data)
      // 复制tab
      // 标记tab内id及组件id
      const newTab = this.handleCopyParamTabObj(data)
      // 创建tab页
      const oldTabIndex = this.paramsPanelList.findIndex(e => e.id === data.id)
      this.paramsPanelList.splice(oldTabIndex + 1, 0, newTab)
      // this.paramsPanelList.push(newTab)
      // 如果是看板标签页模式
      if (!this.isMobile && this.openBoardTab) {
        const tabId = this.boardInfo.tagModeStack.midware[data.id].id
        const tag = this.dynamicTags.find(e => e.id === tabId) || {}

        const newDynamicTab = this.addCopyNowTabs(tag)
        let obj = {
          [newTab.id]: newDynamicTab
        }

        const orginTabTitle = this.boardInfo.tagModeStack.settingTitle[data.id]
        this.$set(this.boardInfo.tagModeStack.settingTitle, newTab.id, this.$_deepClone(orginTabTitle))
        Object.values(this.boardInfo.tagModeStack.midware).forEach(v => {
          v.active = false
          v.isHide = true
        })
        Object.assign(this.boardInfo.tagModeStack.midware, obj)
        Object.assign(this.boardInfo.tagModeStack.unbindRecord, { [newTab.id]: { hasRebuild: true, ids: [] } })

        const tagIdList = Object.values(this.boardInfo.tagModeStack.midware).map(e => e.id)
        const dynamicTagsList = this.dynamicTags.filter(e => !tagIdList.includes(e.id))
        dynamicTagsList.forEach(e => {
          const index = this.dynamicTags.findIndex(tag => e.id === tag.id)
          this.dynamicTags.splice(index, 1)
        })
        this.updateCopyParamTabBindElementId(newTab)
        this.handleCopyElementResetPosition(newDynamicTab)
      }
      // 复制多语言数据
      this.handleCopyElementLanguageByParamTab(newTab)
      this.changeActivePanel(newTab.id, true)
      // 修改参数组件绑定
    },
    updateCopyParamTabBindElementId(tab) {
      const paramTab = this.paramsPanelList.find(e => e.id === tab.id)
      if (!paramTab) return

      const DIC = {}
      this.elList.forEach(el => {
        if (el.content.orginId) {
          DIC[el.content.orginId] = el.id
        }
      })

      paramTab.content.forEach(param => {
        let bindElements = getSetBindElementsVar(param.content, param.type) || []
        const bindList = bindElements.map(id => {
          if (DIC[id]) {
            return DIC[id]
          } else {
            return null
          }
        }).filter(e => e)
        getSetBindElementsVar(param.content, param.type, bindList)
      })
    },
    // 复制多语言数据
    handleCopyElementLanguageByParamTab(tag) {
      let languageList = this.displayRef.newBoardContent.metaDashboardElementLanList || []
      const tab = this.paramsPanelList.find(e => e.id === tag.id)
      const paramList = tab.content || []
      const list = [tab, ...paramList]

      list.forEach(param => {
        const orginId = param.orginId
        if (!orginId) return

        const orginLanguageList = languageList.filter(e => e.key && e.key.includes(orginId))
        const newLanguageList = orginLanguageList.map(item => {
          const cloneItem = this.$_deepClone(item)
          const reg = new RegExp(orginId, 'g')
          cloneItem.key && (cloneItem.key = cloneItem.key.replace(reg, param.id))
          cloneItem.name && (cloneItem.name = cloneItem.name.replace(reg, param.id))
          return cloneItem
        })
        const newMetaDashboardElementLanList = [...languageList.filter(e => e.key && !e.key.includes(param.id)), ...newLanguageList]
        languageList = newMetaDashboardElementLanList
      })

      this.displayRef.newBoardContent.metaDashboardElementLanList = languageList
    },
    // 复制多语言数据
    handleCopyElementLanguageByTab(tag) {
      let languageList = this.displayRef.newBoardContent.metaDashboardElementLanList
      const tab = this.dynamicTags.find(e => e.id === tag.id)
      const newElList = this.elList.filter(e => tab.content.includes(e.id))

      newElList.forEach(el => {
        const orginId = el.content.orginId
        if (!orginId) return

        const orginLanguageList = languageList.filter(e => e.key && e.key.includes(orginId))
        const newLanguageList = orginLanguageList.map(item => {
          const cloneItem = this.$_deepClone(item)
          const reg = new RegExp(orginId, 'g')
          cloneItem.key && (cloneItem.key = cloneItem.key.replace(reg, el.id))
          cloneItem.name && (cloneItem.name = cloneItem.name.replace(reg, el.id))
          return cloneItem
        })
        const newMetaDashboardElementLanList = [...languageList.filter(e => e.key && !e.key.includes(el.id)), ...newLanguageList]
        languageList = newMetaDashboardElementLanList
      })

      this.displayRef.newBoardContent.metaDashboardElementLanList = languageList
    },
    // 重置定位
    handleCopyElementResetPosition(tag) {
      const tab = this.dynamicTags.find(e => e.id === tag.id)
      const newElList = this.elList.filter(e => tab.content.includes(e.id))

      newElList.forEach(el => {
        const orginId = el.content.orginId
        if (!orginId) return

        const target = this.elList.find(e => e.id === orginId)
        const layout = this.$_deepClone(target.layout)
        layout.i = el.id
        Object.assign(el.layout, layout)
        Object.assign(el, { zIndex: target.zIndex })
      })
    },
    // 重置交互
    handleCopyElementResetiInteractionOptions(tag) {
      const tab = this.dynamicTags.find(e => e.id === tag.id)
      const newElList = this.elList.filter(e => tab.content.includes(e.id))

      const DIC = {}
      newElList.forEach(el => {
        const orginId = el.content.orginId
        if (!orginId) return
        DIC[orginId] = el.id
      })

      newElList.forEach(el => {
        if (el.content.interactionOptions && el.content.interactionOptions.length) {
          el.content.interactionOptions.forEach(inter => {
            inter.id = el.id
            inter.associElements.forEach(associElements => {
              DIC[associElements.id] && (associElements.id = DIC[associElements.id])
            })
          })
        }
      })
    },
    // 重置数据报告
    handleCopyElementCloneTabData(tag, newTag) {
      if (!this.utils.isDataReport) return
      const tab = this.dynamicTags.find(e => e.id === newTag.id)
      const newElList = this.elList.filter(e => tab.content.includes(e.id))

      const DIC = {}
      newElList.forEach(el => {
        const orginId = el.content.orginId
        if (!orginId) return
        DIC[orginId] = el.id
      })

      this.dataReport?.cloneTabData?.(tag.id, newTag.id, DIC)
    },
    // 校验是否超出数量
    checkCopyElementMax(tag) {
      // 标记当前tab页
      const activeTab = tag || this.dynamicTags.find(e => e.active)
      if (!activeTab) return false
      // 标记当前tab页元素
      const elements = this.elList.filter(e => activeTab.content.includes(e.id))
      if (elements.length + this.elList.length > MAX_COUNT_ELEMENTS_IN_BOARD) {
        this.$message.info(this.$t('sdp.message.copyMoreElementsInBoard').replace('*', elements.length + this.elList.length))
        return true
      }
      return false
    },
    handleCopyParamTabObj(tab) {
      const cloneTab = this.$_deepClone(tab)

      const DIC = {}
      const copyList = this.paramsPanelList.filter(e => e.orginId === tab.id)
      const copyTimes = copyList.length + 1
      const defaultTab = {
        id: this.$_generateUUID(),
        active: true,
        orginId: tab.id,
        label: `${cloneTab.label}(${copyTimes})`,
      }
      tab.status && (cloneTab.status = TYPE_SUPERSETTING.INIT)
      cloneTab.content.forEach(e => {
        e.orginId = e.id
        e.id = this.$_generateUUID()
        DIC[e.orginId] = e.id
      })

      const newTab = Object.assign({}, cloneTab, defaultTab)
      // 更新组件关联
      // boardInfo.paramGangedList
      if (this.boardInfo.paramGangedList) {
        if (this.boardInfo.paramGangedList[tab.id]) {
          const paramGanged = this.$_deepClone(this.boardInfo.paramGangedList[tab.id])
          // 处理id
          const fun = (arr) => {
            arr.forEach(item => {
              DIC[item.id] && (item.id = DIC[item.id])
              DIC[item.parentId] && (item.parentId = DIC[item.parentId])
              item.children && fun(item.children)
            })
          }
          fun(paramGanged)
          this.$set(this.boardInfo.paramGangedList, defaultTab.id, paramGanged)
        }
      }

      return newTab
    },
    addCopyNowTabs(tag = null) {
      // 标记当前tab页
      const activeTab = tag || this.dynamicTags.find(e => e.active)
      if (!activeTab) return
      // 标记当前tab页元素
      const elements = this.elList.filter(e => activeTab.content.includes(e.id))
      elements.forEach(e => e.content.orginId = e.id)
      // 创建新tab页
      const oldTabIndex = this.dynamicTags.findIndex(e => tag ? e.id === tag.id : e.active)
      const newTab = this.handleCopyTabObj(activeTab)
      this.handleCreateTab(newTab, oldTabIndex)
      // 复制标记元素到新tab页
      this.displayRef.$refs.supernatant.copyElementContent(elements.filter(e => !e._containerId), undefined, newTab.id, undefined, { copyType: COPY_TO_OTHER_PAGE, source: 'tab' })
      // 构建前tab页元素与当前tab页元素关系
      // 复制多语言数据
      this.handleCopyElementLanguageByTab(newTab)
      this.handleCopyElementResetiInteractionOptions(newTab)
      this.handleCopyElementCloneTabData(tag || activeTab, newTab)
      this.handleCopyElementResetPosition(newTab)

      return newTab
    },
    // 复制tab对象 重置content和id等数据
    handleCopyTabObj(tab) {
      const cloneTab = this.$_deepClone(tab)

      const copyList = this.dynamicTags.filter(e => e.orginId === tab.id)
      const copyTimes = copyList.length + 1
      const defaultTab = {
        id: this.$_generateUUID(),
        content: [],
        active: true,
        orginId: tab.id,
        label: `${cloneTab.label}(${copyTimes})`
      }
      // label: `${cloneTab.label.replace(/\([^\)]*\)/g, '')}(${copyTimes})`

      const newTab = Object.assign({}, cloneTab, defaultTab)
      return newTab
    },
    // 使用tab数据创建tab页
    handleCreateTab(tab, oldTabIndex) {
      const eventData = new EventData({
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'addDynamicTag',
        data: tab,
        follow: true,
        oldTabIndex,
      })
      this.$emit('eventBus', eventData)
    },
    reCalcOffset() {
      this.$nextTick(() => {
        this.$refs.newtab.reCalcOffset()
      })
    },
    getParamsPanelListActive() {
      return this.paramsPanelList.find(tab => tab.id === this.activeTabId)
    },
    getParamLength(type) {
      const tempArr = []
      let nowTab = this.getParamsPanelListActive()
      nowTab.content.forEach(item => {
        if (item.type === type) {
          tempArr.push(item.type)
        }
      })
      return tempArr
    },
    // 参数组件 编辑完成
    onCommitChanges({ data }) {
      const nowTab = this.getParamsPanelListActive()
      if (Array.isArray(nowTab.content)) {
        nowTab.content.forEach((item, i) => {
          // 数据替换
          if (item.id === data.id) {
            const { layout } = nowTab
            if (item.content.layout) {
              data.content.layout = layout.find(el => el.i === item.id) || item.content.layout
            }
            this.$set(nowTab.content, [i], data)
            this.$nextTick(() => {
              const elementCompItem = this.elementComp.find(el => el?.paramElement?.id === data.id)
              if (elementCompItem && elementCompItem?.restoreData) {
                elementCompItem.restoreData()
              }
            })
          }
        })
      }
      // this.cancelShow()
    },
    // 关闭编辑页面
    cancelShow() {
      this.settingDialogShow = false
      this.paramSettingVisible = false
    },
    // 重置组件状态(日历)
    restParamsFinish ({ data = [] }) {
      this.$nextTick(() => {
        const elementComp = this.elementComp.filter(el => data.includes(el?.paramElement?.id))
        elementComp.forEach(el => {
          el.restUserDate && el.restUserDate()
        })
      })
    },
    // 删除组件
    removeParams (data) {
      let patamsId = data.data.id
      let nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      nowTab.content.forEach(item => {
        if (item.id === patamsId) {
          this.paramGangedClass.removeGanged(patamsId, PARAM_GANGED_TYPE.PARAMS, this.activeTabId, data.data.type)
          nowTab.content.splice(nowTab.content.indexOf(item), 1)
        }
      })
      // 删除对应的layout
      nowTab.layout.forEach(item => {
        if (item.i === data.data.content.layout.i) {
          nowTab.layout.splice(nowTab.layout.indexOf(item), 1)
        }
      })
      this.setLayoutRule()
      this.followUpWork(data.data)
    },
    // 删除后续操作
    followUpWork({ type, content }) {
      switch (type) {
        case TYPE_PARAM_ELEMENT.LOCATION_QUICK: {
          this.activeStatus.content.some(el => {
            const bool = el.type === TYPE_PARAM_ELEMENT.LOCATION_NEW
            if (bool) {
              this.$set(el.content.labelPickerConfig, 'isLabelPicker', false)
            }
            return bool
          })
          break
        }
        case TYPE_PARAM_ELEMENT.LOCATION_NEW: {
          const index = this.activeStatus.content.findIndex(el => el.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK)
          if (index !== -1) {
            this.activeStatus.content.splice(index, 1)
          }
          const eventData = new EventData({
            data: {},
            target: ['displayPanel', 'displayPanelMobile'],
            targetFn: 'setLanguaeCode'
          })
          this.$emit('eventBus', eventData)
          break
        }
        case TYPE_PARAM_ELEMENT.CALENDAR_QUICK: {
          this.activeStatus.content.some(el => {
            const bool = el.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR
            if (bool) {
              this.$set(el.content.userSetting, 'isQuickLabel', false)
            }
            return bool
          })
          break
        }
        case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR: {
          const index = this.activeStatus.content.findIndex(el => el.type === TYPE_PARAM_ELEMENT.CALENDAR_QUICK)
          if (index !== -1 && content?.userSetting?.isQuickLabel) {
            this.activeStatus.content.splice(index, 1)
          }
          const eventData = new EventData({
            data: {},
            target: ['displayPanel', 'displayPanelMobile'],
            targetFn: 'setLanguaeCode'
          })
          this.$emit('eventBus', eventData)
          break
        }
      }
    },
    // 编辑组件
    async editParams (data) {
      if (data.data.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
        const { viewLevelList } = this.commonData.getViewLevelData()
        if (!viewLevelList.some(({ levelType }) => levelType === '1')) {
          return void 'location不可编辑'
        }
      }
      this.boardShareData.paramsPanelList = this.paramsPanelList
      this.editparam = Vue.prototype.$_deepClone(data.data)
      console.log('location不可编辑', this.boardShareData.paramsPanelList, this.editparam)

      this.paramSettingVisible = true

      // const isSBI = isDev ? true : this.isSbiType
      // if (isSBI) {
      //   this.paramSettingVisible = true
      // } else {
      //   this.settingDialogShow = true
      // }
    },
    setDefaultLanguageConfig(params, modelData) {
      if (params.type === 'selectOrdinary' && params.isModel) {
        // dropbox下拉框模板
        const languageConfig = params.content.languageConfig
        if (Array.isArray(languageConfig) && languageConfig?.length) {
          const paramNameLanguage = languageConfig.find(l => l.code === modelData.id && l.name === modelData.name)
          if (paramNameLanguage?.languageConfig?.length) {
            paramNameLanguage.languageConfig.forEach(c => {
              const dataLanguageItem = generateLanguageItem(c.languageCode, { ...c, id: params.id })
              this.newBoardContent.metaDashboardElementLanList.push(dataLanguageItem)
            })
          }
        }
      }
    },
    // 添加数据集
    async addDatasetList(content) {
      // 维度数据集id
      let datasetId = content.content.dataSets[0].dimensionDataSetId
      if (!this.datasetList.find(item => item.id === datasetId)) {
        const eventData = new EventData({
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'getDataSet',
          data: datasetId
        })
        this.$emit('eventBus', eventData)
      }
    },
    addParamModel(data) {
      let content = JSON.parse(data.content)
      let type = content.type
      let elNameStyle = data.elNameStyle && JSON.parse(data.elNameStyle)
      let isHideElement = data?.hideElementFlag || false // isHideElement
      let nowTab = this.getParamsPanelListActive()

      if (this.setHasSameParam(type)) return this.$t('sdp.views.addMess')

      let isModel = true
      let newParam = new BoardParamElement({ type, elName: this.getElName(type, nowTab, isModel) })
      this.$set(newParam, 'elNameStyle', elNameStyle)
      this.$set(newParam, 'isHideElement', isHideElement)
      this.$set(newParam, 'elName', content.name)
      Object.keys(newParam.content).forEach(item => {
        if (content.content[item] || content.content[item] === '' || content.content[item] === false || item === 'defaultSelect') {
          newParam.content[item] = content.content[item]
        }
      })
      newParam.content.selectOrdinaryArr = content.content.defaultSelect
      // 引用维度数据集
      this.addDatasetList(content)
      // 标记为从模板引用
      newParam.isModel = isModel
      // 模版id
      newParam.modelId = data.id

      this.addParamsSetLayout(newParam, nowTab, type)

      this.setDefaultLanguageConfig(newParam, data)
    },
    addParamCopy(data) {
      let type = data.type
      let elNameStyle = data.elNameStyle
      let isHideElement = data?.hideElementFlag || false // isHideElement
      let nowTab = this.getParamsPanelListActive()
      if (this.setHasSameParamCopy(type)) return this.$t('sdp.views.addMess')
      if (!this.calendarChecked(type)) return false
      if (!this.hierarchyDropdownBoxChecked(type)) return false
      let newParam = new BoardParamElement({ type, elName: this.getElName(type, nowTab) })
      this.$set(newParam, 'elNameStyle', elNameStyle)
      this.$set(newParam, 'isHideElement', isHideElement)
      this.$set(newParam, 'elName', data.elName)
      // nowTab.content.push(data)
      this.addParamsSetLayout(newParam, nowTab, type)
      this.restLayout(nowTab)
      this.setLayoutRule()
    },
    setHasSameParamCopy(type) {
      let nowTab = this.getParamsPanelListActive()
      let hasSameParam = nowTab.content.find(param => param.type === type)

      if (!hasSameParam) return false
      // 处理需要添加多个的参数组件
      switch (type) {
        case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR:
        case TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR:
          if (this.getParamLength(type).length < 3) hasSameParam = false
          break
        case TYPE_PARAM_ELEMENT.SELECT_TYPE:
          if (this.getParamLength(type).length === 1) hasSameParam = false
          break
        case TYPE_PARAM_ELEMENT.SELECT_ORDINARY:
        case TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX:
          if (this.getParamLength(type).length < 10) hasSameParam = false
          break
        case TYPE_PARAM_ELEMENT.TEXTBOX:
          if (this.getParamLength(type).length < 7) hasSameParam = false
          break
        // case TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX:
        // case TYPE_PARAM_ELEMENT.GROUP:
        //   hasSameParam = false
        //   break
      }
      // 因为有情况会同时出现俩种弹窗，所以加上点延时，防止弹窗重合
      if (hasSameParam) {
        if (type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR || type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR) {
          setTimeout(()=>{
            this.$message.info(this.$t('sdp.message.addcalendar'))
          },0)
        } else if (type === TYPE_PARAM_ELEMENT.SELECT_ORDINARY) {
          setTimeout(()=>{
            this.$message.info(this.$t('sdp.message.adddropdown'))
          },100)
        } else if (type === TYPE_PARAM_ELEMENT.TEXTBOX) {
          setTimeout(()=>{
            this.$message.info(this.$t('sdp.message.addtextbox'))
          },200)
        }
      }
      return hasSameParam
    },
    setHasSameParam(type) {
      let nowTab = this.getParamsPanelListActive()
      let hasSameParam = nowTab.content.find(param => param.type === type)

      if (!hasSameParam) return false
      // 处理需要添加多个的参数组件
      switch (type) {
        case TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR:
        case TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR:
          if (this.getParamLength(type).length < 3) hasSameParam = false
          break
        case TYPE_PARAM_ELEMENT.SELECT_TYPE:
          if (this.getParamLength(type).length === 1) hasSameParam = false
          break
        case TYPE_PARAM_ELEMENT.SELECT_ORDINARY:
        case TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX:
          if (this.getParamLength(type).length < 10) hasSameParam = false
          break
        case TYPE_PARAM_ELEMENT.TEXTBOX:
          if (this.getParamLength(type).length < 7) hasSameParam = false
          break
        // case TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX:
        // case TYPE_PARAM_ELEMENT.GROUP:
        //   hasSameParam = false
        //   break
      }
      if (hasSameParam) {
        if (type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR || type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR) {
          this.$message.info(this.$t('sdp.message.addcalendar'))
        } else if (type === TYPE_PARAM_ELEMENT.SELECT_ORDINARY) {
          this.$message.info(this.$t('sdp.message.adddropdown'))
        } else if (type === TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX) {
          this.$message.info(this.$t('sdp.message.addHierarchyDropdown'))
        } else if (type === TYPE_PARAM_ELEMENT.TEXTBOX) {
          this.$message.info(this.$t('sdp.message.addtextbox'))
        }
      }
      return hasSameParam
    },
    getElName(type, nowTab, isModel = false) {
      const elementInfo = elementTypeList[type]
      let elName = elementInfo?.name || this.$t('sdp.views.nameMess')
      if (!isModel && elementInfo.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
        elName = TYPE_PARAM_ELEMENT.LOCATION
      }
      return this.repetitionName(nowTab.content, { type, elName })
    },
    addParamsSetLayout(newParam, nowTab, type, getAddData) {
      const { content, id } = newParam
      this.reSetLayout()
      let layoutList = nowTab.layout || []
      if (this.isMobile) {
        content.layout.w = 7
        content.layout.h = 24
        content.layout.y = nowTab.content.length * 24
      } else {
        content.layout = this.setLayout(layoutList, content.layout, 0)
      }
      typeof getAddData === 'function' && getAddData(newParam)
      nowTab.content.push(newParam)
      content.layout.i = id
      const quickArr = [TYPE_PARAM_ELEMENT.LOCATION_QUICK, TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION, TYPE_PARAM_ELEMENT.CALENDAR_QUICK]
      quickArr.includes(type) || nowTab.layout.push(content.layout)
      this.restLayout(nowTab)
      this.setLayoutRule()
    },
    // 添加组件 s
    async addParams(item, getAddData) {
      let type = item.data
      let nowTab = this.getParamsPanelListActive()

      if (this.setHasSameParam(type)) {
        this.addRepeatMessage(type)
        return this.$t('sdp.views.addMess')
      }

      if (!this.calendarChecked(type)) return false

      if (!this.hierarchyDropdownBoxChecked(type)) return false
      // if (this.limitChecked(type)) return false

      let newParam = new BoardParamElement({ type, elName: this.getElName(type, nowTab) })

      item?.options?.callback && item.options.callback(newParam)
      // 赋值 参数组件 id 到 provide
      this.newParamElement.set(newParam.id)
      this.addParamsSetLayout(newParam, nowTab, type, getAddData)
    },
    addRepeatMessage(type) {
      if ([
        TYPE_PARAM_ELEMENT.LOCATION_NEW,
        TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION,
        TYPE_PARAM_ELEMENT.TIME,
        TYPE_PARAM_ELEMENT.GROUP_BY,
        // TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX,
        TYPE_PARAM_ELEMENT.HIERARCHY_FILTER,
        TYPE_PARAM_ELEMENT.GROUP,
        TYPE_PARAM_ELEMENT.METRIC,
        TYPE_PARAM_ELEMENT.BREAKDOWN,
        TYPE_PARAM_ELEMENT.SEARCH,
        TYPE_PARAM_ELEMENT.PEOPLE_LAYER,
        TYPE_PARAM_ELEMENT.TAGNEW,
      ].includes(type)) {
        this.$message.info(this.$t('sdp.views.cannotAddRepeatedly'))
      }
    },
    // 新增规则
    setLayout (nowTab = [], newGrid, i) {
      // var nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId).layout || []
      let layout = nowTab[i]
      // console.log(layout)
      // 通过与不同区域进行对比 判断添加的位置
      if (layout) {
        if (layout.defaultY === newGrid.defaultY) {
          // console.log(1)
          newGrid.x = layout.x + layout.w
          newGrid.y = layout.y
          if (newGrid.x > 7 - newGrid.w) {
            newGrid.x = 0
            nowTab.forEach(item => {
              if (newGrid.y < item.y) {
                item.y += newGrid.h
              }
            })
            newGrid.y += newGrid.h
          }
          return newGrid
        } else if (layout.defaultY < newGrid.defaultY) {
          // console.log(2)
          newGrid.x = 0
          nowTab.forEach(item => {
            if (layout.y < item.y) {
              item.y += newGrid.h
            }
          })
          newGrid.y = layout.y + layout.h
          return newGrid
        } else if (layout.defaultY > newGrid.defaultY) {
          // console.log(3)
          if (nowTab[i + 1]) {
            return this.setLayout(nowTab, newGrid, (i + 1))
          } else {
            // console.log('无')
            newGrid.x = 0
            const num = layout.y
            nowTab.forEach(item => {
              if (item.y >= layout.y) {
                item.y += newGrid.h
              }
            })
            newGrid.y = num
            return newGrid
          }
        }
      } else {
        newGrid.x = 0
        newGrid.y = 0
        return newGrid
      }
    },
    reBuildArr (arr) {
      var newarr = arr.sort(function (a, b) {
        if (a.y === b.y) {
          return b.x - a.x
        } else {
          return b.y - a.y
        }
      })
      return newarr
    },
    // 对layout进行排序
    reSetLayout (check, tabId = this.activeTabId) {
      var nowTab = this.paramsPanelList.find(tab => tab.id === (tabId || this.activeTabId))
      // console.log(nowTab)
      if (nowTab.layout && nowTab.layout.length > 0) {
        nowTab.layout.sort(function (a, b) {
          if (a.y === b.y) {
            if (check) {
              return a.x - b.x
            } else {
              return b.x - a.x
            }
          } else {
            return b.y - a.y
          }
        })
        this.$set(nowTab, 'layout', nowTab.layout)
      }
    },
    // 设置重组layout
    setLayoutRule (tabId = this.activeTabId) {
      this.reSetLayout(true, tabId)
      var nowTab = this.paramsPanelList.find(tab => tab.id === (tabId || this.activeTabId))
      if (nowTab.layout && nowTab.layout.length > 0) {
        var nowTabRule = []
        nowTab.layout.forEach((item) => {
          if (item.y < 0) {
            item.y = 0
          }
          if (nowTabRule[item.y]) {
            nowTabRule[item.y].list.push(item)
            if (nowTabRule[item.y].h < item.h) {
              nowTabRule[item.y].h = item.h <= item.defaultHeight ? item.defaultHeight : item.h
            } else if (item.h > item.defaultHeight && nowTabRule[item.y].list.length === 1) {
              nowTabRule[item.y].h = item.defaultHeight
            }
          } else {
            nowTabRule[item.y] = {
              list: [item],
              h: item.defaultHeight,
              y: item.y
            }
          }
        })
        // console.log(nowTabRule)
        // 同级取最高高度
        Object.keys(nowTabRule).forEach(a => {
          // console.log(nowTabRule[a])
          nowTabRule[a].list.forEach(b => {
            if (!this.isMobile) {
              b.h = nowTabRule[a].h
            }
          })
        })
        // console.log(nowTabRule)
        // var arr = []
        // var num = 0
        // 获取分割线
        // Object.keys(nowTabRule).forEach(item => {
        //   num += nowTabRule[item].h
        //   if (item.h !== 0) {
        //     arr.push(num)
        //   }
        // })
        // console.log(arr)
        // this.levelHeightList = arr
        // console.log('RULE')
        // this.layoutRuleTop(nowTabRule)
        // this.layoutRuleLeft(nowTabRule)
      } else {
        this.levelHeightList = []
      }
    },
    levelTop (item, index) {
      return { top: item * 2 + 'px' }
    },
    // 布局规则 按区域向上对齐
    layoutRuleTop (nowTabRule) {
      var num = 0
      nowTabRule.forEach(a => {
        if (a.list.length) {
          if (num !== a.y) {
            // console.log('向上缩进')
            a.list.forEach(b => {
              b.y = num
            })
            a.y = num
            num += a.h
          } else {
            num = a.y + a.h
          }
        }
      })
      // console.log(nowTabRule)
    },
    // 布局规则 左对齐
    layoutRuleLeft (nowTabRule) {
      nowTabRule.forEach(a => {
        var num = 0
        a.list.forEach(b => {
          if (b.x !== num) {
            b.x = num
            num += b.w
          } else {
            num += b.w
          }
        })
      })
    },
    // 添加组件 e
    repetitionName (content, {
      type,
      elName
    }) {
      const arrName = content.map(item => item.elName)
      const name = elName
      let num = 0
      while (arrName.length && arrName.includes(elName)) {
        num = num + 1
        elName = `${name} ${num}`
      }
      return elName
    },
    // 日历互斥 s
    calendarChecked(type) {
      const calendar = [
        TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR,
        TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR,
        TYPE_PARAM_ELEMENT.SELECT_PIERIOD_FINANCIAL,
        TYPE_PARAM_ELEMENT.SELECT_PIERIOD_BUSSINESS,
      ]
      const quick = [
        TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION,
        TYPE_PARAM_ELEMENT.CALENDAR_QUICK,
      ]
      let num1, num2, typeTarget, num2Type // 已有、添加、 添加日历类型, 已有日历类型
      let nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
      nowTab.content.forEach(item => {
        if (calendar.indexOf(item.type) !== -1) {
          num2 = calendar.indexOf(item.type)
          num2Type = 'C'
        } else if (quick.indexOf(item.type) !== -1) {
          num2 = quick.indexOf(item.type)
          num2Type = 'Q'
        }
      })
      if ((calendar.indexOf(type) !== -1 || quick.indexOf(type) !== -1) && (num2 || num2 === 0)) {
        let a1 = calendar.indexOf(type)
        let a2 = quick.indexOf(type)
        num1 = a1 < a2 ? a2 : a1
        typeTarget = a1 < a2 ? 'Q' : 'C' // QUICK : CALENDAR
        if (num1 % 2 !== num2 % 2) {
          if (typeTarget === num2Type) {
            this.$message.info(this.$t('sdp.views.calendarMess')) // 相同类型
            return false
          } else {
            this.$message.info(this.$t('sdp.message.selectCalendar')) // 不同类型
            return false
          }
        }
      }
      let trend = nowTab.content.find(item => item.type === TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS || item.type === TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS)
      let isCalendar = nowTab.content.find(item => item.type === TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR || item.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR || item.type === TYPE_PARAM_ELEMENT.DATE_QUICK_OPERATION || item.type === TYPE_PARAM_ELEMENT.CALENDAR_QUICK) || undefined
      if ((type === TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS || type === TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS) && (isCalendar || trend)) return false
      if (calendar.includes(type) || quick.includes(type)) {
        if ((typeTarget || num2Type) && (type === TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS || type === TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS)) {
          return false
        } else if (trend) {
          return false
        }
      }
      return true
    },
    // 层级下拉框 和 classify组件互斥判断
    hierarchyDropdownBoxChecked(type) {
      if (type === TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX) {
        const nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
        const isClassify = nowTab.content.some(e => e.type === TYPE_PARAM_ELEMENT.GROUP)
        if (isClassify) {
          this.$message.info(this.$t('sdp.views.hierarchyDropdownBoxForClassify')) // 已有classify
          return !isClassify
        }
      } else if (type === TYPE_PARAM_ELEMENT.GROUP) {
        const nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)
        const hierarchyDropdownBox = nowTab.content.some(e => e.type === TYPE_PARAM_ELEMENT.HIERARCHY_DROPDOWN_BOX)
        if (hierarchyDropdownBox) {
          this.$message.info(this.$t('sdp.views.hierarchyDropdownBoxForClassify')) // 已有层级下拉框
          return !hierarchyDropdownBox
        }
      }
      return true
    },
    limitChecked(type) {
      const LIMIT = {
        [TYPE_PARAM_ELEMENT.Omit_Zero_Total]: 1,
      }

      const nowTab = this.paramsPanelList.find(tab => tab.id === this.activeTabId)

      const itemList = nowTab.content.filter(e => e.type === type)
      if (itemList.length >= LIMIT[type]) {
        return true
      }

      return false
    }
  }
}
</script>
<style lang="scss">
.sdp-ganged {
  height: 580px;
  .sdp-paramsetting {
    .el-dialog__header {
      background-color: #ffffff !important;
    }

    .el-dialog__title {
      color: $color-main !important;
      font-weight: bold;
      margin: 4px !important;
      font-size: 20px !important;
      background: #ffffff;
    }

    .el-dialog__headerbtn .el-icon-close {
      color: #909399 !important;
    }
  }
  .el-dialog__body {
    padding: 14px 20px !important;
  }
  .el-dialog__footer{
    position: absolute;
    bottom: 0;
    right: 0;
  }
}
</style>
<style lang="scss" scoped>
@import './utils/variable.scss';
.calenderHide {
  opacity: 0;
}
.calenderShow {
  opacity: 1;
}
.calendar-width-b {
  /deep/ .el-dialog__header {
    padding: 0px;
  }
  /deep/ .el-dialog__body {
    padding: 0px!important;
  }
}
.custom-pri-style {
  /deep/ .el-checkbox__label span {
    font-size: 12px;
  }
  margin-top: 16px;
}
.tab-no-data-theme {
  background: $color-KBBJS;
}
.kyzScroll {
  /deep/ .el-dialog {
    position: absolute;
    left: 16px;
  }
}
// .icon-sdp-sdp-canshuzujianguanlian:hover {
//   color: var(--sdp-zs) !important;
// }
.icon-sdp-addhover {
  color: var(--sdp-zs);
}
.sdp-params-tab /deep/ .el-tabs__item {
  width: 141px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  .tab-title {
    opacity: 0.9;
    font-family: NotoSansHans-Regular;
    font-weight: 800;
    font-size: 18px;
    text-align: left;
    line-height: 18px;
    color: #a4a4a4;
  }
  &.is-active .tab-title {
    color: #222222;
  }
  .el-icon-close {
    color: #a4a4a4;
    &:hover {
      color: #979797;
    }
  }
}
.param-font {
  font-family: NotoSansHans-Regular;
  font-weight: 500;
}
.tab-add-style {
  position: absolute;
  right: 16px;
  line-height: 38px;
  height: 38px;
  z-index: 10;
  cursor: pointer;
  // background: #f9f9f9;
  .ml{
    margin-left:12px
  }
}
.tab-line {
  // background-color: #e2e2e2;
  height: 1px;
  width: 100%;
  position: absolute;
  padding-left: 16px;
  padding-right: 16px;
  z-index: 10;
}
.tab-lines {
  height: 1px;
  width: 100%;
  background-color: #e2e2e2;
}
.add-style {
  margin-left: 8px;
  font-size: 12px;
  position: relative;
  bottom: 2px;
  color: #444444;
}
.tab-no-data {
  position: relative;
  width: calc(100% - 32px);
  text-align: center;
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  color: #b8b8b8;
  margin: 0px auto;
  // left: 16px;
}
.tab-no-data-border {
  // border-bottom: 1px solid #E2E2E2;
}
.tab-edite {
  text-align: right;
  margin-right: 16px;
}
.full-screen .el-tabs__content {
  padding: 0 48px;
}
.params-tabs {
  position: relative;
  height: 100%;
  // background: #fff;
  .el-tabs__header {
    margin: 0;
  }
  .params-tabs-footer {
    padding: 10px;
    height: 60px;
  }
  .kyzScroll {
    /*margin-top: 100px;*/
    margin-left: 120px;
    .el-dialog__headerbtn .el-dialog__close {
      display: none;
    }
  }
}
.graybgk {
  background: rgba(52, 56, 61, 0.08);
}
.blockbgk {
  background: rgba(255, 255, 255, 1);
}
.params-tab {
  margin-right: 60px;
}
.params-tab.el-tabs--top {
  .is-top {
    padding-right: 24px;
    border-bottom: 1px solid #edf4f8;
    .el-tabs__nav-scroll {
      /deep/ .el-tabs__nav {
        border: 0;
      }
    }
    .is-active {
      border-bottom: 2px solid #455964;
    }
  }
  .el-tabs__item {
    user-select: none;
    border: 0;
    span {
      color: #ccc;
      font-weight: 700;
      font-size: 24px;
    }
  }
  .is-active {
    border: 0;
    span {
      color: #455964;
      font-weight: 700;
      font-size: 24px;
    }
  }
}
.el-tabs--card > .el-tabs__header .el-tabs__item.is-active.is-closable {
  border-bottom: 2px solid #455964;
}
.isHasFav {
  color: yellow;
}
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
.params-tab-padding {
  padding-left: 48px;
}
.full-screen /deep/ .el-tabs__nav-scroll {
  padding: 0 48px;
}
// 调转默认确认取消按钮
.el-message-box__btns {
  display: flex;
  flex-direction: row-reverse;
  > .el-button--small {
    margin-left: 10px;
  }
  > .el-button--primary {
    margin-left: 0px;
  }
}
.tab-dialog-bottom {
  text-align: right;
  margin-top: 20px;
}
.mask-box {
  position: fixed;
  width: 27%;
  height: 55px;
  top: 0px;
  bottom: 0;
  left: 47px;
  right: 0;
  z-index: 99999;
  background-color: #ffffff;
}
#printData {
  opacity: 0;
  width: 1px;
  height: 1px;
  position: absolute;
  z-index: -111;
}
.sdp-params-tab /deep/ .el-icon-arrow-right:before {
  content: "\E6E0" !important;
}
.sdp-params-tab /deep/ .el-icon-arrow-left:before {
  content: "\E6DE" !important;
}
.set-time-style /deep/ .sdp-dialog {
  width: 420px;
  // height: 320px;
}
.sdp-paramsetting /deep/ .sdp-dialog {
  .el-dialog__header {
    background-color: var(--sdp-szk-bjs);
    .el-dialog__title {
      background-color: var(--sdp-szk-bjs);
    }
  }
  background-color: var(--sdp-szk-bjs);
}
.set-time-style /deep/ .sdp-dialog .el-dialog__body {
  /*padding: 0 0px 0px 24px;*/
}
.time-title {
  font-size: 12px;
  color: #333333;
  line-height: 12px;
  font-family: NotoSansHans-Regular;
  font-weight: 500;
  margin-bottom: 8px;
}
.margin-top-12 {
  margin-top: 12px;
}
.margin-top-24 {
  margin-top: 24px;
}
.bold-time-title {
  font-size: 12px;
  line-height: 12px;
  font-family: NotoSansHans-Regular;
  font-weight: 600;
  color: var(--sdp-ycsz-rskbt);
}
.unlimited {
  margin-left: 24px;
  font-family: NotoSansHans-Regular;
  font-weight: 500;
  font-size: 12px;
  /deep/ .el-checkbox__label {
    font-size: 12px;
    font-family: NotoSansHans-Regular;
    font-weight: 500;
    padding-left: 4px;
  }
}
/deep/ .custom-calendar .el-dialog__header .el-dialog__title {
  font-family: NotoSansHans-Regular;
  font-weight: 800;
}
</style>
