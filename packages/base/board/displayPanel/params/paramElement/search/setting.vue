<template>
  <div class="wrapper search">
    <!-- 基本设置 -->
    <div class="params-top sdp-theme-params-setting-border">
      <el-row>
        <!-- <el-col :span="22" class="wrapper-title">基本设置</el-col> -->
        <el-col
          :span="22"
          class="params-title wrap-title param-setting-normal-font"
        >{{$t('sdp.views.basicSet')}}</el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <param-title-setting :txt.sync="settingParam.elName" :txtStyle.sync="settingParam.elNameStyle"
                               :isHideElement.sync="settingParam.isHideElement">
          </param-title-setting>
        </el-col>
      </el-row>
    </div>
    <div class="flex-row">
      <div class="flex-none border flex-width sdp-theme-params-setting-border">
        <el-scrollbar style="height:100%" ref="scrollbarElist">
        <!-- 关联看板元素 -->
        <!-- <div class="params-title pd10 wrap-title">{{$t('sdp.views.AssociateBoardEle')}}</div> -->
        <div class="params-title pd10 params-title-dataset-title">
          <el-checkbox v-model="checkAll" @change="handleSelectAllEle">{{$t('sdp.views.AssociateBoardEle')}}</el-checkbox>
        </div>
        <div class="boardEl">
          <el-select-list
            ref="elSelectList"
            :board="board"
            multiSelect
            @selection-change="selectionChange"
            @selection-all-change="selectionAllChange"
            v-model="settingParam.content.options.bindTableElements"
          />
        </div>
        </el-scrollbar>
      </div>
      <!-- @change-arr="changeArr" -->
        <div class="flex1">
          <el-scrollbar style="height:100%" ref="scrollbarDatesetList">
            <div class="params-title param-setting-normal-font">{{$t('sdp.views.dimensionFieldBind')}}</div>
            <div
              v-for="(item,i) in dimensionArr"
              :key="i"
              class="flex1-content"
            >
              <div v-show="item.show">
                <div
                  class="params-title param-setting-normal-dataSet-font"
                >{{dimensionFields[i]}}</div>
                <div class="margan-style">
                  <div style="display: inline-block;">
                    <div class="dimension-title param-setting-normal-font">{{$t('sdp.views.Check')}}</div>
                    <!-- CHK -->
                    <el-select
                      v-model="chkSelectedDimensionFields[i]"
                      style="width: 160px"
                      collapse-tags
                      @change="chkSetColumnName(i)"
                      :popper-class="`board-design-pop sdp-paramsetting-select ${getCurrentThemeClass()}`"
                      :placeholder="$t('sdp.placeholder.plsInput')"
                    >
                      <sdp-option
                        v-for="fields of item.fieldList"
                        :key="fields.value"
                        :value="fields.value"
                        :label="fields.value"
                        :item="fields"
                      />
                    </el-select>

                    <!-- <SelectGroup
                      v-model="chkSelectedDimensionFields[i]"
                      style="width: 160px"
                      collapse-tags
                      @change="chkSetColumnName(i)"
                      :fieldValue.sync="fieldValue[`chkFields_${item.id}`]"
                      placeholder="sdp.placeholder.plsInput"
                      :popper-class="
                        `board-design-pop sdp-paramsetting-select ${getCurrentThemeClass()}`
                      "
                      :optionsOne="item.fieldList"
                      :datasetId="item.id"
                    ></SelectGroup> -->
                  </div>
                  <div style="display: inline-block;margin-left:12px;">
                    <!-- AMT -->
                    <div class="dimension-title param-setting-normal-font">
                      {{$t('sdp.views.Amount')}}
                      <el-tooltip
                        effect="dark"
                        :content="$t('sdp.views.OnlyTheFilterSUMFunctionIsSupported')"
                        placement="top-start"
                        style="margin-left: 8px;position: absolute;font-size:14px;"
                      >
                        <i
                          class="icon icon-sdp-tishi"
                        ></i>
                      </el-tooltip>
                    </div>
                    <el-select
                      v-model="amtSelectedDimensionFields[i]"
                      style="width: 160px"
                      collapse-tags
                      @change="amtSetColumnName(i)"
                      :popper-class="`board-design-pop sdp-paramsetting-select ${getCurrentThemeClass()}`"
                      :placeholder="$t('sdp.placeholder.plsInput')"
                    >
                      <sdp-option
                        v-for="fields of item.fieldList"
                        :key="fields.value"
                        :value="fields.value"
                        :label="fields.value"
                        :item="fields"
                      />
                    </el-select>

                    <!-- <SelectGroup
                      v-model="amtSelectedDimensionFields[i]"
                      style="width: 160px"
                      collapse-tags
                      @change="amtSetColumnName(i)"
                      :fieldValue.sync="fieldValue[`amtFields_${item.id}`]"
                      placeholder="sdp.placeholder.plsInput"
                      :popper-class="
                        `board-design-pop sdp-paramsetting-select ${getCurrentThemeClass()}`
                      "
                      :optionsOne="item.fieldList"
                      :datasetId="item.id"
                    ></SelectGroup> -->
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
    </div>
  </div>
</template>

<script>
import ElSelectList from '../../ElSelectList'
import { getDatasetListByIds, getDatasetFields } from './api'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import axios from 'axios'
import EventData from 'packages/assets/EventData'
// import { methodsObj } from './utils'
import { substring15 } from '../../../utils'
import ParamTitleSetting from '../../components/ParamTitleSetting'
import SdpOption from 'packages/base/common/sdpOption/index.vue'
import settingMixin from 'packages/base/board/displayPanel/params/paramElement/settingMixin';
import { PARAM_NAME_MAX_LEN } from '../../utils/constants'
import selectGroupMixin from 'packages/base/board/displayPanel/params/mixins/selectGroupMixin'
import { actionGetDataSetIds } from 'packages/base/board/displayPanel/params/utils/utils.ts'
export default {
  // inject: ['board'],
  inject: ['utils', 'getCurrentThemeClass'],
  mixins: [settingMixin, selectGroupMixin],
  components: {
    ParamTitleSetting,
    ElSelectList,
    SdpOption
  },
  props: {
    settingParam: {
      type: Object,
      default: () => ({}),
    },
    board: {
      type: Object,
      default: () => ({}),
    }
  },
  data () {
    return {
      TYPE_ELEMENT,
      // 数据集列表
      dataSetIds: [],
      // 选中的数据集
      selectedDataset: '',
      // 维度字段列表
      dimensionFields: [],
      // 选中的维度字段
      chkSelectedDimensionFields: [],
      amtSelectedDimensionFields: [],
      menuItemSelectedDimensionFields: [],
      actionSelectedDimensionFields: [],
      ruleList: [],
      // 维度列表
      dimensionArr: [],
      checkAll: false,
      initBindElement: []
    }
  },
  computed: {
    api () {
      return this.utils.api || function () { }
    }
    // amtDimensionArr() {
    //   return this.dimensionArr
    // },
    // menuItemDimensionArr() {
    //   return this.dimensionArr
    // },
    // actionDimensionArr() {
    //   return this.dimensionArr
    // },
  },
  watch: {
    'settingParam.content.options.bindTableElements.length' (val) {
      if (!val) {
        this.dimensionArr = []
        this.chkSelectedDimensionFields = []
        this.amtSelectedDimensionFields = []
        this.menuItemSelectedDimensionFields = []
        this.actionSelectedDimensionFields = []
      }
    },
  },
  mounted () {
    this.initData()
  },
  methods: {
    substring15,
    // ...methodsObj,
    // 关联元素 全选部分s
    handleSelectAllEle() {
      this.$refs.elSelectList.handleSelectAllEleChange(this.checkAll)
    },
    selectionAllChange(checkAll) {
      this.checkAll = checkAll
    },
    initData () {
      const bindElement = this.$_getProp(this.settingParam, 'content.options.bindTableElements', [])
      if (bindElement.length > 0) {
        let initBindElement
        if (Array.isArray(bindElement)) {
          initBindElement = bindElement
        } else {
          initBindElement = [bindElement]
        }
        this.$refs.elSelectList.setSelected(initBindElement)
        var list = this.board.elList.filter(item => initBindElement.indexOf(item.id) !== -1)
        this.selectionChange(bindElement, this.board.elList)
        this.chkSelectedDimensionFields = this.settingParam.content.chkSelectedDimensionFields || []
        this.amtSelectedDimensionFields = this.settingParam.content.amtSelectedDimensionFields || []
        // this.menuItemSelectedDimensionFields = this.settingParam.content.menuItemSelectedDimensionFields
        // this.actionSelectedDimensionFields = this.settingParam.content.actionSelectedDimensionFields
        this.settingParam.content.options.dataSets.forEach((item, i) => {
          item.chkField.columnName = this.chkSelectedDimensionFields[i]
          item.amtField.columnName = this.amtSelectedDimensionFields[i]
          // item.actionField.columnName = this.actionSelectedDimensionFields[i]
        })
      }
    },
    // 将需要修改的参数抛出
    // changeParam() {
    //   this.$emit('commit-change', {
    //     elName: this.settingParam.elName,
    //     content: this.settingParam.content,
    //   })
    // },
    // 关闭
    cancel () {
      const eventData = new EventData({
        ...this.defaultEventData,
        data: this.settingParam,
        target: 'tab',
        targetFn: 'cancelShow'
      })
      this.$emit('eventBus', eventData)
    },
    // 将需要修改的参数抛出
    changeParam () {
      const eventData = new EventData({
        ...this.defaultEventData,
        data: this.settingParam,
        target: 'tab',
        targetFn: 'onCommitChanges'
      })
      this.$emit('eventBus', eventData)
    },
    onConfirm () {
      // const elNameLength = this.$_getStringLength(this.settingParam.elName)
      // if (elNameLength > 30) {
      if (this.settingParam.elName.length > PARAM_NAME_MAX_LEN) {
        return this.$message.warning(this.$t('sdp.views.inputTipsTitle'))
      }
      if (!this.settingParam.elName) return this.$message.warning(this.$t('sdp.placeholder.pleaseInputName'))
      // if (!this.selectedDimensionFields.length || this.settingParam.content.options.dataSets.length !== this.selectedDimensionFields.length) return this.$message.warning(this.$t('placeholder.pleaseSelectDimension'))
      if (this.verifyPlstipSetting()) return this.$message.warning(this.$t('sdp.views.plstipSetting'))
      const nameState = this.funGetReName(this.editParamList, this.settingParam)
      var newDate = []
      const oldDate = this.settingParam.content.options.dataSets
      // this.chkSelectedDimensionFields.forEach((a, i) => {
      //   if (a) {
      //     newDate.push(oldDate[i])
      //   }
      // })
      this.dimensionArr.forEach((item, i) => {
        const bool = this.chkSelectedDimensionFields[i] || this.getFieldValueItem(item.id, 'chkFields')?.length || this.amtSelectedDimensionFields[i] || this.getFieldValueItem(item.id, 'amtFields')?.length

        if (bool) {
          newDate.push(oldDate[i])
        }
      })

      this.settingParam.content.options.dataSets = newDate
      const ids = this.dimensionArr.map(item => item.id)
      this.settingParam.content.options.injectConditionParameter = this.getInjectConditionParameter({
        hasDataSetIds: ids
      })
      // this.board.paramsPanelList.forEach(a => {
      //   a.content.forEach(item => {
      //     if (item.id === this.settingParam.id) {
      //       a.content.filter(item => item.id !== this.settingParam.id).forEach(c => {
      //         if (c.elName.toLowerCase() === this.settingParam.elName.toLowerCase()) {
      //           nameState = false
      //         }
      //       })
      //     }
      //   })
      // })
      this.$set(this.settingParam.content, 'notReset', false)
      if (nameState) {
        this.changeParam()
      } else {
        this.$message.warning(this.$t('sdp.message.componentCannotTheSame'))
      }
    },
    verifyPlstipSetting() {
      const isHasAmtFields = this.hasFieldTypeName('amtFields')
      const isHasChkFields = this.hasFieldTypeName('chkFields')
      const check = (this.chkSelectedDimensionFields.filter(a => a).length > 0 || isHasChkFields) && (this.amtSelectedDimensionFields.filter(a => a).length > 0 || isHasAmtFields)

      // const check = this.dimensionArr.some((item, i) => (this.chkSelectedDimensionFields[i] || this.getFieldValueItem(item.id, 'chkFields')?.length) && (this.amtSelectedDimensionFields[i] || this.getFieldValueItem(item.id, 'amtFields')?.length))
      const bool = !this.settingParam.content.options.bindTableElements.length || !check

      return bool
    },
    settingVerify() {
      // const elNameLength = this.$_getStringLength(this.settingParam.elName)
      // if (elNameLength > 30) {
      //   this.warningMsg(this.$t('sdp.views.inputTipsTitle'))
      //   return false
      // }
      if (this.settingParam.elName.length > PARAM_NAME_MAX_LEN) {
        this.warningMsg(this.$t('sdp.views.inputTipsTitle'))
        return false
      }

      if (!this.settingParam.elName) {
        this.warningMsg(this.$t('sdp.placeholder.pleaseInputName'))
        return false
      }

      const nameState = this.funGetReName(this.editParamList, this.settingParam)
      if (!nameState) {
        this.warningMsg(this.$t('sdp.message.componentCannotTheSame'))
        return false
      }
      if (this.verifyPlstipSetting()) {
        this.warningMsg(this.$t('sdp.views.plstipSetting'))
        return false
      }

      return true
    },
    selectionChange (selectEL) {
      // console.log(selectEL)
      // console.log(selectList)
      // this.dimensionArr = []
      // this.chkSelectedDimensionFields = []
      // this.amtSelectedDimensionFields = []
      // this.ruleList = this.settingParam.content.options.bindTableElements || []
      // 重置选择
      var selectList = this.board.elList.filter(item => item.type !== TYPE_ELEMENT.CONTAINER)
      var reDate = []
      var allList = []
      selectList = Array.isArray(selectList) ? selectList : [selectList]
      selectList.forEach(table => {
        var selectListfilter = []
        var selectListChart = []
        var selectListCardfilter = []
        switch (table.type) {
          case TYPE_ELEMENT.CUSTOMER_ELEMENT: {
            const dataSetIds = table.content?.config?.dataSetIds || []
            selectListChart.push(...dataSetIds)
            break
          }
          case TYPE_ELEMENT.TABLE: {
            if (table.content.dataSetIds) {
              table.content.dataSetIds.forEach(item => {
                if (item) selectListfilter.push(item)
              })
            }
            break
          }
          case TYPE_ELEMENT.CHART: {
            selectListChart = actionGetDataSetIds(TYPE_ELEMENT.CHART, table.content)
            // if (table.content.drillSettings.dataSetId) {
            //   selectListChart.push(table.content.drillSettings.dataSetId)
            // }
            break
          }
          case (TYPE_ELEMENT.ELEMENT_TAG_CARD): {
            if (!Object.keys(table.content.options).includes('dataSetId')) return
            if (table.content.options.dataSetId) {
              selectListCardfilter.push(table.content.options.dataSetId)
            }
            break
          }
          case (TYPE_ELEMENT.ELEMENT_YEAR_CARD): {
            if (!Object.keys(table.content.options).includes('dataSetId')) return
            if (selectListCardfilter.push(table.content.options.dataSetId)) {
              selectListCardfilter.push(table.content.options.dataSetId)
            }
            break
          }
          case (TYPE_ELEMENT.COMPARE_TABLE): {
            if (!Object.keys(table.content.options).includes('dataSetId')) return
            if (selectListfilter.push(table.content.options.dataSetId)) {
              selectListfilter.push(table.content.options.dataSetId)
            }
            break
          }
          case (TYPE_ELEMENT.FOUR_QUADRANT): {
            for (let key in table.content) {
              if (table.content[key]) {
                if (table.content[key].dataSetId) {
                  selectListfilter.push(table.content[key].dataSetId)
                }
              }
            }
            break
          }
          case TYPE_ELEMENT.TEXT || TYPE_ELEMENT.IMAGE: {
            break
          }
          case TYPE_ELEMENT.ELEMENT_TAG_NEW_CARD: {
            if (table.content.optionArray) {
              table.content.optionArray.map(item => item.dataSetId).filter(item => {
                selectListfilter.push(item)
              })
            }
            break
          }
          case (TYPE_ELEMENT.COMBINE_CARD): {
            table.content.choiceTab.forEach(item => {
              item.saveObj.cardList.forEach(el => {
                if (el.content.optionArray) {
                  el.content.optionArray.map(v => v.dataSetId).filter(v => selectListfilter.push(v))
                }
              })
            })
            break
          }
          default: {
            if (!Object.keys(table?.content?.options || {}).includes('dataSetId')) return
            selectListfilter.push(table.content.options.dataSetId)
            break
          }
        }
        var data = [...selectListfilter, ...selectListCardfilter, ...selectListChart]
        reDate.push({ id: table.id, elName: table.elName, data: data })
        allList.push(data)
      })
      var dataSetIds = []
      allList.forEach(item => {
        dataSetIds = dataSetIds.concat(item)
      })
      this.getFiled(dataSetIds, reDate)
    },
    // 获得图表和卡片的字段
    async getFiled (dataSetIds, reDate) {
      // console.log(reDate)
      // console.log(dataSetIds)
      dataSetIds = [...new Set(dataSetIds)]
      const dataSets = await getDatasetListByIds(this.api, dataSetIds)
      const dataSetList = this.$_getProp(this.settingParam, 'content.options.dataSets', [])
      dataSetIds.forEach((item, i) => {
        if (!dataSetList[i]) {
          dataSetList[i] = {}
        }
        dataSetList[i].dataSetId = item
      })
      this.ruleList = dataSetList
      // 获取名字
      // const dataSetName = dataSets.map(item => item.name)
      // console.log('dataSetName', dataSets)
      // 获取多个字段
      let datasetsFields = await axios.all(dataSetIds.map(datasetId => getDatasetFields(this.api, { id: datasetId })))
      datasetsFields = datasetsFields.map((item, index) => {
        const tempArr = item.map(itemFields => {
          return {
            value: itemFields.labeName,
            columnType: itemFields.columnTpe,
            parentId: itemFields.parentId,
            comment: itemFields.comment ? itemFields.comment : '',
            ...itemFields
          }
        })
        return {
          id: tempArr[0].parentId,
          fieldList: tempArr,
        }
      })
      // console.log(dataSets)
      datasetsFields.forEach((item, i) => {
        reDate.forEach(a => {
          if (a.data.indexOf(item.id) !== -1) {
            if (item.bindid) {
              item.bindid.push(a.id)
            } else {
              item.bindid = [a.id]
            }
            item.name = a.elName
          }
        })
        dataSets.forEach((every) => {
          item.id === every.id && (item.name = every.name)
        })
      })
      this.addDimension(datasetsFields, reDate)
      this.dimensionFields = datasetsFields.map(item => item.name)
      this.dimensionFields = Array.from(new Set(this.dimensionFields))
    },
    addDimension (dimensions, reDate) {
      const bindElement = this.$_getProp(this.settingParam, 'content.options.bindTableElements', [])
      let initBindElement = []
      if (bindElement.length > 0) {
        if (Array.isArray(bindElement)) {
          initBindElement = bindElement
        } else {
          initBindElement = [bindElement]
        }
      }
      dimensions.forEach((item, i) => {
        var check = false
        item.bindid.forEach(b => {
          if (initBindElement.indexOf(b) !== -1) {
            check = true
          }
        })
        if (check) {
          item.show = true
        } else {
          item.show = false
          this.chkSelectedDimensionFields[i] = undefined
          this.amtSelectedDimensionFields[i] = undefined
          this.deleteFieldValueItem(item.id, 'chkFields')
          this.deleteFieldValueItem(item.id, 'amtFields')

          this.settingParam.content.options.dataSets.forEach(data => {
            if (data.dataSetId === item.id) {
              this.$delete(data, 'chkField')
              this.$delete(data, 'amtField')
            }
          })
        }
      })
      console.log(dimensions)
      this.dimensionArr = dimensions
    },
    chkSetColumnName (i) {
      this.settingParam.content.chkSelectedDimensionFields = this.chkSelectedDimensionFields
      this.settingParam.content.options.dataSets.some((item, index) => {
        if (!item.chkField) item.chkField = {}
        if (index === i) item.chkField.columnName = this.chkSelectedDimensionFields[i]
        return index === i
      })
    },
    amtSetColumnName (i) {
      this.settingParam.content.amtSelectedDimensionFields = this.amtSelectedDimensionFields
      this.settingParam.content.options.dataSets.some((item, index) => {
        if (!item.amtField) item.amtField = {}
        if (index === i) item.amtField.columnName = this.amtSelectedDimensionFields[i]
        return index === i
      })
    },
    // menuItemSetColumnName(i) {
    //   this.settingParam.content.menuItemSelectedDimensionFields = this.menuItemSelectedDimensionFields
    // },
    // actionSetColumnName(i) {
    //   this.settingParam.content.actionSelectedDimensionFields = this.actionSelectedDimensionFields
    //   this.settingParam.content.options.dataSets.some((item, index) => {
    //     if (!item.actionField) item.actionField = {}
    //     if (index === i) item.actionField.columnName = this.actionSelectedDimensionFields[i]
    //     return index === i
    //   })
    // },
  },
}
</script>

<style lang="scss" scoped>
@import "../style/arguments.scss";
.params-main {
  overflow-y: auto;
  height: 450px;
  float: left;
}
.param-setting-normal-dataSet-font {
  margin-top: 8px;
}
.search {
  .params-top {
    margin: 0px;
    border-bottom: 1px solid #dddddd;
    padding-bottom: 16px;
    margin-bottom: 16px;
  }
  .input-name {
    position: absolute;
    z-index: 9999;
    margin-left: 12px;
    width: 44px;
    height: 32px;
    line-height: 32px;
  }
  .flex-width {
    width: 216px;
  }
  .margin-left26 {
    margin-left: -26px;
  }
  .params-bottom {
    margin-top: 20px;
    text-align: right;
  }
}
.pd10 {
  padding-bottom: 10px !important;
}
.margan-style {
  // margin-left: -50px;
  font-size: 12px;
}
.check-flied {
  margin-right: 6px;
  display: inline-block;
  padding: 4px;
  border: 1px solid #13b5b1;
  margin-bottom: 6px;
  border-radius: 5px;
}
.checkbox-style {
  display: inline-block;
  line-height: 32px;
  height: 32px;
}
.border {
  border-right: 1px solid #dddddd;
}
.params-title {
  padding-bottom: 8px;
  font-weight: 500;
}
.wrap-title {
  font-weight: 600;
}
.flex-row {
  height: 450px;
  width: 100%;
  display: flex;
}
.flex-none {
  // background-color: #fff;
  padding: 10px 0;
  width: 160px;
  overflow-y: auto;
  height: 100%;
  float: left;
}
.flex1 {
  margin-left: 8px;
  overflow-y: auto;
  padding-left: 16px;
  height: 450px;
  width: 720px;
  .flex1-content {
    margin: 0 0 0px 0px;
  }
  .flex1-content:nth-child(2) {
    margin-top: 0px;
  }
}
/deep/ .el-scrollbar__wrap {
  overflow-x: hidden;
}
.dimension-title {
  width: 160px;
  padding-bottom: 8px;
  .icon-sdp-tishi{
    color: var(--sdp-zs);
  }
}
</style>
