<template>
  <div class="omit-zero-wrap">
    <param-element-title
      v-bind="{
        txt: `${paramName}:`,
        elType: paramElement.type,
        txtStyle: paramElement.elNameStyle,
        isHideElement: paramElement.isHideElement
      }"
    />
    <el-radio-group
      v-model="selectedValue"
      @change="selectedChange"
      class="paramcommponents-radio radioContent"
    >
      <el-radio
        v-for=" ({ label, value }) in omitOptions"
        :key="value"
        :label="value"
        class="radio-style sdp-params-theme-radio"
        :title="label"
      >
        {{ label }}
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script>
import ParamElementTitle from "../../../components/ParamElementTitle";
import index_mixin from '../mixins/index'

export default {
  name: 'index',
  mixins: [index_mixin],
  components: {
    ParamElementTitle
  },
}
</script>

<style scoped lang="scss">
.omit-zero-wrap {
  padding-left: 6px;
  width: 100%;
  display: flex;
  align-items: center;

  .el-radio-group{
    /deep/ .el-radio.is-checked{
      .el-radio__label{
        color: var(--sdp-zjbxx-wzs) !important;
      }
    }
    /deep/ .el-radio__input{
      &.is-focus{
        .el-radio__inner{
          border-color: var(--sdp-xk-bks);
        }
      }
      &.is-checked{
        .el-radio__inner{
          background-color: var(--sdp-cszjdxtc);
          border-color: var(--sdp-cszjdxtc);
          &::after{
            border-color: var(--sdp-cszjdxg);
          }
        }
      }
    }
  }
}
</style>
