<template>
  <div class="calendar-mobile" :class="{ 'is-horizontal': horizontalPre }">
    <!--  平铺快选  -->
    <CalendarTileBox :userSetting="userSetting" v-if="isTileShow"
                     :visible="visible" ref="calendarTileBox"
                     :isIconQuick="isIconQuick"
                     :curDataList="dateAllData"
                     :isChangeSubsidiaryQuick.sync="isChangeSubsidiaryQuick"
                     :curQuickSelectData.sync="curQuickSelectData"
                     :horizontalPre="horizontalPre"
                     :paramElement="paramElement"
                     :currentTabActive.sync="currentTabActive"
                     :calendarType="quickFinancialType"
                     :selectQuickDate="selectQuickDate"
                     :parent="getBoardUnderIdElement('#mobileParamsPanel')"
                     :handUserClearAll="handClearAll"
                     :setSaveUserActiveData="setSaveUserActiveData"
                     :boardTileDisplayList="boardTileDisplayList"
                     :handClickUserSubmit="handClickUserSubmit"
                     @handNavBack="handNavBack"
                     @handOk="handOk"/>
    <!-- 日历零散-->
    <CalendarTab
      ref="calendarTabRef"
      @navBack="handNavBack"
      @expandChange="isExpandChange"
      :isCustomDateComparison="isCustomDateComparison"
      :tabs="tabs"
      :isTileShow="isTileShow"
      :isExpand="isExpand"
      :showBack="!isIconQuick"
      v-model="currentTabActive"
    >
      <template v-slot="{ name, value }">
        <CalendarTabPanel :name="name" :value="value">
          <div style="height: 100%;" :class="[horizontalPre ? 'horizontal-flex' : '']">
            <div :style="{ width: horizontalPre ? '55%' : '100%' }">
              <subsidiaryMobile
                :ref="`calendarSubsidiaryRef${value}`"
                :tabKey="value"
                :isTileShow="isTileShow"
                :paramElement="paramElement"
                :visible="visible"
                :isSingle="isSingle"
                :api="api"
                :tenantId="tenantId"
                :userSetting="userSetting"
                :horizontalPre="horizontalPre"
                :isChangeSubsidiaryQuick="isChangeSubsidiaryQuick"
                :isCurrentTab="currentTabActive === 'current'"
                :isShow="currentTabActive === value"
                :selectedData="selectData[value] || {}"
                :selectedYear="selectedYear[value] || {}"
                :shiftSignStart.sync="shiftSignStart"
                :shiftSignEnd.sync="shiftSignEnd"
                :shiftSignStartSaveCallStack.sync="shiftSignStartSaveCallStack"
                :getFinancialData="getFinancialData"
                @setSaveSelectData="setSaveSelectData"
                @shiftClickDay="shiftClickDay"
                @rowNumChange="rowNumChange"
                @yearClick="handleYearClick"
                @monthClick="handleMonthClick"
                @clearQuickSelect="clearQuickSelect"
                @refreshScroll="scrollRefresh"
                :selectList="selectList"
                :dateAllData="dateAllData"
                @initData="initSubsidiaryData"
                :selectedShowType="selectedShowType"
                @handleResultOpen="handleResultOpen"
              />
            </div>
            <!--  月的组件  -->
            <div  :style="{
                width: horizontalPre ? '50%' : '100%',
                height: `calc(100% - ${horizontalPre ? 0 : dataBoxHeight}px)`
              }"
            >
              <CalendarResultList
                  :className="{'transparent-style': selectedShowType !== 'view'}"
                  :ref="`CalendarResultList${value}`"
                  :cubeStyle="{ height: `calc(100% - 50px)` }"
                  :style="{ height: `calc(100% - ${isIconQuick ? 72 : 0}px)` }"
                  :dataList="dataList"
                  :visible="visible"
                  :isTileShow="isTileShow"
                  :isYearData="accordingToYear && !isChangeSubsidiaryQuick"
                  :yearData="selectedYear[value]"
                  @clear="handClearAll($event,value)"
                  @dateClear="handClearSingleDate($event,value)"
                />
              <operate-box
                v-if="isIconQuick"
                :showBack="true"
                :sliderHeight="false"
                :confirmText="$t('sdp.views.Run')"
                :viewportId="viewportId"
                @back="handBack"
                @confirm="handOk"
                @paramConfirm="paramConfirm"
              />
            </div>
          </div>
        </CalendarTabPanel>
      </template>
    </CalendarTab>
    <!--  切换日期和选择年  -->
    <yearSwitchPopup
      ref="yearSwitchPopupRef"
      :isCalendarSelection="contentMap.userSetting.particularData.calendarSelection"
      :accordingToYear="accordingToYear"
      :isChangeSubsidiaryQuick.sync="isChangeSubsidiaryQuick"
      :yearSelectData="yearSelectData"
      :financialYearData="financialYearData"
      @switchDate="switchDate"
      @selectYear="selectYear"
      @clearQuickSelect="clearQuickSelect"
    />
    <!-- 默认 年 月   -->
    <Popup
      ref="sdpPopup"
      :selectedShow="selectedShow"
      :clearData="clearData"
      :handClickUserSubmit="handClickUserSubmit"
      :nowStr="nowStr"
      :calendarType="quickFinancialType"
      :userSetting="userSetting"
      :finishParams="finishParams"
      :paramElement="paramElement"
      :dateYearList="selectList"
      :dateAllData="dateAllData"
      :getFinancialData="getFinancialData"
      :paramsPanelTypeRef="paramsPanelTypeRef"
      :componentKey="selectType"
      :parent="getBoardUnderIdElement('#mobileParamsPanel')"
      @initData="initSubsidiaryData"
    />

    <result-popup
      :visible.sync="resultVisible"
      :selectedList="dataList"
      :dataProp="{ label: 'value' }"
      @confirm="handleResultConfirm"
      @clear="handClearSingleDate($event)"
      @clearAll="handClearAll()"
    />
  </div>
</template>
<script>
  import bussinessCalendarMobile from 'packages/base/board/displayPanel/params/paramElement/bussinessCalendarOld/mobile/render/bussinessCalendar_mobile.vue'
  import subsidiaryMobile from 'packages/base/board/displayPanel/params/paramElement/financialCalendar/components/subsidiaryMobile'
  import Popup from '../components/popup'
  import calendarTileBox from '../../bussinessCalendarOld/mobileComponents/calendarTileBox'
  import yearSwitchPopup from '../components/yearSwitchPopup'
  import operateBox from 'packages/base/board/displayPanel/params/paramElement/bussinessCalendar/mobileComponents/calendarPopup/operateBox'
  import ResultPopup from 'packages/base/board/displayPanel/params/paramElement/bussinessCalendar/components/calendarSubsidiary/components/resultPopup.vue'
  import index_mixins from '../index_mixins'
  import { getFinanicalPriorSelectData } from '../components/utils'
  import {
    DayTime, PERIOD,
    RESTORE_TYPE,
    UNIQUE_SELECT_KEY
  } from '../../bussinessCalendar/components/constants'
  import {
    CALENDARTYPE,
    COMPONENTS_NAME_PC,
    TYPE_LIST,
    quickFinancialType
  } from 'packages/base/board/displayPanel/params/paramElement/financialCalendar/components/constants'
  import { safariCompliant } from 'packages/base/board/displayPanel/params/paramElement/bussinessCalendar/components/utils'
  import { handTransDateToDefault } from '../../bussinessCalendar/pc/render/tools'
  import { FINISH_TYPE } from '../../../utils/constants'
  import { transMonthFormatter } from '../../../utils/dateTools'
  import {getSelectType} from "../utils";

  export default {
    extends: bussinessCalendarMobile,
    mixins: [index_mixins],
    components: {
      operateBox,
      subsidiaryMobile,
      yearSwitchPopup,
      Popup,
      calendarTileBox,
      ResultPopup
    },
    data() {
      return {
        boardTileDisplayList: [],
        dataBoxHeight: 367,
        financialYearData: [],
        isBussiness: false,
        selectedYear: {
          [UNIQUE_SELECT_KEY.CURRENT]: [],
          [UNIQUE_SELECT_KEY.PERIOD]: []
        },
        quickFinancialType,
        resultVisible: false,
        selectedShowType: 'view', // button(按钮触发显示) | view(直接显示) 默认 view
      }
    },
    inject: ['platformMetaDashboardConfig'],
    computed: {
      horizontalPre() {
        return this.commonData?.isHorizontal && this.commonData.isPreview
      },
      // 未来日期
      futureCalendar() {
        return this.userSetting.particularData.future
      },
      // 未来日期
      futureYear() {
        return this.userSetting.particularData.futureYear
      },
      yearSwitchDisabledList() {
        return []
        // if (!this.futureCalendar) return []
        // const yearNow = new Date().getFullYear()
        // const endDateTimestamp = +new Date(`${yearNow + Number(this.futureYear)}/12/31 23:59:59`)
        // return this.selectList.filter(e => new Date(safariCompliant(e.startDate)) > endDateTimestamp).map(year => year.id)
      },
      isTileShow() { // 面板控制快选是否平铺展示
        return this.tenantData?.settingConfig?.calendarInteractionStyle === '2' && !this.userSetting.isQuick && this.userSetting.selectType === TYPE_LIST.default
      },
      // 未来日期
      allYearDates() {
        return this.getAllYearDates()
      },
      selectType() {
        return getSelectType(this.userSetting.selectType, true)
      },
      selectListSort() {
        return this.selectList.sort((a, b) => {
          // @ts-ignore
          return new Date(safariCompliant(b.startDate)) - new Date(safariCompliant(a.startDate))
        })
      },

      viewportId() {
        return `${this.paramElement.type}_${this.paramElement.id}`
      }
    },
    watch: {
      currentTabActive(v) {
        if (this.singleTabActive()) return
        this.shiftSignStart = 0
        this.shiftSignEnd = 0
        this.shiftSignStartSaveCallStack = []
        // 重置选择的整年ID
        if (this.selectedYear[v].length) {
          this.yearSelectData = this.selectedYear[v][0].id || this.yearSelectData
        }
        this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].setMonthData()
        this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.PERIOD}`].setMonthData()
        if (v === UNIQUE_SELECT_KEY.PERIOD) {
          this.clearQuickSelect(true) // 清除快选数据
        }

        if (this.accordingToYear && this.selectedYear[v].length) {
          setTimeout(() => {
            // const curSelYear = this.selectedYear[this.currentTabActive] || {}
            // this.yearSelectData = this.selectedYear[v][0].id || this.yearSelectData
            this.switchSelectYearSubsidiary(this.yearSelectData)
          })
        }
      },
      visible(val) {
        this.observerHeight()
      }
    },
    mounted() {
      this.initBoardTileDisplayList()
    },
    methods: {
      handleResultOpen() {
        this.resultVisible = true
      },
      handleResultConfirm() {
        this.handOk()
        this.resultVisible = false
      },
      observerHeight() {
        const CHANGE_SELECTED_SHOW_TYPE_MIN_HEIGHT = 150
        this.$nextTick(() => {
          if (!this.$refs['CalendarResultList' + this.currentTabActive]) return
          if (this.$refs['CalendarResultList' + this.currentTabActive].$el.clientHeight < CHANGE_SELECTED_SHOW_TYPE_MIN_HEIGHT) {
            // 切换显示方式
            this.selectedShowType = 'button'
          } else {
            this.selectedShowType = 'view'
          }
        })
      },
      initBoardTileDisplayList() {
      console.log('this.platformMetaDashboardConfig: ', this.platformMetaDashboardConfig)
      const TILE_DISPLAY = '2'
      if (this.platformMetaDashboardConfig.calendarInteractionStyle === TILE_DISPLAY) {
        this.boardTileDisplayList = this.platformMetaDashboardConfig.financialCalendar || []
      }
    },
      // location 变化时调用，监听在index_mixins
      async locationStateChangeCallback(isChangeShopId) {
        if (!isChangeShopId) return
        if (!this.isFinished) return
        // 如果是快选，则提交快选，清空零散数据
        // 如果是零散，则清空零散，走初始化
        if (isChangeShopId) {
          this.clearData()
          this.accordingToYear = false
          this.clearSelectedYear()
        }
        const typeSaveData = this.contentMap.typeSaveData
        const isSelectQuick = typeSaveData ? typeSaveData.default?.isSelectQuick : true
        if (isSelectQuick) {
          // 如果是自定义区间
          // 如果是普通快选
          this.finishHook(FINISH_TYPE.AWAIT)
          this.hasFinancialData = true
          this.$refs.sdpPopup.isChangeShopId = true
          this.$refs.sdpPopup[RESTORE_TYPE.reSetUserHandle](typeSaveData.default)
        } else if ([TYPE_LIST.Year, TYPE_LIST.Period].includes(typeSaveData.activeTabs)) {
          this.$refs.sdpPopup.isChangeShopId = true
          this.$refs.sdpPopup[RESTORE_TYPE.reSetDefault]()
        } else {
          if (this.userSetting.isQuick && !this.userSetting.isParticular) {
            this[RESTORE_TYPE.reSetDefault]()
          } else {
            await this.getFinancialData()
          }
          this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`] && this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].setMonthData()
          this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.PERIOD}`] && this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.PERIOD}`].setMonthData()
          if (!this.userSetting.isQuick) {
            const userData = this.emitUserSelectData()
            this.handClickUserSubmit(TYPE_LIST.default, userData)
          }
        }
      },
      // 恢复用户操作
      async restoreUserData() {
        const typeSaveData = this.contentMap.typeSaveData
        // await this.getFinancialData()
        return this.getRestoreCallback({ data: typeSaveData, type: RESTORE_TYPE.reSetUserHandle })
      },
      // 恢复初始化
      async restoreData() {

        return this.getRestoreCallback({ data: handTransDateToDefault(this.contentMap), type: RESTORE_TYPE.reSetDefault })
      },
      getRestoreCallback({ data, type }) {
        // 处理禁用逻辑
        if (this.isDisabled) {
          return this.finishHook(FINISH_TYPE.UNFINISH)
        }
        this.$nextTick(async () => {
          const isInit = type === RESTORE_TYPE.reSetDefault
          const isUser = type === RESTORE_TYPE.reSetUserHandle
          const selectType = isInit ? getSelectType(this.userSetting.selectType, true) : data.activeTabs

          if (isInit) {
            await this[RESTORE_TYPE.reSetDefault](data[selectType] || {})
          }

          if (isUser) {
            await this[RESTORE_TYPE.reSetUserHandle](data[selectType] || {})
          }

          // 禁用快选 初始化零散
          if (isInit && this.isQuick) {
            // this.useScattered(type)
            return
          }
          // 用户操作 处理零散
          if (isUser && selectType === TYPE_LIST.default && !data[selectType].isSelectQuick) {
            this.useScattered(type, data[selectType] || {})
            return
          }
          // 设置数据上去
          const promiseCall = this.example[type](data[selectType] || {})
          const callback = () => {
            // 拿数据
            // const userData = this.example.emitUserSelectData()
            // this.handClickUserSubmit(selectType, userData, type)
          }
          // 快选会调用到handClickUserSubmit
          if (promiseCall instanceof Promise) {
            promiseCall.then(() => {
              callback()
            })
          } else {
            callback()
          }
        })
      },
      getFinanicalPriorSelectData({ data }) {
        return getFinanicalPriorSelectData({ api: this.api, data, tenantId: this.tenantId, calenderCommonData: this.calenderCommonData })
      },
      setSaveSelectData(data, isSelect, key = this.currentTabActive, curYear = this.curYear) {

        if (!data.length) return

        const currentTabActive = this.selectData[key]
        const curYearData = currentTabActive[curYear]
        if (curYearData) {
          if (isSelect) {
            currentTabActive[curYear] = [...new Set([...currentTabActive[curYear], ...data])]
          } else {
            this.$set(currentTabActive, curYear, curYearData.filter(item => !data.includes(item)))
          }
        } else if (isSelect) {
          this.$set(currentTabActive, curYear, data)
        }
        // // 单选操作并且不是快选
        // if (!this.curSelTileQuick && this.isSingle && isSelect) {
        //   Object.keys(currentTabActive).forEach(e => {
        //     if (e !== String(curYear)) {
        //       this.$set(currentTabActive, e, [])
        //     } else {
        //       this.$set(currentTabActive, e, data)
        //     }
        //   })
        //   if (key === this.currentTabActive) {
        //     this.clearMonth(key, data[0])
        //   }
        //   // 清楚本期上期数据 重新选中当前点击数据
        //   // this.handUserClearAll(this.$_flatten(Object.values(this.allDataList)).filter(item => !data.includes(item.timestamp)), key, curYear)
        // }

        // 单选操作并且不是快选
        this.setSaveSingleData(data, isSelect, key, curYear)
        if (key === UNIQUE_SELECT_KEY.CURRENT) { // 清除本期时上期也要联动清除
          // const tmpData = this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].monthArray || []
          // const weekDatas = tmpData.reduce((pre, cur) => {
          //   pre = [...pre, ...cur.weekData]
          //   return pre
          // }, [])
          // // const monthData = this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].monthData || {}
          // // const weekData = monthData.weekData || []
          // this.handCalendarPeriod(weekDatas.filter(date => data.includes(date.timestamp)), isSelect)
          /*
          * 新方法 因为快选数据中不一定是当前年 所以拿全部数据进行对比
          * */
          this.handCalendarPeriod(this.allYearDates.filter(date => data.includes(date.timestamp)), isSelect)
          this.handGetYearData(UNIQUE_SELECT_KEY.PERIOD, this[UNIQUE_SELECT_KEY.PERIOD].year, this[UNIQUE_SELECT_KEY.PERIOD].month)
        }
        // 强行触发computed的更新
        this.$delete(this.selectData, key)
        this.$set(this.selectData, key, currentTabActive)
      },
      handUserFingerMove(position) {
        const key = this.currentTabActive
        const adjustMonth = {
          left: () => {
            this.$refs[`calendarSubsidiaryRef${key}`] && this.$refs[`calendarSubsidiaryRef${key}`].calendarRightToLeftMove()
          },
          right: () => {
            this.$refs[`calendarSubsidiaryRef${key}`] && this.$refs[`calendarSubsidiaryRef${key}`].calendarLeftToRightMove()
          },
        }
        adjustMonth[position] && adjustMonth[position]()
      },
      rowNumChange(rowNum) {
        this.dataBoxHeight = (rowNum + 1) * 40 + 120
        // let maxHeight = 360
        let maxHeight = 295
        if (!this.horizontalPre && this.dataBoxHeight > maxHeight) {
          this.dataBoxHeight = maxHeight
        }
        setTimeout(() => {
          const str = `CalendarResultList${this.currentTabActive}`
          this.$refs[str] && this.$refs[str].refresh()
          this.scrollRefresh()
        })
      },
      scrollRefresh(refName = 'subsidiaryScroll') {
        const str = `calendarSubsidiaryRef${this.currentTabActive}`
        this.$refs[str].$refs.monthSubsidiary.$refs[refName].forceUpdate()
        this.$refs[str].$refs.monthSubsidiary.$refs[refName].refresh()
      },
      handleYearClick(e) {
        this.financialYearData = e
        this.clickYear()
      },
      handleMonthClick() {
        this.clickMonth(this.currentTabActive)
      },
      clearMonth(key, timestamp) {
        this.$refs[`calendarSubsidiaryRef${key}`].clearMonth(timestamp)
      },
      async isExpandValueChange() {
        if (this.isExpand) {
          const list = this.allDataList.current || []
          await this.handCalendarPeriod(list, true, false)
        } else {
          this.clearDataPeriod()
        }
        this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`] && this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].setMonthData()
        this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.PERIOD}`] && this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.PERIOD}`].setMonthData()
      },
      // 设置选中数据
      async selectQuickDate(quickData) {
        quickData.sort((a, b) => b - a)
        if (!this.selectList?.length) {
          await this.getFinancialData()
        }
        // 新方法
        let curSelDataList = this.allYearDates?.filter(date => quickData.includes(date.timestamp)) || []
        const hasAllSelected = curSelDataList?.length === quickData.length
        if (hasAllSelected) {
          this.accordingToYear = false
          this.setCurrentSelData(quickData, true)
        }
        this.$nextTick(() => {
          let calendarSubsidiaryRef = this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`]
          calendarSubsidiaryRef && calendarSubsidiaryRef.setMonthData()
          // hasAllSelected && this.setCurrentSelData(quickData, true)
        })
        return hasAllSelected
      },
      getAllYearDates() {
        let allMonthArr = []
        let allWeekArr = []
        this.selectList && this.selectList.find(item => {
          if (!this.dateAllData[item.id]) return false
          const currDateDataArr = Object.values(this.dateAllData[item.id])
          const monthArr = currDateDataArr.reduce((pre, quart) => { // 全部月份
            return [...pre, ...quart]
          }, [])
          allMonthArr.push(...monthArr)
        })
        const weekDatas = allMonthArr.reduce((pre, cur) => {
          pre = [...pre, ...cur.weekData]
          return pre
        }, [])
        allWeekArr.push(...weekDatas)
        console.log(allWeekArr, '全部数据进行对比')
        return allWeekArr
      },
      switchDate({ year: yearId, month: monthSortNum }) {
        console.log('yearId', yearId)
        console.log('monthSortNum', monthSortNum)
        if (this.accordingToYear) {
          this.clearData()
          this.accordingToYear = false
          this.clearSelectedYear()
        }
        this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`].setMonthData(yearId, monthSortNum, false)
      },
      // 选择整年
      selectYear({ startDate, endDate, calendarYearName, calendarId }, yearId) {
        if (this.accordingToYear) {
          this.currentTabActiveClear()
          this.currentTabActive === UNIQUE_SELECT_KEY.CURRENT && this.$set(this.selectedYear, UNIQUE_SELECT_KEY.PERIOD, [])
        } else {
          this.clearData()
        }
        this.accordingToYear = true
        this.selectedYear[this.currentTabActive] = [{ startDate, endDate, index: 0, value: calendarYearName, id: calendarId }]
        this.yearSelectData = yearId
        this.setYearData()
        // this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`].setMonthData()
        this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`] && this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].setMonthData()
        this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.PERIOD}`] && this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.PERIOD}`].setMonthData()
        this.switchSelectYearSubsidiary(yearId)
      },
      // 获取年上期是拿到下一个数据
      setYearData() {
        Object.entries(this.selectedYear[this.currentTabActive]).forEach(([curYear, data]) => {
          const index = this.selectList.findIndex(item => {
            return item.id === data.id
          })
          const priorData = this.selectList[index + 1] || null
          if (priorData) {
            this.selectedYear[UNIQUE_SELECT_KEY.PERIOD].push({
              startDate: new Date(safariCompliant(priorData.startDate)),
              endDate: new Date(safariCompliant(priorData.endDate)),
              index: index,
              value: priorData.calendarYearName,
              id: priorData.id
            })
          }
        })
      },
      switchSelectYearSubsidiary(yearId) {
        const todayTimestamp = new Date().setHours(0, 0, 0, 0)
        let sortNum = 1
        let absValue = Infinity

        const curYearData = this.selectListSort.find(e => +new Date(safariCompliant(e.startDate)) <= todayTimestamp)
        const monthList = Object.values(this.dateAllData[yearId]).reduce((pre, quarte) => {
          return [...pre, ...quarte]
        }, []).sort((a, b) => {
          // @ts-ignore
          return new Date(safariCompliant(a.startDate)) - new Date(safariCompliant(b.startDate))
        })

        if (curYearData &&
            curYearData.id === yearId &&
            +new Date(safariCompliant(curYearData.startDate)) <= todayTimestamp &&
            +new Date(safariCompliant(curYearData.endDate)) >= todayTimestamp
        ) {
          // 查找对应数据
          monthList.forEach(month => {
            month.weekData.forEach(day => {
              const difference = Math.abs(day.timestamp - todayTimestamp)
              if (difference < absValue && day.timestamp <= todayTimestamp) {
                absValue = difference
                sortNum = month.sortNum
              }
            })
          })
          if (absValue !== Infinity) {
            this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`] && this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`].setMonthData(yearId, sortNum, false)
          }
        } else {
          const yearDetail = monthList[monthList.length - 1]
          this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`] && this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`].setMonthData(yearId, yearDetail.sortNum, false)
        }
      },
      clearSelectedYear() {
        this.selectedYear = {
          [UNIQUE_SELECT_KEY.CURRENT]: [],
          [UNIQUE_SELECT_KEY.PERIOD]: []
        }
      },
      clearSelectedYearByTab(tab = this.currentTabActive) {
        this.selectedYear[tab] = []
      },
      async handClearAll(data) {
        console.log('清除全部', data)
        if (this.accordingToYear && !this.isChangeSubsidiaryQuick) {
          this.clearSelectedYearByTab()
          return
        }
        await this.handUserClearAll(data)
        let calendarSubsidiaryRef = this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`]
        calendarSubsidiaryRef && calendarSubsidiaryRef.clearMonth()
      },
      handClearSingleDate(data) {
        console.log('清除单个', data)
        if (this.curSelTileQuick) {
          this.handClearAll()
          return
        }
        if (this.accordingToYear) {
          this.clearSelectedYearByTab()
          return
        }
        this.handClearDate(data)
        this.$refs[`calendarSubsidiaryRef${this.currentTabActive}`].clearMonthByDay(data.timestamp)
      },
      // 获取数据
      emitUserSelectData() {
        const transformData = (data) => {
          return this.$_flatten(Object.values(data)).map(timestamp => this.$_formatDate(new Date(timestamp)))
        }

        let currentList = []
        let priorList = []
        let currentName = null
        let priorName = null
        let currentId = null
        let priorId = null
        if (this.accordingToYear) {
          const currentData = this.selectedYear[UNIQUE_SELECT_KEY.CURRENT]
          const priorData = this.selectedYear[UNIQUE_SELECT_KEY.PERIOD]
          if (currentData.length) {
            currentName = currentData[0].value || undefined
            currentId = currentData[0].id || undefined
            currentList = transformData([currentData[0].startDate, currentData[0].endDate]).sort()
          }
          if (priorData.length) {
            priorName = priorData[0].value || undefined
            priorId = priorData[0].id || undefined
            priorList = transformData([priorData[0].startDate, priorData[0].endDate]).sort()
          }
        } else {
          currentList = transformData(this.selectData[UNIQUE_SELECT_KEY.CURRENT]).sort()
          priorList = transformData(this.selectData[UNIQUE_SELECT_KEY.PERIOD]).sort()
        }

        const data = Object.assign({
          currentList: currentList,
          priorList: priorList,
          isExpand: this.isExpand,
          calendarCompType: this.accordingToYear ? CALENDARTYPE.Year : CALENDARTYPE.default,
          isSelectQuick: false
        }, this.getIsScatteredType(currentList, priorList))

        if (this.accordingToYear) {
          Object.assign(data, { currentName, priorName, currentId, priorId })
        }

        return data
      },
      // 移动端初始化方法
      async [RESTORE_TYPE.reSetDefault]() {
        this.isExpand = false
        this.clearData()
        this.accordingToYear = false
        this.initTilePickData()
        this.currentTabActive = UNIQUE_SELECT_KEY.CURRENT
        // const date = new Date()
        // this.selectData[UNIQUE_SELECT_KEY.CURRENT][date.getFullYear()] = this.userSetting.selectType === TYPE_LIST.default ? [this.$_formatDate(new Date(date.getTime() - DayTime))] : []
        this.initData()
        // 无快选时默认选择昨天
        if (this.userSetting.isQuick && !this.userSetting.isParticular) {
          await this.getFinancialData()

          // todo 找到最近的日期并选中
          const yesterdayTimestamp = new Date().setHours(0, 0, 0, 0) - DayTime
          let approachingTime = new Date().setHours(0, 0, 0, 0) - DayTime
          let absValue = Infinity

          const curYearData = this.selectListSort.find(e => +new Date(safariCompliant(e.startDate)) <= yesterdayTimestamp)
          if (curYearData) {
            this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].setSelectYearId(curYearData.id)
          } else {
            this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].setSelectData()
          }
          // 全部月份数组
          const currDateDataArr = Object.values(this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`].currDateData) || []
          const monthArr = currDateDataArr.reduce((pre, quarte) => {
            return [...pre, ...quarte]
          }, []).sort((a, b) => {
            // @ts-ignore
            return new Date(safariCompliant(a.startDate)) - new Date(safariCompliant(b.startDate))
          })
          // 判断时间是否在未来时间可选范围内
          // 取最近的时间
          // 查找对应数据
          monthArr.forEach(month => {
            month.weekData.forEach(day => {
              const difference = Math.abs(day.timestamp - yesterdayTimestamp)
              if (difference < absValue && day.timestamp <= yesterdayTimestamp) {
                absValue = difference
                approachingTime = day.timestamp
              }
            })
          })
          if (absValue !== Infinity) {
            this.setSaveSelectData([approachingTime], true, UNIQUE_SELECT_KEY.CURRENT, new Date(approachingTime).getFullYear().toString())
          }
          if (absValue === Infinity && this.futureCalendar) {
            const curRef = this.$refs[`calendarSubsidiaryRef${UNIQUE_SELECT_KEY.CURRENT}`]
            const curYearDetail = this.selectList.find(e => e.id === curRef.selectYearId)
            const month = monthArr.find(e => e.weekData.length)
            if (curYearDetail && month) {
              const year = new Date().getFullYear()
              const dayTimestamp = +new Date(safariCompliant(month.weekData[0].timestamp))
              const lastDayTimestamp = +new Date(`${year + Number(this.futureYear)}/12/31 23:59:59`)
              if (dayTimestamp < lastDayTimestamp) {
                this.setSaveSelectData([dayTimestamp], true, UNIQUE_SELECT_KEY.CURRENT, new Date(dayTimestamp).getFullYear().toString())
              }
            }
          }
          const userData = this.emitUserSelectData()
          this.handClickUserSubmit(TYPE_LIST.default, userData).then(() => {
            this.$refs['sdpPopup'] && this.$refs['sdpPopup'].userCustomDate()
          })
        }
      },
      // 用户操作
      async [RESTORE_TYPE.reSetUserHandle](data) {
        if (this.contentMap.typeSaveData.activeTabs !== TYPE_LIST.default) return
        this.isChangeSubsidiaryQuick = false
        this.currentTabActive = UNIQUE_SELECT_KEY.CURRENT
        const { currentList = [], priorList = [], calendarCompType, currentName, priorName, componentName, currentId, priorId, isExpand } = data
        if (componentName) return
        const currentPeriod = UNIQUE_SELECT_KEY.CURRENT
        const priorPeriod = UNIQUE_SELECT_KEY.PERIOD
        this.isExpand = isExpand || false
        switch (calendarCompType) {
          // 零散
          case CALENDARTYPE.default:
            this.accordingToYear = false
            this.reSetUserScattered(currentPeriod, currentList)
            if (priorList.length) {
              this.reSetUserScattered(priorPeriod, priorList)
            }
            // const date = Math.max(...Object.keys(this.selectData[currentPeriod]).map(e => +e))
            this.initData()
            break
          // 按年
          case CALENDARTYPE.Year:
            this.accordingToYear = true
            await this.getFinancialData()
            const currentYear = this.selectList.find(e => e.id === currentId)
            if (currentYear && currentList.length) {
              this.selectedYear[UNIQUE_SELECT_KEY.CURRENT] = [{
                startDate: new Date(safariCompliant(`${currentList[0]} 00:00:00`)),
                endDate: new Date(safariCompliant(`${currentList[1]} 23:59:59`)),
                index: 0,
                id: currentYear.id,
                value: currentYear.calendarYearName
              }]
              this.yearSelectData = currentYear.id
            }
            const priorYear = this.selectList.find(e => e.id === priorId)
            if (currentYear && priorYear && priorList.length) {
              this.selectedYear[UNIQUE_SELECT_KEY.PERIOD] = [{
                startDate: new Date(safariCompliant(`${priorList[0]} 00:00:00`)),
                endDate: new Date(safariCompliant(`${priorList[1]} 23:59:59`)),
                index: 0,
                id: priorYear.id,
                value: priorYear.calendarYearName
              }]
            }
            this.switchSelectYearSubsidiary(currentYear.id)
            const userData = this.emitUserSelectData()
            this.handClickUserSubmit(TYPE_LIST.default, userData)
            break
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
@import 'packages/base/board/displayPanel/params/utils/variable.scss';
.transparent-style {
  opacity: 0 !important;
}
.mobile-calendar-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 48px;
  font-size: 20px;
  font-weight: bold;
  position: relative;
  color: $color-RLNF;
  & > p {
    flex: 1;
  }
  .header-left {
    position: absolute;
    left: 16px;
  }
  .header-right {
    position: absolute;
    right: 16px;
  }
}
.calendar-mobile {
  z-index: 10;
  background: $color-RLMXBJS !important;
  overflow: hidden;
  height: 100%;
  font-weight: 500;
  position: absolute;
  width: 100%;
  top: 0;
}

.calendar-mobile.is-horizontal {

  /deep/ {
    .tile-select-style {
      &:after {
        display: none;
      }
    }
    .moblie-botton-box {
      box-shadow: unset;
    }
    .subsidiary-mobile {
      height: 100%;
      .sdp-month-subsidiary-box-app {
        height: 100%;
        .cube-scroll-wrapper {
          height: 100% !important;
        }
      }
    }
  }
}
.horizontal-flex {
  display: flex;
  align-content: center;
  justify-content: space-between;
}

/deep/ .calendar-result-list-wrap {
  width: 100%;
  height: 100%;
}

.calendar-quick-btn-wrap {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 8px;
  z-index: 10;

  .sdp-btn-run-m {
    width: calc(100% - 32px);
    height: 44px;
    flex: 1;
    margin: 0 16px;
    background: $color-YXANS !important;
    border-color: $color-YXANS !important;
    border-radius: 2px;
    color: #ffffff !important;
    font-family: NotoSansHans-Regular;
    font-weight: 800;
    font-size: 18px;
  }
  .sdp-btn-run-m:hover{
    background: $color-YXANS !important;
    border-color: $color-YXANS !important;
    color: #ffffff !important;
  }
}
/deep/ .cube-scroll-list-wrapper {
  background: transparent !important;
}
</style>
