<template>
  <div :style="pickHeight" class="calendar-pick-content">
    <cube-scroll ref="scroll" style="width:100%;height:100%;" :style="{ height: showIncludeToday ? 'calc(100% - 40px)' : ''}">
      <div style="padding: 15px;overflow:hidden;width:100%;box-sizing: border-box;">
        <div
          class="select-single"
          :key="item.value"
          :title="`${item[labelType] || item[valueType]}${(week && item[labelType] !== $t('sdp.select.all')) ? $t('sdp.views.Week') : ''}`"
          v-for="(item, index) in data"
          v-show="openShow ? item.show : true"
          @click="handlerClick(item, index)"
        >
          <span :key="!!item[selectedType]" :class="{
            'pick-select':hasIncludeToday ? currentIndex === index : item[selectedType],
            'pick-style': hasIncludeToday ? currentIndex !== index : !item[selectedType],
            'pick-bg': item[selectedType] && setting.needBg,
            'pick-disabled': item.disabled && !getIncludeToday(item.configValue)
          }">
            {{item[labelType] || item[valueType]}}{{(week && item[labelType] !== $t('sdp.select.all')) ? $t('sdp.views.Week') : ''}}
            <span v-if="setting.needIcon && item[selectedType]" class="select-icon"></span>
          </span>
        </div>
      </div>
    </cube-scroll>
    <div class="sdp-quick-checkbox" v-if="showIncludeToday">
      <el-checkbox
        :value="isIncludeToday"
        @change="handleTodayChange"
      >
        {{ $t('sdp.views.includeTodayMobile') }}
      </el-checkbox>
    </div>
    <!--<div class="moblie-botton-box" :style="{bottom : showIncludeToday ? '-108px' : '-72px'}">-->
    <!--  <el-button class="sdp-btn-back" @click="handlerClose">-->
    <!--    <i class="icon sdpiconfont icon-sdp-zuojiantou"></i>-->
    <!--  </el-button>-->
    <!--  <button type="button" class="moblie-botton" @click="handlerOK">-->
    <!--    {{ $t('sdp.views.Run') }}-->
    <!--  </button>-->
    <!--</div>-->
  </div>
</template>

<script>
import { DATE_CONST } from 'packages/base/board/displayPanel/params/paramElement/bussinessCalendar/components/constants'
import { SHOW_INCLUDETO_DAY } from 'packages/base/board/displayPanel/constants'
export default {
  name: 'pick',
  inject: ['commonData'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    openShow: {
      type: Boolean,
      default: false
    },
    isIconQuick: {
      type: Boolean,
      default: false
    },
    option: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Array,
      default: () => ([])
    },
    selectedType: {
      type: String,
      default: 'selected'
    },
    labelType: {
      type: String,
      default: 'label'
    },
    valueType: {
      type: String,
      default: 'value'
    },
    week: {
      type: String,
      default: ''
    },
    hasIncludeToday:{
      type: Boolean,
      default: false
    },
    includeToday: {
      type: Boolean,
      default: false
    },
    includeTodayHistory:{
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    setting() {
      return Object.assign({}, this.defaultSet, this.option)
    },
    firstSelected() {
      return this.data.filter(e => e.show || !Object.hasOwnProperty('show')).findIndex(e => e[this.selectedType])
    },
    pickHeight() {
      return {
        // maxHeight: this.commonData?.isHorizontal ? '220px' : '500px',
        // borderBottomLeftRadius: this.isIconQuick ? '8px' : '',
        // borderBottomRightRadius: this.isIconQuick ? '8px' : '',
        // borderTopLeftRadius: this.isIconQuick ? '' : '8px',
        // borderTopRightRadius: this.isIconQuick ? '' : '8px',
      }
    },
    isIncludeToday(){
      return this.includeToday
    },
    includeTodayValueList() {
      return this.userSetting?.defaultData?.includeTodayValueList || []
    }
  },
  watch: {
    visible(val) {
      this.autoScrollSelected()
    }
  },
  data() {
    return {
      defaultSet: {
        // 单选
        single: false,
        // 是否需要√
        needIcon: true,
        // 是否需要选中背景
        needBg: false,
        // 禁止选择
        disabled: false,
        // 显示默认数据
        default: false,
        // 默认显示月份数据
        month: false,
        // 默认显示年份数据
        year: false,
        // 组件完成自动滑动到选择位置
        autoScroll: true,
      },
      currentItem: null,
      currentIndex: null,
      showIncludeToday: false,
      showIncludeTodayCheckbox: SHOW_INCLUDETO_DAY,
    }
  },
  mounted() {
    this.autoScrollSelected()
  },
  methods: {
    refresh() {
      this.$refs.scroll.refresh()
    },
    handlerClick(item, index) {
      if(this.hasIncludeToday){
        if(item.configValue === DATE_CONST.CustomDatePeriod){
          this.$emit('handlerItemClick', item, index)
        }else{
          this.showIncludeToday = this.showIncludeTodayCheckbox.includes(item.configValue)
          this.$emit('setIncludeToday', item.configValue)
          this.currentItem = item
          this.currentIndex = index
          // this.$emit('handlerItemClick', item, index)
        }
      }else{
        this.$emit('handlerItemClick', item, index)
      }

    },
    handleTodayChange(e) {
      this.$emit('includeTodayChange', this.currentItem.configValue, e)
      // this.handlerOK()
    },
    handlerOK(config){
      this.$emit('handlerItemClick', this.currentItem, this.currentIndex, config)
    },
    handlerClose(){
      this.$emit('handleBack')
    },
    scrollTo(x, y, time, ease) {
      this.$refs.scroll.scrollTo(x, y, time, ease)
    },
    autoScrollSelected() {
      if (!this.visible || !this.setting.autoScroll) return
      this.$nextTick(() => {
        this.$refs.scroll.refresh()
        const index = this.firstSelected < 0 ? 0 : this.firstSelected
        this.scrollTo(0, -15 + (-40 * index + (-10 * (index - 1))), 0)
        this.$refs.scroll.refresh()

        if(this.hasIncludeToday){
          this.data.forEach((e,i)=>{
            if(e.isSelected){
              this.currentItem = e
              this.currentIndex = i
            }
          })
          if (this.currentItem) {
          this.showIncludeToday = this.showIncludeTodayCheckbox.includes(this.currentItem.configValue)
          }
        }
      })
    },
    // 获取类型是否开启包含今天
    getIncludeToday(configValue){
      if(Object.prototype.hasOwnProperty.call(this.includeTodayHistory, configValue)){
        return this.includeTodayHistory[configValue]
      }else if(this.showIncludeTodayCheckbox.includes(configValue)){
        return this.includeTodayValueList.includes(configValue)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import 'packages/base/board/displayPanelMobile/components/variable.scss';
@import 'packages/base/board/displayPanel/params/paramElement/locationNew/css/mobileCommon.scss';
@import 'packages/base/board/displayPanel/params/paramElement/locationNew/css/variable.scss';
.calendar-pick-content {
  //background: #ECECEC;
  height:100%;
  background-color: $color-RLMXBJS;
  .select-single {
    font-family: NotoSansHans-Regular;
    font-size:16px;
    text-align: center;
    width: 100%;
    height: 40px;
    line-height:40px;
    border-radius: 20px;
    overflow: hidden;
    color: var(--sdp-color-KXWXZWZ);
    background: var(--sdp-color-RLKXSKBJ);

    &:nth-child(n+2) {
      margin-top: 10px;
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      font-family: Source Han Sans;
      font-size: 12px;
      font-weight: normal;
      text-align: center;
      letter-spacing: 0em;

      color: var(--sdp-color-KXWXZWZ);
    }

    .pick-select {
      height: 40px;
      line-height: 40px;
      display: inline-block;
      width: 100%;
      font-weight: 500;
      color: #FFFFFF;
      background: var(--sdp-color-ZS2);
    }
    .pick-style {
      height: 40px;
      line-height: 40px;
      font-weight: 400;
      display: inline-block;
      width: 100%;
    }
    .pick-select:active,.pick-style:active {
      background-color: $color-KBDBTBJH;
    }

    .select-icon {
      position: absolute;
      right: 14px;
      color: $color-AYFYJTGL;
    }
    .select-icon::before {
      font-family: "sdpiconfont" !important;
      content: "\E980";
    }
    .pick-bg {
      background: $color-KBDBTBJH;
    }
    .pick-disabled {
      cursor: not-allowed;
      opacity: .6;
      color: var(--sdp-jys) !important;
    }
  }
}

//.moblie-botton-box {
//  position: static !important;
//  background: $color-RLMXBJS !important;
//  border-color: $color-RLMXBJS !important;
//  .moblie-botton {
//    background: $color-YXANS !important;
//  }
//}

.sdp-quick-checkbox{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    width: 100%;
    margin: 0 auto;
    position: relative;
    color: #333333;
    font-family: NotoSansHans-Regular;
    background-color: var(--sdp-color-SLCBJ);
    /deep/ .el-checkbox{
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      .el-checkbox__label{
        padding-right: 10px;
      }
    }
    /deep/ .el-checkbox__label {
      font-size: 12px;
      color: var(--sdp-cszjsz-wzs1) !important;
    }
    /deep/ .el-checkbox__inner:not(.is-checked) {
      // background-color: var(--sdp-cszjdx0) !important;
      // border: 1px solid var(--sdp-cszjdxbk) !important;
    }
    /deep/ .is-checked .el-checkbox__inner {
      background-color: var(--sdp-cszjdxtc) !important;
      border-color: var(--sdp-cszjdxtc) !important;
    }
    /deep/ .is-checked .el-checkbox__inner::after {
      border-color: var(--sdp-cszjdxg) !important;
    }
    /deep/ .el-checkbox__input .el-checkbox__inner:hover {
      border-color: #606266;
    }
    /deep/ .el-checkbox__input .el-checkbox__inner:focus {
      border-color: #606266;
    }
  }
</style>
