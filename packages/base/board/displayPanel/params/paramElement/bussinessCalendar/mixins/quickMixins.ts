import SdpQuickDays from '../components/SdpQuickDays'

import {QUICK_SET_DATA, TYPE_PARAM_ELEMENT} from "../../../utils/constants";
import {orderFilterList, orderSelectList, transData} from "../../dateQuickOperation/utils";
import {DATE_CONST} from "../components/constants";
import EventData from "../../../../../../../assets/EventData";

export default {
  components: {
    SdpQuickDays,
  },
  data() {
    return {
      quickParam: {
        bindElement: [],
        dataSets: [],
        selectList: []
      },
      selectList: [
        { id: 1, value: QUICK_SET_DATA.ALL, label: this.$t('sdp.select.all') },
        { id: 2, value: 'week', label: this.$t('sdp.views.Week') },
        { id: 3, value: 'month', label: this.$t('sdp.views.Month') },
        { id: 4, value: 'year', label: this.$t('sdp.views.Year') },
        { id: 5, value: 'quator', label: this.$t('sdp.views.LastSixMonths') },
      ],
      quickList: []
    }
  },
  computed: {
    activePanel() {
      return this.board.paramsPanelList.find(e => e.active)
      // return this.editParamList
    },
    isLabelDisabled() {
      const isLocationQuick = this.editParamList.some(el => [TYPE_PARAM_ELEMENT.LOCATION_NEW].includes(el.type) && el.content?.labelPickerConfig?.isLabelPicker)

      return isLocationQuick || !!this.editParamList.find(e => e.id !== this.settingParam.id && e.type === TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR && e.content.userSetting.isQuickLabel)
    },
    labelQuick() {
      if (!this.activePanel) return false
      return this.activePanel.content.find(e => e.type === TYPE_PARAM_ELEMENT.CALENDAR_QUICK)
    },
    selectListShow () {
      const list = this.quickParam.selectList || []
      if (list.length === 0) return this.$t('sdp.placeholder.plsInput')
      if (list.length >= 2) return this.$t('sdp.views.multiple')
      if (list[0] === 'All') return this.$t('sdp.select.all')

      const target = this.quickList.find(e => e.configValue === list[0])
      return target ? this.renderTxt(target.configValue, target.remark || target.configValue) : ''
    },
  },
  methods: {
    quickInit() {
      const quick = this.labelQuick || { content: {} }
      const { selectList = [], orderList = [] } = quick.content

      let selectListTmp = selectList
      const data = transData(selectListTmp.length ? selectListTmp : undefined, this.tenantData.quickCalenderConfig, '2', '1', true, false)

      if (this.labelQuick) {
        this.$set(this, 'quickParam', this.$_deepClone(quick.content))
        !this.isLabelDisabled && (this.isQuickLabel = true)
      } else {
        selectListTmp = data.fixList
        this.$set(this.quickParam, 'selectList', selectListTmp)
        this.$set(this.quickParam, 'lastQuickDays', 14)
        this.$set(this.quickParam, 'nextQuickDays', 14)
        this.$set(this.quickParam, 'lastNDays', 7)
        this.$set(this.quickParam, 'nextNDays', 15)
      }

      let olderList = orderList || data.quickList
      let list = []
      if (olderList) {
        olderList.forEach(item => {
          const obj = data.quickList.find(a => a.configValue === item.configValue)
          obj && (list.push(obj))
        })
      } else {
        list = data.quickList
      }
      olderList = list

      this.quickList = orderFilterList(data.quickList, olderList)
      this.$set(this.quickParam, 'selectList', data.fixList)
      this.quickList = orderSelectList(selectList, this.quickList)
    },
    visibleChange() {
      const selectList = this.quickParam.selectList
      this.quickList = orderSelectList(selectList, this.quickList)
    },
    selectListCheck(old, val) {
      const check = this.quickParam.selectList.some(e => e === 'All')

      if (check) {
        if (this.quickParam.selectList.length === 2) {
          this.$set(this.quickParam, 'selectList', [this.quickParam.selectList[1]])
        } else {
          this.$set(this.quickParam, 'selectList', ['All'])
        }
      } else {
        if (this.quickParam.selectList.length === this.quickList.length) {
          this.$set(this.quickParam, 'selectList', ['All'])
        }
      }
      const selectList = this.quickParam.selectList
      this.quickList = orderSelectList(selectList, this.quickList)
    },
    renderTxt(quickClauses, val) {
      if ([DATE_CONST.LastToNext].includes(quickClauses)) {
        let { lastNDays, nextNDays } = this.settingParam.content
        let options = val.split(' n ')
        options.splice(1, 0, ` ${lastNDays} `)
        options.splice(3, 0, ` ${nextNDays} `)
        return options.join('')
      } else if ([DATE_CONST.LastNDays, DATE_CONST.NextNDays].includes(quickClauses)) {
        let field = quickClauses.includes(DATE_CONST.LastNDays) ? 'lastQuickDays' : 'nextQuickDays'
        let quickDays = this.settingParam.content?.[field]
        return val.replace(' n ', ` ${quickDays} `)
      }
      return val
    },
    quickConfirm(contentMap) {
      // 禁用则直接return
      if (this.isLabelDisabled) return

      this.quickParam.dataSets = this.$_deepClone(contentMap.dataSets)
      this.quickParam.bindElement = this.$_deepClone(contentMap.bindElement)

      if (this.labelQuick) {
        // 如果依然开启，则修改
        if (contentMap.userSetting.isQuickLabel) {
          Object.keys(this.quickParam).forEach(key => {
            this.$set(this.labelQuick.content, key, this.$_deepClone(this.quickParam[key]))
          })
          this.callEventBus({ data: this.labelQuick, targetFn: 'onCommitChanges', target: 'tab' })
        } else {
          // 如果关闭，则移除组件
          this.callEventBus({ data: this.labelQuick, targetFn: 'removeParams', target: 'tab' })
        }
      } else {
        // 如果开启，则新增
        if (contentMap.userSetting.isQuickLabel) {
          this.callEventBus({
            data: TYPE_PARAM_ELEMENT.CALENDAR_QUICK,
            targetFn: 'addParams',
            target: 'tab',
            callback: (newParam) => {
              Object.assign(newParam.content, this.$_deepClone(this.quickParam))
            }
          })
        }
      }
    },
    callEventBus({ data, targetFn, target, callback }) {
      const eventData = new EventData({
        source: 'BussinessCalendarLabel',
        options: {
          callback
        },
        data,
        target,
        targetFn,
      })
      this.$emit('eventBus', eventData)
    },
  }
}
