import paramGangedMixin from 'packages/base/board/displayPanel/params/paramElement/paramGangedMixin'
import locationStatusMixin from 'packages/base/board/displayPanel/params/paramElement/locationStatusMixin'
import updateParamsMixin from 'packages/base/board/displayPanel/params/paramElement/updateParamsMixin'
import { EVENT_BUS, TYPE_SUPERSETTING } from '../../../../constants'
import { SelectOrdinary } from '../bridge'
import { CONST_DATA, FINISH_TYPE, NUMBER_FORMAT, PARAMETER_TYPE, SCOPE_TYPE } from '../../../utils/constants'
import { formatterGranularity } from '../../../utils/utils'
export default {
  inject: ['utils', 'levelData', 'sdpBus', 'authority', 'commonData', 'langCode', 'tenantData'],
  mixins: [paramGangedMixin, locationStatusMixin, updateParamsMixin],
  props: {
    paramElement: {
      type: Object,
      default: () => ({}),
    },
    board: {
      type: Object,
      default: () => ({}),
    },
    boardInfo: {
      type: Object,
      default: () => ({}),
    },
    beforeHook: {
      type: Function
    },
  },
  data() {
    return {
      selectOrdinary: null,
      optionData: [],
      selGranularityList: [],
      oldBoardSlectLang: '', // 预览记录语言
      superLinkData: {}
    }
  },
  computed: {
    oldLanguageCode: {
      get() {
        return this.contentMap.oldLanguageCode || this.languageCode
      },
      set(val) {
        this.$set(this.contentMap, 'oldLanguageCode', val)
      }
    },
    // 接口
    api() {
      return this.utils.api || function () {}
    },
    // 语言
    languageCode() {
      return this.boardInfo.languageCode
    },
    datasetPermission() {
      return this.authority.datasetPermission
    },
    // 是否懒加载
    isLazyLoading() {
      return this.levelData.isLazyLoading
    },
    // 是否单选
    isSingle() {
      return this.paramElement.content.single
    },
    isMobile() {
      return this.utils.isMobile
    },
    contentMap() {
      return this.paramElement.content
    },
    languageConfig() {
      return this.paramElement.content.languageConfig || []
    },
    boardSlectLang() {
      return this.commonData.isPreview ? this.commonData.boardSlectLang() : ''
    },
    enableParameters() {
      return this.contentMap.enableParameters
    },
  },
  created() {
    this.selectOrdinary = new SelectOrdinary({
      vm: this,
      isSync: false,
    })
  },
  mounted() {
    this.sdpBus.$on(EVENT_BUS.RUN_SWITCH_LANG, this.runSwitchLang)
    this.paramElementCompatibility()
    const { superLinkData } = this.$_JSONClone(this.beforeHook(this.paramElement))
    this.superLinkData = superLinkData
  },
  destroyed() {
    this.sdpBus.$off(EVENT_BUS.RUN_SWITCH_LANG, this.runSwitchLang)
  },
  watch: {
    // 语言切换
    languageCode(val) {
      this.languageCodeReplace(val)
    },
    'contentMap.selectOrdinaryArr': {
      handler(val) {
        this.setSelectOrdinaryArrLang()
        this.$set(this.contentMap, 'selectOrdinaryArrLang', Array.isArray(val) ? val.map(this.languageChange) : val)
        let selectValues = this.changeSelValues(val)
        this.$set(this.contentMap, 'selectValues', selectValues)
      },
      deep: true,
      immediate: true
    },
    boardSlectLang(val, old) {
      if (!this.enableParameters) return
      this.oldBoardSlectLang = old
      this.languageCodeReplace(val)
      let selectValues = this.changeSelValues(this.contentMap.selectOrdinaryArr)
      this.$set(this.contentMap, 'selectValues', selectValues)
    },
    // 监听location组件状态变化
    locationState({ status }, { status: oldStatus } = { status: false }) {
      // 是否调用完成
      if (status !== oldStatus && this.isCallFinishParamGanged) {
        this.finishHook(FINISH_TYPE.FINISH)
      }
    },
    'commonData.isPreview'() {
      this.setSelectOrdinaryArrLang()
    },
    'tenantData.globalParameterList.length': {
      handler() {
        this.verifyGranularity(true)
      },
      // immediate: true
    }
  },
  methods: {
    // 兼容方法，调整paramElement
    paramElementCompatibility() {
      const content = this.paramElement.content
      if (content.single && content.isSelectAll) {
        this.$set(content, 'isSelectAll', false)
      }
    },
    runSwitchLang() {
      // 切换没有异步处理，该调用如果有异步处理会产生bug
      this.selectOrdinary.languageCodeReplace(this.contentMap, this.boardInfo.languageCode)
    },
    setSelectOrdinaryArrLang() {
      const selectOrdinaryArr = this.contentMap.selectOrdinaryArr
      const bool = this.commonData.isPreview

      const selectOrdinaryArrLang = bool
        ? Array.isArray(selectOrdinaryArr) ? selectOrdinaryArr.map(this.languageChange)
        : typeof selectOrdinaryArr === 'string' ? this.languageChange(selectOrdinaryArr) : ''
        : ''

      this.$set(this.contentMap, 'selectOrdinaryArrLang', selectOrdinaryArrLang)
    },
    languageChange(val) {
      if (this.enableParameters || !this.selectOrdinary?.optionData?.length) return val
      let data = this.selectOrdinary.optionData.find(item => item.name === val)
      if (!data) return val
      let languageData = this.languageConfig.find(item => item.code === data.code)
      if (!this.boardSlectLang || !languageData) {
        return languageData?.name || val
      }
      let currentData = languageData.languageConfig.find(item => item.languageCode === this.boardSlectLang)
      return currentData?.i18nValue || val
    },
    languageCodeReplace(languageCode) {
      const isAwait = this.finishParams[this.paramElement.id] === FINISH_TYPE.AWAIT
      isAwait || this.finishHook(FINISH_TYPE.AWAIT)

      this.selectOrdinary.languageCodeReplace(this.contentMap, languageCode)
      // 切换语言需要触发完成方法
      isAwait || this.finishHook(FINISH_TYPE.FINISH)
    },
    // 全局参数判断
    verifyGranularity(isInit = false) { // 拿到全局参数的数据判断后走初始化
      const { enableParameters, selectGranularity, isFollowGlobalParamsVal } = this.contentMap
      if (enableParameters && this.tenantData?.globalParameterList?.length) {
        selectGranularity.forEach((item, index) => { // 设置最新的全局参数
          let curItem = this.tenantData?.globalParameterList.find(parameter => parameter.id === item.id)
          if (!curItem) return true
          selectGranularity[index] = this.$_JSONClone(curItem)
          isFollowGlobalParamsVal && this.$set(this.contentMap, 'defaultSelect', curItem?.defaultValue)
        })
        this.selGranularityList = this.getSelGranularity()
        if (!this.selGranularityList?.length) return true
        isInit && this.restoreData()
      }
      return false
    },
    getSelGranularity() {
      if (!this.enableParameters || !this.contentMap?.selectGranularity?.length) return []
      let curItem = this.contentMap.selectGranularity[0]
      return formatterGranularity(curItem, 'originDefaultValue')
    },
    changeSelValues(val) {
      if (!this.contentMap?.enableParameters || !val || !val?.length) {
        return val
      }
      if (!this?.selGranularityList?.length) {
        this.selGranularityList = this.getSelGranularity()
      }
      let selectValues = []
      val.forEach(el => {
        let curVal = this.selGranularityList.find(item => {
          return (this.boardSlectLang && item?.lan?.find(e => e?.key === this.boardSlectLang && e?.value === el)) || item?.value === el
        })?.value
        selectValues.push(curVal)
      })
      return selectValues
    },
    restoreData() {
      this.selectOrdinary[TYPE_SUPERSETTING.INIT](this.contentMap, this.languageCode)
    },
    getParams() {
      return this.selectOrdinary.getRequestData(this.contentMap)
    },
    // 类调用组件状态
    setComponentStatus() {
      const data = this.paramElement.content.selectOrdinaryArr
      this.isMobile ? this.setComponentStatusMobile(data) : this.setComponentStatusPc(data)
    },
    setChangeData() {},
    // 完成钩子
    finishHook(type) {
      this.selectOrdinary.childFinishHook(type)
    },
    updateFromContent() {
      this.setComponentStatus()
    }
  },
}
