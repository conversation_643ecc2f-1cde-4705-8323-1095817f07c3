<template>
  <div
    class="select-type"
    ref="box"
    :style="{
      'pointer-events': isDisable ? 'none' : 'auto'
    }"
  >
    <param-element-title v-bind="{
        txt: paramElement.elName,
        elType: paramElement.type,
        txtStyle: paramElement.elNameStyle,
        isHideElement: paramElement.isHideElement
      }"></param-element-title>
    <el-popover
      placement="bottom-start"
      width="680"
      @hide="() => {
        searchSelectClear()
        saveData(true)
      }"
      @show="saveData(false)"
      :popper-class="`sdp-tag-caldendar ${getCurrentThemeClass()}`"
      v-clickoutsides
      trigger="click">
      <div v-loading="isloading" element-loading-spinner="sdp-loading-gif" v-if="!options||!options.length" class="nodata-style">{{$t('sdp.views.noData')}}</div>
      <div v-if="options&&options.length">
        <div class="sdp-tag-header">
          <div class="sdp-tag-title">
            {{$t('sdp.views.matchingMethod')}}:
          </div>
          <div class="sdp-tag-group">
            <span style="margin-right: 16px;">{{$t('sdp.views.SameGroup')}}</span>
            <el-radio-group v-model="paramElement.content.sameGroup">
              <el-radio :label="SameGroupType.ALL">{{ $t('sdp.views.MatchAll') }}</el-radio>
              <el-radio :label="SameGroupType.ANY">{{ $t('sdp.views.MatchAny') }}</el-radio>
            </el-radio-group>
            <span v-show="isTagGroup" class="line"></span>
            <span v-show="isTagGroup" style="margin-right: 16px;">{{$t('sdp.views.BetweenGroup')}}</span>
            <el-radio-group v-show="isTagGroup" v-model="paramElement.content.betweenGroup">
              <el-radio :label="BetweenGroupType.ALL">{{ $t('sdp.views.MatchAll') }}</el-radio>
              <el-radio :label="BetweenGroupType.ANY">{{ $t('sdp.views.MatchAny') }}</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="sdp-tag-content">
          <div class="sdp-tag-content-left">
            <div class="sdp-tag-content-left-head">
              <div class="sdp-tag-content-head-title">
                {{ $t('sdp.views.tagUnselected') }}
              </div>
              <el-select
                class="sdp-select"
                v-if="tagGroup !== ''"
                v-model="activeName"
                :popper-class="`sdp-tag-select ${getCurrentThemeClass()}`"
                @change="handleClick"
              >
                <el-option
                  v-for="(item, i) of options"
                  :key="i"
                  :label="item[tagGroup]"
                  :value="i + ''"
                />
              </el-select>
            </div>
            <div style="padding: 16px 0;">
              <div style="padding: 0 16px;">
                <div style="position: relative;">
                  <el-input
                    :placeholder="$t('sdp.views.PleasetagName')"
                    v-model="searchInput"
                    prefix-icon="el-icon-search"
                    class="search-style search-input"
                    style="width: 100%;margin-bottom: 12px;"
                  >
                  </el-input>
                  <i class="icon-sdp-shanchucanshuzujian search-clear" @click="searchClear()"></i>
                </div>
              </div>

              <div style="padding: 0 16px">
                <el-checkbox
                  v-if="isTagGroup"
                  class="checkbox-style"
                  v-model="options[activeName].all"
                  @change="selectAll(options[activeName])"
                >{{$t('sdp.select.allCapital')}}</el-checkbox>
                <el-checkbox
                  v-else
                  class="checkbox-style"
                  v-model="noGroupAll"
                  @change="selectAll()"
                >{{$t('sdp.select.allCapital')}}</el-checkbox>
              </div>

              <el-scrollbar :style="{ height: `${ 173 + (isTagGroup ? 0 : 24) }px`}" ref="scrollbarSelect" v-sdp-el-scrollbar>
                <div style="overflow:hidden;padding: 0 16px">
                  <div v-for="(item, index) of (isTagGroup ? options[activeName].extendData : options)" :key="index">
                    <div v-if="hiddenTag(item[tagName])" class="tag-style" :class="item.show?'tag-style-select':''" @click="selectTag(isTagGroup ? options[activeName] : item, isTagGroup ? item : undefined)">
                      {{languageChangeWithAll(item[tagName])}}
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
          <div class="sdp-tag-content-right">
            <div class="sdp-tag-content-right-head">
              <div class="sdp-tag-content-head-title">
                {{ $t('sdp.views.Selected') }}
              </div>

              <div class="select-title">
                <span class="clear-title" @click="clearAll()">{{$t('sdp.views.clear')}}</span>
              </div>
            </div>

            <div style="padding: 16px 0;">
              <div style="padding: 0 16px">
                <div style="position: relative; ">
                  <el-input
                    :placeholder="$t('sdp.views.PleasetagName')"
                    v-model="searchSelect"
                    class="search-style search-input"
                    prefix-icon="el-icon-search"
                    style="width: 100%;margin-bottom: 12px;"
                  >
                  </el-input>
                  <i class="icon-sdp-shanchucanshuzujian search-clear" @click="searchSelectClear()"></i>
                </div>
              </div>

              <el-scrollbar :style="{ height: `${ 221 - (isTagGroup ? 0 : 26) }px`}" ref="scrollbarShow" v-sdp-el-scrollbar>
                <div id="group-contetn" style="padding: 0px 16px;">
                  <div v-for="(item, i) of selectList" :key="i" v-show="(isTagGroup ? getNameById(item.groupType).all : noGroupAll) ? $t('sdp.select.allCapital').includes(searchSelect) : item.list.some(e => e.name.includes(searchSelect))" style="overflow:hidden;" class="select-content">
                    <div v-if="isTagGroup && item.list.length" class="tag-title">{{item.groupType}} <span class="clear-title" @click="clearOptionsAll(item.groupType)">{{$t('sdp.views.clear')}}</span></div>
                    <div v-show="$t('sdp.select.allCapital').includes(searchSelect)" v-if="isTagGroup ? getNameById(item.groupType).all : noGroupAll" class="tag-style">
                        {{$t('sdp.select.allCapital')}}
                        <span @click="clearOptionsAll(item.groupType)" class='icon-sdp-shanchucanshuzujian clear-ico-style'></span>
                    </div>
                    <template v-else>
                        <div v-for="(child, index) of item.list" v-show="child.name.includes(searchSelect)" :key="index" class="tag-style">
                          {{languageChangeWithAll(child.name)}}
                          <span @click="clearOptions(item.groupType, child)" class='icon-sdp-shanchucanshuzujian clear-ico-style'></span>
                        </div>
                    </template>
                  </div>
                </div>
              </el-scrollbar>
            </div>

          </div>
        </div>

        <!-- <div style="height: 400px;" class="group" v-if="tagGroup !== ''">
          <div class="select-title" style="padding-bottom:8px;">
            {{$t('sdp.views.Selected')}}
            <span class="clear-title" @click="clearAll()">{{$t('sdp.views.clear')}}</span>
          </div>
          <el-scrollbar :style="heightStyle" ref="scrollbarShow" v-sdp-el-scrollbar class="tab-border-bottom">
            <div id="group-contetn">
              <div v-for="(item, i) of selectList" :key="i" style="overflow:hidden;width:100%;padding-bottom:8px;" class="select-content">
                <div v-if="item.list.length" class="tag-title">{{item.groupType}}</div>
                <div v-if="getNameById(item.groupType).all">
                  <div class="tag-style">
                    {{$t('sdp.select.allCapital')}}
                    <span @click="clearOptionsAll(item.groupType, child)" class='icon-sdp-shanchucanshuzujian clear-ico-style'></span>
                  </div>
                </div>
                <div v-else>
                  <div v-for="(child, index) of item.list" :key="index" class="tag-style">
                    {{languageChangeWithAll(child.name)}}
                    <span @click="clearOptions(item.groupType, child)" class='icon-sdp-shanchucanshuzujian clear-ico-style'></span>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
          <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="tab-style">
            <el-tab-pane :label="item[tagGroup]" v-for="(item, i) of options" :key="i" :name="i + ''">
              <div style="overflow:hidden;margin-bottom:10px;">
                <div style="display: inline-block">
                  <el-checkbox
                    class="checkbox-style setting-style"
                    v-model="item.all"
                    @change="selectAll(item)"
                  >{{$t('sdp.select.allCapital')}}</el-checkbox>
                </div>
                <el-input
                  :placeholder="$t('sdp.views.sousuo')"
                  v-model="searchInput"
                  class="search-style search-input"
                  style="width: 200px;"
                  @keyup.enter.native="setSearchString()"
                >
                </el-input>
                <i class="el-icon-search" @click="setSearchString()"></i>
                <i v-if="searchInput" class="icon-sdp-shanchucanshuzujian search-clear" @click="searchClear()"></i>
              </div>
              <el-scrollbar :style="heighTabtStyle" ref="scrollbarSelect" v-sdp-el-scrollbar>
                <div style="overflow:hidden;">
                  <div v-for="(child, index) of item.extendData" :key="index">
                    <div v-if="hiddenTag(child[tagName])" class="tag-style" :class="child.show?'tag-style-select':''" @click="selectTag(item, child)">
                      {{languageChangeWithAll(child[tagName])}}
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="no-group" v-else>
          <div class="select-title">
            {{$t('sdp.views.Selected')}}
            <span class="clear-title" @click="clearAll()">{{$t('sdp.views.clear')}}</span>
          </div>
          <el-scrollbar :style="heightStyle" ref="scrollbarShow" v-sdp-el-scrollbar class="tab-border-bottom" style="margin-top:10px;">
            <div id="no-group-contetn">
              <div v-for="(item, i) of selectList" :key="i" style="overflow:hidden;width:100%;">
                <div v-if="noGroupAll">
                  <div class="tag-style">
                    {{$t('sdp.select.allCapital')}}
                    <span @click="clearOptionsAll(item.groupType, child)" class='icon-sdp-shanchucanshuzujian clear-ico-style'></span>
                  </div>
                </div>
                <div v-else>
                  <div v-for="(child, index) of item.list" :key="index" class="tag-style">
                    {{languageChangeWithAll(child.name)}}
                    <span @click="clearOptions(item, child)" class='icon-sdp-shanchucanshuzujian clear-ico-style'></span>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
          <div style="overflow:hidden;margin-bottom: 10px;margin-top:12px;position:relative;">
            <div style="display: inline-block">
              <el-checkbox
                class="checkbox-style setting-style"
                v-model="noGroupAll"
                @change="selectAll()"
              >{{$t('sdp.select.allCapital')}}</el-checkbox>
            </div>
            <el-input
              :placeholder="$t('sdp.views.sousuo')"
              v-model="searchInput"
              class="search-style search-input"
              style="width: 200px;"
              @keyup.enter.native="setSearchString()"
            >
            </el-input>
            <i class="el-icon-search" @click="setSearchString()"></i>
            <i class="icon-sdp-shanchucanshuzujian search-clear" @click="setSearchString()"></i>
          </div>
          <el-scrollbar :style="heighTabtStyle" ref="scrollbarSelect" v-sdp-el-scrollbar>
            <div style="overflow:hidden;">
              <div v-for="(item, index) of options" :key="index">
                <div v-if="hiddenTag(item[tagName])" class="tag-style" :class="item.show?'tag-style-select':''" @click="selectTag(item)">
                  {{languageChangeWithAll(item[tagName])}}
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div> -->
      </div>
      <div class="like-input sdp-params-theme-background" :class="disableShow?'disable-style':''" slot="reference" @click="previewData()" :title="languageChangeWithAll(paramElementName)">
        {{languageChangeWithAll(paramElementName)}}
        <span class="select-down el-icon-arrow-down" :class="disableShow?'disable-style':''"></span>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { TYPE_PARAM_ELEMENT, FINISH_TYPE } from '../../utils/constants'
import {initializationData, CONSTANTS, adapter, BetweenGroupType} from './bridge'
import { getAllDataSetIds } from '../../../constants'
import index_mixins from './index_mixins'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import { STATIC_BASE_PATH } from 'packages/assets/constant'
import ParamElementTitle from '../../components/ParamElementTitle'
import clickoutsides from 'packages/directives/clickoutside'
export default {
  components: { ParamElementTitle },
  inject: {
    utils: { default: {} },
    commonData: { default: {} },
    themeData: { default: {} },
    getCurrentThemeClass: { default: () => () => '' }
  },
  directives: {
    clickoutsides,
    SdpElScrollbar,
  },
  mixins: [index_mixins],
  created () {
    // if (this.tagFields.length) {
    //   this.getSelectData()
    // }
    // const skpiName = this.board.planTask()
    //   ? SKPINAME.SAVE_USER_OPERATION
    //   : SKPINAME.INIT_DATA
  },

  props: {
    paramElement: {
      type: Object,
      default: () => ({}),
    },
    board: {
      type: Object,
      default: () => ({}),
    },
    beforeHook: {
      type: Function
    },
    boardInfo: {
      type: Object,
      default: () => ({}),
    },
  },

  data () {
    return {
      loadingImg: `${STATIC_BASE_PATH.images}sdp-loading.gif`,
      superLinkData: '',
      searchSelect: '',
    }
  },
  watch: {
    searchInput() {
      this.setSearchString()
    }
  },
  computed: {
    BetweenGroupType() {
      return BetweenGroupType
    },
    // 请求封装
    api () {
      return this.utils.api || function () { }
    },
    isTagGroup() {
      return this.tagGroup !== ''
    }
  },
  mounted () {
    // this.finishHook(FINISH_TYPE.AWAIT)
    // const { data, type, superLinkData } = this.beforeHook(this.paramElement)
    // this.superLinkData = superLinkData
    // initializationData[type].call(this, data)
  },
  methods: {
    hiddenTag(name) {
      let oldString = this.searchString.toUpperCase()
      let oldname = name.toUpperCase()
      return oldname.includes(oldString)
    },
    handleClick() {
      this.$set(this, 'searchString', '')
      this.searchSelect = ''
    },
    searchSelectClear() {
      this.searchSelect = ''
    },
    // 看板元素数据集发生变化时
    setDatasStatus() {
      // let status = false
      // const content = this.paramElement.content
      // let arr = getAllDataSetIds(content.options.bindElements, this.board.elList)
      // const tagList = content.options.tagFields.filter(item => arr.indexOf(item.dataSetId) !== -1)
      // this.$set(this.paramElement.content.options, 'tagFields', tagList)
      // if (!tagList.length) {
      //   this.$set(this.paramElement.content, 'notReset', true)
      // } else {
      //   initializationData.userState.call(this)
      // }
    },
  }
}
</script>
<style lang="scss">
.sdp-tag-caldendar {
  border: none !important;
  padding: 0 !important;
  outline: none !important;
  border-radius: 8px;
  background-color: transparent;
  box-sizing: border-box;
}
.sdp-tag-select {
  .selected {
    color: var(--sdp-zjbxx-wzs) !important;
  }
}
</style>
<style lang="scss" media="screen" scoped>
@import "../style/paramcommponents.scss";
@import './variable.scss';

.sdp-tag-header {
  padding: 16px;
  color:var(--sdp-xxbt2);
  .sdp-tag-title {
    height: 16px; line-height: 16px;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  .sdp-tag-group {
    margin-top: 10px; height: 16px; line-height: 16px;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    .line {
      margin: 0 24px;
      position: relative;
      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 1px;
        height: 14px;
        background: #DCDFE6;
        top: 2px;
        left: 0;
      }
    }
    /deep/ .el-radio__label {
      color: var(--sdp-zjbxx-wzs) !important;
    }
    /deep/.is-checked .el-radio__inner {
      background: var(--sdp-cszj-onns) !important;
      border-color: var(--sdp-cszj-onns) !important;
    }
    /deep/ .el-radio__inner::after {
      background-color: transparent;
    }
  }
}
.sdp-tag-content {
  border-top: 1px solid var(--sdp-cszj-bkfgx); padding: 16px; display: flex; gap: 16px;
  .sdp-tag-content-left, .sdp-tag-content-right {
    flex: 1; height: 335px; border: 1px solid var(--sdp-cszj-bkfgx);
  }
  .sdp-tag-content-left-head, .sdp-tag-content-right-head {
    padding: 12px 16px;  border-bottom: 1px solid var(--sdp-cszj-bkfgx);background-color: var(--sdp-cszj-bjds);
    .sdp-tag-content-head-title {
      font-size: 12px;height: 12px; line-height: 12px;
    }
  }

  .sdp-tag-content-right-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.sdp-select {
  font-size: 12px;
  margin-top: 8px;
  width: 100%;
  background-color: var(--sdp-szk-bjs);
  /deep/ .el-input__inner {
    height: 24px;
    border: 1px solid var(--sdp-cszj-bkfgx) !important;
    border-radius: 2px;
    color: var(--sdp-xxbt2) !important;
  }
  /deep/ .el-input--small .el-input__icon {
    line-height: 24px;
  }
  /deep/ .el-input__icon {
    color: var(--sdp-xlk-jts) !important;
  }
}

.search-style {
  display: inline-block;
  /deep/ input {
    height: 24px;
    line-height: 24px;
    border-radius: 2px;
    text-indent: 2px;
    padding-left: 23px;
  }
}
.search-style:hover ~ .search-clear {
  display: block;
}

.search-input {
  /deep/ .el-input__prefix {
    .el-input__icon {
      height: 24px;
      width: 20px;
      line-height: 24px;
      color: rgba(117, 135, 162, 1);
    }
  }
  /deep/ .el-input__inner {
    background-color: var(--sdp-fs1);
    border-color: var(--sdp-srk-bks);
    &:hover,&:active,&:focus{
      border-color: var(--sdp-srk-bks) !important;
    }
  }
}

.checkbox-style {
  height: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  /deep/ .el-checkbox__input {
    &:not(.is-disabled).is-checked .el-checkbox__inner {
      background-color: var(--sdp-cszjdxtc);
      border-color: var(--sdp-cszjdxtc);
    }
    .el-checkbox__label {
      height: 12px;
      line-height: 12px;
    }
    &.is-checked + .el-checkbox__label {
      color: var(--sdp-cszjsz-wzs1) !important;
    }
    .el-checkbox__inner {
      width: 12px;
      height: 12px;
    }
    .el-checkbox__inner::after {
      height: 6px;
      top: 1px;
      left: 3px;
    }
    &.is-checked .el-checkbox__inner::after{
      border-color: var(--sdp-cszjdxg);
    }
  }
}

.tag-style {
  background-color: var(--sdp-fs1);
  border: 1px solid var(--sdp-ycsz-srk-bcs);
  padding: 3px 4px;
  color: var(--sdp-cszjsz-wzs1);
  font-size: 12px;
  line-height: 14p;
  font-family: NotoSansHans-Regular;
  float: left;
  border-radius: 2px;
  margin-right: 12px;
  margin-bottom: 12px;
  cursor: pointer;
}
.tag-style-select {
  background-color: var(--sdp-rl-hzts);
  border: 1px solid var(--sdp-rl-hzts);
  color: var(--sdp-nngl);
}

.el-icon-search {
  font-size: 12px;
  position: absolute;
  right: 8px;
  top: 7px;
  color: rgba(117, 135, 162, 1);
}
.search-clear {
  font-size: 12px;
  position: absolute;
  right: 7px;
  top: 5px;
  color: #ADB6CC;
  display: none;
}
.search-clear:hover {
  display: block;
}

.select-down {
  float: right;
  line-height: calc(18px - (var(--color-ParameterBorderLineWidth, 1px) - 1px) * 2) !important;
  height: 20px;
  margin-right: 8px;
}
.group {
  padding: 12px;
  background-color: var(--sdp-szk-bjs);
  border-radius: 8px;
  border: none !important;
}
.no-group {
  padding: 12px;
  background-color: var(--sdp-szk-bjs);
  border-radius: 8px;
  border: none !important;
}
.select-type {
  width: calc(100% - 12px);
  margin-left: 6px;
}
.select-name {
  font-size: 12px;
  margin-bottom: 2px;
}
.like-input {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 20px;
  font-family: NotoSansHans-Regular;
  line-height: calc(18px - (var(--color-ParameterBorderLineWidth, 1px) - 1px) * 2) !important;
  padding: 0px;
  text-indent: 8px;
  text-align: left;
  width: 100%;
  border: var(--color-ParameterBorderLineWidth, 1px) var(--color-ParameterBorderLineType, solid) var(--color-ParameterBorderLineColor, var(--sdp-cszj-srbks)) !important;
  display: inline-block;
  background-color: transparent;
  border-radius: var(--color-ParameterBorderRadius, 0px);
  font-size: 12px;
  cursor: pointer;
}
.tab-border-bottom {
  border-bottom: 1px solid;
  border-color: var(--sdp-cszj-bkfgx);
}
.clear-ico-style {
  font-size: 12px;
  color: #ADB6CC;
}
.select-content {
  .tag-style:hover {
    border: 1px solid var(--sdp-rl-hzts);
    .clear-ico-style {
      color: var(--sdp-rl-hzts);
    }
  }
}
.tag-title {
  font-family: NotoSansHans-Regular;
  font-size: 12px;
  font-weight: 500;
  line-height: 14px;
  margin-bottom: 12px;
  color: var(--sdp-xxbt2);
  display: flex;
  justify-content: space-between;
}
.select-title {
  font-family: NotoSansHans-Regular;
  font-size: 12px;
  font-weight: 600;
  text-align: right;
  line-height: 14px;
  color: var(--sdp-cszj-onns);
}

.clear-title {
  font-family: NotoSansHans-Regular;
  font-size: 12px;
  font-weight: 500;
  color: var(--sdp-zjbxx-wzs);
  line-height: 14px;
  cursor: pointer;
}

.disable-style {
  color: var(--sdp-srk-bxwzs) !important;
}
.tab-style.el-tabs--card {
  margin-top: 11px;
  /deep/ .el-tabs__nav-wrap {
    overflow: inherit;
    border-bottom: 1px solid var(--sdp-cszj-bkfgx);
    height: 28px;
  }
  /deep/ .el-tabs__nav-prev {
    line-height: 28px;
  }
  /deep/ .el-tabs__nav-next {
    line-height: 28px;
  }
  /deep/ .el-tabs__nav {
    display: flex;
    border: none!important;
  }
  /deep/ .el-tabs__header {
    .el-tabs__item:first-child.is-active {
      border-left: 1px solid var(--sdp-cszj-bkfgx)!important;
    }
    .el-tabs__item:not(.is-active) {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      font-family: NotoSansHans-Regular;
      color: var(--sdp-cszjsz-wzs1);
      padding: 0 16px;
      border-color: var(--sdp-cszj-bkfgx);
    }
    .el-tabs__item.is-active {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      font-family: NotoSansHans-Regular;
      padding: 0 16px;
      color: var(--sdp-cszj-onns) !important;
      border-bottom-color: var(--sdp-szk-bjs) !important;
    }
  }
}
.nodata-style {
  height: 400px;
  line-height: 400px;
  text-align: center;
  font-family: NotoSansHans-Regular;
  font-size: 14px;
  color: var(--sdp-cszjsz-wzs1);
  background-color: var(--sdp-szk-bjs);
  padding: 10px 0;
  margin: 0;
  border: 1px solid var(--sdp-cszj-srbks);
  /deep/ .el-loading-spinner {
    top: 0px!important;
  }
}
</style>
