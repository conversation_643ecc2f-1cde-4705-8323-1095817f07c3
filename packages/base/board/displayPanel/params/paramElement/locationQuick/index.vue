<template>
  <div
    class="calendar-quick"
    :class="{ disabledStyle: isDisabled }"
  >
      <span
        v-for="item in labelPickerData"
        :key="item.id"
        class="calendarQuickName"
        :class="[dimensionTypeId === item.id ? 'disabled disabled-style' : '', 'sdp-params-theme-calendar-background sdp-params-quick-theme-style']"
        :title="item.typeName"
        @click="handleClick(item, true)"
      >
        <template v-if="labelPickerData.length > 3">
          {{item.typeName | capitalize}}
        </template>
        <template v-else>
          {{item.typeName}}
        </template>
      </span>
      <el-popover
        placement="bottom"
        width="118"
        :popper-class="`sdp-location-quick-popover-style ${getCurrentThemeClass()}`"
        trigger="click">
          <el-scrollbar style="max-height:208px" class="scrollbar-style" v-sdp-el-scrollbar>
            <span
              v-for="item in labelPickerData_hidden"
              :key="item.id"
              :title="item.typeName"
              class="hideen-style"
              :class="[dimensionTypeId === item.id ? 'hideen-disabled' : '', 'sdp-params-theme-background sdp-params-quick-theme-style']"
              @click="handleClick(item, true)"
            >
              <template v-if="labelPickerData.length > 3">
                {{item.typeName | capitalize}}
              </template>
              <template v-else>
                {{item.typeName}}
              </template>
            </span>
          </el-scrollbar>
        <el-button slot="reference" v-show="labelPickerData_hidden.length" class="more-style sdp-params-theme-background sdp-params-quick-theme-style" :class="checkHiddenSelect?'hideen-disabled disabled-style' : ''">···</el-button>
      </el-popover>
  </div>
</template>

<script>
import { FINISH_TYPE, TYPE_PARAM_ELEMENT } from '../../utils/constants'
import { getLabelListByType, getLanguaeAndCurrency, getReportCurrency, getTagShopAndOletTree } from '../locationNew/api'
import { CONSTANTS } from '../locationNew/bridge'
import { initializationData } from './bridge'
import EventData from '../../../../../../assets/EventData'
import { CREATE_TOAST_TIME, EVENT_DATA_PARAMS, RUN_TYPE } from 'packages/assets/constant'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import { deepGetId } from '../locationNew/constant'
import { EVENT_BUS } from '../../../constants'

export default {
  name: 'locationQuick',
  inject: {
    utils: { default: {} },
    sdpBus: { default: {} },
    commonData: { default: {} },
    themeData: { default: {} },
    langCode: { default: '' },
    tenantData: { default: {} },
    levelData: { default: {} },
    getBoardUnderIdElement: { default: () => () => {} },
    getCurrentThemeClass: { default: () => () => '' }
  },
  directives: {
    SdpElScrollbar,
  },
  filters: {
    capitalize(value) {
      if (value.length > 15) {
        return `${value.slice(0, 15)}...`
      }
      return value
    }
  },
  props: {
    paramElement: {
      type: Object,
      default: () => ({}),
    },
    paramsType: {
      type: String
    },
    board: {
      type: Object,
      default: () => ({}),
    },
    beforeHook: {
      type: Function
    },
    conformityApiData: {
      type: Object,
    }
  },
  data () {
    return {
      labelPickerData: [],
      labelPickerData_hidden: [],
      options: null,
      ReportCurrency: null,
    }
  },

  computed: {
    checkHiddenSelect() {
      let check = false
      this.labelPickerData_hidden.forEach(item => {
        if (item.id === this.dimensionTypeId) {
          check = true
        }
      })
      return check
    },
    getLocation() {
      const { content = [] } = this.board.paramsPanelList.find(el => el.active) || {}
      const el = content?.find(el => el.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) || {}
      el.elName && this.$set(this.paramElement, 'elName', el.elName)
      return el
    },
    // 接口
    api () {
      return this.utils.api || function () {}
    },
    tenantId () {
      return this.utils.tenantId || ''
    },
    viewLevelList() {
      const { viewLevelList = [] } = this.commonData.getViewLevelData()
      return viewLevelList
    },
    // 层级视角是否有收入中心
    needOutlet() {
      return this.viewLevelList.some(({ levelType }) => levelType === '2')
    },
    // 层级视角是否有物业
    needShopId() {
      return this.viewLevelList.some(({ levelType }) => levelType === '1')
    },
    dimensionTypeId() {
      return this.$_getProp(this.paramElement, 'content.dimensionTypeId', '')
    },
    isDisabled() {
      return !this.needShopId || !this.commonData.getIsFinish()
    },
    isLazyLoading() {
      return this.levelData.isLazyLoading
    },
    // 是否为移动端
    isMobile() {
      return this.utils.isMobile
    },
  },
  watch: {
    'getLocation.content.labelPickerConfig': {
      handler(val, old) {
        this.updateLabelPickerConfig(val, old)
      },
      deep: true,
      immediate: true,
    },
    paramsType(val) {
      if (val === RUN_TYPE.handRun) {
        this.$set(this.paramElement.content, 'dimensionTypeId', '')
      }
    },
  },
  created () {
    this.finishHook(FINISH_TYPE.AWAIT)
    const { data, type } = this.$_JSONClone(this.beforeHook(this.paramElement))
    initializationData[type].call(this, data)
  },
  mounted() {
    this.handleClick = this.$_throttle(this.handleClick, 1000)
  },
  methods: {
    updateLabelPickerConfig(val, old) {
      const { labelPickerData = [] } = val
      const { labelPickerData: oldLabelPickerData = [] } = old || {}
      if (labelPickerData.length) {
        // 数组是相同的不需要再去做处理
        if (this.$_equalsObj(labelPickerData, oldLabelPickerData)) return
        this.setlabelPickerData(labelPickerData)
      } else {
        this.$set(this, 'labelPickerData', [])
        this.$set(this, 'labelPickerData_hidden', [])
      }
    },
    // 完成钩子函数
    async setlabelPickerData(val) {
      let strList = ''
      if (!val.length) return
      val.length && val.forEach((item, i) => {
        if (!i) {
          strList += item.id
        } else {
          strList += ',' + item.id
        }
      })
      // const data = await getLabelListByType.call(this, { dimensionTypeId: strList, languageCode: this.language, sortName: 'labelDefault' })
      // 根据维度标签排序 不需要再按首字母排序
      const data = await getLabelListByType.call(this, { dimensionTypeId: strList, languageCode: this.language })
      let list = []
      let hiddenList = []
      data.forEach(tree => {
        tree.children.forEach(item => {
          item.typeName = item.name
          if (list.length < 6) {
            list.push(item)
          } else {
            hiddenList.push(item)
          }
        })
      })
      // this.labelPickerData = data[0].children
      this.$set(this, 'labelPickerData', list)
      this.$set(this, 'labelPickerData_hidden', hiddenList)
    },
    setLocationData(finish) {
      if (this.paramsType !== RUN_TYPE.locationRun) return

      this.sdpBus.$emit(EVENT_BUS.SET_LOCATION_DATA, {
        shopIds: this.options?.shopIds ? [...this.options.shopIds].sort() : [],
        outletIds: this.options?.outletIds ? [...this.options.outletIds].sort() : [],
        id: this.paramElement.id,
        componentStatus: finish,
        type: this.paramElement.type
      })
    },
    async finishHook(finish, hand) {
      const { shopIds = [], outletIds = [] } = this.options || {}
      let message = false
      if (FINISH_TYPE.FINISH === finish) {
        const bool = !shopIds.length
        if (bool) {
          finish = FINISH_TYPE.UNFINISH
          message = this.$t('sdp.message.NoPropertyDataFound')
        } else {
          message = FINISH_TYPE.UNFINISH === finish && this.$t('sdp.views.boardJumpError')
          await this.getLanguaeAndCurrency(shopIds)
        }
        this.$set(this.paramElement.content, 'options', this.options)
      }
      this.setLocationData(finish)
      this.$emit('finishHook', { id: this.paramElement.id, type: finish, message, messageType: 'info' })
    },
    // 请求企业货币类型
    async getLanguaeAndCurrency (shopIds) {
      const { currencyType, languageStatus } = this.tenantData
      if (currencyType === '2' && languageStatus === '0') return
      const languaeAndCurrency = await getLanguaeAndCurrency.call(this, { tenantId: this.tenantId, shopIds, reverseElection: false })
      this.ReportCurrency = this.conformityApiData.reportCurrencyList || this.ReportCurrency || await getReportCurrency.call(this)
      const eventData = new EventData({
        source: TYPE_PARAM_ELEMENT.LOCATION_QUICK,
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'languaeAndCurrency',
        type: 'languaeAndCurrency',
        data: {
          languaeAndCurrency,
          ReportCurrency: this.ReportCurrency
        }
      })
      this.$emit('eventBus', eventData)

      const { tipFlag, message = '' } = languaeAndCurrency
      tipFlag && message && this.setMessage(message)
    },
    setMessage(txt, elementId = 'mobileElementPanel') {
      if (this.isMobile) {
        let confrimToast = this.$createToast({
          type: 'warn',
          time: CREATE_TOAST_TIME,
          txt,
        })
        this.$_insertElement(this.getBoardUnderIdElement(`#${elementId}`), confrimToast.$el)
        confrimToast.show()
      } else {
        this.$message(txt)
      }
    },
    async getAllShopIdOutletId(id) {
      const shopId = []
      const outletId = []
      if (id.length) {
        let shopList = (await getTagShopAndOletTree.call(this, {
          ids: id,
          tenantId: this.tenantId
        })) || []
        if (shopList.length) {
          deepGetId(shopList, shopId, outletId)
        }
      }
      this.$set(this.options, 'shopIds', [
        ...new Set(shopId)
      ])
      this.$set(this.options, 'outletIds', [
        ...new Set(outletId)
      ])
      this.$set(this.options, 'isPullSelect', false)
    },
    async handleClick(dimension = {}, hand) {
      // if (this.$_getProp(this.paramElement, 'content.dimensionTypeId', '') === dimension.id || !dimension.id) {
      if (!dimension?.id) {
        return void '点击相同id不调用接口'
      }
      this.$set(this.paramElement.content, 'dimensionTypeId', dimension.id)
      this.finishHook(FINISH_TYPE.AWAIT)
      const { options } = this.$_JSONClone(this.getLocation.content)
      this.options = options
      this.$set(this.paramElement.content, 'loction', [dimension.name])
      hand && this.refreshBoard()
      await this.getAllShopIdOutletId([dimension.id, dimension.parentId])
      this.$set(this.options, 'dimensionIds', [])
      this.$set(this.options, 'labelIds', [dimension.id, dimension.parentId])
      this.finishHook(FINISH_TYPE.FINISH, hand)
    },
    refreshBoard () {
      const eventData = new EventData({
        source: 'locationQuick',
        target: EVENT_DATA_PARAMS.parent,
        targetFn: 'run',
        type: 'run',
        data: {
          options: {
            type: RUN_TYPE.locationRun
          }
        },
      })
      this.$emit('eventBus', eventData)
    },
    requestAdapter() {
      return {
        type: 'Array',
        key: CONSTANTS.KEY,
        data: this.options,
      }
    }
  },
}
</script>

<style media="screen" lang="scss" scoped>
.disabledStyle {
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
}
.disabled-style {
  color: var(--sdp-nngl) !important;
  border-color: var(--sdp-srk-bks) !important;
  background-color: var(--sdp-cszjxz-hs1) !important;
}
.calendar-quick {
  float: right;
  padding-right: 16px;
  box-sizing: content-box;
  .calendarQuickName + .calendarQuickName {
    margin-left: 12px;
  }
  .chart {
    text-align: right;
    padding: 5px;
  }
  .calendarQuickName {
    display: inline-block;
    border-radius: 2px;
    text-align: center;
    background: transparent;
    border: var(--color-ParameterBorderLineWidth, 1px) var(--color-ParameterBorderLineType, solid) var(--color-ParameterBorderLineColor, var(--sdp-cszj-srbks));
    border-radius: var(--color-ParameterBorderRadius, 0px);
    font-family: NotoSansHans-Regular;
    font-weight: 500;
    font-size: 12px;
    box-sizing: border-box;
    line-height: calc(18px - (var(--color-ParameterBorderLineWidth, 1px) - 1px) * 2) !important;
    height: 20px;
    color: var(--sdp-zxz-wz);
    padding: 0px 10.5px;
    cursor: pointer;
    &:hover {
      color: var(--sdp-nngl) !important;
      border-color: var(--sdp-srk-bks) !important;
      background-color: var(--sdp-cszjxz-hs1) !important;
    }
  }
  .default {
    -webkit-transition: 0.2s ease-in;
    -o-transition: 0.2s ease-in;
    transition: 0.2s ease-in;
  }
  .default-style {
    border: 1px solid var(--sdp-cszj-srbks);
    color: var(--sdp-jys);
    border-color: var(--sdp-srk-bks);
    -webkit-transition: 0.2s ease-in;
    -o-transition: 0.2s ease-in;
    transition: 0.2s ease-in;
    cursor: default !important;
  }
  .default-style:hover {
    border: 1px solid var(--sdp-cszj-srbks) !important;
    color: var(--sdp-jys) !important;
    border-color: var(--sdp-srk-bks) !important;
    background-color: transparent !important;
  }
}
.scrollbar-style {
  max-height: 208px;
  padding-top: 8px;
  /deep/ .is-horizontal {
    display: none;
  }
  /deep/ .el-scrollbar__wrap {
    max-height: 208px;
    margin-bottom: 0px;
  }
  .hideen-style {
    display: inline-block;
    text-align: center;
    font-family: NotoSansHans-Regular;
    font-weight: 500;
    font-size: 12px;
    box-sizing: border-box;
    line-height: normal;
    height: 20px;
    width: 100%;
    border: none;
    float: left;
    color: var(--sdp-cszjsz-wzs1);
    padding: 0px 10.5px;
    cursor: pointer;
    &:hover {
      background: var(--sdp-cszjxz-hs1);
      color: var(--sdp-nngl) !important;
    }
  }
  .hideen-disabled {
    background: var(--sdp-cszjxz-hs1);
    color: var(--sdp-nngl) !important;
  }
}
// .sdp-params-theme-background:hover {
//   color: var(--sdp-jys) !important;
//   background-color: var(--sdp-szk-bjs) !important;
//   border-color: var(--sdp-srk-bks) !important;
// }
.more-style {
  display: inline-block;
  border-radius: 2px;
  text-align: center;
  font-family: NotoSansHans-Regular;
  font-weight: 500;
  font-size: 12px;
  box-sizing: border-box;
  line-height: normal;
  height: 20px;
  padding: 0px 10.5px;
  margin-left: 12px;
  cursor: pointer;
  border: 1px solid var(--sdp-cszj-srbks) !important;
  color: var(--sdp-zxz-wz) !important;
  background-color: transparent !important;
  &:hover {
    border: 1px solid var(--sdp-cszj-srbks) !important;
    color: var(--sdp-zxz-wz) !important;
    background-color: transparent !important;
  }
}
</style>
