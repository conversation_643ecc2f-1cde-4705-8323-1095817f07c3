import {
  TYPE_ELEMENT_GROUP,
  TYPE_PARAM_ELEMENT,
  FINISH_TYPE
} from '../../utils/constants'
import {
  TYPE_SUPERSETTING
} from 'packages/base/board/displayPanel/constants'
import i18n from 'packages/assets/locale'
import DatasetBase, { FieldType, ReplaceRuleType } from '../../../datasetReplace/datasetReplace'

const element = {
  name: 'Date Type',
  // groupName: '参数组件',
  groupName: i18n.t('views.paramComponent'),
  groupType: TYPE_ELEMENT_GROUP.PARAM,
  type: TYPE_PARAM_ELEMENT.DATE_TYPE
}

export class DateTypeDataset extends DatasetBase {
  includesDataPath = []
  excludesDataPath = []
  elementType = TYPE_PARAM_ELEMENT.DATE_TYPE
  isBindField = false
  callDatasetReplace(element: any, replaceRule: ReplaceRuleType): void {
    console.log('callDatasetReplace: ', element, replaceRule)
    const nowDatasetId = this.getProperty(replaceRule, 'datasetList[0].oldId')
    const nowDatasetName = this.getProperty(replaceRule, `datasetData[${nowDatasetId}].labeName`)
    const targetDatasetId = this.getProperty(replaceRule, 'datasetList[0].newId')
    const targetDatasetName = this.getProperty(replaceRule, `datasetData[${targetDatasetId}].labeName`)
    this.getProperty(replaceRule, 'fieldList').forEach(field => {
      if (field.oldField.parentId === nowDatasetId) {
        this.getProperty(element, 'content.dimensions').forEach(item => {
          if (item.columnName === field.oldField.labeName) {
            item.columnName = field.newField.labeName
          }
          (item.orderList || []).forEach(item1 => {
            if (item1.columnName === field.oldField.labeName) {
              item1.columnName = field.newField.labeName
              this.setProperty(element, 'content.aliasstr', field.newField.labeName)
            }
          })
        })
      }
    })
    if (this.getProperty(element, 'content.dataSetId') === nowDatasetId) {
      this.setProperty(element, 'content.dataSetId', targetDatasetId)
    }
  }
  getElementDatasetIdAndUseField(element: any, datasetList: any[]): { [id: string]: FieldType[] } | boolean {
    console.log('getElementDatasetIdAndUseField: ', element, datasetList)
    const { content } = element
    const ids = this.pluck(datasetList, 'id')

    const curBindId = this.getProperty(content, 'dataSetId')

    const overlapIds = this.overlap(ids, [curBindId])

    if (!overlapIds.length) return false

    const fields: {
      labeName: string,
      parentId: string
    }[] = []
    const dimensions = this.getProperty(content, 'dimensions') || []
    dimensions.forEach(item => {
      if (item.columnName) {
        fields.push({
          labeName: item.columnName,
          parentId: curBindId
        })
      }
      ;(item.orderList || []).forEach(orderItem => {
        if (orderItem.columnName) {
          fields.push({
            labeName: orderItem.columnName,
            parentId: curBindId
          })
        }
      })
    })

    const allFields = this.flatten(datasetList.filter(dataset => overlapIds.includes(dataset.id)).map(({ children = [] }) => children))

    const hasFieldsObj = this.getHasFieldsObj(allFields, fields)

    return hasFieldsObj
  }
  getElementDatasetIdAndUseFieldDecorator(...rest: [any, any[]]) {
    return this.decorator(...rest)
}
}

export function adapter(el) {
  let params = {}
  if (el.content.selectedType === 'Consolidate') {
    params = {
      bindTableElements: el.content.bindTableElements,
      dataSetId: el.content.dataSetId,
    }
  } else {
    let dimensions = JSON.parse(JSON.stringify(el.content.dimensions))
    let newDimensions = dimensions.map(({ columnName, columnType, lgeType, orderList }) => {
      let data = {
        columnName,
        columnType,
        lgeType,
      }
      if (orderList) {
        data.orderList = orderList[0] ? orderList[0].columnName ? orderList : undefined : undefined
        data.order = orderList[0] ? orderList[0].columnName ? undefined : orderList[0] ? orderList[0].order : undefined : undefined
      }
      return data
    })
    console.log(newDimensions)
    params = {
      bindTableElements: el.content.bindTableElements,
      dataSetId: el.content.dataSetId,
      dimensions: newDimensions,
    }
  }
  console.log('adapter', el)
  return params
}

class ParamElContent {
  constructor() {
    this.selectedType = 'Consolidate'
    this.bindTableElements = []
    this.layout = {
      i: '9',
      solt: 9,
      x: 2,
      y: 2,
      w: 2,
      h: 15,
      defaultHeight: 15,
      defaultY: 2,
    }
    this.storeLayout = {
      pcLayout: {
        i: '9',
        solt: 9,
        x: 2,
        y: 2,
        w: 2,
        h: 15,
        defaultHeight: 15,
        defaultY: 2,
      },
      mobileLayout: {
        i: '9',
        solt: 9,
        x: 2,
        y: 2,
        w: 7,
        h: 15,
        defaultHeight: 15,
        defaultY: 2,
      }
    }
  }
}

export default {
  element,
  adapter,
  ParamElContent
}

export const CONSTANTS = {
  KEY: 'typeComponents',
  TYPE: 'Array',
}

export const initializationData = {
  // 超链接前进
  [TYPE_SUPERSETTING.GO_FORWARD](data) {
    initializationData.init.call(this)
  },
  // 超链接返回
  [TYPE_SUPERSETTING.RETREAT](data) {
    initializationData.userState.call(this)
  },
  // 超链接清空
  [TYPE_SUPERSETTING.CLOSE_SKIP](data) {
    initializationData.userState.call(this)
  },
  // 初始化
  [TYPE_SUPERSETTING.INIT](data) {
    initializationData.init.call(this)
  },
  // 计划任务
  [TYPE_SUPERSETTING.PLAN_RASK](data) {
    initializationData.userState.call(this)
  },
  // TODO订阅
  [TYPE_SUPERSETTING.SUBSCRIBE](data) {
    initializationData.init.call(this)
  },
  // 用户操作状态
  async userState() {
    this.finishHook(FINISH_TYPE.FINISH)
  },
  // 初始化流程
  async init() {
    const {
      selectedType
    } = this.paramElement.content
    if (!selectedType) {
      this.paramElement.content.selectedType = 'Consolidate'
    }
    // 兼容老看板
    if ((selectedType !== 'Consolidate') && (selectedType !== 'By date')) {
      this.paramElement.content.selectedType = 'Consolidate'
    }
    this.finishHook(FINISH_TYPE.FINISH)
  }
}
