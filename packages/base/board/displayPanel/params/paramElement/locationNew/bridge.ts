import { FINISH_TYPE, TYPE_ELEMENT_GROUP, TYPE_PARAM_ELEMENT } from '../../utils/constants'
import { TYPE_SUPERSETTING } from 'packages/base/board/displayPanel/constants'
import i18n from 'packages/assets/locale'
import { PARAM_FILTER_LABLE_TYPE_LABEL_SELECT } from './settingMixin_intersectionLocation'
import { getTreeInit } from './constant'
import { COMMON_VALUE, TreeDataShopIdAndOutId } from './utils'
import DatasetBase, { ReplaceRuleType } from '../../../datasetReplace/datasetReplace'
import { getProperty, setProperty } from '../../../../../../assets/utils/globalTools'
import { LayoutType } from '../base'

const setDataSetFields = (element: any, replaceRule: ReplaceRuleType, excludesDataPath) => {
  const { datasetList, fieldList } = replaceRule
  const { content } = element

  getProperty(content, 'options.dataSetFields')?.forEach((item, index) => {
    const { dataSetId, ...fields } = item

    const data = datasetList.find(dataset => dataset.oldId === dataSetId)

    if (data) {
      setProperty(item, 'dataSetId', data.newId)

      Object.values(fields).forEach((field: any) => {
          const fieldItem = fieldList.find(({ oldField }) => field.columnName === oldField.labeName)

          if (fieldItem) {
            field.columnName = fieldItem.newField.labeName
          }
      })
    } else {
      excludesDataPath.push(`content.options.dataSetFields[${index}]`)
    }
  })
}

export class LocationDataset extends DatasetBase {
  includesDataPath = []
  excludesDataPath = ['content.checked']
  elementType = TYPE_PARAM_ELEMENT.LOCATION_NEW
  isBindField = false

  callDatasetReplace(element: any, replaceRule: ReplaceRuleType): void {
    const { datasetList, fieldList } = replaceRule
    const { content } = element

    setDataSetFields(element, replaceRule, this.excludesDataPath)

    this.getProperty(content, 'paramSelectCollection')?.forEach(item => {
      const { columnName, datasetId } = item
      const data = datasetList.find(dataset => dataset.oldId === datasetId)

      if (data) {
        this.setProperty(item, 'datasetId', data.newId)

        fieldList.forEach(({ oldField, newField }) => {
          if (oldField.labeName === columnName) {
            this.setProperty(item, 'columnName', newField.labeName)
          }
        })
      }
    })

    console.log(element, replaceRule, 'replaceRule')
  }

  getElementDatasetIdAndUseField(element: any, datasetList: any[]) {
    const { content } = element
    const ids = this.pluck(datasetList, 'id')

    const curBindIds = this.getProperty(content, 'options.dataSetFields').map((item) => item['dataSetId'])

    const overlapIds = this.overlap(ids, curBindIds)

    if (!overlapIds.length) return false

    const fields = this.flatten(this.getProperty(content, 'options.dataSetFields').map(({ dataSetId, ...fields }) => {
      return Object.values(fields).map((field) => ({
        labeName: field.columnName,
        parentId: dataSetId
      }))
    }), 1)

    const allFields = this.flatten(datasetList.filter(dataset => overlapIds.includes(dataset.id)).map(({ children = [] }) => children))

    const hasFieldsObj = this.getHasFieldsObj(allFields, fields)

    return hasFieldsObj
  }

  getElementDatasetIdAndUseFieldDecorator(...rest: [any, any[]]) {
      return this.decorator(...rest)
  }
}

const element = {
  name: 'Location new',
  // groupName: '参数组件',
  groupName: i18n.t('views.paramComponent'),
  groupType: TYPE_ELEMENT_GROUP.PARAM,
  type: TYPE_PARAM_ELEMENT.LOCATION_NEW,
}

const adapter = {}

export enum SelectType { Multiple = '1', Single = '2' }

class ParamElContent {
  layout: {
    i: string,
    solt: number,
    x: number,
    y: number,
    w: number,
    h: number,
    defaultHeight: number,
    defaultY: number,
  }
  storeLayout: {
    pcLayout: LayoutType,
    mobileLayout: LayoutType,
  }
  loction: string[]
  selectType: SelectType
  pageObj: {
    page: number
  }
  disableQuick: boolean
  isSelectedTop: boolean
  LocationCompareName: string
  recordTotal: number
  locationSelectedLabel: string
  selLocationData: {}
  options: any
  constructor() {
    // 布局
    this.layout = {
      i: '1',
      solt: 1,
      x: 0,
      y: 0,
      w: 1,
      h: 25.5,
      defaultHeight: 25.5,
      defaultY: 1,
    }
    this.storeLayout = {
      pcLayout: {
        i: '1',
        solt: 1,
        x: 0,
        y: 0,
        w: 1,
        h: 25.5,
        defaultHeight: 25.5,
        defaultY: 1,
      },
      mobileLayout: {
        i: '1',
        solt: 1,
        x: 0,
        y: 0,
        w: 7,
        h: 25.5,
        defaultHeight: 25.5,
        defaultY: 1,
      }
    }
    // 已选参数
    this.loction = []
    // 多选 1 单选2
    this.selectType = SelectType.Multiple
    // 下拉框分页
    this.pageObj = {
      page: 1
    }
    // 多语言字段
    this.LocationCompareName = 'Compare'
    // 禁用快选
    this.disableQuick = false
    // 选中项置顶 默认勾选
    this.isSelectedTop = true
    this.recordTotal = 0 // 总数据
    this.locationSelectedLabel = '' // location显示的已选数据
    this.selLocationData = {}
    this.options = {
      totalShopIds: 0, // 获取的所有shopId长度
      // 绑定的控件元素id["1", "2"]
      bindElements: [],
      // 根据shopId过滤["300","302","304"]
      shopIds: [],
      // Revenue Center 营利中心（根据outletId过滤）["10","11","12"]
      outletIds: [],
      // 传参类型 1、参数过滤 2、层级统计
      paramType: '',
      // 层级统计类型 3、节点汇总 4、物业或 5,收入中心汇总
      hierarchyType: '',
      // 报表层级树维度id["67549683727192064","67549683727192065"]
      dimensionIds: [],
      // 标签id["67550204001243136","67550204001243136"]
      labelIds: [],
      // 是否有标签（传参类型为1的场景下有用）
      labeled: false,
      // 是否为下拉框：true 是下拉框,反之为预置维度
      isPullSelect: true,
      // 是否比较
      isCompare: false,
      // 反选
      reverseElection: false,
      locationArea: {
        // 本期的shopId，所有时为ALL
        currShopIds: [],
        // 上期shopId，所有时为ALL
        preShopIds: [],
        // 本期Revenue Center 营利中心
        currOutletIds: [],
        // 上期Revenue Center 营利中心
        preOutletIds: [],
        // shopId字段
        shopIdField: 'shop_id',
        // oletId字段
        oletIdField: 'olet_id',
      },
      // 字段参数
      injectConditionParameter: [],
      // 受影响的数据集及字段
      dataSetFields: [{
        dataSetId: '',
        // 维度类型
        dimensionTypeField: {
          // 该数据集下的字段
          columnName: ''
        },
        // 维度类型id
        demensionTypeIdField: {
          columnName: ''
        },
        // shopId字段(过滤字段)
        shopIdField: {
          // 该数据集下的字段
          columnName: ''
        },
        // shopName字段
        shopNameField: {
          // 该数据集下的字段
          columnName: ''
        },
        // outlets字段(过滤字段)
        outletsField: {
          // 该数据集下的字段
          columnName: ''
        },
        // outletName字段
        outletNameField: {
          // 该数据集下的字段
          columnName: ''
        }
      }],
      filterShopFlag: false,
      filterShopIds: []
    }
  }
}

export default {
  element,
  adapter,
  ParamElContent,
}

export const CONSTANTS = {
  KEY: 'locationCombinations',
}

// 验证默认值取消是否选择值
// export function verifyDefault(val, data) {
//   if (!val) {
//     const { shopIds, outletIds } = data.options
//     return data.changeValue || shopIds.length || outletIds.length
//   }
//   return true
// }

// 用源看板的 Location 数据替换目标看板的 Location 数据
function changeLocationWithData(targetLocationData, superLocationData, isIntersection2Location) {
  const { content = {} } = superLocationData
  const { options = {} } = content
  let isHave = true
  // 物业筛选逻辑
  let changeValue = content.changeValue
  let loctionFilter = content.loction
  let shopIdsFilter = options.shopIds
  let outletIdsFilter = options.outletIds
  if (!this.isLazyLoading) {
    let propertyScreeningShopIdsFilter = shopIdsFilter
    let propertyScreeningOutletFilter = outletIdsFilter
    let nextPropertyScreeningShopIdsFilter = shopIdsFilter
    let nextPropertyScreeningOutletFilter = outletIdsFilter
    const isAll = content.loction.length === 1 && content.loction[0] === 'ALL'
    if (content.propertyScreening) {
      // 获取物业
      if (isAll) {
        propertyScreeningShopIdsFilter = []
        propertyScreeningOutletFilter = []
        content.pullDownData.forEach(item => {
          propertyScreeningShopIdsFilter.push(item.id)
          item.children.forEach(c => {
            propertyScreeningOutletFilter.push(c.id)
          })
        })
      }
    }
    if (targetLocationData.content.propertyScreening) {
      nextPropertyScreeningShopIdsFilter = []
      nextPropertyScreeningOutletFilter = []
      targetLocationData.content.pullDownData.forEach(item => {
        nextPropertyScreeningShopIdsFilter.push(item.id)
        item.children.forEach(c => {
          nextPropertyScreeningOutletFilter.push(c.id)
        })
      })
    }
    shopIdsFilter = propertyScreeningShopIdsFilter.filter(id => nextPropertyScreeningShopIdsFilter.includes(id))
    outletIdsFilter = propertyScreeningOutletFilter.filter(id => nextPropertyScreeningOutletFilter.includes(id))
    if (isAll) {
      this.allFlag = shopIdsFilter.length === nextPropertyScreeningShopIdsFilter.length || shopIdsFilter.length === propertyScreeningShopIdsFilter.length
    } else {
      this.allFlag = false
    }
  }
  if (!this.allFlag && shopIdsFilter.length === 0) {
    this.$message.warning(this.$t('sdp.views.Cannot_find'))
  }

  Object.assign(targetLocationData.content, {
    changeValue,
    default: content.default,
    intersectionData: content.intersectionData,
    loction: loctionFilter,
  })

  Object.assign(targetLocationData.content.options, {
    isPullSelect: options.isPullSelect,
    outletIds: outletIdsFilter,
    shopIds: shopIdsFilter,
  },
  isIntersection2Location ? {} : {
    dimensionIds: options.dimensionIds,
    labelIds: options.labelIds,
  })
}

// 参数过滤标签类型为标签选择时的特殊处理逻辑
function intersectionHandler() {
  const { content } = this.paramElement

  const {
    changeValue,
    selectType,
    intersectionData,
    options: {
      outletIds,
      shopIds
    },
    saveActiveTabSelectType
  } = content

  if (intersectionData) {
    const { hierarchiesTreeOpts, dimensionsTreeOpts } = JSON.parse(intersectionData)

    hierarchiesTreeOpts.init = false
    dimensionsTreeOpts.init = !dimensionsTreeOpts.checkedKeys.length

    content.intersectionData = JSON.stringify({ hierarchiesTreeOpts, dimensionsTreeOpts })

    this.$nextTick(() => {
      const intersectionLayoutRef = this.getCurrentRefs('intersectionLayoutRef')
      intersectionData && intersectionLayoutRef && (() => {
        intersectionLayoutRef?.initData && intersectionLayoutRef.initData()
      })()
    })
  }

  if (saveActiveTabSelectType !== COMMON_VALUE.INTERSECTION_VALUE) return false
  this.allFlag = selectType === SelectType.Multiple && changeValue === 'ALL'
  if (outletIds.length || shopIds.length) {
    this.finishHook(FINISH_TYPE.FINISH)
  } else {
    this.finishHook(FINISH_TYPE.UNFINISH)
  }
}

const getSuperSaveActiveTabSelectType = (superSaveActiveTabSelectType: string, data) => {
  if (superSaveActiveTabSelectType) return superSaveActiveTabSelectType

  if (data?.content?.options?.isPullSelect) {
    return COMMON_VALUE.LOCATION_VALUE
  } else if (data?.content?.options?.paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT) {
    return COMMON_VALUE.INTERSECTION_VALUE
  } else if (data?.content?.options?.saveData) {
    const saveData = data?.content?.options?.saveData
    if (saveData.includes(COMMON_VALUE.HIERARCHIES_VALUE)) {
      return COMMON_VALUE.HIERARCHIES_VALUE
    } else if (saveData.includes(COMMON_VALUE.DIMENSIONS_VALUE)) {
      return COMMON_VALUE.DIMENSIONS_VALUE
    }
  }

  return void '不满足条件'
}

export const initializationData = {
  // 超链接前进
  async [TYPE_SUPERSETTING.GO_FORWARD](data, isSubscription = false) {
    console.log(data, 'isReplace - kyz')

    // 如果是根据接收者物业权限
    if (isSubscription) {
      if (this.paramElement.dataPermission === '2' || this.commonData?.subscribeDataPermission === '2') {
        initializationData.subscribePermissionCallback2.call(this)
        return
      }
    }

    const isLink = initializationData.checkIsLinkShopIdOrShopName.call(this)
    if (data) {
      const {
        options: {
          paramFilterLableType,
          paramType,
          isCompare,
        },
        labelSelectType,
        selectType,
      } = this.contentMap

      const {
        options: {
          paramFilterLableType: superParamFilterLableType,
          isCompare: superIsCompare,
          shopIds: superShopIds,
          outletIds: superoutletIds,
        },
        labelSelectType: superLabelSelectType,
        saveActiveTabSelectType: superSaveActiveTabSelectType,
        saveData: superSaveData,
        selectType: superSelectType,
        intersectionData: superIntersectionData,
        tile: superTile,
        treeDataShopIdAndOutId
      } = data.content
      const defaultTabSelectType = initializationData.getDefaultTabSelectType.call(this)
      const tabSelectType = getSuperSaveActiveTabSelectType(superSaveActiveTabSelectType, data) || defaultTabSelectType

      const isReplace = this.tabs.some(item => {
        let isSameTabType = tabSelectType === item.value
        if (tabSelectType === COMMON_VALUE.DIMENSIONS_VALUE) {
          isSameTabType = isSameTabType && labelSelectType === superLabelSelectType
        }
        return isSameTabType
      }) && !isCompare && !superIsCompare && (selectType === superSelectType || superSelectType === SelectType.Single) && !(isLink && tabSelectType === COMMON_VALUE.LOCATION_VALUE)

      // 判断 paramType 传参类型 1、参数过滤 2、层级统计 selectType：多选 1 单选2
      // let isReplace = paramType === superParamType &&
      //   (selectType === superSelectType || superSelectType === '2') &&
      //   !isCompare && !superIsCompare &&
      //   superLoction.length && (disableQuick === superDisableQuick || (isPullSelect === superIsPullSelect && labeled === superLabeled))

      const isIntersectionMode = paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT
      const isIntersectionModeSuper = superParamFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT
      const isIntersection2Location = isIntersectionModeSuper && !isIntersectionMode
      const isIntersection2Intersection = isIntersectionModeSuper && isIntersectionMode
      console.log(isReplace, 'isReplace - kyz')
      if (isReplace) {
        // 比较数据处理掉是否禁用的shopId和outletId
        // 平铺模式不需要处理
        await TreeDataShopIdAndOutId.compareData(this, {
          superTile,
          treeDataShopIdAndOutId,
          options: data.content.options,
          outletIds: data.content.options.outletIds,
          shopIds: data.content.options.shopIds,
          loction: data.content.loction,
          changeValue: data.content.changeValue,
          superSaveData,
          superIntersectionData,
          tabSelectType
        }, (shopIds, outletIds, loction, saveData, intersectionData, changeValue) => {
          Object.assign(data.content.options, {
            outletIds,
            shopIds,
          })
          Object.assign(data.content, {
            loction,
            changeValue
          })
          saveData && Object.assign(data.content, {
            saveData,
          })
          intersectionData && Object.assign(data.content, {
            intersectionData,
          })
        })
        this.notInit = true
        this.$set(this.contentMap, 'saveActiveTabSelectType', superSaveActiveTabSelectType)
        initializationData.setSelectedLabel.call(this, tabSelectType)
        this.allFlag = selectType === superSelectType && data.content.changeValue === 'ALL'
        await this.getPullDownData(superShopIds, superoutletIds)
        changeLocationWithData.call(this, this.paramElement, data, isIntersection2Location)
        this.notInit = false
        if (paramType !== '2' && (!superSaveData || superSaveActiveTabSelectType === COMMON_VALUE.LOCATION_VALUE)) {
          this.initDownData()
          if (this.isIntersectionMode) {
            initializationData.getIntersectionCount.call(this)
          }
          this.finishHook(this.FINISH_TYPE.FINISH)
        } else {
          await this.dataKeepTree(data.content)
          if (!this.isIntersectionMode) {
            let treeData = this.treeData
            if (tabSelectType === COMMON_VALUE.DIMENSIONS_VALUE) {
              // 选中维度标签页（获取数据）
              await this.getTypeList()
              let id = this.typeList[0].id
              try {
                const sData = JSON.parse(superSaveData)
                const selectId = sData.selectedTree
                if (this.typeList.find(e => e.id === selectId)) {
                  id = selectId
                }
              } catch (err) {
                console.log('location error:', err)
              }
              if (this.isMobile) {
                await this.getLabelListByTypeApp({ id: id })
              } else {
                await this.getLabelListByType({ id: id })
              }
              treeData = this.typeTreeData
            }
            getTreeInit.call(this, {
              treeData,
              activeNameBool: tabSelectType === COMMON_VALUE.HIERARCHIES_VALUE,
              originShopIds: this.contentMap.options.shopIds
            }).then((res = []) => {
              console.log(res, 'kyz111')
              this.finishHook(res.length ? FINISH_TYPE.FINISH : FINISH_TYPE.UNFINISH)
            })
          } else {
            // this.finishHook(FINISH_TYPE.FINISH)
          }
          // this.finishHook(FINISH_TYPE.FINISH)
        }
      } else {
        await initializationData.init.call(this, data, true)
      }

      // 目标看板与源看板类型均为 intersectionMode 时需要传参
      isIntersection2Intersection && intersectionHandler.call(this)
    } else {
      initializationData.init.call(this, data, isLink)
    }
  },
  // 超链接返回
  [TYPE_SUPERSETTING.RETREAT]() {
    initializationData.userState.call(this)
  },
  // 超链接清空
  [TYPE_SUPERSETTING.CLOSE_SKIP]() {
    initializationData.userState.call(this)
  },
  // 初始化
  [TYPE_SUPERSETTING.INIT]() {
    initializationData.init.call(this)
  },
  // 计划任务
  [TYPE_SUPERSETTING.PLAN_RASK]() {
    initializationData.userState.call(this)
  },
  // 智能搜索
  async [TYPE_SUPERSETTING.INTELLIGENT]() {
    const { shopIds } = this.utils?.intelligentData

    if (!shopIds || !shopIds.length) return initializationData.init.call(this)

    setTimeout(async () => {
      this.$set(this.contentMap, 'changeValue', '')
      this.$set(this.contentMap, 'saveActiveTabSelectType', COMMON_VALUE.LOCATION_VALUE)
      this.$set(this.contentMap, 'disableQuick', false)
      this.$set(this.contentMap, 'selectType', SelectType.Multiple)
      this.$set(this.optionMap, 'paramType', '1')
      this.$set(this.optionMap, 'hierarchyType', '')
      this.optionMap.dataSetFields.forEach(item => {
        if (!this.optionMap.labeled) {
          this.$set(item, 'selectedDimensionFields', '');
          this.$set(item, 'selectedDemensionTypeIdField', '');
        }
        this.$set(item, 'selectedShopNameField', '');
        this.$set(item, 'selectedOutletNameField', '');
      })
      this.$set(this.optionMap, 'filterShopFlag', false)
      this.$set(this.optionMap, 'filterShopIds', [])
      this.$set(this.optionMap, 'isPullSelect', true)
      this.$set(this.contentMap, 'propertyList', [])
      // this.initCompareData()

      await this.getPullDownData()

      // console.log('INTELLIGENT>', this.pullDownData, this.pullDownData.filter(e => shopIds.includes(e.id)))
      this.$set(this.optionMap, 'shopIds', shopIds)

      const shopList = this.pullDownData.filter(e => shopIds.includes(e.id))
      const outletIds = shopList.reduce((pre, cur) => {
        return pre.concat(cur.children.map(e => e.id))
      }, [])
      this.$set(this.optionMap, 'outletIds', outletIds)

      if (this.optionMap.isCompare) {
        const downSaveData = this.$_getProp(this, 'paramElement.content.downSaveData', false)
        const keys = []
        shopList.forEach(item => {
          keys.push(item.ids)
          if (item.children) {
            item.children.map(e => {
              keys.push(e.ids)
            })
          }
        })

        if (downSaveData) {
          const downSaveDataObj = JSON.parse(downSaveData)
          downSaveDataObj.compareOne = shopList.map(e => e.shopName)
          downSaveDataObj.dataOne.shopIds = shopIds
          downSaveDataObj.dataOne.outletIds = outletIds
          downSaveDataObj.dataOne.keys = keys
          this.$set(this.contentMap, 'downSaveData', JSON.stringify(downSaveDataObj))
          this.loctionData = JSON.stringify(this.contentMap.downSaveData)
        } else {
          this.$set(this.loctionData, 'compareOne', shopList.map(e => e.shopName))
          this.$set(this.loctionData, 'dataOne', {})
          this.$set(this.loctionData.dataOne, 'shopIds', shopIds)
          this.$set(this.loctionData.dataOne, 'outletIds', outletIds)
          this.$set(this.loctionData.dataOne, 'keys', outletIds)
          this.$set(this.loctionData.dataOne, 'allFlag', false)
        }
      }

      this.initDownData()

      this.getAllShopIdOutletId(false)
      !this.optionMap.isCompare && this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, shopIds, outletIds, this.contentMap.loction)
      this.finishHookFun(this.FINISH_TYPE.FINISH)

      // initializationData.userState.call(this)
    }, 1000)
  },
  [TYPE_SUPERSETTING.SUBSCRIBE](data) {
    // 如果是根据接收者物业权限
    if (this.paramElement.dataPermission === '2' || this.commonData?.subscribeDataPermission === '2') {
      initializationData.subscribePermissionCallback2.call(this)
      return
    }

    if (data) {
      const { shopId, oletId } = data
      if (shopId) {
        setTimeout(() => {
          const content = this.contentMap
          const { options } = content
          this.allFlag = false
          this.$set(options, 'shopIds', [shopId])
          this.$set(options, 'outletIds', oletId ? [oletId] : [])
          this.$set(content, 'changeValue', '')
          this.finishHook(this.FINISH_TYPE.FINISH)

          setTimeout(() => {
            const { shopIds: shopIdList, outletIds: outletIdList, loction } = this.getData()
            this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, [shopId], oletId ? [oletId] : [], loction)
            this.setTreeChangeValue()
          }, 100)
        })
        return
      }
    }
    initializationData.init.call(this)
  },
  subscribePermissionCallback() {
    this.finishHook(this.FINISH_TYPE.AWAIT)
    const content = this.contentMap
    const { options } = content
    content.selectType = SelectType.Multiple
    content.changeValue = ''
    this.allFlag = true
    this.$set(options, 'shopIds', [])
    this.$set(options, 'outletIds', [])
    this.finishHook(this.FINISH_TYPE.FINISH)

    setTimeout(() => {
      const { shopIds: shopIdList, outletIds: outletIdList, loction } = this.getData()
      this.$set(content, 'loction', loction)
      this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, [], [], loction)
      this.setTreeChangeValue()
    }, 100)
  },
  subscribePermissionCallback2() {
    const content = this.paramElement.content
    const { options } = content
    content.selectType = SelectType.Multiple
    content.default = true
    content.changeValue = ''
    content.saveActiveTabSelectType = 'Location'
    this.allFlag = true
    this.$set(options, 'shopIds', [])
    this.$set(options, 'outletIds', [])
    this.$set(options, 'filterShopIds', [])
    this.$set(options, 'filterShopFlag', false)
    initializationData.init.call(this)
  },
  // 用户操作状态
  async userState() {
    const content = this.contentMap
    this.saveLocationDataMsg = Object.keys(content?.selLocationData).length ? this.$_deepClone(content.selLocationData) : {}
    // this.$set(this.contentMap, 'selLocationData', this.$_deepClone(this.saveLocationDataMsg))
    const getDefLocationData = () => {
      initializationData.setSelectedLabel.call(this, this.contentMap.saveActiveTabSelectType)
      Object.keys(this.saveLocationDataMsg).length && this.$set(this.contentMap, 'selLocationData', this.$_deepClone(this.saveLocationDataMsg))
    }
    const {
      options: { isPullSelect, shopIds, outletIds },
    } = content
    if (isPullSelect) {
      const delKeys = ['saveCompareData', 'intersectionData']
      delKeys.forEach(key => {
        this.$delete(content, key)
      })
      await this.getPullDownData(shopIds, outletIds)
      this.initDownData()
      const { loction } = this.getData()
      this.$set(this.contentMap, 'loction', loction)

      getDefLocationData()
      this.finishHook(this.FINISH_TYPE.FINISH)
    } else {
      if (this.isIntersectionMode) {
        getDefLocationData()
        intersectionHandler.call(this)
      } else {
        // const tabType = initializationData.getDefaultTabSelectType.call(this)
        // initializationData.setSelectedLabel.call(this, tabType)
        initializationData.setSelectedLabel.call(this, this.contentMap.saveActiveTabSelectType)
        await this.dataKeepTree(content, () => {
          Object.keys(this.saveLocationDataMsg).length && this.$set(this.contentMap, 'selLocationData', this.$_deepClone(this.saveLocationDataMsg))
          this.finishHook(FINISH_TYPE.FINISH)
        })
      }
    }
  },
  setSelectedLabel(val) {
    val && (this.selectedLabel = val)
  },
  getIntersectionCount() {
    intersectionHandler.call(this)
    let intersectionData = this.paramElement.content.intersectionData
    const intersectionLayoutRef = this.getCurrentRefs('intersectionLayoutRef')
    intersectionData && intersectionLayoutRef && (() => {
      const { hierarchiesTreeOpts, dimensionsTreeOpts } = JSON.parse(intersectionData)
      let selectedShopCount = intersectionLayoutRef.getSelShopCount(hierarchiesTreeOpts, dimensionsTreeOpts)
      let selLocationData = this.paramElement?.content?.selLocationData
      this.$set(selLocationData, COMMON_VALUE.INTERSECTION_VALUE, `${selectedShopCount}`)
    })()
  },
  getDefaultTabSelectType() {
    const content = this.contentMap
    // 初始化选中那个页面
    const type = content.defaultTabSelectType || this.tabs[0]?.value || COMMON_VALUE.LOCATION_VALUE
    return type
  },
  async init(data, isOperation = false) {
    this.finishHook(FINISH_TYPE.AWAIT)
    // 清空数据(解决param组件处理dom延迟，会重新触发)
    await new Promise<void>((resolve) => {
      setTimeout(() => {
        this.cleanData()
        resolve()
      })
    })
    const content = this.contentMap

    if (this.utils.isMobile) {
      this.$set(this, 'savePropertyChooseData', {})
    }
    const { selectType, default: defaults, selLocationData } = content
    if (this.$refs.locationMobile) {
      this.$refs.locationMobile.filterText = ''
    }
    if (!selLocationData) {
      this.$set(this.contentMap, 'selLocationData', {})
    }
    let type = initializationData.getDefaultTabSelectType.call(this)
    // 点击shopid跳转时  目前跳转到location
    const isLocationType = isOperation && this.tabs.find(e => e.label === COMMON_VALUE.LOCATION_VALUE)
    this.$set(this.contentMap, 'saveActiveTabSelectType', isLocationType ? COMMON_VALUE.LOCATION_VALUE : type)
    if (isLocationType) {
      type = COMMON_VALUE.LOCATION_VALUE
    }
    let isCallSetSelectedLabel = false
    if (defaults || data?.content?.loction?.length) {
      const { LOCATION_VALUE, HIERARCHIES_VALUE, DIMENSIONS_VALUE, INTERSECTION_VALUE } = COMMON_VALUE
      switch (type) {
        case LOCATION_VALUE:
          await this.getPullDownData()
          this.initCompareData()
          switch (selectType) {
            case SelectType.Multiple:
              let linkId = false
              if (isOperation) {
                linkId = initializationData.shopIdOperation.call(this, isOperation) || false
              }
              if (isOperation && linkId) {
                initializationData.setSuperRadio.call(this, linkId)
                this.setTreeChangeValue(this.FINISH_TYPE.FINISH)
              } else {
                this.allFlag = true
                // 处理allFlag的监听
                setTimeout(() => {
                  const locationDownTileMobile = initializationData.getCurrentRefs.call(this, 'locationDownTileMobile')
                  if (this.isTile && locationDownTileMobile) {
                    locationDownTileMobile.setDefaultValue(true)
                    this.finishHook(this.FINISH_TYPE.FINISH)
                  } else {
                    this.getAllShopIdOutletId()
                  }
                })
              }
              break
            case SelectType.Single:
              // 看板跳转单选操作
              initializationData.setSuperRadio.call(this, initializationData.shopIdOperation.call(this, isOperation))
              break
          }
          break
        case HIERARCHIES_VALUE:
        case DIMENSIONS_VALUE:
        case INTERSECTION_VALUE:
          await this.dataKeepTree(content)
          if (!this.isIntersectionMode) {
            let treeData = this.treeData
            if (type === DIMENSIONS_VALUE) {
              // 选中维度标签页（获取数据）
              await this.getTypeList()
              if (this.isMobile) {
                await this.getLabelListByTypeApp({ id: this.isLabelSelectTypeMulti ? this.typeList.map(item => item.id).join(',') : this.typeList[0].id })

                if (this.isLabelSelectTypeMulti) {
                  const dimensionRef = initializationData.getCurrentRefs.call(this, COMMON_VALUE.DIMENSIONS_VALUE)
                  if (dimensionRef?.$refs?.locationLabelSelectMultiApp) {
                    const locationLabelSelectMultiApp = dimensionRef.$refs.locationLabelSelectMultiApp
                    locationLabelSelectMultiApp.initCallback(this.typeList[0].id, this.typeTreeData)
                  }
                }
              } else {
              await this.getLabelListByType({ id: this.typeList[0].id })
              }
              treeData = this.typeTreeData
            }
            initializationData.setSelectedLabel.call(this, type)
            isCallSetSelectedLabel = true
            await getTreeInit.call(this, {
              treeData,
              activeNameBool: type === HIERARCHIES_VALUE
            }).then(res => {
              res && this.finishHook(this.FINISH_TYPE.UNFINISH)
            })
          }
          break
      }
    } else {
      initializationData.noDefault.call(this)
    }
    isCallSetSelectedLabel || initializationData.setSelectedLabel.call(this, type)
  },
  getCurrentRefs(name) {
    const ref = this.$refs[name]
    if (ref) {
      return Array.isArray(ref) ? ref[0] : ref
    }
    console.log(ref)
  },
  async noDefault() {
    this.$set(this.contentMap, 'selLocationData', {})
    this.isDisableQuick && this.$set(this.optionMap, 'isPullSelect', !this.isDisableQuick)

    this.initDownDataState()

    if (this.isMobile) {
      const intersectionLayoutRef = this.getCurrentRefs('intersectionLayoutRef')
      intersectionLayoutRef?.$refs?.hierarchiesTreeRef?.setCheckedKeys?.([])
    }
    this.finishHook(this.FINISH_TYPE.UNFINISH)
  },
  // 设置location单选操作
  setSuperRadio(linkShopId) {
    linkShopId = linkShopId || this.pullDownData[0] || {}
    const { outItem } = linkShopId
    this.allFlag = false
    this.isAllShopIdOutletIdSetData = this.isCompare
    const hasData = this.pullDownData.find(e => e.id === linkShopId.id)
    if (this.paramElement.content.tile && hasData) {
      const isShowProperty = this.paramElement.content.tileSet !== '2' || false
      const locationDownTileMobile = this.getCurrentRefs('locationDownTileMobile')

      if (isShowProperty) {
        const checkedKeys = [linkShopId.id]
        locationDownTileMobile && (locationDownTileMobile.tileType1Value = checkedKeys)
      } else {
        const activeObjIds = {
          [linkShopId.id]: [linkShopId.id, ...(outItem ? [outItem.id] : linkShopId.children.map((e: { id: any }) => e.id))]
        }
        locationDownTileMobile && (locationDownTileMobile.activeObjIds = activeObjIds)
      }
      locationDownTileMobile && locationDownTileMobile.confirm()
    } else {

      linkShopId && linkShopId.ids && this.getCurrentRefs('tree') && this.getCurrentRefs('tree').setCheckedKeys(outItem ? [outItem.ids] : [linkShopId.ids])

      this.getAllShopIdOutletId()
    }

    this.$set(this.paramElement.content?.selLocationData, COMMON_VALUE.LOCATION_VALUE, `${outItem ? 0 : (linkShopId ? 1 : 0)}/${this.paramElement.content.recordTotal}`)
  },
  // 看板跳转shopid操作
  shopIdOperation (isOperation) {
    if (!isOperation) return false
    // if (!data) return false
    // const { selectType: superSelectType } = data.content
    // const { selectType } = this.contentMap

    const pullDownData = this.pullDownData
    const { options, superLink } = this.superLinkData || {}
    //  || (options && superLink && superSelectType === SelectType.Multiple && selectType !== superSelectType)
    if (isOperation) {
      const { parameterField, labelBoard, clickVal, associateParamsSetting = {} } = options
      // 通过关联目标看板筛选|| item.children.some(data => {
      //   const bool = clickVal === data.id || clickVal === data.name
      //   if (bool) {
      //     outItem = data
      //   }
      //   return bool
      // })
      let { isAssociateParams = false, associateParamsData = [] } = associateParamsSetting
      if (isAssociateParams && associateParamsData.includes(this.paramElement.id) && Array.isArray(superLink?.dataSets)) {
        let outItem = null
        const item = pullDownData.find(item => clickVal === item.id || clickVal === item.name)
        if (item) {
          return {
            ...item,
            outItem
          }
        }
      }
      // SHOP_ID查找
      const isShopId = Object.keys(superLink).length && parameterField.includes('SHOP_ID') && superLink.dataSets.some(e => e.columnName === 'SHOP_ID')
      const isShopName = labelBoard.label.trim().toUpperCase() === 'SHOP_NAME'
      if (isShopId) {
        const item = pullDownData.find(item => superLink.dataSets.find(e => e.columnName === 'SHOP_ID').values[0] === item.id)
        if (item) {
          return item
        }
      }
      // SHOP_NAME查找
      if (isShopName) {
        const item = pullDownData.find(item => clickVal === item.name)
        if (item) {
          return item
        }
      }
      // 9971 【开源框架】【移动看板设计器】图形进行超链接跳转后提示“找不到对应物业”
      if (clickVal === undefined) {
        return pullDownData[0]
      }
      if (isShopId || isShopName) {
        this.setMessage(this.$t('sdp.views.corresponding'))
      }
    }
    return false
  },
  // 检查是或否是由shopId或shopName跳转过来
  checkIsLinkShopIdOrShopName() {
    try {
      if (!Object.keys(this.superLinkData).length) return false
      const { options, superLink } = this.superLinkData || {}

      const { parameterField, labelBoard, associateParamsSetting = {} } = options
      let { isAssociateParams = false, associateParamsData = [] } = associateParamsSetting
      const isAssociateLink = isAssociateParams && associateParamsData.includes(this.paramElement.id)
      // SHOP_ID查找
      const isShopId = Object.keys(superLink).length && parameterField.includes('SHOP_ID') && superLink.dataSets.some(e => e.columnName === 'SHOP_ID')
      const isShopName = labelBoard.label.trim().toUpperCase() === 'SHOP_NAME'

      const isLink = isAssociateLink || isShopId || isShopName || false
      return isLink
    } catch (e) {
      return false
    }
  },
}

export const LOCATION_SELECT_TYPE = {
  // 层级
  DIMENSION: 'dimension',
  // 维度
  LABEL: 'label'
}
