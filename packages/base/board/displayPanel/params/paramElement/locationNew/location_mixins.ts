import locationPopover from './components/locationPopover'
import locationNewTree from './components/locationNewTree/index'
import locationLabelSelectMulti from './components/locationLabelSelectMulti'
import compare from './components/locationNewTree/compare'
import { FINISH_TYPE, TYPE_PARAM_ELEMENT } from '../../utils/constants'
import {
  getLabelListByType,
  getLabelListByTypeApp,
  getLabelTypeList,
  getLanguaeAndCurrency,
  getNewHierarchyDimensionTree2,
  getNewHierarchyDimensionTreeApp,
  getNewLocationShopId,
  getReportCurrency,
  getTagShopAndOletTree,
  getTreeList,
  getTypeShowList, singleFieldPreview,
} from './api'
import { initializationData, LOCATION_SELECT_TYPE, SelectType } from './bridge'
import { EVENT_BUS, TYPE_ELEMENT, TYPE_SUPERSETTING } from 'packages/base/board/displayPanel/constants'
import EventData from 'packages/assets/EventData'
import {CREATE_TOAST_TIME, PARAMS_PANEL_TYPE, RUN_TYPE} from 'packages/assets/constant'
import { PARAM_FILTER_LABLE_TYPE_LABEL_SELECT } from './settingMixin_intersectionLocation'
import {
  COMMON_VALUE,
  TreeDataShopIdAndOutId,
  setDatasStatusByLocation,
  setElBindByLocation,
  LOCATION_COMPARE_TYPE, getHierarchyLocationInfo
} from './utils'
import { LabelSelectType, locationLangAll, searchSplitSymbols } from './constant'
import { DEFAULT_TYPE } from '../../utils/SaveLanAndCur'

const { LOCATION_VALUE, HIERARCHIES_VALUE, DIMENSIONS_VALUE, INTERSECTION_VALUE } = COMMON_VALUE
const KEYS = {
  parent: 'parent',
  son: 'son'
}
export default {
  provide() {
    return {
      topLevelData: this,
    }
  },
  inject: ['sdpBus', 'utils', 'langCode', 'themeData', 'commonData', 'levelData', 'tenantData', 'getBoardUnderIdElement', 'paramsPanelTypeRef'],
  components: {
    locationPopover,
    locationNewTree,
    locationLabelSelectMulti,
    compare,
  },

  props: {
    paramElement: {
      type: Object,
      default: () => ({}),
    },
    board: {
      type: Object,
      default: () => ({}),
    },
    boardInfo: {
      type: Object,
      default: () => ({})
    },
    beforeHook: {
      type: Function,
    },
    clickMobileType: {
      type: String,
      default: '',
    },
    conformityApiData: {
      type: Object,
    },
    paramsType: {
      type: String,
    },
    locationData: {
      type: Object,
    }
  },
  data (vm) {
    const tabList = [
      {
        label: 'Location',
        value: LOCATION_VALUE,
        clickMobileType: 'content',
      }, {
        label: 'sdp.views.Hierarchies',
        value: HIERARCHIES_VALUE,
        clickMobileType: 'icon',
      }, {
        label: 'sdp.views.Dimensions',
        value: DIMENSIONS_VALUE,
        clickMobileType: 'icon',
        activeLabel: 'Dimensions',
      }, {
        label: 'sdp.views.Intersection',
        value: INTERSECTION_VALUE,
        clickMobileType: 'icon',
      },
    ]
    return {
      searchData: {
        selectRecording: '0/0',
        selectAll: true,
        loading: true,
        selectChange: (val) => {
          vm.setSearchChecks(val)
        }
      },
      TreeDataShopIdAndOutId: new TreeDataShopIdAndOutId(),
      tabList,
      COMMON_VALUE,
      selectedLabel: LOCATION_VALUE,
      saveSelectedLabel: '',

      hierarchiesSelectedPromise: {},
      dimensionsSelectedPromise: {},
      changeValue: this.$t('sdp.placeholder.plsInput'),
      locationSelectedLabel: this.$t('sdp.placeholder.plsInput'),
      allFlag: false,
      classStyle: false,
      // 树或标签数据
      optionArr: [],
      treeData: [],
      // type树列表
      typeList: [],
      typeTreeData: [],
      // 快选数据
      recordTotal: 0,
      pullDownData: [],
      filterParams: {},
      filterCheck: [],
      oldLanguaeAndCurrency: {
        shopIds: [],
      },
      // typeTreeData
      loading: false,
      filterText: '',
      pageTotal: 0,
      isShowLoadingFlag: false,
      // 比较数据
      compareStyle: LOCATION_COMPARE_TYPE.ONE,
      loctionData: {
        compareOne: [],
        compareTwo: [],
        dataOne: '',
        dataTwo: '',
      },
      // 看板跳转数据
      superLinkData: null,
      // type
      FINISH_TYPE,
      ReportCurrency: null,
      // isAllShopIdOutletIdSetData: false,
      savePropertyChooseData: {},
      getCompareData: false,
      saveLocationDataMsg: {},
      notInit: false,
      isInitPropertyList: true
    }
  },
  computed: {
    isLabelSelectTypeMulti() {
      return this.contentMap?.labelSelectType === LabelSelectType.Multi
    },
    isSupportQuick() {
      return !this.contentMap.disableQuick && (this.contentMap.options.paramType !== '2')
    },

    isSupportIntersection() {
      return !this.selectType && this.optionMap.paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT
    },

    isSupportHierarchies() {
      return !this.selectType && !this.isSupportIntersection
    },

    isSupportDimensions() {
      if (!this.isSupportHierarchies) return false

      const { labeled, paramType } = this.optionMap
      return labeled || paramType === '2'
    },

    isIconQuick() {
      return [
        PARAMS_PANEL_TYPE.QUICK,
        PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_BUSSINESS_CALENDAR,
        PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_LOCATION
      ].includes(this.paramsPanelTypeRef.value)
      // return this.paramsPanelTypeRef.value === PARAMS_PANEL_TYPE.QUICK_SELECTION_BY_LOCATION
    },

    tabs() {
      return this.tabList.filter(tab => {
        switch (tab.value) {
          case LOCATION_VALUE:
            return this.isSupportQuick
          case HIERARCHIES_VALUE:
            return this.isSupportHierarchies
          case DIMENSIONS_VALUE:
            return this.isSupportDimensions
          case INTERSECTION_VALUE:
            return this.isSupportIntersection
          default:
            console.log('异常场景!!!')
            return false
        }
      })
    },

    contentMap() {
      return this.paramElement.content
    },
    /**
     * Location 配置信息
     * @returns {*}
     */
    optionMap() {
      return this.contentMap.options
    },

    /**
     * 是否为交叉选择模式
     * @returns {boolean}
     */
    isIntersectionMode() {
      return this.optionMap.paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT
    },

    themeStatus() {
      return true
      // return this.$_getProp(this, 'themeData.screenMode', false) && this.commonData.isPreview
    },
    // 比较
    isCompare () {
      return this.$_getProp(this.optionMap, 'isCompare', false)
    },
    // 多选 1 单选 2
    selectType () {
      return this.contentMap.selectType === SelectType.Single
    },
    // 当前语言
    language () {
      return this.langCode
    },
    // 接口
    api () {
      return this.utils.api || function () { }
    },
    tenantId () {
      return this.utils.tenantId || ''
    },
    // 是否禁用快选
    isDisableQuick () {
      return this.$_getProp(this.contentMap, 'disableQuick', false) || this.optionMap.paramType === '2'
    },
    // 是否为移动端
    isMobile() {
      return this.utils.isMobile
    },
    // reverseElection() {
    //   return this.$_getProp(this.paramElement, 'content.options.reverseElection', false)
    // },
    viewLevelList() {
      const { viewLevelList = [] } = this.commonData.getViewLevelData()
      return viewLevelList
    },
    // 层级视角是否有收入中心
    needOutlet() {
      return this.viewLevelList.some(({ levelType }) => levelType === '2')
    },
    // 层级视角是否有物业
    needShopId() {
      return this.viewLevelList.some(({ levelType }) => levelType === '1')
    },
    isPullSelect() {
      this.setLocationSelectType()
      return this.optionMap.isPullSelect
    },
    isLazyLoading() {
      return this.levelData.isLazyLoading
    },
    selLocationData() {
      return this.contentMap?.selLocationData || {}
    },
    locationSelVal() {
      if (this.contentMap?.selLocationData && !this.isCompare) {
        return this.selLocationData[this.selectedLabel] ? `(${this.selLocationData[this.selectedLabel]})` : ''
      }
      return ''
    },
    isSearchAll() {
      return this.pullDownData.every(item => item.name.toUpperCase().indexOf(this.filterText.toUpperCase()) !== -1)
    },
  },
  watch: {
    'optionMap.labeled': {
      handler(val) {
        val && this.getTypeList()
      },
      immediate: true
    },
    'paramElement.elName': {
      handler(val) {
        const loction = this.contentMap.loction
        if (loction && this.isCompare) {
          let obj = Object.entries(loction).reduce((pre, [key, value]) => {
            pre[key === 'Compare' ? key : val] = value
            return pre
          }, {})
          this.$set(this.contentMap, 'loction', obj)
        }
      },
      immediate: true
    },
    'paramElement.content' (val, old: any = {}) {
      if (this.notInit) return
      const labelSelectType = val.labelSelectType
      const labelSelectTypeOld = old.labelSelectType
      const selectType = val.selectType
      const selectTypeOld = old.selectType
      const defaults = val.default
      const defaultOld = old.default
      const propertyScreening = val.propertyScreening
      const propertyScreeningOld = old.propertyScreening
      const propertyList = val.propertyList || []
      const propertyListOld = old.propertyList || []
      const disableQuick = val.disableQuick
      const disableQuickOld = old.disableQuick
      const { paramType, labeled, isCompare, isPullSelect } = val.options
      const { paramType: paramTypeOld, labeled: labeledOld, isCompare: isCompareOld } = old.options
      const isIntersectionMode = val.options.paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT
      const isIntersectionModeOld = old.options.paramFilterLableType === PARAM_FILTER_LABLE_TYPE_LABEL_SELECT
      const isPropertyListEquals = this.$_equalsObj(propertyList, propertyListOld)
      const defaultTabSelectType = val.defaultTabSelectType || COMMON_VALUE.LOCATION_VALUE
      const defaultTabSelectTypeOld = old.defaultTabSelectType || COMMON_VALUE.LOCATION_VALUE
      const saveActiveTabSelectType = val.saveActiveTabSelectType
      const saveActiveTabSelectTypeOld = old.saveActiveTabSelectType
      const tile = val.tile
      const tileOld = old.tile
      const tileSet = val.tileSet
      const tileSetOld = old.tileSet
      if (!isPropertyListEquals || propertyScreening !== propertyScreeningOld) {
        this.pullDownData = []
        this.treeData = []
        this.savePropertyChooseData = {}
        this.optionArr = []
        this.allFlag = false
        this.getCurrentRefs('tree') && this.getCurrentRefs('tree').setCheckedKeys([])
      }
      if ((paramType !== paramTypeOld && (isPullSelect || paramType === '1')) ||
        (defaultTabSelectType !== defaultTabSelectTypeOld) ||
        (saveActiveTabSelectType !== saveActiveTabSelectTypeOld) ||
        !isPropertyListEquals || propertyScreening !== propertyScreeningOld ||
        labeled !== labeledOld ||
        isCompare !== isCompareOld ||
        selectType !== selectTypeOld ||
        isIntersectionMode !== isIntersectionModeOld ||
        tile !== tileOld ||
        tileSet !== tileSetOld ||
        defaults !== defaultOld || (disableQuick !== disableQuickOld && (isPullSelect || !disableQuick)) || !isPropertyListEquals ||
        labelSelectType !== labelSelectTypeOld
      ) {

        this.$nextTick(() => {
        if (isIntersectionModeOld && isIntersectionMode) {
          this.$delete(this.contentMap, 'intersectionData')

          const intersectionLayoutRef = this.getCurrentRefs('intersectionLayoutRef')

          intersectionLayoutRef && (() => {
            intersectionLayoutRef.resetAllTreeOptions()
            intersectionLayoutRef.restoreTreeOpts(this.isMobile)
          })()
        }
        })

        initializationData[TYPE_SUPERSETTING.INIT].call(this)
      }
    },
    allFlag: {
      handler (val) {
        this.clickAll(val)
      },
      immediate: true
    },
    recordTotal() {
      this.getSelectedLocationData()
    },
    // 修改content中的changeValue
    changeValue(val) {
      // this.$set(this.contentMap, 'changeValue', val)
      this.$set(this.contentMap, 'changeValue', val.includes(this.$t('sdp.views.All')) ? 'ALL' : val)
    },
    locationSelectedLabel(val) {
      this.$set(this.contentMap, 'locationSelectedLabel', val)
    },
    needOutlet: {
      handler(val) {
        if (!val) {
          const options = this.optionMap
          this.$set(options, 'outletIds', [])
          options.dataSetFields.forEach(item => {
            this.$set(item.outletsField, 'columnName', '')
          })
        }
      },
      // immediate: true
    },
    // 修改比较多语言丢失问题
    isCompare(val, old) {
      if (!old) {
        val && this.$set(this.contentMap, 'LocationCompareName', 'Compare')
      }
    },
  },
  created () {
    if (this.utils.isPcMobileEdit && !this.utils.isMobile) return
    const { options: { isCompare, paramType }, default: defaults, disableQuick, isSelectedTop, selLocationData, saveData, LocationCompareName, hierarchies = 'Hierarchies', dimensions = 'Dimensions' } = this.contentMap
    this.$set(this.contentMap, 'hierarchies', hierarchies)
    this.$set(this.contentMap, 'dimensions', dimensions)
    // 兼容老数据 设置默认是否勾选
    if (defaults === undefined) {
      this.contentMap.default = true
    }
    // 兼容老数据 设置选中项置顶效果isSelectedTop
    if (isSelectedTop === undefined) {
      this.contentMap.isSelectedTop = !disableQuick && paramType !== '2'
    }
    if (this.contentMap.saveActiveTabSelectType === undefined && saveData) {
      let curSaveData = JSON.parse(saveData)
      this.$set(this.contentMap, 'saveActiveTabSelectType', curSaveData?.activeName || LOCATION_VALUE)
    }
    if (selLocationData === undefined) {
      this.$set(this.contentMap, 'selLocationData', {})
    }
    if ((isCompare && !LocationCompareName) || LocationCompareName === 'Location Compare' || LocationCompareName === 'LocationCompare') {
      this.$set(this.contentMap, 'LocationCompareName', 'Compare')
    }
    // 组件等待状态
    this.finishHook(FINISH_TYPE.AWAIT)
    this.initLabelPickerConfig()
  },
  async mounted () {
    if (this.utils.isPcMobileEdit && !this.utils.isMobile) return
    const { data, type, superLinkData, isSubscription } = this.$_JSONClone(this.beforeHook(this.paramElement))
    this.superLinkData = superLinkData
    if (this.viewLevelList.some(({ levelType }) => levelType === '1')) {
      await this.initPropertyList().then(res => {
        this.isInitPropertyList = false
      })
      await initializationData[type].call(this, data, isSubscription)
    } else {
      // 没有物业未完成
      this.cleanData()
      this.finishHook(FINISH_TYPE.UNFINISH)
    }
    // 处理脏数据
    const { pageObj } = this.contentMap
    if (pageObj) pageObj.page = 1
    this.setElBind()
    this.manageConflict()
    // 是懒加载在监听onScroll方法
    if (this.isLazyLoading) { // 3857ding
      this.$set(this.optionMap, 'filterShopFlag', false)
    } else {
      if (this.contentMap.propertyScreening) {
        this.$set(this.optionMap, 'filterShopFlag', true)
      }
    }
    this.sdpBus.$on(EVENT_BUS.RUN_SET_LOCATION_DATA, this.setLocationData)
    this.sdpBus.$on(EVENT_BUS.PULLING_DOWM_UPDATE, this.pullingDowmUpdate)
  },
  destroyed () {
    this.sdpBus.$off(EVENT_BUS.RUN_SET_LOCATION_DATA, this.setLocationData)
    this.sdpBus.$off(EVENT_BUS.PULLING_DOWM_UPDATE, this.pullingDowmUpdate)
    const eventData = new EventData({
      source: TYPE_PARAM_ELEMENT.LOCATION_NEW,
      target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
      targetFn: 'setLanguageSwitching',
      type: 'setLanguageSwitching',
      data: null
    })
    this.$emit('eventBus', eventData)
  },
  methods: {
    pullingDowmUpdate({ ids, refreshObj, callback }) {
      const { id } = this.paramElement

      if (!ids.includes(id)) return

      console.log('onPullingDown - pullingDowmUpdate', ids, id, refreshObj)

      if (this.isMobile && refreshObj.refreshStatus === '0') {
        initializationData.init.call(this)
        this.pullingDowmCallback = (state) => {
          if (state === FINISH_TYPE.AWAIT) return
          callback(id)
          this.pullingDowmCallback = null
        }
        // this.cleanData()
        // this.allFlag = true
        // this.selectedLabel = COMMON_VALUE.LOCATION_VALUE
        // setTimeout(() => {
        //   const locationDownTileMobile = this.$refs.locationDownTileMobile
        //   if (this.contentMap.tile && Array.isArray(locationDownTileMobile)) {
        //     locationDownTileMobile[0].setDefaultValue(true)
        //     this.finishHook(this.FINISH_TYPE.FINISH)
        //   } else {
        //     this.getAllShopIdOutletId()
        //   }

        //   callback(id)
        // })
      } else {
        callback(id)
      }
    },
    // 用sql语句初始化propertyList数据
    async initPropertyList() {
      const { paramSelectCollection = [], propertyScreening = false } = this.contentMap
      if (!propertyScreening) return

      if (!paramSelectCollection.length) return

      const obj = paramSelectCollection[0]
      const data = await singleFieldPreview(this.api, Object.assign(obj, { tenantId: this.tenantId }))

      if (!data) return
      console.log(data, 'propertyList')

      this.$set(this.contentMap, 'propertyList', data)
      this.$set(this.optionMap, 'filterShopIds', data)
    },
    getLabel(item) {
      switch (item.value) {
        case LOCATION_VALUE:
          // return this.paramElement.elName !== 'Location' ? this.paramElement.elName : this.$t('sdp.views.Location')
          return this.paramElement.elName
        case DIMENSIONS_VALUE:
          return this.paramElement.content.dimensions !== 'Dimensions' ? this.paramElement.content.dimensions : this.$t('sdp.views.dimensions')
        case HIERARCHIES_VALUE:
          return this.paramElement.content.hierarchies !== 'Hierarchies' ? this.paramElement.content.hierarchies : this.$t('sdp.views.Hierarchy')
        default:
          return this.$t(item.label)
      }
    },

    initLabelPickerConfig() {
      const { isLabelPicker, labelPickerId: labelPickerIds = [], labelPickerData = [] } = this.contentMap.labelPickerConfig || {}
      let originLabelPickerData = this.contentMap.labelPickerConfig ? this.contentMap.labelPickerConfig.originLabelPickerData : undefined
      // 初始化快选
      if (isLabelPicker) {
        this.getTypeList().then(res => {
          if (!originLabelPickerData) {
            this.contentMap.labelPickerConfig.originLabelPickerData = res
          }
          originLabelPickerData = originLabelPickerData || res
          const labelPickerId = res.filter(v => labelPickerIds.includes(v.id)).map(({ id }) => id)
          let newlabelPickerData = originLabelPickerData.map(({ id, typeName }) => {
            if (labelPickerId.includes(id)) {
              return { id, typeName }
            }
          }).filter(e => e)
          this.$set(this.contentMap.labelPickerConfig, 'isLabelPicker', !!labelPickerId.length)
          this.$set(this.contentMap.labelPickerConfig, 'labelPickerId', labelPickerId)
          this.$set(this.contentMap.labelPickerConfig, 'labelPickerData', newlabelPickerData)
          if (!this.contentMap.labelPickerConfig.isLabelPicker) {
            this.delLocationQuick()
          }
        })
      } else {
        this.delLocationQuick()
      }
    },
    // 删除快选
    delLocationQuick() {
      const data = this.board.paramsPanelList.find(item => item.active).content.find(e => e.type === TYPE_PARAM_ELEMENT.LOCATION_QUICK)
      if (data) {
        const eventData = new EventData({
          source: 'locationNewSetting',
          data,
          target: 'tab',
          targetFn: 'removeParams',
        })
        this.$emit('eventBus', eventData)
      }
    },
    getCurrentRefs(name) {
      const ref = this.$refs[name]
      if (ref) {
        return Array.isArray(ref) ? ref[0] : ref
      }
      console.log(ref)
    },
    // 看板元素数据集发生变化时
    setDatasStatus() {
      setDatasStatusByLocation({ paramElement: this.paramElement, elList: this.board.elList })
    },
    // 设置树的ChangeValue
    setTreeChangeValue(finish) {
      if (finish === FINISH_TYPE.AWAIT) return

      const { options, downSaveData, loction } = this.contentMap
      const { shopIds } = options
      let locationSelVal = ''
      // 移动端显示信息为全部的设置
      let setAllLocation = (this.isCompare || (Array.isArray(loction) && loction.find(item => item === 'ALL'))) && this.selectedLabel === COMMON_VALUE.LOCATION_VALUE
      const selectedLabel = this.selectedLabel
      if (this.contentMap?.selLocationData && !this.isCompare) {
        locationSelVal = this.selLocationData[selectedLabel] ? `(${this.selLocationData[selectedLabel]})` : ''
      }
      const mobileSelDataMsg = `(${this.selLocationData[selectedLabel]} ${this.$t('sdp.dialog.selected')})`
      if (this.isPullSelect) {
        this.$set(options, 'dimensionIds', [])
        this.$set(options, 'labelIds', [])
        let allFlag = this.allFlag
        // 比较拿第一个数据做处理
        if (this.isCompare) {
          const { dataOne } = downSaveData ? JSON.parse(downSaveData) : this.loctionData
          allFlag = !!dataOne['allFlag']
        }
        this.classStyle = shopIds.length || allFlag
        let changeValue = this.changeValue
        if (allFlag && setAllLocation) {
          changeValue = 'ALL'
          this.locationSelectedLabel = this.$t('sdp.views.All') + locationSelVal
        } else if (shopIds.length > 1) {
          changeValue = this.$t('sdp.views.multiple')
          this.locationSelectedLabel = this.isMobile && locationSelVal ? mobileSelDataMsg : this.$t('sdp.views.multiple') + locationSelVal
        } else if (shopIds.length === 1) {
          this.pullDownData.some(item => item.id === shopIds[0] && (changeValue = item.name) && (this.locationSelectedLabel = item.name))
          if (!this.selectType && !this.isCompare) {
            this.locationSelectedLabel = this.isMobile ? ` ${mobileSelDataMsg}` : `${changeValue} ` + locationSelVal
          }
        } else if (!shopIds.length) {
          changeValue = this.$t('sdp.placeholder.plsInput')
          this.locationSelectedLabel = this.$t('sdp.placeholder.plsInput')
        }
        this.changeValue = changeValue
        // if (changeValue === this.$t('sdp.placeholder.plsInput')) {
        //   this.locationSelectedLabel = this.$t('sdp.placeholder.plsInput')
        // }
      } else {
        // 移动端显示信息为全部的设置
        if (this.allFlag && setAllLocation) {
          this.changeValue = 'ALL'
          this.locationSelectedLabel = this.$t('sdp.views.All') + locationSelVal
        } else if (shopIds.length > 1) {
          this.changeValue = this.$t('sdp.views.multiple')
          this.locationSelectedLabel = this.isMobile && locationSelVal ? mobileSelDataMsg : this.$t('sdp.views.multiple') + locationSelVal
        } else if (shopIds.length === 1) {
          getNewLocationShopId.call(this, { page: 1, shopIds }).then(res => {
            this.changeValue = res.rows[0].name
            this.locationSelectedLabel = res.rows[0].name
            !this.filterText && this.$set(this.contentMap, 'recordTotal', res.recordTotal || res.rows.length)
            if (!this.selectType && !this.isCompare) {
              this.locationSelectedLabel = this.isMobile ? ` ${mobileSelDataMsg}` : `${this.changeValue} ` + locationSelVal
            }
          })
        } else if (!shopIds.length) {
          this.changeValue = this.$t('sdp.placeholder.plsInput')
          this.locationSelectedLabel = this.$t('sdp.placeholder.plsInput')
        }
        this.classStyle = !!shopIds.length
        this.initDownDataState()
      }
    },
    // 初始化状态
    initDownDataState() {
      const { default: defaults } = this.contentMap
      this.allFlag = defaults
      const tree = this.getCurrentRefs('tree')
      if (tree) {
        tree.getCurrentKey() && tree.setCurrentKey(null)
        tree.setCheckedKeys(defaults ? (this.isLazyLoading ? [] : this.pullDownData.map(item => item.ids)) : [])
      }
      this.initCompareData()
      defaults && this.setLoctionData(this.getData())
    },
    // 解决冲突
    manageConflict() {
      const arr = this.board.elList.map(item => item.id)
      if (this.contentMap.bindElements) {
        const arrs = this.contentMap.bindElements.filter(item => arr.includes(item))
        this.$set(this.contentMap, 'bindElements', arrs)
      }
      // 清除旧数据
      const arrs = [
        'outledLabel_pull',
        'outledLabel_tree',
        'shopLabel_pull',
        'shopLabel_tree',
      ]
      arrs.forEach(name => {
        this.$delete(this.contentMap, name)
      })
    },
    // 处理参数组件绑定
    setElBind(ids = []) {
      if (this.utils.isPcMobile) return
      setElBindByLocation({ ids, paramElement: this.paramElement, elList: this.board.elList })
    },
    // 获得看板跳转shopid
    getSuperLinkId(shopIds) {
      const { options, superLink } = this.superLinkData || {}
      if (options && superLink) {
        const { parameterField, associateParamsSetting = {} } = options
        // 通过关联目标看板筛选
        let { isAssociateParams = false, associateParamsData = [] } = associateParamsSetting
        if (isAssociateParams && associateParamsData.includes(this.paramElement.id)) {
          // parameterField
          const value = superLink.dataSets[0].values[0]
          if (value && !shopIds.includes(value)) {
            shopIds.push(value)
          }
          return shopIds
          // return superLink.dataSets[0].values
        }
        // SHOP_ID查找
        if (Object.keys(superLink).length && parameterField.includes('SHOP_ID') && superLink.dataSets.some(e => e.columnName === 'SHOP_ID')) {
          const value = superLink.dataSets.find(e => e.columnName === 'SHOP_ID').values[0]
          if (value && !shopIds.includes(value)) {
            shopIds.push(value)
          }
        }
      }
      return shopIds
    },
    // app与pc共用方法
    async openPopoverMixin(name = 'locationTree') {
      // 这一段没什么用 如果有问题可以考虑移除
      if (this.isMobile && name === 'Hierarchies') {
        const ref = this.getCurrentRefs(name)
        ref && (ref.treeLoading = true)
      }
      // pc 和 移动 都需要执行
      await this.getOptionData()
      this.$nextTick(() => {
        this.$_getProp(this.getCurrentRefs(name), 'getRefs.openInitData', function() {})()
      })
    },
    setMessage(txt, elementId = 'mobileElementPanel') {
      if (this.isMobile) {
          this.getBoardUnderIdElement(`#${elementId}`).querySelectorAll('.cube-toast').forEach(e => {
            e.style.display = 'none'
          })

        let confrimToast = this.$createToast({
          type: 'warn',
          time: CREATE_TOAST_TIME,
          txt,
        })
        // this.$_insertElement(document.getElementById(elementId), confrimToast.$el)
        this.$_insertElement(this.getBoardUnderIdElement(`#${elementId}`), confrimToast.$el)
        confrimToast.show()

          setTimeout(() => {
            confrimToast.hide()
            confrimToast.$el.style.display = 'none'
          }, 3000)
      } else {
        this.$message({
          message: txt,
          duration:10000,
          customClass:'__message__custom__warn'
        });
      }
    },
    // 完成时获取shopId调用语言货币接口
    async setLanguaeAndCurrency () {
      const { currencyType, languageStatus } = this.tenantData
      if (currencyType === '2' && languageStatus === '0') return
      let shopId = []
      if (this.isCompare) {
        const { currShopIds = [], preShopIds = [] } = this.contentMap.options.locationArea
        const arr = [...new Set([...currShopIds, ...preShopIds])]
        const isAll = arr.some(item => item === locationLangAll)
        // arr小于2个以上的全选，调用使用arr
        if (this.pullDownData.length === 1) {
          shopId = this.pullDownData.map(item => item.id)
        } else {
          shopId = isAll ? [locationLangAll] : arr
        }
      } else {
        const shopIds = this.contentMap.options.shopIds
        // shopIds小于2个以上的全选，调用使用shopIds
        const filterText = this.isLazyLoading ? '' : this.filterText
        console.log(filterText, 'filterText')
        if (this.allFlag && this.isPullSelect && !filterText) {
          // shopId = this.allFlag && this.isPullSelect && shopIds.length > 1 ? ['ALL'] : shopIds
          if (this.pullDownData.length === 1) {
            shopId = this.pullDownData.map(item => item.id)
          } else {
            shopId = [locationLangAll]
          }
        } else {
          shopId = shopIds
        }
      }
      await this.getLanguaeAndCurrency(shopId)
    },
    setLocationData(finish, type = this.paramsType) {
      const { isLabelPicker } = this.contentMap.labelPickerConfig || {}
      if (type !== RUN_TYPE.handRun && isLabelPicker) return

        const val = {
          shopIds: [...this.optionMap.shopIds].sort(),
          outletIds: [...this.optionMap.outletIds].sort(),
          id: this.paramElement.id,
          componentStatus: finish,
          type: this.paramElement.type
        }
        const oldVal = this.locationData
        if (this.$_equalsObj(oldVal, val)) return
        this.sdpBus.$emit(EVENT_BUS.SET_LOCATION_DATA, val, type)
    },
    async finishHookFun(finish, message = '') {
      await this.finishHook(finish, message)
    },
    // 完成钩子函数
    async finishHook(finish, message = '') {
      // this.setLocationData(finish)
      !message && this.setTreeChangeValue(finish)
      const { options: { shopIds, outletIds }, downSaveData } = this.contentMap
      if (FINISH_TYPE.FINISH === finish) {
        if (this.isCompare && this.isPullSelect) {
          const { dataOne } = downSaveData ? JSON.parse(downSaveData) : this.loctionData
          if (dataOne) {
            const { shopIds = [], outletIds = [], allFlag } = dataOne
            const bool = !shopIds.length && !outletIds.length && !allFlag
            bool && (finish = FINISH_TYPE.UNFINISH)
          }
        } else {
          const bool = !shopIds.length && !outletIds.length && !this.allFlag
          bool && (finish = FINISH_TYPE.UNFINISH)
        }
      }
      if (FINISH_TYPE.FINISH === finish || FINISH_TYPE.UNFINISH === finish) {
        await this.setLanguaeAndCurrency()

        this.TreeDataShopIdAndOutId.saveTreeDataShopIdAndOutId(this, this.contentMap)
      }
      this.setLocationData(finish)
      this.$emit('finishHook', { id: this.paramElement.id, type: finish })
      this.pullingDowmCallback?.(finish)
    },
    // tree层级树
    async getOptionData () {
      if (!this.optionArr.length) {
        this.optionArr = await getTreeList.call(this, this.tenantId)
      }
      return this.optionArr
    },
    async getNewHierarchyDimensionTree2({ id, cb }) {
      if (id) {
        if (Object.keys(this.savePropertyChooseData).some(key => key === id)) {
          this.treeData = this.$_JSONClone(this.savePropertyChooseData[id])
        } else {
          this.treeData = await getNewHierarchyDimensionTree2.call(this, { id })
          this.savePropertyChooseData[id] = this.treeData
        }
      } else {
        this.treeData = []
      }

      cb && cb(id, this.treeData)

      return this.treeData
    },
    async getNewHierarchyDimensionTreeApp({ id, cb }) {
      // const data = require('./testData.json')
      if (id) {
        if (Object.keys(this.savePropertyChooseData).some(key => key === id)) {
          this.treeData = this.$_JSONClone(this.savePropertyChooseData[id])
        } else {
          const data = await getNewHierarchyDimensionTreeApp.call(this, { id })
          if (data?.[0]?.children?.length) {
            this.treeData = data
            this.savePropertyChooseData[id] = this.treeData
          } else {
            this.treeData = []
          }
        }
      } else {
        this.treeData = []
      }

      cb && cb(id, this.treeData)

      return this.treeData
    },
    // type层级树
    async getTypeList() {
      if (!this.typeList.length) {
        this.typeList = await getLabelTypeList.call(this)
      }
      return this.typeList
    },
    async getLabelListByType({ id, cb, reloadData = false }) {
      if (id) {
        if (!reloadData && Object.keys(this.savePropertyChooseData).some(key => key === id)) {
          this.typeTreeData = this.$_JSONClone(this.savePropertyChooseData[id])
        } else {
          this.typeTreeData = await getLabelListByType.call(this, { dimensionTypeId: id, languageCode: this.language, })
          this.savePropertyChooseData[id] = this.typeTreeData
        }
      } else {
        this.typeTreeData = []
      }

      cb && cb(id, this.typeTreeData)
      return this.typeTreeData
    },
    async getLabelListByTypeApp({ id, cb, reloadData = false }) {
      if (id) {
        if (!reloadData && Object.keys(this.savePropertyChooseData).some(key => key === id)) {
          this.typeTreeData = this.$_JSONClone(this.savePropertyChooseData[id])
        } else {
          const data = await getLabelListByTypeApp.call(this, { dimensionTypeId: id, languageCode: this.language, })
          if (data?.[0]?.children?.length) {
            this.typeTreeData = data
            this.savePropertyChooseData[id] = this.typeTreeData
          } else {
            this.typeTreeData = []
          }
        }
      } else {
        this.typeTreeData = []
      }

      cb && cb(id, this.typeTreeData)
      return this.typeTreeData
    },
    initDownData() {
      const {
        options: { isPullSelect, shopIds, outletIds },
        loction = [],
        changeValue,
        isSelectedTop,
      } = this.contentMap

      const tree = this.getCurrentRefs('tree')

      const isCompare = this.isCompare

      if (!isPullSelect) {
        this.initDownDataState()
        return
      }
      this.compareStyle = LOCATION_COMPARE_TYPE.ONE

      if (isCompare) {
        const downSaveData = this.$_getProp(this, 'paramElement.content.downSaveData', false)
        if (downSaveData) {
          this.loctionData = JSON.parse(downSaveData)
        } else {
          this.$set(this.contentMap, 'downSaveData', JSON.stringify(this.loctionData))
        }
        this.compareFun(this.compareStyle)
      } else {
        // shopIds 下面没有子集需要保留勾选ids的 否则outletIds全勾选上，会勾选夫shopIds
        let setCheckedKeys = [
          ...shopIds.map(id => `${KEYS.parent}${id}`).filter(key => {
            const item = this.pullDownData.find(item => item.ids === key)
            if (item) {
              return !item.children?.length
            }
            return false
          }),
          ...outletIds.map(id => `${KEYS.son}${id}`)
        ]
        this.allFlag = changeValue === 'ALL'
        if (this.allFlag) {
          setCheckedKeys = this.isLazyLoading ? [] : this.pullDownData.map(item => item.ids)
        }
        tree && tree.setCheckedKeys(setCheckedKeys)
        this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, shopIds, outletIds, loction)
        // this.getSelectedLocationData()
      }
    },
    // 下拉弹窗打开触发事件
    async openShowPopover () {
      await this.getPullDownData()
      // location排序
      this.pullDownDataSort()
      setTimeout(() => {
        this.initDownData()
      })
      this.getCompareData = true
    },
    pullDownDataSort() {
      if (!this.contentMap.options.isPullSelect) return

      this.$nextTick(() => {
        const tree = this.getCurrentRefs('tree')
        const { isSelectedTop, options } = this.contentMap
        if (isSelectedTop) {
          if (this.isCompare && this.utils.isMobile) {
            if ((this.compareStyle === 1 && this.loctionData?.dataOne?.shopIds?.length) || (this.compareStyle === 2 && this.loctionData?.dataTwo?.shopIds?.length)) {
              const shopIds = this.compareStyle === 1 ? this.loctionData.dataOne.shopIds || [] : this.loctionData.dataTwo.shopIds || []
              const data = this.pullDownData.filter(item => item.ids.includes(KEYS.parent) && shopIds.includes(item.id))
              this.pullDownData = this.setDataId([], data)
            }
          } else {
            if (options?.shopIds.length) {
              const data = this.pullDownData.filter(item => item.ids.includes(KEYS.parent) && options.shopIds.includes(item.id))
              this.pullDownData = this.setDataId([], data)
            }
          }
        }
        // if (options?.shopIds.length && isSelectedTop) {
        //   const data = this.pullDownData.filter(item => item.ids.includes(KEYS.parent) && options.shopIds.includes(item.id))
        //   this.pullDownData = this.setDataId([], data)
        // }
        if (tree) {
          // const checkedNodes = tree.getCheckedNodes(false, true)
          const checkedKeys = tree.getCheckedKeys()
          // const data = checkedNodes.filter(({ ids }) => ids.includes(KEYS.parent) && checkedKeys.includes(ids))
          // this.pullDownData = isSelectedTop && this.setDataId([], data)
          tree.setCheckedKeys(checkedKeys)
          // tree.forceUpdate()
        }
      })
    },
    // 获得下拉框数据
    async getPullDownData(shopIds = [], outletIds = []) {
      this.loading = true
      let change = false
      if (!this.pullDownData.length) {
        let params = {
          page: 1,
          shopIds: this.getSuperLinkId(this.$_JSONClone(shopIds)),
          shopName: this.filterText ? this.filterText : undefined,
          isSelectedTop: this.contentMap.isSelectedTop
        }
        const {
          currentPage = 0,
          lastPage = 0,
          rows = [],
          recordTotal,
          shopOutletCntMap = {},
        } = await getNewLocationShopId.call(this, params) || {}
        if (params.page === 1) {
          this.$set(this.optionMap, 'shopOutletCntMap', shopOutletCntMap)
        }
        if (!this.filterText) {
          const recordVal = recordTotal || rows.length
          if (recordVal !== this.contentMap.recordTotal) {
            change = true
          }
          this.$set(this.contentMap, 'recordTotal', recordVal)
        }
        this.isShowLoadingFlag = currentPage >= lastPage
        rows.forEach((item, index) => {
          if (item.children.length) {
            item.children.forEach((val) => {
              this.$set(val, 'ids', `${KEYS.son}${val.id}`)
              // if (checkedKeys && outletIds.includes(val.id)) {
              //   checkedKeys.push(val.ids)
              // }
            })
          }
          this.$set(item, 'ids', `${KEYS.parent}${item.id}`)
          this.$set(item, 'sortNum', index)
          // if (checkedKeys && shopIds.includes(item.id)) {
          //   if (!item.children.length) {
          //     checkedKeys.push(item.ids)
          //   }
          // }
        })
        // this.pullDownData = rows.sort((a, b) => a.name.localeCompare(b.name, 'en'))
        this.pullDownData = !this.contentMap.isSelectedTop ? rows : rows.sort((a, b) => (a, b) => a.sortNum - b.sortNum)
        // this.pullDownData = rows
        let copyData = []
        this.pullDownData.forEach(p => {
          let son = p.children.map(s => {
            return { id: s.id, name: s.name }
          })
          copyData.push({ id: p.id, name: p.name, children: son })
        })
        this.$set(this.contentMap, 'pullDownData', copyData)
      }
      if (this.filterText) {
        this.searchTreeContent()
      }
      if (change) {
        const { shopIds: shopIdList, outletIds: outletIdList, loction } = this.getData()
        this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, shopIdList, outletIdList, loction)
        // this.selectedLabel === LOCATION_VALUE && this.setTreeChangeValue()
        if (this.selectedLabel === LOCATION_VALUE && this.contentMap.saveData) {
          const saveDataVal = JSON.parse(this.contentMap.saveData) || {}
          if (saveDataVal.activeName === LOCATION_VALUE) {
            this.setTreeChangeValue()
          }
        }
      } else {
        this.getSelectedLocationData()
      }
      this.loading = false
    },
    // 保存数据
    async dataKeepTree({ saveCompareData, saveData }, cb) {
      // TODO比较不支持跳转
      await this.getOptionData()
      if (this.optionArr.length) {
        const { id = '' } = this.optionArr[0]
        let treeData = []
        if (this.isMobile) {
          let idVal = id
          if (!this.isIntersectionMode) {
            if (saveData) {
              const saveDataVal = JSON.parse(saveData) || {}
              if (saveDataVal && saveDataVal.selectedTree && saveDataVal.activeName === 'Hierarchies') {
                idVal = saveDataVal.selectedTree
              }
            }
            treeData = await this.getNewHierarchyDimensionTreeApp({ id: idVal })
          } else {
            treeData = await this.getNewHierarchyDimensionTreeApp({ id: idVal })
          }
          // treeData = await this.getNewHierarchyDimensionTreeApp({ id })
        } else {
          treeData = await this.getNewHierarchyDimensionTree2({ id })
        }
        // const treeData = await this.isMobile ? this.getNewHierarchyDimensionTreeApp({ id }) : this.getNewHierarchyDimensionTree2({ id })
        if (this.isIntersectionMode) {
          this.finishHook(FINISH_TYPE.AWAIT)
          const intersectionLayoutRef = this.getCurrentRefs('intersectionLayoutRef')
          const hierarchiesTreeOptions = intersectionLayoutRef.hierarchiesTreeOpts.treeOpts
          const { keys, nodes } = (function getKeysAndNodes(list = [], keys = [], nodes = []) {
            return list.reduce(({ keys, nodes }, item) => {
              // 第一次需要特殊处理一下
              keys.push(item.level === 1 ? item.dimensionTypeId : item.id)
              nodes.push(item)
              if (Array.isArray(item.children) && item.children.length) {
                getKeysAndNodes(item.children, keys, nodes)
              }
              return { keys, nodes }
            }, { keys, nodes })
          })(treeData, [], [])
          // hierarchiesTreeOptions.treeId = id
          hierarchiesTreeOptions.checkedKeys = keys
          hierarchiesTreeOptions.checkedNodes = nodes
          intersectionLayoutRef.restoreTreeOpts()
          if (this.contentMap.default) {
            intersectionLayoutRef.$refs?.hierarchiesTreeRef?.setCheckedKeys?.(keys)
          }
          intersectionLayoutRef.confirm({ isNeedUpdateSelectedInfo: true })
        } else {
          if (saveCompareData && this.isCompare) {
            this.$set(this.contentMap, 'saveCompareData', saveCompareData)
          } else if (saveData) {
            this.$set(this.contentMap, 'saveData', saveData)
          }
          typeof cb === 'function' && cb()
        }
      }
    },
    setLoctionData({ shopIds = [], outletIds = [], loction = ['ALL'], keys = [] }) {
      if (this.isCompare) {
        const filterText = this.isLazyLoading || this.isSearchAll ? '' : this.filterText
        const bool = this.compareStyle === LOCATION_COMPARE_TYPE.ONE
        this.$set(this.loctionData, bool ? 'compareOne' : 'compareTwo', loction)
        this.$set(this.loctionData, bool ? 'dataOne' : 'dataTwo', {
          shopIds,
          outletIds,
          keys,
          allFlag: !!this.allFlag,
        })
      }
      this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, shopIds, outletIds, loction)
    },
    getSelLocationValue() {
      this.setLoctionData(this.getData())
    },
    getSelectedLocationData(type = COMMON_VALUE.LOCATION_VALUE, shopIds = [], outletIds = [], loction = ['ALL']) {
      // let pullDownData = this.pullDownData.length ? this.pullDownData : this?.contentMap?.pullDownData || []
      if (this.selectType) {
        this.$set(this.contentMap?.selLocationData, COMMON_VALUE.LOCATION_VALUE, '')
        return
      }
      // 移动端显示信息为全部的设置
      let setAllLocation = Array.isArray(loction) && loction.find(item => item === 'ALL')
      let curListLength = 0
      if (this.allFlag) {
        curListLength = this.contentMap.recordTotal
      } else {
        curListLength = setAllLocation ? shopIds.length : loction.filter(item => {
          return !item.includes(':')
        })?.length || 0
        // shopIds.forEach(shop => {
        //   getCurShopIds(shop)
        // })
      }
      let locationMsg = `${curListLength}/${this.contentMap.recordTotal}`
      this.$set(this.contentMap?.selLocationData, COMMON_VALUE.LOCATION_VALUE, locationMsg)
      console.log(locationMsg, curListLength, setAllLocation, loction, 'locationMsg')
    },
    cleanData() {
      const content = this.contentMap
      const { options, pageObj } = content
      let keys = ['shopIds', 'outletIds', 'labelIds', 'dimensionIds']
      let delKeys = ['saveData', 'intersectionData', 'saveCompareData', 'downSaveData']
      pageObj && (pageObj.page = 1)
      this.allFlag = false
      this.changeValue = this.$t('sdp.placeholder.plsInput')
      this.locationSelectedLabel = this.$t('sdp.placeholder.plsInput')
      this.$set(content, 'changeValue', this.changeValue)
      this.$set(content, 'loction', [])
      this.$set(options, 'locationArea', {
        currShopIds: [],
        preShopIds: [],
        currOutletIds: [],
        preOutletIds: [],
        shopIdField: 'shop_id',
        oletIdField: 'olet_id',
      })
      delKeys.forEach(key => {
        this.$delete(content, key)
      })
      keys.forEach(key => {
        this.$set(options, key, [])
      })
    },
    // 初始化下拉compare数据
    initCompareData () {
      if (!this.isCompare) return
      this.compareStyle = LOCATION_COMPARE_TYPE.ONE
      this.loctionData = {
        compareOne: [],
        compareTwo: [],
        dataOne: '',
        dataTwo: '',
      }
      this.$delete(this.contentMap, 'downSaveData')
    },
    // 设置 id
    setIds({ checkedKeys, activeName = 'Hierarchies', treeDataShopIdAndOutId }, type) {
      this.initCompareData()
      const bool = activeName === 'Hierarchies'
      this.optionMap.isPullSelect = false
      this.$set(this.optionMap, 'labelIds', bool ? [] : checkedKeys)
      this.$set(this.optionMap, 'dimensionIds', bool ? checkedKeys : [])
      this.TreeDataShopIdAndOutId.setDirectTreeDataShopIdAndOutId(treeDataShopIdAndOutId)
      this.finishHook(FINISH_TYPE.FINISH)
      type !== 'init' && this.closePage()
    },

    closePage(type) {
      console.log('YL 执行closePage', type)
      if (this.isMobile) {
        this.$emit('closePage', type)
      } else {
        this.visible = false
        this.isPcClickBottom = true
      }
      this.paramElement.content.saveActiveTabSelectType = this.selectedLabel
    },

    getTypeShowList(leafIds) {
      // if (this.isMobile) {
      //   return getNewHierarchyDimensionTreeApp.call(this, { id: leafIds.join(','), tenantId: this.tenantId })
      // }
      return getTypeShowList.call(this, { id: leafIds.join(','), tenantId: this.tenantId })
    },
    getTagShopAndOutletTree(leafIds) {
      return getTagShopAndOletTree.call(this, { ids: [...leafIds], tenantId: this.tenantId })
    },
    getSelectedShopIds(treeOpts, getShopInfoApi) {
      const shopIdSet = new Set()
      const outletIdSet = new Set()
      let shopIds = []
      let outletIds = []

      const leafIds = treeOpts.checkedNodes
        .filter(item => !item.children.length)
        .map(item => item.id)

      if (leafIds.length === 0) {
        treeOpts.shopIds.length = treeOpts.outletIds.length = 0
        return { leafIds, shopIds, outletIds }
      }

      return new Promise((resolve, reject) => {
        getShopInfoApi(leafIds).then(res => {
          res && Array.isArray(res) && res.forEach(list => {
            const { id, children = [] } = list
            id && shopIdSet.add(id)
            children.forEach(({ id }) => id && outletIdSet.add(id))
          })
          shopIds = treeOpts.shopIds = [...shopIdSet]
          outletIds = treeOpts.outletIds = [...outletIdSet]
          resolve({ leafIds, shopIds, outletIds, treeData: TreeDataShopIdAndOutId.getDataConstruction(res) })
        }).catch(e => reject(e))
      })
    },
    getSelectedShopIdsApp(treeOpts, getShopInfoApi) {
      const shopIdSet = new Set()
      const outletIdSet = new Set()

      const leafIds = treeOpts.checkedNodes
        .filter(item => item.type !== 'OLET')
        .filter(item => item.type !== 'SHOP')
        .map(item => item.id)

      treeOpts.checkedNodes.forEach(item => {
        if (item.type === 'SHOP') {
          const id = item.shopId || item.id
          if (id.includes('_')) {
            const [pId, shopId] = id.split('_')
            shopIdSet.add(shopId || id)
          } else {
            shopIdSet.add(id)
          }
        } else if (item.type === 'OLET') {
          outletIdSet.add(item.oletId)
        }
      })

      if (shopIdSet.size || outletIdSet.size || !leafIds.length) {
        const shopIds = treeOpts.shopIds = Array.from(shopIdSet)
        const outletIds = treeOpts.outletIds = Array.from(outletIdSet)
        treeOpts.shopIds.length = shopIds.length
        treeOpts.outletIds.length = outletIds.length
        const shopNode = treeOpts.checkedNodes.filter(item => item.type === 'SHOP').map(e => {
          const cloneItem = this.$_deepClone(e)
          const id = cloneItem.shopId || cloneItem.id
          if (id.includes('_')) {
            const [pId, shopId] = id.split('_')
            cloneItem.id = shopId || id
          } else {
            cloneItem.id = id
          }

          if (cloneItem.children && cloneItem.children.length) {
            cloneItem.children.forEach(item => {
              item.id = item.oletId || item.id
            })
          }
          return cloneItem
        })
        return { leafIds, shopIds, outletIds, treeData: TreeDataShopIdAndOutId.getDataConstruction(shopNode) }
      }

      let shopIds = []
      let outletIds = []
      return new Promise((resolve, reject) => {
        getShopInfoApi(treeOpts.checkedKeys).then(res => {
          res && Array.isArray(res) && res.forEach(list => {
            const { id, children = [] } = list
            id && shopIdSet.add(id)
            children.forEach(({ id }) => id && outletIdSet.add(id))
          })

          shopIds = treeOpts.shopIds = [...shopIdSet]
          outletIds = treeOpts.outletIds = [...outletIdSet]
          const treeData = TreeDataShopIdAndOutId.getDataConstruction(res)

          treeOpts.checkedNodes.forEach(item => {
            if (item.type === 'SHOP') {
              const id = item.shopId || item.id
              if (!treeData[id]) {
                treeData[id] = item.children.map(e => e.oletId)
              }
            } else if (item.type === 'OLET') {
              const shopId = item.parentId
              if (!treeData[shopId]) {
                treeData[shopId] = []
              }
              if (!treeData[shopId].includes(item.oletId)) {
                treeData[shopId].push(item.oletId)
              }
            }
          })
          resolve({ leafIds, shopIds, outletIds, treeData })
        }).catch(e => reject(e))
      })
    },
    updateHierarchiesSelectedInfo(hierarchiesTreeOpts) {
      if (this.isMobile) {
        this.hierarchiesSelectedPromise = this.getSelectedShopIdsApp(hierarchiesTreeOpts.treeOpts, this.getTypeShowList)
        return
      }
      this.hierarchiesSelectedPromise = this.getSelectedShopIds(hierarchiesTreeOpts.treeOpts, this.getTypeShowList)
    },
    updateDimensionsSelectedInfo(dimensionsTreeOpts) {
      if (this.isMobile) {
        this.dimensionsSelectedPromise = this.getSelectedShopIdsApp(dimensionsTreeOpts.treeOpts, this.getTagShopAndOutletTree)
        return
      }
      this.dimensionsSelectedPromise = this.getSelectedShopIds(dimensionsTreeOpts.treeOpts, this.getTagShopAndOutletTree)
    },

    async intersectionConfirm({ isNeedUpdateSelectedInfo, treeOptsMap, cb }) {
      const validFailedFn = () => {
        this.setMessage(this.$t('sdp.message.noRecordsMatchYourSearchCriteria'), 'mobileParamsPanel')
        cb && cb()
        this.finishHook(FINISH_TYPE.FINISH, this.$t('sdp.message.noRecordsMatchYourSearchCriteria'))
      }

      const { hierarchiesTreeOpts, dimensionsTreeOpts } = treeOptsMap
      const { checkedKeys } = hierarchiesTreeOpts.treeOpts

      if (isNeedUpdateSelectedInfo) {
        this.updateHierarchiesSelectedInfo(hierarchiesTreeOpts)
        this.updateDimensionsSelectedInfo(dimensionsTreeOpts)
      }

      this.finishHook(FINISH_TYPE.AWAIT)

      const [
        { leafIds: hierarchiesLeafIds = [], shopIds: hierarchiesShopIds = [], outletIds: hierarchiesOutletIds = [], treeData: hierarchiesTreeData = {} },
        { leafIds: dimensionsLeafIds = [], shopIds: dimensionsShopIds = [], outletIds: dimensionsOutletIds = [], treeData: dimensionsTreeData = {} },
      ] = await Promise.all([this.hierarchiesSelectedPromise, this.dimensionsSelectedPromise])
        .catch(e => {
          console.error(e)
          return [{}, {}]
        })

        if (
          [
            { opts: dimensionsTreeOpts, idList: dimensionsShopIds, outletIds: dimensionsOutletIds },
            { opts: hierarchiesTreeOpts, idList: hierarchiesShopIds, outletIds: hierarchiesOutletIds }
          ].some(({ opts, idList, outletIds }) => {
            if (this.isMobile) {
              return opts?.treeOpts?.checkedKeys?.length && !idList?.length && !outletIds.length
            }
            return opts?.treeOpts?.checkedKeys?.length && !idList?.length
          })) return validFailedFn()

      const [shopIds = [], outletIds = [], treeDataShopIdAndOutId = {}] = (() => {
        const HSelected = [...hierarchiesLeafIds, ...hierarchiesShopIds, ...hierarchiesOutletIds]
        const DSelected = [...dimensionsLeafIds, ...dimensionsShopIds, ...dimensionsOutletIds]
        if (HSelected.length && DSelected.length) {
          const shopIds = hierarchiesShopIds.filter(id => dimensionsShopIds.includes(id))
          const outletIds = hierarchiesOutletIds.filter(id => dimensionsOutletIds.includes(id))
          const treeData = shopIds.reduce((pre, next) => {
            const hOutletIds = hierarchiesTreeData?.[next]?.filter?.(id => outletIds.includes(id)) || []
            const DOutletIds = dimensionsTreeData?.[next]?.filter?.(id => outletIds.includes(id)) || []
            pre[next] = [...new Set(hOutletIds.concat(DOutletIds))]
            return pre
          }, {})
          return [
            shopIds,
            outletIds,
            treeData
          ]
        } else if (HSelected.length && !DSelected.length) {
          return [hierarchiesShopIds, hierarchiesOutletIds, hierarchiesTreeData]
        } else if (!HSelected.length && DSelected.length) {
          return [dimensionsShopIds, dimensionsOutletIds, dimensionsTreeData]
        } else {
          return []
        }
      })()

      if (this.isMobile) {
        if (shopIds.length === 0 && outletIds.length === 0) return validFailedFn()
      } else {
        if (shopIds.length === 0) return validFailedFn()
      }

      this.optionMap.labelIds = [...dimensionsLeafIds]
      this.optionMap.dimensionIds = [...hierarchiesLeafIds]
      this.optionMap.shopIds = [...shopIds]
      this.optionMap.outletIds = [...outletIds]

      console.log('optionMap:', this.optionMap)

      this.contentMap.intersectionData = JSON.stringify({
        hierarchiesTreeOpts: {
          init: true,
          treeId: hierarchiesTreeOpts.treeOpts.treeId,
          checkedKeys: hierarchiesTreeOpts.treeOpts.checkedKeys,
          shopIds: hierarchiesTreeOpts.treeOpts.shopIds,
          // checkedKeys,
          halfCheckedKeys: hierarchiesTreeOpts.treeOpts.halfCheckedKeys,
        },
        dimensionsTreeOpts: {
          init: true,
          treeId: dimensionsTreeOpts.treeOpts.treeId,
          checkedKeys: dimensionsTreeOpts.treeOpts.checkedKeys,
          shopIds: dimensionsTreeOpts.treeOpts.shopIds,
          halfCheckedKeys: dimensionsTreeOpts.treeOpts.halfCheckedKeys,
        }
      })

      this.optionMap.isPullSelect = false

      this.contentMap.loction = [
        ...hierarchiesTreeOpts.treeOpts.panelRef.tagTreeData.map(tree => {
          const label = hierarchiesTreeOpts.elTreeOpts.props.label
          const content = tree.children.length
            ? tree.children.map(item => item[label]).join(this.$t('sdp.punctuation.commaSpaceForEn'))
            : this.$t('sdp.select.all')
          if (!tree.children.length || !this.isMobile) {
            return `${tree[label]} - ${content}`
          }

          const { checkedNodes = [], halfCheckedNodes = [], checkedKeys = [] } = hierarchiesTreeOpts.treeOpts
          const checkedAll = checkedNodes.find(e => e.level === 1)
          const halfCheckedAll = halfCheckedNodes.find(e => e.level === 1)
          if (checkedAll) {
            return `${tree[label]} - ${this.$t('sdp.select.all')}`
          } else {
            const treeAll = [halfCheckedAll]
            const labels = getHierarchyLocationInfo({ treeAll, checkedKeys, isHierarchy: true })
            return `${labels.join(',')}`
          }
        }),
        ...dimensionsTreeOpts.treeOpts.panelRef.tagTreeData.map(tree => {
          const label = dimensionsTreeOpts.elTreeOpts.props.label
          const content = tree.children.length
            ? tree.children.map(item => item[label]).join(this.$t('sdp.punctuation.commaSpaceForEn'))
            : this.$t('sdp.select.all')

          if (!tree.children.length || !this.isMobile) {
            return `${tree[label]} - ${content}`
          }

          const { checkedNodes = [], halfCheckedNodes = [], checkedKeys = [] } = dimensionsTreeOpts.treeOpts
          const checkedAll = checkedNodes.find(e => e.id === e.rootId)
          const halfCheckedAll = halfCheckedNodes.find(e => e.id === e.rootId)
          if (checkedAll) {
            return `${tree[label]} - ${this.$t('sdp.select.all')}`
          } else {
            const treeAll = [halfCheckedAll]
            const labels = getHierarchyLocationInfo({ treeAll, checkedKeys, isHierarchy: false })
            return `${labels.join(',')}`
          }
        }),
      ]
      this.isShowIntersectionTree = false
      cb && cb(this.closePage)
      this.TreeDataShopIdAndOutId.setDirectTreeDataShopIdAndOutId(treeDataShopIdAndOutId)
      this.finishHook(FINISH_TYPE.FINISH)
      // this.$emit('confirm')
    },

    // 调用图形方法
    setLocationSelectType(type = '') {
      const { bindElements, labelIds, dimensionIds } = this.contentMap.options
      if (dimensionIds.length) {
        type = LOCATION_SELECT_TYPE.DIMENSION
      } else if (labelIds.length) {
        type = LOCATION_SELECT_TYPE.LABEL
      }
      this.board.elList.forEach(el => {
        if (el.type === TYPE_ELEMENT.CHART && el.vm && bindElements.includes(el.id)) {
          el.vm.setLocationSelectType && el.vm.setLocationSelectType(type, this.treeData)
        }
      })
    },
    // 比较方法
    compareFun(num = LOCATION_COMPARE_TYPE.ONE) {
      this.compareStyle = num
      const name = num === LOCATION_COMPARE_TYPE.ONE ? 'compareOne' : 'compareTwo'
      const data = num === LOCATION_COMPARE_TYPE.ONE ? 'dataOne' : 'dataTwo'
      let checkedKeys = []
      // let isAllFlag = this.$_getProp(this.loctionData[data], 'allFlag', false)
      let isAllFlag = this.loctionData[name][0] === 'ALL'
      if (this.$_getProp(this.loctionData[data], 'keys.length', false)) {
        checkedKeys = this.loctionData[data].keys
      } else {
        // isAllFlag = this.loctionData[name][0] === 'ALL'
        if (!this.isLazyLoading) checkedKeys = isAllFlag ? this.pullDownData.map(item => item.ids) : []
      }
      this.allFlag = isAllFlag

      this.getCurrentRefs('tree').setCheckedKeys(checkedKeys)

      // if (checkedKeys.length) {
      //   this.getCurrentRefs('tree').setCheckedKeys(checkedKeys)
      //   this.setSearchData()
      // } else {
      //   this.clearHandler(this.compareStyle)
      // }
      // if (this.getCompareData && num === 2) {
      this.pullDownDataSort()

      setTimeout(() => {
        this.isLazyLoading || this.setSearchData()
      })

        // this.getCompareData = false
      // }
      const { shopIds = [], outletIds = [] } = this.loctionData[data]
      this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, shopIds, outletIds, this.loctionData[name])
    },
    clickButton() {
      // this.setLocationData()
      this.getAllShopIdOutletId()
      this.closePage()
    },
    clearHandler(type) {
      const data = type === LOCATION_COMPARE_TYPE.ONE ? 'dataOne' : 'dataTwo'
      const name = type === LOCATION_COMPARE_TYPE.ONE ? 'compareOne' : 'compareTwo'
      this.$nextTick(() => {
        if (this.compareStyle === type) {
          this.getCurrentRefs('tree').setCheckedKeys([])
          this.setSearchData()
          this.allFlag = false
        }
        this.$set(this.loctionData, name, [])
        this.$set(this.loctionData, data, { 'shopIds': [], 'outletIds': [], 'keys': [], 'allFlag': false })
        const { shopIds = [], outletIds = [] } = this.loctionData[data]
        this.getSelectedLocationData(COMMON_VALUE.LOCATION_VALUE, shopIds, outletIds, this.loctionData[name])
      })
    },
    // 下拉框关闭储存数据
    getAllShopIdOutletId(needFinishHook = true) {
      needFinishHook && this.finishHook(FINISH_TYPE.AWAIT)
      this.optionMap.isPullSelect = true
      const content = this.contentMap
      const { options, selectType } = content
      let setData = ({ shopIds, outletIds, loction }) => {
        this.$set(options, 'shopIds', shopIds)
        this.$set(options, 'outletIds', outletIds)
        this.$set(content, 'loction', loction)
      }
      let state = FINISH_TYPE.FINISH
      if (this.isCompare) {
        this.$delete(this.contentMap, 'saveCompareData')
        this.setLoctionData(this.getData())
        this.$set(content, 'downSaveData', JSON.stringify(this.loctionData))
        // 处理后台数据
        const { dataOne, dataTwo, compareOne, compareTwo } = this.loctionData
        const shopIds = dataOne ? [...new Set([...dataOne['shopIds']])] : []
        const outletIds = dataOne ? [...new Set([...dataOne['outletIds']])] : []
        const shopIdsTwo = dataTwo ? dataTwo['shopIds'] : []
        const outletIdsTwo = dataTwo ? dataTwo['outletIds'] : []
        const oneBool = compareOne[0] === 'ALL'
        const twoBool = compareTwo[0] === 'ALL'
        const arrAll = ['ALL']
        this.$set(options, 'locationArea', {
          currShopIds: oneBool ? arrAll : shopIds,
          currOutletIds: oneBool ? arrAll : outletIds,
          preShopIds: twoBool ? arrAll : shopIdsTwo,
          preOutletIds: twoBool ? arrAll : outletIdsTwo,
          shopIdField: 'shop_id',
          oletIdField: 'olet_id',
        })
        setData({
          shopIds,
          outletIds,
          loction: { [this.paramElement.elName]: compareOne, Compare: compareTwo }
        })
        if (!dataOne || (!dataOne?.allFlag && !shopIds.length && !outletIds.length)) {
          state = FINISH_TYPE.UNFINISH
        }
      } else {
        if (content.tile === true) {
          const tree = this.getCurrentRefs('locationDownTileMobile')
          tree && tree.setDefaultValue()
        } else {
          const data = this.getData()
          const { shopIds = [], outletIds = [], loction = ['ALL'] } = data
          console.log(data, 'kyz')

          this.setLoctionData(data)
          this.$delete(content, 'saveData')
          this.$delete(content, 'intersectionData')
          setData({
            shopIds,
            outletIds,
            loction
          })
          if ((!this.allFlag && !shopIds.length && !outletIds.length) || !this.pullDownData.length) {
            state = FINISH_TYPE.UNFINISH
          }
        }
      }
      needFinishHook && this.finishHook(state)
      setTimeout(() => {
        this.filterText = ''
      })
    },
    getData(data = {}) {
      const isPullSelect = this.optionMap.isPullSelect

      if (this.allFlag) {
        isPullSelect && this.TreeDataShopIdAndOutId.setTreeDataShopIdAndOutId()
        return { shopIds: [], outletIds: [], loction: ['ALL'], keys: [], ...data }
      }

      const tree = this.getCurrentRefs('tree')

      const HalfCheckedData = ((tree && tree.getHalfCheckedNodes()) || []).filter((item) => {
        const node = tree && tree.getNode(item)

        return node?.indeterminate
      })

      const HalfChecked = HalfCheckedData

      const checkedData = ((tree && tree.getCheckedNodes()) || []).filter((item) => {
        const node = tree && tree.getNode(item)

        return node?.checked
      })

      const checked = checkedData

      const hakfKeys = HalfChecked.map(e => e.ids)
      const keys = checked.map(e => e.ids)
      const shopId = []
      const outletId = []
      const loction = []
      if (HalfChecked.length) {
        HalfChecked.forEach(item => {
          if (item.type === '1') {
            shopId.push(item.id)
          }
        })
      }
      if (checked.length) {
        const obj = {}
        checked.forEach(item => {
          if (item.type === '1') {
            shopId.push(item.id)
            loction.push(item.name)
          } else if (item.type === '2') {
            outletId.push(item.id)
            if (hakfKeys.includes(`${KEYS.parent}${item.parentId}`)) {
              const { name } = HalfChecked.find(e => `${KEYS.parent}${item.parentId}` === e.ids)
              Array.isArray(obj[name]) || (obj[name] = [])
              obj[name].push(item.name)
            }
          }
        })
        for (let key in obj) {
          const str = `${key} : ${obj[key].join(', ')}`
          loction.push(str)
        }
      }
      const shopIds = [...new Set(shopId)]
      const outletIds = [...new Set(outletId)]
      isPullSelect && this.TreeDataShopIdAndOutId.setTreeDataShopIdAndOutId(shopIds, checked)

      return { shopIds, outletIds, loction, checked, keys, ...data }
    },
    // 初始化
    clickAll(bool, type) {
      const tree = this.getCurrentRefs('tree')
      if (bool) {
        this.setLoctionData(this.getData())
        if (tree) {
          tree.setCheckedKeys(this.isLazyLoading ? [] : this.pullDownData.map(item => item.ids))
        }
      }
      // 处理点击取消全选操作
      if (type === 'cleanAll') {
        if (tree) {
          tree.setCheckedKeys([])
        }
        this.setLoctionData(this.getData({ loction: [] }))

        this.isLazyLoading || this.setSearchData()
      }
    },
    // 点击下拉框物业
    checkClick(checkedNodes, checkData) {
      console.log('checkClick', checkedNodes, checkData)

      const tree = this.getCurrentRefs('tree')
      if (this.selectType) {
        this.allFlag = false
        if (checkedNodes.ids.includes(KEYS.parent)) {
          if (checkData.checkedKeys.includes(checkedNodes.ids)) {
            tree.setCheckedNodes([checkedNodes])
          } else {
            tree.setCheckedNodes([])
          }
        } else {
          const flag = checkData.checkedNodes.length && checkData.checkedNodes.some(item => {
            return item.parentId ? (item.parentId !== checkedNodes.parentId) : (item.id !== checkedNodes.parentId)
          })
          flag && tree.setCheckedNodes([checkedNodes])
        }
      } else {
        // 修改点击全部的时候勾选上All
        this.allFlag = this.isLazyLoading ? false : this.pullDownData.every(({ ids }) => checkData.checkedKeys.includes(ids))
      }
      this.setLoctionData(this.getData())
      // 筛选后的物业添加
      if (this.filterText) {
        if (checkedNodes.parentId) {
          const node = tree.getNode(`${KEYS.parent}${checkedNodes.parentId}`)
          if (node) {
            checkedNodes = node.data
          }
        }
        this.filterCheck.push(checkedNodes)

        this.isLazyLoading || this.setSearchData()
      }
    },
    // 请求企业货币类型
    async getLanguaeAndCurrency(shopIds) {
      // ALL 筛选了过滤条件特殊处理
      const propertyList = this.contentMap.propertyList
      if (shopIds.includes(locationLangAll) && !this.isLazyLoading && this.contentMap.propertyScreening && Array.isArray(propertyList) && propertyList.length) {
        shopIds = [...this.contentMap.propertyList]
      }
      const { shopIds: oldShopIds } = this.oldLanguaeAndCurrency
      const bool = shopIds.sort().toString() === oldShopIds.sort().toString()
      if (bool) return void '无id或数组无变化不做处理'
      if (!shopIds.length) {
        const eventData = new EventData({
          source: TYPE_PARAM_ELEMENT.LOCATION_NEW,
          target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
          targetFn: 'callLanguaeAndCurrency',
          type: 'callLanguaeAndCurrency',
          data: {
            type: DEFAULT_TYPE
          }
        })
        this.$emit('eventBus', eventData)
        return
      }
      this.oldLanguaeAndCurrency.shopIds = shopIds
      const languaeAndCurrency = await getLanguaeAndCurrency.call(this, { tenantId: this.tenantId, shopIds, reverseElection: false })
      this.ReportCurrency = this.conformityApiData.reportCurrencyList || this.ReportCurrency || await getReportCurrency.call(this)

      const eventData = new EventData({
        source: TYPE_PARAM_ELEMENT.LOCATION_NEW,
        target: this.utils.isPcMobile ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
        targetFn: 'languaeAndCurrency',
        type: 'languaeAndCurrency',
        data: {
          languaeAndCurrency,
          ReportCurrency: this.ReportCurrency
        }
      })
      this.$emit('eventBus', eventData)

      const { tipFlag, message = '' } = languaeAndCurrency
      // 智能搜索隐藏了参数组件去掉提示
      if (this.utils?.intelligentData?.isIntelligentSearch) return

      tipFlag && message && this.setMessage(message)
    },
    // 设置数据keys
    setDataId (data, filterCheck = []) {
      data.forEach((item, index) => {
        item.children.length && item.children.forEach((val) => {
          val.ids = `${KEYS.son}${val.id}`
        })
        item.ids = `${KEYS.parent}${item.id}`
        item.sortNum = this.pullDownData.length + index
      })
      const pullDownData = [...this.pullDownData, ...data]
      this.contentMap.isSelectedTop && pullDownData.sort((a, b) => a.sortNum - b.sortNum)
      // const pullDownData = [...this.pullDownData, ...data]
      const tempArr = this.contentMap.isSelectedTop ? [...filterCheck, ...pullDownData] : pullDownData
      // 数组对象去重复
      const obj = {}
      return tempArr.reduce((item, next) => {
        if (!obj[next.ids]) {
          (obj[next.ids] = true) && item.push(next)
        }
        return item
      }, [])
    },
    // 点击调用筛选
    async searchTreeContent() {
      const tree = this.getCurrentRefs('tree')
      // 接口获取数据合并久数据
      this.isShowLoadingFlag = false
      this.filterParams = {
        page: 1,
        // shopName: this.filterText,
        batchShopName: this.filterText.split(',').map(txt => txt.trim()).filter(e => e),
        isSelectedTop: this.contentMap.isSelectedTop
      }
      await getNewLocationShopId.call(this, this.filterParams).then(res => {
        const { currentPage, lastPage, rows = [], recordTotal } = res
        !this.filterText && this.$set(this.contentMap, 'recordTotal', recordTotal || rows.length)
        this.isShowLoadingFlag = currentPage >= lastPage
        const keys = tree.getCheckedKeys()
        this.pullDownData = this.setDataId(rows, this.filterCheck)
        tree.setCheckedKeys(keys)
      })
      this.filterText || (this.filterCheck = [])
      this.loading = true
      this.filterText || (this.searchData.loading = true)
      tree.filter(this.filterText)
      setTimeout(() => {
        this.loading = false
        this.isLazyLoading || this.setSearchData()
      }, 1000)
    },
    setSearchChecks(val) {
      const tree = this.getCurrentRefs('tree')
      const el = tree.$el

      const allKeys = tree.getCheckedKeys()
      const allNodes = tree.getCheckedNodes()

      const doms = el.querySelectorAll('[data-visible]')
      const keys = [...doms].map(dom => dom.dataset.visible)
      const noKeys = keys.filter(key => !allKeys.includes(key))

      this.searchData.selectRecording = `${val ? keys.length : 0}/${keys.length}`
      const sonKeys = allNodes.filter(node => keys.includes(`${KEYS.parent}${node.parentId}`)).map(node => node.ids)

      const data = val ? [...allKeys, ...noKeys] : allKeys.filter(key => ![...keys, ...sonKeys].includes(key))

      tree.setCheckedKeys(data)

      const allData = tree.getCheckedKeys().filter(e => e.includes(KEYS.parent))

      this.allFlag = this.pullDownData.length === allData.length
      this.setLoctionData(this.getData())
    },
    setSearchData() {
      const tree = this.getCurrentRefs('tree')
      const el = tree.$el
      const allKeys = tree.getCheckedKeys()

      const doms = el.querySelectorAll('[data-visible]')
      const keys = [...doms].map(dom => dom.dataset.visible)
      const checks = keys.filter(key => allKeys.includes(key))

      this.searchData.selectRecording = `${checks.length}/${keys.length}`

      if (!keys.length && !this.allFlag) {
        this.searchData.selectAll = false
      } else {
        this.searchData.selectAll = keys.length === checks.length
      }

      this.searchData.loading = false
    },
    // 筛选事件
    filterNode (value, data, node) {
      if (!value) return true
      return this.filterValue(value, data, node)
    },
    filterAllData(value, data, node) {
      if (!value) return true
      let txt = value.trim()
      const txts = txt.split(searchSplitSymbols).map(e => e.trim()).filter(e => e)

      const names = this.getParentNames(node, data.name, 'name')
      // return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1
      return txts.some(txt => (names.toLowerCase()).indexOf(txt.toLowerCase()) !== -1)
    },
    getParentNames(node, name, key) {
      if (node.parent && node.parent.data[key]) {
        name += '#$&$#'
        name += node.parent.data[key]
        return this.getParentNames(node.parent, name, key)
      }
      return name
    },
    // 筛选父节点 子节点不筛选
    filterValue(value, data, node) {
      const ifOne = node.data.name.toUpperCase().indexOf(value.toUpperCase()) !== -1 && node.level === 1
      const ifTwo = node.parent && node.parent.data && node.parent.data.name && (node.parent.data.name.toUpperCase().indexOf(value.toUpperCase()) !== -1)
      let resultOne = false
      let resultTwo = false
      if (node.level === 1) {
        const timeOut = setTimeout(() => {
          this.$set(node, 'expanded', false)
          timeOut && clearTimeout(timeOut)
        })
        resultOne = ifOne
      } else if (node.level === 2) {
        resultTwo = ifOne || ifTwo
      }
      return resultOne || resultTwo
    },
    tabsBack() {
      // 没有点击确定，初始化进入时原来操作
      if (!this.isPullSelect) this.initDownDataState()
      this.selectedLabel = this.saveSelectedLabel
      this.cancelSaveLocationData()
      this.closePage('back')
    },
    cancelSaveLocationData() {
      this.saveLocationDataMsg && this.$set(this.contentMap, 'selLocationData', this.$_deepClone(this.saveLocationDataMsg))
    }
  },
}
