<template>
  <div
    class="view-type view-container"
    style="padding-left: 6px;width: 100%;"
  >
    <param-element-title v-bind="{
        txt: `${paramName}:`,
        elType: paramElement.type,
        txtStyle: paramElement.elNameStyle,
        isHideElement: paramElement.isHideElement
      }"></param-element-title>
    <el-radio-group
      v-model="selectedElement"
      class="paramcommponents-radio radioContent"
    >
      <el-radio
        v-for=" ({ id, title }) in optionList"
        :key="id"
        :label="id"
        class="radio-style"
        :class="[themeStatus?'sdp-params-theme-radio':'']"
        :title="title"
      >{{ title }}</el-radio>
    </el-radio-group>
  </div>
</template>

<script>
import { FINISH_TYPE } from '../../utils/constants'
import { initializationData } from './bridge'
import { TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import EventData from 'packages/assets/EventData'
import { getContainerCursorCount } from '../../../utils'
import ParamElementTitle from '../../components/ParamElementTitle'
// import { typeManipulation } from './utils'
// import { SKPINAME } from '../../utils/methodParams'
export default {
  components: { ParamElementTitle },
  // inject: ['board'],
  inject: {
    commonData: { default: {} },
    utils: { default: {} },
    themeData: { default: {} },
  },
  props: {
    paramElement: {
      type: Object,
      default: () => ({}),
    },
    board: {
      type: Object,
      default: () => ({}),
    },
    beforeHook: {
      type: Function
    }
  },

  computed: {
    // isParamsPanelDisabled() {
    //   return this.$store.getters.paramsPanelDisabled
    // },
    // 别名长度限制
    themeStatus() {
      return true
      // return this.themeData.screenMode && this.commonData.isPreview
    },
    paramName () {
      if (this.$_getStringLength(this.paramElement.elName) > 20) {
        return this.paramElement.elName.substring(0, 20) + '...'
      }
      return this.paramElement.elName
    },

    isAdvanceContainer() {
      return this.bindElement.subType === TYPE_ELEMENT.ADVANCE_CONTAINER
    },
  },

  data () {
    return {
      selectedElement: '',
      bindElement: {},
      optionList: [],
      superLinkData: null,
    }
  },
  mounted () {
    if (this.paramElement.content.bindElement) {
      this.active(this.paramElement.content.bindElement)
    }
    // this.finishHook(FINISH_TYPE.AWAIT)
    const { data, type, superLinkData } = this.beforeHook(this.paramElement)
    this.superLinkData = superLinkData
    initializationData[type].call(this, data)
  },
  methods: {
    requestAdapter (data) {
      return data
    },
    // 完成钩子
    // finishHook () {
    //   this.$emit('finishHook', '完成钩子')
    // },
    active (newVal) {
      this.bindElement = this.board.elList.find(el => el.id === newVal)
      if (this.bindElement) {
        this.activeContainer()
      } else {
        this.optionList = []
        this.$set(this.paramElement.content, 'arrName', [])
        this.selectedElement = ''
        this.$set(this.paramElement.content, 'bindElement', '')
      }
    },
    activeContainer() {
      if (this.isAdvanceContainer) {
        this.activeAdvanceContainer()
        return
      }

      const activeElId = this.bindElement.content.activeElId
      if (activeElId) {
        const elAliasMap = this.$_deepClone(this.bindElement.content.settings.elAlias)
        const elAlias = elAliasMap[activeElId] || this.getElName(activeElId)
        this.$set(this.paramElement.content, 'arrName', [elAlias])
        this.optionList = this.bindElement.content.includedElIds.map(id => {
          const title = elAliasMap[id] || this.getElName(id)
          return { id, title }
        })
      } else {
        this.optionList = []
        this.$set(this.paramElement.content, 'arrName', [])
      }
      const optionLength = getContainerCursorCount(this.bindElement)
      let fix = optionLength % 5 ? 0 : 1
      let length = parseInt((optionLength) / 5 - fix)
      const originLayout = this.$_getProp(this.paramElement, 'content.layout', [])
      const layout = this.$_deepClone(originLayout)
      if (optionLength > 5) {
        layout.h = 15 + length * 12
        layout.defaultHeight = 15 + length * 12
      } else {
        layout.h = 15
        layout.defaultHeight = 15
      }
      // 当默认高度发生改变时通知布局重置
      if (this.paramElement.content.layout.defaultHeight !== layout.defaultHeight) {
        this.paramElement.content.layout = layout
        this.restData()
      }
      this.selectedElement = this.bindElement.content.activeElId || this.paramElement.content.activeElId
    },
    restData () {
      const eventData = new EventData({
        target: 'tab',
        targetFn: 'restData'
      })
      this.$emit('eventBus', eventData)
    },
    activeAdvanceContainer() {
      const { tabList, activeTabId } = this.bindElement.content
      const activeTab = tabList.find(tab => tab.name === activeTabId)
      const activeTabTitle = activeTab.title

      this.$set(this.paramElement.content, 'arrName', [activeTabTitle])

      this.optionList = tabList.map(({ name: id, title }) => {
        return { id, title }
      })

      this.selectedElement = activeTabId
    },

    getElName(elId) {
      return this.board.elList.find(el => el.id === elId).elName
    }
  },
  watch: {
    'board.elList': {
      handler (newVal, oldval) {
        this.active(this.paramElement.content.bindElement)
      },
      deep: true,
    },
    'paramElement.content.bindElement': {
      handler (newVal, oldval) {
        this.active(newVal)
      },
      deep: true,
    },
    selectedElement (newVal) {
      this.bindElement && this.$set(this.bindElement.content, this.isAdvanceContainer ? 'activeTabId' : 'activeElId', newVal)
      this.bindElement && this.$set(this.paramElement.content, 'activeElId', newVal)
    },
  },
}
</script>

<style lang="scss" media="screen">
.radio-style {
  width: calc( 100% / 5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 0px!important;
  line-height: 16px!important;
  height: 16px;
}
.radioContent {
  width: calc( 500% / 6);
}
.view-type /deep/ {
  .el-radio-group {
    .el-radio {
      padding: 0 10px;
      float: left;
      position: relative;
      top: 0;
    }
    .el-radio + .el-radio {
      margin-left: 0;
    }
  }
}
</style>

<style lang="scss" scoped>
@import "../style/paramcommponents.scss";
.view-container{
  .el-radio-group{
    /deep/ .el-radio.is-checked{
      .el-radio__label{
        color: var(--sdp-zjbxx-wzs) !important;
      }
    }
    /deep/ .el-radio__input{
      &.is-focus{
        .el-radio__inner{
          border-color: var(--sdp-xk-bks);
        }
      }
      &.is-checked{
        .el-radio__inner{
          background-color: var(--sdp-cszjdxtc);
          border-color: var(--sdp-cszjdxtc);
          &::after{
            border-color: var(--sdp-cszjdxg);
          }
        }
      }
    }
  }
}
</style>
