<template>
  <div :style="{'cursor': isAdvanceContainerEdit ? 'not-allowed' : 'pointer'}"
       :class="{'container-bar': isAdvanceContainerEdit, 'data-report-title-bar': isDataReport }"
       class="top-input"
  >
    <div class="title-bar"
         :style="{'pointer-events': isAdvanceContainerEdit ? 'none' : ''}">
      <i class="icon sdpiconfont icon-sdp-fanhui back-btn" @click="close"></i>
      <span class="subtitle">{{ title }}</span>
      <el-input v-if="!isTemplateBoard" class="title-info-input" v-model="boardInfo.code" :disabled="boardInfo.metaType === '1'" :placeholder="plsNO"></el-input>
      <el-input class="title-info-input" v-model="boardInfo.name" :disabled="boardInfo.metaType === '1'" :placeholder="plsName"></el-input>

      <el-input
        v-model="boardInfo.remark"
        :placeholder="plsDesc"
        class="title-info-input input-right-12"
      >
        <i slot="suffix" class="icon sdpiconfont icon-sdp-sdp-rongqiqiehuan" style="font-size: 18px;" @click.stop="handleClickRemark"></i>
      </el-input>

      <el-input class="title-info-input input-right-12" v-if="isShowSubName" v-model="boardInfo.nameSub" :placeholder="$t('sdp.views.pleaseElementName')">
        <i slot="suffix" v-popover:popover class="icon sdpiconfont icon-sdp-biaotiziti"></i>
      </el-input>
      <el-popover :popper-class="'themes-popover '+getCurrentThemeClass()" ref="popover" placement="bottom" trigger="click" @show="showPopover">
        <title-font-name @titleStyle='titleStyle' :customStyle="Tstyle" :name="boardInfo.nameSub" ref="titleFontName"></title-font-name>
      </el-popover>
      <!-- <div class="more-language">
        <i @click="checkMoreLanguage" class="el-icon-sdp-Multilingual"></i>
      </div> -->
      <div class="center" v-if="isDataReport" :class="{ 'pc-mobile-disabled': pcMobileLoading }">
        <div :class="isPcMobileEdit ? '' : 'is-active'" @click="pcMobileChange()">
          <el-tooltip
            effect="dark"
            :open-delay="500"
            :content="$t('sdp.views.pcLayout')"
            :popper-class="`databoard-btn-tooltip ${getCurrentThemeClass()}`"
            placement="bottom"
            :popper-options="{}"
          >
            <i class="icon-sdp-PC"></i>
          </el-tooltip>
        </div>
        <div :class="!isPcMobileEdit ? '' : 'is-active'" @click="pcMobileChange(true)">
          <el-tooltip
            effect="dark"
            :open-delay="500"
            :content="$t('sdp.views.mobileLayout')"
            :popper-class="`databoard-btn-tooltip ${getCurrentThemeClass()}`"
            placement="bottom"
            :popper-options="{}"
          >
            <i class="icon-sdp-Mobile"></i>
          </el-tooltip>
        </div>
      </div>

      <div :class="isBanClick ? 'banClick' : ''" class="right-btn" v-if="!isAdvanceContainerEdit">
        <div class="publish-mobile-box" v-if="isDataReport && utils.isPcMobileEdit">
          <div class="label">
            {{$t('sdp.views.PublishToMobile')}}
          </div>
          <el-switch
            v-model="boardInfo.releaseApp"
          />
        </div>
        <el-tooltip v-if="isTemplateBoard" effect="dark" :content="$t('sdp.views.generateScreenshot')" placement="bottom">
          <el-button class="preview-btn sdp-btn sdp-get-img"
                     icon="icon-sdp-jietubaocun"
                     :loading="isTemplateBoardLoading"
                     @click="saveSupernatantImg"/>
        </el-tooltip>
        <!-- 操作指引 -->
        <el-tooltip v-if="!isTemplateBoard && !utils.isPcMobileEdit" effect="dark" :content="$t('sdp.guide.operatingGuide')" placement="bottom">
          <el-button class="guide-btn" icon="icon-sdp-tips" @click="toggleVisibleByGuide"></el-button>
        </el-tooltip>
        <el-button v-if="utils.isEnterprise" class="preview-btn sdp-btn" @click="preview('')">{{$t('sdp.button.preview')}}</el-button>
        <el-dropdown  v-else :class="[utils.isEnterprise?'enterprise':'noEnterprise']" @click="preview('')" size="small" class="board-header-preview" placement="bottom" trigger="click" split-button>
          {{$t('sdp.button.preview')}}
          <el-dropdown-menu slot="dropdown" class="board-design-pop sdp-dialog sdp-dropdown" :class="themeType">
            <el-dropdown-item
              v-for="lang in langs"
              :key="lang.isoCode"
              @click.native="preview(lang.isoCode)"
            >{{lang.name}}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <GuidePopover
          :content="$t('sdp.guide.clickToSaveTheDashboard')"
          :value="isShowStepTipsBySaved"
          :step="stepEntryKanbanSaved"
          :markPaddingLeft="7"
          :markPaddingRight="7"
          :markPaddingTop="6"
          :markPaddingBottom="6"
          :arrowOffsetX="-12"
          :tipsOffsetY="-18"
        >
          <el-button v-if="isSave" class="save-btn sdp-btn" :loading="isLoading || coverLoading" :disabled="isLoading || coverLoading || isTemplateBoardLoading" @click="save">{{$t('sdp.button.save')}}</el-button>
      </GuidePopover>
        <!-- <el-button v-if="isSavaAndPublish" class="saveAndPublish-btn" :loading="isLoadingPublish" :disabled="isLoadingPublish" @click="saveAndPublish">{{$t('sdp.button.saveAndPublish')}}</el-button> -->
        <el-button v-if="utils.isPC && !isSbiType" class="save-btn sdp-btn" style="margin-left: 0 !important;" :loading="isLoadingSaveAsMobileBoard" :disabled="isLoadingSaveAsMobileBoard" @click="saveAsMobileBoard">{{$t('sdp.button.saveAsMobileBoard')}}</el-button>
      </div>
      <div class="right-btn" v-else />
    </div>
    <div class="container-btn" v-if="isAdvanceContainerEdit">
      <el-button class="save-btn sdp-btn" @click="changeAdvanceContainerEdit(!isAdvanceContainerEdit)">{{$t('sdp.button.ensure')}}</el-button>
    </div>

    <!-- 文件夹弹窗 -->
    <FileDialog v-if="ifFileDialog" folderType="4" :visible.sync="fileDialogVisible" @close="fileDialogVisible = false" @confirm="handleFileDialogConfirm" />
  </div>
</template>
<script>// @ts-nocheck

import EventData from 'packages/assets/EventData'
import TitleFontName from 'packages/base/common/titleFontName/TitleFontName.vue'
import { CODELEN, NAMELEN, NAMESUBLEN, REMARKLEN } from './constans'
import { PREVIEW_STATUS } from 'packages/assets/constant'
import { setTimeout } from 'timers'
import { kanbanGuideStepEntrySavedMixin } from 'packages/base/KanbanGuide'
import FileDialog from './fileDialog.vue'
import { ALL_PROJECT_NAME } from '../../../components/mixins/commonMixin'

export default {
  name: 'titleBar',
  inject: ['utils', 'isTemplateBoard', 'getCurrentThemeClass'],
  mixins: [kanbanGuideStepEntrySavedMixin],
  props: {
    // 是否有保存并发布
    isSavaAndPublish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nameRag: new RegExp('[:*\\[\\]/?\\\\]'),
      boardInfo: {
        code: '',
        name: '',
        remark: '',
        nameSub: '',
        releaseApp: false,
      },
      dataReport: null,
      Tstyle: {},
      loading: false,
      isLoading: false,
      coverLoading: false,
      isLoadingPublish: false,
      pcMobileLoading: false,
      isBanClick: true,
      isTemplateBoardLoading: false,
      isAdvanceContainerEdit: false,
      isLoadingSaveAsMobileBoard: false,
      fileDialogVisible: false,
    }
  },
  components: { TitleFontName, FileDialog },
  computed: {
    title() {
      return (this.boardInfo.id && !this.utils.isPC2App) ? this.$t('sdp.views.editBoard') : this.$t('sdp.views.newBoard')
    },
    ifFileDialog() {
      return this.utils.isPC2App && !this.utils.isPC2AppFolderId
    },
    plsNO() {
      return this.dataReport ? this.$t('sdp.views.PleaseEnterTheDataReportNumber') : this.$t('sdp.views.PleaseEnterTheKanbanNumber')
    },
    plsName() {
      return this.dataReport ? this.$t('sdp.views.pleaseRenameDataReportName') : this.$t('sdp.views.pleaseRenameDoardName')
    },
    plsDesc() {
      return this.dataReport ? this.$t('sdp.views.pleaseDescDataReportName') : this.$t('sdp.views.pleaseDescDoardName')
    },
    isShowSubName() {
      return !this.utils.isScreen && !this.isTemplateBoard && !this.dataReport
    },
    langs() {
      return this.utils.languageList
    },
    isDataReport() {
      return this.utils.isDataReport
    },
    isPcMobileEdit() {
      return this.utils.pcMobileEdit
    },
    isSave() {
      return this.utils.editType !== PREVIEW_STATUS.DESIGNER
    },
    themeType() {
      return this.utils?.themeParameters?.themeType || ''
    },
    isSbiType() {
      let projectName = this.utils.env?.projectName
      return projectName === ALL_PROJECT_NAME.SBI
    },
  },
  watch: {
    'boardInfo.code': {
      handler(val) {
        this.$nextTick(() => {
          this.boardInfo.code = this.isTemplateBoard ? '' : this.filterInput(val)
        })
      },
      immediate: true
    },
    'boardInfo.nameSub'(val) {
      const v = new EventData({
        source: this.$options.name,
        target: ['displayPanel'],
        targetFn: 'changeCurrentTitle',
        data: {
          title: val
        }
      })
      this.$emit('eventBus', v)
      if (val === '') {
        const eventData = new EventData({
          source: this.$options.name,
          target: ['displayPanel', 'displayPanelMobile'],
          targetFn: 'clearNameSub',
          data: ''
        })
        this.$emit('eventBus', eventData)
      }
    }
  },
  methods: {
    saveSupernatantImg() {
      this.isTemplateBoardLoading = true
      this.$emit('eventBus', new EventData({
        source: this.$options.name,
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'saveSupernatantImg',
        options: {
          callback: () => {
            this.isTemplateBoardLoading = false
            this.$message.success(this.$t('sdp.message.UploadSuccessful'))
          }
        }
      }))
    },
    pcMobileChange(isMobile) {
      if (this.pcMobileLoading) return
      this.pcMobileLoading = true
      setTimeout(() => {
        this.pcMobileLoading = false
      }, 3000)

      this.$emit('pcMobileChange', isMobile)
    },
    handleClickRemark() {
      const eventData = new EventData({
        source: this.$options.name,
        target: this.isPcMobileEdit ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
        targetFn: 'showBoardRemarkDialog',
        data: {}
      })
      this.$emit('eventBus', eventData)
    },
    updateTitle({ data }) {
      if (typeof data?.title === 'string') this.boardInfo.nameSub = data.title
      this.Tstyle = data.titleStyle || {}
      // 如果长度为
      if (Object.keys(this.Tstyle).length === 0) {
        this.$nextTick(() => {
          const acolor = this.Tstyle.color
          if (!this.Tstyle.color) {
            this.$refs.titleFontName.style.color = ''
          }
          this.$refs.titleFontName.style['font-size'] = ''
          // this.titleStyle(this.$refs.titleFontName.style)
        })
      }
      this.titleStyle(this.Tstyle)
      this.showPopover()
    },
    close() {
      if (this.isSave) {
        this.$sdp_eng_confirm(`${this.$t('sdp.views.boardTipsConfirmTitle')}`, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
          confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
          cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
          closeOnHashChange: false,
          type: 'warning',
          closeOnClickModal: false,
          cancelButtonClass: 'el-button--sdp-cancel',
          confirmButtonClass: 'el-button--sdp-ensure',
          customClass: 'sdp-dialog',
          beforeClose: (action, instance, done) => {
            instance.$el.style.zIndex = -1
            done()
          },
        }).then(() => {
          this.$emit('close')
        }).catch(() => {})
      } else {
        this.$emit('close')
      }
    },
    // camelCase(str) {
    //   const strArr = str.split('-')
    //   for (var i = 1; i < strArr.length; i++) {
    //     strArr[i] = strArr[i].charAt(0).toUpperCase() + strArr[i].substring(1)
    //   }
    //   return strArr.join('')
    // },
    titleStyle(obj) {
      // const objs = {}
      // for (const key in obj) {
      //   objs[this.camelCase(key)] = obj[key]
      // }
      Object.assign(this.Tstyle, obj)
      const v = new EventData({
        source: this.$options.name,
        target: ['displayPanel'],
        targetFn: 'changeCurrentTitle',
        data: {
          titleStyle: this.$_deepClone(this.Tstyle)
        }
      })
      this.$emit('eventBus', v)
      this.$emit('changeTitleStyle', obj)
    },
    showPopover() {
      this.$nextTick(() => {
        if (Object.keys(this.Tstyle).length) {
          if (this.Tstyle['font-size']) {
            this.$refs.titleFontName.value = this.Tstyle['font-size'].replace('px', '')
          } else if (this.Tstyle['font-size'] === '') {
            this.$refs.titleFontName.value = 14
            this.Tstyle['font-size'] = '14px'
          }
          this.$refs.titleFontName.style = Object.assign(this.$refs.titleFontName.style, this.Tstyle)
        }
      })
    },
    // TODO:传递事件到预览界面
    filterInput(val) {
      return val.replace(this.nameRag, '')
    },
    updateAdvanceContainerEdit({ data }) {
      this.isAdvanceContainerEdit = data?.val
    },
    changeAdvanceContainerEdit(val) {
      const eventData = new EventData({
        data: val,
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'changeAdvanceContainerEdit',
      })
      this.$emit('eventBus', eventData)
    },
    updateBoardRes(res) {
      this.boardInfo = res.data.boardInfo
      this.dataReport = res.data.dataReport || null
      this.Tstyle = res.data.titleStyle
      this.isBanClick = !!res.data.isBanClick
      this.$nextTick(() => {
        if (this.Tstyle['font-family'] === '') {
          this.Tstyle['font-family'] = this.$refs.titleFontName.defaultStyle['font-family']
        }
        this.$refs.titleFontName.assignObject(this.Tstyle)
        const acolor = this.Tstyle.color
        if (!this.Tstyle.color) {
          this.$refs.titleFontName.style.color = ''
        }
        this.titleStyle(this.$refs.titleFontName.style)
      })
    },
    preview(selectLang) {
      if (this.isTemplateBoardLoading) return
      const eventData = new EventData({
        source: this.$options.name,
        target: this.isPcMobileEdit ? ['displayPanelPcToMobile'] : ['displayPanel', 'displayPanelMobile'],
        targetFn: 'preview',
        data: selectLang
      })
      this.$emit('eventBus', eventData)
    },
    validateLength() {
      let res = false
      if (this.boardInfo.code && this.boardInfo.code.length > CODELEN) {
        this.$message({
          type: 'warning',
          message: this.$t('sdp.views.codelen')
        })
        res = true
      }
      if (this.boardInfo.name && this.boardInfo.name.length > NAMELEN) {
        this.$message({
          type: 'warning',
          message: this.$t('sdp.views.namelen')
        })
        res = true
      }
      if (this.boardInfo.remark && this.boardInfo.remark.length > REMARKLEN) {
        this.$message({
          type: 'warning',
          message: this.$t('sdp.views.remarklen')
        })
        res = true
      }
      let otherTitle = false
      let stack = this.boardInfo.tagModeStack
      if (stack && Object.keys(stack).length > 0 && Object.keys(stack.settingTitle).length > 0) {
        for (let key in stack.settingTitle) {
          if (stack.settingTitle[key].title.length > NAMESUBLEN) {
            otherTitle = true
            break
          }
        }
      }
      if ((this.boardInfo.nameSub && this.boardInfo.nameSub.length > NAMESUBLEN) || otherTitle) {
        this.$message({
          type: 'warning',
          message: this.$t('sdp.views.namesublen')
        })
        res = true
      }
      return res
    },
    loadingCall(name = '', delay = 5000) {
      if (typeof name !== 'string' || !name) return

      if (typeof delay !== 'number') return

      this[name] = true
      setTimeout(() => {
        this[name] = false
      }, delay)
    },
    saveCall(name, isPublish, options) {
      let isValidate = this.validateLength()
      if (isValidate) return

      this.loadingCall(name)

      if (typeof isPublish !== 'boolean') return

      const eventData = new EventData({
        source: this.$options.name,
        target: ['displayPanel', 'displayPanelMobile', 'displayPanelPcToMobile'],
        targetFn: 'save',
        data: {
          isPublish,
          ...options
        }
      })
      this.$emit('eventBus', eventData)
    },
    save() {
      if (this.isTemplateBoardLoading) return
      if (this.utils.isPC2App) return this.handlePC2App()
      this.saveCall('isLoading', false)
    },
    handlePC2App() {
      if (this.utils.isPC2AppFolderId) {
        this.saveCall('isLoading', false, { folderId: this.utils.isPC2AppFolderId })
      } else {
        this.fileDialogVisible = true
      }
    },
    handleFileDialogConfirm(data) {
      this.fileDialogVisible = false
      this.saveCall('isLoading', false, { folderId: data.id })
    },
    saveAndPublish() {
      this.$sdp_eng_confirm(`${this.$t('sdp.views.isSaveAndPublish')}`, `${this.$t('sdp.message.tipsConfirmTitle')}`, {
        confirmButtonText: `${this.$t('sdp.message.confirmButton')}`,
        cancelButtonText: `${this.$t('sdp.message.cancelButton')}`,
        closeOnHashChange: false,
        type: 'warning',
        closeOnClickModal: false,
        cancelButtonClass: 'el-button--sdp-cancel',
        confirmButtonClass: 'el-button--sdp-ensure',
        customClass: 'sdp-dialog',
        beforeClose: (action, instance, done) => {
          instance.$el.style.zIndex = -1
          done()
        },
      }).then(() => {
        this.saveCall('isLoadingPublish', true)
      }).catch(() => {})
    },
    //  切换多语言
    checkMoreLanguage() {
      const eventData = new EventData({
        source: this.$options.name,
        target: ['displayPanel', 'displayPanelMobile'],
        targetFn: 'getMoreLanguageContent'
      })
      this.$emit('eventBus', eventData)
    },
    saveAsMobileBoard() {
      this.saveCall('isLoadingSaveAsMobileBoard', false, { save2App: true })
    },
  }
}
</script>
<style lang="scss" scoped>
  /deep/  .el-input__inner {
    border-color: transparent;
  }
  /deep/ .el-input.is-disabled .el-input__inner {
    color: var(--sdp-jys2) !important;
  }

  .banClick{
    pointer-events:none
  }
  .save-btn, .saveAndPublish-btn {
    @include common-header-save-button;
  }
  .saveAndPublish-btn {
    margin-left: 4px !important;
  }

  .sdp-get-img {
    margin-right: 12px !important;
    width: 24px !important;
    padding: 0 !important;
    @include common-header-preview-button;
    &:hover {
      @include common-header-preview-hover-button;
    }
  }
  .preview-btn {
    padding: 0 15px 0 15px;
    @include common-header-preview-hover-button;
    height: 24px;
    line-height: 24px;
    border-radius: 2px;
    font-size: 12px;
  }
  .preview-btn:hover {
    @include common-header-preview-hover-button;
  }
  .board-header-preview {
    /deep/ .el-button {
      @include common-header-preview-button;
      color: var(--sdp-cyfhwzs) !important;
    }
    &:hover {
      /deep/ .el-button {
        @include common-header-preview-hover-button;
        opacity: 1 !important;
      }
    }
  }
  .title-bar{
    z-index: 1;
    background: var(--sdp-tb-bj);
    height: 40px;
    line-height: 40px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    padding: 0 8px 0 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    .back-btn{
      margin: 0 12px 0 16px;
      font-size: 16px;
      cursor: pointer;
      color: var(--sdp-cyfhwzs);
    }
    .right-btn{
      flex:1;
      line-height: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      min-width: 150px;

      .publish-mobile-box {
        margin-right: 16px;
        color: var(--sdp-cyfhwzs);

        display: flex;
        align-items: center;

        .label {
          margin-right: 4px;
        }
      }
    }
    .subtitle{
      white-space: nowrap;
      font-family: NotoSansHans-Regular;
      font-weight: 400;
      font-size: 16px;
      color: var(--sdp-cyfhwzs);
      text-align: left;
      margin-right: 39px;
    }

    .title-info-input{
      width: 200px;
      margin-right: 8px;
      /deep/ .el-input__inner {
        font-family: PingFangSC-Regular;
        font-weight: 500;
        font-size: 12px;
        opacity: 0.9;
        border-radius: 0;
        height: 24px;
        line-height: 24px;
        padding: 0 12px;
      }
    }
    .input-right-12{
      /deep/ .el-input__inner {
        padding-right: 24px;
      }
    }
    /deep/ .el-input__inner {
      width: 200px;
      font-size: 12px;
      opacity: 0.9;
      border-radius: 0;
      height: 24px;
      line-height: 24px;
      padding: 0 12px;
    }
    .sdp-btn{
      // padding: 9px 30px;
      cursor: pointer;
    }
  }
  .enterprise /deep/ .el-icon-arrow-down {
    visibility: hidden;
  }
  .more-language {
    height: 48px;
    display: inline-block;
    padding-left: 16px;
    position: relative;
    i {
      cursor: pointer;
      font-size: 26px;
      vertical-align: middle;
    }
  }
  .title-font-name{
    width: 28px;
    height: 28px;
    background: #FFFFFF;
    box-shadow: 0 1px 4px 0 rgba(0,0,0,0.10);
    border-radius: 2px;
    margin-right: 16px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .font-icon{
    width: 20px;
    height: 18px;
  }
  .icon-sdp-sdp-rongqiqiehuan, .icon-sdp-biaotiziti{
    font-size: 12px;
    color: var(--sdp-cszjq-is);
  }
  .container-bar{
    position: relative;
    background: #000 !important;
  }
  .container-btn{
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    padding: 0 8px 0 0;
    pointer-events: painted;
    z-index: 99;
    flex:1;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    background: rgba(0,0,0,0.7) !important;
  }

  .data-report-title-bar {
    .title-bar {
      justify-content: space-between;

      .left{
        flex: 1;
      }
      .center {
        margin-left: 14px;
        display: flex;
        height: 24px;
        border-radius: 2px;
        overflow: hidden;
        div {
          width: 48px;
          line-height: 24px;
          background: var(--sdp-fs2);
          color: var(--sdp-zs);
          text-align: center;
          font-size: 24px;
          &.is-active {
            background: var(--sdp-zs);
            color: var(--sdp-nngl);
          }
          i {
            font-size: 24px;
          }
        }
      }

      // .right-btn {}
    }
  }
  .pc-mobile-disabled {
    opacity: 0.8;
    cursor: not-allowed;
  }
  .guide-btn {
    width: 24px;
    height: 24px;
    padding: 3px;
    margin-right: 12px;
    border: none !important;
    background: var(--sdp-nn-fzds) !important;

    /deep/ .icon-sdp-tips {
      font-size: 16px;
      color: var(--sdp-cyfhwzs) !important;
    }

    &:hover {
      background: var(--sdp-zs) !important;
      opacity: 1 !important;
      /deep/ .icon-sdp-tips {
        color: #ffffff !important;
      }
    }
  }
</style>
