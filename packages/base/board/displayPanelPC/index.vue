<template>
  <div
    id="data-screen-table"
    class="board-display-panel"
    ref="dataScreenTableRef"
    v-sdp-border-scroll="{loadFun: borderScroll }"
    :class="[
      ...dataScreenClass,
      isDarkMode && 'theme-dialog-background',
      hiddenScroll && 'hiddenScroll',
      isDataReport && 'isDataReport',
      isMobileTablet && 'sdpTabletScale',
      getCurrentThemeClass(),
    ]"
    :style="{
      ...isHiddenX,
      // ...setLargeScreenFormatter(isScreen ? '' : 'elementBoardBg'),
      ...boardScaleStyle,
    }"
  >
      <loading :isLoading="fullScreenLoading" />
      <!-- 点击超链接等待locading -->
      <loading :isLoading="superLinkWait" :isImg="false" :styles="{ backgroundColor: 'transparent !important', cursor: 'wait', zIndex: 99999 }"/>
      <div id="sdp-record" v-show="false">
        {{record}}
      </div>
      <!-- 此 ID 在别处被用来获取元素高度 -->
      <div ref="borderBoxRef"
          class="borad-box"
          :style="{...boardStyle}"
          id="board-display-panel-border-box"
          :class="[
            commonData.isPreview?'theme-dialog-background preview-bgc':'',
            isShowWatermark ? 'watermark-area' : ''
          ]"
      >
      <!-- :style="{ height: isScreen ? '100%' : displayPanelHeight }" -->
        <div
          class="board-display-panel-box"
        >
          <div
            v-show="!isSbiDashPreview || isAddDailyConcernElement"
            class="sdp-box"
            :class="{
              'border-box-fixed': isParamsPanelFixed,
              'box-inherit': isDataReport && boardInfo.hideComponent && commonData.isPreview,
            }"
            :style="{
              zIndex: isRealScreen ? 9 : undefined,
            }"
          >
            <i
              class="icon-sdp-Nguanbi"
              v-if="!isSbiDashPreview && !isTemplatePreview && isShowOff && !isIntelligentSearch"
              :class="[(themeData.enableTheme && themeData.themeType === 'sdp-dark-blue') ? 'theme-dark-mode' : '']"
              @click="closePreview()"
            />

            <!--大屏关闭按钮-->
            <i
              v-if="!isSbiDashPreview && !isTemplatePreview && isShowRightBtn && isRealScreen && !boardInfo.hideComponent && isShowFullScreenParamsPanel && (commonData.staticParamsPanel || !isParamsPanelSlideInUp) && (isShowFullscreenModeBtn && !isSbiDashPreview && (!isDisabledScreenMode || !isScreen))"
              class="sdpiconfont theme-dark-mode icon-sdp-Nguanbi"
              @click="handleCloseFullscreen"
            />

            <i v-show="isPinShow"
              :class="{
                [`icon-sdp-Ngudingshaixuanqu${commonData.staticParamsPanel ? '2' : '1'}`]: true,
                'theme-dark-mode': (themeData.enableTheme && themeData.themeType === 'sdp-dark-blue'),
              }"
              :style="{ marginRight }"
              :title="$t('sdp.views.borderPeg')"
              @click="clickStaticParamsPanel"
            />

            <!--大屏全屏按钮-->
            <i
              v-if="!isSbiDashPreview && isShowRightBtn && isScreen && !boardInfo.hideComponent && isShowFullScreenParamsPanel && (commonData.staticParamsPanel || !isParamsPanelSlideInUp) && (isShowFullscreenModeBtn && !isSbiDashPreview && (!isDisabledScreenMode || !isScreen))"
              class="sdpiconfont theme-dark-mode icon-sdp-a-Nquanping"
              style="line-height: 24px;"
              :title="$t('sdp.button.Fullscreen')"
              @click="handleFullscreen"
            />

            <div
              class="export-box"
              :class="[commonData.isPreview && !boardInfo.hideComponent ? 'export-box-right': '']"
              v-show="!isSbiDashPreview && commonData.isPreview && !isRealScreen && isShowRightBtn"
              ref="printItem"
            >
              <FileExport
                v-bind="{elList, boardInfo, boardRes, paramsPanelList, previewType, levelData,conformityApiData,dynamicTags, platformMetaDashboardConfig, init }"
                v-show="!isMobile && !isRemind && modalBool && !isOpenAutoRefresh"
                :startWeekDefaultValue="startWeekDefaultValue"
                :fullscreenState.sync="fullscreenState"
                :isIntelligentSearch="isIntelligentSearch"
                @startWeekChangeType="startWeekChangeType"
                @eventBus="eventBus"
                @eventEmit="eventEmit"
                :watermarkUrl="watermarkUrl"
                ref="fileExport"
              ></FileExport>
            </div>
          </div>
          <!-- 没有nameSub属性，占位保留关闭预览 -->
          <div v-if="supportShow && commonData.isPreview && modalBool && !boardInfo.nameSub && !(isShowParamsPanel && isHideTab) && !isScreen" class="sdp-support"></div>
          <params-panel
            v-if="renderParamsPanel"
            v-show="isShowFullScreenParamsPanel && isShowParamsPanel && isHideTab"
            :class="[
            utils.isDataReport ? 'params-data-report' : '',
            commonData.isPreview ? '' : toggle % 2 ? 'part-screen':'panel-active',
            {'slideIn': isParamsPanelSlideIn && isSlideInTransition },
            isParamsPanelFixed ? 'params-panel-fixed' : '',
            modalBool ? '' : 'modal-params-panel'
          ]"
            :style="{
            top: isParamsPanelSlideInUp ? `-${paramsPanelHeight}px` : '0',
              zIndex: isRealScreen ? 8 : undefined,
            }"

            :paramsPanelShowSync.sync="paramsPanelShow"
            ref="paramsPanel"
            class="paramspanel-pc theme-dialog-background"
            @onExportSuccess="() => $emit('onExportSuccess')"
            @onExportFail="() => $emit('onExportFail')"
            @setSupernatantTop="setSupernatantTop()"
            @finishRequest="finishRequest"
            @finishResultState="finishResultState"
            @changeBoardStatus="(type) => boardStatus = type"
            v-bind="bindParamsPanelData"
            @eventBus="eventBus"
          />
          <supernatant
            id="supernatant"
            v-loading="boardLoading"
            element-loading-spinner="sdp-loading-gif"
            ref="supernatant"
            class="supernatant-pc"
            :class="{
              'transition': needTransition,
              'theme-background': isDarkMode,
              'supernatant-slider': commonData.isPreview && !isMobile && !boardInfo.hideComponent,
              'screenModalBottomAndTop': !modalBool && isScreen && !commonData.staticParamsPanel,
              [kanbanId]: true,
              'freeze-panel': isParamsPanelFixed
            }"
            :style="{
              ...supernatantStyle,
              ...setLargeScreenFormatter(isScreen ? '' : 'elementBoardBg'),
              ...isDataReport && commonData.isPreview ? { bottom: '0 !important' } : {}
            }"
            :needTransition="needTransition"
            v-bind="bindSupernatantData"
            @eventBus="eventBus"
            @eventEmit="eventEmit"
          >
            <template v-slot:tabs="{ isCloseCarousel, isShowLargeTabs, screenModeDate, tagModeStack, pcTabsHeight, contentInfoStyle }">
              <supernatantTabs
                :key="boardRes && boardRes.id"
                :style="contentInfoStyle"
                @eventBus="eventBus"
                @triggerRun="triggerRun"
                @tabPanelChange="tabPanelChange"
                v-if="!boardInfo.openBoardTab || isShowLargeTabs"
                v-bind="{
                runCollection,
                commonData,
                dynamicTags,
                elList,
                init,
                isFinish,
                isDailyAdding,
                dynamicTagsRun,
                isCloseCarousel,
                tagModeStack,
                screenModeDate,
                paramsPanelList,
                supernatant: $refs['supernatant'],
                pcTabsHeight,
                isOpenAutoRefresh,
                boardInfo,
                isChangePos,
                previewModeStyle,
                modalBool,
                parent
              }" />
            </template>

            <!-- 看板描述 -->
            <template v-slot:boardRemark>
              <div
                class="supernatant-board-remark"
                v-if="isShowBoardDes"
              >
                {{boardDescriptionContent}}
                <i
                  v-if="commonData.isPreview && isTenant && isTenantReportPage && !exporting"
                  class="el-tooltip icon-sdp-zhibiaoxuanzeqibianji"
                  @click="() => { boardDescriptionVisible = true }"
                />
              </div>
            </template>

            <template v-slot:paramsInfo>
              <SelectedParamsInfo
                v-bind="{boardInfo,paramsPanelList,languageAndCurrency,show,exporting,paramsType,isEnterPriseCurrency, boardSlectLang}"
                v-if="commonData.isPreview && !boardInfo.hideComponent"
                @setOrderParamsList="setOrderParamsList"
              ></SelectedParamsInfo>
            </template>
            <template v-slot:language>
              <LanguageSwitching
                ref="LanguageSwitching"
                @onHide="hideParam"
                @saveFixedTemp="saveFixedTemp"
                @change-runtime="changeRuntime"
                @eventBus="eventBus"
                @onChangeCurrency="onChangeCurrency"
                @onChangeLanguage="onChangeLanguage"
                @currencyAbbr="emitCurrencyAbbr"
                :isCurrencyAble="isEnterPriseCurrency"
                :isLanguageAble="isEnterPriseLanguage"
                :themeFullScreen="themeFullScreen"
                :boardStatus="boardStatus"
                :showBoardDescription.sync="showBoardDescription"
                :boardDescriptionContent="boardDescriptionContent"
                :isParamsPanelFixed="isParamsPanelFixed"
                :paramsPanelHeight="paramsPanelHeight"
                :isCurrencyAbbr.sync="isCurrencyAbbr"
                v-bind="bindLanguageSwitchingData"
                v-if="commonData.isPreview && !boardInfo.hideComponent && !themeFullScreen && !boardInfo.autoRefreshData.isAutoRefresh"
              ></LanguageSwitching>
            </template>
            <template v-slot:breadcrumb="{contentInfoStyle, isShowBreadcrumb}">
              <breadcrumb
                v-if="isShowBreadcrumb"
                :style="Object.assign({}, contentInfoStyle, { top: `${!boardInfo.hideComponent && isParamsPanelFixed && !isLargeScreen ? paramsPanelHeight + 1 : 1}px`})"
                v-bind="{ modalBool, boardArr: superLinkArr, boardInfo, superLinkData, isHideTitle }"
                separator-class="el-icon-arrow-right"
                @retreatSkip="retreatSkip"
              />
            </template>
            <template v-slot:fullscreenMode>
              <div class="fullscreenModeBox" v-if="isShowScreenModeBox">
                <i
                  class="sdpiconfont theme-dark-mode icon-sdp-a-Nquanping"
                  :title="$t('sdp.button.Fullscreen')"
                  @click="handleFullscreen"
                />

                <i class="sdpiconfont theme-dark-mode icon-sdp-Nguanbi"
                  @click="handleCloseFullscreen"
                ></i>
              </div>
            </template>
          </supernatant>
        </div>
      </div>
      <!-- 多语言弹窗 -->
      <!-- 杜邦分析图先注释，后续需求会需要 -->
      <!-- :has-tab-desc-lang-dupont-element-list="hasTabDescLangDupontElementList" -->
      <!-- @updateDupontElListDesc="updateDupontElListDesc" -->
      <language-dialog
        ref="languageDialog"
        :board-tree-data="boardTreeData"
        :has-tab-desc="hasTabDesc"
        :has-tab-desc-lang-table-element-list="hasTabDescLangTableElementList"
        :has-tab-desc-lang-customer-element-list="hasTabDescLangCustomerElementList"
        :board-status="boardInfo.status"
        :new-board-content="newBoardContent"
        :checked-tree-list="checkedTreeList"
        :popout-visible.sync="languageDialogVisible"
        :languageFrom.sync="languageFrom"
        :paramsPanelList="paramsPanelList"
        :elList="elList"
        :hasTipSet="true"
        :datasetList="datasetList"
        @updateElListDesc="updateElListDesc"
        @updateTableElListDesc="updateTableElListDesc"
        @updateCustomerElListDesc="updateCustomerElListDesc"
      />
      <!-- 看板描述弹窗 -->
      <boardDescriptionDialog
        v-if="commonData.isPreview && isTenant && isTenantReportPage"
        :visible.sync="boardDescriptionVisible"
        :boardInfo="boardInfo"
      />
      <!-- 看板弹框 -->
      <board-dialog
        v-if="commonData.isPreview && dialogVisible"
        :dialogVisible.sync="dialogVisible"
        :superLinkData="superLinkData"
        :isScreen="isScreen"
        :isMobileTablet="isMobileTablet"
        :tabletScale="tabletScale"
        :modelExporting="modelExporting"
      >
        <template v-slot:default="slotProps">
          <!-- modalHeight:slotProps.height -->
          <displayPanel
            v-loading="dialogLoading"
            element-loading-spinner="sdp-loading-gif"
            class="super-link-dialog"
            :modalBool="slotProps.modalBool"
            :modalDatas="{
              modalSuperLinkData:slotProps.superLinkData,
              modalSuperLinkArr:superLinkArr,
              modalData:information,
              modalStatus:boardStatus,
              modalParamsType:paramsType,
              modalBoardSlectLang:boardSlectLang,
              modalPreviewType:previewType,
              modalViewLevelData:viewLevelData,
              modalIsCurrencyAbbr:isCurrencyAbbr,
              modalRunSource: runSource,
              modalThemeData: themeData,
            }"
            @dingFlagChange="staticParamsPanelChange"
            @currencyAbbrChange="currencyAbbrChange"
            @exportingChange="(val) => modelExporting = val"
            @getBoardMergeInfo="dialogLoadingChange"
          />
        </template>
      </board-dialog>
    <!-- </el-scrollbar> -->
  </div>
</template>

<script type="text/ecmascript-6">
import displayPanel_mixin from '../displayPanel/index'
import { closeTabMode, generateRealEl } from '../displayPanel/utils'
import { EVENT_BUS, SUPERNATANT_TRANSITION_DURATION, TYPE_ELEMENT } from 'packages/base/board/displayPanel/constants'
import websocket_mixin from '../mixins/websocket'
import { THEME_TYPE, PREVIEW_STATUS } from 'packages/assets/constant'
import { changeDingFlag, changeScreenDingFlag, changeReportDingFlag } from '../displayPanel/components/api'
import SdpElScrollbar from 'packages/directives/v-el-scrollbar'
import SdpBorderScroll from 'packages/directives/v-border-scrollbar'
import { DISPLAY_PANEL_COMPONENT_NAME } from '../utils/constant'
import { initFilterSorterHandler, logByFilterSorter } from '../displayPanel/supernatant/filterDialog/utils'
import { getPictures } from '../../../../packagesTs/utils'
import elementResizeDetectorMaker from 'element-resize-detector'
import { largeScreenFormatter } from '../displayPanel/components/utils'
import { CALL_TYPE } from '../../../../packagesTs/fullscreenClass'
import EventData from '../../../assets/EventData'
import { isRealScreenFn } from 'packages/helpers'
import BoardDescriptionDialog from '../displayPanel/components/boardDescriptionDialog'
import { SUBSCRIPTION_TYPE } from '../displayPanel/constants'
import intelligent_mixins from '../intelligentSearch/intelligent_mixins.ts'
import boardScaleMixin from '../mixins/boardScaleMixin'

export default {
  inject: {
    isMultiplePage: { default: false },
    isSbiPreview: { default: false },
  },
  name: DISPLAY_PANEL_COMPONENT_NAME,
  components: { BoardDescriptionDialog },
  componentName: DISPLAY_PANEL_COMPONENT_NAME,
  mixins: [displayPanel_mixin, websocket_mixin, intelligent_mixins, boardScaleMixin],
  data() {
    return {
      modelExporting: false,
      bigFullscreenState: false, // 大屏看板
      fullscreenState: false, // pc看板
      paramsPanelHeight: 0,
      isSuperLink: false, // 判断是否是弹窗中的看板
      needTransition: false, // 首次进入看板时不执行过度效果
      themeFullScreen: false,
      isSlideInTransition: false,
      supernatantStyle: {},
      paramsPanelShow: true,
      boardDescriptionVisible: false,

      marginRight: '0',
      templateBoardBgImgIds: [],
      supernatantHeight: 0,
      parent: this,
      isChangePos: false,
      previewModeStyle: {},
      renderParamsPanel: true,
      activeDisplayPanel: ''
    }
  },
  provide() {
    return {
      getActiveDisplayPanel: () => this.activeDisplayPanel
    }
  },
  directives: { SdpElScrollbar, SdpBorderScroll },
  computed: {
    isShowRightBtn() {
      return true
      // return !this.isDisabledScreenMode
      // return !this.themeFullScreen && (!this.isDisabledScreenMode || !this.isScreen)
    },
    boardStyle() {
      const isScreenAndModalBool = !this.modalBool && this.isRealScreen
      return {
        minWidth: (isScreenAndModalBool || this.themeFullScreen) ? '100%' : (this.isSbiPreview && this.modalBool) ? '800px' : '1280px'
      }
    },
    isShowBoardDes() {
      return this.commonData.isPreview &&
             (
               (this.showBoardDescription && this.boardInfo.showBoardDescription) || // 正常情况
               (this.exporting && this.boardInfo.exportStyle.pdfBoardRemark === '1') // 导出
             )
    },
    dataScreenClass() {
      if (!this.modalBool) return ['modal-screen', 'modal-screen-color']

      if (this.commonData.isPreview && (this.isFullScreen || this.isScreen)) {
        let arr = ['full-screen']
        this.fullScreenLoading && arr.push('scroll-disable')
        return arr
      } else if (this.isFullScreen) {
        return [this.toggle % 2 === 0 ? 'part-screen' : 'panel-active']
      } else {
        let arr = ['modal-screen', this.commonData.isPreview && !this.isFullScreen ? 'index-screen' : 'modal-screen-color']
        return arr
      }
    },
    isHideTitle() {
      return !this.themeFullScreen && this.commonData.isPreview && !this.utils.isMobile && (!!(this.boardInfo.nameSub || '') || !this.boardInfo.hideComponent)
    },
    settingConfig() {
      return this.tenantData.settingConfig
    },
    boardDescriptionContent() {
      if (this.isTenant && this.isTenantReportPage) {
        return this.boardInfo.remark || ''
      }
      let lang = this.commonData.boardSlectLang() || this.langCode
      const langItem = this.newBoardContent.metaDashboardLanguageList.find(e => e.type === 'remark' && e.languageCode === lang)
      return langItem?.value || this.boardInfo.remark || ''
    },
    supportShow() {
      return this.previewType !== PREVIEW_STATUS.REMIND
    },
    // sbi仪表盘预览界面
    isSbiDashPreview() {
      return this.utils?.sbiOptions?.isSbiDashPreview || false
    },
    isAddDailyConcernElement() {
      return this.utils?.sbiOptions?.isAddDailyConcernElement || false
    },
    isDarkMode() {
      return this.themeData.themeType === THEME_TYPE.darkBlue && this.themeData.enableTheme && this.commonData.isPreview
    },
    // isStackParms() {
    //   return this.commonData.isPreview && this.staticParamsPanel && !this.boardInfo.hideComponent
    // },
    hoverIcon() {
      return this.commonData.hoverIcon && !this.boardInfo.hideComponent && this.commonData.isPreview
    },
    isParamsPanelSlideIn() {
      return !this.commonData.staticParamsPanel && this.commonData.isPreview
    },
    isParamsPanelSlideInUp() {
      return this.isParamsPanelSlideIn && !this.paramsPanelShow
    },
    isParamsPanelFixed() {
      return this.isShowParamsPanel && this.commonData.staticParamsPanel && this.commonData.freezeParamsPanel && this.commonData.isPreview
    },
    isShowWatermark() {
      return this.commonData.isPreview && !this.themeData.themeFullScreen && this.watermarkUrl
    },
    isRealScreen() {
      return isRealScreenFn(this.utils)
    },
    // 是否显示钉住图标
    isPinShow() {
      let isPcBoard = !this.utils?.isMobile && !this.isRealScreen
      let isSbiDashPreview = this.utils?.sbiOptions?.isSbiDashPreview && isPcBoard
      let dingFlag = false
      if (this.isRealScreen) {
        dingFlag = this.settingConfig.screenDingFlag === '1'
      } else if (this.isDataReport) {
        dingFlag = this.platformMetaDashboardConfig?.reportDingFlag === '1'
      } else {
        dingFlag = this.settingConfig.dingFlag === '1'
      }
      const isShowFullScreenParamsPanel = this.isRealScreen ? this.isShowFullScreenParamsPanel || false : true
      return (isSbiDashPreview || this.hoverIcon) && !this.isOpenAutoRefresh && dingFlag && isShowFullScreenParamsPanel
    },
    isShowOff() {
      const closeFlag = this.isDataReport
        ? this.platformMetaDashboardConfig?.reportCloseFlag === '1'
        : this.settingConfig.closeFlag === '1'

      return this.commonData.isPreview &&
        !(this.commonData.isSubscribe && this.isMultiplePage) &&
        this.isFullScreen &&
        this.isShowRightBtn &&
        !this.isRealScreen &&
        this.modalBool &&
        closeFlag &&
        !this.fullscreenState &&
        !this.utils.isShare &&
        !this?.elementWarnSubscription?.isEMail
    },
    isShowScreenModeBox() {
      const isHide = !this.commonData.staticParamsPanel || this.boardInfo.hideComponent || !this.isShowFullScreenParamsPanel
      return isHide && this.isShowFullscreenModeBtn && !this.isSbiDashPreview && !(this.isDisabledScreenMode && this.isScreen)
    },
    bindParamsPanelData() {
      return {
        hasFav: this.hasFav,
        nowStr: this.nowStr,
        conformityApiData: this.conformityApiData,
        activeTabIds: this.activeTabIds,
        toggle: this.toggle,
        beforeHook: this.beforeHook,
        elList: this.elList,
        boardInfo: this.boardInfo,
        languageAndCurrency: this.languageAndCurrency,
        isEnterPriseCurrency: this.isEnterPriseCurrency,
        paramsPanelList: this.paramsPanelList,
        orderParamsList: this.orderParamsList,
        dynamicTags: this.dynamicTags,
        paramsType: this.paramsType,
        previewType: this.previewType,
        levelData: this.levelData,
        boardStatus: this.boardStatus,
        // datasetList: this.datasetList,
        paramsPanelHeight: this.paramsPanelHeight,
        isFinish: this.isFinish,
        getWebsocketData: this.getWebsocketData,
        parent: this,
        dynamicTagsRun: this.dynamicTagsRun,
        paramsDataItem: this.paramsDataItem,
        // datasetList: this.datasetList,
        boardBtnOptions: this.boardBtnOptions
      }
    },
    bindSupernatantData() {
      return {
        layoutDeploy: this.layoutDeploy,
        // datasetList: this.datasetList,
        init: this.init,
        toggle: this.toggle,
        elList: this.elList,
        boardInfo: this.boardInfo,
        titleStyle: this.titleStyle,
        dynamicTags: this.dynamicTags,
        themeFullScreen: this.themeFullScreen,
        isFinish: this.isFinish,
        recordRunTimes: this.recordRunTimes,
        paramsPanelList: this.paramsPanelList,
        isScreenSkipToScreen: this.isScreenSkipToScreen,
        breadcrumbHeight: this.breadcrumbHeight,
        isHideTitle: this.isHideTitle,
        hideSupernatantTop: this.hideSupernatantTop,
        parent: this,
        watermarkUrl: this.watermarkUrl,
        superLinkArr: this.superLinkArr,
        dialogVisible: this.dialogVisible,
        modalBool: this.modalBool,
        isChangePos: this.isChangePos,
        isDisabledScreenMode: this.isDisabledScreenMode,
        languageAndCurrency: this.languageAndCurrency,
        dailyConcernData: this.dailyConcernData,
      }
    },
    bindLanguageSwitchingData() {
      return {
        elList: this.elList,
        refreshEl: this.refreshEl,
        boardInfo: this.boardInfo,
        requestOptions: this.requestOptions,
        languageAndCurrency: this.languageAndCurrency,
        languageStatus: this.languageStatus,
        show: this.show,
        fixedOptionTemp: this.fixedOptionTemp,
        utils: this.utils,
        previewType: this.previewType,
        levelData: this.levelData,
        superLinkArr: this.superLinkArr,
        isRuntimeBtnClass: this.isRuntimeBtnClass,
        conformityApiData: this.conformityApiData,
        isDisabledScreenMode: this.isDisabledScreenMode,
      }
    },
    hideSupernatantTop() {
      return this.themeFullScreen ? 0 : 22
    },
    getCurrentDynamicTags() {
      return this.dynamicTags.find(v => v.active) || {}
    },
    // 大屏参数组件配置数据
    paramsDataItem() {
      if (!this.themeFullScreen) return null

      const { id: paramId = '' } = this.paramsPanelList.find(v => v.active) || {}
      const paramsData = this.boardInfo?.screenModeDate?.paramsData || {}
      if (this.boardInfo.openBoardTab) {
        const { id = '' } = this.getCurrentDynamicTags
        const { midware = {} } = this.boardInfo.tagModeStack || {}
        for (let key in midware) {
          const { id: tabId } = midware[key]
          if (tabId === id) {
            if (paramsData[key]) {
              const keys = Object.keys(paramsData[key].content)
              const { content = [] } = this.paramsPanelList.find(v => v.id === key)
              const ids = content.map(el => el.id)
              if (keys.some(id => ids.includes(id))) {
                return paramsData[key]
              }
            }
            return undefined
          }
        }
      }
      return paramsData[paramId]
    },
    isTvScreen() {
      return this.utils.isTvScreen
    },
    isHiddenX() {
      // 大屏TV不展示滚动条
      const height = this.isSbiDashPreview ? `100%` : ''
      if (this.isTvScreen) return { 'overflow': 'hidden', height }

      const overflow = (this.commonData.isPreview && this.boardInfo.isHideXBar) || this.themeFullScreen ? 'hidden' : 'auto'
      return { 'overflow-x': overflow, height }
    },
    isShowFullScreenParamsPanel() {
      // 大屏TV不展示参数组件
      if (this.isTvScreen) return false

      const data = this.paramsDataItem
      let getHideElement = (list) => {
        return list && list.filter(item => {
          return !item.isHideElement
        })?.length
      }
      if (this.isRealScreen && this.themeFullScreen) {
        if (this.boardInfo.openBoardTab) {
          const { id = '' } = this.getCurrentDynamicTags
          const { midware = {} } = this.boardInfo.tagModeStack || {}
          return Object.entries(midware).some(([key, el]) => {
            if (el.id === id) {
              const { content = [] } = this.paramsPanelList.find(el => el.id === key) || {}
              return !!content.length && getHideElement(content)
            }
            return el.id === id
          })
        }
        const el = this.paramsPanelList.find(el => el.active)
        // 判断是否全部隐藏组件
        return !!el?.content?.length && getHideElement(el.content)
      }
      // if (this.themeFullScreen && data) {
      //   const idSet = this.paramsPanelList.reduce((idSet, params = {}) => new Set([...idSet, ...(params.content || []).map(({ id }) => id)]), new Set())
      //   return Object.entries(data.content).some(([key, el]) => el.open && idSet.has(key))
      // }
      return !this.themeFullScreen
    },
    isSetParamsPanelHeight() {
      return this.themeFullScreen || this.paramsPanelHeight
    },
    setLargeScreenFormatter (formatter) {
      if (!formatter) return ''
      return function (formatter) {
        let activePanel = this.dynamicTags.find(item => item.active)
        const canvasBg = activePanel?.[formatter]
        if (this.isMobile || !canvasBg) return {}
        const obj = largeScreenFormatter(activePanel, formatter, this.utils.themeParameters)
        return {
          '--sdp-large-screen-bg': obj?.background?.replace('!important', '') || '',
          ...obj,
        }
      }
    },
    isShowFullscreenModeBtn() {
      return this.commonData.isPreview && !this.bigFullscreenState && this.modalBool && this.isRealScreen && !this.isTvScreen
    },
    hasTabDescLangTableElementList() {
      let list = []
      this.elList.forEach(item => {
        if (item.type === TYPE_ELEMENT.TABLE) {
          console.log(item.content.dataScreenRes.tableControlsLanguageList)
          if (!Object.keys(item.content.dataScreenRes).length) { // 如果新增的图形未保存过，需要过滤掉
            return
          }
          let resList = item.content?.dataScreenRes?.tableControlsLanguageList.filter(i => i.type === 'remark') || []
          list.push({
            chartDescription: item.content?.dataScreenRes.remark || '',
            res: resList,
            descInputVisible: true,
            id: item.id,
            type: item.type,
            value: item.elName
          })
        }
      })
      return list
    },
    hasTabDescLangCustomerElementList() {
      let list = []
      this.elList.forEach(item => {
        if (item.type === TYPE_ELEMENT.CUSTOMER_ELEMENT) {
          // console.log(item.content.dataScreenRes.tableControlsLanguageList)
          // if (!Object.keys(item.content.dataScreenRes).length) { // 如果新增的图形未保存过，需要过滤掉
          //   return
          // }
          let resList = item.content?.userConfig?.languageList?.filter(i => i.type === 'remark') || []
          list.push({
            chartDescription: item.content?.userConfig.remark || '',
            res: resList,
            descInputVisible: true,
            id: item.id,
            type: item.type,
            value: item.elName
          })
        }
      })
      return list
    },
    // hasTabDescLangDupontElementList(){
    //   let list = []
    //   this.elList.forEach(item=>{
    //     if(item.type===TYPE_ELEMENT.DUPONT_ANALYSIS){
    //       let obj = {
    //         children: [],
    //         value:item.elName,
    //         descInputVisible:false,
    //         id:item.id,
    //         // langId:item.langId,
    //         type:item.type,
    //         content:item.content,
    //       }
    //       if(!item.content?.cardData?.length){  // 如果新增的杜邦分析未保存过，需要过滤掉
    //         return
    //       }
    //       item.content.cardData.forEach(i=>{
    //         if(i.content.optionArray===''){ // 如果新增的杜邦分析中的卡片未保存过，需要过滤掉
    //           return
    //         }
    //         let resList =  item.content?.userConfig?.lang?.tableControlsElementLanList.filter(ii=>{
    //           let rules = ii.key.includes('_remark') && ii.key.includes(i.langId)
    //           return rules
    //         })
    //         obj.children.push({
    //           chartDescription:i.content.chartUserConfig.remark || '',
    //           res:resList || [],
    //           descInputVisible:true,
    //           id:i.id,
    //           langId:i.langId,
    //           value:i.content.optionArray[0].cardName || '', // 因为杜邦只是单指标所以0
    //         })
    //       })
    //       list.push(obj)
    //     }
    //   })
    //   return list
    // },
    hasTabDesc() {
      return !this.isMobile && !this.utils.isDataReport && !this.utils.isLargeScreen
    }
  },
  watch: {
    'boardInfo.openBoardTab': {
      handler(val) {
        if (!val && ((this.boardInfo.tagModeStack && Object.keys(this.boardInfo.tagModeStack).length === 0) || !this.boardInfo.tagModeStack)) return
        this.$nextTick(() => {
          val ? this.initTagMode() : closeTabMode.call(this)
        })
      }
    },
    isSetParamsPanelHeight(val) {
      this.watchSupernatantTop(val)
    },
    themeFullScreen() {
      setTimeout(() => {
        this.setSupernatantStyle()
      })
    },
    'themeData.themeFullScreen'(bool) {
      if (bool) {
        logByFilterSorter('由于目前不支持大屏, 因此使用了"过滤器/筛选器"的看板元素, 进入大屏后重置"过滤器/筛选器"配置')
      }
    },
    paramsPanelHeight() {
      if (!this.commonData.isPreview) {
        this.setSupernatantStyle()
      } else {
        if (this.isScreen) {
          this.setSupernatantStyle()
        }
      }
    },
    'commonData.isPreview'(v) {
      setTimeout(() => {
        this.handleScroll()
        if (!v) {
          this.handleCloseFullscreen()
        }
        this.updateParamsPanelHeight()
      }, 0)
    },
    'commonData.staticParamsPanel'() {
      setTimeout(() => {
        this.handleSendData(['staticParamsPanel'], { staticParamsPanel: this.commonData.staticParamsPanel })
        this.handleScroll()
        this.setSupernatantStyle()
      }, 0)
    },
    isShowFullScreenParamsPanel() {
      if (!this.isScreen) return
      this.setSupernatantStyle()
    },
    'boardInfo.tabSuspension'() {
      this.handleScroll()
    },
    isPinShow: {
      handler(val) {
        this.handleSendData(['isPinShow'], { isPinShow: val })
      },
      immediate: true
    },
    'utils.isPcMobileEdit': {
      handler(val, oldVal) {
        if (oldVal && !val) {
          this.renderParamsPanel = false
          this.$nextTick(() => {
            this.renderParamsPanel = true
            this.$nextTick(() => {
              this.$refs?.paramsPanel?.setInitParameterBtn()
              // 视角层级，解决location切换到pc被禁用的情况
              this.setTenantDataView()
            })
          })
        }
      },
      immediate: true
    }
  },
  created() {
    this.getLangList()
    this.getStandardChartSchemeList()
  },
  mounted() {
    this.sdpBus.$on(EVENT_BUS.SBI_BUTTON_PIN, this.clickStaticParamsPanel)
    this['sdpBus'].$on(EVENT_BUS.SET_APPEND_MESSAGE_EL, this.setAppendMessageEl)
    this.mousemoveNone = this.$_debounce(this.mousemoveNone, 1500)
    const { supernatant, paramsPanel } = this.$refs
    if (supernatant?.$el) {
      supernatant.$el.addEventListener('mouseenter', this.mousemoveNone)
      supernatant.$el.addEventListener('mouseout', this.mousemoveNone)
    }
    if (paramsPanel?.$el) {
      paramsPanel.$el.addEventListener('mousemove', this.mousemoveNone)
      paramsPanel.$el.addEventListener('mouseout', this.mousemoveNone)
    }
    // 监听元素宽度变化
    this.erd = elementResizeDetectorMaker()
    this.erd.listenTo(this.$refs.paramsPanel.$el, (element) => {
      if (!element || !element.offsetHeight || this.utils.isPcMobileEdit) return
      const height = element.offsetHeight
      if (this.paramsPanelHeight !== height) {
        this.paramsPanelHeight = height
        this['sdpBus'].$emit(EVENT_BUS.FEATURE_HEIGHT_UPDATE)
      } else {
        this.watchSupernatantTop()
      }
    })
    // 监听元素高度变化
    this.erd2 = elementResizeDetectorMaker()
    this.erd2.listenTo(this.$refs.supernatant.$el, (element) => {
      if (!element || !element.offsetHeight) return
      const height = element.offsetHeight
      if (height !== this.supernatantHeight) {
        this.supernatantHeight = height
      }
    })
    // 延迟获取数据
    setTimeout(() => {
      this.marginRight = this.getMarginRight()
    }, 1000)
    if (!this.isMobile) {
      // 监听滚动
      const dom = this.getBoardUnderIdElement('#data-screen-table')
      dom.onscroll = () => {
        this.handleScroll()
      }
    }
  },
  beforeDestroy() {
    this.sdpBus.$off(EVENT_BUS.SBI_BUTTON_PIN, this.clickStaticParamsPanel)
    this['sdpBus'].$off(EVENT_BUS.SET_APPEND_MESSAGE_EL, this.setAppendMessageEl)
    const { supernatant, paramsPanel } = this.$refs
    if (supernatant?.$el) {
      supernatant.$el.removeEventListener('mouseenter', this.mousemoveNone)
      supernatant.$el.removeEventListener('mouseout', this.mousemoveNone)
    }
    if (paramsPanel?.$el) {
      paramsPanel.$el.removeEventListener('mousemove', this.mousemoveNone)
      paramsPanel.$el.removeEventListener('mouseout', this.mousemoveNone)
    }
    // 移除所有监听
    this.isSuperLink = false
    if (this.$refs.paramsPanel.$el) {
      this.erd.uninstall(this.$refs.paramsPanel.$el)
    }
    if (this.$refs.supernatant.$el) {
      this.erd2.uninstall(this.$refs.supernatant.$el)
    }
  },
  methods: {
    handleScroll() {
      if (!this.boardInfo.tabSuspension) {
        this.previewModeStyle = {}
        return
      }
      let isTabSuspension = this.commonData.isPreview && this.boardInfo.tabSuspension
      if (this.isScreen) {
        this.isChangePos = isTabSuspension
        this.getPreviewModeStyle()
        return
      }
      const domScrollTop = this.modalBool ? document.getElementById('data-screen-table')?.scrollTop : document.getElementsByClassName('modal-screen')[0]?.scrollTop
      const containerMode = document.getElementById('previewModeTabs')
      const containerModeHeight = containerMode?.offsetTop + containerMode?.offsetHeight || 0
      const isScrollMaxHeight = domScrollTop > containerModeHeight
      this.isChangePos = isTabSuspension && isScrollMaxHeight
      this.getPreviewModeStyle()
    },
    getPreviewModeStyle() {
      const paramsPanelHeight = document.getElementsByClassName(this.modalBool ? 'paramspanel-pc' : 'modal-params-panel')[0]?.offsetHeight
      let paramsHeight = this.commonData.staticParamsPanel ? paramsPanelHeight || 0 : 0
      let screenSpaceTop = (document.getElementsByClassName('screenSpaceTop')[0]?.offsetHeight || 0) + 68
      if (this.modalBool) {
        this.previewModeStyle = {
          top: this.isChangePos ? this.utils.isScreen ? screenSpaceTop + 'px' : paramsHeight + 10 + 'px' : '',
          position: 'fixed !important'
        }
      } else {
        const domScroll = document.getElementsByClassName('super-link-dialog')[0]?.scrollTop
        if (this.utils.isScreen) {
          screenSpaceTop = screenSpaceTop + domScroll
        } else {
          const containerModeTop = document.querySelector('.super-link-dialog .board-veiw-container').offsetHeight + document.querySelector('.super-link-dialog .selected-params-info').offsetHeight
          paramsHeight = domScroll - containerModeTop
        }
        this.previewModeStyle = {
          top: this.isChangePos ? this.utils.isScreen ? screenSpaceTop + 'px' : paramsHeight + 'px' : '',
          position: 'absolute !important'
        }
      }
    },
    updateParamsPanelHeight() {
      const element = this.$refs.paramsPanel
      if (element) {
        if (!element || !element?.$el?.offsetHeight) return
        const height = element.$el.offsetHeight
        if (this.paramsPanelHeight !== height) {
          this.paramsPanelHeight = height
          this['sdpBus'].$emit(EVENT_BUS.FEATURE_HEIGHT_UPDATE)
        } else {
          this.watchSupernatantTop()
        }
      }
    },
    handleFullscreen() {
      this.fullscreenClass.toggleFullscreen('enter', CALL_TYPE.Click)
    },
    handleCloseFullscreen() {
      this.fullscreenClass.changeFullscreen(false)
    },
    getMarginRight() {
      if (this.modalBool) return '0'
      const el = this.$refs.borderBoxRef
      return el.scrollHeight < el.clientHeight ? '15px' : '9px'
    },
    watchSupernatantTop() {
      this.$nextTick(() => {
        if (this.commonData.isPreview) {
          // 布尔值说明进入了大屏
          if (typeof (this.themeFullScreen || this.paramsPanelHeight) === 'boolean') {
            this.$nextTick(() => {
              this.upDataSupernatantTop()
            })
          } else {
            this.setSupernatantTop(true)
          }
        }
      })
    },
    setSupernatantStyle() {
      const supernatant = this.$refs.supernatant.$el
      const topStr = supernatant?.style?.top || '0px'
      const top = parseInt(topStr)

      this.supernatantStyle = Object.assign(
        // !this.commonData.isPreview ? { 'height': `calc(100% - ${this.paramsPanelHeight + 13}px)` } : {},
        !this.commonData.isPreview || this.boardInfo?.hideComponent || this.boardInfo?.autoRefreshData.isAutoRefresh ? {
          top: '0'
        } : {},
        // this.themeFullScreen ? {
        //   height: `${window.screen.height - this.hideSupernatantTop}px`
        // } : {},
        this.needTransition ? { 'transition-duration': SUPERNATANT_TRANSITION_DURATION + 'ms' } : {},

        this.isShowFullScreenParamsPanel &&
        !this.boardInfo?.hideComponent &&
        this.commonData.isPreview &&
        this.isRealScreen &&
        this.commonData.staticParamsPanel &&
        !this.commonData.freezeParamsPanel &&
        this.settingConfig.screenDingFlag === '1' ? { 'margin-top': (this.paramsPanelHeight - top) + 'px' } : {}
      )
    },
    // 弹框在大屏下展示调用该方法
    setAppendMessageEl(el) {
      this.$nextTick(() => {
        if (el) {
          this.$refs.fullscreen.$el.appendChild(el?.$el || el)
        }
      })
    },
    judgeParamsCondition() {
      this.paramsPanelShow = this.commonData.staticParamsPanel
      this.commonData.hoverIcon = this.commonData.staticParamsPanel
    },
    // 跳转超链接/预览/退出预览时需要更新参数组件上下拉状态
    setRunStatus() {
      this.setSupernatantStyle()
      if (this.commonData.isPreview) {
        if (this.boardInfo?.hideComponent || this.boardInfo?.autoRefreshData.isAutoRefresh) return
        this.judgeParamsCondition()
        this.setSupernatantTop(true)
      } else {
        this.paramsPanelShow = true
      }
    },
    handleClick() {
      const mousedown = document.createEvent('MouseEvents')
      const mouseup = document.createEvent('MouseEvents')
      const click = document.createEvent('MouseEvents')
      mousedown.initEvent('mousedown', false, true)
      mouseup.initEvent('mouseup', false, true)
      click.initEvent('click', false, true)
      document.dispatchEvent(mousedown)
      document.dispatchEvent(mouseup)
      document.dispatchEvent(click)
    },
    mousemoveNone(event) {
      if (this?.boardInfo?.hideComponent) return
      const paramsPanel = this.$refs.paramsPanel || {}
      const supernatant = this.$refs.supernatant || {}
      if (paramsPanel.$el && paramsPanel.$el.contains(event.target)) return
      if (supernatant.$el && event.type === 'mouseout' && event.relatedTarget && !supernatant.$el.contains(event.relatedTarget)) return
      if (this.commonData.isPreview && !this.commonData.staticParamsPanel && this.paramsPanelShow && !paramsPanel.isMoveDisabled) {
        this.handleClick()
        this.judgeParamsCondition()
        this.setSupernatantTop()
      }
    },
    emitCurrencyAbbr(v) {
      this.$emit('currencyAbbrChange', v)
    },
    // 禁止动画效果
    async clickStaticParamsPanel() {
      try {
        const staticParamsPanel = this.isRealScreen
                                    ? await changeScreenDingFlag(this.utils.api, { dingFlag: (!this.commonData.staticParamsPanel ? '1' : '0') })
                                    : this.isDataReport ? await changeReportDingFlag(this.utils.api, { dingFlag: (!this.commonData.staticParamsPanel ? '1' : '0') }) : await changeDingFlag(this.utils.api, { dingFlag: (!this.commonData.staticParamsPanel ? '1' : '0') })
        this.commonData.staticParamsPanel = staticParamsPanel
        if (staticParamsPanel) {
          this.setSupernatantTop()
        }
        this.$emit('dingFlagChange', staticParamsPanel)
      } catch (e) {
        console.log(e)
      }
    },
    staticParamsPanelChange(val) {
      if (this.commonData.staticParamsPanel !== val) {
        this.commonData.staticParamsPanel = val
        if (val) {
          this.setSupernatantTop()
        }
      }
    },
    // 传参数据
    handleSendData(key = [], data = {}) {
      // todo 判断sbi才执行
      let targetFile = this.isRealScreen ? 'sdpBoardFullscreenPreview' : 'sdpBoardPreview'
      const eventData = new EventData({
        type: '',
        target: targetFile,
        targetFn: 'getDisplayData',
        source: 'displayPanel',
        key,
        data: {
          ...data
        }
      })
      this.$emit('eventBus', eventData)
    },
    upDataSupernatantTop(top = '0px') {
      const supernatant = this.$refs.supernatant.$el
      if (supernatant && supernatant.style.top !== top) {
        supernatant.style.top = top
      }
    },
    setSupernatantTop(data) {
      if (this.themeFullScreen) return
      this.setNeedTransition(data)
      this.$nextTick(() => {
        const top = this.paramsPanelShow || this.commonData.staticParamsPanel
          ? `${this.paramsPanelHeight}px`
          : `${this.hideSupernatantTop}px`
        this.upDataSupernatantTop(top)
      })
    },
    setNeedTransition(data) {
      this.needTransition = !data
      setTimeout(() => {
        this.needTransition = true
      })
    },
    setSlideInTransition() {
      this.isSlideInTransition = true
      setTimeout(() => {
        this.isSlideInTransition = false
      }, 1000)
    },
    changeSuperLinkFlag() {
      this.isSuperLink = true
    }
  },
  deactivated() {
    this.activeDisplayPanel = 'MOBILE'
  },
  activated() {
    this.activeDisplayPanel = 'PC'
  }
}
</script>

<style lang="scss" scoped>
@import '../../../assets/styles/animation.css';
.sdp-box {
  display: flex;
  flex-direction: row-reverse;
  padding-top:8px;
  position:absolute;
  z-index:8;
  top:0;
  right:0;
}
.box-inherit {
  position: inherit;
  padding-top: 4px;
  padding-bottom: 4px;
}
.fullscreenModeBox {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 8px;
}
/*大屏*/
/*.full-screen-class-preview {
  @include sdp-background_0();
}
// 主题背景色
.theme-screen-background{
  @include sdp-mixin-style($type:(
    backgroundColor:('themeFulssBackground':true),
  ));
}
.full-screen-class {
  flex: 1;
  display: flex;
  flex-direction: column;
}*/
/*-----------------------------*/

.theme-dialog-background {
  @include sdp-mixin-style($type:(
    // 背景颜色调整
    backgroundColor:('themeDialogBackground':true),
  ));
}
.hiddenScroll{
  overflow: hidden!important;
}
// 主题背景色
#board-display-panel-border-box {
  display: flex;
  flex-direction: column;
}
.board-display-panel-box {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}
.normal-bg {
}
.preview-bgc {
  // @include sdp-mixin-style($type:(
  //   backgroundColor:(dialogbgc:true),
  // ));
  &.watermark-area {
    .sdp-box {
      z-index: 10000;
    }
    .paramspanel-pc {
      z-index: 9999;
    }
  }
  // .watermark {
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  //   z-index: 999;
  //   pointer-events: none;
  // }
}
// .theme-background {
//   @include sdp-background_0();
// }

.supernatant-pc.transition {
  transition-property: top;
  transition-timing-function: ease;
  transition-delay: 0s;
}
.supernatant-slider {
  position: absolute;
  left: 0;
  right: 0;
  //top: 0;
  //bottom: 0;
}
.screenModalBottomAndTop {
  top: 0;
  bottom: 0;
}
/*.stop-duration {*/
/*  animation-duration: 0s !important;*/
/*}*/
.slideIn {
  transition: top 1s;
}
.slideInDown{
  /*animation-name: slideInDown;*/
  /*-webkit-animation-duration: 1s;*/
  /*animation-duration: 1s;*/
  /*-webkit-animation-fill-mode: both;*/
  /*animation-fill-mode: both;*/
}
.slideInUp{
  /*animation-name: slideInUp;*/
  /*-webkit-animation-duration: 1s;*/
  /*animation-duration: 1s;*/
  /*-webkit-animation-fill-mode: both;*/
  /*animation-fill-mode: both;*/
}
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}
::-webkit-scrollbar-corner {
    background: #C8C7CA;
    border-radius: 3px;
}
::-webkit-scrollbar-thumb {
    background: #C8C7CA;
    border-radius: 3px;
}
.board-display-panel {
  position: relative;
  &.isDataReport {
    background-color: var(--sdp-ysbjc) !important;
  }
}
.sdp-support {
  height: 40px;
}
.sdpiconfont {
  font-size: 24px;
}
.placeholder {
  width: 100%;
  height: 40px;
}
.fullscreenModeBox {
  .icon-sdp-Nguanbi {
    font-size: 24px;
    z-index: 7;
    cursor: pointer;
  }
  .icon-sdp-a-Nquanping {
    z-index: 7;
    font-size: 24px;
    cursor: pointer;
  }
}
#data-screen-table {
  display: flex;
  flex-direction: column;
  border-top: #EAEAEA;
  // box-shadow: -2px -2px 6px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%;
  /deep/ .el-tooltip__popper[data-sdp-el-insert='el-tooltip'],  /deep/ .el-tooltip__popper[data-sdp-el-insert-firefox='el-tooltip']{
    position: absolute !important;
  }
  /deep/ .el-select-dropdown[data-sdp-el-insert-firefox='el-select'] {
    transition: opacity .3s;
  }
  .theme-dark-mode {
    @include operation_icon-0;
  }
  .borad-box {
    //min-width: 1280px;
    min-height: 100%;
    position: relative;
    flex: none;
    .border-box-fixed {
      position: sticky;
      z-index: 2001;
      top: 8px;
      height: 0;
      padding: 0;
      /*i {*/
      /*  line-height: 30px !important;*/
      /*}*/
    }
    .params-panel-fixed {
      width: 100%;
      z-index: 2000;
      position: sticky !important;
    }
    .icon-sdp-Nguanbi {
      font-size: 24px;
      color: var(--sdp-cszjq-is);
      z-index: 7;
      cursor: pointer;
      height: 24px;
      line-height: 24px;
      width: 30px;
      text-align: center;
    }
    .hidden {
      position: absolute;
      z-index: 6;
      top: 8px;
      right:0;
      width: 100px;
      height: 25px;
      cursor: pointer;
      float: right;
      text-align: right;
    }
    .icon-sdp-Ngudingshaixuanqu1, .icon-sdp-Ngudingshaixuanqu2  {
      font-size: 24px;
      width: 30px;
      height: 24px;
      line-height: 24px;
      color: var(--sdp-cszjq-is);
      z-index: 7;
      cursor: pointer;
      text-align: center;
      margin: 0 4px;
    }
  }
}
/deep/ .sdp-board-scrollbar {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .el-scrollbar__wrap {
    overflow-x: hidden;
    overflow-y: auto;
  }
  .el-scrollbar__bar {
    z-index: 30;
  }
}
.full-screen {
  position: fixed;
  height: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1002;
  background-color: var(--sdp-ysbjc);
  box-shadow: none !important;
}
.modal-screen {
  height: 100%;
  width: 100%;
  box-shadow: none !important;
}
.modal-screen-color {
  background-color: var(--sdp-ysbjc);
}
.index-screen {
  background-color: #f7f7f7;
}
.params-data-report {
  position: relative;
}
.part-screen {
  // 这里颜色需要和packages/base/gridPreview/render/index.vue class='cover'类下面的颜色一起修改 不然会产生容器表头吸顶样式问题
  background-color: var(--sdp-gjys-bjs);
}
.panel-active {
  background-color: var(--sdp-panel-active-bgc);
  // background-color: #f8f8f8;
  // @include sdp-background_0($key: 'panelActiveBgc');
}
.scroll-disable{
  overflow: hidden!important;
}
.export-box {
  display: inline-block;
    .container {
      display: inline-block;
      margin-right: 13px;
    }
    i {
      margin-right: 8px;
      font-size: 22px;
      color: #333;
      cursor: pointer;
    }
  }
  .export-box-right {
    right: 58px;
  }
  .freeze-panel {
    position: inherit;
    top: 0 !important;
  }

  // 平板缩放时 样式调整
  .sdpTabletScale {
    /deep/ {
      .el-input {
        .el-input__suffix {
          display: flex;
          align-items: center;
        }
      }
    }
  }
</style>
