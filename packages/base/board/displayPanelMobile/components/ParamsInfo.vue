<template>
  <transition name="slide-fade">
    <div class="paramsInfo" v-loading="!paramLoadStatus" element-loading-spinner="sdp-loading-gif" :class="{ 'board-params-info': isInBoard, 'params-info-first-width': showAppDingFlag }" :style="style" v-show="isInfoShow">
      <div class="params-info-ding" v-if="showAppDingFlag">
        <i class="sdpiconfont"
           :class="appDingFlag ? 'icon-sdp-gudingshaixuanqu1' : 'icon-sdp-gudingshaixuanqu'"
           @click="appDingClick"
        >
        </i>
      </div>
      <cube-scroll ref="scroll"
                   style="width:100%;height:100%;"
                   nest-mode="native"
                   :scrollEvents="['scroll', 'scroll-end']"
                   @scroll="scrollHandler"
                   @scroll-end="scrollEndHandler"
      >
        <div ref="paramsInfo">
          <!-- 数据更新时间 -->
          <div class="params-wrap" v-if="showBoardUpdateTime">
            <div class="params-item">
              <!--            <p class="params-item-title">{{currencyAlias}}:</p>-->
              <p class="params-item-content">
              <span class="params-item-title">
                {{boardUpdateTime.titleName}}:
              </span>
                {{boardUpdateTime.updateTime}}
              </p>
            </div>
          </div>
        <div class="params-wrap" v-for="(paramEl,index) in paramsPanelCont" :key="index">
          <div class="params-item" :class="!paramLoadStatus ? 'hide' : ''" v-if="isArray(paramEl) && showParamsValue(paramEl) && isHideElement(paramEl)">
<!--            <p class="params-item-title">{{ paramEl.elName }}:</p>-->
            <p class="params-item-content" @click.prevent.stop="showAllClick(paramEl)">
              <span class="params-item-title">
                {{ paramEl.elName }}:
              </span>
              <span v-html="languageChangeWithAll(showParamsValue(paramEl))"></span>
              <span
                :class="[isShowAll?'el-icon-arrow-down':'el-icon-arrow-right']"
                v-if="isAll(paramEl)"/>
            </p>
            <span
              v-if="isShowAll && isAll(paramEl)"
              class="params-item-content"
              :title="showParamsAllValue(paramEl)"
              v-text="languageChangeWithAll(showParamsAllValue(paramEl))"
            ></span>

            </div>
            <div v-else v-for="(el, names,  i) in isObject(getLabels(paramEl))">
              <div class="params-item" :class="!paramLoadStatus ? 'hide' : ''" v-if="!!decideFun(el) && isHideElement(paramEl)">
                <!--              <p class="params-item-title">{{nameFun(names,paramEl)}}:</p>-->
                <p class="params-item-content" @click="isAll2(paramEl,el)? checkAll(i): ''">
                <span class="params-item-title">
                  {{nameFun(names,paramEl)}}:
                </span>
                  {{languageChangeWithAll(decideFun(el, paramEl))}}
                  <span
                    :class="[isCheck(i)?'el-icon-arrow-down':'el-icon-arrow-right']"
                    v-if="isAll2(paramEl,el)">
                </span>
                </p>
                <span
                  v-if="isAll2(paramEl,el) && isCheck(i)"
                  class="params-item-content"
                  :title="showParamsAllValue(paramEl)"
                  v-text="languageChangeWithAll(showParamsAllValue(paramEl))"
                ></span>
              </div>
            </div>
          </div>
          <div class="params-wrap" v-if="showCurrency">
            <div class="params-item">
              <!--            <p class="params-item-title">{{currencyAlias}}:</p>-->
              <p class="params-item-content">
              <span class="params-item-title">
                {{currencyAlias}}:
              </span>
                {{languageChangeWithAll(languaeAndCurrency)}}
              </p>
            </div>
          </div>
        </div>
      </cube-scroll>
    </div>
  </transition>
</template>

<script>
import { getParamElementLabels } from 'packages/base/board/displayPanel/components/utils'
import { TYPE_PARAM_ELEMENT } from 'packages/base/board/displayPanel/params/utils/constants'
import { setTimeout } from 'timers'
import { sortLayout } from 'packages/base/common/sdpGrid/utils'
import { languageChangeWithAll } from '../../displayPanel/params/utils/utils'
import { changeAppDingFlag } from '../../displayPanel/components/api'
import { COMMON_VALUE } from '../../displayPanel/params/paramElement/locationNew/utils'
import { boardUpdateTime } from 'packages/base/board/displayPanel/utils/helpers/api'

export default {
  inject: ['sdpLangcode', 'tenantData', 'levelData', 'commonData', 'utils'],
  props: {
    isInfoShow: {
      type: Boolean,
      default: false
    },
    paramsPanelList: {
      type: Array,
      default: () => []
    },
    isEnterPriseCurrency: {
      type: Boolean
    },
    languageAndCurrency: {
      type: Object
    },
    show: {
      type: Boolean,
      default: true
    },
    isShowParameter: {
      type: Boolean,
      default: true
    },
    exporting: {
      type: Boolean,
      default: false
    },
    paramsType: {
      type: String
    },
    isHorizontal: {
      type: Boolean,
      default: false
    },
    isInBoard: {
      type: Boolean,
      default: false
    },
    paramLoadStatus: {
      type: Boolean,
      default: true
    },
    scrollFun: {
      type: Function,
    },
    scrollEndFun: {
      type: Function,
    },
    closePopup: {
      type: Function,
    },
    paramInfoHeight: {
      type: Number,
    },
    boardInfo: {
      type: Object,
      default: () => ({}),
    },
    boardStatus: {
      type: String,
      default: '',
    },
    storeSet: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    isInfoShow(val) {
      if (val) {
        this.setCurrency()
        // setTimeout(() => {
        //   console.log(this.$refs)
        //   console.log(this.$refs.paramsInfo.offsetHeight)
        //   this.getHeight()
        //   this.refreshScroll()
        // }, 100)
        this.$nextTick(() => {
          this.getHeight()
          this.refreshScroll()
        })
      }
    },
    languageAndCurrency: {
      handler(newval) {
        this.setCurrency()
      },
      deep: true
    },
    isEnterPriseCurrency(newval) {
      this.setCurrency()
    },
    activeParamsPanelCont: {
      handler(val) {
        this.style = {}
        this.updateActiveParamsPanelCont()
      },
      deep: true
    },
    'isShowAll'() {
      this.refreshUi()
    },
    'showAll0'() {
      this.refreshUi()
    },
    'showAll1'() {
     this.refreshUi()
    },
    showCurrency() {
      this.refreshUi()
    },
    isShowParameter() {
      this.refreshUi()
    },
    paramLoadStatus(v) {
      if (v) {
        this.refreshUi()
      }
    },
    boardStatus() {
      setTimeout(() => {
        this.refreshUi()
      }, 300)
      setTimeout(() => {
        this.refreshUi()
      }, 500)
    },
    'commonData.isHorizontal'() {
      setTimeout(() => {
        this.refreshUi()
      }, 300)
    },
    'storeSet.mobileSet.paramList': {
      handler(val) {
        this.updateActiveParamsPanelCont()
      },
      deep: true
    },
    'boardInfo.id': {
      handler(newVal, oldVal) {
        if(newVal && newVal !== '' && newVal !== oldVal) {
          this.getBoardUpdateTime()
        }
      },
      deep: true,
      immediate: true
    },
    boardSlectLang() {
      this.getBoardUpdateTime()
    }
  },
  computed: {
    activeParamsPanelCont() {
      let activePanel = this.paramsPanelList.find(panel => panel.active)
      if (activePanel) {
        return activePanel.content.filter(
          paramEl =>
            this.getLabels(paramEl) !== undefined &&
            this.getLabels(paramEl) !== null
        )
      } else {
        return []
      }
    },
    currencyAlias() {
      let locationItem = this.paramsPanelCont.find(k => k.type === TYPE_PARAM_ELEMENT.LOCATION_NEW)
      if (locationItem) {
        return locationItem.content.currency || 'Currency'
      }
      try {
        const activeTabIds = this.paramsPanelList.find(panel => panel.active)?.id || ''
        const { boardInfo, boardSlectLang } = this
        if (boardInfo && boardInfo.moreLanguageList) {
          const noLocationItem = boardInfo.moreLanguageList.find((item) => item.id.includes(activeTabIds))
          if (noLocationItem) {
            const currencyItem = noLocationItem.children.find((item) => item.value === 'Currency')
            if (currencyItem) {
              return currencyItem.res.find((item) => item.languageCode === boardSlectLang)?.value || 'Currency'
          }
        }
         }
      } catch (e) {
        console.log('%c [ e ]-244', 'font-size:13px; background:#4b5ddd; color:#8fa1ff;', e)
      }
      return 'Currency'
    },
    isLazyLoading() {
      return this.levelData.isLazyLoading
    },
    boardSlectLang() {
      return this.commonData.isPreview ? this.commonData.boardSlectLang() : ''
    },
    showAppDingFlag() {
      return this.tenantData.settingConfig.appDingFlag === '1'
    },
    appDingFlag() {
      return this.commonData.appDingFlag || false
    },
    showCurrency() {
      return this.languaeAndCurrency && this.boardInfo.currencyInfo
    },
    isHideElement(paramEl) {
      return function (paramEl) {
        return !this.commonData.isPreview || !paramEl.isHideElement
      }
    },
    showBoardUpdateTime() {
      return this.boardUpdateTime && this.boardUpdateTime.show
    }
  },
  data() {
    return {
      TYPE_PARAM_ELEMENT,
      languageChangeWithAll: languageChangeWithAll,
      paramsPanelCont: [],
      languaeAndCurrency: '',
      style: {},
      isShowAll: false,
      showAll0: false,
      showAll1: false,
      infoHeight: 0,
      hasInfoValue: true,
      boardUpdateTime: {
        show: false
      }
    }
  },
  created() {
    if (this.appDingFlag && this.isInBoard) {
      this.showAllClick = this.$__debounce(this.showAllClick, 100)
    }
    this.logg('created')
  },
  mounted() {
    if (this.isInfoShow && this.isInBoard) {
      this.setCurrency()
      this.updateActiveParamsPanelCont()
    }
    this.logg('mounted')
  },
  methods: {
    isObject(data) {
      return this.$_isObject(data) ? data : null
    },
    logg(type) {
      console.log('%cPARAM_INFO: ', 'background: pink;', type)
      const pan = this.paramsPanelList.find(panel => panel.active)
      if (!pan) return console.log('%cPARAM_INFO: NO PAN!', 'background: pink;')
      const content = pan.content
      console.log('%cPARAM_INFO: ', 'background: pink;', content)
    },
    async appDingClick() {
      try {
        const appDingFlag = await changeAppDingFlag(this.utils.api, { dingFlag: (!this.commonData.appDingFlag ? '1' : '0') })
        this.commonData.appDingFlag = appDingFlag
        if (appDingFlag) {
          this.closePopup && this.closePopup()
          // this.$emit('update:isInfoShow', false)
        }
      } catch (e) {
        console.log(e)
      }
    },
    showAllClick(paramEl) {
      if (this.isAll(paramEl)) {
        this.isShowAll = !this.isShowAll
      }
    },
    updateActiveParamsPanelCont() {
      const activePanel = this.paramsPanelList.find(panel => panel.active)
      if (activePanel) {
        let res = this.orderParamsList() || []
        this.paramsPanelCont = res
        this.hasInfoValue = this.getHasValue()
        this.$emit('update:hasInfoValue', this.hasInfoValue)
      }
      this.$nextTick(() => {
        this.isInfoShow && this.getHeight()
        this.refreshScroll()
      })
    },
    getHasValue() {
      return this.paramsPanelCont.some(paramEl => {
        if (this.isArray(paramEl)) {
          return this.showParamsValue(paramEl) !== ''
        } else {
          const labels = this.getLabels(paramEl)
          return Object.values(labels).some(label => {
            return this.decideFun(label) !== ''
          })
        }
      })
    },
    scrollHandler(e) {
      this.scrollFun && this.scrollFun(e)
    },
    scrollEndHandler(e) {
      this.scrollEndFun && this.scrollEndFun(e)
    },
    checkAll(i) {
      if (i === 0) {
        this.showAll0 = !this.showAll0
      } else {
        this.showAll1 = !this.showAll1
      }
    },
    isCheck(i) {
      if (i === 0) {
        return this.showAll0
      } else {
        return this.showAll1
      }
    },
    refreshUi() {
      this.style = {}
      // setTimeout(() => {
      //   if (this.isInfoShow) {
      //     this.getHeight()
      //   }
      //   this.refreshScroll()
      // })
      this.$nextTick(() => {
        this.isInfoShow && this.getHeight()
        this.refreshScroll()
      })
    },
    orderParamsList() {
      let res = []
      let activePanel = this.paramsPanelList.find(panel => panel.active)
      let newLayout = sortLayout(JSON.parse(JSON.stringify(activePanel.layout)))
      newLayout.forEach(v => {
        activePanel.content.forEach(k => {
          if (k.id === v.i) {
            res.push(k)
          }
        })
      })
      res = res.filter(
        paramEl =>
          this.getLabels(paramEl) !== undefined &&
        this.getLabels(paramEl) !== null
      )
      // 如果是在报告移动端，则需要判断storeSet是否加进去了
      if (this.utils.isPcMobile || this.commonData.isMobileDataReport()) {
        const paramList = this.storeSet?.mobileSet?.paramList || {}
        const setIds = paramList[activePanel.id] || []
        res = res.filter(item => setIds.includes(item.id))
      }
      return res
    },
    getSelectParamsValue(item) {
      let res = ''
      let value = this.getLabels(item)
      let valueMsg = value.join(', ')
      res = value && valueMsg
      if (item.type === TYPE_PARAM_ELEMENT.TAGNEW) {
        return valueMsg
      } else if ([TYPE_PARAM_ELEMENT.LOCATION, TYPE_PARAM_ELEMENT.LOCATION_NEW, TYPE_PARAM_ELEMENT.LOCATION_QUICK].includes(item.type)) {
        const { locationSelectedLabel, options = {}, selectType } = item?.content || {}
        if (this.languageChangeWithAll(valueMsg) === this.$t('sdp.select.allCapital') || locationSelectedLabel.includes(this.$t('sdp.views.All'))) {
          return locationSelectedLabel
        } else if (options?.shopIds.length > 1) {
          return `${locationSelectedLabel}</br>${valueMsg}`
        } else if (selectType === '1' && options?.shopIds.length === 1) {
          return `${locationSelectedLabel}</br>${valueMsg}`
        }
        // item.content?.selLocationData?.COMMON_VALUE.LOCATION_VALUE
        return valueMsg
      } else if ([TYPE_PARAM_ELEMENT.BUSSINESS_TRENDS, TYPE_PARAM_ELEMENT.FINANCIAL_TRENDS].includes(item.type)) {
        if (value.length && value.includes('ALL')) {
          const res = value.filter(e => e !== 'ALL').join(', ')
          return `${this.$t('sdp.select.allCapital')}: ${res}`
        } else {
          return res
        }
      } else {
        return res
      }
    },
    isArray(paramEl) {
      let activePanel = this.paramsPanelList.find(panel => panel.active)
      const data = getParamElementLabels.call(this, paramEl, activePanel.content, this.boardSlectLang)
      return Array.isArray(data) && (data.length > 0)
    },
    getHeight() {
      if (this.$refs.paramsInfo.clientHeight === 0) return
      let infoH = this.$refs.paramsInfo.clientHeight + 25
      let maxHeight = 567
      if (this.commonData.isPreview && this.commonData?.isHorizontal) {
        maxHeight = 220
      }
      if (this.isInBoard && this.showAppDingFlag && this.appDingFlag) {
        maxHeight = 172
      }
      this.$emit('update:paramInfoHeight', infoH > maxHeight ? maxHeight : infoH)
      console.log(infoH, 'infoH')
      this.style = {
        height: infoH + 'px',
        maxHeight: `${maxHeight}px`
      }
    },
    refreshScroll () {
      this.$nextTick(() => {
        const scroll = this.$refs['scroll']
        scroll && scroll.refresh()
      })
    },
    showParamsValue(paramEl) {
      // console.log(paramEl, 'showParamsValue')
      if (paramEl.type === TYPE_PARAM_ELEMENT.SEARCH) {
        let searchV = paramEl.content.options.dataSets[0]
        let res = ''
        let billText = ''
        let moneyTest = ''
        if (searchV) {
          if (searchV.chkField.values.length) {
            billText = `${paramEl.content.language.check.name} ${searchV.chkField.values[0]}`
          }
          if (searchV.amtField.values.length) {
            moneyTest = `${paramEl.content.language.amount.name} ${searchV.amtField.filterType} ${searchV.amtField.values[0]}`
          }
        }
        res = billText || moneyTest
        if (billText && moneyTest) {
          res = `${billText}，${moneyTest}`
        }
        return res
      } else {
        return this.getSelectParamsValue(paramEl)
      }
    },
    decideFun(data, paramEl) {
      if (Array.isArray(data)) {
        if (paramEl && [TYPE_PARAM_ELEMENT.BUSSINESS_CALENDAR, TYPE_PARAM_ELEMENT.FINANCIAL_CALENDAR].includes(paramEl.type) && data.includes('ALL')) {
          const arr = data.filter(e => e !== 'ALL')
          arr[0] = `${this.$t('sdp.select.allCapital')}: ${arr[0]}`
          return arr.join(', ')
        }
        return data.join(', ')
      } else if (typeof data === 'string') {
        return data
      }
    },
    nameFun(names, paramEl, el) {
      console.log(names, 'names')
      // 物业比较处理
      if (names === 'Compare' && paramEl.type === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
        if (paramEl.content.options.isCompare) {
          return paramEl.content.LocationCompareName
        }
      }
      if (names === 'Location') {
        return paramEl.elName
      }
      // 日历处理
      if (names === 'Prior Period') {
        return paramEl.content.campareAlise
      }
      return names
    },
    getLabels(el, title) {
      let activePanel = this.paramsPanelList.find(panel => panel.active)
      let data = getParamElementLabels.call(this, el, activePanel.content, this.boardSlectLang)
      let isDateObc = data && typeof data === 'object' && data['Prior Period']
      if (isDateObc) {
        let obj = {}
        obj[el.elName] = data[el.elName]
        obj['Prior Period'] = data['Prior Period']
        if (obj[el.elName] && obj[el.elName].length) {
          data = obj
        } else {
          data = null
        }
      }
      if (title) {
        if (data !== null && data !== undefined) {
          return data.join(';')
        }
      } else {
        if (data !== null && data !== undefined) {
          return data
        }
      }
    },
    setCurrency() {
      if (Object.keys(this.languageAndCurrency).length > 0) {
        if (this.tenantData.currencyType === '2') {
          this.languaeAndCurrency = this.tenantData.currency
        } else {
          if (this.isEnterPriseCurrency) {
            this.languaeAndCurrency = this.languageAndCurrency.shopCurrency || null
          } else {
            this.languaeAndCurrency = this.languageAndCurrency.tenantCurrency || null
          }
        }
      }
    },
    isAll(paramEl) {
      return paramEl.type === 'locationNew' && paramEl?.content?.saveActiveTabSelectType === COMMON_VALUE.LOCATION_VALUE && this.showParamsValue(paramEl).includes(this.$t('sdp.views.All')) && !this.isLazyLoading
    },
    isAll2(paramEl, el) {
      return paramEl.type === 'locationNew' && this.decideFun(el) === 'ALL' && !this.isLazyLoading
    },
    showParamsAllValue(paramEl) {
      let value = ''
      paramEl.content.pullDownData.forEach((item, i) => {
        if (i === 0) {
          value = item.name
        } else {
          value = value + ',  ' + item.name
        }
      })
      return value
    },
    //获取数据更新时间
    getBoardUpdateTime() {
      let param ={
        tenantId: this.utils.tenantId || '',
        boardId: this.boardInfo.id || '',
        languageCode:  this.boardSlectLang || this.sdpLangcode || 'en'
      }
      boardUpdateTime(this.utils.api, param).then(res => {
        if(res?.titleName ) {
          this.$set(this, 'boardUpdateTime', {...res, show: true})
          this.$nextTick(() => {
            this.getHeight()
            this.refreshScroll()
          })
        }
        if(res?.status === '1') {
          this.$set(this.boardUpdateTime, 'updateTime', this.$t('sdp.views.quotedRuleExpired'))
          this.$message({
            message: `${res?.titleName}: ${this.$t('sdp.views.quotedRuleExpired')}`,
            type: 'warning',
          })
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
@import './variable.scss';
@import 'packages/assets/theme/theme-style/variable';
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}
.hide {
  opacity: 0;
}
.paramsInfo {
  width: 100%;
  padding: 18px 16px 0;
  background: #ffffff;
  min-height: 49px;
  position: relative;
  background: $color-DBTCBJ;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  .params-wrap{
    &:last-child {
      .params-item:last-child{
        padding-bottom: 14px;
      }
    }
  }
  .params-item {
    padding-bottom: 6px;
    .params-item-title {
      font-family: PingFangSC-Semibold;
      font-size: 12px;
      //color: #383838;
      color: $color-XXBTWZ !important;
      margin-right: 16px;
      /*min-width: 85px;*/ // 固定宽度时开启
      /*display:inline-block;*/
      //@include sdp-mixin-style($type:(
      //  color:(titleFont:true),
      //), $self: $inform);
      letter-spacing: 0;
      font-weight: bold;
      line-height: 14px;
    }
    .params-item-content {
      /*margin-top: 6px;*/
      font-family: PingFang-SC-Regular;
      font-size: 12px;
      color: $color-XXNR;
      //@include sdp-mixin-style($type:(
      //  color:(contentFont:true),
      //), $self: $inform);
      letter-spacing: 0;
      line-height: 16px;
    }
  }
  .params-info-ding {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    /*background: rgb(14, 147, 57);*/
    position: absolute;
    right: 5px;
    top: 13px;
    cursor: pointer;
    z-index: 2;
    i {
      font-size: 18px;
      width: 25px;
      height: 24px;
      line-height: 24px;
      color: $color-DZTB;
      z-index: 7;
      cursor: pointer;
      text-align: center;
    }
  }
}
.board-params-info {
  /*box-shadow: 0px 1px 8px rgba(0, 0, 0, 0.2);*/
}
  .params-wrap-no-ding {
    .params-item-content {
      width: calc(100% - 30px)
    }
  }
</style>
