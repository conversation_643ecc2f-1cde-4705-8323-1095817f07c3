<template>
  <div v-if="isShowComponent">
    <div class="node-box" v-if="!isDataReport">
      <div class="node-box-title fontStyle title-color">
        {{$t('sdp.views.BigDataExport')}}
        <el-tooltip  effect="dark" :content="$t('sdp.views.BigDataExportTips')" placement="top" >
          <i class="el-tooltip icon-sdp-info" style="color: var(--sdp-zs);"></i>
        </el-tooltip>
      </div>
      <el-switch v-model="initData.isExportNode" :disabled="!canBigDataExport"/>
    </div>

    <div class="exportFormat-title fontStyle">{{$t('sdp.views.exportFormat')}}</div>

    <el-collapse class="sdp-word-export-collapse" v-if="isDataReport">
      <el-collapse-item>
        <template slot="title">
          <i class="el-icon-arrow-right"></i>
          <el-checkbox v-model="wordChecked" class="export-checkbox">{{TYPE.word}}</el-checkbox>
        </template>
        <div class="pdf-box fontStyle">
          <p class="m16 title-color">{{$t('sdp.views.ExportSettings')}}</p>
          <!-- <el-checkbox v-model="initData.wordExport.isParam" class="export-checkbox">{{$t('sdp.boardAttr.ParameterDetails')}}</el-checkbox> -->
          <el-checkbox v-model="initData.wordExport.isPage" class="export-checkbox m16">{{$t('sdp.boardAttr.page')}}</el-checkbox>
          <p style="color: var(--sdp-srk-bxwzs)">{{$t('sdp.boardAttr.wordNote')}}</p>
        </div>
      </el-collapse-item>
    </el-collapse>

    <el-collapse>
      <el-collapse-item>
        <template slot="title">
          <i class="el-icon-arrow-right"></i>
          <el-checkbox v-model="pdfChecked" class="export-checkbox">{{TYPE.pdf}}</el-checkbox>
        </template>
        <div class="pdf-box fontStyle">
          <div class="pdf-row m24">
            <div class="w236">
              <p class="m12 title-color">{{$t('sdp.views.exportType')}}</p>
              <div class="pdf-cell">
                <el-radio v-model="initData.pdfType" @input="pdfTypeChange" label="1">{{$t('sdp.views.screenshot')}}</el-radio>
                <el-radio v-model="initData.pdfType" @input="pdfTypeChange" label="2">{{$t('sdp.views.elementshot')}}</el-radio>
              </div>
            </div>
            <div>
              <p class="m12 title-color">{{$t('sdp.views.ExportSettings')}}</p>
              <div class="exportSettingsChecked">
                <el-checkbox class="exportSettingsCheckedChild" :disabled="pdfType" v-model="pdfPreferencesChecked">{{$t('sdp.boardAttr.ParameterDetails')}}</el-checkbox>
                <el-checkbox class="exportSettingsCheckedChild" :disabled="pdfType" v-model="pdfHeaderChecked">
                  {{$t('sdp.boardAttr.pageAttachHeader')}}
                  <el-tooltip  effect="dark" :content="$t('sdp.views.headExportHint')" placement="top" >
                    <i class="el-tooltip icon-sdp-info" style="color: var(--sdp-zs);"></i>
                  </el-tooltip>
                </el-checkbox>
              </div>
              <div class="exportSettingsChecked">
                <el-checkbox class="exportSettingsCheckedChild" :disabled="pdfType" v-model="pdfPageSizeChecked">{{$t('sdp.views.BoardPage')}}</el-checkbox>
                <el-checkbox class="exportSettingsCheckedChild" :disabled="pdfType && isDataReport" v-model="pdfBoardRemarkChecked">{{$t('sdp.views.boardDescription')}}</el-checkbox>
              </div>
            </div>
          </div>
          <div class="pdf-row">
            <div class="w236">
              <p class="m8 title-color">{{$t('sdp.views.layout')}}</p>
              <div class="pdf-cell">
                <el-radio :disabled="pdfType" v-model="initData.pdfLayout" label="1">{{$t('sdp.views.T_vertical')}}</el-radio>
                <el-radio :disabled="pdfType" v-model="initData.pdfLayout" label="2">{{$t('sdp.views.T_horizontal')}}</el-radio>
              </div>
            </div>
            <div>
              <p class="m8 title-color">{{$t('sdp.views.pageSize')}}</p>
              <el-select :popper-class="getCurrentThemeClass()" :disabled="pdfType" v-model='initData.pdfSize'>
                <el-option
                  v-for="item in TYPE_PAGE_SIZE"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div style="margin-left: 24px">
              <p class="m8 title-color">{{$t('sdp.views.scale')}}</p>
              <el-select :popper-class="getCurrentThemeClass()" :disabled="pdfType" v-model='initData.pdfRatio'>
                <el-option
                  v-for="item in TYPE_PAGE_RATIO"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>

    <el-collapse v-if="!isDataReport">
      <el-collapse-item>
        <template slot="title">

          <i class="el-icon-arrow-right"></i>
          <el-checkbox v-model="excelChecked" class="export-checkbox">{{TYPE.excel}}</el-checkbox>
        </template>
        <div class="pdf-box fontStyle">
          <p class="m16 title-color">{{$t('sdp.views.ExportSettings')}}</p>
          <el-checkbox v-model="excelPreferencesChecked" class="export-checkbox">{{$t('sdp.boardAttr.ParameterDetails')}}</el-checkbox>
          <el-checkbox v-model="excelBoardRemarkChecked" class="export-checkbox">{{$t('sdp.boardAttr.boardDescription')}}</el-checkbox>
        </div>
      </el-collapse-item>
    </el-collapse>

    <div style="height: 48px; display: flex; align-items: center" v-if="!isDataReport">
      <el-checkbox v-model="csvChecked" class="export-checkbox">{{TYPE.csv}}</el-checkbox>
    </div>
  </div>
</template>

<script>
import { TYPE_PAGE_RATIO, TYPE_PAGE_SIZE } from '../../constants'
const TYPE = {
  pdf: 'PDF',
  csv: 'CSV',
  excel: 'Excel',
  word: 'Word'
}
export default {
  name: 'boardExportType',
  inject: ['utils', 'getCurrentThemeClass'],
  props: {
    boardInfo: {
      type: Object,
      default: () => ({})
    },
    isOpen: {
      type: Boolean,
      default: false
    },
    canBigDataExport: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      TYPE_PAGE_SIZE,
      TYPE_PAGE_RATIO,
      TYPE,
      initData: null
    }
  },
  computed: {
    pdfType() {
      return this.initData.pdfType === '1'
    },
    pdfLayout () {
      return this.initData.pdfLayout === '1'
    },
    isShowComponent() {
      return !!this.initData
    },
    isDataReport() {
      return this.utils.isDataReport || false
    },
    pdfHeaderChecked: {
      get() {
        return this.initData.exportStyle.pdfHeader === '1'
      },
      set(val) {
        this.initData.exportStyle.pdfHeader = String(Number(val))
      }
    },
    pdfPreferencesChecked: {
      get() {
        return this.initData.exportStyle.pdfPreferences === '1'
      },
      set(val) {
        this.initData.exportStyle.pdfPreferences = String(Number(val))
      }
    },
    pdfPageSizeChecked: {
      get() {
        return this.initData.exportStyle.pdfPageSize === '1'
      },
      set(val) {
        this.initData.exportStyle.pdfPageSize = String(Number(val))
      }
    },
    pdfBoardRemarkChecked: {
      get() {
        return this.initData.exportStyle.pdfBoardRemark === '1'
      },
      set(val) {
        this.initData.exportStyle.pdfBoardRemark = String(Number(val))
      }
    },
    excelPreferencesChecked: {
      get() {
        return this.initData.exportStyle.excelPreferences === '1'
      },
      set(val) {
        this.initData.exportStyle.excelPreferences = String(Number(val))
      }
    },

    excelBoardRemarkChecked: {
      get() {
        return this.initData.exportStyle.excelBoardRemark === '1'
      },
      set(val) {
        this.initData.exportStyle.excelBoardRemark = String(Number(val))
      }
    },
    pdfChecked: {
      get() {
        return this.initData.exportType.includes(TYPE.pdf)
      },
      set(val) {
        if (val) {
          this.initData.exportType.push(TYPE.pdf)
        } else {
          const index = this.initData.exportType.findIndex(item => item === TYPE.pdf)
          this.initData.exportType.splice(index, 1)
        }
      }
    },
    excelChecked: {
      get() {
        return this.initData.exportType.includes(TYPE.excel)
      },
      set(val) {
        if (val) {
          this.initData.exportType.push(TYPE.excel)
        } else {
          const index = this.initData.exportType.findIndex(item => item === TYPE.excel)
          this.initData.exportType.splice(index, 1)
        }
      }
    },
    wordChecked: {
      get() {
        return this.initData.exportType.includes(TYPE.word)
      },
      set(val) {
        if (val) {
          this.initData.exportType.push(TYPE.word)
        } else {
          const index = this.initData.exportType.findIndex(item => item === TYPE.word)
          this.initData.exportType.splice(index, 1)
        }
      }
    },
    csvChecked: {
      get() {
        return this.initData.exportType.includes(TYPE.csv)
      },
      set(val) {
        if (val) {
          this.initData.exportType.push(TYPE.csv)
        } else {
          const index = this.initData.exportType.findIndex(item => item === TYPE.csv)
          this.initData.exportType.splice(index, 1)
        }
      }
    },
  },
  watch: {
    isOpen(val) {
      val && this.setInitData()
    }
  },
  created() {
    this.setInitData()
  },
  methods: {
    setInitData() {
      const {
        isExportNode,
        exportType,
        pdfSize,
        pdfRatio,
        pdfType,
        pdfLayout,
        exportStyle: {
          pdfPreferences,
          pdfPageSize,
          pdfBoardRemark = '0',
          excelPreferences = '0',
          excelBoardRemark = '0',
          pdfHeader = '0'
        },
        wordExport: {
          isParam,
          isPage,
        },
      } = this.boardInfo

      this.initData = {
        isExportNode,
        exportType: [...exportType],
        pdfSize,
        pdfRatio,
        pdfType,
        pdfLayout,
        exportStyle: {
          pdfPreferences,
          pdfPageSize,
          pdfBoardRemark,
          excelPreferences,
          excelBoardRemark,
          pdfHeader,
        },
        wordExport: {
          isParam,
          isPage,
        }
      }
    },
    save() {
      Object.entries(this.initData).forEach(([key, val]) => {
        this.$set(this.boardInfo, key, val)
      })
    },
    pdfTypeChange(v) {
      console.log('pdfTypeChange', v)
      if (this.isDataReport && v === '1') {
        this.pdfBoardRemarkChecked = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import 'packages/base/board/settingPanel/components/variable.scss';

.fontStyle {
  font-size: 12px;
  letter-spacing: 0;
  line-height: 20px;
}
.title-color {
  color: var(--sdp-xxbt2) !important;
}
.node-box{
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 57px;
  .node-box-title {
    margin-right: 42px;
    line-height: 17px;
    color: #333333;
    display: flex;
    align-items: center;
  }
}
.exportFormat-title {
  margin-top: 14px;
  color: var(--sdp-xxbt2);
}

.el-icon-arrow-right {
  margin-right: 10px;
  transform: rotate(0deg);
  transition: transform .3s;
  color: #979797;
}
$w: 180px;
/deep/ .el-collapse {
  border: none;
  [class~=is-active] .el-icon-arrow-right{
    transform: rotate(90deg);
    transition: transform .3s;
  }
  .el-collapse-item__arrow {
    display: none;
  }
  .el-collapse-item__header, .el-collapse-item__wrap {
    background-color: transparent;
    border: none;
  }
}
.pdf-box {
  padding-left: 47px;
  .pdf-row {
    display: flex;
    flex-direction: row;
    /deep/ .el-input {width: $w;}
    .exportSettingsChecked {
      display: flex;
      flex-direction: row;
      width: 410px;
      .exportSettingsCheckedChild{
        width: $w;
      }
      .exportSettingsCheckedChild:first-child {
        margin-right: 24px;
      }
    }
    .pdf-cell {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 32px;
      /deep/ .el-radio {
        flex:1;
        margin: 0;
      }
    }
    .w236 {
      width: 236px;
    }
  }
}
.m24 {
  margin-bottom: 24px;
}
.m16{
  margin-bottom: 16px;
}
.m12 {
  margin-bottom: 12px;
}
.m8 {
  margin-bottom: 8px;
}

.export-checkbox {
  line-height: 26px;
  height: 26px;
}
</style>
