<template>
  <div id="app" :class="{
    [themeClass]: true,
    'homePageFlag': isHomePageFlag
  }">
    <!-- <p>{{msg}}</p>
    <p>process: {{process}}</p>
    <p>{{messageData}}</p> -->
    <template v-if="isUpdateMixin">
      <update />
    </template>
    <template>
      <displayPanelMobile
        v-if="isShowLib"
        ref='displayPanelMobile'
        @eventBus="eventBus"
        v-bind="{ process, appData, toggle, paddingBottomMobileElementPanel, paddingLeftMobileElementPanel }"
      ></displayPanelMobile>
      <app-mobile
        v-if="isShow"
        ref='displayPanelMobile'
        @eventBus="eventBus"
        v-bind="{ process, appData, toggle, paddingBottomMobileElementPanel, paddingLeftMobileElementPanel }"
      ></app-mobile>
    </template>
  </div>
</template>

<script>
  // import { boardPreview } from '../../../packages/index'
  // import api from '../../utils/request'
  import mixins from '../../../packages/components/mixins/index'
  import { MESSAGE_TYPE, EVENT_TYPE, PARAMS_TYPE } from './constant'
  import { setToken } from '../../utils/auth'
  import appService from '@/utils/requestApp'
  import eventBus from 'packages/assets/eventBus'
  import EventData from 'packages/assets/EventData'
  import { PREVIEW_STATUS, TARO_ENV_TYPE, STATIC_BASE_PATH, LANGUAGE_LIST } from 'packages/assets/constant'
  import axios from 'axios'
  import AppMobile from '../../../packages/base/board/displayPanelMobile/index'
  import { THEME_TYPE, THEME_TYPE_KEY, VERSION as PUS_VERSION, PROJECT } from '../../../packages/assets/constant'
  import Vue from 'vue'
  import { isObject } from '../../../packages/assets/utils/globalTools'
  import { JinMaoKanBanIdSet } from 'packages/JinMaoKanBanData'

  const isProduction = process.env.NODE_ENV === 'production'
  const Sentry = process.env.NODE_ENV === 'production' ? require('@sentry/browser') : null
  // const VueIntegration = process.env.NODE_ENV === 'production' ? require('@sentry/integrations').Vue : null
  const Integrations = process.env.NODE_ENV === 'production' ? require('@sentry/tracing').Integrations : null

  const REQUEST_ENV = {
    'http://172.16.81.20:8080/mobile.html': 'https://sdptest.shijicloud.com/static/sdp_app/web/develop',
    'https://sdptest.shijicloud.com/eng/mobile.html': 'https://sdptest.shijicloud.com/static/sdp_app/web/release',
    'https://sdp-pre.shijicloud.com/eng/mobile.html': 'https://sdptest.shijicloud.com/static/sdp_app/web/pre_production',
    'https://sdp-pre.shijicloud.com/engzsc/mobile.html': 'https://sdptest.shijicloud.com/static/sdp_app/web/zsc_production',
    'https://sdpops.shijicloud.com/engzsc/mobile.html': 'https://sdpops.shijicloud.com/static/sdp_app/web/production',
    'https://sdp.shijicloud.com/eng/mobile.html': 'https://sdp.shijicloud.com/static/sdp_app/web/production',
    'http://39.98.182.210:82/eng/mobile.html': 'https://sdp.shijicloud.com/static/sdp_app/web/production',
  }
  let sdp = window.sdp
  let _window = window
  _window.SDP_APP_VERSION = PUS_VERSION

  export default {
    name: 'mobileApp',
    provide () {
      const param = this.$_getRequest()
      if (param?.appType === TARO_ENV_TYPE.WEAPP) {
        const { data: { languageCode } } = JSON.parse(param.data)
        this.provideParam.langCode = languageCode
        this.provideParam.sdpLangcode = LANGUAGE_LIST.indexOf(languageCode) === -1 ? 'en' : languageCode
      }
      return Object.defineProperty(this.provideParam, 'isEdit', {
        value: false,
        writable: false
      })
    },
    mixins: [...Object.values(mixins)],
    data () {
      const vm = this
      return {
        paddingBottomMobileElementPanel: '0',
        paddingLeftMobileElementPanel: '',
        awaitCallBack: null,
        isLoading: false,
        updateChecked: true, // 检测系统升级
        isShow: false,
        isShowLib: false,
        messageData: '',
        msg: 'this is mobile page',
        process: '',
        appData: {
          appPreviewParameter: false
        },
        toggle: 0,
        provideParam: {
          utils: {
            get isJinMaoApp() {
              return JinMaoKanBanIdSet.has(vm.appData.boardId)
            },
            api: appService,
            isMobile: true,
            tenantId: this.tenantId,
            themeParameters: {
              themeType: THEME_TYPE.default,
              isOpen: true,
            },
            env: this.env
          },
          langCode: 'en',
          sdpLangcode: LANGUAGE_LIST.indexOf(this.langCode) === -1 ? 'en' : this.langCode,
          // 子组件共享方法，此请求可以直接交互app，所需要参数为对象
          // 目前定义有四种交互形式，详情见./constant.js
          // {type: 'init, data: {}} || {type: 'data', data: {}} || {type: 'call', data: {}} || {type: 'message', data: {}}
          requestAppEvent: (data) => {
            if (this.appData.appType !== TARO_ENV_TYPE.WEAPP && this.appData.appType !== TARO_ENV_TYPE.WEB) {
              if (Object.prototype.toString.call(data) !== '[object Object]') {
                throw new Error('app参数需要对象类型')
              }
              // js交互ios不能通过window.postMessage交互
              // const _window = window.ReactNativeWebView || (this.isIOS ? window.webkit.messageHandlers.jsToIos : this.isAndroid ? window.sdp : window)
              // _window.postMessage(JSON.stringify(data))
              let _window = window.ReactNativeWebView || window
              let isParent = false
              if (this.appData.appType === TARO_ENV_TYPE.IOS) {
                if (window?.webkit?.messageHandlers?.jsToIos) {
                  isParent = false
                  _window = window.webkit.messageHandlers.jsToIos
                } else {
                  isParent = true
                  _window = window.parent || window
                }
              } else if (this.appData.appType === TARO_ENV_TYPE.ANDROID) {
                _window = sdp
              }
              if (data.type === 'back') {
                // 如果订阅页面是打开的状态，监听到系统左滑返回，不调用退出，只是关闭订阅弹窗
                const panel = this.$refs.displayPanelMobile
                if (panel?.$refs?.subscriptionPopup?.isShow) {
                  panel.$refs.subscriptionPopup.hide()
                  return
                } else {
                  this.callExit()
                  this.clearBoardData()
                }
              }
              if (isParent) {
                _window.postMessage && _window.postMessage(JSON.stringify(data), '*')
              } else {
                _window.postMessage && _window.postMessage(JSON.stringify(data))
              }
              // this.wx && this.wx.miniProgram.postMessage(data)
            }
          },
        },
        wx: null,
        setMessageDataCache: () => {},
        isCompleteStr: '',
      }
    },
    components: {
      AppMobile,
    },
    computed: {
      // 是否IOS原生
      isIOS() {
        return navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
      },
      isAndroid() {
        const u = navigator.userAgent
        return u.indexOf('Android') > -1 || u.indexOf('Adr') > -1
      },
      isHomePageFlag(){
        return this.provideParam.utils.env.isHomePageFlag || false
      }
    },
    watch: {
      isCompleteStr(val) {
        if (val) {
          this.setMessageDataCache()
          this.setMessageDataCache = () => {}
        }
      },
      wx(val) {
        if (val) {
          // document.title = '微信小程序'
          // 监听浏览器回退事件的方法
          window.addEventListener('popstate', (e) => {
            this.$nextTick(() => {
              if (this.$refs.displayPanelMobile.appPreviewParameter) {
                this.$refs.displayPanelMobile.appPreviewParameter = false
              } else {
                val.miniProgram.navigateBack({ delta: 1 })
              }
            })
          }, false)
        }
      }
    },
    created () {
      if (window.__POWERED_BY_QIANKUN__) {
        this.$bus.$on(EVENT_TYPE.onGlobalStateChange, this.globalStateChange)
      }

      // 因为android的postmessage只能触发绑定在document上面的message函数，ios只能触发绑定在window上面的,
      // 所以对处理逻辑进行防抖处理，同时都绑定message事件,并阻止冒泡
      // this.postMessageFn = this.$_debounce(this.postMessageFn)
      const param = this.$_getRequest()
      let isWx = false
      // 小程序传参
      if (Object.keys(param).length) {
        const { data = '' } = param
        if (param.appType === TARO_ENV_TYPE.WEAPP) {
          isWx = true
          this.asyncLoadFile('https://res.wx.qq.com/open/js/jweixin-1.3.2.js').then(() => {
            this.wx = window.wx
            this.postMessageFn(data)
          })
        } else if (param.appType === TARO_ENV_TYPE.ANDROID || param.appType === TARO_ENV_TYPE.IOS || param.appType === TARO_ENV_TYPE.WEB) {
          this.postMessageFn(data)
        }
      }
      // ios交互web需要调用挂载在window下面的方法
      _window.iosToJs = this.iosToJs
      document.addEventListener('message', (messageEvent) => {
        if (messageEvent.data.type === 'iosToJs') {
          this.iosToJs(messageEvent.data.data)
        }
      }, true)
      window.addEventListener('message', (messageEvent) => {
        if (messageEvent.data.type === 'iosToJs') {
          this.iosToJs(messageEvent.data.data)
        }
      }, true)
      this.initResource(isWx)
    },
    mounted () {
      _window.onbeforeunload = () => {
        if (this.appData.appType === TARO_ENV_TYPE.WEAPP) {
          this.callExit()
        }
      }
    },
    destroyed() {
      window.iosToJs = null
    },
    methods: {
      initResource(flag) {
        const { origin, pathname } = location
        const path = REQUEST_ENV[`${origin}${pathname}`]

        if (!path) {
          this.isCompleteStr = 'isShow'
          return
        }

        let sdpPath = location.host.slice(0, 9) === 'localhost' ? '' : origin

        if (pathname.includes('engzsc')) {
          sdpPath += '/zsc'
        }

        axios({
          method: 'get',
          url: `${sdpPath}/update.json?refrash_t=${Date.now()}`,
          dataType: 'json'
        }).then(({ data: { report_version: version } }) => {
          const cssPath = `${path}/${version}/sdp.css`
          const jsPath = `${path}/${version}/sdp.umd.min.js`
          // const fontPath = `${STATIC_BASE_PATH.css}font-family.css`
          const fontPath = 'https://sdp-cdn.shijicloud.com/static/sdp_app/web/assets/font-family.css'
          const arr = [
            this.asyncLoadFile(cssPath, 'link'),
            this.asyncLoadFile(jsPath)
          ]
          // 小程序不加载字体
          flag && arr.push(this.asyncLoadFile(fontPath, 'link'))
          Promise.all(arr).then(() => {
            this.isCompleteStr = 'isShowLib'
          }).catch((err) => {
            this.initSentry(err)
            this.isCompleteStr = 'isShow'
          })
        }).catch(err => {
          this.initSentry(err)
          this.isCompleteStr = 'isShow'
        })
      },
      callExit() {
        const callExit = this.$refs.displayPanelMobile.callExit
        callExit && callExit()
      },
      // 处理js上的eventBus不一致问题
      eventBus(eventData, afterFn, beforeFn) {
        const afterResult = afterFn ? afterFn() : true
        if (afterResult) {
          let { targetFn, target } = eventData
          target = Array.isArray(target) ? target : [target]
          target.forEach(comp => {
            let ref = this.$refs[comp]
            if (ref && ref[targetFn]) {
              ref[targetFn](eventData)
            }
          })
        }
        beforeFn && beforeFn()
      },
      clearBoardData() {
        this[this.isCompleteStr] = false
        // if (window.__POWERED_BY_QIANKUN__) return
        // window.location.reload()
      },
      initBoard(param) {
        const paramData = JSON.parse(param)
        const isInit = paramData.type === MESSAGE_TYPE.INIT
        if (isInit) {
          this.initData(paramData)
          // this.$nextTick(() => {
          //   this.$refs.displayPanelMobile[`_app${paramData.type}`](this.appData)
          // })
          setTimeout(() => {
            this.provideParam.requestAppEvent({ type: 'loadingFinish', data: {} })
          }, 700)
        }
        return isInit
      },
      iosToJs(param) {
        console.log('TX: iosToJs1', param, this.isLoading)
        if (this.isLoading && this.initBoard(param)) return 'success1'

        this.isLoading = true
        this.postMessageFn(param)
        return 'success2'
      },
      setMessageData({ type, data }) {
        if (data) {
            this[`_app${type}`](data)
            this[this.isCompleteStr] = true
        }
        this.$nextTick(() => {
           data && this.setDomBindData(data)
        })
      },
      setDomBindData(data) {
        if (!this.$refs.displayPanelMobile) return

        const { remindData, elementWarnSubscription } = data || {}
        if (remindData && isObject(remindData) && Object.keys(remindData).length && Object.keys(remindData).some(key => remindData[key])) {
          this.$refs.displayPanelMobile.previewType = PREVIEW_STATUS.REMIND
          this.$refs.displayPanelMobile.remindData = remindData
        }

        if (elementWarnSubscription && isObject(elementWarnSubscription) && Object.keys(elementWarnSubscription).length && Object.keys(elementWarnSubscription).some(key => elementWarnSubscription[key])) {
          this.$refs.displayPanelMobile.commonData.isElementSubscribe = elementWarnSubscription.isWarn
          this.$refs.displayPanelMobile.elementWarnSubscription = elementWarnSubscription
        }
      },
      initData(messageEvent) {
        if (this.isCompleteStr) {
          this.setMessageData(messageEvent)
        } else {
          this.setMessageDataCache = () => {
            this.setMessageData(messageEvent)
          }
        }
      },
      postMessageFn(messageEvent) {
        if (typeof messageEvent !== 'string') return
        try {
          const messageData = JSON.parse(messageEvent)
          const { type, data } = messageData
          
          // 处理 ping 消息
          if (type === 'ping') {
            this.provideParam.requestAppEvent({
              type: 'pong',
              data: { timestamp: Date.now() }
            })
            return
          }
          
          if (type === MESSAGE_TYPE.INIT) {
            this.initData(messageData)
          } else if (Object.values(MESSAGE_TYPE).includes(type)) {
            this[`_app${type}`](data)
          }
        } catch (err) {
          console.warn('JSON 解析失败')
        }
      },
      asyncLoadFile(url, type = 'script') {
        return new Promise((resolve, reject) => {
          let srcArr = document.getElementsByTagName(type)
          let hasLoaded = false
          for (let i = 0; i < srcArr.length; i++) {
            // 判断当前文件是否加载上
            hasLoaded = srcArr[i][type === 'script' ? 'src' : 'link'] === url
          }
          if (hasLoaded) {
            resolve()
            return
          }
          let loadFile = document.createElement(type)
          if (type === 'script') {
            loadFile.type = 'text/javascript'
          } else {
            loadFile.rel = 'stylesheet'
            loadFile.type = 'text/css'
          }
          loadFile[type === 'script' ? 'src' : 'href'] = url
          document[[type === 'script' ? 'body' : 'head']].appendChild(loadFile)
          loadFile.onload = data => {
            resolve(data)
          }
          loadFile.onerror = err => {
            reject(err)
          }
        })
      },
      initSentry(err) {
        if (window.$sentry) {
          return void ''
        }

        if (!isProduction || PROJECT !== 'SDP') return

        const SENTRYSDK = 'https://<EMAIL>//4'
        Sentry.init({
          Vue,
          dsn: SENTRYSDK,
          environment: process.env.VUE_APP_BUILD_ENV === 'build_prod' ? 'Production' : 'Release',
          integrations: [
            new Integrations.BrowserTracing(),
            // new VueIntegration({
            //   Vue,
            //   tracing: true,
            // }),
          ],
          release: PUS_VERSION,
          whitelistUrls: ['*'],
          tracesSampleRate: 1.0, // Be sure to lower this in production
        })
        window.$sentry = Vue.prototype.$sentry = Sentry
        Sentry.setTag('Eng-mobile', 'JS-Down-Error')
        Sentry.captureException(err)
      },
      verifyToken(token) {
        if (typeof token === 'string' && token !== '') return

          this.provideParam.requestAppEvent({
            type: 'back'
          })
      },
      globalStateChange({ type, data }) {
        console.log('globalStateChange', type, data);
        switch (type) {
          case PARAMS_TYPE.init:
            this.init(data)
            break
          case PARAMS_TYPE.updateToken:
            this.updateToken(data.token)
            break
          case PARAMS_TYPE.off:
            this.$bus.$off(EVENT_TYPE.onGlobalStateChange, this.globalStateChange)
            break
        }
      },
      init(data){
        this.isShow = false
        setTimeout(() => {
          this.isShow = true
        }, 300)
        this[`_appinit`](data)
      },
      updateToken(token) {
        setToken(token)
      },
      // 初始化看板
      [`_app${MESSAGE_TYPE.INIT}`] (data) {
        const { marginBottom, marginLeft, boardId, folderId, token, languageCode, themeType = '0', customColor = {}, tenantId, appPreviewParameter, ishome, isHideFavorites, IsVertical, IsVerticalId, paramsPanelList, appType, remindData, env, subscription, isSbiAppDefaultDashboard } = data

        // customColor.mainColor = '#FF0000'
        if (Object.keys(customColor).length) {
          this?.updateAppCustomColor?.(customColor)
        }

        this.provideParam.langCode = languageCode
        this.provideParam.sdpLangcode = LANGUAGE_LIST.indexOf(languageCode) === -1 ? 'en' : languageCode
        this.provideParam.utils.tenantId = tenantId
        this.provideParam.utils.env = env
        this.$set(this.provideParam.utils.themeParameters, 'themeType', THEME_TYPE[THEME_TYPE_KEY[themeType]])
        const subscribeObj = subscription?.subscribeObj || {}
        this.appData = { boardId, folderId, appPreviewParameter, ishome, isHideFavorites, languageCode, customColor, IsVertical, IsVerticalId, paramsPanelList, appType, remindData, subscribeObj, subscription, isSbiAppDefaultDashboard }

        this.paddingBottomMobileElementPanel = marginBottom
        this.paddingLeftMobileElementPanel = marginLeft
        this.process = MESSAGE_TYPE.INIT

        this.verifyToken(token)
        setToken(token)
      },
      // 获取post Message 数据\
      [`_app${MESSAGE_TYPE.DATA}`] (data) {
        this.process = MESSAGE_TYPE.DATA
        this.appData = data
      },
      // 方法调用
      [`_app${MESSAGE_TYPE.CALL}`] (data) {
        this.process = MESSAGE_TYPE.CALL
        this.appData = data
      },
      // 提示消息
      [`_app${MESSAGE_TYPE.MESSAGE}`] (data) {
        this.process = MESSAGE_TYPE.MESSAGE
        this.appData = data
      },
      // 提示消息
      [`_app${MESSAGE_TYPE.VERTICAL}`] (data) {
        // 兼容rn
        if ([TARO_ENV_TYPE.ANDROID, TARO_ENV_TYPE.IOS].includes(this.appData.appType)) {
          this.$nextTick(() => {
            this.$refs.displayPanelMobile[`_app${MESSAGE_TYPE.VERTICAL}`](data)
          })
        } else {
          let newdata = { type: 'vertical', IsVertical: true }
          this.appData = newdata
        }
      },
      // 返回
      [`_app${MESSAGE_TYPE.BACK}`] (data = {}) {
        // this.process = MESSAGE_TYPE.BACK
        // this.appData = data
        this.$nextTick(() => {
          this.$refs.displayPanelMobile[`_app${MESSAGE_TYPE.BACK}`](data)
        })
      },
      [`_app${MESSAGE_TYPE.GRAVITY}`] (data) {
        this.$nextTick(() => {
          this.$refs.displayPanelMobile[`_app${MESSAGE_TYPE.GRAVITY}`](data)
        })
      },
    }
  }
</script>

<style lang="scss" scoped>

#app {
  background: var(--sdp-color-KBBJS) !important;
}
#app.homePageFlag{
  background: transparent !important;
}
</style>
