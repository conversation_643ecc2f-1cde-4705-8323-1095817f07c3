<template>
  <div class="sdp-board" v-show="isShow">
    <sdp-board-preview
      ref="sdpboard"
      v-if="showBoard"
      @onFinish="onFinish"
      @onErr="onErr"
      @onExportSuccess="onExportSuccess"
      @onExportFail="onExportFail"
      @onGroupExportSuccess="onGroupExportSuccess"
      @close="close"
      v-bind="shareData"></sdp-board-preview>
    <!-- <board
      ref="sdpboard"
      :isSubscribe="true"
      :subscribeObj="boardData"
      :tenantId="boardData.tenantId"
      :id="boardData.boardId"
      :folderId="boardData.folderId"
      :language="language"
      v-if="showBoard"
      @onFinish="onFinish"
      @onExportSuccess="onExportSuccess"
      @onExportFail="onExportFail"
    ></board>-->
    <div v-if="!isLinkSubscriptionType" class="spd-mask">
      <div class="sdp-loding">
        <img class="logo" :src="logoSVG" alt />
        <div v-if="isFail">
          <p v-if="isInvalid">{{disableText}}</p>
          <p v-else-if="isLocationErr">{{errText}}</p>
          <p v-else-if="boardData && boardData.boardExport === '0'">
            {{failText + ',' +messageInfo}}
          </p>
          <p v-else>
            {{failText}}，
            <a @click="handleRetry">{{retryText}}</a>
          </p>
        </div>
        <div v-else>
          <p v-if="isDownload">{{successText}}</p>
          <p v-else>
            {{waitText}}
            <span class="dot-ani"></span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
import Cookies from 'js-cookie'
import { setToken } from '@/utils/auth'
import {
  subscribecheck,
  qywxSubscribecheck,
  getCompanyImg,
  getExportGroupBoards,
  startExport,
  updateExportDetail, finishExport, download
} from './api'
import { setTimeout } from 'timers'
import api from '@/utils/requestSubscribe'
import { INSET_OK_TYPE, insetExportEl } from '../utils/utils'
import { ALL_PROJECT_NAME } from '../../../packages/components/mixins/commonMixin'
import { TYPE_PARAM_ELEMENT } from '../../../packages/base/board/displayPanel/params/utils/constants'
import { KANBAN_CHANNELS } from 'packages/constants'
import { SUBSCRIPTION_TYPE } from '../../../packages/base/board/displayPanel/constants'
import common from '../mixins/common'
const isSubsystem = window.location.href.includes('env=1')
const isCodeSub = window.location.href.includes('code=')
// const logoSVG = 'https://sdptest.shijicloud.com/static/images/infrasys-svg.svg'
export default {
  name: 'sdpBoard',
  components: {},
  mixins: [common],
  data() {
    const getNowTime = () => {
      const date = new Date()
      function getZeroDate(date) {
        return date >= 10 ? date : `0${date}`
      }
      return `${date.getFullYear()}${getZeroDate(date.getMonth() + 1)}${getZeroDate(date.getDate())}`
    }
    return {
      nodeExportData: null,
      nowTime: getNowTime(),
      logoSVG: '',
      isLocationErr: false,
      isDownload: false,
      showBoard: false,
      isFail: false,
      isInvalid: false,
      // 是否已经导出
      isExportAlready: false,
      language: '',
      boardData: {},
      downTimer: null,
      subscriptionType: '',
      isShow: false,
      subscriptionElementId: '',
      shareData: {
        api,
        env: {
          // 应用项目的项目名称
          projectName: isSubsystem || isCodeSub ? ALL_PROJECT_NAME.SUBSYSTEM : '',
          // 模块 一级菜单国际码
          modelI18Key: '',
          // 页面 二级菜单国际码
          menuI18Key: '',
          // 渠道 PC APP SUB
          channel: '',
          // 是否为线上版本
          production: true,
          // 应用项目的项目模块
          projectModule: '',
          // 畅联传入的userId
          externalUserId: ''
        },
        boardInfo: {},
        isMobile: false,
        // isMobile:
        //   this.$route.query.isMobile === 'true' || this.$route.query.isMobile,
        langCode: this.$i18n.locale,
        subscription: {
          isSubscribe: true,
          subscribeObj: {},
        },
        options: {
          tenantId: localStorage.getItem('am')
        }
      },
      groupBoardList: [],
      groupBoardStatus: {
        // '86127220701315072071': true,
        // '87359580756926464071': true,
        // '87359592121393152071': true,
      },
      groupBoardFinishStatus: {},
      groupId: '',
      recordId: '',
      planExecSubId: '',
      // recordId: '94749790192163635271',
      isGroup: false,
      currentBoardId: '',
      // groupId: "94633823569913856071",
      // recordId: "94749641742323302471",
    }
  },
  created() {
    this.getBoardParams()
  },
  computed: {
    waitText() {
      return this.language === 'zh'
        ? '浏览器正在为您下载数据看板，请等待'
        : 'Browser is downloading data board for you, please wait'
    },
    successText() {
      return this.language === 'zh'
        ? '恭喜，数据看板已成功下载！'
        : 'Congratulations, data board has been successfully downloaded!'
    },
    failText() {
      return this.language === 'zh'
        ? '数据看板下载失败'
        : 'Data board download failed'
    },
    errText() {
      return this.language === 'zh'
        ? '订阅的物业被禁用，停用或解绑！'
        : 'Subscribed properties are disabled, deactivated or unbound！'
    },
    retryText() {
      return this.language === 'zh'
        ? '请点击这里重新下载'
        : 'please click here to download again!'
    },
    disableText() {
      return this.language === 'zh'
        ? '此链接已失效...'
        : 'The link is invalid!'
    },
    messageInfo() {
      return this.language === 'zh'
        ? '用户权限不足!'
        : 'Insufficient user rights!'
    },
    // 是不是超链接查看类型
    isLinkSubscriptionType() {
      return this.subscriptionType === SUBSCRIPTION_TYPE.link
    }
  },
  watch: {
  },
  beforeRouteEnter(to, from, next) {
    // 在渲染该组件的对应路由被 confirm 前调用
    // 不！能！获取组件实例 `this`
    // 因为当守卫执行前，组件实例还没被创建
    next()
  },
  beforeRouteLeave(to, from, next) {
    // 导航离开该组件的对应路由时调用
    // 可以访问组件实例 `this`
    console.log(to.path)
    next('/subscribe')
  },
  methods: {
    getBoardParams() {
      this.subscriptionElementId = ''
      this.isGroup = false

      // const queryRecordId = this.$route?.query?.recordId || ''
      // if (queryRecordId) {
      //   this.recordId = queryRecordId
      //   this.setRecordId(queryRecordId)
      // }

      const isParams = !!this.$route?.query?.params
      const isCode = !!this.$route?.query?.code
      const isGroupId = !!this.$route?.query?.groupId
      if (isGroupId) {
        const token = this.$route?.query?.token
        if (!token) return
        setToken(token)

        this.shareData.options.tenantId = this.$route?.query?.tenantId || ''
        this.shareData.langCode = this.$route?.query?.language || 'zh'
        this.initGroup()
        // this.finishGroupExport()
        return
      }
      let postData = isCode ? {
        code: this.$route && this.$route.query && this.$route.query.code,
        state: this.$route && this.$route.query && this.$route.query.state,
      } : {
        // urlParams: 'ztH6Iup5EAJfllwHDSHjNlH4GxSA3KrulX81grryTYuAX5IN2BTz3GPHm1aHk2i4mCwoHf70bUPf8KcPfAk1LgFfjwCpkSWug73FBZqosvY43NGLD+vcYznTClB0YTNAcGi5N3dD1hQ+Ndnmg4vKC323aO/cc83HyNfukkKYSNVJn/4/g/DcPvr8WAQWS0wl0pvWtRmhibi+kT/mFDoW2xs95UjmozfvvxvRGR7GZmLtou6wQZeZ+jOCO3agJPV5zl4BxBgNuEMejzOYqll5uRW47nkTZG46k6aNaXJEr+7vwpNHe/vrOCEHz/WhuIwfCm6fYO+gQFCVzdJgiGr1LdAFErbfnhxOdulyr4jJ6aBFSphJ6mE8uvBWg/pJbGFxaDVRP9atO5h/J8N3e67Ri6gF0onnA55Kkai6U/6dE56TrshYnsB7kpmtgSXeQXzVz9A6cjoQm0tA2/4arD/f3/Ch/K22diKIBJzWrLSbh6f5OGKOmJScBgcLMeMGeJNmPhObRsAHSE8YfQlYx0KKbstB1dIA7bPwJHMUVGzya+Vsp3fXr8OrtKAiGT6MxXXIbYMwcbLUh/Q2pbcuQVG2Pbid5kdGr0xzXa5OOJS2gXvE84nHJJmXRPkiKtlrT88wUcbxL8BKyt91JYW5P1+F0aJOMGyUtUGTxEdKCtxpxDW1uvr9gjTVaTODId1RgXEdPkGiBWeaPzo1+VOr+yQ2',
        urlParams: this.$route && this.$route.query && this.$route.query.params,
        // urlParams: 'ztH6Iup5EAJfllwHDSHjNlH4GxS08LnBrhYSuo3WWpuLWIg+4U+A5VrU6UbmpUvhkAgMGMibdX7pi4AUeAxuLgFY9QqNjgO5/Za4BZqs9fhk+qqHfsTDTyLXFWU9PFVKFBLRPXYb+xMTN/TouIrQTh3XZ/zxdNPNnLPP/A2Dc61IocAuyPHWD9PrcSwjRGAu+sL8hy+Vmbi+kT/mFDoW2xs95UjmozfvvxzSHxLKYGPqpuWwSpad+jeKO3agJPV5zl4BxBgNuEMejzKSqFl8vxW/7HsRamk6mqyFaXJEou7vwpNHe/vrOCEHz/WhuIwfCnOGbO+XTkmleoJ6CxRCfcrEF6TElBFAZf9Xgo3B++6cGrYPu1NcyMzVEzaSGWtBXPqJUY+f/BGCykOGlHtaDn6oYVxJhUzpFn4Pz5NGmwmDxxa/1B2x1bMmd+SFGSKHmpFHLz8QnUpC3PYcrj/d2POonHM/5TDm2hMrJWRSxZpkzqPW1dPGR0JqfaVUdJEH5p0KVa7YwdtERIiAXx662ZtbFYwobX4hFGsQVB+sQWeybmy9cE499fF/UzPFjRwU6BMmE21aen/odPQ4wZx+KLHOtRkA1QV+CrNOOuxoDeTWmlxKtLExn2F0EQbk2otmZ0gg/q92GAHgdd+AooRlmpvI45qBkkHn0BlMAthpxFltPWro6+pe+HTPepwEwiwns06u2sCcmS8XZdyu08XQjFJlF9XKHbr3v7g3MrVC/t7FYJVqOo6Y2FefC7n+56+MXwLONMftDIyIivTSPIz7Htb9ypv75Q=='
        // urlParams: 'ztH6Iup5EAJfllwHDSHjNlH4GxSg9KK13Awch5D2GovzLfYlkQj92ECvnEe2kGHuiVI4OfTqVm35+JkUFwALLgF4sC2bijjR9JOlEruCtdc13IGCcrKcTw3pCShZaE5OCxjACFY2zEoFUv3GxvjTXk2hPJmFQ+iVm5D63DWUVuhLkN1Q/MvDCtznYVgEXg540evt2Aqombi+kT/mFDoW2xs95UjmozfvvxzXHBDAb2Lvoee/RpGf/TOKM3agJPV5zl4BxBgNuEMejzKSrVh5vhm97HoTamg/m62DYXJErO7vwpNHe/vrOCEHz/WhuIwfCv1Vmu8yr66VzdJgiGr1LdAFErbfnhxOdulyr4jJ6aBJYr1F7Vs/h8yecTgJ0gJHKqi9NdOuvEacsh2Z9hNoWz6yslVrn4wkSScnA6TIVsjs+8QzQUzqgvN6D7aJFiWBnpZKLzUQnEwsB3COvVQEUWf3q/DkJWT3UZG/czkGlc0gt/TY0dPBRUpseqtTdZYMiUOBxb6wGFXReR9PlUqWYl2OFclpWuPq5TQ43p04deljrHef96l3JjU3bOEAQCCaI9NOJPCIvne0MqFEncMjf/DCvgkD1xJ7DKRMPZS2gXvE84nHJJ27TqU8PaA/Qp4zOxwWZVm3Azg/+E+oBF/9zKAXbA/B6H01H99aagfmUnL34LytwWyDOFPDaIpa3jApukI4FQCH85hHfxvn6lgLbF1rH8v9Pbnyn7BZYOkVho7KY5BsPouV21KaCrj34a6DbegYURTZhFhtpZrQ77d9ag=='
      }

      console.log('subapp>', isCode, this.$route.query)
      const xhrApi = isCode ? qywxSubscribecheck : subscribecheck
      xhrApi(postData).then(res => {
        if (res.groupId) {
          // 校验是否在加载过程中
          const cookiesGroupId = this.getGroupId()
          const cookiesRecordId = this.getRecordId()
          const cookiesGroupBoardStatus = this.getGroupBoardStatus()
          const queryRecordId = res.recordId || ''
          if (queryRecordId) {
            this.recordId = queryRecordId
            this.setRecordId(queryRecordId)
          }
          if (cookiesGroupId === res.groupId) {
            this.recordId = queryRecordId || cookiesRecordId
            this.$set(this, 'groupBoardStatus', cookiesGroupBoardStatus)
          } else if (!queryRecordId) {
            this.setRecordId('')
            this.setGroupId('')
            this.setGroupBoardStatus({})
          }

          setToken(res.token)
          this.planExecSubId = res.boardEmailSendId || ''
          this.shareData.options.tenantId = res.tenantId
          this.shareData.langCode = res.language
          this.language = res.language
          this.subscriptionType = res.subscriptionType
          this.setLanguageCode(this.language)

          res.tenantId && getCompanyImg(res.tenantId).then(data => {
            this.logoSVG = data
          }).catch(e => {
            console.log(e)
          })

          this.initGroup(res.groupId)
          return
        }

        this.subscriptionType = res.subscriptionType
        this.isShow = true

        res.tenantId && getCompanyImg(res.tenantId).then(data => {
          this.logoSVG = data
        }).catch(e => {
          console.log(e)
        })
        if (res.warn) {
          this.logRecord()
          this.isFail = true
          this.language = res.language
          if (res.warnCode === '1') this.isInvalid = true
          return
        }

        if (isCode && res.authFlag === '0') {
          this.$message.error(this.messageInfo)
          this.isFail = true
          return
        }
        if (isCode && res.codeValid === '0') {
          const url = res.loginUrl
          window.location.href = url
          return
        }

        this.boardData = res
        document.title = res.boardName
        window.parent.postMessage(res.boardName, '*')
        this.language = res.language
        this.shareData.subscription.subscribeObj = res
        this.shareData.boardInfo.id = res.boardId
        this.shareData.boardInfo.folderId = res.folderId
        this.shareData.boardInfo.boardName = res.boardName
        this.shareData.options.tenantId = res.tenantId
        this.shareData.isMobile = res.app || res.channel === '2' || false
        if (res.exportType === '4' || res.exportType === '5') {
          this.shareData.env.channel = KANBAN_CHANNELS.DATA_REPORT
          this.shareData.channel = KANBAN_CHANNELS.DATA_REPORT
        }
        this.shareData.langCode = res.language
        if (res.planExecSubId && res.resourceType === '1') {
          this.nodeExportData = {
            planExecSubId: res.planExecSubId,
            resourceType: res.resourceType
          }
        }
        if (res.elementId) {
          this.subscriptionElementId = res.elementId
        }
        setToken(res.token)
        localStorage.setItem('am', res.tenantId)
        this.setLanguageCode(this.language)
        const subscriptionType = res.subscriptionType
        if (this.boardData.boardExport && this.boardData.boardExport === '0' && ['0', '1'].includes(subscriptionType) && !this.shareData.isMobile) {
          this.$message.error(this.messageInfo)
          this.isFail = true
          return
        }
        setTimeout(() => {
          this.showBoard = true
        }, 300)
      }).catch(() => {
        this.isShow = true
      })
    },
    initGroup(id = '') {
      const groupId = id || this.$route.query.groupId
      if (!groupId) {
        insetExportEl({ text: INSET_OK_TYPE, attr: { type: 'err', value: this.failText } })
        return
      }
      this.groupId = groupId
      this.setGroupId(groupId)

      // !id && setToken('bearer ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')
      getExportGroupBoards(groupId).then(res => {
        console.log('getExportGroupBoards', res)
        this.$set(this, 'groupBoardList', res.filter(e => e.boardStatus === '1'))
        // 开始进入看板函数 todo
        // 功能要求：根据当前看板列表 查找到未加载的看板，修改ShareData信息，进入看板，等待加载完成并导出
        !this.recordId && this.startGroupExport(this.groupId)
        this.initGroupBoard()
      }).catch(err => {
        insetExportEl({ text: INSET_OK_TYPE, attr: { type: 'err', value: this.failText } })
      })
    },
    checkGroupFinish() {
      const boardIdList = this.groupBoardList.map(e => e.id)
      const firstBoardId = boardIdList.find(id => !this.groupBoardStatus[id])
      // 全部完成
      if (!firstBoardId) {
        // 调用导出完成接口
        setTimeout(() => {
          this.finishGroupExport()

          this.setRecordId('')
          this.setGroupId('')
          this.setGroupBoardStatus({})
        }, 3000)
        return true
      }
      return false
    },
    initGroupBoard() {
      const boardIdList = this.groupBoardList.map(e => e.id)
      const firstBoardId = boardIdList.find(id => !this.groupBoardStatus[id])
      // const firstBoardId = boardIdList[0]
      // 全部完成
      if (this.checkGroupFinish()) {
        return
      }

      this.isShow = false
      this.showBoard = false
      this.isFail = false
      this.isInvalid = false
      this.isGroup = true

      const targetBoard = this.groupBoardList.find(e => e.id === firstBoardId)

      this.currentBoardId = targetBoard.id

      this.shareData.boardInfo.id = targetBoard.id
      // this.shareData.boardInfo.folderId = res.folderId
      this.shareData.boardInfo.boardName = targetBoard.name
      this.shareData.options.groupId = this.groupId
      this.shareData.isMobile = false

      document.title = targetBoard.name
      window.parent.postMessage(targetBoard.name, '*')

      setTimeout(() => {
        this.showBoard = true
        this.isShow = true
      }, 300)
    },
    async onGroupExportSuccess(res, errMsg = '') {
      const boardIdList = this.groupBoardList.map(e => e.id)
      const firstBoardId = boardIdList.find(id => !this.groupBoardStatus[id])
      // const firstBoardId = boardIdList[0]
      if (!firstBoardId) return

      console.log('10532 onGroupExportSuccess', this.currentBoardId)

      this.groupBoardStatus[this.currentBoardId] = true
      this.setGroupBoardStatus()
      try {
        await this.updateGroupExportDetail(res, errMsg)
      } catch (e) {
        console.log('error', e)
        insetExportEl({ text: INSET_OK_TYPE, attr: { type: 'err', value: e.message || this.failText } })
      }
      // todo reload
      // this.initGroupBoard()
      if (this.checkGroupFinish()) return
      this.handleRetry()
    },
    startGroupExport(id, planId = this.planExecSubId) {
      if (!id) {
        this.isFail = true
        this.isGroup = false
        console.log('10532 开始导出 失败')
        return
      }

      const params = {
        exportGroupId: id,
        planExecSubId: planId
      }

      if (planId) {
        params.planExecSubId = planId
      }

      startExport(params).then(res => {
        // todo 获取recordId
        this.recordId = res.id
        this.setRecordId(res.id)
        console.log('10532 开始导出', res)
      }).catch(err => {
        this.isFail = true
        this.isGroup = false
        console.log('10532 开始导出 失败', err)
        insetExportEl({ text: INSET_OK_TYPE, attr: { type: 'err', value: this.failText } })
      })
    },
    finishGroupExport() {
      if (!this.groupId || !this.recordId) {
        this.isFail = true
        this.isGroup = false
        console.log('10532 结束导出 失败')
        insetExportEl({ text: INSET_OK_TYPE, attr: { type: 'err', value: this.failText } })
        return
      }
      const params = {
        groupId: this.groupId, // 组合ID
        recordId: this.recordId, // 记录ID
        // planExecSubId: '' // 计划任务执行记录ID
      }
      if (this.planExecSubId) {
        params.planExecSubId = this.planExecSubId
      }

      finishExport(params).then(res => {
        console.log('10532 导出完成', res)
        const requestData = res || {}
        if (this.planExecSubId && this.subscriptionType === '0' && requestData?.fileUrl) {
          download(requestData.fileUrl).then(res => {
            this.downloadFile(res, 'xlsx', requestData.groupName)
          })
          this.isDownload = true
        } else {
          insetExportEl({ text: INSET_OK_TYPE, attr: null })
          // const isDev = process.env.NODE_ENV === 'development'
          // if (!isDev) {
          //   setTimeout(() => {
          //     window.close()
          //   }, 5000)
          // }
        }
      }).catch(err => {
         insetExportEl({ text: INSET_OK_TYPE, attr: { type: 'err', value: this.failText } })
      })
    },
    async updateGroupExportDetail(res = '', errMsg = '') {
      if (!this.groupId || !this.recordId || !this.currentBoardId) {
        this.isFail = true
        this.isGroup = false
        console.log('10532 上传失败')
        return
      }

      const params = {
        groupId: this.groupId, // 组合ID
        recordId: this.recordId, // 记录ID
        boardId: this.currentBoardId, // 看板ID
        result: '1', // 1、失败  2、成功
        content: null // 生成的文档元素内容
      }

      if (res) {
        params.content = res
      }
      if (errMsg) {
        params.failMsg = errMsg
        params.result = '0'
      }

      await updateExportDetail(params)
      console.log('10532 上传成功', res, this.currentBoardId)
    },
    downloadFile(res, suffix, fileName = '') {
      const blob = new Blob([res], {
      })
      const _URL = window.URL || window.webkitURL || window || {}

      this.clickDownload(_URL.createObjectURL(blob), suffix, fileName)
    },
    clickDownload(href, suffix, fileName = '') {
      const link = document.createElement('a')
      link.download = `${fileName}_${this.nowTime}.${suffix}`
      link.style.display = 'none'
      link.href = href
      document.body.appendChild(link)
      link.click()
      link.remove()
    },
    close() {

    },
    onFinish() {
      if (this.isLinkSubscriptionType) return
      if (this.isGroup) {
        if (this.groupBoardFinishStatus[this.currentBoardId]) {
          return
        }
        this.groupBoardFinishStatus[this.currentBoardId] = true
      }

      console.log('finish===================>')
      setTimeout(() => {
        // this.setDownloadTimer()
        this.doDownload()
      }, 3000)
    },
    onErr(res = '') {
      if (!this.isFail) {
        this.onGroupExportSuccess(undefined, res === TYPE_PARAM_ELEMENT.LOCATION_NEW ? this.errText : this.failText)
      }
      this.isFail = true
      if (res === TYPE_PARAM_ELEMENT.LOCATION_NEW) {
        this.isLocationErr = true
      }
      if (this.groupId) return
      insetExportEl({ text: INSET_OK_TYPE, attr: { type: 'err', value: res } })
    },
    onExportSuccess() {
      this.isDownload = true
      if (this.groupId) return
      insetExportEl({ text: INSET_OK_TYPE, attr: null })
    },
    onExportFail() {
      if (!this.isFail && this.groupId) {
        this.onGroupExportSuccess(undefined, this.failText)
      }
      this.isFail = true
      this.onErr('api')
    },
    handleRetry() {
      window.location.reload()
    },
    setDownloadTimer() {
      this.downTimer && clearTimeout(this.downTimer)
      this.downTimer = setTimeout(() => {
        let opacity = document.getElementById('supernatant').style.opacity
        if (opacity === '1') {
          this.doDownload()
        } else {
          this.setDownloadTimer()
        }
      }, 3000)
    },
    doDownload() {
      this.setExportType()
      let exportType = this.boardData.exportType
      // 1:pdf,2:excel,3:csv
      if (exportType === '1' || exportType === '5') {
        if (!this.isExportAlready) {
          this.isExportAlready = true
          this.exportPdf()
        }
      } else if (exportType === '6') {
        if (!this.isExportAlready) {
          this.isExportAlready = true
          this.exportOfd()
        }
      } else if (exportType === '2') {
        if (!this.isExportAlready) {
          this.isExportAlready = true
          this.exportExcel()
        }
      } else if (exportType === '3') {
        if (!this.isExportAlready) {
          this.isExportAlready = true
          this.exportCSV()
        }
      } else if (exportType === '4') {
        if (!this.isExportAlready) {
          this.isExportAlready = true
          this.exportWord()
        }
      } else if (this.isGroup) {
        this.exportExcel()
      }
      this.logRecord()
    },
    logRecord() {
      // let OPERATION_LOG = {
      //   operateType: 27,
      //   logType: 2,
      //   modelI18Key: 'T_02',
      //   menuI18Key: 'T_0202',
      //   objectId: this.boardData.boardId,
      //   objectName: '',
      //   operateContent: this.boardData.warn || '下载成功'
      // }
      // this.$store.dispatch('operationalLog', OPERATION_LOG)
    },
    setExportType() {
      this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].setExportType({
        data: {
          nodeExportData: this.nodeExportData,
          isOpenChioceTab: true
        }
      })
    },
    exportPdf() {
      this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].exportSinglePdf()
    },
    exportOfd() {
      this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].exportSingleOfd()
    },
    exportExcel() {
      // todo singleElementExportExcel 无元素异常情况
      if (this.subscriptionElementId) {
        this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].singleElementExportExcel(this.subscriptionElementId)
        return
      }
      this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].doExportExcel('Excel(.xlsx)')
    },
    exportCSV() {
      if (this.subscriptionElementId) {
        this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].doExcelCsvPdf({ subscriptionElementId: this.subscriptionElementId })
        return
      }
      this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].doExcelCsvPdf('csv')
    },
    exportWord() {
      this.$refs['sdpboard'].$refs[this.shareData.isMobile ? 'displayPanelMobile' : 'displayPanel'].doExportWord('docx')
    },
    getLanguageCode() {
      this.language = Cookies.get('language')
    },
    setLanguageCode() {
      Cookies.set('language', this.language)
      this.$i18n.locale = this.language
    },
    getRecordId() {
      return sessionStorage.getItem('recordId') || ''
    },
    setRecordId(id = this.recordId) {
      sessionStorage.setItem('recordId', id)
    },
    getGroupId() {
      return sessionStorage.getItem('groupId') || ''
    },
    setGroupId(id = this.groupId) {
      sessionStorage.setItem('groupId', id)
    },
    getGroupBoardStatus() {
      const str = sessionStorage.getItem('groupBoardStatus')
      return str ? JSON.parse(str) || {} : {}
    },
    setGroupBoardStatus(data = this.groupBoardStatus) {
      sessionStorage.setItem('groupBoardStatus', JSON.stringify(data))
    },
  }
}
</script>

<style lang="scss" scoped>
a {
  text-decoration: underline;
  color: blue;
}
.sdp-board {
  height: 100%;
}
.spd-mask {
  background-color: #ffffff;
  height: 100%;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
}
.sdp-loding {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  font-family: PingFang-SC-Regular;
  font-size: 16px;
  color: #818181;
  letter-spacing: 0.25px;
  .logo {
    width: 180px;
    height: 72px;
    margin-top: 180px;
    margin-bottom: 100px;
  }
}
.dot-ani {
  display: inline-block;
  height: 12px;
  line-height: 12px;
  width: 15px;
  overflow: hidden;
}
.dot-ani::after {
  overflow: hidden;
  display: inline-block;
  vertical-align: bottom;
  animation: spin 2s infinite;
  content: "\2026";
}
@keyframes spin {
  0% {
    width: 0px;
  }
  33% {
    width: 4px;
  }
  66% {
    width: 8px;
  }
  100% {
    width: 15px;
  }
}
</style>
