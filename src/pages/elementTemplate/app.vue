<template>
    <sdp-element-template
      v-if="isShow"
      ref="sdpElementTemplate"
      @update="update"
      @on-back="onBack"
      v-bind="shareData"
    ></sdp-element-template>
</template>

<script>
import api from '@/utils/request'
import { setToken, getToken } from '@/utils/auth'
import { MicroApp } from './utils.ts'
import { FOLDER_ID, OPERATE_TYPE, EL_TYPE } from 'packages/base/elementTemplate/constants/index.ts'
import { changeVarible } from 'packages/assets/newTheme/theme'
import { THEME_MAPS } from 'packages/assets/constant'
import { mockConfig } from './simMock'

export default {
  name: 'element',
  data() {
    return {
      microAppClass: null,
      isShow: false,
      shareData: {
        api,
        element: {
          id: '',
          folderId: '',
          elType: EL_TYPE.chart
        },
        env: {
          isOpenAliasDict: true
        },
        options: {
          operateType: OPERATE_TYPE.add,
          langCode: this.$i18n.locale,
          themeCode: 0,
          isMobile: false,
          tenantId: '',
          viewType: 'fullScreen',
          configData: null,
          onlyIndex: false,
          featureConfig: {},
          systemConfig: {}
        },
      }
    }
  },
  created() {
    this.microAppClass = new MicroApp(this.dataListener)
    this.init()
  },

  destroyed() {
    this.microAppClass?.destroy()
  },

  methods: {
    init() {
      const { data: {
        token = getToken(),
        themeCode = 0,
        tenantId = 'a025',
        operateType = OPERATE_TYPE.add,
        elType = EL_TYPE.chart,
        id = '',
        folderId = FOLDER_ID,
        mainColor = '#225ED1',
        onlyIndex,
        featureConfig = {},
        systemConfig = {},
        viewType = 'fullScreen',
        langCode = 'zh-CN'
        }
      } = this.microAppClass.getData() || mockConfig
      console.log('getData!!!!', this.microAppClass.getData())

      changeVarible([THEME_MAPS.classicWhite], {
        color: '#553CCE',
        changeColor: mainColor
      })

      this.updateToken(token)
      const options = this.shareData.options
      const element = this.shareData.element
      Object.assign(options, {
        themeCode,
        tenantId,
        operateType,
        onlyIndex,
        featureConfig,
        systemConfig,
        viewType,
        langCode: onlyIndex ? langCode.split('-')[0] : langCode
      })
      element.elType = elType
      element.folderId = folderId
      element.id = id

      this.isShow = true

      if (onlyIndex) {
        [...document.getElementsByTagName('micro-app-body')].forEach(el => {
          if (el.contains(this.$root.$el)) {
            el.setAttribute('sdp-only-Index', '1')
          }
        })
      }

      window.Vue.prototype.$getFeatureConfig = (key) => {
        if (!key) return undefined
        const cfg = JSON.parse(JSON.stringify(this.shareData.options.featureConfig || {}))
        const keys = key.split('.')
        return keys.reduce((obj, k) => {
          return obj && typeof obj === 'object' ? obj[k] : undefined
        }, cfg)
      }
      window.Vue.prototype.$getSystemConfig = (key) => {
        if (!key) return undefined
        const cfg = JSON.parse(JSON.stringify(this.shareData.options.systemConfig || {}))
        const keys = key.split('.')
        return keys.reduce((obj, k) => {
          return obj && typeof obj === 'object' ? obj[k] : undefined
        }, cfg)
      }
    },
    dataListener(data) {
      console.log('dataListener', data)
      if (data?.type === 'init') {
        this.isShow = false
        setTimeout(() => {
          this.init()
        }, 100)
        return '初始化完成'
      }
      if (data?.type === 'updateToken') {
        this.updateToken(data.data)

        return '更新完成token'
      } else if (data?.type === 'back') {
        const sdpElementTemplate = this.$refs.sdpElementTemplate
        if (sdpElementTemplate) {
          const el = sdpElementTemplate.$refs[sdpElementTemplate.componentName]
          if (el) {
            const dom = el.$refs[el.componentName]
            dom?.backHandler()
          }
        }
      }
      // this.isShow = false

      // setTimeout(() => {
      //   this.isShow = true
      // })
      // 传入回调数据给父应用
      return '456'
    },
    updateToken(token) {
      setToken(token)
    },
    update(data) {
      this.microAppClass.forceDispatch(data)
    },
    onBack() {
      this.microAppClass.forceDispatch({
        type: 'back'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
