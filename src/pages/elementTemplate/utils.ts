export class MicroApp {
  dataListener: () => void

  get microApp() {
    return (window as any).__MICRO_APP_ENVIRONMENT__ ? (window as any).microApp : null
  }

  constructor(dataListener) {
    this.dataListener = dataListener

    this.microApp?.addDataListener(this.dataListener)
  }

  getData() {
    const data = this.microApp?.getData() || null
    return data
  }

  dispatch(data) {
    this.microApp?.dispatch(data, (res) => {
      // 获取父应用回调数据
      console.log('数据已经发送完成', res)
    })
  }

  forceDispatch(data) {
    this.microApp?.forceDispatch(data, (res) => {
      // 获取父应用回调数据
      console.log('数据已经发送完成', res)
    })
  }

  destroy() {
    this.microApp?.removeDataListener(this.dataListener)
  }
}
