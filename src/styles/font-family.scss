// @font-face {
//   font-family: 'SourceHanSansSC-Bold';
//   src: url('../assets/fonts/SourceHanSansSC-Bold.otf');
//   font-weight: normal;
//   font-style: normal;
// }

// @font-face {
//   font-family: 'SourceHanSansSC-ExtraLight';
//   src: url('../assets/fonts/SourceHanSansSC-ExtraLight.otf');
//   font-weight: normal;
//   font-style: normal;
// }

// @font-face {
//   font-family: 'SourceHanSansSC-Heavy';
//   src: url('../assets/fonts/SourceHanSansSC-Heavy.otf');
//   font-weight: normal;
//   font-style: normal;
// }

// @font-face {
//   font-family: 'SourceHanSansSC-Light';
//   src: url('../assets/fonts/SourceHanSansSC-Light.otf');
//   font-weight: normal;
//   font-style: normal;
// }

// @font-face {
//   font-family: 'SourceHanSansSC-Medium';
//   src: url('../assets/fonts/SourceHanSansSC-Medium.otf');
//   font-weight: normal;
//   font-style: normal;
// }

// @font-face {
//   font-family: 'SourceHanSansSC-Normal';
//   src: url('../assets/fonts/SourceHanSansSC-Normal.otf');
//   font-weight: normal;
//   font-style: normal;
// }

// @font-face {
//   font-family: 'SourceHanSansSC-Regular';
//   src: url('../assets/fonts/SourceHanSansSC-Regular.otf');
//   font-weight: normal;
//   font-style: normal;
// }


@font-face {
  font-family: 'Roboto-Bold';
  src: url('https://sdp-cdn.shijicloud.com/static/fonts/Roboto-Bold.ttf') format('truetype'),
    url('https://sdp-cdn.shijicloud.com/static/fonts/Roboto-Bold.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto-Regular';
  src: url('https://sdp-cdn.shijicloud.com/static/fonts/Roboto-Regular.ttf') format('truetype'),
    url('https://sdp-cdn.shijicloud.com/static/fonts/Roboto-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'NotoSansHans-Bold';
  src: url('https://sdp-cdn.shijicloud.com/static/fonts/NotoSansHans-Bold.otf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Robot-Light';
  src: url('https://sdp-cdn.shijicloud.com/static/fonts/Roboto-Light.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'NotoSansHans-Regular';
  src: url('https://sdp-cdn.shijicloud.com/static/fonts/NotoSansHans-Regular.otf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
