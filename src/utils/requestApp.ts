import { axios, requestSuccessFunc, requestErrFunc, responseSuccessFuc, responseErrFunc } from './request'
const isProduction = process.env.NODE_ENV === 'production'
// app测试
let testURL = 'https://sdptest.shijicloud.com/mobile/'
// app预发布
// const testURL = 'https://sdp-pre.shijicloud.com/mobile/'
// 生产
// const testURL = 'https://sdp.shijicloud.com/mobile'
// 准生产
// const testURL = 'https://sdp-pre.shijicloud.com/fezsc/mobile/'
// 零售
// testURL = 'http://*************:82/mobile/'

let productionURL = `${window.location.protocol}//${window.location.host}${window.location.href.includes('engzsc') ? '/fezsc' : ''}/mobile/`
try {
  // APP 中获取看板请求地址，这里需要特殊判断是因为在 qiankun 嵌入时无法取的看板地址对应的信息，拿到的是主应用的地址
  const appRequestUrl = window.localStorage.getItem('__SBI_BASE_URL__') || ''
  if (appRequestUrl && appRequestUrl.includes('/fezsc')) {
    productionURL = `${window.location.protocol}//${window.location.host}/fezsc/mobile/`
  }
} catch (e) {
  console.error('get __SBI_BASE_URL__ error:', e)
}

// if (window.location.host === '***********:8081') {
// //   productionURL = 'https://sdptest.shijicloud.com/mobile/'
// // }
const data = {
  baseURL: isProduction ? productionURL : testURL
}
// 配置App手机专用路径
const appConfig = data
const appService = axios.create(appConfig)
// App手机添加请求拦截器
appService.interceptors.request.use(requestSuccessFunc, requestErrFunc)
// App手机添加响应拦截器
appService.interceptors.response.use(responseSuccessFuc, responseErrFunc)
export default appService
